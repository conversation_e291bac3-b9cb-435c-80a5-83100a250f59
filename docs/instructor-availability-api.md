# Instructor Availability REST API

## Overview
This RESTful web service provides instructor availability information for scheduling lessons. It returns available time slots for a specific instructor over a 30-day period.

## Endpoint
```
POST /instructors/availability
```

## Request Headers
```
Content-Type: application/json
```

## Request Body (InstructorAVLServiceDTO)
```json
{
  "instructorId": "20057",
  "startDate": "2024-01-15",
  "custMemberID": "12345",
  "aptId": 67890,
  "seriesID": "SER123",
  "instructorStatus": "Active"
}
```

### Required Fields
- **instructorId** (String): The instructor's unique identifier
- **startDate** (String): Start date for availability calculation in "yyyy-MM-dd" format

### Optional Fields
- **custMemberID** (String): Customer member ID for logging purposes
- **aptId** (Long): Appointment ID to get location profile details
- **seriesID** (String): Series ID for logging purposes
- **instructorStatus** (String): Instructor status filter

## Response Body (InstructorAVLServiceResponseDTO)
```json
{
  "instructorId": "20057",
  "name": "<PERSON>",
  "instructorStatus": "Active",
  "storeNumber": "583",
  "storeLocation": "Guitar Center - Downtown",
  "email": "<EMAIL>",
  "phoneNumber": "(*************",
  "availability": "[{\"date\":\"2024-01-15\",\"slots\":[\"09:00-09:30#BOTH\",\"09:30-10:00#ONLINE\",\"10:00-10:30#INSTORE\"]}]",
  "lessons": [
    {
      "date": "2024-01-15",
      "times": [
        {
          "startTime": "09:00",
          "endTime": "09:30",
          "insName": "John Smith",
          "insId": 20057
        }
      ]
    }
  ]
}
```

## Response Fields
- **instructorId**: Instructor's unique identifier
- **name**: Instructor's full name
- **instructorStatus**: Current status (Active, Inactive, etc.)
- **availability**: JSON string containing formatted availability slots
- **lessons**: Structured lesson objects grouped by date

## Availability Slot Format
Each availability slot includes:
- **Time Range**: "HH:mm-HH:mm" (24-hour format)
- **Service Mode**: 
  - `#ONLINE` - Online lessons only
  - `#INSTORE` - In-store lessons only
  - `#BOTH` - Both delivery modes available

## Business Rules
1. **30-Day Window**: Returns availability for 30 days starting from startDate
2. **30-Minute Slots**: All slots are exactly 30 minutes long
3. **Time Boundaries**: Slots start only at :00 or :30 minutes
4. **Instructor Availability**: Respects instructor's weekly schedule
5. **Time-Off Handling**: Excludes instructor time-off periods
6. **Existing Appointments**: Excludes already booked time slots

## Error Handling
If an error occurs, the response will include:
```json
{
  "instructorStatus": "ERROR",
  "availability": "[]"
}
```

## Example Usage

### cURL Request
```bash
curl -X POST http://localhost:8080/scheduler/instructors/availability \
  -H "Content-Type: application/json" \
  -d '{
    "instructorId": "20057",
    "startDate": "2024-01-15"
  }'
```

### JavaScript/Fetch Request
```javascript
fetch('/scheduler/instructors/availability', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    instructorId: '20057',
    startDate: '2024-01-15'
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

## Security Configuration
The endpoint is configured in `security.xml` to allow access without authentication:
```xml
<sec:http pattern="/instructors/availability/**" security="none" />
```

## Implementation Notes
- Uses the `setInstructorsFreeSlotsforSelfService` method from InstructorService
- Includes comprehensive logging for debugging and monitoring
- Handles both appointment-based and default location profile scenarios
- Returns structured data suitable for both JSON parsing and direct display
