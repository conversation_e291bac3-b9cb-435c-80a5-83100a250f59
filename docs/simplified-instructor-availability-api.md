# Simplified Instructor Availability API

## Overview
This simplified RESTful web service provides instructor availability information using only the required fields for optimal performance and clarity. It eliminates unnecessary parameters and provides a cleaner interface.

## New Simplified Endpoint
```
POST /instructors/availability/simple
```

## Request Headers
```
Content-Type: application/json
```

## Simplified Request Body (SimpleInstructorAvailabilityDTO)
```json
{
  "instructorId": "20057",
  "startDate": "2024-01-15"
}
```

### Required Fields Only
- **instructorId** (String): The instructor's unique identifier - REQUIRED
- **startDate** (String): Start date for availability calculation in "yyyy-MM-dd" format - REQUIRED

## Response Body (Same as original)
```json
{
  "instructorId": "20057",
  "name": "<PERSON>",
  "instructorStatus": "Active",
  "availability": "[{\"date\":\"2024-01-15\",\"slots\":[\"09:00-09:30#BOTH\"]}]",
  "lessons": [
    {
      "date": "2024-01-15",
      "times": [
        {
          "startTime": "09:00",
          "endTime": "09:30",
          "insName": "<PERSON>",
          "insId": 20057
        }
      ]
    }
  ]
}
```

## Key Benefits

### 1. Simplified Interface
- **Only 2 required fields** (vs 10+ in original)
- **No optional parameters** to confuse the API
- **Clear validation** with meaningful error messages

### 2. Better Performance
- **Reduced payload size** - smaller request objects
- **Faster serialization/deserialization**
- **Less memory usage** during processing

### 3. Improved Maintainability
- **Single responsibility** - focused only on availability
- **Clear documentation** - obvious what's required
- **Type safety** with built-in validation

## Usage Examples

### Java Code Example
```java
// Create simplified request with only required fields
SimpleInstructorAvailabilityDTO request = new SimpleInstructorAvailabilityDTO();
request.setInstructorId("20057");
request.setStartDate("2024-01-15");

// Create location profile with defaults
SimpleLocationProfileDTO locationProfile = new SimpleLocationProfileDTO(1L, 25L);

// Create response object
InstructorAVLServiceResponseDTO result = new InstructorAVLServiceResponseDTO();

// Call the simplified service method
instructorService.setInstructorsFreeSlotsforSelfService(result, locationProfile, request);
```

### cURL Request
```bash
curl -X POST http://localhost:8080/scheduler/instructors/availability/simple \
  -H "Content-Type: application/json" \
  -d '{
    "instructorId": "20057",
    "startDate": "2024-01-15"
  }'
```

### JavaScript/Fetch Request
```javascript
fetch('/scheduler/instructors/availability/simple', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    instructorId: '20057',
    startDate: '2024-01-15'
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

## Validation

### Built-in Validation
The `SimpleInstructorAvailabilityDTO` includes automatic validation:
- **Non-null checks** for required fields
- **Date format validation** (yyyy-MM-dd pattern)
- **Empty string validation**

### Error Response
```json
{
  "instructorStatus": "ERROR",
  "availability": "[]"
}
```

## Default Configuration
The simplified API uses default values for location profile:
- **Profile ID**: 1 (configurable)
- **Activity ID**: 25 (Guitar lessons - configurable)

## Comparison with Original API

| Feature | Original API | Simplified API |
|---------|-------------|----------------|
| **Required Fields** | 4 fields | 2 fields |
| **Optional Fields** | 6+ fields | 0 fields |
| **Request Size** | Large | Minimal |
| **Validation** | Manual | Built-in |
| **Error Handling** | Basic | Enhanced |
| **Performance** | Standard | Optimized |

## Migration Guide

### From Original to Simplified
```java
// OLD WAY - Complex DTO with many fields
InstructorAVLServiceDTO oldRequest = new InstructorAVLServiceDTO();
oldRequest.setInstructorId("20057");
oldRequest.setStartDate("2024-01-15");
oldRequest.setCustMemberID("12345");     // Not needed
oldRequest.setAptId(67890L);             // Not needed
oldRequest.setSeriesID("SER123");        // Not needed
oldRequest.setInstructorStatus("Active"); // Not needed

// NEW WAY - Simple DTO with only required fields
SimpleInstructorAvailabilityDTO newRequest = new SimpleInstructorAvailabilityDTO("20057", "2024-01-15");
```

## Security Configuration
Both endpoints are configured in `security.xml`:
```xml
<sec:http pattern="/instructors/availability/**" security="none" />
<sec:http pattern="/instructors/availability/simple/**" security="none" />
```

## Implementation Details

### DTOs Created
1. **SimpleLocationProfileDTO** - Contains only profileID and activityID
2. **SimpleInstructorAvailabilityDTO** - Contains only instructorId and startDate

### Methods Added
1. **Service Interface**: Overloaded method signature
2. **Service Implementation**: Validation and conversion logic
3. **REST Controller**: New simplified endpoint

### Backward Compatibility
- **Original API unchanged** - existing clients continue to work
- **Internal conversion** - simplified DTOs convert to full DTOs
- **Same business logic** - uses existing availability calculation

The simplified API provides a cleaner, more efficient way to request instructor availability while maintaining full compatibility with existing functionality.
