-- Set up ETL staging tables
--
-- MEmes changes from offshore originals
--  1. Add drops to clean-up existing tables
--  2. combined DDL and population scripts
--  3. Removed tablespace declarations
--  4. Removed schema declarations
--  5. Increased width of table and key columns in rejected/required
--

drop table STG_ACTIVITY_MAP cascade constraints;
drop table STG_APP_SEC_RESOURCES cascade constraints;
drop table STG_APPOINTMENTS cascade constraints; 
drop table STG_APPT_SET cascade constraints;
drop table STG_CLIENTS cascade constraints;
drop table STG_ETL_STATUS cascade constraints;
drop table STG_REJECTED_RECORD cascade constraints;
drop table STG_REQUIRED_RECORD cascade constraints;
drop table TFLOWMETER_CATCHER cascade constraints;
drop table TLOG_CATCHER cascade constraints;
drop table TSTATE_CATCHER cascade constraints;


--------------------------------------------------------
--  File created - Sunday-November-03-2013   
--------------------------------------------------------
--------------------------------------------------------
--  DDL for Table STG_ACTIVITY_MAP
--------------------------------------------------------

  CREATE TABLE "STG_ACTIVITY_MAP" 
   (	"ACTIVITY_ID_SRC" VARCHAR2(20 BYTE), 
	"ACTIVITY_NAME_SRC" VARCHAR2(100 BYTE), 
	"ACTIVITY_ID_TARGET" VARCHAR2(20 BYTE), 
	"ACTIVITY_NAME_TARGET" VARCHAR2(100 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Table STG_APP_SEC_RESOURCES
--------------------------------------------------------

  CREATE TABLE "STG_APP_SEC_RESOURCES" 
   (	"APPOINTMENTID" NUMBER, 
	"LICENSEEID" NUMBER, 
	"LOCATIONID" NUMBER, 
	"RESOURCEID" NUMBER, 
	"RESOURCENAME" VARCHAR2(50 BYTE), 
	"RESOURCEPOOLID" NUMBER, 
	"RESOURCEEXTERNALID" VARCHAR2(50 BYTE), 
	"RESOURCEPOOLNAME" VARCHAR2(50 BYTE), 
	"PRICE" NUMBER, 
	"APPOINTMENTENDTIME" VARCHAR2(50 BYTE), 
	"RECOVERYTIME" NUMBER, 
	"ATTENDEES" NUMBER, 
	"APPLYCANCELLATIONFEE" NUMBER, 
	"CANCELLATIONFEE" NUMBER, 
	"CAMPAIGNTIMEZONE" VARCHAR2(50 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Table STG_APPOINTMENTS
--------------------------------------------------------

  CREATE TABLE "STG_APPOINTMENTS" 
   (	"APPOINTMENTID" VARCHAR2(100 BYTE), 
	"APPOINTMENTEXTERNALID" VARCHAR2(100 BYTE), 
	"LICENSEEID" VARCHAR2(100 BYTE), 
	"LOCATIONID" VARCHAR2(100 BYTE), 
	"LOCATIONNAME" VARCHAR2(100 BYTE), 
	"CAMPAIGNID" VARCHAR2(100 BYTE), 
	"CAMPAIGNNAME" VARCHAR2(100 BYTE), 
	"PROGRAMID" VARCHAR2(100 BYTE), 
	"PROGRAMEXTERNALID" VARCHAR2(100 BYTE), 
	"PROGRAMNAME" VARCHAR2(100 BYTE), 
	"ACTIVITYID" VARCHAR2(100 BYTE), 
	"ACTIVITYEXTERNALID" VARCHAR2(100 BYTE), 
	"ACTIVITYNAME" VARCHAR2(100 BYTE), 
	"RESOURCEID" VARCHAR2(100 BYTE), 
	"RESOURCEEXTERNALID" VARCHAR2(100 BYTE), 
	"RESOURCENAME" VARCHAR2(100 BYTE), 
	"SCHEDULETYPEID" VARCHAR2(100 BYTE), 
	"APPOINTMENTSTART" VARCHAR2(100 BYTE), 
	"APPOINTMENTEND" VARCHAR2(100 BYTE), 
	"ATTENDEES" VARCHAR2(100 BYTE), 
	"PRICE" VARCHAR2(100 BYTE), 
	"PAYMENTTYPEID" VARCHAR2(100 BYTE), 
	"PAYMENTTYPENAME" VARCHAR2(100 BYTE), 
	"USERID" VARCHAR2(100 BYTE), 
	"USEREXTERNALID" VARCHAR2(100 BYTE), 
	"USERFIRSTNAME" VARCHAR2(100 BYTE), 
	"USERLASTNAME" VARCHAR2(100 BYTE), 
	"CREATIONDATE" VARCHAR2(100 BYTE), 
	"CREATEDBYUSERID" VARCHAR2(100 BYTE), 
	"CREATEDBYUSEREXTERNALID" VARCHAR2(100 BYTE), 
	"CREATEDBYUSERFIRSTNAME" VARCHAR2(100 BYTE), 
	"CREATEDBYUSERLASTNAME" VARCHAR2(100 BYTE), 
	"ISCANCELLED" VARCHAR2(100 BYTE), 
	"CANCELLEDDATE" VARCHAR2(100 BYTE), 
	"CANCELLEDBYUSERID" VARCHAR2(100 BYTE), 
	"CANCELLEDBYUSEREXTERNALID" VARCHAR2(100 BYTE), 
	"CANCELLEDBYFIRSTNAME" VARCHAR2(100 BYTE), 
	"CANCELLEDBYLASTNAME" VARCHAR2(100 BYTE), 
	"ISCHECKEDIN" VARCHAR2(100 BYTE), 
	"ISCHECKEDOUT" VARCHAR2(100 BYTE), 
	"ISCONFIRMED" VARCHAR2(100 BYTE), 
	"DURATION" VARCHAR2(100 BYTE), 
	"INITIALDURATION" VARCHAR2(100 BYTE), 
	"APPOINTMENTDURATIONLABEL" VARCHAR2(100 BYTE), 
	"APPTSETID" VARCHAR2(100 BYTE), 
	"ISWORKFLOWENABLED" VARCHAR2(100 BYTE), 
	"COMPLETEDWORKFLOWSTEPS" VARCHAR2(100 BYTE), 
	"ISWORKFLOWCOMPLETED" VARCHAR2(100 BYTE), 
	"PRIMARYRESOURCEPRICE" VARCHAR2(100 BYTE), 
	"RECOVERYTIME" VARCHAR2(100 BYTE), 
	"SETUPTIME" VARCHAR2(100 BYTE), 
	"CLOSEOUTTIME" VARCHAR2(100 BYTE), 
	"CANCELLATIONTYPEID" VARCHAR2(100 BYTE), 
	"CANCELLATIONTYPE" VARCHAR2(100 BYTE), 
	"D_APPOINTMENTMULTILOCATIONID" VARCHAR2(100 BYTE), 
	"INITIALAPPOINTMENTID" VARCHAR2(100 BYTE), 
	"APPLYACTIVITYCANCELLATIONFEE" VARCHAR2(100 BYTE), 
	"ACTIVITYCANCELLATIONFEE" VARCHAR2(100 BYTE), 
	"APPLYPRCANCELLATIONFEE" VARCHAR2(100 BYTE), 
	"PRCANCELLATIONFEE" VARCHAR2(100 BYTE), 
	"CONFIRMATIONNUMBER" VARCHAR2(100 BYTE), 
	"D_ISMULTILOCATIONHOST_D" VARCHAR2(100 BYTE), 
	"CAMPAIGNTIMEZONE" VARCHAR2(100 BYTE), 
	"RESOURCETIMEZONE" VARCHAR2(100 BYTE), 
	"CLIENTTIMEZONE" VARCHAR2(100 BYTE), 
	"ISLATECANCELED" VARCHAR2(100 BYTE), 
	"APPTGROUPID" VARCHAR2(100 BYTE), 
	"SEQUENCENUMBER" VARCHAR2(100 BYTE), 
	"WAITLISTID" VARCHAR2(100 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Table STG_APPT_SET
--------------------------------------------------------

  CREATE TABLE "STG_APPT_SET" 
   (	"LICENSEEID" VARCHAR2(45 BYTE), 
	"APPTSETID" VARCHAR2(45 BYTE), 
	"APPTSETTYPEID" VARCHAR2(45 BYTE), 
	"APPTSETNAME" VARCHAR2(45 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Table STG_CLIENTS
--------------------------------------------------------

  CREATE TABLE "STG_CLIENTS" 
   (	"LICENSEEID" VARCHAR2(45 BYTE), 
	"USERID" VARCHAR2(45 BYTE), 
	"EXTERNALID" VARCHAR2(45 BYTE), 
	"SALUTATION" VARCHAR2(45 BYTE), 
	"FIRSTNAME" VARCHAR2(50 BYTE), 
	"LASTNAME" VARCHAR2(50 BYTE), 
	"USERNAME" VARCHAR2(50 BYTE), 
	"USERGROUPID" VARCHAR2(45 BYTE), 
	"USERGROUPNAME" VARCHAR2(45 BYTE), 
	"CUSTOMERID" VARCHAR2(45 BYTE), 
	"CUSTOMERNAME" VARCHAR2(45 BYTE), 
	"RULESGROUPID" VARCHAR2(45 BYTE), 
	"RULESGROUPNAME" VARCHAR2(45 BYTE), 
	"CREATEDBYUSERID" VARCHAR2(45 BYTE), 
	"CREATEDBYUSERFIRSTNAME" VARCHAR2(45 BYTE), 
	"CREATEDBYUSERLASTNAME" VARCHAR2(45 BYTE), 
	"CREATEDATE" VARCHAR2(45 BYTE), 
	"TIMEZONE" VARCHAR2(45 BYTE), 
	"COMPANY" VARCHAR2(45 BYTE), 
	"JOBTITLE" VARCHAR2(45 BYTE), 
	"MEMBERID" VARCHAR2(45 BYTE), 
	"GENDER" VARCHAR2(45 BYTE), 
	"BIRTHDATE" VARCHAR2(45 BYTE), 
	"ADDRESS1" VARCHAR2(45 BYTE), 
	"ADDRESS2" VARCHAR2(45 BYTE), 
	"CITY" VARCHAR2(45 BYTE), 
	"POSTALCODE" VARCHAR2(45 BYTE), 
	"STATENAME" VARCHAR2(45 BYTE), 
	"COUNTRYNAME" VARCHAR2(45 BYTE), 
	"BILLINGADDRESS1" VARCHAR2(45 BYTE), 
	"BILLINGADDRESS2" VARCHAR2(45 BYTE), 
	"BILLINGCITY" VARCHAR2(45 BYTE), 
	"BILLINGPOSTALCODE" VARCHAR2(45 BYTE), 
	"BILLINGSTATENAME" VARCHAR2(45 BYTE), 
	"BILLINGCOUNTRYNAME" VARCHAR2(45 BYTE), 
	"HOMEPHONE" VARCHAR2(45 BYTE), 
	"WORKPHONE" VARCHAR2(45 BYTE), 
	"MOBILEPHONE" VARCHAR2(45 BYTE), 
	"FAXPHONE" VARCHAR2(45 BYTE), 
	"CREDITCARDNUMBER" VARCHAR2(45 BYTE), 
	"D_ISSPECIALPROMOTIONSENABLED" VARCHAR2(45 BYTE), 
	"CONTACTMETHOD" VARCHAR2(45 BYTE), 
	"USERSCOMMENT" VARCHAR2(45 BYTE), 
	"SSN" VARCHAR2(45 BYTE), 
	"ISEMAILENABLED" VARCHAR2(45 BYTE), 
	"EMAILADDRESS" VARCHAR2(500 BYTE), 
	"APPLYTIMEZONE" VARCHAR2(45 BYTE), 
	"CUSTOM_FIELD_1" VARCHAR2(45 BYTE), 
	"CUSTOM_FIELD_2" VARCHAR2(45 BYTE), 
	"CUSTOM_FIELD_3" VARCHAR2(45 BYTE), 
	"CUSTOM_FIELD_4" VARCHAR2(45 BYTE), 
	"CUSTOM_FIELD_5" VARCHAR2(45 BYTE), 
	"CUSTOM_FIELD_6" VARCHAR2(45 BYTE), 
	"CUSTOM_FIELD_7" VARCHAR2(45 BYTE), 
	"CUSTOM_FIELD_8" VARCHAR2(45 BYTE), 
	"CUSTOM_FIELD_9" VARCHAR2(45 BYTE), 
	"CUSTOM_FIELD_10" VARCHAR2(45 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Table STG_ETL_STATUS
--------------------------------------------------------

  CREATE TABLE "STG_ETL_STATUS" 
   (	"JOB_ID" NUMBER, 
	"START_DATE" DATE, 
	"END_DATE" DATE, 
	"STATUS" VARCHAR2(40 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Table STG_REJECTED_RECORD
--------------------------------------------------------

  CREATE TABLE "STG_REJECTED_RECORD" 
   (	"JOB_ID" VARCHAR2(10 BYTE), 
	"TABLE_NAME" VARCHAR2(100 BYTE), 
	"KEY" VARCHAR2(100 BYTE), 
	"ERROR_CODE" VARCHAR2(255 BYTE), 
	"ERROR_MESSAGE" VARCHAR2(255 BYTE), 
	"CREATION_DATE" DATE
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Table STG_REQUIRED_RECORD
--------------------------------------------------------

  CREATE TABLE "STG_REQUIRED_RECORD" 
   (	"JOB_ID" VARCHAR2(10 BYTE), 
	"TABLE_NAME" VARCHAR2(100 BYTE), 
	"KEY" VARCHAR2(100 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Table TFLOWMETER_CATCHER
--------------------------------------------------------

  CREATE TABLE "TFLOWMETER_CATCHER" 
   (	"MOMENT" DATE, 
	"PID" VARCHAR2(20 BYTE), 
	"FATHER_PID" VARCHAR2(20 BYTE), 
	"ROOT_PID" VARCHAR2(20 BYTE), 
	"SYSTEM_PID" NUMBER, 
	"PROJECT" VARCHAR2(50 BYTE), 
	"JOB" VARCHAR2(50 BYTE), 
	"JOB_REPOSITORY_ID" VARCHAR2(255 BYTE), 
	"JOB_VERSION" VARCHAR2(255 BYTE), 
	"CONTEXT" VARCHAR2(50 BYTE), 
	"ORIGIN" VARCHAR2(255 BYTE), 
	"LABEL" VARCHAR2(255 BYTE), 
	"COUNT" NUMBER, 
	"REFERENCE" NUMBER, 
	"THRESHOLDS" VARCHAR2(255 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Table TLOG_CATCHER
--------------------------------------------------------

  CREATE TABLE "TLOG_CATCHER" 
   (	"MOMENT" DATE, 
	"PID" VARCHAR2(20 BYTE), 
	"ROOT_PID" VARCHAR2(20 BYTE), 
	"FATHER_PID" VARCHAR2(20 BYTE), 
	"PROJECT" VARCHAR2(50 BYTE), 
	"JOB" VARCHAR2(255 BYTE), 
	"CONTEXT" VARCHAR2(50 BYTE), 
	"PRIORITY" NUMBER, 
	"TYPE" VARCHAR2(255 BYTE), 
	"ORIGIN" VARCHAR2(255 BYTE), 
	"MESSAGE" VARCHAR2(255 BYTE), 
	"CODE" NUMBER
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Table TSTATE_CATCHER
--------------------------------------------------------

  CREATE TABLE "TSTATE_CATCHER" 
   (	"MOMENT" DATE, 
	"PID" VARCHAR2(20 BYTE), 
	"FATHER_PID" VARCHAR2(20 BYTE), 
	"ROOT_PID" VARCHAR2(20 BYTE), 
	"SYSTEM_PID" NUMBER, 
	"PROJECT" VARCHAR2(50 BYTE), 
	"JOB" VARCHAR2(50 BYTE), 
	"JOB_REPOSITORY_ID" VARCHAR2(255 BYTE), 
	"JOB_VERSION" VARCHAR2(255 BYTE), 
	"CONTEXT" VARCHAR2(50 BYTE), 
	"ORIGIN" VARCHAR2(255 BYTE), 
	"MESSAGE_TYPE" VARCHAR2(255 BYTE), 
	"MESSAGE" VARCHAR2(255 BYTE), 
	"DURATION" NUMBER
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);

-- Populate
--------------------------------------------------------
--  File created - Thursday-October-17-2013   
--------------------------------------------------------
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values (null,null,'0','Rehearsal');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values ('24','Solo','1','Solo Rehearsal');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values (null,null,'2','Clinic');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values (null,null,'3','Group Class');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values (null,null,'4','Summer Camp');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values (null,null,'5','Rockshow');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values (null,null,'6','Open Office');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values ('9','Guitar Lesson','7','Guitar Lesson');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values ('11','Drum Lesson','8','Drum Lesson');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values ('10','Bass Lesson','9','Bass Lesson');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values ('17','Keys / Piano Lesson','10','Keys/Piano Lesson');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values ('12','Vocal Lesson','11','Vocal Lesson');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values ('31','DJ Lesson','12','DJ Lesson');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values ('27','Horn/Brass Lesson','13','Horn/Brass Lesson');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values ('19','Logic Lesson','14','Logic Lesson');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values ('18','Protools Lesson','15','Protools Lesson');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values (null,null,'16','Sparring Partner');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values ('29','Strings Lesson','17','Strings Lesson');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values ('28','Woodwind Lesson','18','Woodwind Lesson');
Insert into STG_ACTIVITY_MAP (ACTIVITY_ID_SRC,ACTIVITY_NAME_SRC,ACTIVITY_ID_TARGET,ACTIVITY_NAME_TARGET) values ('21','Recording','14','Logic Lesson');

-- Large, medium and small studios will map to 'Rehearsal'
insert into stg_activity_map (activity_id_src, activity_name_src,
                              activity_id_target, activity_name_target)
  values('16', 'Large Studio', '0', 'Rehearsal');
insert into stg_activity_map (activity_id_src, activity_name_src,
                              activity_id_target, activity_name_target)
  values('23', 'Medium Studio', '0', 'Rehearsal');
insert into stg_activity_map (activity_id_src, activity_name_src,
                              activity_id_target, activity_name_target)
  values('22', 'Small Studio', '1', 'Solo Rehearsal');
insert into stg_activity_map (activity_id_src, activity_name_src,
                              activity_id_target, activity_name_target)
  values('25', 'Mainstage', '0', 'Rehearsal');