--
-- Add account to PROD for <PERSON> So<PERSON>tty
--
-- Applicable to:-
--  Production: required to be applied once
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to create account, do not apply more than once
--
-- $Id: 40_add_Shiva_as_site_admin_gcss-724.sql 2997 2014-11-24 18:34:49Z memes $

whenever sqlerror exit sql.sqlcode rollback;

-- <PERSON> as Site Admin
insert into person (person_id, version, updated, updated_by, first_name,
                    last_name, email, phone, external_id, auth_id)
  values(person_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), '<PERSON>', '<PERSON><PERSON>hetty',
         '<EMAIL>', null, null, 'Shiva.Somishetty');
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name = 'Site Admin'), null);

-- Update patch log
insert into gcss_patches(description)
  values('Create access for <PERSON> Somishetty $Id: 40_add_<PERSON>_as_site_admin_gcss-724.sql 2997 2014-11-24 18:34:49Z memes $');
