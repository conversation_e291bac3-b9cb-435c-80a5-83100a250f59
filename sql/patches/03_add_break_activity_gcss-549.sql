--
-- Create 'Break' activity
--
-- Applicable to:-
--  All: required to be applied once
--
-- Steps:
--  1. Execute this script once to avoid multiple instances of activity from
--     being established
--
-- $Id: 03_add_break_activity_gcss-549.sql 2499 2014-01-21 03:27:21Z memes $

whenever sqler<PERSON>r exit sql.sqlcode rollback;

-- Create a 'Break' activity
insert into activity (activity_id, version, updated, updated_by, site_id,
                     activity_name, service_id, minimum_attendees,
                     maximum_attendees, minimum_duration, maximum_duration,
                     requires_instructor, enabled)
  values (activity_id_seq.nextval, 0, systimestamp, 1, 1, 'Break', 1, 0, 0,
          30, null, 'R', 'Y');

-- Update patch log
insert into gcss_patches(description)
  values('Create new activity for breaks $Id: 03_add_break_activity_gcss-549.sql 2499 2014-01-21 03:27:21Z memes $');
