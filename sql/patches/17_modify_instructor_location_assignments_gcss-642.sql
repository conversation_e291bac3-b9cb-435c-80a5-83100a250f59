--
-- Modify instructors with employee id 084374 to studio 828 to reflect
--
-- Applicable to:-
--  Production: required to be applied once
--  QA: required to be applied as needed to confirm resolution
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to update location association for instructor 084374
--
-- $Id: 17_modify_instructor_location_assignments_gcss-642.sql 2812 2014-05-06 22:50:11Z memes $

whenever sqlerror exit sql.sqlcode rollback;

-- Update the instructor associations
update instructor
  set location_id = (select location_id from location where external_id = '828'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '084374';
update instructor
  set location_id = (select location_id from location where external_id = '822'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '082774';
update instructor
  set location_id = (select location_id from location where external_id = '324'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '084611';
update instructor
  set location_id = (select location_id from location where external_id = '555'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '087196';

-- Update patch log
insert into gcss_patches(description)
  values('Update location for instructors $Id: 17_modify_instructor_location_assignments_gcss-642.sql 2812 2014-05-06 22:50:11Z memes $');
