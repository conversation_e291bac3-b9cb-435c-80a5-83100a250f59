--
-- Add accounts to PR<PERSON> for Victoria Wisot and <PERSON>
--
-- Applicable to:-
--  Production: required to be applied once
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to create account, do not apply more than once
--
-- $Id: 09_add_accounts_for_<PERSON>_and_<PERSON>_gcss-558.sql 2526 2014-01-31 21:25:35Z memes $

whenever sqlerror exit sql.sqlcode rollback;

-- Victoria Wisot as Site Admin
insert into person (person_id, version, updated, updated_by, first_name,
                    last_name, email, phone, external_id, auth_id)
  values(person_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 'Victoria', 'Wisot',
         '<EMAIL>', null, null, 'vwisot');
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name = 'Site Admin'), null);

-- <PERSON> as Site Admin
insert into person (person_id, version, updated, updated_by, first_name,
                    last_name, email, phone, external_id, auth_id)
  values(person_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 'Scott', 'Alvis',
         '<EMAIL>', null, null, 'Scott.Alvis');
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name = 'Site Admin'), null);

-- Update patch log
insert into gcss_patches(description)
  values('Create access for Victotia W and Scott A $Id: 09_add_accounts_for_Victoria_and_Scott_gcss-558.sql 2526 2014-01-31 21:25:35Z memes $');
