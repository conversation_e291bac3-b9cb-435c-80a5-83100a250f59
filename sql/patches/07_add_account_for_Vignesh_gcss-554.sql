--
-- Add Vignesh account to PROD
--
-- Applicable to:-
--  Production: required to be applied once
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to create account, do not apply more than once
--
-- $Id: 07_add_account_for_Vignesh_gcss-554.sql 2512 2014-01-26 18:38:37Z memes $

whenever sqlerror exit sql.sqlcode rollback;

-- <PERSON><PERSON><PERSON> as Site Admin
insert into person (person_id, version, updated, updated_by, first_name,
                    last_name, email, phone, external_id, auth_id)
  values(person_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 'Vignesh', 'Thileepan',
         '<EMAIL>', null, null, 'Vignesh.Thileepan');
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name = 'Site Admin'), null);

-- Update patch log
insert into gcss_patches(description)
  values('Create access for Vignesh $Id: 07_add_account_for_Vignesh_gcss-554.sql 2512 2014-01-26 18:38:37Z memes $');
