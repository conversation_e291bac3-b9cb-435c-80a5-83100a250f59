--
-- Modify instructor with employee id 087026 to studio 246 and 080314 to 493
--
-- Applicable to:-
--  Production: required to be applied once
--  QA: required to be applied as needed to confirm resolution
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to update location association for instructor 087026
--
-- $Id: 31_modify_instructor_locations_087026_080314_gcss-702.sql 2974 2014-09-16 13:38:18Z memes $
whenever sqlerror exit sql.sqlcode rollback;

-- Update the instructor associations
update instructor
  set location_id = (select location_id from location where external_id = '246'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '087026';
update instructor
  set location_id = (select location_id from location where external_id = '493'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '080314';

-- Update patch log
insert into gcss_patches(description)
  values('Update location for instructors <PERSON> and <PERSON> $Id: 31_modify_instructor_locations_087026_080314_gcss-702.sql 2974 2014-09-16 13:38:18Z memes $');
