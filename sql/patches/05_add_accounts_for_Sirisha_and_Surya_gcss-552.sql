--
-- Add <PERSON><PERSON><PERSON> and <PERSON><PERSON> accounts to PROD
--
-- Applicable to:-
--  Production: required to be applied once
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to create accounts, do not apply more than once
--
-- $Id: 05_add_accounts_for_Sirisha_and_Surya_gcss-552.sql 2503 2014-01-21 18:27:25Z memes $

whenever sqlerror exit sql.sqlcode rollback;

-- <PERSON><PERSON><PERSON> as Site Admin
insert into person (person_id, version, updated, updated_by, first_name,
                    last_name, email, phone, external_id, auth_id)
  values(person_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 'Sirisha', 'Yarru',
         '<EMAIL>', null, null, 'Sirisha.Yarru');
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name = 'Site Admin'), null);

-- <PERSON><PERSON> as Site Admin
insert into person (person_id, version, updated, updated_by, first_name,
                    last_name, email, phone, external_id, auth_id)
  values(person_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 'Surya', 'Panigrahi',
         '<EMAIL>', null, null,
         'Suryakanta.Panigrahi');
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name = 'Site Admin'), null);

-- Update patch log
insert into gcss_patches(description)
  values('Create access for Sirisha and Surya  $Id: 05_add_accounts_for_Sirisha_and_Surya_gcss-552.sql 2503 2014-01-21 18:27:25Z memes $');
