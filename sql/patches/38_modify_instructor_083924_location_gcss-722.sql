--
-- Modify instructor with employee id 083924 to studio 784
--
-- Applicable to:-
--  Production: required to be applied once
--  QA: required to be applied as needed to confirm resolution
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to update location association for instructor 083924
--
-- $Id: 38_modify_instructor_083924_location_gcss-722.sql 2993 2014-11-14 19:45:56Z memes $
whenever sqlerror exit sql.sqlcode rollback;

-- Update the instructor associations
update instructor
  set location_id = (select location_id from location where external_id = '784'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '083924';

-- Update patch log
insert into gcss_patches(description)
  values('Update location for instructor <PERSON> $Id: 38_modify_instructor_083924_location_gcss-722.sql 2993 2014-11-14 19:45:56Z memes $');
