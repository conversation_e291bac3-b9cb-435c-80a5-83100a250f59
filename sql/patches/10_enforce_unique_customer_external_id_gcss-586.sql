--
-- Enforce unique constraint on customer external id and site
--
-- Applicable to:-
--  All: apply
--
-- Steps:-
--  1. Execute this script to enforce constraint *AFTER* identifying and removing
--     duplicate entries from database
--
-- $Id: 10_enforce_unique_customer_external_id_gcss-586.sql 2563 2014-02-18 01:12:31Z memes $
whenever sqlerror continue;

-- Enforce the constraint
alter table customer
  add constraint cust_site_external_uniq unique (site_id, external_id);
  
-- Update patch log
insert into gcss_patches(description)
  values('Enforce unique customer external id constraint $Id: 10_enforce_unique_customer_external_id_gcss-586.sql 2563 2014-02-18 01:12:31Z memes $');


