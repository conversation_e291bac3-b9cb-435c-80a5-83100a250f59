--
-- This SQL is used to update migrated data to meet the new rule for
-- identifying unending appointments.
--
-- Applicable to:-
--  All: required to be applied once
--
-- Steps:
--  1. Execute this script once, just in case users have made changes to the
--     values post-execution
--
--
-- $Id: 12_make_unending_series_gcss-591.sql 2646 2014-03-11 20:26:15Z memes $

whenever sqler<PERSON>r exit sql.sqlcode rollback;

-- Modify migrated appointment series that have a start-end date difference of
-- > 5 weeks
update appointment_series
  set series_end_time = null
 where external_id is not null
   and series_end_time is not null
   and trunc(series_end_time) - trunc(series_start_time) >= 35;
 
-- Update patch log
insert into gcss_patches(description)
  values('Modify series end date for migrated series that should be unending $Id: 12_make_unending_series_gcss-591.sql 2646 2014-03-11 20:26:15Z memes $');
