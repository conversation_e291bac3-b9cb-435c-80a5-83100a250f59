--
-- Modify instructor with employee id 087172 to studio 809
--
-- Applicable to:-
--  Production: required to be applied once
--  QA: required to be applied as needed to confirm resolution
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to update location association for instructor 087172
--
-- $Id: 24_modify_instructor_087172_location_gcss-691.sql 2962 2014-08-11 17:06:17Z memes $

whenever sqlerror exit sql.sqlcode rollback;

-- Update the instructor associations
update instructor
  set location_id = (select location_id from location where external_id = '809'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '087172';

-- Update patch log
insert into gcss_patches(description)
  values('Update location for instructor <PERSON> $Id: 24_modify_instructor_087172_location_gcss-691.sql 2962 2014-08-11 17:06:17Z memes $');
