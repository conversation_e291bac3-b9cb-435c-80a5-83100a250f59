--
-- Create onetime table to set one-time availability for instructors GCSS
--
-- Applicable to:-
--  All: apply
--
-- Steps:-
--  1. Execute this script once, for create table, constraints and indices, etc.
--
-- $Id: 19_create_onetime_table_gcss-650.sql $

whenever sqlerror continue;

--create sequence
drop sequence onetime_id_seq;

create sequence onetime_id_seq start with 0 increment by 1 maxvalue
  9999999999999999999999999999 minvalue 0 cache 20 ;
  
--create table
drop table onetime cascade constraints ;

create
  table onetime
  (
	onetime_id            number not null ,
    instructor_id         number not null ,
    start_time            timestamp with time zone not null ,
    end_time              timestamp with time zone not null ,
    version               number not null ,
    updated               timestamp with local time zone not null ,
    updated_by            number not null
  )
  logging ;
  
comment on table onetime is 'Set one-time availability for instructors' ;

--create constraints
alter table onetime add constraint onetime_pk primary key
  (
    onetime_id
  )
  ;
  
alter table onetime add constraint onetime_instructor_fk foreign key (
instructor_id ) references instructor ( instructor_id ) not deferrable ;

--create indices
create index onetime_instructor_id_idx on onetime (instructor_id) online;
create index onetime_start_time_idx on onetime (start_time) online;
create index onetime_char_start_time_idx on onetime (to_char(start_time, 'MM/DD/YYYY')) online;
create index onetime_munge_start_time_idx on onetime (to_date(to_char(start_time, 'YYYY-MM-DD'), 'YYYY-MM-DD')) online;
create index onetime_end_time_idx on onetime (end_time) online;
create index onetime_char_end_time_idx on onetime (to_char(end_time, 'MM/DD/YYYY')) online;
create index onetime_munge_end_time_idx on onetime (to_date(to_char(end_time, 'YYYY-MM-DD'), 'YYYY-MM-DD')) online;
create index onetime_version_idx on onetime (version) online;

-- Update patch log
insert into gcss_patches(description)
  values('Create onetime table, constraints and indexes $Id: 19_create_onetime_table_gcss-650.sql');