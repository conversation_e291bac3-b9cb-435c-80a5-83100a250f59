--
-- Modify instructor with employee id 079320 to studio 246
--
-- Applicable to:-
--  Production: required to be applied once
--  QA: required to be applied as needed to confirm resolution
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to update location association for instructor 079320
--
-- $Id: 
whenever sqlerror exit sql.sqlcode rollback;

-- Update the instructor associations
update instructor
  set location_id = (select location_id from location where external_id = '246'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '079320';

-- Update patch log
insert into gcss_patches(description)
  values('Update location for instructor <PERSON><PERSON> $Id: 34_modify_instructor_079320_location_gcss-711.sql 2979 2014-10-03 14:07:03Z memes $');
