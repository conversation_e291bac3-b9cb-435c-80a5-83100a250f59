--
-- Modify instructor with employee id 057326 to studio 766
--
-- Applicable to:-
--  Production: required to be applied once
--  QA: required to be applied as needed to confirm resolution
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to update location association for instructor 057326
--
-- $Id: 39_modify_instructor_057326_gcss-723.sql 2994 2014-11-17 19:55:47Z memes $
whenever sqlerror exit sql.sqlcode rollback;

-- Update the instructor associations
update instructor
  set location_id = (select location_id from location where external_id = '766'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '057326';

-- Update patch log
insert into gcss_patches(description)
  values('Update location for instructor <PERSON> $Id: 39_modify_instructor_057326_gcss-723.sql 2994 2014-11-17 19:55:47Z memes $');
