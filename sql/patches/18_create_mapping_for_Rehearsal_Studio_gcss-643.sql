--
-- Add activity mapping for 'Rehearsal Studio'
--
-- Applicable to:-
--  All: required to be applied once
--
-- Steps:
--  1. Execute this script once, just in case the rules change again in the
--     future
--
--
-- $Id: 18_create_mapping_for_Rehearsal_Studio_gcss-643.sql 2811 2014-05-06 06:36:44Z memes $

whenever sqlerror exit sql.sqlcode rollback;

insert into stg_activity_map(activity_id_src, activity_name_src,
                             activity_id_target, activity_name_target)
  values('22', 'Rehearsal Studio', '0', 'Rehearsal');

-- Update patch log
insert into gcss_patches(description)
  values('Add activity mapping for Rehearsal Studio $Id: 18_create_mapping_for_Rehearsal_Studio_gcss-643.sql 2811 2014-05-06 06:36:44Z memes $');
