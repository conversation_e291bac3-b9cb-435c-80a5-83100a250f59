--
-- Cancel Open Office appointments for instructor 087172 at studio 811
--
-- Applicable to:-
--  Production: required to be applied once
--  QA: required to be applied as needed to confirm resolution
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to update appointments
--
-- $Id: 25_cancel_open_office_appts_for_instructor_087172_at_811_gcss-693.sql 2963 2014-08-19 02:01:16Z memes $

whenever sqlerror exit sql.sqlcode rollback;

-- Update the appointments
update appointment
  set canceled = 'Y',
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where instructor_id = (select instructor_id from instructor where external_id = '087172')
   and trunc(start_time) >= trunc(sysdate)
   and profile_id = (select profile_id from location where external_id = '811')
   and activity_id = 6;

-- Update patch log
insert into gcss_patches(description)
  values('Cancel Open Office appts for instructor <PERSON> at 811 $Id: 25_cancel_open_office_appts_for_instructor_087172_at_811_gcss-693.sql 2963 2014-08-19 02:01:16Z memes $');
