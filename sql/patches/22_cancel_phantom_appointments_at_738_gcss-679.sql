--
-- Cancel future appointments in series 76492 and 76493 so that the timeslot and
-- room becomes available for booking again.
--
-- Applicable to:-
--  Production: required to be applied once
--  QA: required to be applied as needed to confirm resolution
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to cancel phantom appointments at studio 738
--
-- $Id: 22_cancel_phantom_appointments_at_738_gcss-679.sql 2927 2014-07-17 22:43:29Z memes $

whenever sqlerror exit sql.sqlcode rollback;

-- Cancel future appointments in series 76492 and 76493
update appointment
  set canceled = 'Y',
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where appointment_series_id in (76492, 76493)
   and trunc(start_time) >= trunc(sysdate);

-- Update patch log
insert into gcss_patches(description)
  values('Cancel phantom appointments at 738 $Id: 22_cancel_phantom_appointments_at_738_gcss-679.sql 2927 2014-07-17 22:43:29Z memes $');
