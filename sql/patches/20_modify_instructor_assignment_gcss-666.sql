--
-- Modify instructors with employee id 083499 to studio 157
--
-- Applicable to:-
--  Production: required to be applied once
--  QA: required to be applied as needed to confirm resolution
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to update location association for instructor 083499
--
-- $Id: 20_modify_instructor_assignment_gcss-666.sql 2901 2014-07-08 22:52:42Z memes $

whenever sqlerror exit sql.sqlcode rollback;

-- Update the instructor associations
update instructor
  set location_id = (select location_id from location where external_id = '157'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '083499';

-- Update patch log
insert into gcss_patches(description)
  values('Update location for instructor <PERSON> $Id: 20_modify_instructor_assignment_gcss-666.sql 2901 2014-07-08 22:52:42Z memes $');
