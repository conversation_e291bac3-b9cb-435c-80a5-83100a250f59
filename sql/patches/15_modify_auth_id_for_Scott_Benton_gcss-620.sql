--
-- Modify authentication id for <PERSON>
--
-- Applicable to:-
--  Production: required to be applied once
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to modify authentication id
--
-- $Id: 15_modify_auth_id_for_<PERSON>_<PERSON>_gcss-620.sql 2765 2014-03-28 17:58:07Z memes $

whenever sqler<PERSON>r exit sql.sqlcode rollback;

-- Modify the person record for <PERSON>
update person
  set auth_id = 'Scott<PERSON>Benton',
      updated = systimestamp,
      version = version + 1
 where auth_id = '065728';

-- Update patch log
insert into gcss_patches(description)
  values('Modify <PERSON> authentication id back to Scott.Benton $Id: 15_modify_auth_id_for_<PERSON>_<PERSON>_gcss-620.sql 2765 2014-03-28 17:58:07Z memes $');
