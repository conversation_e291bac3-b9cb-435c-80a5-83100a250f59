--
-- This SQL is used to update data migration mapping for sparring partner.
--
-- Applicable to:-
--  All: required to be applied once
--
-- Steps:
--  1. Execute this script once, just in case the rules change again in the
--     future
--
--
-- $Id: 11_update_sparring_partner_mapping_TimeTrade_gcss-587.sql 2569 2014-02-18 05:12:06Z memes $

whenever sqlerror exit sql.sqlcode rollback;

-- Modify data migration mapping - add source id/name for Sparring Partner
update stg_activity_map
  set activity_id_src = 20,
      activity_name_src = 'Sparring Partner'
 where activity_id_target = 16;
 
-- Update patch log
insert into gcss_patches(description)
  values('Modify TT mapping for Sparring Partner $Id: 11_update_sparring_partner_mapping_TimeTrade_gcss-587.sql 2569 2014-02-18 05:12:06Z memes $');
