--
-- This SQL is used to create a new instructor entity for <PERSON><PERSON> in studio
-- 199.
--
-- Applicable to:-
--  PROD/QA: required to be applied once
--
-- Steps:
--  1. Execute this script once since it will create a new instructor every time
--
--
-- $Id: 14_add_robert_duran_(199)_as_instructor_gcss-608.sql 2653 2014-03-12 23:00:26Z memes $

whenever sqlerror exit sql.sqlcode rollback;

-- Create an empty availability record for <PERSON>'s instructor record
insert into availability (availability_id, version, updated, updated_by,
                          site_id, monday_start_time, monday_end_time,
                          tuesday_start_time, tuesday_end_time,
			  wednesday_start_time, wednesday_end_time,
			  thursday_start_time, thursday_end_time,
			  friday_start_time, friday_end_time,
			  saturday_start_time, saturday_end_time,
			  sunday_start_time, sunday_end_time)
  values(availability_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, null, null, null, null, null,
         null, null, null, null, null, null, null, null, null);

-- Create a new person record for <PERSON> in-case instructor login is ever
-- required and don't want to allow sharing of employee/instructor credentials
insert into person(person_id, version, updated, updated_by, external_id,
                   first_name, last_name, email)
  values(person_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), null, 'Robert', 'Duran',
         '<EMAIL>');
-- Create new instructor record for Robert Duran using an existing person entity
insert into instructor(instructor_id, version, updated, updated_by, site_id,
                       status, enabled, person_id, location_id,
                       availability_id, external_id, external_source)
  values(instructor_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, 'A', 'Y',
         (select max(person_id) from person where email = '<EMAIL>'),
	 (select location_id from location where external_id = '199'),
	 (select max(availability_id) from availability), null, null);
 
-- Update patch log
insert into gcss_patches(description)
  values('Add Robert Duran as instructor at 199 $Id: 14_add_robert_duran_(199)_as_instructor_gcss-608.sql 2653 2014-03-12 23:00:26Z memes $');
