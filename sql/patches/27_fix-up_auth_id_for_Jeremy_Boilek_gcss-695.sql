--
-- Modify authentication id for <PERSON>
--
-- Applicable to:-
--  Production: required to be applied once
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to modify authentication id
--
-- $Id: 

whenever sqler<PERSON>r exit sql.sqlcode rollback;

-- Modify the person record for <PERSON>
update person
  set auth_id = 'jeremy.boilek',
      updated = systimestamp,
      version = version + 1
 where email = '<EMAIL>';

-- Update patch log
insert into gcss_patches(description)
  values('Modify <PERSON> authentication id  $Id: 27_fix-up_auth_id_for_<PERSON>_<PERSON>_gcss-695.sql 2968 2014-08-20 21:09:38Z memes $');
