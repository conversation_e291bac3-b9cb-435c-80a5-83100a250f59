--
-- Modify authentication and roles for <PERSON>
--
-- Applicable to:-
--  Production: required to be applied once
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to modify authentication
--
-- $Id: 26_modify_auth_for_<PERSON>_<PERSON>ilek_gcss-695.sql 2967 2014-08-19 19:15:24Z memes $

whenever sqlerror exit sql.sqlcode rollback;

-- Modify the person record for <PERSON>; should only match one row - check before commit
update person
  set auth_id = 'jboilek',
      email = '<EMAIL>',
      updated = systimestamp,
      version = version + 1
 where person_id = (select p.person_id
                      from person p
                        inner join employee e on
                          p.person_id = e.person_id
                     where lower(p.first_name||p.last_name) = 'jeremy<PERSON>ile<PERSON>');
-- Remove existing roles
delete from person_role
  where person_id = (select person_id from person where auth_id = 'jboilek');

-- Add site admin role
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1,
         (select person_id from person where auth_id = 'jboilek'),
         (select role_id from role where role_name = 'Site Admin'), null);

-- Update patch log
insert into gcss_patches(description)
  values('Modify Jeremy Boilek authentication $Id: 26_modify_auth_for_Jeremy_Boilek_gcss-695.sql 2967 2014-08-19 19:15:24Z memes $');
