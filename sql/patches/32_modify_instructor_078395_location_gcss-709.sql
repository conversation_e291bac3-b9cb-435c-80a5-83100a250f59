--
-- Modify instructor with employee id 078395 to studio 777
--
-- Applicable to:-
--  Production: required to be applied once
--  QA: required to be applied as needed to confirm resolution
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to update location association for instructor 078395
--
-- $Id: 
whenever sqlerror exit sql.sqlcode rollback;

-- Update the instructor associations
update instructor
  set location_id = (select location_id from location where external_id = '777'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '078395';

-- Update patch log
insert into gcss_patches(description)
  values('Update location for instructor <PERSON> $Id: 32_modify_instructor_078395_location_gcss-709.sql 2977 2014-09-28 14:06:11Z memes $');
