--
-- Create timeoff table to set time off for instructors GCSS
--
-- Applicable to:-
--  All: apply
--
-- Steps:-
--  1. Execute this script once, for create table, constraints and indices, etc.
--
-- $Id: 13_create_time_off_table_gcss-590.sql $

whenever sqlerror continue;

--create sequence
drop sequence timeoff_id_seq;

create sequence timeoff_id_seq start with 0 increment by 1 maxvalue
  9999999999999999999999999999 minvalue 0 cache 20 ;
  
--create table
drop table timeoff cascade constraints ;

create
  table timeoff
  (
	timeoff_id            number not null ,
    instructor_id         number not null ,
    start_time            timestamp with time zone not null ,
    end_time              timestamp with time zone not null ,
    version               number not null ,
    updated               timestamp with local time zone not null ,
    updated_by            number not null
  )
  logging ;
  
comment on table timeoff is 'Set time off for instructors' ;

--create constraints
alter table timeoff add constraint timeoff_pk primary key
  (
    timeoff_id
  )
  ;
  
alter table timeoff add constraint timeoff_instructor_fk foreign key (
instructor_id ) references instructor ( instructor_id ) not deferrable ;

--create indices
create index timeoff_instructor_id_idx on timeoff (instructor_id) online;
create index timeoff_start_time_idx on timeoff (start_time) online;
create index timeoff_char_start_time_idx on timeoff (to_char(start_time, 'MM/DD/YYYY')) online;
create index timeoff_munge_start_time_idx on timeoff (to_date(to_char(start_time, 'YYYY-MM-DD'), 'YYYY-MM-DD')) online;
create index timeoff_end_time_idx on timeoff (end_time) online;
create index timeoff_char_end_time_idx on timeoff (to_char(end_time, 'MM/DD/YYYY')) online;
create index timeoff_munge_end_time_idx on timeoff (to_date(to_char(end_time, 'YYYY-MM-DD'), 'YYYY-MM-DD')) online;
create index timeoff_version_idx on timeoff (version) online;

-- Update patch log
insert into gcss_patches(description)
  values('Create timeoff table, constraints and indexes $Id: 13_create_time_off_table_gcss-590.sql');