--
-- Modify instructor with employee id 086182 to studio 809
--
-- Applicable to:-
--  Production: required to be applied once
--  QA: required to be applied as needed to confirm resolution
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to update location association for instructor 086182
--
-- $Id: 36_modify_instructor_086182_location_gcss-717.sql 2984 2014-11-04 17:39:27Z memes $
whenever sqlerror exit sql.sqlcode rollback;

-- Update the instructor associations
update instructor
  set location_id = (select location_id from location where external_id = '809'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '086182';

-- Update patch log
insert into gcss_patches(description)
  values('Update location for instructor <PERSON> $Id: 36_modify_instructor_086182_location_gcss-717.sql 2984 2014-11-04 17:39:27Z memes $');
