--
-- Modify instructor with employee id 073399 to studio 809
--
-- Applicable to:-
--  Production: required to be applied once
--  QA: required to be applied as needed to confirm resolution
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to update location association for instructor 073399
--
-- $Id: 37_modify_instructor_073399_location_gcss-719.sql 2986 2014-11-05 16:53:20Z memes $
whenever sqlerror exit sql.sqlcode rollback;

-- Update the instructor associations
update instructor
  set location_id = (select location_id from location where external_id = '323'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '073399';

-- Update patch log
insert into gcss_patches(description)
  values('Update location for instructor <PERSON> $Id: 37_modify_instructor_073399_location_gcss-719.sql 2986 2014-11-05 16:53:20Z memes $');
