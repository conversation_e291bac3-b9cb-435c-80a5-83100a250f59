--
-- Modify instructor with employee id 085767 to studio 809
--
-- Applicable to:-
--  Production: required to be applied once
--  QA: required to be applied as needed to confirm resolution
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to update location association for instructor 085767
--
-- $Id: 28_modify_instructor_085767_location_gcss-696.sql 2969 2014-08-21 15:12:48Z memes $

whenever sqlerror exit sql.sqlcode rollback;

-- Update the instructor associations
update instructor
  set location_id = (select location_id from location where external_id = '822'),
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where external_id = '085767';

-- Update patch log
insert into gcss_patches(description)
  values('Update location for instructor <PERSON> $Id: 28_modify_instructor_085767_location_gcss-696.sql 2969 2014-08-21 15:12:48Z memes $');
