--
-- Alter the Appointment and Appointment_Customer Table to  Make the Room_ID and Appointment_Id foregien keys have on delete cascade action
--  This is to fix the issue JIRA- GSSP-151
--
-- Applicable to:-
--  Production: required to be applied once
--  All others: do not apply
--
-- 
--


whenever sq<PERSON><PERSON>r exit sql.sqlcode rollback;


ALTER TABLE APPOINTMENT
DROP CONSTRAINT APPOINTMENT_ROOM_FK;

ALTER TABLE Appointment
ADD CONSTRAINT APPOINTMENT_ROOM_FK
FOREIGN KEY (Room_Id) 
REFERENCES Room(Room_Id)
ON DELETE CASCADE;

ALTER TABLE APPOINTMENT_CUSTOMERS
DROP CONSTRAINT APPT_CUSTOMERS_APPT_FK;

ALTER TABLE APPOINTMENT_CUSTOMERS
ADD CONSTRAINT APPT_CUSTOMERS_APPT_FK
FOREIGN KEY (APPOINTMENT_ID) 
REFERENCES APPOINTMENT(APPOINTMENT_ID)
ON DELETE CASCADE;
