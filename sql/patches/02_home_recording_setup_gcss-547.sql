--
-- This SQL is used to rename 'Protools Lesson' as 'Home Recording' and inform
-- data migration mapping of the changes.
--
-- Applicable to:-
--  All: required to be applied once
--
-- Steps:
--  1. Execute this script once, just in case the rules change again in the
--     future
--
--
-- $Id: 02_home_recording_setup_gcss-547.sql 2496 2014-01-17 23:17:40Z memes $

whenever sqlerror exit sql.sqlcode rollback;

-- Rename the existing 'Protools Lesson' activity to 'Home Recording'
update activity
  set activity_name = 'Home Recording',
      minimum_duration = 60,
      maximum_duration = 60
 where activity_id = 15;

-- Disable 'Logic Lesson'
update activity
  set enabled = 'N'
 where activity_id = 14;

-- Modify data migration mappings; <PERSON><PERSON> Lesson, Logic Lesson and Recording
-- all to map to 'Home Recording' activity
update stg_activity_map
  set activity_id_target = 15,
      activity_name_target = 'Home Recording'
 where activity_id_src in (18, 19, 21);

-- Update patch log
insert into gcss_patches(description)
  values('Handle activity change for Home Recording $Id: 02_home_recording_setup_gcss-547.sql 2496 2014-01-17 23:17:40Z memes $');
