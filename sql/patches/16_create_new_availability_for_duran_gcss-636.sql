--
-- Create a duplicate availability record for <PERSON> so that each
-- instructor has a unique availability record
--
-- Applicable to:-
--  Production: required to be applied once
--  All others: do not apply
--
-- Steps:
--  1. Execute this script to create a new availability record and assign to
--     <PERSON>
--
-- $Id: 16_create_new_availability_for_duran_gcss-636.sql 2800 2014-04-23 16:52:09Z memes $

whenever sqlerror exit sql.sqlcode rollback;

-- Clone the existing record with id 996
insert into availability (availability_id, version, updated, updated_by,
                          site_id, monday_start_time, monday_end_time,
                          tuesday_start_time, tuesday_end_time,
                          wednesday_start_time, wednesday_end_time,
                          thursday_start_time, thursday_end_time,
                          friday_start_time, friday_end_time,
                          saturday_start_time, saturday_end_time,
                          sunday_start_time, sunday_end_time,
                          external_id)
  select availability_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), site_id,
         monday_start_time, monday_end_time,
         tuesday_start_time, tuesday_end_time,
         wednesday_start_time, wednesday_end_time,
         thursday_start_time, thursday_end_time,
         friday_start_time, friday_end_time,
         saturday_start_time, saturday_end_time,
         sunday_start_time, sunday_end_time, null
    from availability
   where availability_id = 996;

-- Modify Robert Duran's instructor record with id 974
update instructor
  set availability_id = availability_id_seq.currval,
      version = version + 1,
      updated = systimestamp,
      updated_by = (select min(person_id) from person)
 where instructor_id = 974;

-- Update patch log
insert into gcss_patches(description)
  values('Create new availability record for Robert Duran $Id: 16_create_new_availability_for_duran_gcss-636.sql 2800 2014-04-23 16:52:09Z memes $');
