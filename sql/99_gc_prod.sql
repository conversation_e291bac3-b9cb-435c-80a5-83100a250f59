--
-- Prepare production GC environment
--
-- This file is NOT dependent on any other processing and can be executed during
-- database setup.
--

-- Add people that are not automatically processed by EDW import
--
-- <PERSON> as Site Admin
insert into person (person_id, version, updated, updated_by, first_name,
                    last_name, email, phone, external_id, auth_id)
  values(person_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), '<PERSON>', '<PERSON>',
         '<EMAIL>', null, null, 'Scott<PERSON>Benton');
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name = 'Site Admin'), null);

-- <PERSON> as Site Admin
insert into person (person_id, version, updated, updated_by, first_name,
                    last_name, email, phone, external_id, auth_id)
  values(person_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), '<PERSON>', '<PERSON><PERSON><PERSON>',
         'sbar<PERSON><PERSON>@guitarcenter.com', null, null, '<PERSON><PERSON><PERSON><PERSON>');
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name = 'Site Admin'), null);

-- Howard Weinstein as Site Admin
insert into person (person_id, version, updated, updated_by, first_name,
                    last_name, email, phone, external_id, auth_id)
  values(person_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 'Howard', 'Weinstein',
         '<EMAIL>', null, null, 'howard.weinstein');
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name = 'Site Admin'), null);

-- Jeff Steinmetz as Site Admin
insert into person (person_id, version, updated, updated_by, first_name,
                    last_name, email, phone, external_id, auth_id)
  values(person_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 'Jeff', 'Steinmetz',
         '<EMAIL>', null, null, 'jsteinmetz');
insert into person_role (person_role_id, version, updated, updated_by, site_id,
                         person_id, role_id, location_id)
  values(person_role_id_seq.nextval, 0, systimestamp,
         (select min(person_id) from person), 1, person_id_seq.currval,
         (select role_id from role where role_name = 'Site Admin'), null);
