/*
 * package com.guitarcenter.scheduler.integration.listner;
 * 
 * import javax.annotation.PostConstruct;
 * 
 * import org.hibernate.SessionFactory; import
 * org.hibernate.event.service.spi.EventListenerRegistry; import
 * org.hibernate.event.spi.EventType; import
 * org.hibernate.internal.SessionFactoryImpl; import
 * org.springframework.beans.factory.annotation.Autowired; import
 * org.springframework.beans.factory.annotation.Qualifier; import
 * org.springframework.stereotype.Component;
 * 
 * 
 * @Component public class HibernateEventWiring {
 * 
 * @Autowired
 * 
 * @Qualifier("sessionFactory") private SessionFactory sessionFactory;
 * 
 * 
 * private SaveUpdateEventListener listener = new SaveUpdateEventListener();
 * 
 * @PostConstruct public void registerListeners() { EventListenerRegistry
 * registry = ((SessionFactoryImpl)
 * sessionFactory).getServiceRegistry().getService(
 * EventListenerRegistry.class);
 * registry.getEventListenerGroup(EventType.POST_COMMIT_INSERT).appendListener(
 * listener);
 * registry.getEventListenerGroup(EventType.POST_COMMIT_UPDATE).appendListener(
 * listener); } }
 */