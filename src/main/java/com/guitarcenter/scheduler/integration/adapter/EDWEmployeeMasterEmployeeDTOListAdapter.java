package com.guitarcenter.scheduler.integration.adapter;

import java.util.Scanner;

import org.apache.commons.lang.StringUtils;

import com.guitarcenter.scheduler.integration.dto.EmployeeDTO;

/**
 * Converts a Readable source into a List of EmployeeDTO instances.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public class EDWEmployeeMasterEmployeeDTOListAdapter<T, V>
    extends EDWDTOListAdapter<T, V> {
    
    /**
     * Defines the system property key for an override that if true will prevent
     * the authentication id of employees being updated from EDW file.
     * 
     * Used in DEV and QA environments to avoid resetting auth_id that has been
     * manually mapped to a specific value.
     */
    public static final String ENV_KEY_GCSS_AUTH_ID_PRESERVE =
        "gcss.preserveAuthId";
    
    public EDWEmployeeMasterEmployeeDTOListAdapter(T externalData) {
        super(externalData);
    }
    
    /**
     * Returns an EmployeeDTO built from the values in the supplied parser.
     * 
     * @return instance of EmployeeD<PERSON> or null
     */
    @SuppressWarnings("unchecked")
    @Override
    protected V handleLine(Scanner parser) {
        /* The file has been split into fields, but not all fields are relevant
         * to scheduler. Build a DTO using the known fields and let the business
         * logic handle validation.
         */
        EmployeeDTO employee = new EmployeeDTO();
        employee.setExternalSource(EDW_SOURCE_VALUE);
        
        /* EMP_SRCNBR; employee id in system of record becomes external_id
         */
        String value = parser.next();
        if (StringUtils.isNotBlank(value)) {
            employee.setExternalId(value.trim());
        }
        
        /* EMP_FST_NAME; first name
         */
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
            employee.setFirstName(value.trim());
        }
        
        /* EMP_MID_NAME; middle name is unused and is skipped
         */
        skipFields(parser, 1);
        
        /* EMP_LST_NAME; last name
         */
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
            employee.setLastName(value.trim());
        }
        
        /*
         * GSSP-284 Changes:Preferred Name is added as FirstName if not null(EMP_PREFER_NAME)  
         */
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
            employee.setFirstName(value.trim());
            }
        
        /* GSSP-284 Changes : Since Preferred Name is saving , skipFields are only 3 now as below.
         * EMP_NAME_SFX; suffix is skipped
         * EMP_WORK_PHN; work phone is unused, skip
         * EMP_WORK_EXT; work phone extension is unused, skip
         */
        skipFields(parser, 3);
        
        /* EMP_EMAIL; email
         */
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
            employee.setEmail(value.trim());
        }
        
        /* EMP_STS_CD; status
         * Status is handled specially because there is a valid stat code for
         * empty string in POS (Prospect). Treat a null as empty for the
         * purposes of status.
         */
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
            employee.setStatus(value.trim());
        }
        
        /* EMP_ACTN_CD; unknown but looks like cost center, skip
         * HR_EFF_DT; HR effective date?, skip
         * HR_EXP_DT; HR expiration date?, skip
         */
        skipFields(parser, 3);

        /* EMP_CMPY_CD; company code - should match with a Site external id
         */
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
            employee.setSite(value.trim());
        }
        
        /* EMP_PYGRP_CD; skip
         * EMP_HIRE_DT; skip
         * EMP_REHIRE_DT; skip
         * EMP_SENRTY_DT; skip
         * EMP_TERM_DT; skip
         * REG_SRCNUM; skip
         * DST_SRCNUM; skip
         */
        skipFields(parser, 7);
        
        /* PHY_LOC_SRCNBR; physical location? use as studio/store number
         */
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
            employee.setStudio(value.trim());
        }
        
        /* RPT_LOC_SRCNBR; report location? skip
         */
        skipFields(parser, 1);
        
        /* JOB_SRCNBR; job code is an integer
         * Note: latest EDW has a file where the job code is blank and cannot
         * be handled as an integer.
         */
        if (parser.hasNextInt()) {
            employee.setJobCode(parser.nextInt());
        }
        
        /* JOB_DESC; skip
         * JOB_ENT_DT; skip
         * JOB_WRK_COMP_CD; skip
         * JOB_GCPRO_FLG; skip
         * MGR_LVL_CD; skip
         * MGR_LVL_DESC; skip
         * HR_DEPT_SRCNBR; skip
         * HR_DEPT_DESC; skip
         * HR_DEPT_LOC; skip
         * HR_DEPT_LVL; skip
         * MGR_SRCNBR; skip
         * MGR_LST_NAME; skip
         * MGR_FST_NAME; skip
         * MGR_MID_NAME; skip
         * SUPV_SRCNBR; skip
         * SUPV_LST_NAME; skip
         * SUPV_FST_NAME; skip
         * SUPV_MID_NAME; skip
         * STD_HRS; skip
         * FULL_PART_TM_CD; skip
         * EMP_REG_TEMP_CD; skip
         * AD_DOMAIN_NAME; skip
         */
        skipFields(parser, 22);
         
        /* AD_LOGIN_ID; the id used for authentication
         */
        value = parser.next();
        if (StringUtils.isNotBlank(value) &&
            !Boolean.getBoolean(ENV_KEY_GCSS_AUTH_ID_PRESERVE)) {
            employee.setAuthId(value.trim());
        }
        
        /* The following fields are all skipped:-
         * 
         * FEDERATION_ID; skip
         * STR_MENU_LVL; skip
         * HLDY_SCHD_CD; skip
         * SHIFT_CD; skip
         * LEAD_CD; skip
         * LOAD_DT; skip
         * LST_UPDT; skip
         */
        parser.nextLine();
        
        return (V) employee;
    }

}
