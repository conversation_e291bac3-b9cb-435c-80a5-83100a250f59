package com.guitarcenter.scheduler.integration.adapter;

import java.util.HashMap;
import java.util.Map;
import java.util.Scanner;
import java.util.regex.Pattern;

import org.apache.commons.lang.StringUtils;

import com.guitarcenter.scheduler.dto.LocationDTO;

/**
 * Converts a Readable source into a List of LocationDTO instances.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public class EDWStudioMasterLocationDTOListAdapter<T, V>
    extends EDWDTOListAdapter<T, V> {
    
    /**
     * EDW file uses a slightly different specification for GC Studios site than
     * other external sources. This map contains any known re-writing that
     * should happen for an EDW company id -> Site external id.
     */
    private static final Map<String, String> EDW_SCHEDULER_SITE_MAP =
        new HashMap<String, String>();
    
    static {
        EDW_SCHEDULER_SITE_MAP.put("GC", "GCS");
    }
    
    /**
     * <PERSON><PERSON> to match studio flag to confirm studio status.
     */
    public static final Pattern STUDIO_LOCATION_FLAG_CONFIRM =
        Pattern.compile("^[yY1]$");
    
    public EDWStudioMasterLocationDTOListAdapter(T externalData) {
        super(externalData);
    }
    
    /**
     * Returns a LocationDTO built from the values in the supplied parser. Only
     * records that have Studio flag set to Y will be processed.
     * 
     * @return instance of LocationDTO or null
     */
    @SuppressWarnings("unchecked")
    @Override
    protected V handleLine(Scanner parser) {
        /* The file has been split into fields, but not all fields are relevant
         * to scheduler. Build a DTO using the known fields and let the business
         * logic handle validation.
         */
        LocationDTO location = new LocationDTO();
        location.setExternalSource(EDW_SOURCE_VALUE);
        
        /* CORP_SRCNBR; skip
         * CORP_DESC; skip
         */
        skipFields(parser, 2);
        
        /* SLS_CMPY_SRCNBR; abbreviated form of company; will be used as a site
         * indicator
         */
        String value = sanitiseSiteId(parser.next());
        if (StringUtils.isNotBlank(value)) {
            location.setSite(value.trim());
        }
        
        /* SLS_CMPY_DESC; skip
         * INV_CMPY_SRCNBR; skip
         * INV_CMPY_DESC; skip
         * ENTPR_DIV_SRCNBR; skip
         * ENTPR_DIV_DESC; skip
         * ENTPR_REG_SRCNBR; skip
         * ENTPR_REG_DESC; skip
         * ENTPR_REG_MGR_SRCNBR; skip
         * ENTPR_REG_MGR_NAME; skip
         * ENTPR_DST_SRCNBR; skip
         * ENTPR_DST_DESC; skip
         * ENTPR_DST_MGR_SRCNBR; skip
         * ENTPR_DST_MGR_NAME; skip
         */
        skipFields(parser, 13);
        
        /* ENTPR_STR_SRCNBR; store/studio number
         */
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
            location.setExternalId(value.trim());
        }
        
        /* ENTPR_STR_DESC; skip
         * ENTPR_STR_SDESC; skip
         */
        skipFields(parser, 2);
        
        /* ENTPR_STR_NAME; display name of store/studio
         */
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
            location.setName(value.trim());
        }
        
        /* ENTPR_STR_MGR_SRCNBR; skip
         * ENTPR_STR_MGR_NAME; skip
         */
        skipFields(parser, 2);
        
        /* ENTPR_STR_ADDR1; location address line 1
         */
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
            location.setAddress1(value.trim());
        }
        
        /* ENTPR_STR_ADDR2; location address line 2
         */
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
            location.setAddress2(value.trim());
        }
        
        /* ENTPR_STR_ADDR3; skip - data model does not have a third line for
         * address
         * ENTPR_STR_CNTY; address county?, skip as not in data model
         */
        skipFields(parser, 2);
        
        /* ENTPR_STR_CTY; location city
         */
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
            location.setCity(value.trim());
        }
        
        /* ENTPR_STR_ST; location state
         */
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
            location.setState(value.trim());
        }
        
        /* ENTPR_STR_CNTRY; location country
         */
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
            location.setCountry(value.trim());
        }
        
        /* ENTPR_STR_ZIPCD; location zip
         */
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
            location.setZip(value.trim());
        }
        
        /* LATITUDE
         * LONGITUDE; not used in data model but may revisit for timezone
         * XXX: MEmes - follow-up for TZ determination
         */
        skipFields(parser, 2);
        
        /* ENTPR_STR_PHN1; use as store phone number
         */
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
            location.setPhone(value.trim());
        }

        /* ENTPR_STR_PHN1_DESC; skip
         */
        skipFields(parser, 1);
        
        /* If the first phone number field is blank, then try to get from phone
         * field 2.
         */
        if (StringUtils.isBlank(value)) {
            /* ENTPR_STR_PHN2 - other phone
             * ENTPR_STR_PHN2_DESC; skip
             */
            value = parser.next();
            if (StringUtils.isNotBlank(value)) {
                location.setPhone(value.trim());
            }
            skipFields(parser, 1);
        } else {
            /* ENTPR_STR_PHN2; skip
             * ENTPR_STR_PHN2_DESC; skip
             */
            skipFields(parser, 2);
        }
        
        /* ENTPR_STR_FAX; fax number
         */
        value = parser.next();
        if (StringUtils.isNotBlank(value)) {
            location.setFax(value.trim());
        }
        
        /* ENTPR_STR_TYPE_SRCNBR; skip
         * ENTPR_STR_TYPE_DESC; skip
         * ENTPR_FIN_STR_TYP_SRCNBR; skip
         * ENTPR_STR_OPN_DT; skip
         * ENTPR_STR_CLS_DT; skip
         * ENTPR_STR_GRAND_OPN_DT; skip
         * ENTPR_STR_COMP_DT; skip
         * ENTPR_STR_COMP_FLG; skip
         * ENTPR_STR_ACTV_FLG; skip
         * ENTPR_STR_GCPRO_FLG; skip
         * ENTPR_STR_SELLNG_FLG; skip
         */
        skipFields(parser, 11);
        
        /* ENTPR_STUDIO_FLG; flag for studio
         */
        boolean isStudio =
            STUDIO_LOCATION_FLAG_CONFIRM.matcher(parser.next().trim()).matches();
        
        /* Skip all the remaining fields...
         * STR_SLS_SQFT; skip
         * STR_STK_SQFT; skip
         * STR_USABLE_SQFT; skip
         * LOAD_DT; skip
         * LST_UPDT; skip
         */
        parser.nextLine();
        
        return isStudio ? (V) location : null;
    }

    /**
     * Given a String containing an EDW company identifier, return a preferred
     * scheduler external site id to use instead. In the absence of a positive
     * matching preference, the original value will be returned.
     * 
     * @param edwCompanyId String containing the EDW company identifier
     * @return String containing the scheduler site external id to use instead,
     *         or edwCompanyId
     */
    private String sanitiseSiteId(String edwCompanyId) {
        if (EDW_SCHEDULER_SITE_MAP.containsKey(edwCompanyId)) {
            return EDW_SCHEDULER_SITE_MAP.get(edwCompanyId);
        } else {
            return edwCompanyId;
        }
    }
}
