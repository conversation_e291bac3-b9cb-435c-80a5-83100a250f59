package com.guitarcenter.scheduler.integration.adapter;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.regex.Pattern;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.WordUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.dto.CustomerDTO;
import com.guitarcenter.scheduler.dto.InstrumentDTO;
import com.guitarcenter.scheduler.integration.generated.EmailType;
import com.guitarcenter.scheduler.integration.generated.PhoneType;
import com.guitarcenter.scheduler.integration.generated.SyncCustomer;
import com.guitarcenter.scheduler.integration.generated.SyncCustomer.PurchaseDetails.GCStudio.LessonInstrument;
import com.guitarcenter.scheduler.integration.generated.SyncCustomer.AssociatedStores.StoreLocations;
import com.guitarcenter.scheduler.integration.generated.LocationType;
import com.guitarcenter.scheduler.integration.generated.ID;
import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;
import com.guitarcenter.scheduler.model.CustomerStatus;

/**
 * An adapter that knows how to convert an POS SyncCustomer record to an
 * instance of CustomerDTO.
 * 
 * Note that the interface is generic but this class is assuming specific types
 * will be used.POS
 *
 * <AUTHOR> Emes <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 * @param <T>
 */ 

public class SyncCustomerCustomerDTOAdapter<T, V> implements ExternalAdapter<T, V> {
    Logger log = LoggerFactory.getLogger(SyncCustomerCustomerDTOAdapter.class); 
    
    /**
     * Initial SyncCustomer XSD does not have a canonical entity that defines
     * the destination site for the data. In the absence of a (future) node that
     * determines the site, force the data to be ready for phase 1 GCS site.
     */
    public static final String GCS_SITE = "GCS";

    /**
     * Identified the source of the data update. In this case, the data will
     * always be POS
     */
    public static final String EXTERNAL_SOURCE_POS = "POS";
    
    /**
     * Defines the system property key for an email override value to use
     * instead of POS defined email address.
     * 
     * Used in DEV and QA environments to avoid sending email to real customer
     * addresses.
     */
    public static final String ENV_KEY_GCSS_EMAIL_OVERRIDE = "gcss.emailOverride";

    /**
     * Defines the value of TelephoneNumber/Type value that is of interest to
     * this adapter.
     */
    private static final String TELEPHONE_DAY_TYPE = "Day";

    /**
     * Defines the value of Email/Type value that is of interest to
     * this adapter.
     */
    private static final String EMAIL_ALL_TYPE = "All";

    /**
     * Define a Pattern that can be used to split a name into first and last
     * components.
     */
    private Pattern NAME_SPLIT_PATTERN = Pattern.compile("\\s+");
    
    /**
     * The SyncCustomer instance to use as a source of data to extract.
     */
    private SyncCustomer syncCustomer;
    
    /**
     * An instance of CustomerDTO that will be populated from external data.
     */
    private CustomerDTO customer;
    
    /**
     * The default constructor is private to prevent construction outside of
     * factory methods.
     */
    @SuppressWarnings("unused")
    private SyncCustomerCustomerDTOAdapter() {
    }
    
    /**
     * The constructor is protected to try to make sure the adapter is used via
     * an ExternalAdapterFactory method.
     * 
     * @param externalData the external data representation <em>expected to be
     *                     <strong>SyncCustomer</strong</em>
     */
    protected SyncCustomerCustomerDTOAdapter(T externalData) {
        if (log.isDebugEnabled()) {
            log.debug("created SyncCustomerAdapter for {}", externalData);
        }
        /* The factory should have enforced the requirement for externalData to
         * be a SyncCustomer instance; trust this and accept that a
         * ClassCastException will be thrown if this rule. 
         */
        this.syncCustomer = (SyncCustomer) externalData;
        this.customer = new CustomerDTO();
    }
    
    /**
     * Returns an instance of CustomerDTO populated with data extracted from
     * SyncCustomer instance supplied.
     * 
     * Note: although the method is marked as unchecked, in reality the returned
     * object is almost certainly a CustomerDTO instance due to the way the
     * factory chooses an ExternalDataAdapter.
     * 
     * @return an instance of CustomerDTO
     * @throws IntegrationServiceException
     */
    @SuppressWarnings("unchecked")
    @Override
    public V getEntity() {
        if (log.isDebugEnabled()) {
            log.debug("about to extract data from {}", syncCustomer);
        }
        addSite();
        addExternalIdentifiers();
        addCustomerName();
        addPhone();
        addEmail();
        addStatus();
        addInstruments();
        
        //Changes made for LES_19
        addBarCodeID();
        
        //Changes made for GSSP-220
        addPrimaryLocation();
        
        //POS-808 Add Customer LessonCount
        addLessonCounts();
        
      //POS-808 Add Customer LessonCount
        addLastBookedDate();
        
        addCustomerParentName();
        
        //POS-1001 Changes.
        addCustoerContractDay();
        
        
      
        addLastModificationDateTime();
        
        
        if (log.isDebugEnabled()) {
            log.debug("returning {}", customer);
        }
        
        
        return (V) customer;
    }
    
    /**
     * Returns a List of CustomerDTO instances, populated from the SyncCustomer
     * supplied.
     * 
     * Note: SyncCustomer records contain a single record, so the method will
     * always return an empty list or a list with one entry.
     * 
     * @return a List of CustomerDTOs
     * @throws IntegrationServiceException
     */
    @Override
    public List<V> getEntities()
        throws IntegrationServiceException
    {
        List<V> entities = new LinkedList<V>();
        V entity = getEntity();
        if (entity != null) {
            entities.add(entity);
        }
        if (log.isDebugEnabled()) {
            log.debug("returning {}", entities);
        }
        return entities;
    }
    
    /**
     * Find a matching Site in the system for the SyncCustomer record and adds
     * the identifier to the CustomerDTO instance.
     */
    private void addSite() {
        /* For now all POS records are for GCS site; there is nothing in the
         * current record that identifies this. Using hard-coded value.
         */
        customer.setSite(GCS_SITE);
        if (log.isDebugEnabled()) {
            log.debug("added site external id {} to customer",
                      customer.getSite());
        }
    }

    /**
     * Add external identifiers to the CustomerDTO record.
     */
    private void addExternalIdentifiers() {
        String enterpriseId =
            syncCustomer.getEnterpriseCustomerID() == null ?
                null : syncCustomer.getEnterpriseCustomerID().getEnterpriseCustomerID();
        if (StringUtils.isNotBlank(enterpriseId)) {
            customer.setExternalId(enterpriseId.trim());
        }
        customer.setExternalSource(EXTERNAL_SOURCE_POS);
        if (log.isDebugEnabled()) {
            log.debug("added external id {} and source {} to customer",
                      customer.getExternalId(), customer.getExternalSource());
        }
    }

    /**
     * Private helper to extract a name and add it to the supplied customer
     * record.
     * 
     * @param customer
     */
    private void addCustomerName() {
        String fullName = syncCustomer.getCustomerDetailsName();
        if (StringUtils.isNotBlank(fullName)) {
            String[] names = NAME_SPLIT_PATTERN.split(fullName.trim(), 2);
            customer.setFirstName(WordUtils.capitalizeFully(names[0]));
            if (names.length > 1) {
                customer.setLastName(WordUtils.capitalizeFully(names[1]));
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("added first '{}' and last '{}' names to customer",
                      customer.getFirstName(), customer.getLastName());
        }
    }
    
   

    /**
     * Add phone number to the CustomerDTO record.
     */
    private void addPhone() {
        if (syncCustomer.getContactDetails() != null) {
            List<PhoneType> phoneNumbers =
                syncCustomer.getContactDetails().getTelephoneNumberList();
            for (PhoneType phone: phoneNumbers) {
                if (phone.getTypeList().contains(TELEPHONE_DAY_TYPE)) {
                    if (StringUtils.isNotBlank(phone.getPhone())) {
                        customer.setPhone(phone.getPhone().trim());
                        break;
                    }
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("added phone number {} to customer", customer.getPhone());
        }
    }

    /**
     * Add email to the CustomerDTO record.
     */
    private void addEmail() {
        if (StringUtils.isNotBlank(System.getProperty(ENV_KEY_GCSS_EMAIL_OVERRIDE))) {
            customer.setEmail(System.getProperty(ENV_KEY_GCSS_EMAIL_OVERRIDE).trim());
        } else if (syncCustomer.getContactDetails() != null) {
            List<EmailType> emails =
                syncCustomer.getContactDetails().getEMailAddressList();
            for (EmailType email: emails) {
                if (email.getTypeList().contains(EMAIL_ALL_TYPE)) {
                    if (StringUtils.isNotBlank(email.getEmail())) {
                        customer.setEmail(email.getEmail().trim());
                        break;
                    }
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("added email {} to customer", customer.getEmail());
        }
    }

    /**
     * Add status to the CustomerDTO record.
     */
    private void addStatus() {
        /* Note that the method does not test for empty status, since there is
         * a valid status (Prospect) that has an empty string as a code in POS.
         * 
         * Default to Prospect status if not found
         */
        if (syncCustomer.getPurchaseDetails() != null &&
            syncCustomer.getPurchaseDetails().getGCStudio1() != null) {
            customer.setCustomerStatus(syncCustomer.getPurchaseDetails().getGCStudio1().getCustomerStatus().trim());
        }
        if (customer.getCustomerStatus() == null){
            customer.setCustomerStatus(CustomerStatus.CUSTOMER_STATUS_PROSPECT);
        }
        if (log.isDebugEnabled()) {
            log.debug("added status {} to customer", customer.getCustomerStatus());
        }
    }

    /**
     * Add instruments to the CustomerDTO record.
     */
    private void addInstruments() {
        if (syncCustomer.getPurchaseDetails() != null &&
            syncCustomer.getPurchaseDetails().getGCStudio1() != null) {
            List<LessonInstrument> instruments =
                syncCustomer.getPurchaseDetails().getGCStudio1().getLessonInstrumentList();
            for (LessonInstrument instrument: instruments) {
                if (StringUtils.isNotBlank(instrument.getID())) {
                    if (customer.getInstruments() == null) {
                        customer.setInstruments(new HashSet<InstrumentDTO>());
                    }
                    InstrumentDTO customerInstrument = new InstrumentDTO();
                    customerInstrument.setExternalId(instrument.getID());
                    customerInstrument.setInstrumentName(instrument.getDescription());
                    customer.getInstruments().add(customerInstrument);
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("added instruments {} to customer", customer.getInstruments());
        }
    }
    
    
  //Changes made for LES_19
    /**
     * Add external identifiers to the CustomerDTO record.
     */
    private void addBarCodeID() {
        String barCodeID =
            syncCustomer.getBarCodeID() == null ?
                null : syncCustomer.getBarCodeID().getBarCodeID();
        if (StringUtils.isNotBlank(barCodeID)) {
            customer.setBadgeNumber(barCodeID);
        }
        
        
        customer.setExternalSource(EXTERNAL_SOURCE_POS);
        if (log.isDebugEnabled()) {
            log.debug("added external id {} and source {} to customer",
                      customer.getExternalId(), customer.getExternalSource());
        }
    }
    
    /**
     * Private helper to extract a name and add it to the supplied customer
     * record.
     * 
     * @param customer
     */
    private void addCustomerParentName() {
        /*String parentfullName = syncCustomer.getParentName();
        if (StringUtils.isNotBlank(parentfullName)) {
           customer.setParentFullName(parentfullName);
           System.out.println("parentfullName"+parentfullName);
        }*/
    	
    	/* if (syncCustomer.getPurchaseDetails() != null &&
    	            syncCustomer.getPurchaseDetails().getGCStudio1() != null 
    	            && syncCustomer.getPurchaseDetails().getGCStudio1().getParentName() != null) {
    	            customer.setParentFullName(syncCustomer.getPurchaseDetails().getGCStudio1().getParentName().trim());
    	            System.out.println("ParentName:"+customer.getParentName());
    	        }*/
    	
    	 String parentName =
    	            syncCustomer.getParentName() == null ?
    	                null : syncCustomer.getParentName().getParentName();
    	        if (StringUtils.isNotBlank(parentName)) {
    	            customer.setParentFullName(parentName);    	            
    	           /* System.out.println("customer.getExternalId()-->ParentName:"
    	            +customer.getExternalId() + "--->" + customer.getParentFullName());*/
    	        }
    	 
        if (log.isDebugEnabled()) {
            log.debug("added ParentName '{}' to customer",
                      customer.getParentFullName());
        }
    }
    
    //POS-1001
    private void addCustoerContractDay() {
     
    	String contractDay =
    	            syncCustomer.getContractDay() == null ?
    	                null : syncCustomer.getContractDay().getContractDay();
    	        if (StringUtils.isNotBlank(contractDay)) {
    	            customer.setContractDay(contractDay);    	            
    	            log.error("customer.getContractDay()-->getExternalId:" +customer.getExternalId() + "--->" + customer.getContractDay());
    	        }
    	 
        if (log.isDebugEnabled()) {
            log.debug("added ContractDay '{}' to customer",
                      customer.getContractDay());
        }
    }
    
    
    
    private void addLastModificationDateTime() {
    	
    	//Date date = new Date();  
     //   SimpleDateFormat formatter = new SimpleDateFormat("dd-MMM-yy HH:mm:ss.SSSS");  
	
        String lastModificationDateTime =
    	            syncCustomer.getLastModificationDateTime() == null ?
    	                null : syncCustomer.getLastModificationDateTime().getLastModificationDateTime();
    	        if (null != lastModificationDateTime) {
    	            customer.setPosUpdated(lastModificationDateTime);    	            
    	        }
    	 
        if (log.isDebugEnabled()) {
            log.debug("added lastModificationDateTime '{}' to lastModificationDateTime",
                      customer.getPosUpdated());
        }
    }
    
    //Changes made for GSSP-220
    
    private void addPrimaryLocation() {
    	
    	
  
    	List<StoreLocations> storeLocations = null;
    	
    	StoreLocations primaryLocationType = null;
    	    	
    	LocationType primaryLocation = null;
    	
    	ID locationId = null;
    	
    	String primaryLocationString = "";
        
    	if(null != syncCustomer.getAssociatedStores()  &&  null !=   syncCustomer.getAssociatedStores().getStoreLocationList())
    	{
    		storeLocations = syncCustomer.getAssociatedStores().getStoreLocationList();
    		
    		if(null !=  storeLocations &&  storeLocations.size() > 0)
    		{
    			primaryLocationType = storeLocations.get(0);
    			
    			if(null != primaryLocationType && null != primaryLocationType.getLocation() )
    			{
    				
    				primaryLocation = primaryLocationType.getLocation();
    				
    				if(null != primaryLocation  && null != primaryLocation.getID())
    				{
    					
    					locationId =  primaryLocation.getID();
    					
    					if(null != locationId && null != locationId.getString())
    					{
    						primaryLocationString = locationId.getString();
    					}
    				}
    			}
    			
    		}
    			
    	}
    	
        if (StringUtils.isNotBlank(primaryLocationString)) {
            customer.setLocation_external_id(primaryLocationString);
        }
        
        
       
        if (log.isDebugEnabled()) {
            log.debug("added locationId to customer",
                      customer.getLocation_external_id());
        }
    }
    
    
    /**
     * Add lesson Counts to the CustomerDTO record.
     */
    private void addLessonCounts() {
       
    	
        if (syncCustomer.getPurchaseDetails() != null &&
            syncCustomer.getPurchaseDetails().getGCStudio1() != null 
            && syncCustomer.getPurchaseDetails().getGCStudio1().getLessonCount() != null) {
            customer.setLessonCounts(syncCustomer.getPurchaseDetails().getGCStudio1().getLessonCount().trim());
        }
        if (customer.getLessonCounts() == null){
            customer.setLessonCounts("0");
        }
        if (log.isDebugEnabled()) {
            log.debug("added status {} to customer", customer.getLessonCounts());
        }
       
    }
    
    /**
     * Add lesson Counts to the CustomerDTO record.
     */
    private void addLastBookedDate() {
       
    	
        if (syncCustomer.getPurchaseDetails() != null &&
            syncCustomer.getPurchaseDetails().getGCStudio1() != null 
            && syncCustomer.getPurchaseDetails().getGCStudio1().getLastBookedDate() != null) {
            customer.setLastBookedDate(syncCustomer.getPurchaseDetails().getGCStudio1().getLastBookedDate().trim());
        }
       
        
        if (log.isDebugEnabled()) {
            log.debug("added getLastBookedDate {} to customer", customer.getLastBookedDate());
        }
       
    }
    
  
    
    
    
    
    
    
    
    
}
