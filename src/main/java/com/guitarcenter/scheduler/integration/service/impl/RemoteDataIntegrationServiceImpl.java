package com.guitarcenter.scheduler.integration.service.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;
import com.guitarcenter.scheduler.integration.service.RemoteDataIntegrationService;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;

/**
 * Implements a service that can connect to a remote server using SSH private
 * key exchange and retrieve data as an InputStream.
 * 
 * Note that the implementation makes heavy use of properties that must be
 * defined for each working environment. At GuitarCenter the key and username
 * will be different for each of DEV, QA and PROD environments.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */
@Component
public class RemoteDataIntegrationServiceImpl implements RemoteDataIntegrationService
{
    Logger log = LoggerFactory.getLogger(RemoteDataIntegrationServiceImpl.class);
    
    private static final String SFTP_TYPE = "sftp";
    
    /**
     * The hostname of the remote server
     */
    @Value("${edw.hostname}")
    private String hostname;
    
    /**
     * The username for connection to remote server
     */
    @Value("${edw.username}")
    private String username;
    
    /**
     * A path to the private SSH key to use for PKI authentication. All EDW at
     * GC will be performed without passwords.
     */
    @Value("${edw.privateKeyPath}")
    private String privateKeyPath;
    
    /**
     * A path to a known_hosts file that has been pre-populated for the
     * environment, to confirm remote server identity.
     */
    @Value("${edw.knownHostsPath}")
    private String knownHostsPath;
    
    /**
     * Configure JSch before making any connections.
     */
    static {
        /* This is the default, but specified here just in case that changes
         * in the future.
         */
        JSch.setConfig("StrictHostKeyChecking", "yes");
    }
    
    /**
     * Connects to a remote data source and returns an InputStream of the
     * contents of the file named.
     * 
     * @param filePath a String that identifies the resource to stream from the
     *        remote server; can be anything that is meaningful to the
     *        implementation. No error or validation checking occurs by the
     *        method, but the remote server may raise an exception if the
     *        filePath is invalid.
     * @return instance of InputStream linked to the contents of filePath
     * @throws IntegrationServiceException on any caught exception
     */
    @Override
    public InputStream getRemoteData(String filePath) throws IntegrationServiceException {
        if (log.isDebugEnabled()) {
            log.debug("attempting to retrieve {} from {}", filePath, hostname);
        }
       
        /* Create a buffer and stream the data from the remote site to memory
         */
        ByteArrayOutputStream buf = new ByteArrayOutputStream();
        ChannelSftp sftp = null;
        try {
            sftp = getChannel();
            try {
                if (log.isDebugEnabled()) {
                    log.debug("attempting transfer of {} from {}", filePath,
                              hostname);
                }
                sftp.get(filePath, buf);
            } catch (SftpException sftpe) {
                log.warn("Caught an SftpException retrieving data from {}:{}",
                         hostname, filePath, sftpe);
                throw new IntegrationServiceException("Problem retrieving data from " +
                                                      hostname, sftpe);
            }
        } finally {
            /* Release any channels and sessions that may be in use
             */
            if (sftp != null) {
                sftp.disconnect();
                try {
                    sftp.getSession().disconnect();
                } catch (JSchException jse) {
                    log.warn("Caught a JschException retrieving session", jse);
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("transfer completed, returning data");
        }
        return new ByteArrayInputStream(buf.toByteArray());
    }

    /**
     * Validate that the environment is suitable for remote SSH transfer
     * 
     * @return true if all required parameters are present, false otherwise
     */
    private boolean isValidEnvironment() {
        if (log.isDebugEnabled()) {
            log.debug("validating environment: hostname = {}, username = {}, " +
                      "privateKeyPath = {}, knownHostsPath = {}", hostname,
                      username, privateKeyPath, knownHostsPath);
        }
        boolean isValid = StringUtils.isNotBlank(hostname) &&
            StringUtils.isNotBlank(username) &&
            StringUtils.isNotBlank(privateKeyPath) &&
            StringUtils.isNotBlank(knownHostsPath) &&
            new File(privateKeyPath).canRead() &&
            new File(knownHostsPath).canRead();
        if (log.isDebugEnabled()) {
            log.debug("returning {}", isValid);
        }
        return isValid;
    }
    
    /**
     * Returns an SFTP channel ready for file retrieval
     * 
     * @return ChannelSftp for use by caller
     * @throws IntegrationServiceException if the channel could not be setup for
     *         any reason
     */
    private ChannelSftp getChannel()
        throws IntegrationServiceException
    {
        if (log.isDebugEnabled()) {
            log.debug("trying to setup channel for SFTP");
        }
        ChannelSftp channel = null;
        if (isValidEnvironment()) {
            try {
                JSch jsch = new JSch();
                jsch.addIdentity(privateKeyPath);
                jsch.setKnownHosts(knownHostsPath);
                Session session = jsch.getSession(username, hostname);
                session.connect();
                channel = (ChannelSftp) session.openChannel(SFTP_TYPE);
                channel.connect();
            } catch (JSchException jse) {
                log.warn("Caught a JSchException setting up EDW transfer", jse);
                throw new IntegrationServiceException("SSH setup issue", jse);
            }
        } else {
            throw new IntegrationServiceException("EDW integration properties are invalid");
        }
        if (log.isDebugEnabled()) {
            log.debug("returning {}", channel);
        }
        return channel;
    }
    
}
