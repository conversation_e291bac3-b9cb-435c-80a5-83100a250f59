package com.guitarcenter.scheduler.model;

import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Represents an employee of a studio
 */
@Entity
@Table(name = "EMPLOYEE")
@SequenceGenerator(name = "EMPLOYEE_ID_SEQ", sequenceName = "EMPLOYEE_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Employee implements java.io.Serializable {

	private static final long	serialVersionUID	= -660696588484746940L;
	private Long				employeeId;
	private long				version;
	private Person				updatedBy;
	private Location			location;
	private Person				person;
	private Date				updated;
	private String				status;
	private String				enterpriseStatus;
	private Site				site;
	private String				externalId;
	private String				externalSource;



	public Employee() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "EMPLOYEE_ID_SEQ")
	@Column(name = "EMPLOYEE_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getEmployeeId() {
		return this.employeeId;
	}



	public void setEmployeeId(Long employeeId) {
		this.employeeId = employeeId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person personByUpdatedBy) {
		this.updatedBy = personByUpdatedBy;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "LOCATION_ID", nullable = false)
	public Location getLocation() {
		return this.location;
	}



	public void setLocation(Location location) {
		this.location = location;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PERSON_ID", nullable = false)
	public Person getPerson() {
		return this.person;
	}



	public void setPerson(Person person) {
		this.person = person;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "STATUS", length = 512)
	public String getStatus() {
		return this.status;
	}



	public void setStatus(String status) {
		this.status = status;
	}



	@Column(name = "ENTERPRISE_STATUS", length = 512)
	public String getEnterpriseStatus() {
		return this.enterpriseStatus;
	}



	public void setEnterpriseStatus(String enterpriseStatus) {
		this.enterpriseStatus = enterpriseStatus;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}



	@Column(name = "EXTERNAL_SOURCE", length = 512)
	public String getExternalSource() {
		return this.externalSource;
	}



	public void setExternalSource(String externalSource) {
		this.externalSource = externalSource;
	}
	
	
	//Changes made for GSSP-209
	private static final Integer GCS_RVP_JOB_CODE =
	        new Integer(2021);
	    private static final Integer GCS_DM_JOB_CODE =
	        new Integer(1724);
	    private static final Integer GCS_ADM_JOB_CODE =
		        new Integer(2614);
		
	private static final Set<Integer> GCS_DM_RVP_JOB_CODES =
	    new HashSet<Integer>(Arrays.asList(GCS_RVP_JOB_CODE,
	    								   GCS_DM_JOB_CODE,GCS_ADM_JOB_CODE
	                                      ));
	
	/**
	 * Tests the supplied job code to determine if it represents an employee
	 * that should be recognised as an VP/DM.
	 * 
	 * @param jobCode integer job code to test
	 * @return true if the job code is for an instructor, false otherwise
	 */
	public static boolean isRVP_DMJobCode(int jobCode) {
	    return GCS_DM_RVP_JOB_CODES.contains(new Integer(jobCode));
	}

}
