/**
 * @Title: Timeoff.java
 * @Package com.guitarcenter.scheduler.model
 * @Description: TODO
 * Copyright: Copyright (c) 2014 
 * Company:
 * 
 * <AUTHOR>
 * @date Mar 10, 2014 3:48:37 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * @ClassName: Timeoff
 * @Description: TODO
 * <AUTHOR>
 * @date Mar 10, 2014 3:48:37 PM
 *
 */
@Entity
@Table(name = "TIMEOFF")
@SequenceGenerator(name = "TIMEOFF_ID_SEQ", sequenceName = "TIMEOFF_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Timeoff implements java.io.Serializable {
	private static final long	serialVersionUID	= 6718026907095237892L;
	private Long				timeoffId;
	private Instructor			instructor;
	private Date				startTime;
	private Date				endTime;
	private long				version;
	private Person				updatedBy;
	private Date				updated;
	
	/**
	  * Timeoff. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  */
	public Timeoff() {
	}
	
	/**
	 * getter method
	 * @return the timeoffId
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TIMEOFF_ID_SEQ")
	@Column(name = "TIMEOFF_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getTimeoffId() {
		return timeoffId;
	}
	/**
	 * setter method
	 * @param timeoffId the timeoffId to set
	 */
	public void setTimeoffId(Long timeoffId) {
		this.timeoffId = timeoffId;
	}
	/**
	 * getter method
	 * @return the instructor
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "INSTRUCTOR_ID", nullable = false)
	public Instructor getInstructor() {
		return instructor;
	}
	/**
	 * setter method
	 * @param instructor the instructor to set
	 */
	public void setInstructor(Instructor instructor) {
		this.instructor = instructor;
	}
	/**
	 * getter method
	 * @return the startTime
	 */
	@Column(name = "START_TIME", nullable = false)
	public Date getStartTime() {
		return startTime;
	}
	/**
	 * setter method
	 * @param startTime the startTime to set
	 */
	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	/**
	 * getter method
	 * @return the endTime
	 */
	@Column(name = "END_TIME", nullable = false)
	public Date getEndTime() {
		return endTime;
	}
	/**
	 * setter method
	 * @param endTime the endTime to set
	 */
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	/**
	 * getter method
	 * @return the version
	 */
	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return version;
	}
	/**
	 * setter method
	 * @param version the version to set
	 */
	public void setVersion(long version) {
		this.version = version;
	}
	/**
	 * getter method
	 * @return the updatedBy
	 */

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return updatedBy;
	}
	/**
	 * setter method
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(Person updatedBy) {
		this.updatedBy = updatedBy;
	}
	/**
	 * getter method
	 * @return the updated
	 */
	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return updated;
	}
	/**
	 * setter method
	 * @param updated the updated to set
	 */
	public void setUpdated(Date updated) {
		this.updated = updated;
	}
	
}
