package com.guitarcenter.scheduler.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Represents appointments in the scheduler
 */
@Entity
@Table(name = "USER_LOG")
@SequenceGenerator(name = "USER_LOG_ID_SEQ", sequenceName = "USER_LOG_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class UserLog implements java.io.Serializable {

	private static final long	serialVersionUID	= -3273105690730365834L;
	private Long				userLogId;
	private Long				personID;
	private Integer				version;
	private Date			    Updated;
	private Date				loginTimeStamp;
	private Date				logOutTimeStamp;
	private Integer				roleId;
	private String		        locationExternalId;
	 

	public UserLog() {
	}
	
	public UserLog(Long personID, Integer version, Date updated, Date loginTimeStamp,
			Date logOutTimeStamp, Integer roleId, String locationExternalId) {
		super();
		this.personID = personID;
		this.version = version;
		Updated = updated;
		this.loginTimeStamp = loginTimeStamp;
		this.logOutTimeStamp = logOutTimeStamp;
		this.roleId = roleId;
		this.locationExternalId = locationExternalId;
	}

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "USER_LOG_ID_SEQ")
	@Column(name = "USER_LOG_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getUserLogId() {
		return userLogId;
	}

	public void setUserLogId(Long userLogId) {
		this.userLogId = userLogId;
	}

	
	
	@Column(name = "PERSON_ID", nullable = false, precision = 22, scale = 0)
	public Long getPersonID() {
		return personID;
	}


	public void setPersonID(Long personID) {
		this.personID = personID;
	}


	public Integer getVersion() {
		return version;
	}


	public void setVersion(Integer version) {
		this.version = version;
	}


	public Date getUpdated() {
		return Updated;
	}


	public void setUpdated(Date updated) {
		Updated = updated;
	}

	@Column(name = "LOGIN_TIMESTAMP", length = 500)
	public Date getLoginTimeStamp() {
		return loginTimeStamp;
	}


	public void setLoginTimeStamp(Date loginTimeStamp) {
		this.loginTimeStamp = loginTimeStamp;
	}

	@Column(name = "LOGOUT_TIMESTAMP", length = 500)
	public Date getLogOutTimeStamp() {
		return logOutTimeStamp;
	}


		

	public void setLogOutTimeStamp(Date logOutTimeStamp) {
		this.logOutTimeStamp = logOutTimeStamp;
	}

	@Column(name = "ROLE_ID", length = 500)
	public Integer getRoleId() {
		return roleId;
	}


	public void setRoleId(Integer roleId) {
		this.roleId = roleId;
	}

	@Column(name = "LOCATION_EXTERNAL_ID", length = 500)
	public String getLocationExternalId() {
		return locationExternalId;
	}

	public void setLocationExternalId(String locationExternalId) {
		this.locationExternalId = locationExternalId;
	}

	
	 


	
	
	
}
