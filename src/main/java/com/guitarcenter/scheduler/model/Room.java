package com.guitarcenter.scheduler.model;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.SplitRoom;

/**
 * Represents a physical space or room at a location
 */
@Entity
@Table(name = "ROOM")
@SequenceGenerator(name = "ROOM_ID_SEQ", sequenceName = "ROOM_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Room implements java.io.Serializable {

	private static final long	serialVersionUID	= -7905307924298692045L;
	private Long				roomId;
	private long				version;
	private RoomSize			roomSize;
	private Site				site;
	private RoomType			roomType;
	private RoomNumber			roomNumber;
	private Person				updatedBy;
	private RoomTemplate		roomTemplate;
	private Date				updated;
	private SplitRoom			isSplitRoom;
	private Room				parentRoom;
	private String				profileRoomName;
	private Enabled				enabled;
	private LocationProfile		locationProfile;
	private String				externalId;
	private Set<Activity>		activities			= new HashSet<Activity>(0);
	private Set<Service>		services			= new HashSet<Service>(0);



	public Room() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ROOM_ID_SEQ")
	@Column(name = "ROOM_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getRoomId() {
		return this.roomId;
	}



	public void setRoomId(Long roomId) {
		this.roomId = roomId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ROOM_SIZE_ID")
	public RoomSize getRoomSize() {
		return this.roomSize;
	}



	public void setRoomSize(RoomSize roomSize) {
		this.roomSize = roomSize;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ROOM_TYPE_ID", nullable = false)
	public RoomType getRoomType() {
		return this.roomType;
	}



	public void setRoomType(RoomType roomType) {
		this.roomType = roomType;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ROOM_NUMBER_ID", nullable = false)
	public RoomNumber getRoomNumber() {
		return this.roomNumber;
	}



	public void setRoomNumber(RoomNumber roomNumber) {
		this.roomNumber = roomNumber;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ROOM_TEMPLATE_ID")
	public RoomTemplate getRoomTemplate() {
		return this.roomTemplate;
	}



	public void setRoomTemplate(RoomTemplate roomTemplate) {
		this.roomTemplate = roomTemplate;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "IS_SPLIT_ROOM", length = 1)
	@Enumerated(EnumType.STRING)
	public SplitRoom getIsSplitRoom() {
		return this.isSplitRoom;
	}



	public void setIsSplitRoom(SplitRoom isSplitRoom) {
		this.isSplitRoom = isSplitRoom;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PARENT_ID")
	public Room getParentRoom() {
		return this.parentRoom;
	}



	public void setParentRoom(Room pParentRoom) {
		this.parentRoom = pParentRoom;
	}



	@Column(name = "PROFILE_ROOM_NAME", length = 512)
	public String getProfileRoomName() {
		return this.profileRoomName;
	}



	public void setProfileRoomName(String profileRoomName) {
		this.profileRoomName = profileRoomName;
	}



	@Column(name = "ENABLED", length = 1)
	@Enumerated(EnumType.STRING)
	public Enabled getEnabled() {
		return this.enabled;
	}



	public void setEnabled(Enabled enabled) {
		this.enabled = enabled;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PROFILE_ID", nullable = false)
	public LocationProfile getLocationProfile() {
		return this.locationProfile;
	}



	public void setLocationProfile(LocationProfile locationProfile) {
		this.locationProfile = locationProfile;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}



	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "ROOM_ACTIVITIES", joinColumns = { @JoinColumn(name = "ROOM_ID", nullable = false, updatable = false) }, inverseJoinColumns = { @JoinColumn(name = "ACTIVITY_ID", nullable = false, updatable = false) })
	@Fetch(FetchMode.SUBSELECT)
	public Set<Activity> getActivities() {
		return this.activities;
	}



	public void setActivities(Set<Activity> activities) {
		this.activities = activities;
	}



	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "ROOM_SERVICES", joinColumns = { @JoinColumn(name = "ROOM_ID", nullable = false, updatable = false) }, inverseJoinColumns = { @JoinColumn(name = "SERVICE_ID", nullable = false, updatable = false) })
	@Fetch(FetchMode.SUBSELECT)
	public Set<Service> getServices() {
		return this.services;
	}



	public void setServices(Set<Service> services) {
		this.services = services;
	}

}
