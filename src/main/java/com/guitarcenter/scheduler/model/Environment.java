package com.guitarcenter.scheduler.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

/**
 * Represents activities that are known to the system
 */
@Entity
public class Environment implements java.io.Serializable {

	private static final long	serialVersionUID	= 3166026907095259951L;
	private Long				environmentId;
	private String				environmentName;
	
	public Environment() {
	}

	@Id	
	@Column(name = "ENVIRONMENT_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getEnvironmentId() {
		return this.environmentId;
	}

	public void setEnvironmentId(Long environmentId) {
		this.environmentId = environmentId;
	}

	@Column(name = "ENVIRONMENT_NAME", nullable = false, length = 512)
	public String getEnvironmentName() {
		return this.environmentName;
	}



	public void setEnvironmentName(String environmentName) {
		this.environmentName = environmentName;
	}



	}
