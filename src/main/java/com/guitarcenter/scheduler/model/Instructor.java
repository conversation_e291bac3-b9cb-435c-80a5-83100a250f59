package com.guitarcenter.scheduler.model;

import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.guitarcenter.scheduler.integration.service.impl.EmployeeIntegrationServiceImpl;
import com.guitarcenter.scheduler.model.enums.Enabled;

/**
 * Represents an employee of a studio
 */
@Entity
@Table(name = "INSTRUCTOR")
@SequenceGenerator(name = "INSTRUCTOR_ID_SEQ", sequenceName = "INSTRUCTOR_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Instructor implements java.io.Serializable {

    /**
     * The job code for GCS employees that indicate they are an instructor.
     * 
     * @see EmployeeIntegrationServiceImpl
     */
    private static final Integer GCS_INSTRUCTOR_JOB_CODE = new Integer(1878);
    private static final Integer GCS_INSTRUCTOR_COORDINATOR_JOB_CODE =
        new Integer(2006);
    private static final Integer GCS_INSTRUCTOR_3016_JOB_CODE =
        new Integer(3016);
    private static final Integer GCS_LEAD_INSTRUCTOR_JOB_CODE =
        new Integer(3027);
    private static final Set<Integer> GCS_INSTRUCTOR_JOB_CODES =
        new HashSet<Integer>(Arrays.asList(GCS_INSTRUCTOR_JOB_CODE,
                                           GCS_INSTRUCTOR_COORDINATOR_JOB_CODE,
                                           GCS_INSTRUCTOR_3016_JOB_CODE,
                                           GCS_LEAD_INSTRUCTOR_JOB_CODE));
    
    private static final long	serialVersionUID	= 1778070834197028604L;
	private Long				instructorId;
	private long				version;
	private Person				updatedBy;
	private Availability		availability;
	private Location			location;
	private Person				person;
	private Date				updated;
	private String				status;
	private Enabled				enabled;
	private Site				site;
	private String				externalId;
	private String				externalSource;
	private Set<Activity>		activities			= new HashSet<Activity>(0);
	
	private Set<ServiceMode>		serviceMode			= new HashSet<ServiceMode>(0);
  

	


	

// changes made for GSSP-275

	public Instructor(Person person ,Site site) {
		super();
		this.person=person;
		this.site=site;
	}
//  GSSP-275 changes ends here


	public Instructor() {
		// TODO Auto-generated constructor stub
	}







	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "INSTRUCTOR_ID_SEQ")
	@Column(name = "INSTRUCTOR_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getInstructorId() {
		return this.instructorId;
	}



	public void setInstructorId(Long instructorId) {
		this.instructorId = instructorId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person personByUpdatedBy) {
		this.updatedBy = personByUpdatedBy;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "AVAILABILITY_ID", nullable = false)
	public Availability getAvailability() {
		return this.availability;
	}



	public void setAvailability(Availability availability) {
		this.availability = availability;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "LOCATION_ID", nullable = false)
	public Location getLocation() {
		return this.location;
	}



	public void setLocation(Location location) {
		this.location = location;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PERSON_ID", nullable = false)
	public Person getPerson() {
		return this.person;
	}



	public void setPerson(Person person) {
		this.person = person;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "STATUS", length = 512)
	public String getStatus() {
		return this.status;
	}



	public void setStatus(String status) {
		this.status = status;
	}


	//Changes made for GSSP-231
	@Column(name = "ENABLED", length = 1,insertable = false)
	@Enumerated(EnumType.STRING)
	public Enabled getEnabled() {
		return this.enabled;
	}



	public void setEnabled(Enabled enabled) {
		this.enabled = enabled;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}



	@Column(name = "EXTERNAL_SOURCE", length = 512)
	public String getExternalSource() {
		return this.externalSource;
	}



	public void setExternalSource(String externalSource) {
		this.externalSource = externalSource;
	}



	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "INSTRUCTOR_ACTIVITIES", joinColumns = { @JoinColumn(name = "INSTRUCTOR_ID", nullable = false, updatable = false) }, inverseJoinColumns = { @JoinColumn(name = "ACTIVITY_ID", nullable = false, updatable = false) })
	@Fetch(FetchMode.SUBSELECT)
	public Set<Activity> getActivities() {
		return this.activities;
	}



	public void setActivities(Set<Activity> activities) {
		this.activities = activities;
	}
	
	//---GSSP Instructor Mode update changes
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "INSTRUCTOR_MODE", joinColumns = { @JoinColumn(name = "INSTRUCTOR_ID") }, inverseJoinColumns = { @JoinColumn(name = "SERVICE_MODE_ID") })
	@Fetch(FetchMode.SUBSELECT)
	public Set<ServiceMode> getServiceMode() {
		return serviceMode;
	}


	public void setServiceMode(Set<ServiceMode> serviceMode) {
		this.serviceMode = serviceMode;
	}


	/**
	 * Tests the supplied job code to determine if it represents an employee
	 * that should be recognised as an instructor.
	 * 
	 * @param jobCode integer job code to test
	 * @return true if the job code is for an instructor, false otherwise
	 */
	public static boolean isInstructorJobCode(int jobCode) {
	    return GCS_INSTRUCTOR_JOB_CODES.contains(new Integer(jobCode));
	}

}