package com.guitarcenter.scheduler.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * A customer has status in POS; this table provides a lookup between the stat
 * code provided and a display value
 */
@Entity
@Table(name = "CUSTOMER_STATUS")
@SequenceGenerator(name = "CUSTOMER_STATUS_ID_SEQ", sequenceName = "CUSTOMER_STATUS_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated" })
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
public class CustomerStatus implements java.io.Serializable {

	private static final long	serialVersionUID			= -3393915019164959043L;
	/**
	 * Cancelled customers have a special code of "C" in the data feed. As far
	 * as scheduler is concerned this is one of the few statuses that matter.
	 */
	public static final String	CUSTOMER_STATUS_CANCELLED	= "C";
	/**
	 * Prospect customers may turn into real customers, but until they do they
	 * have a status of "" in POS. Scheduler must recognise and handle "" as a
	 * valid status.
	 */
	public static final String CUSTOMER_STATUS_PROSPECT = "";
	private Long				customerStatusId;
	private long				version;
	private Site				site;
	private Person				updatedBy;
	private Date				updated;
	private String				externalId;
	private String				statusName;



	public CustomerStatus() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CUSTOMER_STATUS_ID_SEQ")
	@Column(name = "CUSTOMER_STATUS_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getCustomerStatusId() {
		return this.customerStatusId;
	}



	public void setCustomerStatusId(Long customerStatusId) {
		this.customerStatusId = customerStatusId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "STATUS_NAME", nullable = false, length = 512)
	public String getStatusName() {
		return this.statusName;
	}



	public void setStatusName(String statusName) {
		this.statusName = statusName;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}

}
