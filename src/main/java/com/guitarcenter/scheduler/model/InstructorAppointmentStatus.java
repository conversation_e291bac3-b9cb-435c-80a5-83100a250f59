package com.guitarcenter.scheduler.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

/**
 * Represents an Instructor comments of a studio
 */
@Entity
@Table(name = "INSTRUCTOR_APPOINTMENT_STATUS")
@JsonAutoDetect
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class InstructorAppointmentStatus implements java.io.Serializable {

 
    
    private static final long	serialVersionUID	= 1778070834197028604L;
	private Long				appointmentId;	 
	private String				status;
	private String				comments;
	private String 	            studentNote;
	private Date				updated;
	private Long				updatedBy;
	private Long				siteId;	
	private Long				version;
	//private String				lessonStatus;
	//private String				nextLessonStatus;
	private String				assignment;
	private String				practiceNotes;
	private String				remarks;
	//private String				rate;
	
	             





	@Id
	@Column(name = "APPOINTMENT_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getAppointmentId() {
		return this.appointmentId;
	}



	public void setAppointmentId(Long appointmentId) {
		this.appointmentId = appointmentId;
	}
	
	@Column(name = "COMMENTS", length = 2048)
	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

 
	@Column(name = "SHOW_STATUS", length = 256)
	public String getStatus() {
		return this.status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return updated;
	}

	public void setUpdated(Date updated) {
		this.updated = updated;
	}

	
	@Column(name = "UPDATED_BY", nullable = false)
	public Long getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(Long updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Column(name = "SITE")
	public Long getSiteId() {
		return siteId;
	}

	public void setSiteId(Long siteId) {
		this.siteId = siteId;
	}


	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}


	@Column(name = "STUDENT_NOTE", length = 2048)
	public String getStudentNote() {
		return studentNote;
	}



	public void setStudentNote(String studentNote) {
		this.studentNote = studentNote;
	}


	/*@Column(name = "LESSON_STATUS", length = 2048)
	public String getLessonStatus() {
		return lessonStatus;
	}



	public void setLessonStatus(String lessonStatus) {
		this.lessonStatus = lessonStatus;
	}


	@Column(name = "NEXT_LESSON_STATUS", length = 2048)
	public String getNextLessonStatus() {
		return nextLessonStatus;
	}



	public void setNextLessonStatus(String nextLessonStatus) {
		this.nextLessonStatus = nextLessonStatus;
	}*/


	@Column(name = "ASSIGNMENT", length = 2048)
	public String getAssignment() {
		return assignment;
	}



	public void setAssignment(String assignment) {
		this.assignment = assignment;
	}


	@Column(name = "REMARKS", length = 2048)
	public String getRemarks() {
		return remarks;
	}



	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}


	@Column(name = "PRACTICE_NOTES", length = 2048)
	public String getPracticeNotes() {
		return practiceNotes;
	}



	public void setPracticeNotes(String practiceNotes) {
		this.practiceNotes = practiceNotes;
	}


	/*@Column(name = "RATE", length = 2048)
	public String getRate() {
		return rate;
	}



	public void setRate(String rate) {
		this.rate = rate;
	}*/
	
	
	
	
	


	 

}