package com.guitarcenter.scheduler.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Represents booked appointment
 */
@Entity
@Table(name = "APPOINTMENT_BOOK_TRANSACTION")
//@SequenceGenerator(name = "APPT_BOOK_TRANS_SEQ", sequenceName = "APPT_BOOK_TRANS_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class BookedApptTransaction implements java.io.Serializable {

	private static final long	serialVersionUID	= -7905307924298692045L;
	private Long 				orderId;
	private Long				appointmentId;
	private Long				duration;
	private long				version;
	private String			    lessonBookCode;
	private Site				site;
	private Person				updatedBy;
	private Date				updated;
	private String				bookType;
	private String				status;
	private String				locationExternalId;
	private String				customerExternalId;
	private String				instructorExternalId;
	private Long 				managerApproval;
	private String				posRefNumber;
	private String				errorInfo;

	public BookedApptTransaction() {
	}

	@Id
//	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "APPT_BOOK_TRANS_ID_SEQ")
	@Column(name = "ORDER_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getOrderId() {
		return this.orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	@Column(name = "APPOINTMENT_ID", unique = true, nullable = false)
	public Long getAppointmentId() {
		return appointmentId;
	}

	public void setAppointmentId(Long appointmentId) {
		this.orderId = appointmentId;
	}


	@Column(name = "DURATION")
	public Long getDuration() {
		return duration;
	}

	public void setDuration(Long duration) {
		this.duration = duration;
	}

	@Column(name = "LESSON_BOOK_CD", length = 256)
	public String getLessonBookCode() {
		return lessonBookCode;
	}

	public void setLessonBookCode(String lessonBookCode) {
		this.lessonBookCode = lessonBookCode;
	}

	@Column(name = "BOOK_TYPE", length = 256)
	public String getBookType() {
		return bookType;
	}

	public void setBookType(String bookType) {
		this.bookType = bookType;
	}

	@Column(name = "STATUS", length = 256)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "LOCATION_EXTERNAL_ID", length = 256)
	public String getLocationExternalId() {
		return locationExternalId;
	}

	public void setLocationExternalId(String locationExternalId) {
		this.locationExternalId = locationExternalId;
	}

	@Column(name = "CUSTOMER_EXTERNAL_ID", length = 256)
	public String getCustomerExternalId() {
		return customerExternalId;
	}

	public void setCustomerExternalId(String customerExternalId) {
		this.customerExternalId = customerExternalId;
	}

	@Column(name = "INSTRUCTOR_EXTERNAL_ID", length = 256)
	public String getInstructorExternalId() {
		return instructorExternalId;
	}

	public void setInstructorExternalId(String instructorExternalId) {
		this.instructorExternalId = instructorExternalId;
	}

	@Column(name = "MANAGER_APPROVAL")
	public Long getManagerApproval() {
		return managerApproval;
	}

	public void setManagerApproval(Long managerApproval) {
		this.managerApproval = managerApproval;
	}

	@Column(name = "POS_REFERENCE_NUMBER", length = 256)
	public String getPosRefNumber() {
		return posRefNumber;
	}

	public void setPosRefNumber(String posRefNumber) {
		this.posRefNumber = posRefNumber;
	}

	@Column(name = "ERROR_INFO", length = 256)
	public String getErrorInfo() {
		return errorInfo;
	}

	public void setErrorInfo(String errorInfo) {
		this.errorInfo = errorInfo;
	}


	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}


	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}

}
