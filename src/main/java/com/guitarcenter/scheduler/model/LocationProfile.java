package com.guitarcenter.scheduler.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.guitarcenter.scheduler.model.enums.Enabled;

/**
 * Represents a Location Profile
 */
@Entity
@Table(name = "LOCATION_PROFILE")
@SequenceGenerator(name = "LOCATION_PROFILE_ID_SEQ", sequenceName = "LOCATION_PROFILE_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class LocationProfile implements java.io.Serializable {

	private static final long	serialVersionUID	= -4969443001972286978L;
	private Long				profileId;
	private long				version;
	private Site				site;
	private Person				updatedBy;
	private Availability		availability;
	private String				externalId;
	private Date				updated;
	private Enabled				enabled;
	private String				timeZone;



	public LocationProfile() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LOCATION_PROFILE_ID_SEQ")
	@Column(name = "PROFILE_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getProfileId() {
		return this.profileId;
	}



	public void setProfileId(Long profileId) {
		this.profileId = profileId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "AVAILABILITY_ID", nullable = false)
	public Availability getAvailability() {
		return this.availability;
	}



	public void setAvailability(Availability availability) {
		this.availability = availability;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "ENABLED", length = 1)
	@Enumerated(EnumType.STRING)
	public Enabled getEnabled() {
		return this.enabled;
	}



	public void setEnabled(Enabled enabled) {
		this.enabled = enabled;
	}



	@Column(name = "TZ", nullable = false, length = 512)
	public String getTimeZone() {
		return this.timeZone;
	}



	public void setTimeZone(String timeZone) {
		this.timeZone = timeZone;
	}

}
