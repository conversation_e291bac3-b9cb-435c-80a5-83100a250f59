package com.guitarcenter.scheduler.model;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.SplitRoom;

/**
 * Represents booked appointment
 */
@Entity
@Table(name = "APPOINTMENT_BOOK")
//@SequenceGenerator(name = "APPOINTMENT_BOOK_ID_SEQ", sequenceName = "APPOINTMENT_BOOK_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class BookedAppointment implements java.io.Serializable {

	private static final long serialVersionUID = 4675332458977389122L;
	private Long				appointmentId;
	private long				version;
	private Site				site;
	private Person				updatedBy;
	private Date				updated;
	private Enabled				bookFlag;
	private String				externalSource;
	//private BookedApptTransaction		orderId;



	public BookedAppointment() {
	}

	@Id
//	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "APPOINTMENT_BOOK_ID_SEQ")
	@Column(name = "APPOINTMENT_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getAppointmentId() {
		return this.appointmentId;
	}



	public void setAppointmentId(Long appointmentId) {
		this.appointmentId = appointmentId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}

	public void setSite(Site site) {
		this.site = site;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}

	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}


	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}


	@Column(name = "BOOK_FLAG", length = 1)
	@Enumerated(EnumType.STRING)
	public Enabled getBookFlag() {
		return this.bookFlag;
	}



	public void setBookFlag(Enabled bookFlag) {
		this.bookFlag = bookFlag;
	}

	@Column(name = "EXTERNAL_SOURCE", length = 256)
	public String getExternalSource() {
		return this.externalSource;
	}

	public void setExternalSource(String externalSource) {
		this.externalSource = externalSource;
	}

	/*
	public BookedApptTransaction getOrderId() {
		return orderId;
	}

	public void setOrderId(BookedApptTransaction orderId) {
		this.orderId = orderId;
	}
	*/


}
