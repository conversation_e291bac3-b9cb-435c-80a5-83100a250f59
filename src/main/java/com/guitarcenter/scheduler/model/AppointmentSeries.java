package com.guitarcenter.scheduler.model;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.guitarcenter.scheduler.model.enums.IsRecurring;

/**
 * Maps appointments to a series of appointments
 */
@Entity
@Table(name = "APPOINTMENT_SERIES")
@SequenceGenerator(name = "APPOINTMENT_SERIES_ID_SEQ", sequenceName = "APPOINTMENT_SERIES_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class AppointmentSeries implements java.io.Serializable {

	private static final long	serialVersionUID	= -9011550754053372474L;
	private Long				appointmentSeriesId;
	private long				version;
	private Site				site;
	private Person				updatedBy;
	private LocationProfile		locationProfile;
	private Activity			activity;
	private Date				updated;
	private String				externalId;
	private IsRecurring			isRecurring;
	private Date				seriesStartTime;
	private Date				seriesEndTime;
	private String				bandName;
	private String				note;
	private Set<Customer>		customers			= new HashSet<Customer>(0);



	public AppointmentSeries() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "APPOINTMENT_SERIES_ID_SEQ")
	@Column(name = "APPOINTMENT_SERIES_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getAppointmentSeriesId() {
		return this.appointmentSeriesId;
	}



	public void setAppointmentSeriesId(Long appointmentSeriesId) {
		this.appointmentSeriesId = appointmentSeriesId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PROFILE_ID", nullable = false)
	public LocationProfile getLocationProfile() {
		return this.locationProfile;
	}



	public void setLocationProfile(LocationProfile locationProfile) {
		this.locationProfile = locationProfile;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ACTIVITY_ID", nullable = false)
	public Activity getActivity() {
		return this.activity;
	}



	public void setActivity(Activity activity) {
		this.activity = activity;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}



	@Column(name = "IS_RECURRING", length = 1)
	@Enumerated(EnumType.STRING)
	public IsRecurring getIsRecurring() {
		return this.isRecurring;
	}



	public void setIsRecurring(IsRecurring isRecurring) {
		this.isRecurring = isRecurring;
	}



	@Column(name = "SERIES_START_TIME", nullable = false)
	public Date getSeriesStartTime() {
		return this.seriesStartTime;
	}



	public void setSeriesStartTime(Date seriesStartTime) {
		this.seriesStartTime = seriesStartTime;
	}



	@Column(name = "SERIES_END_TIME")
	public Date getSeriesEndTime() {
		return this.seriesEndTime;
	}



	public void setSeriesEndTime(Date seriesEndTime) {
		this.seriesEndTime = seriesEndTime;
	}



	@Column(name = "BAND_NAME", length = 512)
	public String getBandName() {
		return this.bandName;
	}



	public void setBandName(String bandName) {
		this.bandName = bandName;
	}



	@Column(name = "NOTE", length = 512)
	public String getNote() {
		return this.note;
	}



	public void setNote(String note) {
		this.note = note;
	}



	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "CUSTOMER_APPOINTMENT_SERIES", joinColumns = { @JoinColumn(name = "APPOINTMENT_SERIES_ID", nullable = false, updatable = false) }, inverseJoinColumns = { @JoinColumn(name = "CUSTOMER_ID", nullable = false, updatable = false) })
	@Fetch(FetchMode.SUBSELECT)
	public Set<Customer> getCustomers() {
		return this.customers;
	}



	public void setCustomers(Set<Customer> customers) {
		this.customers = customers;
	}

}
