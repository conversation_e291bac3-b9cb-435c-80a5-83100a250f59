package com.guitarcenter.scheduler.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Provides a lookup of instruments for a site
 */
@Entity
@Table(name = "INSTRUMENT")
@SequenceGenerator(name = "INSTRUMENT_ID_SEQ", sequenceName = "INSTRUMENT_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated" })
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
public class Instrument implements java.io.Serializable {

	private static final long	serialVersionUID	= -8885322119418038076L;
	private Long				instrumentId;
	private long				version;
	private Site				site;
	private Person				updatedBy;
	private Date				updated;
	private String				externalId;
	private String				instrumentName;



	public Instrument() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "INSTRUMENT_ID_SEQ")
	@Column(name = "INSTRUMENT_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getInstrumentId() {
		return this.instrumentId;
	}



	public void setInstrumentId(Long instrumentId) {
		this.instrumentId = instrumentId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}



	@Column(name = "INSTRUMENT_NAME", length = 512)
	public String getInstrumentName() {
		return this.instrumentName;
	}



	public void setInstrumentName(String instrumentName) {
		this.instrumentName = instrumentName;
	}

}
