/**
 * @Title: Onetime.java
 * @Package com.guitarcenter.scheduler.model
 * @Description: 
 * Copyright: Copyright (c) 2014 
 * Company:
 * 
 * <AUTHOR>
 * @date May 29, 2014 1:52:32 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * @ClassName: Onetime
 * @Description: 
 * <AUTHOR>
 * @date May 29, 2014 1:52:32 PM
 *
 */
@Entity
@Table(name = "ONLINE_AVAILABILITY")
@SequenceGenerator(name = "ONLINE_AVAILABILITY_ID_SEQ", sequenceName = "ONLINE_AVAILABILITY_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class OnlineAvailability {

	@SuppressWarnings("unused")
	private static final long	serialVersionUID	= 2123445809734350913L;
	private Long				onlineAvailabilityId;
	private Instructor			instructor;
	private Date				startTime;
	private Date				endTime;
	private String				day;
	private Site				site;
	private long				version;
	private Person				updatedBy;
	private Date				updated;
	
	/**
	  * Onetime. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  */
	public OnlineAvailability() {
	}
	
	

	/**
	 * getter method
	 * @return the onetimeId
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ONLINE_AVAILABILITY_ID_SEQ")
	@Column(name = "ONLINE_AVAILABILITY_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getOnlineAvailabilityId() {
		return onlineAvailabilityId;
	}

	public void setOnlineAvailabilityId(Long onlineAvailabilityId) {
		this.onlineAvailabilityId = onlineAvailabilityId;
	}
	/**
	 * getter method
	 * @return the instructor
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "INSTRUCTOR_ID", nullable = false)
	public Instructor getInstructor() {
		return instructor;
	}
	/**
	 * setter method
	 * @param instructor the instructor to set
	 */
	public void setInstructor(Instructor instructor) {
		this.instructor = instructor;
	}
	/**
	 * getter method
	 * @return the startTime
	 */
	@Column(name = "START_TIME", nullable = false)
	public Date getStartTime() {
		return startTime;
	}
	/**
	 * setter method
	 * @param startTime the startTime to set
	 */
	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	/**
	 * getter method
	 * @return the endTime
	 */
	@Column(name = "END_TIME", nullable = false)
	public Date getEndTime() {
		return endTime;
	}
	/**
	 * setter method
	 * @param endTime the endTime to set
	 */
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	
	@Column(name = "DAY", length = 256)
	public String getDay() {
		return day;
	}

	public void setDay(String day) {
		this.day = day;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}

	public void setSite(Site site) {
		this.site = site;
	}
	
	
	/**
	 * getter method
	 * @return the version
	 */
	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return version;
	}
	/**
	 * setter method
	 * @param version the version to set
	 */
	public void setVersion(long version) {
		this.version = version;
	}
	/**
	 * getter method
	 * @return the updatedBy
	 */

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return updatedBy;
	}
	/**
	 * setter method
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(Person updatedBy) {
		this.updatedBy = updatedBy;
	}
	/**
	 * getter method
	 * @return the updated
	 */
	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return updated;
	}
	/**
	 * setter method
	 * @param updated the updated to set
	 */
	public void setUpdated(Date updated) {
		this.updated = updated;
	}
}
