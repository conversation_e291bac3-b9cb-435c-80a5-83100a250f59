package com.guitarcenter.scheduler.model;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Represents a customer
 */
@Entity
@Table(name = "CUSTOMER")
@SequenceGenerator(name = "CUSTOMER_ID_SEQ", sequenceName = "CUSTOMER_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Customer implements java.io.Serializable {

	
	
	private static final long serialVersionUID =   2085422946647504613L;
	
	private Long				customerId;
	private long				version;
	private Site				site;
	private Person				updatedBy;
	private CustomerStatus		customerStatus;
	private Person				person;
	private Date				updated;
	private String				externalId;
	private ParentDetails		parentId;
	
	//Added for Scheduling Phase II - Client Facing project
	private String              badgeNumber;
	
	//Added for GSSP-220
	private String  location_external_id;
	
	//Added for POS-808
	private String  lessonCounts;
	private Date lastBooked;
	
	private Date customerCreated;
	private Date subscriptionStartDate; 
	private Date subscriptionEndDate;
	private String contractDay;
	
	//----GSSP-435
	private Date posUpdated;

		
		
	/**
	* @return the lessonCounts
	*/
	@Column(name = "LESSON_COUNT", length = 256)
	public String getLessonCounts() {
		return lessonCounts;
	}



		/**
		 * @param lessonCounts the lessonCounts to set
		 */
		public void setLessonCounts(String lessonCounts) {
			this.lessonCounts = lessonCounts;
		}
		 
		@Column(name = "LAST_BOOKED")		 
		public Date getLastBooked() {
			return lastBooked;
		}



		public void setLastBooked(Date lastBooked) {
			this.lastBooked = lastBooked;
		}	
		
	
	@Column(name = "LOCATION_EXTERNAL_ID", length = 256)
	public String getLocation_external_id() {
		return location_external_id;
	}



	public void setLocation_external_id(String location_external_id) {
		this.location_external_id = location_external_id;
	}

	private String				externalSource;
	private Set<Instrument>		instruments			= new HashSet<Instrument>(0);



	public Customer() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CUSTOMER_ID_SEQ")
	@Column(name = "CUSTOMER_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getCustomerId() {
		return this.customerId;
	}



	public void setCustomerId(Long customerId) {
		this.customerId = customerId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person personByUpdatedBy) {
		this.updatedBy = personByUpdatedBy;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CUSTOMER_STATUS_ID", nullable = false)
	public CustomerStatus getCustomerStatus() {
		return this.customerStatus;
	}



	public void setCustomerStatus(CustomerStatus customerStatus) {
		this.customerStatus = customerStatus;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PERSON_ID", nullable = false)
	public Person getPerson() {
		return this.person;
	}



	public void setPerson(Person person) {
		this.person = person;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}


	@Column(name = "EXTERNAL_SOURCE", length = 512)
	public String getExternalSource() {
		return this.externalSource;
	}



	public void setExternalSource(String externalSource) {
		this.externalSource = externalSource;
	}
	
	
	@Column(name = "BADGE_NUMBER", length = 512)
	public String getBadgeNumber() {
		return this.badgeNumber;
	}

	public void setBadgeNumber(String badgeNumber) {
		this.badgeNumber = badgeNumber;
	}
	
	@Column(name = "CONTRACT_DAY", length = 256)
	public String getContractDay() {
		return contractDay;
	}



	public void setContractDay(String contractDay) {
		this.contractDay = contractDay;
	}



	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "CUSTOMER_INSTRUMENT", joinColumns = { @JoinColumn(name = "CUSTOMER_ID", nullable = false, updatable = false) }, inverseJoinColumns = { @JoinColumn(name = "INSTRUMENT_ID", nullable = false, updatable = false) })
	@Fetch(FetchMode.SUBSELECT)
	public Set<Instrument> getInstruments() {
		return this.instruments;
	}

	



	public void setInstruments(Set<Instrument> instruments) {
		this.instruments = instruments;
	}
	
	//For bug GCSS-546
	 public boolean equals(Object obj) {
	     if (this == obj) {
	        return true;
	     }
	     if (!(obj instanceof Customer)) {
	        return false;
	     }
	     Customer customer = (Customer) obj;
	     return new EqualsBuilder().append(this.getCustomerId(), customer.getCustomerId()).isEquals();
	 }

	 //For bug GCSS-546
	 public int hashCode() {
	     return new HashCodeBuilder().append(getCustomerId()).toHashCode();
	 }

	//@Column(name = "PARENT_ID")
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PARENT_ID", nullable = true)
	public ParentDetails getParentId() {
		return parentId;
	}



	public void setParentId(ParentDetails parentId) {
		this.parentId = parentId;
	}


	@Column(name = "CUSTOMER_CREATED", nullable = true)
	public Date getCustomerCreated() {
		return customerCreated;
	}



	public void setCustomerCreated(Date customerCreated) {
		this.customerCreated = customerCreated;
	}



	@Column(name = "SUBSCRIPTION_START_DATE", nullable = true)
	public Date getSubscriptionStartDate() {
		return subscriptionStartDate;
	}



	public void setSubscriptionStartDate(Date subscriptionStartDate) {
		this.subscriptionStartDate = subscriptionStartDate;
	}


	@Column(name = "SUBSCRIPTION_END_DATE", nullable = true)
	public Date getSubscriptionEndDate() {
		return subscriptionEndDate;
	}


	public void setSubscriptionEndDate(Date subscriptionEndDate) {
		this.subscriptionEndDate = subscriptionEndDate;
	}




	@Column(name = "POS_UPDATED" )
	public Date getPosUpdated() {
		return posUpdated;
	}



	public void setPosUpdated(Date posUpdated) {
		this.posUpdated = posUpdated;
	}



	@Override
	public String toString() {
		return "Customer [customerId=" + customerId + ", version=" + version + ", site=" + site + ", updatedBy="
				+ updatedBy + ", customerStatus=" + "customerStatus" + ", person=" + person + ", updated=" + updated
				+ ", externalId=" + externalId + ", parentId=" + parentId + ", badgeNumber=" + badgeNumber
				+ ", location_external_id=" + location_external_id + ", lessonCounts=" + lessonCounts + ", lastBooked="
				+ lastBooked + ", externalSource=" + externalSource + ", instruments=" + instruments + "]";
	}



 
	 
	 
	 

}
