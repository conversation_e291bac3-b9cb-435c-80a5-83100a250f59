package com.guitarcenter.scheduler.model;



import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.guitarcenter.scheduler.model.enums.Enabled;

/**
 * These are the high-level roles that will be available in the system
 */
@Entity
@Table(name = "APPOINTMENT_CANCEL_REASON")
@SequenceGenerator(name = "APPOINTMENT_CANCEL_REASON_ID_SEQ", sequenceName = "APPOINTMENT_CANCEL_REASON_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated" })
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
public class AppointmentCancelReason implements java.io.Serializable {

	private static final long	serialVersionUID	= 5422781799411531675L;
	private Long				appointmentcancelreasonID;
	private long				version;
	private Site				siteId;
	private Person				updatedBy;
	private Date				updated;
	private String				cancelReason;
	private String				isRecuring;
	private Enabled				enabled;
	
	

	
	
	public AppointmentCancelReason() {
	}

	
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "APPOINTMENT_CANCEL_REASON_ID_SEQ")
	@Column(name = "APPOINTMENT_CANCEL_REASON_ID", nullable = false)
	public Long getAppointmentcancelreasonID() {
		return appointmentcancelreasonID;
	}




	public void setAppointmentcancelreasonID(Long appointmentcancelreasonID) {
		this.appointmentcancelreasonID = appointmentcancelreasonID;
	}
	

	@Column(name = "IS_FOR_RECURRING", nullable = false)
	public String getIsRecuring() {
		return isRecuring;
	}


	public void setIsRecuring(String isRecuring) {
		this.isRecuring = isRecuring;
	}

	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return version;
	}




	public void setVersion(long version) {
		this.version = version;
	}


	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSiteId() {
		return siteId;
	}




	public void setSiteId(Site siteId) {
		this.siteId = siteId;
	}


	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)

	public Person getUpdatedBy() {
		return updatedBy;
	}




	public void setUpdatedBy(Person updatedBy) {
		this.updatedBy = updatedBy;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return updated;
	}




	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "CANCEL_REASON", nullable = false)
	public String getCancelReason() {
		return cancelReason;
	}




	public void setCancelReason(String cancelReason) {
		this.cancelReason = cancelReason;
	}
	
	@Column(name = "ENABLED", length = 1,insertable = false)
	@Enumerated(EnumType.STRING)
	public Enabled getEnabled() {
		return this.enabled;
	}



	public void setEnabled(Enabled enabled) {
		this.enabled = enabled;
	}
	
	


	

	

}