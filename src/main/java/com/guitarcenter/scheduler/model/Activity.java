package com.guitarcenter.scheduler.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.RequiresInstructor;

/**
 * Represents activities that are known to the system
 */
@Entity
@Table(name = "ACTIVITY")
@SequenceGenerator(name = "ACTIVITY_ID_SEQ", sequenceName = "ACTIVITY_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Activity implements java.io.Serializable {

	private static final long	serialVersionUID	= 3166026907095259951L;
	private Long				activityId;
	private long				version;
	private Site				site;
	private Person				updatedBy;
	private Service				service;
	private Date				updated;
	private String				activityName;
	private Long				minimumAttendees;
	private Long				maximumAttendees;
	private Long				minimumDuration;
	private Long				maximumDuration;
	private RequiresInstructor	requiresInstructor;
	private Enabled				enabled;
	private String				externalId;



	public Activity() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ACTIVITY_ID_SEQ")
	@Column(name = "ACTIVITY_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getActivityId() {
		return this.activityId;
	}



	public void setActivityId(Long activityId) {
		this.activityId = activityId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SERVICE_ID", nullable = false)
	public Service getService() {
		return this.service;
	}



	public void setService(Service service) {
		this.service = service;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "ACTIVITY_NAME", nullable = false, length = 512)
	public String getActivityName() {
		return this.activityName;
	}



	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}



	@Column(name = "MINIMUM_ATTENDEES", nullable = false, precision = 22, scale = 0)
	public Long getMinimumAttendees() {
		return this.minimumAttendees;
	}



	public void setMinimumAttendees(Long minimumAttendees) {
		this.minimumAttendees = minimumAttendees;
	}



	@Column(name = "MAXIMUM_ATTENDEES", precision = 22, scale = 0)
	public Long getMaximumAttendees() {
		return this.maximumAttendees;
	}



	public void setMaximumAttendees(Long maximumAttendees) {
		this.maximumAttendees = maximumAttendees;
	}



	@Column(name = "MINIMUM_DURATION", nullable = false, precision = 22, scale = 0)
	public Long getMinimumDuration() {
		return this.minimumDuration;
	}



	public void setMinimumDuration(Long minimumDuration) {
		this.minimumDuration = minimumDuration;
	}
	
	
	@Column(name = "MAXIMUM_DURATION", nullable = false, precision = 22, scale = 0)
	public Long getMaximumDuration() {
		return this.maximumDuration;
	}
	
	
	
	public void setMaximumDuration(Long maximumDuration) {
		this.maximumDuration = maximumDuration;
	}



	@Column(name = "REQUIRES_INSTRUCTOR", length = 1)
	@Enumerated(EnumType.STRING)
	public RequiresInstructor getRequiresInstructor() {
		return this.requiresInstructor;
	}



	public void setRequiresInstructor(RequiresInstructor requiresInstructor) {
		this.requiresInstructor = requiresInstructor;
	}



	@Column(name = "ENABLED", length = 1)
	@Enumerated(EnumType.STRING)
	public Enabled getEnabled() {
		return this.enabled;
	}



	public void setEnabled(Enabled enabled) {
		this.enabled = enabled;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}



	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).append(activityId).toHashCode();
	}



	@Override
	public boolean equals(Object obj) {
		boolean flag = false;
		if (obj != null && Activity.class.isAssignableFrom(obj.getClass())) {
			Activity f = (Activity) obj;
			flag = new EqualsBuilder().append(activityId, f.getActivityId()).isEquals();
		}
		return flag;
	}
}
