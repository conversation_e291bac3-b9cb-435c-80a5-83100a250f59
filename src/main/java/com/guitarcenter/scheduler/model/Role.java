package com.guitarcenter.scheduler.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * These are the high-level roles that will be available in the system
 */
@Entity
@Table(name = "ROLE")
@SequenceGenerator(name = "ROLE_ID_SEQ", sequenceName = "ROLE_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated" })
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
public class Role implements java.io.Serializable {

	private static final long	serialVersionUID	= 5422781799411531670L;
	private Long				roleId;
	private long				version;
	private Site				site;
	private Person				updatedBy;
	private Date				updated;
	private String				roleName;
	private String				description;
	private String				externalId;



	public Role() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ROLE_ID_SEQ")
	@Column(name = "ROLE_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getRoleId() {
		return this.roleId;
	}



	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SITE_ID", nullable = false)
	public Site getSite() {
		return this.site;
	}



	public void setSite(Site site) {
		this.site = site;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person person) {
		this.updatedBy = person;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "ROLE_NAME", nullable = false, length = 512)
	public String getRoleName() {
		return this.roleName;
	}



	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}



	@Column(name = "DESCRIPTION", length = 512)
	public String getDescription() {
		return this.description;
	}



	public void setDescription(String description) {
		this.description = description;
	}



	@Column(name = "EXTERNAL_ID", length = 512)
	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}

}
