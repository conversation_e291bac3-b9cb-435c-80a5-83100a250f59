package com.guitarcenter.scheduler.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Represents an individual person in the system; the person may also be a
 * customer, employee or instructor
 */
@Entity
@Table(name = "PARENT_DETAILS")
@SequenceGenerator(name = "PARENT_ID_SEQ", sequenceName = "PARENT_ID_SEQ", allocationSize = 1)
@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class ParentDetails implements java.io.Serializable {

	private static final long	serialVersionUID	= 630363237392565394L;
	private Long				parentId;
	private long				version;
	private Person				updatedBy;
	private Date				updated;
	private String				fullName;
	private String				externalSource;
	private String				secondaryEmail;
 



	public ParentDetails() {
	}



	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PARENT_ID_SEQ")
	@Column(name = "PARENT_ID", unique = true, nullable = false, precision = 22, scale = 0)
	public Long getParentId() {
		return this.parentId;
	}



	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}



	@Version
	@Column(name = "VERSION", nullable = false, precision = 22, scale = 0)
	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}



	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UPDATED_BY", nullable = false)
	public Person getUpdatedBy() {
		return this.updatedBy;
	}



	public void setUpdatedBy(Person personByUpdatedBy) {
		this.updatedBy = personByUpdatedBy;
	}



	@Column(name = "UPDATED", nullable = false)
	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}



	@Column(name = "FULL_NAME", length = 256)
	public String getFullName() {
		return this.fullName;
	}



	public void setFullName(String fullName) {
		this.fullName = fullName;
	}




	@Column(name = "EXTERNAL_SOURCE", length = 256)
    public String getExternalSource() {
		return externalSource;
	}



	public void setExternalSource(String externalSource) {
		this.externalSource = externalSource;
	}


	@Column(name = "SECONDARY_EMAIL", length = 256)
	public String getSecondaryEmail() {
		return secondaryEmail;
	}



	public void setSecondaryEmail(String secondaryEmail) {
		this.secondaryEmail = secondaryEmail;
	}



	@Override
	public String toString() {
		return "ParentDetails [parentId=" + parentId + ", version=" + version + ", updatedBy=" + updatedBy
				+ ", updated=" + updated + ", fullName=" + fullName + ",  " + "" + ", " + ""
				+ ", externalSource=" + externalSource + "]";
	}

 

}
