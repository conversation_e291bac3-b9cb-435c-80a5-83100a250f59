package com.guitarcenter.scheduler.jobs;

import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.persistence.Transient;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.quartz.StatefulJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.guitarcenter.scheduler.elesticsearch.InstructorAvailablityElasticUploader;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.service.GetInstructorFullAvailabiltyService;
import com.guitarcenter.scheduler.service.JobNotificationEmailService;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceResponseDTO;

/**
 * Implements a job bean for rCRM Appointment data File transferring.
 *
 */

public class GetInstructorFullAvailabiltyJob extends QuartzJobBean implements StatefulJob, Serializable {
    
	@Transient
    Logger log = LoggerFactory.getLogger(GetInstructorFullAvailabiltyJob.class);
    
    private static final long serialVersionUID = 4L;

     
     /**
     * The method that will be called by Quartz when the trigger fires.
     * 
     * @see @see org.springframework.scheduling.quartz.QuartzJobBean#executeInternal(org.quartz.JobExecutionContext)
     * @param jec
     * @throws JobExecutionException
     */
    @SuppressWarnings("unused")
	@Override
    protected void executeInternal(JobExecutionContext jec)
            throws JobExecutionException {

    	log.error("GetInstructorFullAvailabilty    start");
		JobNotificationEmailService jobNotificationEmailService = null;
		GetInstructorFullAvailabiltyService  getInstructorFullAvailabiltyService = null;
		if (log.isDebugEnabled()) {
			log.debug("trigger fired; retrieve and process GetInstructorAvailability. jec = {}", jec);
			
		}
		try {
		
			getInstructorFullAvailabiltyService = (GetInstructorFullAvailabiltyService)jec.getScheduler().getContext().get("getInstructorFullAvailabiltyService");
			jobNotificationEmailService = (JobNotificationEmailService) jec.getScheduler().getContext()
					.get("jobNotificationEmailService");
	 
			List<InstructorAVLServiceResponseDTO> result = new ArrayList<InstructorAVLServiceResponseDTO>();
			
			if (getInstructorFullAvailabiltyService != null) {
				 try
				  {//get all instructors list
										 
										 
											List<Instructor> insList = getInstructorFullAvailabiltyService.getFullInstructorsAffectRecent();
											log.error("GetInstructorFullAvailabilty    result fetching done");
											 log.error("insList FUllAvail batch   "+insList); 
											List<Instructor> records = insList;
											int totalRecords = records.size();
										    int batchSize =50;
											int iterations = totalRecords % batchSize  == 0 ? totalRecords / batchSize : (totalRecords / batchSize) + 1;
						
										    for (int i = 0; i < iterations; i++) {
											List<Instructor> batch = records.subList(i * batchSize, Math.min((i + 1) * batchSize, totalRecords));
											
											
											//System.out.println(batch.size());
											// log.error("COMPLETED FUllAvail batch   "+i);
										 
										  result = getInstructorFullAvailabiltyService.getInstructorFullAvailabilitySlots(batch);
										  String env = getInstructorFullAvailabiltyService.getEnvironmentName();
										  if (log.isDebugEnabled()) {
												log.debug("trigger fired; retrieve and Job GetInstructorFullAvailabiltys result", result);
												
											}
										  InstructorAvailablityElasticUploader iaeu = new InstructorAvailablityElasticUploader();
										  Map<String, String> elasticConnect =  getInstructorFullAvailabiltyService.getPropertiesDataForElasticLoader();
										  log.error("elasticInsertOrUpdateopen..");
										 if(result!= null && result.size()> 0) iaeu.elasticInsertOrUpdate(result,env,elasticConnect,"GetInstructorFullAvailabilty");
										 log.error("elasticInsertOrUpdateStart..."); 
											/*
											 * for (InstructorAVLServiceResponseDTO rd:result){
											 * //System.out.println("  "+rd.getInstructorId()); }
											 */
										 // System.out.println(" completed " +i );
										  log.error("COMPLETED FUllAvail batch   "+i);
										    }}
				  catch(Exception e)
				  {
					  log.error("Error during fetching of data in new instructor Availabilitiy api Web Service", e);
					  throw new SchedulerException();
					  
				  }						 
				
				if(jobNotificationEmailService != null)
				{							
					if(result!= null && result.size()== 0){
						jobNotificationEmailService.sendEmailForCRMAppointmentDataFile("Get Instructor Full  Availability Job with no records", "");
					}else {
						jobNotificationEmailService.sendEmailForCRMAppointmentDataFile("Get Instructor Full  Availability Job", "");
					}
				}
				else
				{
					   log.warn("getInstructorFullAvailabiltyJob jobNotificationEmailService is null");
				}

			} else {
				log.warn("In GetInstructorAvailability Job   Service is null");
				throw new SchedulerException();
			}
	
		} catch (SchedulerException se) {
			//Code changes made for CRMI-338 Notification for Lesson Scheduler Data Email  Success and Failure
			if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				se.printStackTrace(new PrintWriter(errors));
				jobNotificationEmailService.sendEmailforJobFailureNotification("Get Instructor Full Availability Job",errors.toString());
			}
			else
			{
				   log.warn("In getInstructorAvailabilityJob.java ,Cancel Hold Appointments jobNotificationEmailService is null");
			}
			//End of Code changes made for CRMI-338 Notification for Lesson Scheduler Data Job Success and Failure
			log.warn("In GetInstructorAvailabilityJob.java at Caught a SchedulerException", se);
			
		}
		log.error("getInstructorFullAvailabilty   ends");
     }
 
     
}
