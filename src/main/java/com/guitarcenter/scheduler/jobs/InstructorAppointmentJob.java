package com.guitarcenter.scheduler.jobs;

import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.TimeZone;

import javax.persistence.Transient;

import org.quartz.CronTrigger;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.quartz.StatefulJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.guitarcenter.scheduler.dto.InstructorWeeklySuccessDTO;
import com.guitarcenter.scheduler.service.AppointmentEmailService;
import com.guitarcenter.scheduler.service.JobNotificationEmailService;

/**
 * Implements a job bean for sending instructor apppointments email.
 *
 * 
 */

public class InstructorAppointmentJob extends QuartzJobBean implements StatefulJob, Serializable {
    @Transient
    Logger log = LoggerFactory.getLogger(InstructorAppointmentJob.class);
    
    private static final long serialVersionUID = -5011766345613082109L;
    
    /**
     * The method that will be called by Quartz when the trigger fires.
     * 
     * @see @see org.springframework.scheduling.quartz.QuartzJobBean#executeInternal(org.quartz.JobExecutionContext)
     * @param jec
     * @throws JobExecutionException
     */
    @SuppressWarnings("unused")
	@Override
    protected void executeInternal(JobExecutionContext jec)
            throws JobExecutionException {
    	
    	
    	JobNotificationEmailService jobNotificationEmailService = null;
    	//GSSP-267 Job notification 
    	List<InstructorWeeklySuccessDTO> sucessMap = new ArrayList<InstructorWeeklySuccessDTO>();
    	   			
        if (log.isDebugEnabled()) {
            log.debug("about to execute Instructor schedule reminders, jec = {}", jec);
        }
        AppointmentEmailService appointmentEmailService = null;
        
        TimeZone tz =  ((CronTrigger)jec.getTrigger()).getTimeZone();        
        String timezoneID =  tz.getID();      
               
        try {        	    	   
                    	
            appointmentEmailService = (AppointmentEmailService)
                jec.getScheduler().getContext().get("appointmentEmailService");
            
          
            jobNotificationEmailService = (JobNotificationEmailService) jec.getScheduler().getContext()
					.get("jobNotificationEmailService");
					
			  if (appointmentEmailService != null) {
				  				  
				  sucessMap = appointmentEmailService.sendInstuctorScheduleEmail(timezoneID);
                
            	if (log.isDebugEnabled()) {
					log.debug("job completed");
				}							
				
				if(jobNotificationEmailService != null)
				{					
					//GSSP-267 Job notification 
					jobNotificationEmailService.sendEmailForJobNotificationforInstructorReport("Instructor Schedule Email Job for Time Zone - " + timezoneID,sucessMap);
				}
				else
				{
					   log.warn("jobNotificationEmailService is null");
				}			
                
                
            } else {
                log.warn("appointmentEmailService is null");
            }
        } catch (SchedulerException se) {
            log.warn("Caught a SchedulerException", se);
            
        	if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				se.printStackTrace(new PrintWriter(errors));
				jobNotificationEmailService.sendEmailforJobFailureNotification("Instructor Schedule Email Job for Time Zone - " + timezoneID,errors.toString());
			}
			else
			{
				   log.warn("jobNotificationEmailService is null");
			}
			
        }
    }

}
