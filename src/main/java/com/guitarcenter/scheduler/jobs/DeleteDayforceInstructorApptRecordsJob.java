package com.guitarcenter.scheduler.jobs;

import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.persistence.Transient;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.quartz.StatefulJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.guitarcenter.scheduler.service.DeleteDayforceInstructorApptRecordsService;
import com.guitarcenter.scheduler.service.JobNotificationEmailService;

/**
 * Implements a job bean for rCRM Appointment data File transferring.
 *
 */

public class DeleteDayforceInstructorApptRecordsJob extends QuartzJobBean implements StatefulJob, Serializable {
    
	@Transient
    Logger log = LoggerFactory.getLogger(DeleteDayforceInstructorApptRecordsJob.class);
    
    private static final long serialVersionUID = 4L;

     /**
     * The method that will be called by Quartz when the trigger fires.
     * 
     * @see @see org.springframework.scheduling.quartz.QuartzJobBean#executeInternal(org.quartz.JobExecutionContext)
     * @param jec
     * @throws JobExecutionException
     */
    @SuppressWarnings("unused")
	@Override
    protected void executeInternal(JobExecutionContext jec)
            throws JobExecutionException {

    	 log.error("Delete Day force  Instructor Appt Records Job Start");
		JobNotificationEmailService jobNotificationEmailService = null;
		DeleteDayforceInstructorApptRecordsService  deleteDayforceInstructorApptRecordsService = null;
		if (log.isDebugEnabled()) {
			log.debug("trigger fired; retrieve and process DeleteDayforceInstructorApptRecords. jec = {}", jec);
			
		}
		try {
		
			deleteDayforceInstructorApptRecordsService = (DeleteDayforceInstructorApptRecordsService)jec.getScheduler().getContext().get("deleteDayforceInstructorApptRecordsService");
			jobNotificationEmailService = (JobNotificationEmailService) jec.getScheduler().getContext().get("jobNotificationEmailService");
	 
			if (deleteDayforceInstructorApptRecordsService != null) {
			 
				
				try {
					List<String> customerEmailList = new ArrayList<String>();
					Date currentDateTime =  new Date();
					Date dateBefore = new Date(currentDateTime.getTime() - 1 * 24 * 3600 * 1000 );
					
 
					/*String startDate="10/14/2022";
					String endDate="10/16/2022";*/
					String startTime = getStartTime();
			        String endTime = getEndTime();
			        //Holding this changes for now as per new dayforce chagnes.
					// String result = deleteDayforceInstructorApptRecordsService.getDeleteDayforceInstructorApptRecords(startTime,endTime);
				} catch (Exception e) {
					throw new SchedulerException();
					
				}
				if(jobNotificationEmailService != null)
				{							
					  jobNotificationEmailService.sendEmailForCRMAppointmentDataFile("Employee Schedule Report Job", "");
				}
				else
				{
					   log.warn("Cancel Hold Appointments Service is null");
				}

			} else {
				log.warn("In CancelHoldAppointmentsJob class CancelHoldAppointmentsService is null");
				throw new SchedulerException();
			}
	
		} catch (SchedulerException se) {
			//Code changes made for CRMI-338 Notification for Lesson Scheduler Data Email  Success and Failure
			if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				se.printStackTrace(new PrintWriter(errors));
				jobNotificationEmailService.sendEmailforJobFailureNotification("Employee Schedule Report Job",errors.toString());
			}
			else
			{
				   log.warn("In cancelHoldAppointmentsJob.java ,Cancel Hold Appointments jobNotificationEmailService is null");
			}
			//End of Code changes made for CRMI-338 Notification for Lesson Scheduler Data Job Success and Failure
			log.warn("In CancelHoldAppointmentsJob.java at Caught a SchedulerException", se);
			
		}	
		System.out.println("END");
     }
    
    //2022-17-10
    private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-dd-MM HH:mm:ss");
    private static String getStartTime() {

      	 Calendar day = Calendar.getInstance();
      	
   		day.set(Calendar.MILLISECOND, 0);
   		day.set(Calendar.SECOND, 0);
   		day.set(Calendar.MINUTE, 0);
   		day.set(Calendar.HOUR_OF_DAY, 0);
   		 // day.add(Calendar.DATE, -1);
   		  //day.add(Calendar.DATE, 1);
      
          return simpleDateFormat.format(day.getTime());
      }

       private static String getEndTime() {

    	   Calendar day = Calendar.getInstance();
    	   	
    		day.set(Calendar.MILLISECOND, 0);
    		day.set(Calendar.SECOND, 50);
    		day.set(Calendar.MINUTE, 59);
    		day.set(Calendar.HOUR_OF_DAY, 23);
    		 day.add(Calendar.DATE, 14);
      
          return simpleDateFormat.format(day.getTime());
       } 
}
