package com.guitarcenter.scheduler.jobs;

import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;

import javax.persistence.Transient;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.quartz.StatefulJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.guitarcenter.scheduler.service.AppointmentEmailService;
import com.guitarcenter.scheduler.service.JobNotificationEmailService;

/**
 * Implements a job bean for CompletedLessonsJob
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public class CompletedLessonsJob extends QuartzJobBean implements StatefulJob, Serializable {
    @Transient
    Logger log = LoggerFactory.getLogger(CompletedLessonsJob.class);
    
    private static final long serialVersionUID = -5011766345613082108L;
    
    /**
     * The method that will be called by <PERSON>uartz when the trigger fires.
     * 
     * @see @see org.springframework.scheduling.quartz.QuartzJobBean#executeInternal(org.quartz.JobExecutionContext)
     * @param jec
     * @throws JobExecutionException
     */
    @SuppressWarnings("unused")
	@Override
    protected void executeInternal(JobExecutionContext jec)
            throws JobExecutionException {
    	
    	//Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure 
    	JobNotificationEmailService jobNotificationEmailService = null;
  			
        if (log.isDebugEnabled()) {
            log.debug("about to execute email reminders, jec = {}", jec);
        }
        log.error("Completed Lesson Job Started");
        AppointmentEmailService appointmentEmailService = null;
        try {
        	
          appointmentEmailService = (AppointmentEmailService)
                jec.getScheduler().getContext().get("appointmentEmailService");
            
          //Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure
			jobNotificationEmailService = (JobNotificationEmailService) jec.getScheduler().getContext()
					.get("jobNotificationEmailService");				
		
			  if (appointmentEmailService != null) {
				  try {
                appointmentEmailService.triggerAPIOnTrailLessoncomplection();
                jobNotificationEmailService.sendEmailForJobNotification("CompletedLessonsJob");
				  }catch(Exception e) {
					  StringWriter errors = new StringWriter();
						e.printStackTrace(new PrintWriter(errors));
					  jobNotificationEmailService.sendEmailforJobFailureNotification("CompletedLessonsJob",errors.toString());
				  }
                
            	//Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure 
				if (log.isDebugEnabled()) {
					log.debug("job completed");
				}

				//End of Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure
							
                
                
            } else {
                log.warn("CompletedLessonsJob is null");
                jobNotificationEmailService.sendEmailforJobFailureNotification("CompletedLessonsJob","CompletedLessonsJob is nul");
            }
        } catch (SchedulerException se) {
            log.warn("Caught a CompletedLessonsJob", se);
            
        	//Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure
			if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				se.printStackTrace(new PrintWriter(errors));
				
				jobNotificationEmailService.sendEmailforJobFailureNotification("CompletedLessonsJob",errors.toString());
			}
			else
			{
				   log.warn("CompletedLessonsJob is null");
			}
			//End of Code changes made for GSSP-172 Notification for Scheduler Batch Job Success and Failure
        }
        log.error("Completed Lesson Job Completed");
    }

}
