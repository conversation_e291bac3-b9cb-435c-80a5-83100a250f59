package com.guitarcenter.scheduler.jobs;
import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.util.TimeZone;

import javax.persistence.Transient;

import org.quartz.CronTrigger;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.quartz.StatefulJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.guitarcenter.scheduler.service.InstructorFloorTimeService;
import com.guitarcenter.scheduler.service.JobNotificationEmailService;

//New class created for GSSP - 272
public class InstructorFloorTimeJob extends QuartzJobBean implements StatefulJob, Serializable{

	@Transient
    Logger log = LoggerFactory.getLogger(InstructorFloorTimeJob.class);
    
    private static final long serialVersionUID = -5123766345613082109L;
    
    
    /**
     * The method that will be called by Quartz when the trigger fires.
     * 
     * @see @see org.springframework.scheduling.quartz.QuartzJobBean#executeInternal(org.quartz.JobExecutionContext)
     * @param jec
     * @throws JobExecutionException
     */
    @SuppressWarnings("unused")
	@Override
    protected void executeInternal(JobExecutionContext jec)
            throws JobExecutionException {
    	
    	
    	JobNotificationEmailService jobNotificationEmailService = null;
        if (log.isDebugEnabled()) {
            log.debug("about to execute instructorFloorTimeJob, jec = {}", jec);
        }
        InstructorFloorTimeService instructorFloorTimeService = null;
        
        TimeZone tz =  ((CronTrigger)jec.getTrigger()).getTimeZone();        
        String timezoneID =  tz.getID();      
               
        try {        	    	   
        	instructorFloorTimeService = (InstructorFloorTimeService)
                jec.getScheduler().getContext().get("instructorFloorTimeService");
            
        	
            jobNotificationEmailService = (JobNotificationEmailService) jec.getScheduler().getContext()
					.get("jobNotificationEmailService");
            
			  if (instructorFloorTimeService != null) {		  
				  instructorFloorTimeService.generateInstructorFloorTimeReport();
                
            	if (log.isDebugEnabled()) {
					log.debug("job completed");
				}							
				
				if(jobNotificationEmailService != null)
				{					
					jobNotificationEmailService.sendEmailForJobNotification("Weekly Instructor In-Store floor time report " );
				}
				else
				{
					   log.warn("jobNotificationEmailService is null");
				}			
                
                
            } else {
                log.warn("Unable to send weekly Instructor floor time report due to instructorFloorTimeService null value");
            }
        } catch (SchedulerException se) {
            log.warn("Caught a SchedulerException", se);
            
        	if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				se.printStackTrace(new PrintWriter(errors));
				jobNotificationEmailService.sendEmailforJobFailureNotification("Weekly Instructor In-Store floor time report ",errors.toString());
			}
			else
			{
				   log.warn("jobNotificationEmailService is null");
			}
			
        }        
        catch(Exception e)
        {
        	
        }
    }
}
