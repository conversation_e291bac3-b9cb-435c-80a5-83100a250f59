package com.guitarcenter.scheduler.jobs;

import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;

import javax.persistence.Transient;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.quartz.StatefulJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.guitarcenter.scheduler.integration.service.InstructorPersonalDetailsIntegrationService;
import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;
import com.guitarcenter.scheduler.service.JobNotificationEmailService;


//New class created for GSSP-275
public class ADPFileJob extends QuartzJobBean implements StatefulJob, Serializable {
    @Transient
    Logger log = LoggerFactory.getLogger(ADPFileJob.class);
    
    private static final long serialVersionUID = -8750509713905705093L;
    
    /**
     * The method that will be called by <PERSON>uartz when the trigger fires.
     * 
     * @see @see org.springframework.scheduling.quartz.QuartzJobBean#executeInternal(org.quartz.JobExecutionContext)
     * @param jec
     * @throws JobExecutionException
     */
    @SuppressWarnings("unused")
	@Override
    protected void executeInternal(JobExecutionContext jec)
        throws JobExecutionException
    {
    	
    	
    	JobNotificationEmailService jobNotificationEmailService = null;
    	    	
        if (log.isDebugEnabled()) {
            log.debug("trigger fired; retrieve and process ADP files. jec = {}",
                      jec);
        }
        
        try {
        	
        	InstructorPersonalDetailsIntegrationService instructorPersonalDetailsIntegrationService = null;
        	instructorPersonalDetailsIntegrationService = (InstructorPersonalDetailsIntegrationService)
                     			jec.getScheduler().getContext().get("instructorPersonalDetailsIntegrationService");
        	jobNotificationEmailService = (JobNotificationEmailService) jec.getScheduler().getContext()
       					.get("jobNotificationEmailService");       	 	
       	 	
        	if (instructorPersonalDetailsIntegrationService != null) {
                if (log.isDebugEnabled()) {
                    log.debug("about to call instructorPersonalDetailsIntegrationService.scheduledUpdate()");
                }
               
                instructorPersonalDetailsIntegrationService.scheduledUpdate();
            } else {
                log.warn("instructorPersonalDetailsIntegrationService is null");
            }
			if (log.isDebugEnabled()) {
				log.debug("job completed");
			}
						
			if(jobNotificationEmailService != null)
			{				
				jobNotificationEmailService.sendEmailForJobNotification("ADP Job");
			}
			else
			{
				   log.warn("jobNotificationEmailService is null");
			}
					
        } catch (IntegrationServiceException ise) {
            log.warn("Caught an IntegrationServiceException retrieving or " +
                     "processing ADP Instructor file", ise);
            
          
			if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				ise.printStackTrace(new PrintWriter(errors));				
				jobNotificationEmailService.sendEmailforJobFailureNotification("ADP Job",errors.toString());
			}
			else
			{
				   log.warn("jobNotificationEmailService is null");
			}
			
        } catch (SchedulerException se) {
            log.warn("Caught a SchedulerException", se);
            
            
			if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				se.printStackTrace(new PrintWriter(errors));				
				jobNotificationEmailService.sendEmailforJobFailureNotification("ADP Job",errors.toString());
			}
			else
			{
				   log.warn("jobNotificationEmailService is null");
			}
			
        }
        
       
    }    
    
}
