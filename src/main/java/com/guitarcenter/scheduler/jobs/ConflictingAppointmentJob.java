package com.guitarcenter.scheduler.jobs;

import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.util.TimeZone;

import javax.persistence.Transient;

import org.quartz.CronTrigger;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.quartz.StatefulJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.guitarcenter.scheduler.service.JobNotificationEmailService;
import com.guitarcenter.scheduler.service.ReportService;

/**
 * Implements a job bean for sending conflicting appointments as email.
 *
 * 
 */

public class ConflictingAppointmentJob extends QuartzJobBean implements StatefulJob, Serializable {
    @Transient
    Logger log = LoggerFactory.getLogger(ConflictingAppointmentJob.class);
    
    private static final long serialVersionUID = -5011766345613082129L;
    
    /**
     * The method that will be called by <PERSON>uartz when the trigger fires.
     * 
     * @see @see org.springframework.scheduling.quartz.QuartzJobBean#executeInternal(org.quartz.JobExecutionContext)
     * @param jec
     * @throws JobExecutionException
     */
    @SuppressWarnings("unused")
	@Override
    protected void executeInternal(JobExecutionContext jec)
            throws JobExecutionException {
    	
    	
    	JobNotificationEmailService jobNotificationEmailService = null;
    	   			
        if (log.isDebugEnabled()) {
            log.debug("about to execute ConflictingAppointment job, jec = {}", jec);
        }
        ReportService reportService = null;
        
        TimeZone tz =  ((CronTrigger)jec.getTrigger()).getTimeZone();        
        String timezoneID =  tz.getID();      
               
        try {        	    	   
                    	
        	reportService = (ReportService)
                jec.getScheduler().getContext().get("reportService");
            
          
            jobNotificationEmailService = (JobNotificationEmailService) jec.getScheduler().getContext()
					.get("jobNotificationEmailService");
					
			  if (reportService != null) {
				  				  
				  reportService.generateConflictReportBatch();
                
            	if (log.isDebugEnabled()) {
					log.debug("job completed");
				}							
				
				if(jobNotificationEmailService != null)
				{					
					jobNotificationEmailService.sendEmailForJobNotification("Conflicting Appointment Batch " );
				}
				else
				{
					   log.warn("jobNotificationEmailService is null");
				}			
                
                
            } else {
                log.warn("ReportService is null");
            }
        } catch (SchedulerException se) {
            log.warn("Caught a SchedulerException", se);
            
        	if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				se.printStackTrace(new PrintWriter(errors));
				jobNotificationEmailService.sendEmailforJobFailureNotification("Conflicting Appointment Batch ",errors.toString());
			}
			else
			{
				   log.warn("jobNotificationEmailService is null");
			}
			
        }
        
        catch(Exception e)
        {
        	
		    log.warn("Caught a SchedulerException", e);
		            
		        	if(jobNotificationEmailService != null)
					{
						StringWriter errors = new StringWriter();
						e.printStackTrace(new PrintWriter(errors));
						jobNotificationEmailService.sendEmailforJobFailureNotification("Conflicting Appointment Batch ",errors.toString());
					}
					else
					{
						   log.warn("jobNotificationEmailService is null");
					}
		        }
    }

}
