package com.guitarcenter.scheduler.jobs;

import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.util.TimeZone;

import javax.persistence.Transient;

import org.quartz.CronTrigger;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import org.quartz.StatefulJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.guitarcenter.scheduler.service.DailySubscriptionReportService;
import com.guitarcenter.scheduler.service.InstructorActivitiesService;
import com.guitarcenter.scheduler.service.JobNotificationEmailService;

/**
 * Runs Instructor activities related job 
 * 
 */
public class DailySubscriptionReportEmailJob extends QuartzJobBean implements StatefulJob, Serializable{
	//GSSP-240 - Sends e-mail to store user with attachment
	
    @Transient
    Logger log = LoggerFactory.getLogger(DailySubscriptionReportEmailJob.class);
    
    private static final long serialVersionUID = -5123766345613082108L;
    
    
    /**
     * The method that will be called by Quartz when the trigger fires.
     * 
     * @see @see org.springframework.scheduling.quartz.QuartzJobBean#executeInternal(org.quartz.JobExecutionContext)
     * @param jec
     * @throws JobExecutionException
     */
    @SuppressWarnings("unused")
	@Override
    protected void executeInternal(JobExecutionContext jec)
            throws JobExecutionException {
    	
    	
    	JobNotificationEmailService jobNotificationEmailService = null;
    	   			
        if (log.isDebugEnabled()) {
            log.debug("about to execute DailySubscriptionReport EmailJob, jec = {}", jec);
        }
        DailySubscriptionReportService dailySubscriptionReportService = null;
        
        TimeZone tz =  ((CronTrigger)jec.getTrigger()).getTimeZone();        
        String timezoneID =  tz.getID();      
               
        try {        	    	   
        	dailySubscriptionReportService = (DailySubscriptionReportService)
                jec.getScheduler().getContext().get("dailySubscriptionReportService");
            
          
            jobNotificationEmailService = (JobNotificationEmailService) jec.getScheduler().getContext()
					.get("jobNotificationEmailService");
					
			  if (dailySubscriptionReportService != null) {
				  				  
				  dailySubscriptionReportService.generateDailySubscriptionReportDetails();
                
            	if (log.isDebugEnabled()) {
					log.debug("job completed");
				}							
				
				if(jobNotificationEmailService != null)
				{					
					jobNotificationEmailService.sendEmailForJobNotification("Daily Subscription Report " );
				}
				else
				{
					   log.warn("jobNotificationEmailService is null");
				}			
                
                
            } else {
                log.warn("Unable to send daily Daily Subscription Report  instructorActivitiesService null value");
            }
        } catch (SchedulerException se) {
            log.error("Caught a DailySubscriptionReport SchedulerException", se);
            
        	if(jobNotificationEmailService != null)
			{
				StringWriter errors = new StringWriter();
				se.printStackTrace(new PrintWriter(errors));
				jobNotificationEmailService.sendEmailforJobFailureNotification("Daily Subscription Report ",errors.toString());
			}
			else
			{
				   log.warn("jobNotificationEmailService is null");
			}
			
        }        
        catch(Exception e)
        {
        	
        }
    }

}
