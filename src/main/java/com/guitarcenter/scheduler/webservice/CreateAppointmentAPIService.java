package com.guitarcenter.scheduler.webservice;

import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ACTIVITY_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_APPOINTMENT_TIMEOFF;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ATTENDEES;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_CUSTOMER_APPOINTMENT;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_CUSTOMER_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_DATE_ILLEGAL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_INSTRUCTOR_ACTIVITY;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_INSTRUCTOR_APPOINTMENT;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_INSTRUCTOR_AVAILABILITY;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_INSTRUCTOR_REQUIRED;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_JUMP_START_RECURRENT;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_MINIMUM_DURATION;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ONLINE_TRIAL_RECURRENT;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_PROFILELOCATION_AVAILABILITY;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ROOM_ACTIVITY;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ROOM_AVAILABILITY;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ROOM_DISABLED;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ROOM_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_SEC_EMAIL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_SERVICE_ACTIVITY;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_SERVICE_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_START_DATE_ILLEGAL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_START_DATE_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_START_DATE_ONE_YEAR;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_START_TIME_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_STATUS_API_EMAIL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_STATUS_API_REFERENCE_NO;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_STATUS_INVALID;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_TIME_ILLEGAL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_TIME_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_TO_PROFILE_DATE_WITH_APPPOINTMENT;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_TO_PROFILE_DATE_WITH_APPPOINTMENT_CREATION;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import com.guitarcenter.scheduler.service.*;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.guitarcenter.scheduler.common.util.AvailabilityUtil;
import com.guitarcenter.scheduler.common.util.CalendarUtil;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.MessageConstants;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dto.CreateAppointmentDTO;
import com.guitarcenter.scheduler.dto.CustomerAppointmentDetailsDTO;
import com.guitarcenter.scheduler.dto.CustomerDTO;
import com.guitarcenter.scheduler.dto.InstoreAvailableDTO;
import com.guitarcenter.scheduler.dto.OnLineAvailableDTO;
import com.guitarcenter.scheduler.dto.OnLineInstoreAvailableDTO;
import com.guitarcenter.scheduler.dto.RoomDTO;
import com.guitarcenter.scheduler.dto.UpdateAppointmentDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.AppointmentSeries;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.LocationProfile;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.RequiresInstructor;
import com.guitarcenter.scheduler.webservice.dto.CustomerAppointmentDetailsResultDTO;
import com.guitarcenter.scheduler.webservice.dto.UpdateLessonServiceDTO;

@RestController
public class CreateAppointmentAPIService {

	 
	private static final String MESSAGE_KEY            = "message";
	private static final String MESSAGE_VALUE		   = "Create Appointment Success!";
	private static final String STATUS_KEY			   = "status";
	
	public static final String SPLITOR_SLASH		   = "/";
	private static final int[] END_TIME				   = new int[]{23, 59};
	private static final int ZERO_TIME				   = 0;
	
	@Autowired
	private AppointmentSeriesService appointmentSeriesService;
	
	@Autowired
	private AppointmentDAO appointmentDAO;
	
	@Autowired
	private ServiceService serviceService;
	
	@Autowired
	private CustomerService customerService;

	@Resource
	private AvailabilityService availabilityService;

	@Autowired
	private RoomService roomService;
	
	@Autowired
	private ActivityService activityService;

	private static final Logger		LOG					        = LoggerFactory.getLogger("schedulerlog");
	
	@Autowired
	private InstructorService instructorService;
	
	@Autowired
	private OnlineAvailabilityService onlineAvailabilityService;
	
	@Autowired
	private InstoreAvailabilityService instoreAvailabilityService;

	@Autowired
	private ValidationService validationService;

	@Autowired
	private AppointmentService appointmentService;
	
	@Autowired
	private LocationManagerService locationManagerService;

	@Autowired
	private AppointmentEmailService appointmentEmailService;
	
	@Autowired
	CancelHoldAppointmentsService cancelHoldAppointmentsService;

	@Autowired
	AsyncEmailService  asyncEmailService;

	
	@RequestMapping(value = "/updateAppointment", method = RequestMethod.POST, headers = { "content-type=application/json" })
	public Map<String, Object> updateAppointmentApi(@RequestBody final UpdateAppointmentDTO dto) {
		
		//System.out.println("updateAppointment Api  dto "+dto);
		LOG.error("Info Loading updateAppointment Api  dto {} ", dto);
		Map<String, Object>  result = null;
		try {
		
			 result = cancelHoldAppointmentsService.appointmentStatusUpdateService(dto);
			 if(result.get("message") != null)LOG.error("Responsce of updateAppointmentApi   "+ result.get("message"));
		}catch(Exception e) {
		
			Map<String, Object> map = new HashMap<String, Object>();
			map.put(STATUS_KEY, true);
			map.put(MESSAGE_KEY, "Internal Error, Please try again later!");
			if(map.get("message") != null)LOG.error("Responsce of updateAppointmentApi   "+ map.get("message"));
			return map;
			
		}
		
		return result;
		
		
	}
	

	@RequestMapping(value = "/createAppointment", method = RequestMethod.POST, headers = { "content-type=application/json" })
	public Map<String, Object> createAppointmentApi(@RequestBody final CreateAppointmentDTO dto) {
		if(LOG.isDebugEnabled()) {
		   LOG.debug("CalendarController.createAppointment: start");
		} 
		 
		LOG.error("Info Loading create Appointment Api  dto {} ", dto);
		 
		Map<String, Object> map = new HashMap<String, Object>();
		if(null == dto) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Build appointmentDTO error!");
			if(map.get("message") != null)LOG.error("error 1 of createAppointmentApi   "+ map.get("message"));
			return map;
		}
		if("".equals(dto.getCustomerId()))dto.setCustomerId(null);
		if(null == dto.getCustomerId()) {
			if(null==dto.getAppStatus() || !dto.getAppStatus().equals("H")){
				map.put(MESSAGE_KEY, VALIDATION_STATUS_INVALID);
				map.put(STATUS_KEY, false);
				if(map.get("message") != null)LOG.error("error 2 of createAppointmentApi   "+ map.get("message"));
				return map;
			}
			
		}
		Customer customer = customerService.getCustomerByExternalId(1l,dto.getCustomerId());
		if( "N".equals(dto.getAppStatus()))
		{
			if(customer == null)
			{
				map.put(STATUS_KEY, false);
				map.put(MESSAGE_KEY, "Invalid Customer!");
				if(map.get("message") != null)LOG.error("error 3 of createAppointmentApi   "+ map.get("message"));
				return map;
			}else{ 
			    dto.setCustomerId(customer.getCustomerId()+"");
			}
		} else if(customer != null) {
			map.put(MESSAGE_KEY, VALIDATION_STATUS_INVALID);
			map.put(STATUS_KEY, false);
			if(map.get("message") != null)LOG.error("error 4 of createAppointmentApi   "+ map.get("message"));
			return map;
		}
		
//Log for conflict appointment in create appointment -GCSS 299
		if(dto != null)	{
			LOG.error("Before Creating Appointment -  log underHostAddress: " +SystemUtil.getHostAddress() != null ? SystemUtil.getHostAddress() : "Invalide Host Name");
		}
		long siteId = 1L;
		 Location location = null;
		Person person =  new Person();
		List<Location> locations = locationManagerService.findByExternalId(siteId, dto.getLocationId());
		
		if (locations.size() == 1) {
            location = locations.get(0);
		}else{
				map.put(STATUS_KEY, false);
				map.put(MESSAGE_KEY, "Incorrect Location ID");
				if(map.get("message") != null)LOG.error("error first of createAppointmentApi   "+ map.get("message"));
				return map;
		}
		long profileId = location.getLocationProfile().getProfileId();

		LocationProfile locationProfile = new LocationProfile();
		locationProfile.setProfileId(profileId);
		Site site = new Site();
		site.setSiteId(siteId);
		person.setPersonId(1l);
		//#####################
		Date startTime = new Date();
		if((null != dto.getStartDate() && 0 != dto.getStartDate().length())) {
			startTime = DateTime.parse(dto.getStartDate() + " " + "00:00", DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
			if(null != dto.getStartTime() && 0 != dto.getStartTime().length()) {
				startTime = DateTime.parse(dto.getStartDate() + " " + dto.getStartTime(), DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
			}
		} else {
			startTime = new DateTime(startTime).withTime(0, 0, 0, 0).toDate();
		}
		String durationString = dto.getDuration();
		int duration;
		 
		if(null != durationString && 0 != durationString.length()) {
			duration = Integer.parseInt(durationString);
		} else {
			duration = 1440;
		}
		Date endTime = SystemUtil.getEndTimeIfOnMidnight(new DateTime(startTime).plusMinutes(duration).toDate());
		//0000000000000000000000
		Long appId = null;
         List<RoomDTO> roomList = roomService.loadRoomListByProfileIdAndActivityIdAndDateTime(profileId,dto.getServiceId(),((null == dto.getActivityId() || 0 == dto.getActivityId().length())) ? null: Long.parseLong(dto.getActivityId()),(null == dto.getInstructorId() ? null : dto.getInstructorId()),new DateTime(startTime).plusSeconds(1).toDate(), new DateTime(endTime).minusSeconds(1).toDate(), appId);

         if(roomList != null && roomList.size() > 0 && roomList.get(0)!= null){
        	 dto.setRoomId(roomList.get(0).getRoomId());
         }
		//###########################
		
		try {
			map = this.checkAppointment(dto,profileId);
			if(map.get("message") != null)LOG.error("error checkAppointment of createAppointmentApi   "+ map.get("message"));
			if((Boolean)map.get(STATUS_KEY)){
				
				

				
				
				AppointmentSeries series = dto.getAppointmentSeries();
				series.setSite(site);
				series.setUpdatedBy(person);
				series.setLocationProfile(locationProfile);
				
				//Changes made for GSSP-234[
				if(null != dto.getNote())
					series.setNote(dto.getNote());
				
				AppointmentSeries appointmentSeries = appointmentSeriesService.createAppointmentSeries(series, person);
				Appointment appointment = dto.getAppointment();
				
				appointment.setSite(site);
				appointment.setAppointmentSeries(appointmentSeries);
				appointment.setUpdatedBy(person);
				appointment.setLocationProfile(locationProfile);
				appointmentService.createAppointment(appointment,dto);
				//fix the bug GCSS-411
				if(appointment.getCustomers() != null){
					//GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-starts
										
					Room room = roomService.getRoom(dto.getRoomId());
					Activity  activityName  = activityService.getActivityByActivityId(Long.parseLong(dto.getActivityId()));
				    String instructorName=appointmentService.findInstructorName(dto);
					
				 
					//For GSSP-243 send email with cc Associates/Managers/Leads
					//changes made for 237 method name changed
					appointmentEmailService.sendEmailForCreateAppt(appointment,dto.getIsRecurring(),Integer.parseInt(dto.getDuration()),instructorName, room,activityName, profileId);
				}//GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-ends
				map.put(STATUS_KEY, true);
				map.put(MESSAGE_KEY, MESSAGE_VALUE);
				//Log for conflict appointment in create appointment -GSSP-299
				if(appointment != null && appointment.getAppointmentSeries().getAppointmentSeriesId() != null &&  appointment.getUpdated() != null ){
				LOG.error("After creating Appointment -"
						+ "Appointment created with the appointmentSeriesId " + appointment.getAppointmentSeries().getAppointmentSeriesId() 
						+ " Appointment created - timeStamp " + appointment.getUpdated());
				}
			}
		} catch (Exception e) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Create Appointment Failed");
			if(map.get("message") != null)LOG.error("error 1 of createAppointmentApi   "+ map.get("message"));
			LOG.error("Caught an exception from CalendarController.createAppointment: {}", e);
		}
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.createAppointment: end");
		}
		if(map.get("message") != null)LOG.error("Responsce of end Create Appointment "+ map.get("message"));
		return map;
	}

	@RequestMapping(value = "/createAppointmentWithCustomer", method = RequestMethod.POST, headers = { "content-type=application/json" })
	public Map<String, Object> createAppointmentApiWithCustomer(@RequestBody final CreateAppointmentDTO dto) {
		if(LOG.isDebugEnabled()) {
		   LOG.debug("CalendarController.createAppointment: start");
		} 
		Map<String, Object> map = new HashMap<String, Object>();
		if(null == dto) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Build appointmentDTO error!");
			if(map.get("message") != null)LOG.error("error 1 of createAppointmentApi   "+ map.get("message"));
			return map;
		}
		
		LOG.error("Customer details dto   :"+dto );
		LOG.error("Customer details:   "+dto.getCustomerDetails()); 
		if(dto.getCustomerId()==null && dto.getCustomerDetails() != null)dto.setCustomerId(dto.getCustomerDetails().getCustomerId());
		LOG.error("Info Loading create Appointment Api  dto {} ", dto);
		CustomerDTO custDTO = dto.getCustomerDetails();
		
		//System.out.println("Customer details custDTO :"+custDTO );
		map = this.checkAppointmentCreate(dto,custDTO);
		if(!(Boolean)map.get(STATUS_KEY)){
			map.put(STATUS_KEY, false);
			return map;
		}
		
		
		if("".equals(dto.getCustomerId()))dto.setCustomerId(null);
		if(null == dto.getCustomerId()) {
			if(null==dto.getAppStatus() || !dto.getAppStatus().equals("H")){
				map.put(MESSAGE_KEY, VALIDATION_STATUS_INVALID);
				map.put(STATUS_KEY, false);
				if(map.get("message") != null)LOG.error("error 2 of createAppointmentApi   "+ map.get("message"));
				return map;
			}
			
		}/*else if(dto.getAppStatus()==null || dto.getAppStatus().equals("H")|| dto.getAppStatus().equals("Y")|| dto.getAppStatus().equals("")){
			map.put(MESSAGE_KEY, VALIDATION_STATUS_INVALID);
			map.put(STATUS_KEY, false);
			if(map.get("message") != null)LOG.error("error 2 of createAppointmentApi   "+ map.get("message"));
			return map;
		}*/
		
		
		if( "H".equals(dto.getAppStatus()) && (dto.getCustomerId() !=null  || "".equals(dto.getCustomerId()) )){
			map.put(MESSAGE_KEY, VALIDATION_STATUS_INVALID);
			map.put(STATUS_KEY, false);
			if(map.get("message") != null)LOG.error("error 13 of createAppointmentApi   "+ map.get("message"));
			return map;
		}
		
		//dto.setCustomerId(dto.getCustomerDetails().getCustomerId());
		//System.out.println("customerId::"+dto.getCustomerId());
		Customer customer = customerService.getCustomerByExternalId(1l,dto.getCustomerId());
		
		if( "H".equals(dto.getAppStatus()) && customer != null){
			map.put(MESSAGE_KEY, VALIDATION_STATUS_INVALID);
			map.put(STATUS_KEY, false);
			if(map.get("message") != null)LOG.error("error 13 of createAppointmentApi   "+ map.get("message"));
			return map;
		}
		
		if( "N".equals(dto.getAppStatus()))
		{
			if(customer == null)
			{
				if(custDTO == null){
					map.put(MESSAGE_KEY, "Invalid Customer Details");
					map.put(STATUS_KEY, false);
					if(map.get("message") != null)LOG.error("error 8 of createAppointmentApi   "+ map.get("message"));
					return map;
				}
				if (custDTO.getEmail() != null && !"".equals(custDTO.getEmail().trim())) {
					String regex = "^(.+)@(.+)$";
					Pattern pattern = Pattern.compile(regex);
					Matcher matcher = pattern.matcher(custDTO.getEmail());
					 if(matcher.matches()){
						 //map.put(STATUS_KEY, matcher.matches()); 
					 }else{
					map.put(MESSAGE_KEY, "The Customer email format is invalid");
					if(map.get("message") != null)LOG.error("error 14 of createAppointmentApi   "+ map.get("message"));
					map.put(STATUS_KEY, matcher.matches());
					return map; 
					 }
					 
				}
				if(custDTO != null){
				if((custDTO != null)  && (null== custDTO.getFirstName()||"".equals(custDTO.getFirstName()))) {
					map.put(MESSAGE_KEY, "First name should not be null");
					map.put(STATUS_KEY, false);
					if(map.get("message") != null)LOG.error("error 9 of createAppointmentApi   "+ map.get("message"));
					return map;
				}
				
				else if(null== custDTO.getLastName()||"".equals(custDTO.getLastName())) {
					map.put(MESSAGE_KEY, "Last name should not be null");
					map.put(STATUS_KEY, false);
					if(map.get("message") != null)LOG.error("error 10 of createAppointmentApi   "+ map.get("message"));
					return map;
				}
				
				else if(null== custDTO.getCustomerStatus()||"".equals(custDTO.getCustomerStatus())) {
					map.put(MESSAGE_KEY, "Customer status should not be null should not be null");
					map.put(STATUS_KEY, false);
					if(map.get("message") != null)LOG.error("error 11 of createAppointmentApi   "+ map.get("message"));
					return map;
				}
				else {
					try {
						CustomerDTO c = new CustomerDTO();
						 //create Customer  STEVEN JACKSON
						String firstName = custDTO.getFirstName();
						String lastName = custDTO.getLastName();
					  
						c.setParentFullName(custDTO.getParentFullName());
						c.setFirstName(firstName); 
						c.setLastName(lastName);
						c.setSite("GCS");
						c.setExternalSource("FROM ATG");
						c.setLocation_external_id(dto.getLocationId());
						c.setExternalId(dto.getCustomerId());
						c.setCustomerStatus(custDTO.getCustomerStatus());
						c.setEmail(custDTO.getEmail());
						c.setPhone(custDTO.getPhone());
						c.setBadgeNumber(custDTO.getBadgeNumber());
						c.setLessonCounts(custDTO.getLessonCounts());

							customerService.updateFromExternal(c);
						} catch (Exception e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
							map.put(MESSAGE_KEY, "Error at Customer create or update ");
							map.put(STATUS_KEY, false);
							return map;
						}
					customer = customerService.getCustomerByExternalId(1l,dto.getCustomerId());
					 dto.setCustomerId(customer.getCustomerId()+"");
				
				}	
			  }
			}else{ 
			    dto.setCustomerId(customer.getCustomerId()+"");
			}
		} else if(customer != null) {
			map.put(MESSAGE_KEY, VALIDATION_STATUS_INVALID);
			map.put(STATUS_KEY, false);
			if(map.get("message") != null)LOG.error("error 4 of createAppointmentApi   "+ map.get("message"));
			return map;
		}
		
//Log for conflict appointment in create appointment -GCSS 299
		if(dto != null)	{
			LOG.error("Before Creating Appointment -  log underHostAddress: " +SystemUtil.getHostAddress() != null ? SystemUtil.getHostAddress() : "Invalide Host Name");
		}
		long siteId = 1L;
		 Location location = null;
		Person person =  new Person();
		List<Location> locations = locationManagerService.findByExternalId(siteId, dto.getLocationId());
		
		if (locations.size() == 1) {
            location = locations.get(0);
		}else{
				map.put(STATUS_KEY, false);
				map.put(MESSAGE_KEY, "Incorrect Location ID");
				if(map.get("message") != null)LOG.error("error first of createAppointmentApi   "+ map.get("message"));
				return map;
		}
		long profileId = location.getLocationProfile().getProfileId();

		LocationProfile locationProfile = new LocationProfile();
		locationProfile.setProfileId(profileId);
		Site site = new Site();
		site.setSiteId(siteId);
		person.setPersonId(1l);
		//#####################
		Date startTime = new Date();
		if((null != dto.getStartDate() && 0 != dto.getStartDate().length())) {
			startTime = DateTime.parse(dto.getStartDate() + " " + "00:00", DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
			if(null != dto.getStartTime() && 0 != dto.getStartTime().length()) {
				startTime = DateTime.parse(dto.getStartDate() + " " + dto.getStartTime(), DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
			}
		} else {
			startTime = new DateTime(startTime).withTime(0, 0, 0, 0).toDate();
		}
		String durationString = dto.getDuration();
		int duration;
		 
		if(null != durationString && 0 != durationString.length()) {
			duration = Integer.parseInt(durationString);
		} else {
			duration = 1440;
		}
		Date endTime = SystemUtil.getEndTimeIfOnMidnight(new DateTime(startTime).plusMinutes(duration).toDate());
		
		Long appId = null;
		//OLL-3612 Bug Fix
         List<RoomDTO> roomList = roomService.loadRoomListByProfileIdAndActivityIdAndDateTime(profileId,dto.getServiceId(),((null == dto.getActivityId() || 0 == dto.getActivityId().length())) ? null: Long.parseLong(dto.getActivityId()),(null == dto.getInstructorId() ? null : dto.getInstructorId()),new DateTime(startTime).plusSeconds(1).toDate(), new DateTime(endTime).minusSeconds(1).toDate(), appId);
        // roomList.subList(2, roomList.size()).clear();
         if(null != roomList && roomList.size() <= 0) {
        	map.put(MESSAGE_KEY, VALIDATION_ROOM_AVAILABILITY);
			map.put(STATUS_KEY, false);
			map.put("isConflicting", true);	
			
         }
         
         try {
			
			 for(RoomDTO room : roomList) {
		         if(null != room){
		        	 dto.setRoomId(room.getRoomId());
		        	 map = this.checkAppointment(dto,profileId);
		        	 if(null!= map &&  map.get("status").equals(false) &&
		        			 map.get("message") != null && map.get("message").equals(MessageConstants.VALIDATION_ROOM_AVAILABILITY)) {
		        		LOG.error("error checkAppointment of createAppointmentApi   "+ map.get("message"));
		        		continue;
		        	 }else {
		        		 break;
		        	 }
		         }
	         }
			//map = this.checkAppointment(dto,profileId);
			
			if((Boolean)map.get(STATUS_KEY)){
				
				

				
				
				AppointmentSeries series = dto.getAppointmentSeries();
				series.setSite(site);
				series.setUpdatedBy(person);
				series.setLocationProfile(locationProfile);
				
				//Changes made for GSSP-234[
				if(null != dto.getNote())
					series.setNote(dto.getNote());
				
				AppointmentSeries appointmentSeries = appointmentSeriesService.createAppointmentSeries(series, person);
				Appointment appointment = dto.getAppointment();
				
				appointment.setSite(site);
				appointment.setAppointmentSeries(appointmentSeries);
				appointment.setUpdatedBy(person);
				appointment.setLocationProfile(locationProfile);
				appointmentService.createAppointment(appointment,dto);
				//fix the bug GCSS-411
				if(appointment.getCustomers() != null){
					//GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-starts
										
					Room room = roomService.getRoom(dto.getRoomId());
					Activity  activityName  = activityService.getActivityByActivityId(Long.parseLong(dto.getActivityId()));
				    String instructorName=appointmentService.findInstructorName(dto);
					
				 
					//For GSSP-243 send email with cc Associates/Managers/Leads
					//changes made for 237 method name changed
					//OLL-3811 Changes
					asyncEmailService.sendEmailForCreateApptCRMFlowAsync(appointment,dto.getIsRecurring(),Integer.parseInt(dto.getDuration()),instructorName, room,activityName, profileId,dto.getOrderId());
					//appointmentEmailService.sendEmailForCreateApptCRMFlow(appointment,dto.getIsRecurring(),Integer.parseInt(dto.getDuration()),instructorName, room,activityName, profileId,dto.getOrderId());
				}//GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-ends
				map.put(STATUS_KEY, true);
				map.put(MESSAGE_KEY, MESSAGE_VALUE);
				//Log for conflict appointment in create appointment -GSSP-299
				if(appointment != null && appointment.getAppointmentSeries().getAppointmentSeriesId() != null &&  appointment.getUpdated() != null ){
				LOG.error("After creating Appointment -"
						+ "Appointment created with the appointmentSeriesId " + appointment.getAppointmentSeries().getAppointmentSeriesId() 
						+ " Appointment created - timeStamp " + appointment.getUpdated());
				}
			}
		} catch (Exception e) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Create Appointment Failed");
			if(map.get("message") != null)LOG.error("error 1 of createAppointmentApi   "+ map.get("message"));
			LOG.error("Caught an exception from CalendarController.createAppointment: {}", e);
		}
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.createAppointment: end");
		}
		if(map.get("message") != null)LOG.error("Responsce of end Create Appointment "+ map.get("message"));
		return map;
	}
	
	@RequestMapping(value = "/getAppointmentDetailsByCustomer", method = RequestMethod.POST, headers = { "content-type=application/json" })
	public Map<String, Object> getAppointmentDetailsByCustomer(@RequestBody final CustomerAppointmentDetailsDTO dto) {
		if(LOG.isDebugEnabled()) {
		   LOG.debug("Get appointment details by customer: start");
		} 
		Map<String, Object> map = new HashMap<String, Object>();
		if(null == dto || dto.getCustomerId() == null || dto.getStartDate() == null || dto.getEndDate() == null) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Invalid Input!");
			if(map.get("message") != null)
				LOG.error("error 1 of getAppointmentDetailsAPI  "+ map.get("message"));
			return map;
		}
		
		LOG.error("Customer DTO   :"+dto );
		LOG.error("Customer ID:   "+dto.getCustomerId()); 
		
		Customer customer = customerService.getCustomerByExternalId(1l, dto.getCustomerId());
		
		if (null == customer || null == customer.getExternalId() || null == customer.getCustomerId()) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Invalid CustomerId");
			return map;
			
		}
		
		if(null != customer) {
		if(dto.getCustomerId()!= null && dto.getCustomerId()!="") {	
			
			//Customer customer = customerService.getCustomerByExternalId(1l, dto.getCustomerId());
			
		}else {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Customer Id can't be null");
		}
		}
		
		if(dto.getStartDate()== null) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Start date can't be null");
			return map;
		}
		
		if(dto.getStartDate()== null) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "end date can't be null");
			return map;
		}
		
		String startDate = dto.getStartDate();
		String endDate = dto.getEndDate();
		
		Date startTime = null;
		Date endTime = null;
		try {
			String[] st = startDate.split(SPLITOR_SLASH);
			String[] et = endDate.split(SPLITOR_SLASH);
			startTime = new DateTime(Integer.parseInt(st[2]), Integer.parseInt(st[0]), Integer.parseInt(st[1]), ZERO_TIME, ZERO_TIME, ZERO_TIME).toDate();
			endTime = new DateTime(Integer.parseInt(et[2]), Integer.parseInt(et[0]), Integer.parseInt(et[1]), END_TIME[0], END_TIME[1], END_TIME[1]).toDate();
		} catch (Exception e) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Start Date and/or End Date Formate incorrect");
			return map;
		}
		
		List<CustomerAppointmentDetailsResultDTO> list =null;
		if (dto != null && dto.getCustomerId() != null && startTime != null && endTime != null) {
			list = appointmentService.loadByCustomerIdAndDate(dto.getCustomerId(), startTime, endTime);
		}
		
		if(list == null || list.isEmpty() ) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "No Appointment exits for the input data ");
			return map;
		}
		
		map.put(STATUS_KEY, true);
		map.put("CustomerAppointmentDetails", list);
		
		
		
		return map;
	}
	public boolean checkInsRecordInDB(
			UpdateLessonServiceDTO updateLessonServiceDTO) {

		Criterion<Appointment, Long> instructorCriterion = AppointmentCriterion
				.checkInstructorDBRecord(updateLessonServiceDTO.getInsId());
		Long checkIns = appointmentDAO.get(instructorCriterion,
				DAOHelper.FETCH_APPOINTMENT_SERIES);

		return checkIns == 1 ? Boolean.TRUE : Boolean.FALSE;

	}

	public Map<String, String> checkExternalRecordInDB(
			UpdateLessonServiceDTO updateLessonServiceDTO) {

		Map<String, String> check = new HashMap<String, String>();
		check.put("status", "false");
		String customerExternalIds[] = updateLessonServiceDTO.getCustMemberID()
				.split("_");

		for (int i = 0; i < customerExternalIds.length; i++) {

			Criterion<Appointment, Long> customerCriterion = AppointmentCriterion
					.checkCustomerExternalIdDBRecord(customerExternalIds[i]);
			Long checkExternal = appointmentDAO.get(customerCriterion,
					DAOHelper.FETCH_APPOINTMENT_SERIES);
			if (checkExternal == 1) {
				check.put("status", "true");
				check.put("custMemberID", customerExternalIds[i]);
				return check;
			}
		}
		return check;
	}
	
	public Map<String, Object> checkAppointmentCreate(CreateAppointmentDTO dto,CustomerDTO custDTO) throws RuntimeException{
		Map<String, Object > map = new HashMap<String, Object>();
		map.put(STATUS_KEY, true);
		if(custDTO !=null){
			if(null== custDTO.getFirstName()||"".equals(custDTO.getFirstName())) {
				map.put(MESSAGE_KEY, "First name should not be null");
				map.put(STATUS_KEY, false);
				if(map.get("message") != null)LOG.error("error 4 of createAppointmentApi   "+ map.get("message"));
				return map;
			}
			if(null== custDTO.getFirstName()||"".equals(custDTO.getFirstName())) {
				map.put(MESSAGE_KEY, "First name should not be null");
				map.put(STATUS_KEY, false);
				if(map.get("message") != null)LOG.error("error 4 of createAppointmentApi   "+ map.get("message"));
				return map;
			}
			if (custDTO.getEmail() != null && !"".equals(custDTO.getEmail().trim())) {
				String regex = "^(.+)@(.+)$";
				Pattern pattern = Pattern.compile(regex);
				Matcher matcher = pattern.matcher(custDTO.getEmail());
				 if(matcher.matches()){
					 //map.put(STATUS_KEY, matcher.matches()); 
				 }else{
				map.put(MESSAGE_KEY, "The Customer email format is invalid");
				if(map.get("message") != null)LOG.error("error 14 of createAppointmentApi   "+ map.get("message"));
				map.put(STATUS_KEY, matcher.matches());
				return map; 
				 }
				 
			}	
		}
 
				if(null==dto.getServiceId() || "".equals(dto.getServiceId()) ){
					map.put(MESSAGE_KEY, "The Service Id should not be null");
					map.put(STATUS_KEY, false);
					return map;
				}
				if(null==dto.getOrderId() || "".equals(dto.getOrderId()) ){
					map.put(MESSAGE_KEY, "The Order Id should not be null");
					map.put(STATUS_KEY, false);
					return map;
				}
				if(null==dto.getActivityId() || "".equals(dto.getActivityId()) ){
					map.put(MESSAGE_KEY,  "The Activity Id should not be null");
					map.put(STATUS_KEY, false);
					return map;
				}
				if(null==dto.getStartTime() || "".equals(dto.getStartTime()) ){
					map.put(MESSAGE_KEY,  "The Start Time should not be null");
					map.put(STATUS_KEY, false);
					return map;
				}
				if(null==dto.getStartDate() || "".equals(dto.getStartDate()) ){
					map.put(MESSAGE_KEY,  "The Start Date should not be null");
					map.put(STATUS_KEY, false);
					return map;
				}
				
				
				if (dto.getEmail() != null && !"".equals(dto.getEmail().trim())) {
					String regex = "^(.+)@(.+)$";
					Pattern pattern = Pattern.compile(regex);
					Matcher matcher = pattern.matcher(dto.getEmail());
					 if(matcher.matches()){
						 //map.put(STATUS_KEY, matcher.matches()); 
					 }else{
					map.put(MESSAGE_KEY, "Email format is invalid");
					map.put(STATUS_KEY, matcher.matches());
					return map;
					 }
					 
				}
				return map;
				//-----------------------------------------
	}
	
	public Map<String, Object> checkAppointment(CreateAppointmentDTO dto,long profileId) throws RuntimeException {
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.checkAppointment: start");
		}
		Map<String, Object> map = new HashMap<String, Object>();
		String startDate = dto.getStartDate();
		String endDate = dto.getEndDate();
		String startTime = dto.getStartTime();
		String duration =  dto.getDuration();
		String activityDTOId = dto.getActivityId();
		Set<Customer> cs = dto.getCustomers();
		boolean recurring = dto.getIsRecurring()==null?false:Boolean.parseBoolean(dto.getIsRecurring());
 

		if(null==dto.getAppStatus() || "".equals(dto.getAppStatus()) ){
			map.put(MESSAGE_KEY, VALIDATION_STATUS_INVALID);
			map.put(STATUS_KEY, false);
			return map;
		}
		if(null==dto.getOrderId()){
			map.put(MESSAGE_KEY, VALIDATION_STATUS_API_REFERENCE_NO);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		if(null==dto.getEmail() ){
			map.put(MESSAGE_KEY, VALIDATION_STATUS_API_EMAIL);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		if (dto.getEmail() != null && !"".equals(dto.getEmail().trim())) {
			String regex = "^(.+)@(.+)$";
			Pattern pattern = Pattern.compile(regex);
			Matcher matcher = pattern.matcher(dto.getEmail());
			 if(matcher.matches()){
				 //map.put(STATUS_KEY, matcher.matches()); 
			 }else{
			map.put(MESSAGE_KEY, "Email format is invalid");
			map.put(STATUS_KEY, matcher.matches());
			return map;
			 }
			 
		}
		
		//----------------------Validation---forOnline aand Instore Availabilty -----
		Date dateVal = getDateFromString(dto.getStartDate());
		String endTimeStr = CalendarUtil.getEndTime(dto.getStartTime(), dto.getDuration());
		boolean verifyAppointmentServiceMode =false;
		String service_msg="";
		try {
			verifyAppointmentServiceMode = verifyAppointmentServiceModeStatus(dto.getInstructorId(),dateVal.getDay(),dto.getServiceId(),dto.getStartTime(),endTimeStr);
		} catch (Exception e1) {
			e1.printStackTrace();
			LOG.error("Caught {} when check API verifyAppointmentServiceModeStatus Appointment", e1);
		}
		if(dto.getServiceId() == 1){
			service_msg ="The In-store Appointment conflict with Online Availabilty";
		}
		if(dto.getServiceId() == 20){
			service_msg ="The Online Appointment conflict with In-store Availabilty";
		}
	 
		if(verifyAppointmentServiceMode){
			map.put(MESSAGE_KEY, service_msg);
			map.put(STATUS_KEY, false);
			return map;
		}
		//---------------------------------------------------------End ---------------
		//long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		if(null==dto.getServiceId()){
			map.put(MESSAGE_KEY, VALIDATION_SERVICE_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		if(activityDTOId==null || "".equals(activityDTOId.trim())){
			long serviceId = dto.getServiceId();
			Service service = serviceService.getServiceById(serviceId);
			String message = VALIDATION_ACTIVITY_NULL.replace("&REPLASE", service.getServiceName());
			map.put(MESSAGE_KEY, message);
			map.put(STATUS_KEY, false);
			return map;
		}
		if(startDate==null || "".equals(startDate.trim())){
			map.put(MESSAGE_KEY, VALIDATION_START_DATE_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		//279 GSSP-   pop -up message, while Creating an appointment to 140with recurring
		if("140".equals(dto.getActivityId())&&recurring){
			
				map.put(MESSAGE_KEY, VALIDATION_JUMP_START_RECURRENT);
				map.put(STATUS_KEY, false);
				return map;
			}
		if("320".equals(dto.getActivityId())&&recurring){
			
			map.put(MESSAGE_KEY, VALIDATION_ONLINE_TRIAL_RECURRENT);
			map.put(STATUS_KEY, false);
			return map;
		}
			
		//GSSP-233 Changes
		if(CalendarUtil.isDateafterAnYear(startDate)){
			map.put(MESSAGE_KEY, VALIDATION_START_DATE_ONE_YEAR);
			map.put(STATUS_KEY, false);
			return map;
		}
		if(endDate != null && !"".equals(endDate.trim()) && !CalendarUtil.checkDate(startDate, endDate)){
			map.put(MESSAGE_KEY, VALIDATION_DATE_ILLEGAL);
			map.put(STATUS_KEY, false);
			return map;
		}
		if(startTime==null || "".equals(startTime.trim())){
			map.put(MESSAGE_KEY, VALIDATION_START_TIME_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		if(duration == null || "".equals(duration.trim())){
			map.put(MESSAGE_KEY, VALIDATION_TIME_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
				
		if(!CalendarUtil.checkTime(startTime, duration)){
			map.put(MESSAGE_KEY, VALIDATION_TIME_ILLEGAL);
			map.put(STATUS_KEY, false);
			return map;
		}
		//check the end date if input value is null
		if(recurring && (endDate==null || "".equals(endDate.trim()))){
			endDate = CalendarUtil.getDefaultEndDate(startDate);
		}
		
		//264 start //777
		if(recurring &&(endDate!=null && !"".equals(endDate.trim()))&&!validationService.validateStartEndDiff(startDate, endDate))
		{
			map.put(MESSAGE_KEY, "The end date can not be later than one year from the start date!!");
			map.put(STATUS_KEY, false);
			return map;
		}
		//264 end
		
		if(!validationService.checkStartTime(startDate, startTime)){
			map.put(MESSAGE_KEY, VALIDATION_START_DATE_ILLEGAL);
			map.put(STATUS_KEY, false);
			return map;
		}
		String endTime = CalendarUtil.getEndTime(startTime, duration);
		if(StringUtils.isEmpty(dto.getRoomId())){
			map.put(MESSAGE_KEY, VALIDATION_ROOM_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		long roomId= dto.getRoomId();
		Room room = roomService.getRoom(roomId);
		if(Enabled.N.equals(room.getEnabled())){
			map.put(MESSAGE_KEY, VALIDATION_ROOM_DISABLED);
			map.put(STATUS_KEY, false);
			return map;
		}
		boolean checkProfileLocation = validationService.checkLocationProfileByTime(profileId, startDate, endDate, startTime, endTime);
		if(!checkProfileLocation){
			map.put(MESSAGE_KEY, VALIDATION_PROFILELOCATION_AVAILABILITY);
			map.put(STATUS_KEY, false);
			return map;
		}
		//check room - time
		boolean checkRoomByTime = validationService.checkRoomByAppointmentTime(recurring, roomId, startDate, endDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1), profileId);
		if(!checkRoomByTime){
			map.put(MESSAGE_KEY, VALIDATION_ROOM_AVAILABILITY);
			map.put(STATUS_KEY, false);
			
			//Changes made for GSSP-241
			map.put("isConflicting", true);			
			
			return map;
		}
		long serviceId = dto.getServiceId();
		long activityId = Long.parseLong(activityDTOId);
		Activity activity = activityService.getActivityByActivityId(activityId);
		if(serviceId != activity.getService().getServiceId()){
			map.put(MESSAGE_KEY, VALIDATION_SERVICE_ACTIVITY);
			map.put(STATUS_KEY, false);
			return map;
		}
		if(Long.parseLong(duration)<activity.getMinimumDuration()){
			map.put(MESSAGE_KEY, VALIDATION_MINIMUM_DURATION);
			map.put(STATUS_KEY, false);
			return map;
		}
		int inputAttendees = 0;
		if(cs != null ){
			inputAttendees = cs.size();
		}
		//GCSS-437 User tries to save an appointment without selecting a customer
		/*if(inputAttendees==0 && activity.getMinimumAttendees()>0){
			map.put(MESSAGE_KEY, VALIDATION_CUSTOMER_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		if((activity.getMinimumAttendees() >0 && inputAttendees<activity.getMinimumAttendees())||(activity.getMaximumAttendees()!=null 
				&& !"".equals(activity.getMaximumAttendees().toString().trim()) && inputAttendees>activity.getMaximumAttendees())){
			map.put(MESSAGE_KEY, VALIDATION_ATTENDEES);
			map.put(STATUS_KEY, false);
			return map;
		}*/
		//GCSS-417 check the customer with appointment parameters
		List<Long> customerIds = getCustomerIdList(cs);
		boolean checkCustomers = validationService.checkCustomerByAppointmentTime(recurring, customerIds, startDate, endDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1), profileId);
		if(!checkCustomers){
			map.put(MESSAGE_KEY, VALIDATION_CUSTOMER_APPOINTMENT);
			map.put(STATUS_KEY, false);
			return map;
		}
		//fix the bug GCSS-506
		if(RequiresInstructor.R.equals(activity.getRequiresInstructor())||(RequiresInstructor.O.equals(activity.getRequiresInstructor()) && !org.springframework.util.StringUtils.isEmpty(dto.getInstructor()))){
			
			//For GCSS-193,instructor is required but with unavailible instructor
			if(org.springframework.util.StringUtils.isEmpty(dto.getInstructor())) {
				map.put(MESSAGE_KEY, VALIDATION_INSTRUCTOR_REQUIRED);
				map.put(STATUS_KEY, false);
				return map;
			}
			
			long instructorId = dto.getInstructorId();
			
			//GCSS-590
			boolean timeOff = validationService.checkTimeoffByTime(recurring, startDate, endDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1), instructorId);
			if(!timeOff){
				map.put(MESSAGE_KEY, VALIDATION_APPOINTMENT_TIMEOFF);
				map.put(STATUS_KEY, false);
				return map;
			}
			//---GSSP-334	Req 3 Recurring Appointment with profile Timeoff------------------
			try {
				if(!recurring && !validationService.checkProfileTimeoffByTime(profileId,startDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1))){
					map.put(MESSAGE_KEY, VALIDATION_TO_PROFILE_DATE_WITH_APPPOINTMENT);
					map.put(STATUS_KEY, false);
					return map;
				}
			} catch (Exception e) {
				map.put(MESSAGE_KEY, VALIDATION_TO_PROFILE_DATE_WITH_APPPOINTMENT_CREATION);
				map.put(STATUS_KEY, true);
				return map;
			}
			//---GSSP-334	Req 3 ------------------------------------------------------------
			
			//check instructor - activity
			boolean checkInstructorByActivityId = validationService.checkInstructorByProfileActivityId(instructorId, activityId, profileId);
			if(!checkInstructorByActivityId){
				map.put(MESSAGE_KEY, VALIDATION_INSTRUCTOR_ACTIVITY);
				map.put(STATUS_KEY, false);
				return map;
			}
			//check instructor - availability- GSSP-286
			boolean checkInstructorByTime = validationService.checkInstructorByTime(instructorId, startDate, endDate, startTime, endTime);
			if(!checkInstructorByTime){
				//GCSS-670
				boolean checkOneTime = validationService.checkOnetime(recurring, startDate, endDate, startTime, endTime, instructorId);
				if(!checkOneTime){
							List<String> startDateList = CalendarUtil.getPeriodWeekDate(startDate, endDate);
							for(String date : startDateList){
								Date st_ = DateTime.parse(date + " " + startTime, DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
								Date et_ = DateTime.parse(date + " " + endTime, DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
								boolean checkAvailabilityAndOntime=validationService.checkAvailabilityAndOntime(instructorId, st_, et_);
								if(!checkAvailabilityAndOntime){
									map.put(MESSAGE_KEY, VALIDATION_INSTRUCTOR_AVAILABILITY);
									map.put(STATUS_KEY, false);
									return map;
								}
							}
					
				}
			}
			//check instructor-appointment
			boolean checkInstructorByAppointmentTime = validationService.checkInstructorByAppointmentTime(recurring, instructorId, startDate, endDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1));
			if(!checkInstructorByAppointmentTime){
				map.put(MESSAGE_KEY, VALIDATION_INSTRUCTOR_APPOINTMENT);
				map.put(STATUS_KEY, false);
				
				// Changes made for GSSP-241
				map.put("isConflicting", true);
				
				return map;
			}
		}
		//check room - activity
		boolean checkRoomByActivity = validationService.checkRoomByActivity(roomId, activityId);
		if(!checkRoomByActivity){
			map.put(MESSAGE_KEY, VALIDATION_ROOM_ACTIVITY);
			map.put(STATUS_KEY, false);
			
			return map;
		}
		map.put(STATUS_KEY, true);
		
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.checkAppointment: end");
		}
		return map;
	}
	private List<Long> getCustomerIdList(Set<Customer> cs){
		List<Long> customerIds = new ArrayList<Long>();
		if(cs != null){
			for(Customer c : cs){
				customerIds.add(c.getCustomerId());
			}
		}
		return customerIds;
	}
	
	private boolean verifyAppointmentServiceModeStatus(long instructorId,int day,Long mode,String startTimeStr,String endTimeStr){ 
		
		Instructor instructor1 = instructorService.getInstructor(instructorId);
		boolean flag=false;
		Availability availability = instructor1.getAvailability();
		String dayStr = day+"";
		//int getDay = day-
		OnLineAvailableDTO onl = getDayAvailabilityStr(dayStr,availability);
		String dayStrUtil = onl.getWeekDay();
		List<OnLineAvailableDTO>  onlineAvlList = null;
		List<InstoreAvailableDTO>  instoreAvlList = null;
		List<OnLineInstoreAvailableDTO>  alllist = new ArrayList<OnLineInstoreAvailableDTO>();
		if(mode == 1){
			 onlineAvlList = onlineAvailabilityService.getOnlineAvlFormatByInstructorId(instructorId);
		
		for(OnLineAvailableDTO onls: onlineAvlList){
			if(dayStrUtil.equals(onls.getWeekDay())){
			OnLineInstoreAvailableDTO allDto= new OnLineInstoreAvailableDTO();
			 allDto.setFromTime(onls.getFromTime());
			 allDto.setToTime(onls.getToTime());
			 allDto.setServiceMode("Online");
			 alllist.add(allDto);
			}
		}
		}
		if(mode == 20){
		instoreAvlList = instoreAvailabilityService.getInstoreAvlFormatByInstructorId(instructorId);
		for(InstoreAvailableDTO onls: instoreAvlList){
			if(dayStrUtil.equals(onls.getWeekDay())){
			OnLineInstoreAvailableDTO allDto= new OnLineInstoreAvailableDTO();
			 allDto.setFromTime(onls.getFromTime());
			 allDto.setToTime(onls.getToTime());
			 allDto.setServiceMode("Instore");
			 alllist.add(allDto);
			}
		}
		}
		Collections.sort(alllist);
		alllist.stream().map(s -> s.getFromTime()).forEach(System.out::print);

		//List<InstoreAvailableDTO>  instoreAvlList = instoreAvailabilityService.getInstoreAvlFormatByInstructorId(dto.getInstructorId());
		
		try {
			 
			int cnt =1;
			String fromTime ="";

			//---++++++++++++++++++++++++++++++++++++++++++
		 
			int listVal =alllist.size();
			if(listVal>0){
			for(OnLineInstoreAvailableDTO onls: alllist){
			 
				flag= checkSlotUnderInstructorAvlty(startTimeStr,endTimeStr,onls.getFromTime(),onls.getToTime());
				
				if(flag)break;
			}
			}
		 

			
		} catch (Exception e1) {
			LOG.error("Caught {} when   checkSlotUnderInstructor Avlty  ", e1);
		}
		
	   return flag;
	}
	
	
	private OnLineAvailableDTO getDayAvailabilityStr(String day,Availability avl){
		OnLineAvailableDTO onl = new OnLineAvailableDTO();
		if("0".equals(day)){
			onl.setFromTime(AvailabilityUtil.format24(avl.getSundayStartTime()));
			onl.setToTime(AvailabilityUtil.format24(avl.getSundayEndTime()));
			onl.setWeekDay("0");
		}
		if("1".equals(day)){
			onl.setFromTime(AvailabilityUtil.format24(avl.getMondayStartTime()));
			onl.setToTime(AvailabilityUtil.format24(avl.getMondayEndTime()));	
			onl.setWeekDay("1");
						}
		if("2".equals(day)){
			onl.setFromTime(AvailabilityUtil.format24(avl.getTuesdayStartTime()));
			onl.setToTime(AvailabilityUtil.format24(avl.getTuesdayEndTime()));	
			onl.setWeekDay("2");
		}
		if("3".equals(day)){
			onl.setFromTime(AvailabilityUtil.format24(avl.getWednesdayStartTime()));
			onl.setToTime(AvailabilityUtil.format24(avl.getWednesdayEndTime()));	
			onl.setWeekDay("3");
		}
		if("4".equals(day)){
			onl.setFromTime(AvailabilityUtil.format24(avl.getThursdayStartTime()));
			onl.setToTime(AvailabilityUtil.format24(avl.getThursdayEndTime()));	
			onl.setWeekDay("4");
		}
		if("5".equals(day)){
			onl.setFromTime(AvailabilityUtil.format24(avl.getFridayStartTime()));
			onl.setToTime(AvailabilityUtil.format24(avl.getFridayEndTime()));	
			onl.setWeekDay("5");
		}
		if("6".equals(day)){
			onl.setFromTime(AvailabilityUtil.format24(avl.getSaturdayStartTime()));
			onl.setToTime(AvailabilityUtil.format24(avl.getSaturdayEndTime()));	
			onl.setWeekDay("6");
		}
		 
		
		return onl;
	}
	public static boolean checkSlotUnderInstructorAvlty(String onlinTimeStart,String onlinTimeEnd,String instAvailStart,String instAvailEnd) throws Exception{
		
		boolean flag = false;
		
		try {

			DateFormat sdf = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN);
			Date insAvlStart = sdf.parse(instAvailStart);
			Date insAvlEnd = sdf.parse(instAvailEnd);
			Date onlineStart = sdf.parse(onlinTimeStart);
			Date onlineEnd = sdf.parse(onlinTimeEnd);
  
			if(onlineStart.before(onlineEnd) && insAvlStart.before(insAvlEnd)){
			 
				if(onlineStart.after(insAvlStart) && onlineEnd.before(insAvlEnd)){
					flag = true;
				}
				

				if(onlineStart.equals(insAvlStart) && onlineEnd.before(insAvlEnd)){
					flag = true;
				}
				if(onlineEnd.equals(insAvlEnd) && onlineStart.after(insAvlStart)){
					flag = true;
				}
				if( onlineStart.before(insAvlStart) &&  onlineEnd.before(insAvlEnd) &&  onlineEnd.after(insAvlStart)){
					flag = true;
				}
				if( onlineStart.before(insAvlEnd) &&  onlineEnd.after(insAvlEnd) &&  onlineStart.after(insAvlStart)){
					flag = true;
				}
				if(onlineStart.equals(insAvlStart) && onlineEnd.equals(insAvlEnd)){
					flag = true;
				}
			 
			}

		} catch (ParseException e) {
			throw  e;	 
		}
  
    return flag;
} 
	
	
	private static Date getDateFromString(String dateStr){
		SimpleDateFormat formatter = new SimpleDateFormat("MM/dd/yyyy");
        //String dateInString = "7-Jun-2013";
        Date date = new Date();

        try {

              date = formatter.parse(dateStr);

        } catch (ParseException e) {
        	LOG.error("Caught {} when check getDateFromString Appointment", e);
        }
        return date;
	}	
}
