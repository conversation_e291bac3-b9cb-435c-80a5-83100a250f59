package com.guitarcenter.scheduler.webservice;

import com.guitarcenter.scheduler.webservice.dto.ErrorResponseDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.core.annotation.Order;

import javax.servlet.http.HttpServletRequest;

/**
 * Global exception handler for REST API endpoints
 * Provides consistent error responses across all webservice controllers
 * This handler is specifically for webservice package controllers
 */
@ControllerAdvice(basePackages = "com.guitarcenter.scheduler.webservice")
@Order(1)
public class GlobalExceptionHandler {

    private static final Logger LOG = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    // Handle 400 Bad Request - JSON parsing errors
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ErrorResponseDTO handleJsonParseError(HttpMessageNotReadableException ex, HttpServletRequest request) {
        LOG.error("JSON parsing error for request: {} - {}", request.getRequestURI(), ex.getMessage());
        return new ErrorResponseDTO(
            "BadRequest",
            "Invalid JSON format in request body. Please check your request payload.",
            "error"
        );
    }

    // Handle 400 Bad Request - Validation errors
    @ExceptionHandler({
            MethodArgumentNotValidException.class,
            IllegalArgumentException.class
    })
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ErrorResponseDTO handleValidationError(Exception ex, HttpServletRequest request) {
        LOG.error("Validation error for request: {} - {}", request.getRequestURI(), ex.getMessage());
        String errorMessage = "Invalid request parameters.";

        if (ex instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException validationEx = (MethodArgumentNotValidException) ex;
            if (validationEx.getBindingResult().hasErrors()) {
                errorMessage = "Validation failed: " + validationEx.getBindingResult().getAllErrors().get(0).getDefaultMessage();
            }
        } else if (ex instanceof IllegalArgumentException) {
            errorMessage = "Invalid argument: " + ex.getMessage();
        }

        return new ErrorResponseDTO("ValidationError", errorMessage, "error");
    }

    // Handle 405 Method Not Allowed
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    @ResponseBody
    public ErrorResponseDTO handleMethodNotSupported(HttpRequestMethodNotSupportedException ex, HttpServletRequest request) {
        LOG.error("Method not supported for request: {} - {}", request.getRequestURI(), ex.getMessage());
        return new ErrorResponseDTO(
            "MethodNotAllowed",
            "HTTP method '" + ex.getMethod() + "' is not supported for this endpoint.",
            "error"
        );
    }

    // Handle 500 Internal Server Error (specific runtime exceptions)
    @ExceptionHandler({
            RuntimeException.class,
            NullPointerException.class,
            ClassCastException.class
    })
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ResponseBody
    public ErrorResponseDTO handleInternalServerError(Exception ex, HttpServletRequest request) {
        LOG.error("Internal server error for request: {} - {}", request.getRequestURI(), ex.getMessage(), ex);
        return new ErrorResponseDTO(
            "UnexpectedError",
            "An unexpected error occurred. Please try again later.",
            "error"
        );
    }
}
