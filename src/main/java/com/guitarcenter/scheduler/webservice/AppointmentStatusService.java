package com.guitarcenter.scheduler.webservice;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.guitarcenter.scheduler.service.AppointmentStatusDataService;
import com.guitarcenter.scheduler.webservice.dto.AppointmentStatusDTO;
import com.guitarcenter.scheduler.webservice.dto.AppointmentStatusServiceDTO;
import com.guitarcenter.scheduler.webservice.dto.AppointmentStatusServiceResponseDTO;


@RestController
public class AppointmentStatusService {
	
	private static final Logger		LOG					        = LoggerFactory.getLogger("schedulerlog");
	
	@Autowired
	private AppointmentStatusDataService appointmentStatusService;
	
	

	@RequestMapping(value = "/AppointmentStatusService", method = RequestMethod.POST, headers = { "content-type=application/json" })
	public AppointmentStatusServiceResponseDTO getActivityCode(@RequestBody AppointmentStatusServiceDTO appointmentStatusServiceDTO) throws JsonProcessingException {
		
		AppointmentStatusServiceResponseDTO responce = new AppointmentStatusServiceResponseDTO();
		
		try {
			List<String> customerEmailList = new ArrayList<String>();
			customerEmailList.add(appointmentStatusServiceDTO.getCustomerEmailID());
			Date startDate = new Date();
			List<AppointmentStatusDTO> apptStatusList = appointmentStatusService.findCustomerAppointmentStatusByEmailId(customerEmailList,startDate);
			if(apptStatusList != null &&  (!apptStatusList.isEmpty()) && (appointmentStatusServiceDTO.getCustomerEmailID() != null))
			{
				responce.setApptStatusList(apptStatusList);
				responce.setCustomerEmail(appointmentStatusServiceDTO.getCustomerEmailID());
				
			}else if(appointmentStatusServiceDTO.getCustomerEmailID() != null && apptStatusList.isEmpty()){

				responce.setStatus("No Record Found for the Customer Email ID");
			}
		} catch (Exception e) {
			LOG.error("Error during fetching of data in Appointment Status API Web Service", e);;
			responce.setStatus("Unexpected error occured.");
			
		}
		 
			return responce;
		 
		 
	}


}
