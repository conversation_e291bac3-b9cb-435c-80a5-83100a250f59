package com.guitarcenter.scheduler.webservice;

import java.util.HashMap;
import java.util.Map;

import com.guitarcenter.scheduler.service.AppointmentService;
import com.guitarcenter.scheduler.webservice.dto.PauseServiceUpdateResponseDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.guitarcenter.scheduler.webservice.dto.PauseServiceUpdateDTO;

/**
 * REST Controller for pause service update appointment
 */
@RestController
public class PauseServiceUpdateAppointmentController {

    private static final Logger LOG = LoggerFactory.getLogger(PauseServiceUpdateAppointmentController.class);
    private static final String STATUS_KEY = "status";
    private static final String MESSAGE_KEY = "message";

    @Autowired
    private AppointmentService appointmentService;

    /**
     * Endpoint to pause service and update appointment
     *
     * @param pauseServiceUpdateDTO the request DTO containing customerId and endDate
     * @return response map with status and message
     */
   @RequestMapping(value = "/pauseServiceupdateAppointment", method = RequestMethod.POST,
        consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
public PauseServiceUpdateResponseDTO pauseServiceupdateAppointment(@RequestBody PauseServiceUpdateDTO pauseServiceUpdateDTO) {
       try {
           LOG.info("Received request to pause service for customer: {}", pauseServiceUpdateDTO.getCustomerId());

           // 1. Validate the input data: customer# is 10 digits and enddate should be today or future date
           if (!pauseServiceUpdateDTO.isValidCustomerId()) {
               return new PauseServiceUpdateResponseDTO(false, "Invalid customer ID. Customer ID must be a 10-digit number.");
           }

           if (!pauseServiceUpdateDTO.isValidEndDate()) {
               return new PauseServiceUpdateResponseDTO(false, "Invalid end date. End date must be today or a future date.");
           }

           PauseServiceUpdateResponseDTO PauseServiceUpdateResponseDTO = appointmentService.pauseCustomer(pauseServiceUpdateDTO);
           return new PauseServiceUpdateResponseDTO(true, "Successfully processed pause service request for customer: "
                   + pauseServiceUpdateDTO.getCustomerId() + " with end date: " + pauseServiceUpdateDTO.getEndDate());

       } catch (Exception e) {
           LOG.error("Error processing pause service request", e);
           return new PauseServiceUpdateResponseDTO(false, "Error processing request: " + e.getMessage());
       }
   }
}
