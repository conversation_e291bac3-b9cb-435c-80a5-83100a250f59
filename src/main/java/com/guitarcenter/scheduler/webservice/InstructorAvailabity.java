package com.guitarcenter.scheduler.webservice;


import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.LocalTime;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.AvailabilityDAO;
import com.guitarcenter.scheduler.dao.RoomDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentCriterion;
import com.guitarcenter.scheduler.dao.criterion.AvailabilityCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.RoomCriterion;
import com.guitarcenter.scheduler.dao.criterion.dto.RoomDTO;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dto.InstructorInfoDTO;
import com.guitarcenter.scheduler.dto.ProfileTimeOffDTO;
import com.guitarcenter.scheduler.dto.StudioHoursDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.AppointmentCancelReason;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.ServiceLogger;
import com.guitarcenter.scheduler.service.AppointmentService;
import com.guitarcenter.scheduler.service.AvailabilityService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.RoomService;
import com.guitarcenter.scheduler.service.TimeoffService;
import com.guitarcenter.scheduler.service.ValidationService;
import com.guitarcenter.scheduler.webservice.dto.CancelReasonDTO;
import com.guitarcenter.scheduler.webservice.dto.CurrentLesson;
import com.guitarcenter.scheduler.webservice.dto.InstructorAvailableHoursDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceResponseDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorServiceDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorServiceResponseDTO;
import com.guitarcenter.scheduler.webservice.dto.SimpleInstructorAvailabilityDTO;
import com.guitarcenter.scheduler.webservice.dto.SimpleLocationProfileDTO;
import com.guitarcenter.scheduler.webservice.dto.Lesson;
import com.guitarcenter.scheduler.webservice.dto.LessonServiceDTO;
import com.guitarcenter.scheduler.webservice.dto.Lessons;
import com.guitarcenter.scheduler.webservice.dto.LocationProfileInfoDTO;
import com.guitarcenter.scheduler.webservice.dto.Times;
import com.guitarcenter.scheduler.webservice.util.WebServiceUtil;






@RestController
public class InstructorAvailabity {
	
	@Autowired
	private AppointmentDAO			appointmentDAO;
	
	@Resource
	private AvailabilityService availabilityService;
	
	@Autowired
	private InstructorService instructorService;
	
	@Autowired
	private AvailabilityDAO availabilityDAO;
	
	
	private static final Logger		LOG					        = LoggerFactory.getLogger("schedulerlog");
	
	private Calendar c = Calendar.getInstance();
	
	
	@Autowired
	private AppointmentService appointmentService;
	
	
	
	@Autowired
	private RoomDAO roomDAO;
	
	@Autowired
	TimeoffService timeoffService;
	
	private Map<Long, Map<Long, RoomDTO>> profileRoomcache = new HashMap<Long, Map<Long, RoomDTO>>();
	
	
	
	  
	  
	  
	  	//	Added for Phase2_LES-27 Changes 
	  	/**
		 * Service method used for Instructor Fetch Service
		 *  
	  	 * @param InstructorServiceDTO  instructorServiceDTO
	  	 * @return InstructorServiceResponseDTO
	  	 * @throws JsonProcessingException 
	  	 * 
		 * 
		 */
		 
		  @RequestMapping(value="/instructoravailabity",
				  method=RequestMethod.POST, headers = {"content-type=application/x-www-form-urlencoded"})  
	  
		
		  public InstructorServiceResponseDTO getInstructorAvailability(InstructorServiceDTO  instructorServiceDTO) throws JsonProcessingException 
		  {					  
			  	
			 
			   CurrentLesson currentLesson = new CurrentLesson();
			   
			   LocationProfileInfoDTO locationProfileDetails =  null;
			  
			   InstructorServiceResponseDTO result = new InstructorServiceResponseDTO();
			   
			   String fullName = "";
			   //GSSP-208 changes
			   ServiceLogger serviceLogger = new ServiceLogger();
			   String successString;
			   //GSSP-208 changes
				  serviceLogger.setServiceName("InstructorService");
				
				 
				  serviceLogger.setAppointmentSeriesId(instructorServiceDTO.getSeriesID());
				  serviceLogger.setCustomerMemberId(instructorServiceDTO.getCustMemberID());
				  Criterion<Appointment,String>  appointmentCriterion =
						    AppointmentCriterion.logService(serviceLogger);
				  
				  successString = appointmentDAO.get(appointmentCriterion);	
				  	
			  try
			  
			  {
				  
				  
				  //Set Customer Name
				  fullName = getCustomerName(instructorServiceDTO.getCustMemberID());	
				  if(!StringUtils.isBlank(fullName))
				  {	  
					 
					  result.setName(fullName);
				  }	  
				  
				  		  
				  //Set Current Lesson Details
				  result.setCurrentLesson(currentLesson);
	  
				  
				  //Find the Studio Details and set the same
				  locationProfileDetails = getStudioDetails(result,instructorServiceDTO.getAptId());
				
				//Set the Current Lesson Details and Badge Number
				  setCurrentLessonData(result, currentLesson,instructorServiceDTO,locationProfileDetails);
				
				  //Set the instructor Free slots and the instructor Name
				  setInstructorsFreeSlots(result, locationProfileDetails, instructorServiceDTO);
				   
			  }
			  catch(Exception e)
			  {
				  LOG.error("Error during fetching of data in Instructor Availability Web Service", e);
			  }						 
			
			 
			  return result;
			  			  
		  }


		  /**
		   * RESTful Web Service to get instructor availability
		   *
		   * @param instructorAVLServiceDTO - Request DTO containing instructor ID and start date
		   * @return InstructorAVLServiceResponseDTO - Response containing instructor availability data
		   * @throws JsonProcessingException
		   */
		  @RequestMapping(value="/instructors/availability",
				  method=RequestMethod.POST,
				  headers = {"Content-Type=application/json"},
				  consumes = "application/json",
				  produces = "application/json")
		  public InstructorAVLServiceResponseDTO getInstructorsAvailability(@RequestBody InstructorAVLServiceDTO instructorAVLServiceDTO)
				  throws JsonProcessingException {

			  LOG.info("InstructorAvailability REST API called with instructorId: {} and startDate: {}",
					  instructorAVLServiceDTO.getInstructorId(), instructorAVLServiceDTO.getStartDate());

			  InstructorAVLServiceResponseDTO result = new InstructorAVLServiceResponseDTO();
			  LocationProfileInfoDTO locationProfileDetails = null;

			  // Service logging for tracking API usage
			  ServiceLogger serviceLogger = new ServiceLogger();
			  serviceLogger.setServiceName("InstructorAvailabilityREST");
			  serviceLogger.setAppointmentSeriesId(instructorAVLServiceDTO.getSeriesID());
			  serviceLogger.setCustomerMemberId(instructorAVLServiceDTO.getCustMemberID());

			  try {
				  Criterion<Appointment, String> appointmentCriterion = AppointmentCriterion.logService(serviceLogger);
				  appointmentDAO.get(appointmentCriterion);

				  // Get location profile details from appointment ID if provided
				  if (instructorAVLServiceDTO.getAptId() != null) {
					  locationProfileDetails = getStudioDetails(null, instructorAVLServiceDTO.getAptId());
				  } else {
					  // If no appointment ID, create default location profile
					  locationProfileDetails = createDefaultLocationProfile();
				  }

				  // Set instructor availability using the service implementation
				  instructorService.setInstructorsFreeSlotsforSelfService(result, locationProfileDetails, instructorAVLServiceDTO);

				  LOG.info("Successfully processed instructor availability for instructorId: {}",
						  instructorAVLServiceDTO.getInstructorId());

			  } catch (Exception e) {
				  LOG.error("Error during fetching instructor availability data for instructorId: {} - {}",
						  instructorAVLServiceDTO.getInstructorId(), e.getMessage(), e);

				  // Set error response
				  result.setInstructorStatus("ERROR");
				  result.setAvailability("[]");
			  }

			  return result;
		  }

		  /**
		   * Simplified RESTful Web Service to get instructor availability using minimal required fields
		   * This endpoint provides better performance and cleaner interface by using only required fields
		   *
		   * @param request - Simple request DTO containing only instructor ID and start date
		   * @return InstructorAVLServiceResponseDTO - Response containing instructor availability data
		   * @throws JsonProcessingException
		   */
		  @RequestMapping(value="/instructors/availability/simple",
				  method=RequestMethod.POST,
				  headers = {"Content-Type=application/json"},
				  consumes = "application/json",
				  produces = "application/json")
		  public InstructorAVLServiceResponseDTO getInstructorsAvailabilitySimple(@RequestBody SimpleInstructorAvailabilityDTO request)
				  throws JsonProcessingException {

			  LOG.info("Simple InstructorAvailability REST API called with instructorId: {} and startDate: {}",
					  request.getInstructorId(), request.getStartDate());

			  InstructorAVLServiceResponseDTO result = new InstructorAVLServiceResponseDTO();

			  try {
				  // Validate input
				  if (!request.isValid()) {
					  throw new IllegalArgumentException("Invalid request data: " + request.toString());
				  }

				  // Create default location profile with required fields
				  SimpleLocationProfileDTO locationProfile = createDefaultSimpleLocationProfile();

				  // Call the simplified service method
				  instructorService.setInstructorsFreeSlotsforSelfService(result, locationProfile, request);

				  LOG.info("Successfully processed simple instructor availability for instructorId: {}",
						  request.getInstructorId());

			  } catch (Exception e) {
				  LOG.error("Error during fetching simple instructor availability data for instructorId: {} - {}",
						  request.getInstructorId(), e.getMessage(), e);

				  // Set error response
				  result.setInstructorStatus("ERROR");
				  result.setAvailability("[]");
			  }

			  return result;
		  }

		  /**
		   * Helper method to create default simple location profile
		   *
		   * @return SimpleLocationProfileDTO with default values
		   */
		  private SimpleLocationProfileDTO createDefaultSimpleLocationProfile() {
			  // Set default values - these should be configurable based on business requirements
			  return new SimpleLocationProfileDTO(1L, 25L); // Default profile ID and activity ID (Guitar lessons)
		  }



		//Added for LES_27 Method to find Profile details and Studio Hours
		/**
		 * Method to find Profile details and Studio Hours
		 * 
		 * @param InstructorServiceResponseDTO result
		 * @param Long aptId
		 * @return LocationProfileInfoDTO
		 */
		private LocationProfileInfoDTO getStudioDetails(InstructorServiceResponseDTO result,Long aptId)
		{

			 List<StudioHoursDTO> studioHours = new ArrayList<StudioHoursDTO>();
			
			  LocationProfileInfoDTO locationProfileDetails =  null;
			
			 //Logic for getting the Profile, location , activity type  and the date of the appointment id
			  Criterion<Appointment,  LocationProfileInfoDTO> profileCriterion =  AppointmentCriterion
						.findProfileDetailByAppointmentID(aptId);
			  
			  List<LocationProfileInfoDTO> locationProfileDetailsList  = appointmentDAO.search(profileCriterion,
					  DAOHelper.FETCH_APPOINTMENT_SERIES);		
			
			  if(null != locationProfileDetailsList && locationProfileDetailsList.size() > 0 )		  					  			  
				  locationProfileDetails = locationProfileDetailsList.get(0);
			  
			  
			  if(null != locationProfileDetails)
			  {
				  
				  
				  //Code for setting the Studio related information
							  
				  if(!StringUtils.isBlank(locationProfileDetails.getStoreName()))	 				  
					  result.setStore(locationProfileDetails.getStoreName());
				  
				  if(!StringUtils.isBlank(locationProfileDetails.getPhoneNumber()))	 	 				  
					  result.setPhoneNumber(locationProfileDetails.getPhoneNumber());	
				  
				  if(!StringUtils.isBlank(locationProfileDetails.getSeriesStartDate()) )	 				  
					  result.setSeriesStartDate(locationProfileDetails.getSeriesStartDate());	
				  
			  
				  if(!StringUtils.isBlank(locationProfileDetails.getSeriesStartDate()) && !locationProfileDetails.getIsRecurring().equalsIgnoreCase("N"))	 				  
						  result.setSeriesEndDate(locationProfileDetails.getSeriesEndDate());	
				  
				  
			  }		  
			  	
			  
			  return locationProfileDetails;
			
		}
		  
		  
	
		  
		  //Added for LES_27 Method to get the Customer Name based on badge Number
		 /**
		 * Method to get the Customer Name based on badge Number
		 * @param String badgeNumber
		 * @return String
		 */
		private String getCustomerName(String externalId)
		  {
						
			  String fullName;
			  
			  //Logic for Getting Customer Name from badge number				 
			   
			  
			  Criterion<Appointment,  String> customerCriterion =  AppointmentCriterion
							.findCustomerNameByExternalId(externalId);
				  
			  fullName  = appointmentDAO.get(customerCriterion, DAOHelper.FETCH_APPOINTMENT_SERIES);

			  return fullName;
			  
		  }
		  
		  
		  
		  
		  //Added for LES_27 Method to set the Current Lesson Details		  
		  /**
		 * 
		 * Method to set the Current Lesson Details	
		 * 
		 * @param result
		 * @param currentLesson
		 * @param instructorServiceDTO
		 * @throws ParseException 
		 * 
		 * 
		 */
		private void setCurrentLessonData(InstructorServiceResponseDTO result, CurrentLesson currentLesson,InstructorServiceDTO instructorServiceDTO,
				LocationProfileInfoDTO locationProfileDetails) throws ParseException
		  {
			  
					
			  Appointment appointment = null;
			  
			  String activityName="";
			  
			  currentLesson.setAptId(instructorServiceDTO.getAptId());
			  currentLesson.setStartTime(locationProfileDetails.getStartTime());
			  currentLesson.setEndTime(locationProfileDetails.getEndTime());
			  currentLesson.setName(instructorServiceDTO.getInsName());
			  currentLesson.setDate(locationProfileDetails.getAppointmentDate());			
			  			
			  Criterion<Appointment, Appointment> appointmentCriterion = AppointmentCriterion.getByAppointmentId(instructorServiceDTO.getAptId());
			  	appointment = appointmentDAO.get(appointmentCriterion);	
			  	  	
			  	if(null != appointment.getActivity())
			  	{	
			  		activityName=  ((Activity) appointment.getActivity()).getActivityName();					
			  		currentLesson.setType(activityName);
			  	}	
						  	
			  	result.setCustMemberID(instructorServiceDTO.getCustMemberID());
			
			  	result.setCurrentLesson(currentLesson);			  				  	
			
		  }
		  
		  
		  //Added for LES_27 Method to find Instructor free slots and Instructor Name
		 /**
		 *
		 * Method to find Instructor free slots and Instructor Name
		 * @param result
		 * @param locationProfileDetails
		 * @param instructorServiceDTO
		 * @param currentLesson
		 * @throws Exception 
		 */
		private void  setInstructorsFreeSlots(InstructorServiceResponseDTO result,LocationProfileInfoDTO locationProfileDetails,
				  InstructorServiceDTO  instructorServiceDTO) throws Exception
		  {			 
					
			
			  LocalTime profileTimeoffStartTime= null;
			  LocalTime profileTimeoffEndTime= null;
			  int dayOfWeek =0;			  		
			  
			  long profileId = locationProfileDetails.getProfileID();
			  
			  String instructorName ="";
			  
			  Availability instructorAvailability = null;
			  
			  Map<String, List<InstructorAvailableHoursDTO>>  instructorDayAvailabilityMap = null;
			  
			  List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList  = null ,timeOffHoursDTOList = null ;
			  
			  DateTime startTime = null ,endTime =null;
			  
			  List<InstructorAvailableHoursDTO> freeSlotsList = null; 
			  
			  List<Long> instructorsList = new ArrayList<Long>();
			  
			  List<Times>  dividedSlotsList = null, appointmentDateList = null;;
			  
			  Map<String,List<Times>> slotsByDateMap = new TreeMap<String,List<Times>>();
			  
			  Map<String,List<InstructorAvailableHoursDTO>> timeOffMap = new HashMap<String,List<InstructorAvailableHoursDTO>>();
			  
			  List<InstructorAvailableHoursDTO>  timeOffList = null;
			  
			  Map<String,List<String>> nonAvailableSlots = null;
			  
			  InstructorAvailableHoursDTO timeOffHoursDTO = null;
			  
			  LocalTime timeOffStartTime = null,  timeOffEndTime = null;
			  
			  boolean getAvailability = false;
			  
			  DateTime seriesEndTime = null , timeOffStartDate  = null, timeOffEndDate = null;
			  
			  
			  Lessons instructorLessonsByDay  =  null;
			   
			  List<Lessons> instructorLessonsByDayList = new ArrayList<Lessons>();			
			  		
			  
			  DateFormat df1 = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH);			  	
			  
			  Long duration = locationProfileDetails.getDuration();
			  
			  if(duration % 30  !=0)
				  duration = duration + (30 - (duration % 30) );
	 
			  //-- GSSP-321-Prevent customers from scheduling a lesson online, when the stores are not offering lessons
			  //----Changes for GSSP-321 for get the profileTimeOffDetails from DB --------
			  	Date startDateInput =null;
			  	SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		        String dateInString = instructorServiceDTO.getStartDate();
		        try 
		        {
		        	startDateInput = formatter.parse(dateInString);

		        } catch (ParseException e) {
		        	 LOG.error("Error during on Date format in Instructor Availability Web Service", e);
		        }
   
		        Map<String, ProfileTimeOffDTO> profileTimeOffMap = timeoffService.profileTimeOffDetails(profileId,startDateInput);
 	
			  //Getting the instructor list based on location type and activity type	 
			  
			  Long[] activityIds = new Long[]{ locationProfileDetails.getActivityID()};
			  
			  List<InstructorInfoDTO> iList = instructorService.findByLocationIdAndActivityIds(locationProfileDetails.getLocationID(), activityIds);	
			  
			
			  for(InstructorInfoDTO instructorInfoDTO : iList)
			  {
				  
				  instructorsList.add(new Long(instructorInfoDTO.getInstructorId()));
			  }		  
			
		
			  
			  long[] instructorArray = instructorsList.stream().mapToLong(i->i).toArray();
			  
			  Long[] InstructorArray  =  ArrayUtils.toObject (instructorArray);
		  
	  				   		  
			  DateTime availaibilityStartDay = new DateTime(instructorServiceDTO.getStartDate());	
					  
			  // Added for Stored Procedure
			 			  
			  // Added for GET_RM_SLTS_FNC
			  Criterion<Room, Map<String, List<String>>> storedcriterion = RoomCriterion.findNonAvailableSlots(profileId,
						 availaibilityStartDay.toDate(),locationProfileDetails.getActivityID(),duration);
					
				  nonAvailableSlots = roomDAO.get(storedcriterion);
			  
			  
			  
			  if(null != locationProfileDetails.getSeriesEndDate() )			  
				   seriesEndTime = new DateTime(locationProfileDetails.getSeriesEndDate());
			  
			  Date availaibilityWeekDay =  null;
			  
			  int days = 6;
			  
			  if( null == locationProfileDetails.getSeriesEndDate() || locationProfileDetails.getIsRecurring().equalsIgnoreCase("N"))
			  {
				  getAvailability = true;
				  availaibilityWeekDay =  availaibilityStartDay.plusWeeks(1).toDate();
				  availaibilityWeekDay = DateTimeUtil.handleEndDate(df1.format(availaibilityWeekDay));
				  
			  }
			  
			  else
			  {	  
			  
				  if(!seriesEndTime.isBefore(availaibilityStartDay))
				  {
					  getAvailability = true;
							  
						if(seriesEndTime.isAfter(availaibilityStartDay.plusWeeks(1)))
						{
							 availaibilityWeekDay =  availaibilityStartDay.plusWeeks(1).toDate();
							 			 
						}
						else
						{
							availaibilityWeekDay = seriesEndTime.toDate();
							
							Days d = Days.daysBetween(availaibilityStartDay, seriesEndTime);
						     days = d.getDays();
						}
						 availaibilityWeekDay = DateTimeUtil.handleEndDate(df1.format(availaibilityWeekDay));
					  
				  }			
				  
			  } 
			  
			  
		
			  if(InstructorArray.length > 0)
			  {	  
				//Logic for getting the instructor appointment for the week
				  Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> timeOffriterion = 
						  		AppointmentCriterion.getTimeOffForInstructors(InstructorArray,availaibilityStartDay.toDate(),availaibilityWeekDay);
				  
				  timeOffMap  = appointmentDAO.get(timeOffriterion);
				  
			  
			  }
			  
			  Map<String,Boolean>  dateTimeRoomAvalabilityMap  = new HashMap<String,Boolean>();
  	
			 if(getAvailability) 
			 {	 
				 
				 List ls= new ArrayList();
				 for(InstructorInfoDTO instructorInfoDTO : iList)				  
					  
				  {	
					 Long instructorID = new Long(instructorInfoDTO.getInstructorId());
					 ls.add(instructorID+"");
				  }
			 


				 //Block for iterating through all instructors and finding their free slots				  
					  for(InstructorInfoDTO instructorInfoDTO : iList)				  
						  
					  {	  
						   
						  if(null != instructorInfoDTO.getInstructorName())
							  instructorName = instructorInfoDTO.getInstructorName();
					
						  Long instructorID = new Long(instructorInfoDTO.getInstructorId());
					
						  long startTime6 = System.currentTimeMillis();
						  //Logic for getting instructor Availability 
						  Criterion<Availability, Availability> criterion = AvailabilityCriterion.getByInstructorId(instructorID);	
						  instructorAvailability= availabilityDAO.get(criterion);
						  long endTime6 = System.currentTimeMillis();
						  long totaltime6 = endTime6 - startTime6;			  					  
						  //Logic for getting the instructor appointment for the week
						  long startTime7 = System.currentTimeMillis();
						  Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> instructorCriterion = 
								  		AppointmentCriterion.findInstructorAvailableHours(instructorID,  availaibilityStartDay.toDate(),availaibilityWeekDay);
						  long endTime7 = System.currentTimeMillis();
						  long totaltime7 = endTime7 - startTime7;
						  instructorDayAvailabilityMap  = appointmentDAO.get(instructorCriterion);
						 			  
						  String parsingDate = "";
						  
						  DateTime currentDay = null;
						
						  //Block for get details of each day
						  
						  for(int i=0;i<=days;i++)
						  {
							  
							
							  
							  instructorAvailableHoursDTOList  = null;
							  
							  currentDay =  availaibilityStartDay.plusDays(i);
							  
							   parsingDate = currentDay.toString(DateTimeUtil.DATE_PATTERN);
						
							   Boolean isTimeOffDay = false;
								  
							   if (timeOffMap.containsKey(instructorInfoDTO.getInstructorId())) {
									  
									  timeOffList = timeOffMap.get(instructorInfoDTO.getInstructorId());
									  
									  timeOffHoursDTOList = new ArrayList<InstructorAvailableHoursDTO>();
										
																  
									  for(InstructorAvailableHoursDTO timeOffAvailabilityDTO : timeOffList)
									  {
										  
										
										  
										  
										  timeOffStartDate = timeOffAvailabilityDTO.getAppointmentStartDate();
										  
										  timeOffEndDate  = timeOffAvailabilityDTO.getAppointmentEndDate();
										  
										  if(currentDay.isEqual(timeOffStartDate )&& currentDay.isEqual(timeOffEndDate))
										  {
											  timeOffStartTime = timeOffAvailabilityDTO.getAppointmentStartTime().plusMinutes(1);
											  timeOffEndTime   = timeOffAvailabilityDTO.getAppointmentEndTime();
										  }
										  
										  else
										  {
											  if(currentDay.isEqual(timeOffStartDate ) && currentDay.isBefore(timeOffEndDate))
											  {
												  timeOffStartTime = timeOffAvailabilityDTO.getAppointmentStartTime().plusMinutes(1);
												  timeOffEndTime   = new LocalTime ("23:59");		
												  
											  }	  
												  
											  else 
											  {	  
												 if (currentDay.isAfter(timeOffStartDate) && currentDay.isEqual(timeOffEndDate))
												 {
													 timeOffStartTime = new LocalTime ("00:01");
													  timeOffEndTime  = timeOffAvailabilityDTO.getAppointmentEndTime();;		
												 }
												 
												 else
												 {
													 if (currentDay.isAfter(timeOffStartDate) && currentDay.isBefore(timeOffEndDate))
													 {
														 isTimeOffDay = true;													 
														 break;
													 }
													 
												 }	 
											  }	 
																					  
										  }
										  
										  if(null != timeOffStartTime &&  null != timeOffEndTime)
										  {
											  timeOffHoursDTO = new InstructorAvailableHoursDTO(timeOffStartTime,timeOffEndTime);
											  timeOffHoursDTOList.add(timeOffHoursDTO);
											  
										  }
										  
										  timeOffStartTime = null;
										  timeOffEndTime = null;
											  
												  
									  }
							
							  }
							   
							   
							   else
							   {
								   timeOffHoursDTOList = null;
							   }
							   
							   
							 
							if(!isTimeOffDay)
							{	
							   
							  if(instructorDayAvailabilityMap.containsKey(parsingDate))
							   {	  
								    instructorAvailableHoursDTOList = instructorDayAvailabilityMap.get(parsingDate);
								  
							   }	   
							  
							   if(null != instructorAvailableHoursDTOList  && instructorAvailableHoursDTOList.size() > 0 )
							   {
								   if(null != timeOffHoursDTOList &&  timeOffHoursDTOList.size() > 0)
									   instructorAvailableHoursDTOList.addAll(timeOffHoursDTOList);							   
								   
							   }
							   else
							   {
								   if(null != timeOffHoursDTOList &&  timeOffHoursDTOList.size() > 0)
									   instructorAvailableHoursDTOList = timeOffHoursDTOList;
								   
								   
							   }
								   
							 	   	  	   
							   dayOfWeek = currentDay.getDayOfWeek();
							 
							   
							   if(null !=  instructorAvailability.getStartTimeByDayOfWeek(dayOfWeek) && null !=    instructorAvailability.getEndTimeByDayOfWeek(dayOfWeek))
							   {	   
									   startTime = new DateTime(instructorAvailability.getStartTimeByDayOfWeek(dayOfWeek)).minuteOfDay().roundFloorCopy();;					   
											 
									   endTime   = new DateTime(instructorAvailability.getEndTimeByDayOfWeek(dayOfWeek)).minuteOfDay().roundFloorCopy();;
							  
								   if(null != startTime && null != endTime)
								   {	  
									   
 
						   //----Changes for GSSP-321 for get profileTimeoffStartTime profileTimeoffEndTime value for free slots
									   profileTimeoffStartTime=null;//for clear the object on iteration
									   profileTimeoffEndTime=null;
									   SimpleDateFormat getHourMin = new SimpleDateFormat("HH:mm");
									   if(profileTimeOffMap.get(currentDay.toString("yyyy-MM-dd")) != null)
									   {
										   ProfileTimeOffDTO profileTimeOffDate =(ProfileTimeOffDTO)profileTimeOffMap.get(currentDay.toString("yyyy-MM-dd"));
										   if(profileTimeOffDate !=null)
										   {
													String startTimeHrs =  getHourMin.format(profileTimeOffDate.getStartTime());
												   	String endTimeHrs =  getHourMin.format(profileTimeOffDate.getEndTime());
												   	profileTimeoffStartTime = new LocalTime(startTimeHrs);
													profileTimeoffEndTime =   new LocalTime(endTimeHrs);
										    }
									   }
	
							 //----Changes for GSSP-321 for call getAvailableSlotsOnProfileTimeOff only on leave days
									   if(profileTimeoffStartTime != null && profileTimeoffEndTime!= null  ){
										    freeSlotsList = WebServiceUtil.getAvailableSlotsOnProfileTimeOff(startTime, endTime, instructorAvailableHoursDTOList,profileTimeoffStartTime,profileTimeoffEndTime);
									   }else{
										    freeSlotsList = WebServiceUtil.getAvailableSlots(startTime, endTime, instructorAvailableHoursDTOList);
									   }
									   
									  if(null != freeSlotsList && freeSlotsList.size() > 0)
									   {
										   dividedSlotsList = divideSlots(dateTimeRoomAvalabilityMap,currentDay,freeSlotsList,profileId,duration.intValue(),
												   instructorID,activityIds[0],instructorServiceDTO.getAptId(),instructorName,nonAvailableSlots);
										   
										   if(!slotsByDateMap.containsKey(parsingDate))
										   {
											   List<Times> list = new ArrayList<Times>();											   
											   slotsByDateMap.put(parsingDate, list);     
										   }
										   
										   appointmentDateList = slotsByDateMap.get(parsingDate);    
										   appointmentDateList.addAll(dividedSlotsList);
										   
										   
									   }
								
							   }   
							   
						   }	   
					 
						}
						

					  }	
  
				  }
			        					  
		  			for(String slotDate : slotsByDateMap.keySet())
					{
						
						 appointmentDateList = slotsByDateMap.get(slotDate);
						//Added for LES-149
						 appointmentDateList.sort(Times.startTimeasLTComparator);//Java 8 comparator
						 instructorLessonsByDay  = new Lessons(slotDate,appointmentDateList);
						 instructorLessonsByDayList.add(instructorLessonsByDay);
						 
						 
					}
					
					if(null != instructorLessonsByDayList && instructorLessonsByDayList.size() > 0)
					{
						result.setLessons(instructorLessonsByDayList);
					}
					
			 }	
			 

		}	
		  
		  
		  
		  //Added for LES_27 Method to divide Slots based on Duration
		  /**
		 * 
		 * Method to divide Slots based on Duration
		 * @param currentDay
		 * @param freeSlotsList
		 * @param profileId
		 * @param duration
		 * @param instructorId
		 * @param activityId
		 * @param appId
		 * @param instructorName
		 * 
		 * @return List<Times>
		 * @throws Exception 
		 */
		private List<Times> divideSlots(Map<String,Boolean> dateTimeRoomAvalabilityMap , DateTime currentDay,List<InstructorAvailableHoursDTO> freeSlotsList, 
				  long profileId,int duration,long instructorId, long activityId, long appId,String instructorName, Map<String,List<String> >nonAvailableSlots) throws Exception
			{				  					
			
					List<Times> dividedFreeSlotList = new ArrayList<Times>();
				
					LocalTime newStartTime  = null,newEndTime=null,slotEndTime=null; 
					
					 Map<Long, RoomDTO> roomCache = new HashMap<Long, RoomDTO>();
					
					Times dividedFreeSlot = null;				
					
					String startTime = "", endTime="",currentDayTimeString = "";	
					
					String parsingDate = currentDay.toString(DateTimeUtil.DATE_PATTERN_SLASH);						
				
									
					DateTime currentDateStartime = null, currentDateEndTime = null;
					
				
					//Define divideSlotList
					 for (InstructorAvailableHoursDTO  freeSlots:freeSlotsList)
					 {
						 newStartTime = freeSlots.getAppointmentStartTime();
						 slotEndTime =  freeSlots.getAppointmentEndTime();
						 
						 
						 if (newStartTime.getMinuteOfHour() == 15 || newStartTime.getMinuteOfHour() == 45)
								 newStartTime = newStartTime.plusMinutes(15);
						 
						 newEndTime = newStartTime.plusMinutes(duration-1);			
						
						 currentDateStartime = currentDay.plusHours(newStartTime.getHourOfDay());
						 
						 currentDateStartime = currentDateStartime.plusMinutes(newStartTime.getMinuteOfHour());
						 
						 currentDateEndTime  = currentDateStartime.plusMinutes(duration-1);
												
					     while (newEndTime.isBefore(slotEndTime) || newEndTime.equals(slotEndTime)) 
					     {	
					    	 						 						 
							 if(!currentDateStartime.dayOfMonth().equals(currentDay.dayOfMonth()) || 
									 !currentDateEndTime.dayOfMonth().equals(currentDay.dayOfMonth()))
								 break;
							
							 
							 newEndTime =  newEndTime.plusMinutes(1);
							 
							 currentDateEndTime  = currentDateStartime.plusMinutes(1);
					    	 
					    	 if(!profileRoomcache.containsKey(profileId))
					    	 {	 					    		 
						    	 //Get the Rooms for that profile
						    	 Criterion<Room, Map<Long, RoomDTO>> profileRoomcriterion = 
						    	 		RoomCriterion.findByProfileIdAndActivityId(profileId, activityId);
						    	 roomCache = roomDAO.get(profileRoomcriterion);
						    	 profileRoomcache.put(profileId, roomCache);
					    	 }	 
					    	 
					    	 roomCache = profileRoomcache.get(profileId);
					    	 
					    	 startTime = newStartTime.toString(DateTimeUtil.HOURS_PATTERN); 		    			 
					    			 
				    		 endTime= newEndTime.toString(DateTimeUtil.HOURS_PATTERN) ;  
				    		 
				    		 			    		 
				    		 currentDayTimeString = profileId + "_ " + parsingDate + "_" + startTime + "_" + endTime;
				    		 
				    		 boolean available = false ;
				    		 
					    	if(! dateTimeRoomAvalabilityMap.containsKey(currentDayTimeString))
					    		 {
					    		 
						    	 
						    		 if(nonAvailableSlots != null && nonAvailableSlots.containsKey(parsingDate))
						    		 {
						    			  
						    			 List<String> timeList = nonAvailableSlots.get(parsingDate);
						    			 
						    			 for (String curtimeSt : timeList){
						    				
						    				 int index = curtimeSt.indexOf("_");
						                	 String stTime =  curtimeSt.substring(0,index);
						                	 String edTime =  curtimeSt.substring(index+1);
						                	 
						                	 LocalTime lStTime = DateTimeFormat.forPattern("HH:mm").parseLocalTime(stTime);
						                	 LocalTime lEdTime = DateTimeFormat.forPattern("HH:mm").parseLocalTime(edTime);
						                	 
						                	 if((lStTime.isBefore(newStartTime) || lStTime.equals(newStartTime)) && (lEdTime.isAfter(newEndTime) || lEdTime.equals(newEndTime))) {
						                		 available = true;
						                		 break;
						                	 }

						    			 }
						    			 
						    			
						    		 }						    	 
						    	
					    		 								    	 
							    	 dateTimeRoomAvalabilityMap.put(currentDayTimeString, available);
							    	 
					    		 }
							    	 
				    		 				    		
						    	 
					    	 available = dateTimeRoomAvalabilityMap.get(currentDayTimeString);
					
					    	 if(available)			    		 			    	 
					    	 {		 
					    			
					    		 dividedFreeSlot = new Times(WebServiceUtil.formatTimeTo12Hr(startTime), 
					    				 WebServiceUtil.formatTimeTo12Hr(endTime),instructorName,instructorId);

					    		 dividedFreeSlotList.add(dividedFreeSlot);					    		 
					    	 }	
					  
					    	 newStartTime = newStartTime.plusMinutes(30);
					    	 
					    	 newEndTime =  newStartTime.plusMinutes(duration-1);  
					    	 
					    	 currentDateStartime= currentDateStartime.plusMinutes(30);
					    	 
					    	 currentDateEndTime = currentDateEndTime.plusMinutes(duration-1);
					    	 
					    	 
					    
					     }		
					 }
				 
					 return dividedFreeSlotList;
					
			}
		
		
		
		//  Added for GSSP-270
		
		private  List<CancelReasonDTO>  setCancelReasonCode () 
		{
			
			
			List<CancelReasonDTO>  cancelReasonDTOList =  new ArrayList<CancelReasonDTO>();
			
			CancelReasonDTO  cancelReasonDTO;  
			
			List<AppointmentCancelReason> cancelReasonList = new ArrayList<AppointmentCancelReason>();
			
			
			cancelReasonList=appointmentService.getcancelReason(AppConstants.SINGLE_CANCEL_REASON);
			
			for(AppointmentCancelReason appointmentCancelReason: cancelReasonList){
				cancelReasonDTO =  new CancelReasonDTO(appointmentCancelReason.getAppointmentcancelreasonID(),
						appointmentCancelReason.getCancelReason());
				
				cancelReasonDTOList.add(cancelReasonDTO);
				
				
			}
			
			
			return  cancelReasonDTOList;
			
			
			
			
		}

		/**
		 * Helper method to create default location profile when appointment ID is not provided
		 *
		 * @return LocationProfileInfoDTO with default values
		 */
		private LocationProfileInfoDTO createDefaultLocationProfile() {
			LocationProfileInfoDTO locationProfile = new LocationProfileInfoDTO();

			// Set default values - these should be configurable based on business requirements
			locationProfile.setProfileID(1L); // Default profile ID
			locationProfile.setActivityID(25L); // Default activity ID (Guitar lessons)

			return locationProfile;
		}

}