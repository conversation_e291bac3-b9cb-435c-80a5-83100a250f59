package com.guitarcenter.scheduler.webservice;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.webservice.dto.InstructorACTServiceDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceResponseDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorActivitiesAPIDTO;

 
@RestController
public class InstructorAvailabilityService {
	
	
	
	
	private static final Logger		LOG  = LoggerFactory.getLogger("schedulerlog");
 
	@Autowired
	private InstructorService instructorService;
 
 
		  @RequestMapping(value="/InstructorAvailabilty",
				  method=RequestMethod.POST, headers = {"content-type=application/x-www-form-urlencoded"})  
		  public InstructorAVLServiceResponseDTO getInstructorAvailability(InstructorAVLServiceDTO  instructorServiceDTO) 
		  {					  
			  	
			  InstructorAVLServiceResponseDTO result = new InstructorAVLServiceResponseDTO();
			  try
			  {
				  result = instructorService.getInstructorAvailability(instructorServiceDTO);
  
			  }
			  catch(Exception e)
			  {
				  e.printStackTrace();
				  LOG.error("Error during fetching of data in new instructor Availability api Web Service", e);
			  }						 
			
			  return result;
			  			  
		  }
		  
		  
		  @RequestMapping(value="/InstructorActivity",
				  method=RequestMethod.POST, headers = {"content-type=application/x-www-form-urlencoded"})  
		  public InstructorActivitiesAPIDTO getInstructorActivity(InstructorACTServiceDTO  instructorServiceDTO)  
		  {					  
			  	
			  InstructorActivitiesAPIDTO result = new InstructorActivitiesAPIDTO();
			 // List<Instructor>  list = new ArrayList<Instructor>();
			  try{
				  result = instructorService.getInstructorActivity(instructorServiceDTO);
  
			  }
			  catch(Exception e)
			  {
				  e.printStackTrace();
				  LOG.error("Error during fetching of data in new instructor's Activity api Web Service", e);
			  }						 
			
			  return result;
			  			  
		  }
 
	
}