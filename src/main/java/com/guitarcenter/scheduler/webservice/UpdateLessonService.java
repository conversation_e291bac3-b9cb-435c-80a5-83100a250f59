package com.guitarcenter.scheduler.webservice;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.guitarcenter.scheduler.common.util.CalendarUtil;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.AvailabilityDAO;
import com.guitarcenter.scheduler.dao.RoomDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.RoomCriterion;
import com.guitarcenter.scheduler.dao.criterion.dto.RoomDTO;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.ServiceLogger;
import com.guitarcenter.scheduler.model.enums.IsRecurring;
import com.guitarcenter.scheduler.service.AppointmentEmailService;
import com.guitarcenter.scheduler.service.AppointmentService;
import com.guitarcenter.scheduler.service.AvailabilityService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.RoomService;
import com.guitarcenter.scheduler.service.ValidationService;
import com.guitarcenter.scheduler.webservice.dto.UpdateLessonServiceDTO;
import com.guitarcenter.scheduler.webservice.dto.UpdateLessonServiceResponseDTO;
import com.guitarcenter.scheduler.webservice.util.WebServiceUtil;

@RestController
public class UpdateLessonService {

	@Autowired
	private AppointmentDAO appointmentDAO;

	@Resource
	private AvailabilityService availabilityService;

	@Autowired
	private InstructorService instructorService;

	@Autowired
	private RoomService roomService;

	@Autowired
	private AvailabilityDAO availabilityDAO;

	private static final Logger		LOG					        = LoggerFactory.getLogger("schedulerlog");
	

	@Autowired
	private ValidationService validationService;

	@Autowired
	private AppointmentService appointmentService;

	@Autowired
	private RoomDAO roomDAO;

	@Autowired
	private AppointmentEmailService appointmentEmailService;

	@RequestMapping(value = "/UpdateLessonService", method = RequestMethod.POST, headers = { "content-type=application/x-www-form-urlencoded" })
	public UpdateLessonServiceResponseDTO getActivityCode(
			UpdateLessonServiceDTO updateLessonServiceDTO) throws JsonProcessingException {
		 UpdateLessonServiceResponseDTO result = new UpdateLessonServiceResponseDTO();
		//GSSP-208 changes
			ServiceLogger serviceLogger = new ServiceLogger();
			   String successString;
			serviceLogger.setServiceName("UpdateLessonService");
			
			serviceLogger.setAppointmentId(updateLessonServiceDTO
					.getAptId());
			serviceLogger.setCustomerMemberId(updateLessonServiceDTO
					.getCustMemberID());
			Criterion<Appointment,String>  appointmentCriterion =
				    AppointmentCriterion.logService(serviceLogger);
		  
		  successString = appointmentDAO.get(appointmentCriterion);
			  
		if (updateLessonServiceDTO.getAptId() != null
				&& (updateLessonServiceDTO.getAptId().trim().length() > 0)
				&& updateLessonServiceDTO.getCustMemberID() != null
				&& (updateLessonServiceDTO.getCustMemberID().trim().length() > 0)
				&& updateLessonServiceDTO.getEndTime() != null
				&& (updateLessonServiceDTO.getEndTime().trim().length() > 0)
				&& updateLessonServiceDTO.getInsId() != null
				&& (Long.toString(updateLessonServiceDTO.getInsId()).trim()
						.length() > 0)
				&& updateLessonServiceDTO.getStartTime() != null
				&& (updateLessonServiceDTO.getStartTime().trim().length() > 0)
				&& updateLessonServiceDTO.getDate() != null
				&& (updateLessonServiceDTO.getDate().trim().length() > 0)
				&& updateLessonServiceDTO.getDay() != null
				&& (updateLessonServiceDTO.getDay().trim().length() > 0)
				&& (updateLessonServiceDTO.getCurCustMemberID().trim().length() >0)
				&& (updateLessonServiceDTO.getCurCustMemberID()!=null)
				) {

			String sStartTime = null, sEndTime = null, custMemberID = null, cancelType = "0",
					timeZone=null;

			if (checkInsRecordInDB(updateLessonServiceDTO)) {
				Map<String, String> check = new HashMap<String, String>();

				check = checkExternalRecordInDB(updateLessonServiceDTO);
				if (check.get("status").equalsIgnoreCase("true")) {
					custMemberID = check.get("custMemberID");
				} else {
					result.setErrorKey("UnexpectedError");
					result.setErrorMessage("Unexpected error occured.");
					result.setStatus("error");
					return result;
				}

				// LocalDate lday;

				Date dDate = null, dStartTime = null, dEndTime = null;

				DateTime dStartDateTime = null, dEndDateTime = null;
				
				String sStartDateTime=null, sCurStartDateTime=null;

				Room availableRoom;

				Appointment dbapmnt = null;

				DateFormat df1 = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN);
				DateFormat df2 = new SimpleDateFormat(
						DateTimeUtil.DATETIME_PATTERN_HYPHEN);
				DateFormat df3 = new SimpleDateFormat(
						DateTimeUtil.HOURS_PATTERN);
				DateFormat df4 = new SimpleDateFormat(
						DateTimeUtil.TIME_OFF_DATE_TIME2);				
				DateTimeFormatter dtf1 = DateTimeFormat
						.forPattern(DateTimeUtil.DATETIME_PATTERN);
				Criterion<Appointment, Appointment> criterion = AppointmentCriterion
						.getByAppointmentId(new Long(updateLessonServiceDTO
								.getAptId()));

				dbapmnt = appointmentDAO.get(criterion);
				//GSSP-278
				String roomName=dbapmnt.getRoom().getProfileRoomName();
				Criterion<Appointment, String> tzCriterion = AppointmentCriterion
						.findTimeZoneByAptID(updateLessonServiceDTO
								.getAptId());
				
				timeZone=appointmentDAO.get(tzCriterion);
				Set<Customer> customers =new HashSet<Customer>();
				customers=dbapmnt.getCustomers();
				List<Long> customerIds=new ArrayList<Long>();
				
				for(Customer child:customers){
					customerIds.add(child.getCustomerId());
				}

				try {
					dDate = df1.parse(updateLessonServiceDTO.getDate());
					dStartTime = df4.parse(updateLessonServiceDTO
							.getStartTime());
					dEndTime = df4.parse(updateLessonServiceDTO.getEndTime());
					String sDate1 = df1.format(dDate);
					sStartTime = df3.format(dStartTime);
					sEndTime = df3.format(dEndTime);
					sStartDateTime = sDate1 + " " + sStartTime + ":00";
					String sEndDateTime = sDate1 + " " + sEndTime + ":00";
					sCurStartDateTime= WebServiceUtil.getCurDateForTZ(timeZone) + " " +  
							WebServiceUtil.getCurTmForTZ(timeZone);
					dStartDateTime = dtf1.parseDateTime(sStartDateTime);
					dEndDateTime = dtf1.parseDateTime(sEndDateTime);
					
				} catch (ParseException e1) {

					LOG.error("Error while typecasting the date format");
				}
			if(!isTodaysAppointment(sStartDateTime,sCurStartDateTime)){
				if (!isTodaysAppointment(dbapmnt.getStartTimeStr(),sCurStartDateTime)) {
					if(validationService
							.checkUpdateCustomerByAppointmentTime(false,
									customerIds,
									df2.format(dDate), df2.format(dDate),
									CalendarUtil.plusFormatTime(sStartTime, 1),
									CalendarUtil.plusFormatTime(sEndTime, -1),
									dbapmnt.getLocationProfile().getProfileId(),
									updateLessonServiceDTO.getAptId())){
					if (validationService
							.checkUpdateInstructorByAppointmentTime(false,
									updateLessonServiceDTO.getInsId(),
									df2.format(dDate), df2.format(dDate),
									CalendarUtil.plusFormatTime(sStartTime, 1),
									CalendarUtil.plusFormatTime(sEndTime, -1),
									updateLessonServiceDTO.getAptId())) {
						Map<Long, RoomDTO> roomCache = new HashMap<Long, RoomDTO>();

						Criterion<Room, Map<Long, RoomDTO>> profileRoomcriterion = RoomCriterion
								.findByProfileIdAndActivityId(dbapmnt
										.getLocationProfile().getProfileId(),
										dbapmnt.getActivity().getActivityId());

						roomCache = roomDAO.get(profileRoomcriterion);

						// Check if any rooms are available for this new Start and new endTime for this lesson type. If yes and add it to the slot, else don’t add
						
						Criterion<Room, Room> roomCriterion = RoomCriterion
								.findRoomByProfileIdAndActivityIdAndDate(
										roomCache, dbapmnt.getLocationProfile()
												.getProfileId(), dStartDateTime
												.plusSeconds(1).toDate(),
										dEndDateTime.minusSeconds(1).toDate());

						availableRoom = roomDAO.get(roomCriterion);
						
						
						if (availableRoom != null) {

							Long personID = getPersonID(custMemberID);
							dbapmnt.setStartTime(dStartDateTime.toDate());
							dbapmnt.setEndTime(dEndDateTime.toDate());
							dbapmnt.setRoom(availableRoom);
							dbapmnt.getInstructor().setInstructorId(
									updateLessonServiceDTO.getInsId());
							try {

								appointmentService
										.updateLessonServiceAppointment(
												dbapmnt, personID);
								
								appointmentService.updateAppointmentLog(dbapmnt, personID);
								
							} catch (Exception e) {

								LOG.error("Error while updating the record ");
								result.setErrorKey("UnexpectedError");
								result.setErrorMessage("Unexpected error occured.");
								result.setStatus("error");								
								return result;
							}

							try {
								if (IsRecurring.Y.equals(dbapmnt
										.getAppointmentSeries()
										.getIsRecurring()))
									cancelType = "1";
								//changes made for GSSp 243
								sendEmailForModifyAppt(
										updateLessonServiceDTO
												.getCustMemberID(),
										dbapmnt.getActivity().getService()
												.getServiceId(),
										updateLessonServiceDTO, cancelType,dbapmnt.getLocationProfile()
										.getProfileId(),dbapmnt.getActivity().getActivityName(),roomName);
								
							} catch (Exception e) {

								LOG.error("Error while sending email to the customers ");
								result.setErrorKey("UnexpectedError");
								result.setErrorMessage("Unexpected error occured.");
								result.setStatus("error");								
								return result;
							}
						} else {

							result.setErrorKey("RoomNotAvailable");
							result.setErrorMessage("The room is not available for the time specified. Please make a different selection.");
							result.setStatus("error");
							return result;
						}
					} else {

						result.setErrorKey("InsNotAvailable");
						result.setErrorMessage("The instructor is not available for the time specified. Please make a different selection.");
						result.setStatus("error");
						return result;
					}

				} else {
					result.setErrorKey("CustNotAvailable");
					result.setErrorMessage("Customer already booked for the specified time. Please make a different selection.");
					result.setStatus("error");
					return result;
					
				}
			
				}else{
					result.setErrorKey("AptInNextDay");
					result.setErrorMessage("This appointment cannot be modified as it falls within next 24 hours. Please contact the store.");
					result.setStatus("error");
					return result;
				}
				}
			
				else{
				LOG.error("Error while updating");
				result.setErrorKey("SelectedTimeInNextDay");
				result.setErrorMessage("Cannot be updated as the selected time falls in next 24 hours. Please contact the store.");
				result.setStatus("error");
				return result;
			}
			
				result.setCustMemberID(updateLessonServiceDTO.getCustMemberID());
				result.setDate(updateLessonServiceDTO.getDate());
				result.setDay(updateLessonServiceDTO.getDay());
				result.setStartTime(updateLessonServiceDTO.getStartTime());
				result.setEndTime(updateLessonServiceDTO.getEndTime());
				result.setInstructorName(updateLessonServiceDTO
						.getInstructorName());
				result.setStatus("Successfully Updated");
				return result;
			} else {
				result.setErrorKey("UnexpectedError");
				result.setErrorMessage("Unexpected error occured.");
				result.setStatus("error");
				return result;
			}

		} else {
			result.setErrorKey("UnexpectedError");
			result.setErrorMessage("Unexpected error occured.");
			result.setStatus("error");
			return result;
		}
		
	}

	/**
	 * @param Date
	 * @return boolean status
	 */
	private boolean isTodaysAppointment(String date1, String date2) {

		boolean status = false;
		try {
			// CONVERTING DATES IN TO STRING
			DateTimeFormatter formatter = DateTimeFormat
					.forPattern(DateTimeUtil.DATETIME_PATTERN);
			DateTime dt1 = formatter.parseDateTime(date1);
			DateTime dt2 = formatter.parseDateTime(date2);
			Duration duration = new Duration(dt2, dt1);

			if (duration.getStandardMinutes() <= 1440) {

				status = true;
			}
		} catch (Exception e) {
			LOG.error("Error while date parsing ");
		}
		return status;
	}

	/**
	 * @param customerMemberID
	 * @return Long personID
	 */

	private Long getPersonID(String customerMemberID) {

		Long personID;
		Criterion<Appointment, Long> customerCriterion = AppointmentCriterion
				.findPersonIDByExternalId(customerMemberID);

		personID = appointmentDAO.get(customerCriterion,
				DAOHelper.FETCH_APPOINTMENT_SERIES);

		return personID;
	}

	/**
	 * @param customerMemberID
	 *            ,serviceId,updateLessonServiceDTO
	 * @return Boolean
	 */
	//GSSP-278 code changes
	private boolean sendEmailForModifyAppt(String customerMemberID,
			Long serviceId, UpdateLessonServiceDTO updateLessonServiceDTO,
			String cancelType,long profileId,String activityName,String roomName) {
		
		final String serviceType="UPDATE";

		String customerExternalIds[] = customerMemberID.split("_");
		String cancelreason = null;
		List<Map<String, Object>> emailBodies = new ArrayList<Map<String, Object>>();
		Criterion<Appointment, List<Map<String, Object>>> customerCriterion = AppointmentCriterion
				//changes for  GSSP 253
				.getEmailIdsByCustomersExternalId(customerExternalIds, updateLessonServiceDTO.getCurCustName(),profileId);
		emailBodies = appointmentDAO.get(customerCriterion,
				DAOHelper.FETCH_APPOINTMENT_SERIES);
		//GSSP-278

		//GSSP-253 profile id  parameter passed.
		appointmentEmailService.sendEmailForModifySingleAppt(
				updateLessonServiceDTO.getDate(),updateLessonServiceDTO.getStartTime(),updateLessonServiceDTO.getEndTime(), emailBodies, serviceId, cancelType,updateLessonServiceDTO.getInstructorName(),serviceType,updateLessonServiceDTO.getCurCustName(), profileId,activityName,roomName);

		return false;

	}

	public boolean checkInsRecordInDB(
			UpdateLessonServiceDTO updateLessonServiceDTO) {

		Criterion<Appointment, Long> instructorCriterion = AppointmentCriterion
				.checkInstructorDBRecord(updateLessonServiceDTO.getInsId());
		Long checkIns = appointmentDAO.get(instructorCriterion,
				DAOHelper.FETCH_APPOINTMENT_SERIES);

		return checkIns == 1 ? Boolean.TRUE : Boolean.FALSE;

	}

	public Map<String, String> checkExternalRecordInDB(
			UpdateLessonServiceDTO updateLessonServiceDTO) {

		Map<String, String> check = new HashMap<String, String>();
		check.put("status", "false");
		String customerExternalIds[] = updateLessonServiceDTO.getCustMemberID()
				.split("_");

		for (int i = 0; i < customerExternalIds.length; i++) {

			Criterion<Appointment, Long> customerCriterion = AppointmentCriterion
					.checkCustomerExternalIdDBRecord(customerExternalIds[i]);
			Long checkExternal = appointmentDAO.get(customerCriterion,
					DAOHelper.FETCH_APPOINTMENT_SERIES);
			if (checkExternal == 1) {
				check.put("status", "true");
				check.put("custMemberID", customerExternalIds[i]);
				return check;
			}
		}
		return check;
	}
	

}
