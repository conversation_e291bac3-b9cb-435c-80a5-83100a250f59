package com.guitarcenter.scheduler.webservice;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;


import com.guitarcenter.scheduler.service.*;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.guitarcenter.scheduler.common.util.CalendarUtil;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.AvailabilityDAO;
import com.guitarcenter.scheduler.dao.RoomDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.RoomCriterion;
import com.guitarcenter.scheduler.dao.criterion.dto.RoomDTO;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.ServiceLogger;
import com.guitarcenter.scheduler.model.enums.IsRecurring;
import com.guitarcenter.scheduler.webservice.dto.UpdateSingleApptDTO;
import com.guitarcenter.scheduler.webservice.dto.UpdateSingleApptResponseDTO;
import com.guitarcenter.scheduler.webservice.util.WebServiceUtil;

@RestController
public class UpdateSingleApptController {

    @Autowired
    private AppointmentDAO appointmentDAO;

    @Autowired
    private AvailabilityService availabilityService;

    @Autowired
    private InstructorService instructorService;

    @Autowired
    private RoomService roomService;

    @Autowired
    private AvailabilityDAO availabilityDAO;

    private static final Logger LOG = LoggerFactory.getLogger("schedulerlog");

    @Autowired
    private ValidationService validationService;

    @Autowired
    private AppointmentService appointmentService;

    @Autowired
    private RoomDAO roomDAO;

    @Autowired
    private AppointmentEmailService appointmentEmailService;

    @Autowired
    private AsyncEmailService asyncEmailService;


    @RequestMapping(value = "/UpdateSingleAppt", method = RequestMethod.POST, headers = { "content-type=application/json" })
    public UpdateSingleApptResponseDTO updateSingleApptResponse(
            @RequestBody UpdateSingleApptDTO updateSingleApptDTO) throws JsonProcessingException {
        final UpdateSingleApptResponseDTO result = new UpdateSingleApptResponseDTO();

        // Log service
        final ServiceLogger serviceLogger = new ServiceLogger();
        serviceLogger.setServiceName("UpdateSingleAppt");
        serviceLogger.setAppointmentId(updateSingleApptDTO.getAptId());
        serviceLogger.setCustomerMemberId(updateSingleApptDTO.getCustMemberID());
        final Criterion<Appointment, String> appointmentCriterion = AppointmentCriterion.logService(serviceLogger);
        appointmentDAO.get(appointmentCriterion);

        // Default curCustMemberID and curCustName if not provided
        if (isNullOrEmpty(updateSingleApptDTO.getCurCustMemberID())) {
            LOG.info("curCustMemberID not provided, using custMemberID instead");
            updateSingleApptDTO.setCurCustMemberID(updateSingleApptDTO.getCustMemberID());
        }
        if (isNullOrEmpty(updateSingleApptDTO.getCurCustName())) {
            LOG.info("curCustName not provided, using default value");
            updateSingleApptDTO.setCurCustName("Customer");
        }

        // Validate required fields
        if (isNullOrEmpty(updateSingleApptDTO.getAptId())
                || isNullOrEmpty(updateSingleApptDTO.getCustMemberID())
                || isNullOrEmpty(updateSingleApptDTO.getEndTime())
                || updateSingleApptDTO.getInsId() == null
                || isNullOrEmpty(updateSingleApptDTO.getStartTime())
                || isNullOrEmpty(updateSingleApptDTO.getDate())) {
            return setErrorResult(result, "UnexpectedError", "Unexpected error occured.");
        }

        String sStartTime, sEndTime, custMemberID, cancelType = "0", timeZone;
        sStartTime = sEndTime = custMemberID = timeZone = null;

        if (!checkInsRecordInDB(updateSingleApptDTO)) {
            return setErrorResult(result, "UnexpectedError", "Unexpected error occured.");
        }

        Map<String, String> check = checkExternalRecordInDB(updateSingleApptDTO);
        if (!"true".equalsIgnoreCase(check.get("status"))) {
            return setErrorResult(result, "UnexpectedError", "Unexpected error occured.");
        }
        custMemberID = check.get("custMemberID");

        Date dDate = null, dStartTime = null, dEndTime = null;
        DateTime dStartDateTime = null, dEndDateTime = null;
        String sStartDateTime = null, sCurStartDateTime = null;
        Room availableRoom;
        Appointment dbapmnt;

        final DateFormat df1 = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN);
        final DateFormat df2 = new SimpleDateFormat(DateTimeUtil.DATETIME_PATTERN_HYPHEN);
        final DateFormat df3 = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN);
        final DateFormat df4 = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_TIME2);
        final DateTimeFormatter dtf1 = DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN);

        dbapmnt = appointmentDAO.get(AppointmentCriterion.getByAppointmentId(Long.valueOf(updateSingleApptDTO.getAptId())));
        final String roomName = dbapmnt.getRoom().getProfileRoomName();
        timeZone = appointmentDAO.get(AppointmentCriterion.findTimeZoneByAptID(updateSingleApptDTO.getAptId()));

        final Set<Customer> customers = dbapmnt.getCustomers();
        final List<Long> customerIds = new ArrayList<>();
        for (Customer child : customers) {
            customerIds.add(child.getCustomerId());
           if (child != null
                   && child.getPerson() != null
                   && child.getPerson().getPhone() != null
                   && !child.getPerson().getPhone().trim().isEmpty()) {
               updateSingleApptDTO.setCustomerPhone(child.getPerson().getPhone());
           }
        }

        try {
            dDate = df1.parse(updateSingleApptDTO.getDate());
            dStartTime = df4.parse(updateSingleApptDTO.getStartTime());
            dEndTime = df4.parse(updateSingleApptDTO.getEndTime());
            String sDate1 = df1.format(dDate);
            sStartTime = df3.format(dStartTime);
            sEndTime = df3.format(dEndTime);
            sStartDateTime = sDate1 + " " + sStartTime + ":00";
            String sEndDateTime = sDate1 + " " + sEndTime + ":00";
            sCurStartDateTime = WebServiceUtil.getCurDateForTZ(timeZone) + " " + WebServiceUtil.getCurTmForTZ(timeZone);
            dStartDateTime = dtf1.parseDateTime(sStartDateTime);
            dEndDateTime = dtf1.parseDateTime(sEndDateTime);
        } catch (ParseException e) {
            LOG.error("Error while typecasting the date format", e);
            return setErrorResult(result, "UnexpectedError", "Unexpected error occured.");
        }

        if (isTodaysAppointment(sStartDateTime, sCurStartDateTime)
                || isTodaysAppointment(dbapmnt.getStartTimeStr(), sCurStartDateTime)) {
            return setErrorResult(result, "AptInNextDay", "This appointment cannot be modified as it falls within next 24 hours. Please contact the store.");
        }

        if (!validationService.checkUpdateCustomerByAppointmentTime(false, customerIds, df2.format(dDate), df2.format(dDate),
                CalendarUtil.plusFormatTime(sStartTime, 1), CalendarUtil.plusFormatTime(sEndTime, -1),
                dbapmnt.getLocationProfile().getProfileId(), updateSingleApptDTO.getAptId())) {
            return setErrorResult(result, "CustNotAvailable", "Customer already booked for the specified time. Please make a different selection.");
        }

        if (!validationService.checkUpdateInstructorByAppointmentTime(false, updateSingleApptDTO.getInsId(), df2.format(dDate), df2.format(dDate),
                CalendarUtil.plusFormatTime(sStartTime, 1), CalendarUtil.plusFormatTime(sEndTime, -1), updateSingleApptDTO.getAptId())) {
            return setErrorResult(result, "InsNotAvailable", "The instructor is not available for the time specified. Please make a different selection.");
        }

        final Map<Long, RoomDTO> roomCache = roomDAO.get(RoomCriterion.findByProfileIdAndActivityId(
                dbapmnt.getLocationProfile().getProfileId(), dbapmnt.getActivity().getActivityId()));

        availableRoom = roomDAO.get(RoomCriterion.findRoomByProfileIdAndActivityIdAndDate(
                roomCache, dbapmnt.getLocationProfile().getProfileId(),
                dStartDateTime.plusSeconds(1).toDate(), dEndDateTime.minusSeconds(1).toDate()));

        if (availableRoom == null) {
            return setErrorResult(result, "RoomNotAvailable", "The room is not available for the time specified. Please make a different selection.");
        }

        final Long personID = getPersonID(custMemberID);
        dbapmnt.setStartTime(dStartDateTime.toDate());
        dbapmnt.setEndTime(dEndDateTime.toDate());
        dbapmnt.setRoom(availableRoom);
        dbapmnt.getInstructor().setInstructorId(updateSingleApptDTO.getInsId());
        // Add note from DTO if it exists
        if (updateSingleApptDTO.getNote() != null) {
            dbapmnt.setNote(updateSingleApptDTO.getNote());
        }
        //update customer phone number if provided
      if (updateSingleApptDTO.getCustomerPhone() == null || updateSingleApptDTO.getCustomerPhone().trim().isEmpty()) {
            Set<Customer> customersSet = new HashSet<>();
            for (Customer customer : customers) {
                if (customer.getExternalId().equals(custMemberID)) {
                    updateSingleApptDTO.setCustomerPhone(customer.getPerson().getPhone());
                }
              //  customersSet.add(customer);
            }
          //  dbapmnt.setCustomers(customersSet);
        }

        try {
            appointmentService.updateLessonServiceAppointment(dbapmnt, personID);
            appointmentService.updateAppointmentLog(dbapmnt, personID);
        } catch (Exception e) {
            LOG.error("Error while updating the record", e);
            return setErrorResult(result, "UnexpectedError", "Unexpected error occured.");
        }

        try {
            if (IsRecurring.Y.equals(dbapmnt.getAppointmentSeries().getIsRecurring())) {
                cancelType = "1";
            }
            sendEmailForModifyAppt(updateSingleApptDTO.getCustMemberID(),
                    dbapmnt.getActivity().getService().getServiceId(),
                    updateSingleApptDTO, cancelType, dbapmnt.getLocationProfile().getProfileId(),
                    dbapmnt.getActivity().getActivityName(), roomName);
        } catch (Exception e) {
            LOG.error("Error while sending email to the customers", e);
            return setErrorResult(result, "UnexpectedError", "Unexpected error occured.");
        }

        result.setCustMemberID(updateSingleApptDTO.getCustMemberID());
        result.setDate(updateSingleApptDTO.getDate());
        if (updateSingleApptDTO.getDay() != null && !updateSingleApptDTO.getDay().trim().isEmpty()) {
            result.setDay(updateSingleApptDTO.getDay());
        } else {
            try {
                DateFormat df = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN);
                Date date = df.parse(updateSingleApptDTO.getDate());
                DateFormat dayFormat = new SimpleDateFormat("EEEE");
                result.setDay(dayFormat.format(date));
            } catch (Exception e) {
                LOG.warn("Could not calculate day from date: {}", e.getMessage());
            }
        }
        result.setStartTime(updateSingleApptDTO.getStartTime());
        result.setEndTime(updateSingleApptDTO.getEndTime());
        result.setInstructorName(updateSingleApptDTO.getInstructorName());
        result.setNote(updateSingleApptDTO.getNote());
        result.setStatus("Successfully Updated");
        return result;
    }

    // Utility methods
    private boolean isNullOrEmpty(String s) {
        return s == null || s.trim().isEmpty();
    }

    private UpdateSingleApptResponseDTO setErrorResult(UpdateSingleApptResponseDTO result, String errorKey, String errorMessage) {
        result.setErrorKey(errorKey);
        result.setErrorMessage(errorMessage);
        result.setStatus("error");
        return result;
    }

    /**
     * @param
     * @return boolean status
     */
  private boolean isTodaysAppointment(final String date1, final String date2) {
        if (isNullOrEmpty(date1) || isNullOrEmpty(date2)) {
            LOG.error("Input dates are null or empty");
            return false;
        }
        try {
            final DateTimeFormatter formatter = DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN);
            final DateTime startDateTime = formatter.parseDateTime(date1);
            final DateTime compareDateTime = formatter.parseDateTime(date2);
            final Duration duration = new Duration(compareDateTime, startDateTime);
            return duration.getStandardMinutes() <= 1440;
        } catch (Exception e) {
            LOG.error("Error while parsing dates for isTodaysAppointment: date1={}, date2={}", date1, date2, e);
            return false;
        }
    }

    /**
     * @param customerMemberID
     * @return Long personID
     */

  private Long getPersonID(final String customerMemberID) {
        if (isNullOrEmpty(customerMemberID)) {
            LOG.warn("customerMemberID is null or empty");
            return null;
        }
        final Criterion<Appointment, Long> customerCriterion =
                AppointmentCriterion.findPersonIDByExternalId(customerMemberID);
        return appointmentDAO.get(customerCriterion, DAOHelper.FETCH_APPOINTMENT_SERIES);
    }

    /**
     * @param customerMemberID
     *            ,serviceId,updateSingleApptDTO
     * @return Boolean
     */
    //GSSP-278 code changes
  private boolean sendEmailForModifyAppt(
            final String customerMemberID,
            final Long serviceId,
            final UpdateSingleApptDTO updateSingleApptDTO,
            final String cancelType,
            final long profileId,
            final String activityName,
            final String roomName) {

        final String serviceType = "UPDATE";
        final String[] customerExternalIds = customerMemberID.split("_");

        final Criterion<Appointment, List<Map<String, Object>>> customerCriterion =
                AppointmentCriterion.getEmailIdsByCustomersExternalId(
                        customerExternalIds,
                        updateSingleApptDTO.getCurCustName(),
                        profileId);

        final List<Map<String, Object>> emailBodies = appointmentDAO.get(
                customerCriterion,
                DAOHelper.FETCH_APPOINTMENT_SERIES);

        appointmentEmailService.sendEmailForModifySingleAppt(
                updateSingleApptDTO.getDate(),
                updateSingleApptDTO.getStartTime(),
                updateSingleApptDTO.getEndTime(),
                emailBodies,
                serviceId,
                cancelType,
                updateSingleApptDTO.getInstructorName(),
                serviceType,
                updateSingleApptDTO.getCurCustName(),
                profileId,
                activityName,
                roomName);

      asyncEmailService.sendSMSForModifySingleApptAsync(
                updateSingleApptDTO.getDate(),
                updateSingleApptDTO.getStartTime(),
                updateSingleApptDTO.getEndTime(),
                emailBodies,
                serviceId,
                cancelType,
                updateSingleApptDTO.getInstructorName(),
                serviceType,
                updateSingleApptDTO.getCurCustName(),
                profileId,
                activityName,
                roomName,updateSingleApptDTO);


        return false;
    }

    public boolean checkInsRecordInDB(
            UpdateSingleApptDTO updateSingleApptDTO) {

        try {
            Criterion<Appointment, Long> instructorCriterion = AppointmentCriterion
                    .checkInstructorDBRecord(updateSingleApptDTO.getInsId());
            Long checkIns = appointmentDAO.get(instructorCriterion,
                    DAOHelper.FETCH_APPOINTMENT_SERIES);

            return checkIns != null && checkIns == 1;
        } catch (Exception e) {
            LOG.error("Error checking instructor record: {}", updateSingleApptDTO.getInsId(), e);
            // Return true as a fallback to allow the process to continue
            return true;
        }
    }

  public Map<String, String> checkExternalRecordInDB(final UpdateSingleApptDTO updateSingleApptDTO) {
        final Map<String, String> result = new HashMap<>();
        result.put("status", "false");

        try {
            final String custMemberID = updateSingleApptDTO.getCustMemberID();
            if (!custMemberID.contains("_")) {
                if (isCustomerExternalIdValid(custMemberID)) {
                    result.put("status", "true");
                    result.put("custMemberID", custMemberID);
                    return result;
                }
            } else {
                final String[] customerExternalIds = custMemberID.split("_");
                for (final String externalId : customerExternalIds) {
                    if (isCustomerExternalIdValid(externalId)) {
                        result.put("status", "true");
                        result.put("custMemberID", externalId);
                        return result;
                    }
                }
            }
            LOG.warn("No valid customer found for custMemberID: {}", custMemberID);
            // Fallback: use the original custMemberID
            result.put("status", "true");
            result.put("custMemberID", custMemberID);
        } catch (Exception e) {
            LOG.error("Error in checkExternalRecordInDB", e);
        }
        return result;
    }

    private boolean isCustomerExternalIdValid(final String externalId) {
        try {
            final Criterion<Appointment, Long> criterion = AppointmentCriterion.checkCustomerExternalIdDBRecord(externalId);
            final Long checkExternal = appointmentDAO.get(criterion, DAOHelper.FETCH_APPOINTMENT_SERIES);
            return checkExternal != null && checkExternal == 1;
        } catch (Exception e) {
            LOG.error("Error checking customer external ID: {}", externalId, e);
            return false;
        }
    }
}
