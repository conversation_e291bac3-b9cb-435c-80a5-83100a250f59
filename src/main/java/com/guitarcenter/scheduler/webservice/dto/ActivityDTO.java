package com.guitarcenter.scheduler.webservice.dto;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.RequiresInstructor;

/**
 * Represents activities that are known to the system
 */

@JsonAutoDetect
@JsonIgnoreProperties({ "updatedBy", "updated" })
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class ActivityDTO implements java.io.Serializable {

	private static final long	serialVersionUID	= 3166026907095259951L;
	private Long				activityId;
	private long				version;
	private String				site_id;
	private String				service_id;
	private Date				updated;
	private String				activityName;
	private Long				minimumAttendees;
	private Long				maximumAttendees;
	private Long				minimumDuration;
	private Long				maximumDuration;
	private RequiresInstructor	requiresInstructor;
	private Enabled				enabled;
	private String				externalId;



	public ActivityDTO() {
	}

 
	public Long getActivityId() {
		return this.activityId;
	}



	public void setActivityId(Long activityId) {
		this.activityId = activityId;
	}



	public long getVersion() {
		return this.version;
	}



	public void setVersion(long version) {
		this.version = version;
	}

 

	public String getSite_id() {
		return site_id;
	}


	public void setSite_id(String site_id) {
		this.site_id = site_id;
	}


	public String getService_id() {
		return service_id;
	}


	public void setService_id(String service_id) {
		this.service_id = service_id;
	}


	public Date getUpdated() {
		return this.updated;
	}



	public void setUpdated(Date updated) {
		this.updated = updated;
	}


	public String getActivityName() {
		return this.activityName;
	}



	public void setActivityName(String activityName) {
		this.activityName = activityName;
	}


	public Long getMinimumAttendees() {
		return this.minimumAttendees;
	}



	public void setMinimumAttendees(Long minimumAttendees) {
		this.minimumAttendees = minimumAttendees;
	}


	public Long getMaximumAttendees() {
		return this.maximumAttendees;
	}



	public void setMaximumAttendees(Long maximumAttendees) {
		this.maximumAttendees = maximumAttendees;
	}

	public Long getMinimumDuration() {
		return this.minimumDuration;
	}



	public void setMinimumDuration(Long minimumDuration) {
		this.minimumDuration = minimumDuration;
	}

	public Long getMaximumDuration() {
		return this.maximumDuration;
	}
	
	
	
	public void setMaximumDuration(Long maximumDuration) {
		this.maximumDuration = maximumDuration;
	}


	@Enumerated(EnumType.STRING)
	public RequiresInstructor getRequiresInstructor() {
		return this.requiresInstructor;
	}



	public void setRequiresInstructor(RequiresInstructor requiresInstructor) {
		this.requiresInstructor = requiresInstructor;
	}


	@Enumerated(EnumType.STRING)
	public Enabled getEnabled() {
		return this.enabled;
	}



	public void setEnabled(Enabled enabled) {
		this.enabled = enabled;
	}


	public String getExternalId() {
		return this.externalId;
	}



	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}


}
