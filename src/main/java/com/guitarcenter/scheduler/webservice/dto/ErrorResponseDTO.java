package com.guitarcenter.scheduler.webservice.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * Standard error response DTO for REST API endpoints
 * Provides consistent error format across all webservice controllers
 */
@JsonInclude(Include.NON_NULL)
public class ErrorResponseDTO {
    
    private String errorKey;
    private String errorMessage;
    private String status;
    
    public ErrorResponseDTO() {
    }
    
    public ErrorResponseDTO(String errorKey, String errorMessage, String status) {
        this.errorKey = errorKey;
        this.errorMessage = errorMessage;
        this.status = status;
    }
    
    public String getErrorKey() {
        return errorKey;
    }
    
    public void setErrorKey(String errorKey) {
        this.errorKey = errorKey;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
}
