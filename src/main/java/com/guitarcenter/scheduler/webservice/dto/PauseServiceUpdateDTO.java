package com.guitarcenter.scheduler.webservice.dto;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;

/**
 * DTO for pause service update appointment request
 */
public class PauseServiceUpdateDTO {

    private static final Logger LOG = LoggerFactory.getLogger(PauseServiceUpdateDTO.class);

    private String customerId;
    private String endDate;

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    /**
     * Validates if the customer ID is a 10-digit number
     *
     * @return true if valid, false otherwise
     */
    public boolean isValidCustomerId() {
        return customerId != null && customerId.matches("^\\d{10}$");
    }

    /**
     * Validates if the end date is today or a future date
     *
     * @return true if valid, false otherwise
     */
    public boolean isValidEndDate() {
        if (endDate == null || endDate.trim().isEmpty()) {
            return false;
        }

        try {
            // Parse the input date
            DateFormat dateFormat = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH);
            dateFormat.setLenient(false);
            Date inputDate = dateFormat.parse(endDate);

            // Get today's date without time component
            Date today = new Date();
            today = dateFormat.parse(dateFormat.format(today));

            // Check if input date is today or in the future
            return !inputDate.before(today);

        } catch (ParseException e) {
            LOG.error("Error parsing end date: {}", endDate, e);
            return false;
        }
    }

    @Override
    public String toString() {
        return "PauseServiceUpdateDTO [customerId=" + customerId + ", endDate=" + endDate + "]";
    }
}
