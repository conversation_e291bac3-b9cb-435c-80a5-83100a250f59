package com.guitarcenter.scheduler.webservice.dto;

/**
 * Simplified DTO for location profile information containing only required fields
 * for instructor availability calculations
 */
public class SimpleLocationProfileDTO {
    
    private Long profileID;
    private Long activityID;
    
    /**
     * Default constructor
     */
    public SimpleLocationProfileDTO() {
    }
    
    /**
     * Constructor with required fields
     * 
     * @param profileID - Location profile ID
     * @param activityID - Activity ID
     */
    public SimpleLocationProfileDTO(Long profileID, Long activityID) {
        this.profileID = profileID;
        this.activityID = activityID;
    }
    
    /**
     * Get profile ID
     * 
     * @return profileID
     */
    public Long getProfileID() {
        return profileID;
    }
    
    /**
     * Set profile ID
     * 
     * @param profileID - Location profile ID (required)
     */
    public void setProfileID(Long profileID) {
        this.profileID = profileID;
    }
    
    /**
     * Get activity ID
     * 
     * @return activityID
     */
    public Long getActivityID() {
        return activityID;
    }
    
    /**
     * Set activity ID
     * 
     * @param activityID - Activity ID (required)
     */
    public void setActivityID(Long activityID) {
        this.activityID = activityID;
    }
    
    /**
     * Convert to full LocationProfileInfoDTO for backward compatibility
     * 
     * @return LocationProfileInfoDTO with required fields populated
     */
    public LocationProfileInfoDTO toLocationProfileInfoDTO() {
        LocationProfileInfoDTO dto = new LocationProfileInfoDTO();
        dto.setProfileID(this.profileID);
        dto.setActivityID(this.activityID);
        return dto;
    }
    
    @Override
    public String toString() {
        return "SimpleLocationProfileDTO [profileID=" + profileID + ", activityID=" + activityID + "]";
    }
}
