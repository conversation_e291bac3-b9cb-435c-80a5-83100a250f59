package com.guitarcenter.scheduler.webservice.dto;

/**
 * Simplified DTO for instructor availability requests containing only required fields
 * for instructor availability calculations
 */
public class SimpleInstructorAvailabilityDTO {
    
    private String instructorId;
    private String startDate;
    
    /**
     * Default constructor
     */
    public SimpleInstructorAvailabilityDTO() {
    }
    
    /**
     * Constructor with required fields
     * 
     * @param instructorId - Instructor ID
     * @param startDate - Start date in "yyyy-MM-dd" format
     */
    public SimpleInstructorAvailabilityDTO(String instructorId, String startDate) {
        this.instructorId = instructorId;
        this.startDate = startDate;
    }
    
    /**
     * Get instructor ID
     * 
     * @return instructorId
     */
    public String getInstructorId() {
        return instructorId;
    }
    
    /**
     * Set instructor ID
     * 
     * @param instructorId - Instructor ID (required)
     */
    public void setInstructorId(String instructorId) {
        this.instructorId = instructorId;
    }
    
    /**
     * Get start date
     * 
     * @return startDate in "yyyy-MM-dd" format
     */
    public String getStartDate() {
        return startDate;
    }
    
    /**
     * Set start date
     * 
     * @param startDate - Start date in "yyyy-MM-dd" format (required)
     */
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }
    
    /**
     * Convert to full InstructorAVLServiceDTO for backward compatibility
     * 
     * @return InstructorAVLServiceDTO with required fields populated
     */
    public InstructorAVLServiceDTO toInstructorAVLServiceDTO() {
        InstructorAVLServiceDTO dto = new InstructorAVLServiceDTO();
        dto.setInstructorId(this.instructorId);
        dto.setStartDate(this.startDate);
        return dto;
    }
    
    /**
     * Validate required fields
     * 
     * @return true if all required fields are present and valid
     */
    public boolean isValid() {
        return instructorId != null && !instructorId.trim().isEmpty() &&
               startDate != null && !startDate.trim().isEmpty() &&
               startDate.matches("\\d{4}-\\d{2}-\\d{2}"); // Basic date format validation
    }
    
    @Override
    public String toString() {
        return "SimpleInstructorAvailabilityDTO [instructorId=" + instructorId + ", startDate=" + startDate + "]";
    }
}
