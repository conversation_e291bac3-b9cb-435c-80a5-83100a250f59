package com.guitarcenter.scheduler.webservice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Objects;

/**
 * DTO for sending lesson scheduled data to external CRM API.
 * This class represents the JSON structure for lesson events including:
 * - Trial lesson scheduled events
 * - Lesson modifications (reschedule/cancel)
 * - Subscription lesson events
 *
 * <AUTHOR> Center Development Team
 * @version 1.0
 * @since 2024
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LessonScheduledDTO {

    /**
     * Type of lesson: "Trial" or "Subscription"
     */
    @JsonProperty("LessonsType")
    private String lessonsType;

    /**
     * Event type: "Scheduled", "ReScheduled", or "Cancelled"
     */
    @JsonProperty("Event")
    private String event;

    /**
     * Salesforce Customer ID extracted from order transaction
     */



    /**
     * Requested lesson date in MM-dd-yyyy format
     */
    @JsonProperty("LessonsRequestedDate")
    private String lessonsRequestedDate;

    /**
     * Requested lesson time in h:mm a format
     */
    @JsonProperty("LessonsRequestedTime")
    private String lessonsRequestedTime;

    /**
     * Musical instrument for the lesson
     */
    @JsonProperty("Instrument")
    private String instrument;

    /**
     * Store number/external ID
     */
    @JsonProperty("StoreNumber")
    private String storeNumber;

    /**
     * Store street address
     */
    @JsonProperty("StoreStreet")
    private String storeStreet;

    /**
     * Store city
     */
    @JsonProperty("StoreCity")
    private String storeCity;

    /**
     * Store state
     */
    @JsonProperty("StoreState")
    private String storeState;

    /**
     * Store ZIP code
     */
    @JsonProperty("StoreZip")
    private String storeZip;

    /**
     * Lesson duration in minutes
     */
    @JsonProperty("LessonsDuration")
    private String lessonsDuration;

    /**
     * Follow-up flag: "Y" or "N"
     */
    @JsonProperty("LessonsFollowup")
    private String lessonsFollowup;

    /**
     * Customer full name for follow-up
     */
    @JsonProperty("LessonsFollowupName")
    private String lessonsFollowupName;

    /**
     * Customer email for follow-up
     */
    @JsonProperty("LessonsFollowupEmail")
    private String lessonsFollowupEmail;

    /**
     * Customer phone for follow-up
     */
    @JsonProperty("LessonsFollowupPhone")
    private String lessonsFollowupPhone;

    /**
     * Customer ZIP code for follow-up
     */
    @JsonProperty("LessonsFollowupZipCode")
    private String lessonsFollowupZipCode;

    /**
     * Additional comments or notes
     */
    @JsonProperty("Comments")
    private String comments;

    /**
     * Agent information (if applicable)
     */
    @JsonProperty("Agent")
    private AgentDTO agent;

    /**
     * Source system identifier (typically "GCSS")
     */
    @JsonProperty("Source")
    private String source;

    /**
     * Last updated timestamp in MM-dd-yyyy hh:mm a format
     */
    @JsonProperty("updated:")
    private String updated;

    /**
     * Internal appointment ID
     */
    @JsonProperty("AppointmentId")
    private String appointmentId;

    /**
     * POS Customer ID (external customer identifier)
     */
    @JsonProperty("POSCustomerId")
    private String customerId;

    /**
     * Test field for testing purposes
     */
    @JsonProperty("SFCustomerID")
    private String sfCustomerId1;




    /**
     * Default constructor
     */
    public LessonScheduledDTO() {
    }

    /**
     * Constructor with essential fields for lesson creation
     *
     * @param lessonsType Type of lesson (Trial/Subscription)
     * @param event Event type (Scheduled/ReScheduled/Cancelled)
     * @param sfCustomerId Salesforce Customer ID
     * @param appointmentId Internal appointment ID
     */
    public LessonScheduledDTO(String lessonsType, String event, String sfCustomerId, String appointmentId) {
        this.lessonsType = lessonsType;
        this.event = event;
        this.sfCustomerId1 = sfCustomerId1;
        this.appointmentId = appointmentId;
        this.source = "GCSS"; // Default source
        this.lessonsFollowup = "Y"; // Default follow-up
    }

    // ==================== GETTERS AND SETTERS ====================

    public String getLessonsType() {
        return lessonsType;
    }

    public void setLessonsType(String lessonsType) {
        this.lessonsType = lessonsType;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }



    public String getLessonsRequestedDate() {
        return lessonsRequestedDate;
    }

    public void setLessonsRequestedDate(String lessonsRequestedDate) {
        this.lessonsRequestedDate = lessonsRequestedDate;
    }

    public String getLessonsRequestedTime() {
        return lessonsRequestedTime;
    }

    public void setLessonsRequestedTime(String lessonsRequestedTime) {
        this.lessonsRequestedTime = lessonsRequestedTime;
    }

    public String getInstrument() {
        return instrument;
    }

    public void setInstrument(String instrument) {
        this.instrument = instrument;
    }

    public String getStoreNumber() {
        return storeNumber;
    }

    public void setStoreNumber(String storeNumber) {
        this.storeNumber = storeNumber;
    }

    public String getStoreStreet() {
        return storeStreet;
    }

    public void setStoreStreet(String storeStreet) {
        this.storeStreet = storeStreet;
    }

    public String getStoreCity() {
        return storeCity;
    }

    public void setStoreCity(String storeCity) {
        this.storeCity = storeCity;
    }

    public String getStoreState() {
        return storeState;
    }

    public void setStoreState(String storeState) {
        this.storeState = storeState;
    }

    public String getStoreZip() {
        return storeZip;
    }

    public void setStoreZip(String storeZip) {
        this.storeZip = storeZip;
    }

    public String getLessonsDuration() {
        return lessonsDuration;
    }

    public void setLessonsDuration(String lessonsDuration) {
        this.lessonsDuration = lessonsDuration;
    }

    public String getLessonsFollowup() {
        return lessonsFollowup;
    }

    public void setLessonsFollowup(String lessonsFollowup) {
        this.lessonsFollowup = lessonsFollowup;
    }

    public String getLessonsFollowupName() {
        return lessonsFollowupName;
    }

    public void setLessonsFollowupName(String lessonsFollowupName) {
        this.lessonsFollowupName = lessonsFollowupName;
    }

    public String getLessonsFollowupEmail() {
        return lessonsFollowupEmail;
    }

    public void setLessonsFollowupEmail(String lessonsFollowupEmail) {
        this.lessonsFollowupEmail = lessonsFollowupEmail;
    }

    public String getLessonsFollowupPhone() {
        return lessonsFollowupPhone;
    }

    public void setLessonsFollowupPhone(String lessonsFollowupPhone) {
        this.lessonsFollowupPhone = lessonsFollowupPhone;
    }

    public String getLessonsFollowupZipCode() {
        return lessonsFollowupZipCode;
    }

    public void setLessonsFollowupZipCode(String lessonsFollowupZipCode) {
        this.lessonsFollowupZipCode = lessonsFollowupZipCode;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public AgentDTO getAgent() {
        return agent;
    }

    public void setAgent(AgentDTO agent) {
        this.agent = agent;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getUpdated() {
        return updated;
    }

    public void setUpdated(String updated) {
        this.updated = updated;
    }

    public String getAppointmentId() {
        return appointmentId;
    }

    public void setAppointmentId(String appointmentId) {
        this.appointmentId = appointmentId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getSfCustomerId1() {
        return sfCustomerId1;
    }
    public void setSfCustomerId1(String sfCustomerId1) {
        this.sfCustomerId1 = sfCustomerId1;
    }

    // ==================== UTILITY METHODS ====================

    /**
     * Validates that all required fields are populated
     *
     * @return true if all required fields are present, false otherwise
     */
    public boolean isValid() {
        return lessonsType != null && !lessonsType.trim().isEmpty() &&
               event != null && !event.trim().isEmpty() &&
               appointmentId != null && !appointmentId.trim().isEmpty() &&
               source != null && !source.trim().isEmpty();
    }

    /**
     * Checks if this is a trial lesson
     *
     * @return true if lesson type is "Trial"
     */
    public boolean isTrialLesson() {
        return "Trial".equalsIgnoreCase(lessonsType);
    }

    /**
     * Checks if this is a subscription lesson
     *
     * @return true if lesson type is "Subscription"
     */
    public boolean isSubscriptionLesson() {
        return "Subscription".equalsIgnoreCase(lessonsType);
    }

    /**
     * Checks if this is a scheduled event
     *
     * @return true if event is "Scheduled"
     */
    public boolean isScheduledEvent() {
        return "Scheduled".equalsIgnoreCase(event);
    }

    /**
     * Checks if this is a rescheduled event
     *
     * @return true if event is "ReScheduled"
     */
    public boolean isRescheduledEvent() {
        return "ReScheduled".equalsIgnoreCase(event);
    }

    /**
     * Checks if this is a cancelled event
     *
     * @return true if event is "Cancelled"
     */
    public boolean isCancelledEvent() {
        return "Cancelled".equalsIgnoreCase(event);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        LessonScheduledDTO that = (LessonScheduledDTO) o;
        return Objects.equals(appointmentId, that.appointmentId) &&
               Objects.equals(customerId, that.customerId) &&
               Objects.equals(lessonsType, that.lessonsType) &&
               Objects.equals(event, that.event);
    }

    @Override
    public int hashCode() {
        return Objects.hash(appointmentId, customerId, lessonsType, event);
    }

    @Override
    public String toString() {
        return "LessonScheduledDTO{" +
                "lessonsType='" + lessonsType + '\'' +
                ", event='" + event + '\'' +
                ", sfCustomerId1='" + sfCustomerId1 + '\'' +
                ", lessonsRequestedDate='" + lessonsRequestedDate + '\'' +
                ", lessonsRequestedTime='" + lessonsRequestedTime + '\'' +
                ", instrument='" + instrument + '\'' +
                ", storeNumber='" + storeNumber + '\'' +
                ", storeCity='" + storeCity + '\'' +
                ", storeState='" + storeState + '\'' +
                ", appointmentId='" + appointmentId + '\'' +
                ", customerId='" + customerId + '\'' +
                ", source='" + source + '\'' +
                ", updated='" + updated + '\'' +
                '}';
    }

    /**
     * Nested DTO class for Agent information.
     * Represents the agent/employee who handled the lesson booking.
     */
    public static class AgentDTO {

        /**
         * Agent unique identifier
         */
        @JsonProperty("Id")
        private String id;

        /**
         * Agent first name
         */
        @JsonProperty("FirstName")
        private String firstName;

        /**
         * Agent last name
         */
        @JsonProperty("LastName")
        private String lastName;

        /**
         * Default constructor
         */
        public AgentDTO() {
        }

        /**
         * Constructor with all fields
         *
         * @param id Agent ID
         * @param firstName Agent first name
         * @param lastName Agent last name
         */
        public AgentDTO(String id, String firstName, String lastName) {
            this.id = id;
            this.firstName = firstName;
            this.lastName = lastName;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getFirstName() {
            return firstName;
        }

        public void setFirstName(String firstName) {
            this.firstName = firstName;
        }

        public String getLastName() {
            return lastName;
        }

        public void setLastName(String lastName) {
            this.lastName = lastName;
        }

        /**
         * Gets the full name of the agent
         *
         * @return Full name (firstName + lastName) or empty string if both are null
         */
        public String getFullName() {
            if (firstName == null && lastName == null) {
                return "";
            }
            return (firstName != null ? firstName : "") + " " + (lastName != null ? lastName : "");
        }

        /**
         * Validates that the agent has required information
         *
         * @return true if agent has at least an ID
         */
        public boolean isValid() {
            return id != null && !id.trim().isEmpty();
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            AgentDTO agentDTO = (AgentDTO) o;
            return Objects.equals(id, agentDTO.id);
        }

        @Override
        public int hashCode() {
            return Objects.hash(id);
        }

        @Override
        public String toString() {
            return "AgentDTO{" +
                    "id='" + id + '\'' +
                    ", firstName='" + firstName + '\'' +
                    ", lastName='" + lastName + '\'' +
                    '}';
        }
    }
}
