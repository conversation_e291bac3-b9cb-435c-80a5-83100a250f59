package com.guitarcenter.scheduler.webservice;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.AvailabilityDAO;
import com.guitarcenter.scheduler.dao.RoomDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ServiceLogger;
import com.guitarcenter.scheduler.model.enums.IsRecurring;
import com.guitarcenter.scheduler.service.AppointmentEmailService;
import com.guitarcenter.scheduler.service.AppointmentService;
import com.guitarcenter.scheduler.service.AvailabilityService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.RoomService;
import com.guitarcenter.scheduler.service.ValidationService;
import com.guitarcenter.scheduler.webservice.dto.LessonCancelServiceDTO;
import com.guitarcenter.scheduler.webservice.dto.LessonCancelServiceResponseDTO;
import com.guitarcenter.scheduler.webservice.util.WebServiceUtil;


@RestController
public class LessonCancelService {
	@Autowired
	private AppointmentDAO appointmentDAO;

	@Resource
	private AvailabilityService availabilityService;

	@Autowired
	private InstructorService instructorService;

	@Autowired
	private RoomService roomService;

	@Autowired
	private AvailabilityDAO availabilityDAO;


	private static final Logger		LOG					        = LoggerFactory.getLogger("schedulerlog");
	

	@Autowired
	private ValidationService validationService;

	@Autowired
	private AppointmentService appointmentService;

	@Autowired
	private RoomDAO roomDAO;

	@Autowired
	private AppointmentEmailService appointmentEmailService;
	
	

	@RequestMapping(value = "/LessonCancelService", method = RequestMethod.POST, headers = { "content-type=application/x-www-form-urlencoded" })
	public LessonCancelServiceResponseDTO getActivityCode(
			LessonCancelServiceDTO lessonCancelServiceDTO) throws JsonProcessingException {
		
		LessonCancelServiceResponseDTO response = new LessonCancelServiceResponseDTO();
		
		long cancelReasonCode =0;

		if (lessonCancelServiceDTO.getAptId() != null
				&& lessonCancelServiceDTO.getAptId().trim().length() > 0
				&& lessonCancelServiceDTO.getCustMemberID() != null
				&& lessonCancelServiceDTO.getCustMemberID().trim().length() > 0
				&& lessonCancelServiceDTO.getDate() != null
				&& lessonCancelServiceDTO.getDate().trim().length() > 0
				&& lessonCancelServiceDTO.getDay() != null
				&& lessonCancelServiceDTO.getDay().trim().length() > 0
				&& lessonCancelServiceDTO.getEndTime() != null
				&& lessonCancelServiceDTO.getEndTime().trim().length() > 0
				&& lessonCancelServiceDTO.getStartTime() != null
				&& lessonCancelServiceDTO.getStartTime().trim().length() > 0) {
			String cancelType = "0", custMemberID = null, timeZone=null,sCurStartDateTime=null;
			boolean isCancelled = false;

			Map<String, String> check = new HashMap<String, String>();

			check = checkExternalRecordInDB(lessonCancelServiceDTO);
			if (check.get("status").equalsIgnoreCase("true")) {
				custMemberID = check.get("custMemberID");
			} else {
				response.setErrorKey("UnexpectedError");
				response.setErrorMessage("Unexpected error occured.");
				response.setStatus("error");
				return response;
			}
			//GSSP-208 changes
			ServiceLogger serviceLogger = new ServiceLogger();
			   String successString;
			serviceLogger.setServiceName("LessonCancelService");
			serviceLogger.setCustomerMemberId(lessonCancelServiceDTO.getCustMemberID());
			serviceLogger.setAppointmentId(lessonCancelServiceDTO.getAptId());
			Criterion<Appointment,String>  appointmentCriterion =
				    AppointmentCriterion.logService(serviceLogger);
		  
		  successString = appointmentDAO.get(appointmentCriterion);
			  
			// Logic for getting Appointment object using AppointmentID
			Appointment dbapmnt = null;
			Criterion<Appointment, Appointment> criterion = AppointmentCriterion
					.getByAppointmentId(new Long(lessonCancelServiceDTO
							.getAptId()));

			dbapmnt = appointmentDAO.get(criterion);
			
			Criterion<Appointment, String> tzCriterion = AppointmentCriterion
					.findTimeZoneByAptID(lessonCancelServiceDTO
							.getAptId());
			
			timeZone=appointmentDAO.get(tzCriterion);
			
			
				sCurStartDateTime= WebServiceUtil.getCurDateForTZ(timeZone) + " " +  
						WebServiceUtil.getCurTmForTZ(timeZone);
			
				 
			if (!isTodaysAppointment(dbapmnt.getStartTimeStr(), sCurStartDateTime)) {
				try {
					Long personID = getPersonID(custMemberID);
					Person person = new Person();
					person.setPersonId(personID);
					try{
//250 Changes
					 
					
					 
					 
					 //Changes made for GSSP-270
					 if(! StringUtils.isEmpty(lessonCancelServiceDTO.getCancelReasonCode())){
						 cancelReasonCode = new Long(lessonCancelServiceDTO.getCancelReasonCode());
					 }
					 else
					 {
						 cancelReasonCode = 0;
					 }
					 Appointment activityinfo=appointmentService.getAppointment(Long.parseLong(lessonCancelServiceDTO.getAptId()));
					 String activityId=activityinfo.getActivity().getActivityId().toString();
						long serviceId=activityinfo.getActivity().getService().getServiceId();
						
					isCancelled = appointmentService.cancelAppointment(Long.parseLong(lessonCancelServiceDTO.getAptId()),AppConstants.SINCLE_CANCEL, cancelReasonCode, person,activityId, serviceId);
					 
					}
					catch(Exception e){
						LOG.error("Error while Cancelling the appointment ");
						response.setErrorKey("UnexpectedError");
						response.setErrorMessage("Unexpected error occured.");
						response.setStatus("error");						
						return response;
					}
					//GSSP-278 added cancel reason code, Activity type and the Roomname.
					String cancelReasonDesc=null;
					String Room= null;
					// Existing it self we have instructor name here
					Instructor instructor = instructorService.getInstructor(dbapmnt.getInstructor().getInstructorId());	
					
					String instructorName =( org.apache.commons.lang.StringUtils.isBlank(instructor.getPerson().getFirstName())?
							"":instructor.getPerson().getFirstName()) + " " 									
							+ (org.apache.commons.lang.StringUtils.isBlank(instructor.getPerson().getLastName())?"":
								instructor.getPerson().getLastName());
									
					//Cancel Reason message will not dispaly for Rehearsal,Floor,Jumpstart in online lesson services.
					/*if(dbapmnt.getActivity().getActivityId()== 100 ||dbapmnt.getActivity().getActivityId()== 140 ){
						cancelReasonDesc= null;
					}
					else{
						List<AppointmentCancelReason> CancelCodeList= appointmentService.getCancelReasonCode(cancelReasonCode);
						 
						cancelReasonDesc=CancelCodeList.get(0).getCancelReason();
						
					}*/
					if (isCancelled) {
						if (IsRecurring.Y.equals(dbapmnt.getAppointmentSeries()
								.getIsRecurring()))
							cancelType = "1";
						try{
						 //changes for  GSSP 243
							//changes for  GSSP 253
							sendEmailForCancelAppt(lessonCancelServiceDTO.getCustMemberID(),dbapmnt.getActivity().getService().getServiceId(),
									lessonCancelServiceDTO, cancelType,instructorName,dbapmnt.getLocationProfile().getProfileId(),dbapmnt.getActivity().getActivityName(),Room);
						}catch(Exception e){
							LOG.error("Error while Sending email to the customers ");
							response.setErrorKey("UnexpectedError");
							response.setErrorMessage("Unexpected error occured.");
							response.setStatus("error");							
							return response;
						}
					}

				} catch (Exception e) {
					e.printStackTrace();

					LOG.error("Error while updating the record ");
				}
			} else {
				response.setErrorKey("AptInNextDay");
				response.setErrorMessage("This appointment cannot be cancelled as it falls within next 24 hours. Please contact the store.");
				response.setStatus("error");
				return response;
			}
			
		  
			response.setCustMemberID(lessonCancelServiceDTO.getCustMemberID());
			response.setDate(lessonCancelServiceDTO.getDate());
			response.setDay(lessonCancelServiceDTO.getDay());
			response.setStartTime(lessonCancelServiceDTO.getStartTime());
			response.setEndTime(lessonCancelServiceDTO.getEndTime());
			String cancelReasonId = Long.toString(cancelReasonCode);
			response.setCancelReasoncode(cancelReasonId);			
			response.setStatus("Successfully Cancelled");
			return response;
		} else {
			response.setErrorKey("UnexpectedError");
			response.setErrorMessage("Unexpected error occured.");
			response.setStatus("error");
			return response;
		}
		 
	}

	private boolean isTodaysAppointment(String date1,String date2) {

		boolean status = false;
		try {
			// CONVERTING DATES IN TO STRING
			DateTimeFormatter formatter = DateTimeFormat
					.forPattern(DateTimeUtil.DATETIME_PATTERN);
			DateTime dt1 = formatter.parseDateTime(date1);
			DateTime dt2 = formatter.parseDateTime(date2);
			Duration duration = new Duration(dt2, dt1);

			if (duration.getStandardMinutes() <= 1440) {

				status = true;
			}
		} catch (Exception e) {
			LOG.error("Error while date parsing ");
		}
		return status;
	}

	/**
	 * @param customerMemberID
	 * @return Long personID
	 */

	private Long getPersonID(String customerMemberID) {

		Long personID;

		// String customerExternalIds[]= customerMemberID.split("_");

		Criterion<Appointment, Long> customerCriterion = AppointmentCriterion
				.findPersonIDByExternalId(customerMemberID);

		personID = appointmentDAO.get(customerCriterion,
				DAOHelper.FETCH_APPOINTMENT_SERIES);

		return personID;
	}

	/**
	 * @param customerMemberID
	 *            ,serviceId,updateLessonServiceDTO
	 * @param dbapmnt 
	 * @return Boolean
	 *///GSSP-278 code changes
	private boolean sendEmailForCancelAppt(String customerMemberID,
			Long serviceId, LessonCancelServiceDTO lessonCancelServiceDTO,
			String cancelType,String instructorName,long profileID,String activityName,String roomName) {
		final String serviceType="CANCEL";
		String customerExternalIds[] = customerMemberID.split("_");

		//GSSP-253 profile id  parameter passed.
		
		List<Map<String, Object>> emailBodies = new ArrayList<Map<String, Object>>();
		Criterion<Appointment, List<Map<String, Object>>> customerCriterion = AppointmentCriterion
				.getEmailIdsByCustomersExternalId(customerExternalIds,lessonCancelServiceDTO.getCurCustName(), profileID);
		emailBodies = appointmentDAO.get(customerCriterion,
				DAOHelper.FETCH_APPOINTMENT_SERIES);
		//Introduce a dto object of the type CretaeAppointmnetDto
		
		//GSSP-253 profile id  parameter passed.
		//GSSP-278 code changes
		appointmentEmailService.sendEmailForModifySingleAppt(
				lessonCancelServiceDTO.getDate(),lessonCancelServiceDTO.getStartTime(),lessonCancelServiceDTO.getEndTime(),emailBodies, serviceId, cancelType,instructorName,serviceType,lessonCancelServiceDTO.getCurCustName(),profileID,activityName,roomName);

		return false;

	}

	public Map<String, String> checkExternalRecordInDB(
			LessonCancelServiceDTO lessonCancelServiceDTO) {

		// boolean status = false;
		Map<String, String> check = new HashMap<String, String>();
		check.put("status", "false");
		String customerExternalIds[] = lessonCancelServiceDTO.getCustMemberID()
				.split("_");

		for (int i = 0; i < customerExternalIds.length; i++) {

			Criterion<Appointment, Long> customerCriterion = AppointmentCriterion
					.checkCustomerExternalIdDBRecord(customerExternalIds[i]);
			Long checkExternal = appointmentDAO.get(customerCriterion,
					DAOHelper.FETCH_APPOINTMENT_SERIES);
			if (checkExternal == 1) {
				check.put("status", "true");
				check.put("custMemberID", customerExternalIds[i]);
				return check;
			}
		}
		return check;
	}

}
