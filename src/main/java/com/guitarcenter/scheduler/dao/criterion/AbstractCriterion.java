package com.guitarcenter.scheduler.dao.criterion;

import java.util.List;

import org.hibernate.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class AbstractCriterion<T, E> implements Criterion<T, E> {

	private static final Logger	LOGGER	= LoggerFactory.getLogger(AbstractCriterion.class);
	private final Class<T>		mTClass;



	AbstractCriterion(final Class<T> pTClass) {
		this.mTClass = pTClass;
	}



	protected Class<T> getTClass() {
		return this.mTClass;
	}



	public static <T> Criterion<T, T> getAbstractInstance(final Class<T> pTClass) {
		Criterion<T, T> instance = new AbstractCriterion<T, T>(pTClass) {

			@Override
			public List<T> search(Session pSession, int pFetchMode) {
				throw new UnsupportedOperationException("current criteria object unsupport this operation.");
			}



			@Override
			protected String getFetchScript(int pFetchMode) {
				return "";
			}



			@Override
			public T get(Session pSession, int pFetchMode) {
				throw new UnsupportedOperationException("current criteria object unsupport this operation.");
			}

		};
		return instance;
	}



	@Override
	public List<E> search(Session pSession) {
		return search(pSession, 0);
	}



	@Override
	public List<T> searchAll(Session pSession) {
		return searchAll(pSession, 0);
	}



	@Override
	public E get(Session pSession) {
		return get(pSession, 0);
	}



	@Override
	@SuppressWarnings("unchecked")
	public List<T> searchAll(Session pSession, int pFetchMode) {
		StringBuilder sb = new StringBuilder("from " + getTClass().getName() + " t ");
		sb.append(getFetchScript(pFetchMode));
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { sb };
			LOGGER.debug("AbstractCriterion.searchAll : execute HQL is [{}]", objects);
		}
		return pSession.createQuery(sb.toString()).list();
	}



	protected abstract String getFetchScript(int pFetchMode);

}
