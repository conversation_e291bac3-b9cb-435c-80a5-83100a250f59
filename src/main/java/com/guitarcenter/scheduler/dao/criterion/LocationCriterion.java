package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION_PROFILE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.List;

import org.hibernate.Query;
import org.hibernate.Session;

import com.guitarcenter.scheduler.model.Location;

public abstract class LocationCriterion<E> extends AbstractCriterion<Location, E> implements Criterion<Location, E> {

	private static final Criterion<Location, Location>	DEFAULT_INSTANCE	= new LocationCriterion<Location>() {
																			};



	private LocationCriterion() {
		super(Location.class);
	}



	@Override
	protected String getFetchScript(int pFetchMode) {
		StringBuilder sb = new StringBuilder();
		sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
		sb.append(addFetchHQL(FETCH_LOCATION_PROFILE, pFetchMode, "t.locationProfile", true));
		sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
		return sb.toString();
	}



	@Override
	public List<E> search(Session pSession, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	@Override
	public E get(Session pSession, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	public static Criterion<Location, Location> getInstance() {
		return DEFAULT_INSTANCE;
	}



	public static Criterion<Location, Location> findByLocationName(final String pLocationName) {
		LocationCriterion<Location> instance = new LocationCriterion<Location>() {

			@Override
			@SuppressWarnings("unchecked")
			public List<Location> search(Session pSession, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from Location t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append("where lower(t.locationName) like :locationName");
				Query query = pSession.createQuery(sb.toString());
				query.setString("locationName", pLocationName.toLowerCase() + "%");
				return query.list();
			}

		};
		return instance;
	}



	public static Criterion<Location, Location> getByProfileId(final long pProfileId) {
		LocationCriterion<Location> instance = new LocationCriterion<Location>() {

			@Override
			public Location get(Session pSession, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from Location t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append("where t.locationProfile.profileId = :profileId");
				Query query = pSession.createQuery(sb.toString());
				query.setLong("profileId", pProfileId);
				query.setMaxResults(1);
				return (Location) query.uniqueResult();
			}

		};
		return instance;
	}

    /**
     * Returns a Criterion that can be used to find any existing locations in
     * the supplied site with a matching external id.
     * 
     * @param pSiteId site identifier
     * @param pExternalId String containing the external id to match
     * @return Criterion instance
     */
    public static Criterion<Location, Location> findByExternalId(final long pSiteId, final String pExternalId) {
        return new LocationCriterion<Location>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Location> search(Session pSession, int pFetchMode) {
                Query query = pSession.createQuery(new StringBuilder(" from Location t ")
                                                       .append(getFetchScript(pFetchMode))
                                                       .append(" where t.site.siteId = :siteId ")
                                                       .append("   and t.externalId = :externalId ")
                                                       .toString());
                query.setLong("siteId", pSiteId);
                query.setString("externalId", pExternalId);
                return query.list();
            }
        };
    }

    /**
     * Criterion to return all locations that are associated with the site
     * provided.
     * 
     * @param siteId the Site identifier to use for search
     * @return Criterion
     */
    public static Criterion<Location, Location> findBySiteId(final long siteId) {
        return new LocationCriterion<Location>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Location> search(Session pSession, int pFetchMode) {
                Query query = pSession.createQuery(new StringBuilder(" from Location t ")
                                                       .append(getFetchScript(pFetchMode))
                                                       .append(" where t.site.siteId = :siteId ")
                                                       .append(" order by t.externalId asc")
                                                       .toString());
                query.setLong("siteId", siteId);
                return query.list();
            }
        };
    }
}
