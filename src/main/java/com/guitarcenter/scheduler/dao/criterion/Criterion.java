package com.guitarcenter.scheduler.dao.criterion;

import java.util.List;

import org.hibernate.Session;

public interface Criterion<T, E> {

	List<E> search(Session pSession);



	List<E> search(Session pSession, int pFetchMode);



	List<T> searchAll(Session pSession);



	List<T> searchAll(Session pSession, int pFetchMode);



	E get(Session pSession);



	E get(Session pSession, int pFetchMode);

}
