package com.guitarcenter.scheduler.dao.criterion;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.criterion.dto.*;
import com.guitarcenter.scheduler.dao.util.PGPUtils;
import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.dto.CancelledAppointmentDateListDTO;
import com.guitarcenter.scheduler.dto.CancelledApptDailyDTO;
import com.guitarcenter.scheduler.dto.CustomerAppointmentDetailsDTO;
import com.guitarcenter.scheduler.dto.CustomerSearchPageDTO;
import com.guitarcenter.scheduler.dto.EmployeeScheduleDTO;
import com.guitarcenter.scheduler.dto.LessonCompletionDTO;
import com.guitarcenter.scheduler.model.*;
import com.guitarcenter.scheduler.model.dto.InstructorAppointmentBusinessHours;
import com.guitarcenter.scheduler.model.enums.Canceled;
import com.guitarcenter.scheduler.model.enums.RequiresInstructor;
import com.guitarcenter.scheduler.webservice.dto.AppointmentStatusDTO;
import com.guitarcenter.scheduler.webservice.dto.CustomerAppointmentDetailsResultDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceResponseDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAvailableHoursDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorServiceResponseDTO;
import com.guitarcenter.scheduler.webservice.dto.Lesson;
import com.guitarcenter.scheduler.webservice.dto.LessonSeries;
import com.guitarcenter.scheduler.webservice.dto.LocationProfileInfoDTO;
import org.apache.commons.lang.StringUtils;
import org.hibernate.*;
import org.hibernate.type.StandardBasicTypes;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.text.ParseException;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.*;

public abstract class AppointmentCriterion<E> extends AbstractCriterion<Appointment, E> implements
        Criterion<Appointment, E> {

    private static final Logger		LOG					= LoggerFactory.getLogger(AppointmentCriterion.class);

    private static final Criterion<Appointment, Appointment> DEFAULT_INSTANCE = new AppointmentCriterion<Appointment>() {
    };

    //Added for Client Facing project
    //279 GSSP-  edit disabled for jumpstart  activity Id in the webservices.
    //LES-631 Activity changed from Jump Start to Trial Lesson and Online Trial Lesson 

    
    private static final String[] editDisabled = {"In-Store Group Class", "In-Store Summer Camp","In-Store Rockshow", "In-Store Open Office", "In-Store Home Recording","In-Store Sparring Partner","In-Store Trial Lesson","Online Trial Lesson"};
    private static final String EMAIL_REGEX ="^(.+)@(.+)$";

    private AppointmentCriterion() {
        super(Appointment.class);
    }

    public static Criterion<Appointment, Appointment> getInstance() {
        return DEFAULT_INSTANCE;
    }

    public static Criterion<Appointment, Appointment> find(long pSiteId, long pProfileId, Date pStartTime,
                                                           Date pEndTime, Long[] pServiceTypeIds, Long[] pInstructorIds, Long[] pRoomTypeIds, Long[] pActivityIds) {
        final long siteId = pSiteId;
        final long profileId = pProfileId;
        //Added for GSSP-282 Changes:: Dataformate chagnes for dayview load
        final Date dStartTime = new Date(pStartTime.getTime());
        final Date dEndTime = new Date(pEndTime.getTime());

        DateFormat outputFormatter = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH);

        DateFormat outputFormattermonth = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_HIPHEN_MONTH);

        String startTime = outputFormattermonth.format(dStartTime);
        String endTime = outputFormattermonth.format(dEndTime);

        String comparStartDate = outputFormatter.format(pStartTime);
        String comparEndDate = outputFormatter.format(pEndTime);


        final Long[] serviceTypeIds = (pServiceTypeIds == null || pServiceTypeIds.length == 0) ? null : Arrays.copyOf(
                pServiceTypeIds, pServiceTypeIds.length);
        final Long[] instructorIds = (pInstructorIds == null || pInstructorIds.length == 0) ? null : Arrays.copyOf(
                pInstructorIds, pInstructorIds.length);
        final Long[] roomTypeIds = (pRoomTypeIds == null || pRoomTypeIds.length == 0) ? null : Arrays.copyOf(
                pRoomTypeIds, pRoomTypeIds.length);
        final Long[] activityIds = (pActivityIds == null || pActivityIds.length == 0) ? null : Arrays.copyOf(
                pActivityIds, pActivityIds.length);

        return new AppointmentCriterion<Appointment>() {

            private boolean allowNullInstructor = false;
            private boolean rejectNullInstructor = false;

            @Override
            @SuppressWarnings("unchecked")
            public List<Appointment> search(Session pSession, int pFetchMode) {

                initializeRequireInstructorOption(pSession);

                if (checkCondition()) return Collections.EMPTY_LIST;

                /**
                 * For gcss-574,add 'AppointmentSeries' object fetch in query,use the attribute 'isRecurring' of it to identify recurring appointment
                 */
                StringBuilder sb = new StringBuilder("from Appointment t ");
                sb.append(" left join fetch t.appointmentSeries apse ");
                sb.append(" left join fetch t.activity a");
                sb.append(" left join fetch a.service s");
                sb.append(" left join fetch t.customers c ");
                sb.append(" left join fetch c.person ");
                sb.append(" left join fetch t.instructor i");
                sb.append(" left join fetch t.room r ");

                if(comparStartDate.equals(comparEndDate))
                {
                    sb.append("where t.site.siteId = :siteId and t.locationProfile.profileId = :profileId and trunc(t.startTime) = :startTime and trunc(t.endTime) = :endTime");
                }
                else
                {
                    sb.append("where t.site.siteId = :siteId and t.locationProfile.profileId = :profileId and trunc(t.startTime) >= :startTime and trunc(t.endTime) <= :endTime");
                }

                if (null != serviceTypeIds) {
                    sb.append(" and s.serviceId in (:serviceTypeIds) ");
                }
                if (null != roomTypeIds) {
                    sb.append(" and r.roomId in (:roomTypeIds)");
                }
                if (null != activityIds) {
                    sb.append(" and a.activityId in (:activityIds)");
                }
                if (rejectNullInstructor && allowNullInstructor && null != instructorIds) {
                    sb.append(" and (i.instructorId in (:instructorIds) or i.instructorId is null)");
                } else {
                    if (rejectNullInstructor && null != instructorIds) {
                        sb.append(" and i.instructorId in (:instructorIds)");
                    } else if (allowNullInstructor) {
                        sb.append(" and i.instructorId is null");
                    }
                }
                sb.append(" and (t.canceled = :canceled or t.canceled is null or t.canceled = 'H' )");
                sb.append(" order by t.appointmentId ");
                Query query = pSession.createQuery(sb.toString());
                query.setLong("siteId", siteId);
                query.setLong("profileId", profileId);
                query.setString("startTime", startTime);
                query.setString("endTime", endTime);
                query.setString("canceled", Canceled.N.toString());
                if (null != serviceTypeIds) {
                    query.setParameterList("serviceTypeIds", serviceTypeIds);
                }
                if (null != roomTypeIds) {
                    query.setParameterList("roomTypeIds", roomTypeIds);
                }
                if (null != activityIds) {
                    query.setParameterList("activityIds", activityIds);
                }
                if (null != instructorIds && rejectNullInstructor) {
                    query.setParameterList("instructorIds", instructorIds);
                }
                List<Appointment> list = query.list();
                Set<Long> ids = new HashSet<Long>();
                List<Appointment> result = new ArrayList<Appointment>();
                for (Appointment appt : list) {
                    Long id = appt.getAppointmentId();
                    if (!ids.contains(id)) {
                        ids.add(id);
                        result.add(appt);
                    }
                }
                return result;
            }

            private void initializeRequireInstructorOption(Session pSession) {
                if (null != activityIds) {
                    SQLQuery query = pSession
                            .createSQLQuery("select t.requires_instructor from activity t where t.activity_id in (:activityIds) group by t.requires_instructor");
                    query.setParameterList("activityIds", activityIds);
                    @SuppressWarnings("unchecked")
                    List<Character> list = query.list();
                    for (Character character : list) {
                        if (RequiresInstructor.N.name().equals(character.toString())) {
                            allowNullInstructor = true;
                        } else if (RequiresInstructor.R.name().equals(character.toString())) {
                            rejectNullInstructor = true;
                        } else if (RequiresInstructor.O.name().equals(character.toString())) {
                            allowNullInstructor = true;
                            rejectNullInstructor = true;
                        }
                    }
                }
            }

            private boolean checkCondition() {
                boolean roomAndActivityIsNull = (null == roomTypeIds || null == activityIds);
                return (rejectNullInstructor && roomAndActivityIsNull)
                        || (!allowNullInstructor && rejectNullInstructor && (null == instructorIds || roomAndActivityIsNull));
            }
        };
    }

    public static Criterion<Appointment, Appointment> findByCustomerAndDateTime(final long pCustomerId,
                                                                                final Date pStartTime, final Date pEndTime, final long pProfileId) {

        return new AppointmentCriterion<Appointment>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Appointment> search(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select {t.*},{a.*},{i.*},{r.*},{aps.*} from appointment t");
                sb.append("  left join appointment_customers a_c on t.appointment_id = a_c.appointment_id");
                sb.append("  left join customer c on a_c.customer_id = c.customer_id");
                sb.append("  left join person p on c.person_id = p.person_id");
                sb.append("  left join activity a on t.activity_id = a.activity_id");
                sb.append("  left join service s on a.service_id = s.service_id");
                sb.append("  left join instructor i on t.instructor_id = i.instructor_id");
                sb.append("  left join room r on t.room_id = r.room_id");

                /**
                 * For GCSS-544,add join to fetch AppointmentSeries
                 */
                sb.append("  left join appointment_series aps on t.appointment_series_id=aps.appointment_series_id");

                sb.append("  left join profile_service p_s on s.service_id = p_s.service_id");
                sb.append("  left join profile_activity p_a on a.activity_id = p_a.activity_id");
                sb.append(" where c.customer_id = :customerId");
                sb.append("   and to_date(to_char(t.start_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') >= :startTime");
                sb.append("   and to_date(to_char(t.end_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') <= :endTime");
                sb.append("   and t.profile_Id = :profileId");
                sb.append("   and (t.canceled = :canceled1 or t.canceled = :canceled or t.canceled is null)");
                sb.append("   and p_s.enabled = 'Y'");
                sb.append("   and p_a.enabled = 'Y'");
                sb.append("   and (i.enabled = 'Y' or i.enabled is null)");
                sb.append("   and r.enabled = 'Y'");
                SQLQuery query = pSession.createSQLQuery(sb.toString());
                query.addEntity("t", Appointment.class);
                query.addJoin("a", "t.activity");
                query.addJoin("i", "t.instructor");
                query.addJoin("r", "t.room");

                /**
                 * For GCSS-544,add join to fetch AppointmentSeries
                 */
                query.addJoin("aps", "t.appointmentSeries");

                query.setLong("customerId", pCustomerId);
                query.setDate("startTime", pStartTime);
                query.setDate("endTime", pEndTime);
                query.setLong("profileId", pProfileId);
                query.setString("canceled", Canceled.N.toString());
                query.setString("canceled1", Canceled.H.toString());
                List<Object[]> list = query.list();
                Map<Long, Appointment> maps = new HashMap<Long, Appointment>();
                if (list != null) {
                    for (Object[] object : list) {
                        Appointment app = ((Appointment) object[0]);
                        Long id = app.getAppointmentId();
                        if (maps.get(id) == null) {
                            maps.put(id, app);
                            Hibernate.initialize(app.getCustomers());
                            for (Customer customer : app.getCustomers()) {
                                Hibernate.initialize(customer.getPerson());
                            }
                        }
                    }
                }
                List<Appointment> result = new ArrayList<Appointment>();
                if (!maps.isEmpty()) {
                    result.addAll(maps.values());
                }
                return result;
            }

        };
    }

    public static Criterion<Appointment, Appointment> getByAppointmentId(final long pAppointmentId) {

        return new AppointmentCriterion<Appointment>() {

            @Override
            public Appointment get(Session pSession, int pFetchMode) {

                StringBuilder sb = new StringBuilder("select t from Appointment t ");
                sb.append(" left join fetch t.activity a");
                sb.append(" left join fetch t.appointmentSeries");
                sb.append(" left join fetch t.instructor i");
                sb.append(" left join fetch t.room");
                sb.append(" left outer join fetch t.customers c");
                sb.append(" left join fetch a.service");
                sb.append(" left join fetch c.person");
                sb.append(" left join fetch i.person");
                sb.append(" where t.appointmentId = :appointmentId");
                Query query = pSession.createQuery(sb.toString());


                query.setLong("appointmentId", pAppointmentId);

                return (Appointment) query.uniqueResult();
            }

        };
    }

    public static Criterion<Appointment, Appointment> findApptsForMailReminder() {

        return new AppointmentCriterion<Appointment>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Appointment> search(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder(" from Appointment appt ");
                sb.append(" left join fetch appt.activity ac ");
                sb.append(" left join fetch appt.locationProfile lp ");
                sb.append(" left join fetch ac.service s");
                sb.append(" left join fetch appt.customers c");
                sb.append(" left join fetch c.person");
                sb.append(" where  s.serviceId = 0 and trunc(appt.startTime) = trunc(sysdate + 1) ");
                /**
                 * For gcss-589,don't send mail for cancelled appointments
                 */
                sb.append(" and (appt.canceled = 'N' or appt.canceled is null) ");

                Query query = pSession.createQuery(sb.toString());
                return query.list();
            }

        };
    }

    public static Criterion<Appointment, Appointment> getClosestScheduledAppByActivity(final long pActivityId) {

        return new AppointmentCriterion<Appointment>() {

            @Override
            public Appointment get(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder(" from Appointment a ");
                sb.append(" left join fetch a.instructor i ");
                sb.append(" left join fetch i.person ");
                sb.append(" where a.activity.activityId=:activityId ");
                sb.append(" order by a.appointmentId desc ");
                Query query = pSession.createQuery(sb.toString());
                query.setMaxResults(1);
                query.setLong("activityId", pActivityId);
                return (Appointment) query.uniqueResult();
            }

        };
    }

    public static Criterion<Appointment, Appointment> findByAppointmentSeriesAndStartTime(
            final long pAppointmentSeriesId, final Date pStartTime) {

        return new AppointmentCriterion<Appointment>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Appointment> search(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Appointment t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" where t.appointmentSeries.appointmentSeriesId = :appointmentSeriesId and t.startTime >= :startTime and (t.canceled='H' or t.canceled='N' or t.canceled is null) ");
                sb.append(" order by t.appointmentId ");
                Query query = pSession.createQuery(sb.toString());
                query.setLong("appointmentSeriesId", pAppointmentSeriesId);
                query.setDate("startTime", pStartTime);
                return query.list();
            }

        };
    }

    public static Criterion<Appointment, Appointment> findByAppointmentSeriesAndDateTime(final long pAppointmentSeriesId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<Appointment>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Appointment> search(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Appointment t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" where t.appointmentSeries.appointmentSeriesId = :appointmentSeriesId and t.startTime >= :startTime ");
                if (pEndTime != null) {
                    sb.append(" and t.endTime <= :endTime ");
                }
                sb.append(" order by t.appointmentId ");
                Query query = pSession.createQuery(sb.toString());
                query.setLong("appointmentSeriesId", pAppointmentSeriesId);
                query.setTimestamp("startTime", pStartTime);
                if (pEndTime != null) {
                    query.setTimestamp("endTime", pEndTime);
                }
                return query.list();
            }

        };
    }
    
    public static Criterion<Appointment, Appointment> findByActiveAppointmentSeriesAndDateTime(final long pAppointmentSeriesId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<Appointment>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Appointment> search(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Appointment t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" where t.appointmentSeries.appointmentSeriesId = :appointmentSeriesId and t.canceled != 'Y' and t.startTime >= :startTime ");
                if (pEndTime != null) {
                    sb.append(" and t.endTime <= :endTime ");
                }
                sb.append(" order by t.appointmentId ");
                Query query = pSession.createQuery(sb.toString());
                query.setLong("appointmentSeriesId", pAppointmentSeriesId);
                query.setTimestamp("startTime", pStartTime);
                if (pEndTime != null) {
                    query.setTimestamp("endTime", pEndTime);
                }
                return query.list();
            }

        };
    }
    
 
    public static Criterion<Appointment, Boolean> hasByProfileIdAndActivityId(final Long pProfileId,
                                                                              final Long pActivityId) {

        return new AppointmentCriterion<Boolean>() {

            @Override
            public Boolean get(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select count(t) from Appointment t ");
                sb.append(" where t.activity.activityId = :activityId ");
                sb.append(" and t.locationProfile.profileId = :profileId ");
                sb.append(" and (t.canceled = :canceled or t.canceled = :canceled1 )");
                sb.append(" and (t.startTime >= systimestamp or t.endTime >= systimestamp) ");
                Query query = pSession.createQuery(sb.toString());
                query.setMaxResults(1);
                query.setLong("activityId", pActivityId);
                query.setLong("profileId", pProfileId);
                query.setString("canceled", Canceled.N.toString());
                query.setString("canceled1", Canceled.H.toString());
                Long count = (Long) query.uniqueResult();
                return count > 0 ? Boolean.TRUE : Boolean.FALSE;
            }

        };

    }

    public static Criterion<Appointment, Boolean> hasBySiteIdAndServiceId(final Long pSiteId, final Long pServiceId) {

        return new AppointmentCriterion<Boolean>() {

            @Override
            public Boolean get(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select count(t) from Appointment t ");
                sb.append(" where t.activity.service.serviceId = :serviceId ");
                sb.append(" and t.site.siteId = :siteId ");
                sb.append(" and (t.canceled = :canceled or t.canceled = :canceled1)");
                sb.append(" and (t.startTime >= systimestamp or t.endTime >= systimestamp) ");
                Query query = pSession.createQuery(sb.toString());
                query.setMaxResults(1);
                query.setLong("serviceId", pServiceId);
                query.setLong("siteId", pSiteId);
                query.setString("canceled", Canceled.N.toString());
                query.setString("canceled1", Canceled.H.toString());
                Long count = (Long) query.uniqueResult();
                return count > 0 ? Boolean.TRUE : Boolean.FALSE;
            }

        };

    }

    public static Criterion<Appointment, Boolean> hasBySiteAndActivityId(final long pSiteId, final Long pActivityId) {

        return new AppointmentCriterion<Boolean>() {

            @Override
            public Boolean get(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select count(t) from Appointment t ");
                sb.append(" where t.activity.activityId = :activityId ");
                sb.append(" and t.site.siteId = :siteId");
                sb.append(" and (t.canceled = :canceled or t.canceled = :canceled1)");
                sb.append(" and (t.startTime >= systimestamp or t.endTime >= systimestamp) ");
                Query query = pSession.createQuery(sb.toString());
                query.setMaxResults(1);
                query.setLong("siteId", pSiteId);
                query.setLong("activityId", pActivityId);
                query.setString("canceled", Canceled.N.toString());
                query.setString("canceled1", Canceled.H.toString());
                Long count = (Long) query.uniqueResult();
                return count > 0 ? Boolean.TRUE : Boolean.FALSE;
            }

        };

    }

    public static Criterion<Appointment, Boolean> hasByInstructorId(final Long pInstructorId) {

        return new AppointmentCriterion<Boolean>() {

            @Override
            public Boolean get(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select count(t) from Appointment t ");
                sb.append(" where t.instructor = :instructorId ");
                sb.append(" and (t.canceled = :canceled or t.canceled is null or t.canceled = :canceled1)");
                sb.append(" and t.startTime > sysdate ");
                Query query = pSession.createQuery(sb.toString());
                query.setMaxResults(1);
                query.setLong("instructorId", pInstructorId);
                query.setString("canceled", Canceled.N.toString());
                query.setString("canceled1", Canceled.H.toString());
                Long count = (Long) query.uniqueResult();
                return count > 0 ? Boolean.TRUE : Boolean.FALSE;
            }

        };

    }


    public static Criterion<Appointment, InstructorDateListDTO> findInstructorScheduleByLocationIdAndDateTime(
            final long pLocationId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<InstructorDateListDTO>() {

            @Override
            public List<InstructorDateListDTO> search(Session pSession, int pFetchMode) {
                //Changes made in date1 date pattern for GSSP-200
                //Changes Made for ExternalId in the Query for Gssp-252
                String sb = "select to_char(t.start_time, 'dd-MON-yyyy') as date1 ,t.appointment_id as appointment_id, " +
                        " t.start_time as start_time, t.end_time as end_time, t.canceled as canceled," +
                        " (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration," +
                        " r.profile_room_name as profile_room_name, a.activity_name as activity_name," +
                        " i.instructor_id as instructor_id, p_i.first_name as i_first_name, p_i.last_name as i_last_name," +
                        " c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name, " +
                        " ay.monday_start_time as mon_s,ay.monday_end_time as mon_e," +
                        " ay.tuesday_start_time as tue_s,ay.tuesday_end_time as tue_e,ay.wednesday_start_time as wed_s,ay.wednesday_end_time as wed_e," +
                        " ay.thursday_start_time as thu_s,ay.thursday_end_time as thu_e, ay.friday_start_time as fri_s, ay.friday_end_time as fri_e," +
                        " ay.saturday_start_time as sat_s,ay.saturday_end_time as sat_e,ay.sunday_start_time as sun_s,ay.sunday_end_time as sun_e," +
                        " i.external_id as external_id" +
                        " from appointment t" +
                        " left join location l on t.profile_id = l.profile_id" +
                        " left join appointment_customers a_c on t.appointment_id = a_c.appointment_id" +
                        " left join customer c on a_c.customer_id = c.customer_id" +
                        " left join customer_status c_s on c.customer_status_id = c_s.customer_status_id" +
                        " left join person p_c on c.person_id = p_c.person_id" +
                        " left join instructor i on t.instructor_id = i.instructor_id" +
                        " left join person p_i on i.person_id = p_i.person_id" +
                        " left join room r on t.room_id = r.room_id" +
                        " left join activity a on t.activity_id = a.activity_id" +
                        " left join availability ay on i.availability_id = ay.availability_id" +
                        " where l.location_id = :locationId " +
                        "   and t.instructor_id is not null" +
                        "   and t.start_time >= :startTime " +
                        "   and t.end_time <= :endTime " +
                        "   and decode(c_s.external_id, null, '_', c_s.external_id) != 'C' " +
                        "   and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        " order by to_char(t.start_time, 'dd-MON-yyyy'), p_i.first_name, p_i.last_name, t.start_time, p_c.first_name, p_c.last_name";
                SQLQuery query = pSession.createSQLQuery(sb);
                query.setLong("locationId", pLocationId);
                query.setTimestamp("startTime", pStartTime);
                query.setTimestamp("endTime", pEndTime);
                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("appointment_id", StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("end_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("canceled", StandardBasicTypes.STRING);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.addScalar("profile_room_name", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("instructor_id", StandardBasicTypes.LONG);
                query.addScalar("i_first_name", StandardBasicTypes.STRING);
                query.addScalar("i_last_name", StandardBasicTypes.STRING);
                query.addScalar("customer_id", StandardBasicTypes.LONG);
                query.addScalar("c_first_name", StandardBasicTypes.STRING);
                query.addScalar("c_last_name", StandardBasicTypes.STRING);
                query.addScalar("mon_s", StandardBasicTypes.TIMESTAMP);
                query.addScalar("mon_e", StandardBasicTypes.TIMESTAMP);
                query.addScalar("tue_s", StandardBasicTypes.TIMESTAMP);
                query.addScalar("tue_e", StandardBasicTypes.TIMESTAMP);
                query.addScalar("wed_s", StandardBasicTypes.TIMESTAMP);
                query.addScalar("wed_e", StandardBasicTypes.TIMESTAMP);
                query.addScalar("thu_s", StandardBasicTypes.TIMESTAMP);
                query.addScalar("thu_e", StandardBasicTypes.TIMESTAMP);
                query.addScalar("fri_s", StandardBasicTypes.TIMESTAMP);
                query.addScalar("fri_e", StandardBasicTypes.TIMESTAMP);
                query.addScalar("sat_s", StandardBasicTypes.TIMESTAMP);
                query.addScalar("sat_e", StandardBasicTypes.TIMESTAMP);
                query.addScalar("sun_s", StandardBasicTypes.TIMESTAMP);
                query.addScalar("sun_e", StandardBasicTypes.TIMESTAMP);
                query.addScalar("external_id", StandardBasicTypes.STRING);
                ScrollableResults scroll = query.scroll();
                List<InstructorDateListDTO> result = new ArrayList<InstructorDateListDTO>();
                Map<InstructorDateDTO, List<InstructorScheduleDTO>> cache = new HashMap<InstructorDateDTO, List<InstructorScheduleDTO>>();
                Map<Long, InstructorScheduleDTO> instructorScheduleDTOs = new HashMap<Long, InstructorScheduleDTO>();
                Map<Long, Instructor> instructors = new HashMap<Long, Instructor>();
                Map<Long, Customer> customers = new HashMap<Long, Customer>();
                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    String date1 = (String) objects[0];
                    Long appointment_id = (Long) objects[1];
                    Long instructorId = (Long) objects[8];
                    String externalId = (String) objects[28];
                    InstructorDateDTO search = new InstructorDateDTO(date1, instructorId);
                    Instructor instructor = instructors.get(instructorId);
                    if (instructor == null) {
                        instructor = new Instructor();
                        instructor.setExternalId(externalId);
                        instructor.setInstructorId(instructorId);
                        Person person = new Person();
                        person.setFirstName((String) objects[9]);
                        person.setLastName((String) objects[10]);
                        instructor.setPerson(person);
                        instructors.put(instructorId, instructor);
                    }
                    if (!cache.containsKey(search)) {
                        List<InstructorScheduleDTO> list = new ArrayList<InstructorScheduleDTO>();
                        cache.put(search, list);
                        int dayOfWeek = new DateTime(objects[2]).getDayOfWeek();
                        Date startHours;
                        Date endHours;
                        switch (dayOfWeek) {
                            case 1:
                                startHours = (Date) objects[14];
                                endHours = (Date) objects[15];
                                break;
                            case 2:
                                startHours = (Date) objects[16];
                                endHours = (Date) objects[17];
                                break;
                            case 3:
                                startHours = (Date) objects[18];
                                endHours = (Date) objects[19];
                                break;
                            case 4:
                                startHours = (Date) objects[20];
                                endHours = (Date) objects[21];
                                break;
                            case 5:
                                startHours = (Date) objects[22];
                                endHours = (Date) objects[23];
                                break;
                            case 6:
                                startHours = (Date) objects[24];
                                endHours = (Date) objects[25];
                                break;
                            case 7:
                                startHours = (Date) objects[26];
                                endHours = (Date) objects[27];
                                break;
                            default:
                                startHours = null;
                                endHours = null;
                        }
                        result.add(new InstructorDateListDTO(date1, instructor, list, startHours, endHours, externalId));
                    }
                    List<InstructorScheduleDTO> instructorScheduleDTOList = cache.get(search);
                    InstructorScheduleDTO instructorScheduleDTO = instructorScheduleDTOs.get(appointment_id);
                    if (instructorScheduleDTO == null) {
                        instructorScheduleDTO = new InstructorScheduleDTO(appointment_id, (Date) objects[2], (Date) objects[3], (String) objects[4], (Long) objects[5], (String) objects[6], (String) objects[7], externalId);
                        instructorScheduleDTOList.add(instructorScheduleDTO);
                        instructorScheduleDTOs.put(appointment_id, instructorScheduleDTO);
                    }
                    if (instructorScheduleDTO.getInstructor() == null) {
                        instructorScheduleDTO.setInstructor(instructor);
                    }
                    Long customerId = (Long) objects[11];
                    if (customerId == null) continue;
                    Customer customer = customers.get(customerId);
                    if (customer == null) {
                        customer = new Customer();
                        customer.setCustomerId(customerId);
                        Person person = new Person();
                        person.setFirstName((String) objects[12]);
                        person.setLastName((String) objects[13]);
                        customer.setPerson(person);
                        customers.put(customerId, customer);
                    }
                    instructorScheduleDTO.addCustomer(customer);
                }
                scroll.close();
                /**
                 * For GCSS-592,Show instructor schedule in instructor schedule report
                 */
                for (InstructorDateListDTO instructorDateListDTO : result) {
                    List<InstructorScheduleDTO> list = instructorDateListDTO.getList();
                    Date startTime = list.get(0).getStartTime();
                    Date endTime = list.get(list.size() - 1).getEndTime();
                    instructorDateListDTO.setStartTimeAndEndTime(startTime, endTime);
                }
                return result;
            }
        };

    }

    public static Criterion<Appointment, ExportAppointmentDTO> searchAppointmentByLocationIdAndDateTime(
            final long pLocationId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<ExportAppointmentDTO>() {

            @Override
            public List<ExportAppointmentDTO> search(Session pSession, int pFetchMode) {
                String sb = "	select to_char(t.start_time, 'MM/DD/YYYY') as start_date, 	" +
                        "	TO_CHAR(t.start_time, 'd') start_day,	" +
                        "	      t.canceled as canceled,	" +
                        "	      a.activity_name as activity_name,	" +
                        "	     p_i.first_name || ' ' || p_i.last_name as instructor_name,	" +
                        "	      c.external_id customer_gc_id, c_s.external_id stat_code, p_c.first_name as customer_first_name, p_c.last_name as customer_last_name,	" +
                        "         nvl(a_s.is_recurring, 'N') as recurring " +
                        "	      from appointment t	" +
                        "	      left join location l on t.profile_id = l.profile_id	" +
                        "	      left join appointment_customers a_c on t.appointment_id = a_c.appointment_id	" +
                        "	      left join customer c on a_c.customer_id = c.customer_id	" +
                        "	      left join customer_status c_s on c.customer_status_id = c_s.customer_status_id	" +
                        "	      left join person p_c on c.person_id = p_c.person_id	" +
                        "	      left join instructor i on t.instructor_id = i.instructor_id	" +
                        "	      left join person p_i on i.person_id = p_i.person_id	" +
                        "	      left join activity a on t.activity_id = a.activity_id	" +
                        "	      left join service s on a.service_id = s.service_id	" +
                        "         inner join appointment_series a_s on t.appointment_series_id = a_s.appointment_series_id    " +
                        "	      where l.location_id = :locationId	" +
                        "	      and upper(s.service_name) = upper('Lesson')	" +
                        "	        and t.instructor_id is not null	" +
                        "	        and t.start_time >= :startTime	" +
                        "	        and t.end_time <= :endTime	" +
                        "	        and decode(c_s.external_id, null, '_', c_s.external_id) != 'C'	" +
                        "	      order by to_char(t.start_time, 'YYYY-MM-DD')";
                SQLQuery query = pSession.createSQLQuery(sb);
                query.setLong("locationId", pLocationId);
                query.setTimestamp("startTime", pStartTime);
                query.setTimestamp("endTime", pEndTime);

                query.addScalar("start_date", StandardBasicTypes.STRING);
                query.addScalar("start_day", StandardBasicTypes.STRING);
                query.addScalar("canceled", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("instructor_name", StandardBasicTypes.STRING);
                query.addScalar("customer_gc_id", StandardBasicTypes.STRING);
                query.addScalar("stat_code", StandardBasicTypes.STRING);
                query.addScalar("customer_first_name", StandardBasicTypes.STRING);
                query.addScalar("customer_last_name", StandardBasicTypes.STRING);
                query.addScalar("recurring", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();
                List<ExportAppointmentDTO> result = new ArrayList<ExportAppointmentDTO>();
                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    String startDate = (String) objects[0];
                    String startDay = (String) objects[1];
                    if("1".equals(startDay)){
                        startDay = "Sun";
                    }else if("2".equals(startDay)){
                        startDay = "Mon";
                    }else if("3".equals(startDay)){
                        startDay = "Tues";
                    }else if("4".equals(startDay)){
                        startDay = "Wed";
                    }else if("5".equals(startDay)){
                        startDay = "Thurs";
                    }else if("6".equals(startDay)){
                        startDay = "Fri";
                    }else if("7".equals(startDay)){
                        startDay = "Sat";
                    }

                    String appointmentCancelled = (String) objects[2];

                    String activityName = (String) objects[3];
                    String instructorName = (String) objects[4];
                    String customerGCID = (String) objects[5];
                    String statCode = (String) objects[6];
                    String customerFirstName = (String) objects[7];
                    String customerLastName = (String) objects[8];
                    String recurring = (String) objects[9];

                    ExportAppointmentDTO dto = new ExportAppointmentDTO(customerFirstName, customerLastName, customerGCID, statCode, startDate, startDay, activityName, instructorName, appointmentCancelled, recurring);
                    result.add(dto);
                }
                scroll.close();

                return result;
            }
        };

    }

    public static Criterion<Appointment, RehearsalScheduleDateListDTO> findRehearsalScheduleByProfileIdAndDateTime(
            final long pProfileId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<RehearsalScheduleDateListDTO>() {

            @Override
            public List<RehearsalScheduleDateListDTO> search(Session pSession, int pFetchMode) {
                String sb = "select to_char(t.start_time, 'MM/DD/YYYY') as date1 ,t.appointment_id as appointment_id, " +
                        " t.start_time as start_time, t.end_time as end_time, t.canceled as canceled," +
                        " (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration," +
                        " r.profile_room_name as profile_room_name, a.activity_name as activity_name," +
                        " c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name," +
                        " t.band_name as band_name " +
                        " from appointment t" +
                        " left join appointment_customers a_c on t.appointment_id = a_c.appointment_id" +
                        " left join customer c on a_c.customer_id = c.customer_id" +
                        " left join customer_status c_s on c.customer_status_id = c_s.customer_status_id" +
                        " left join person p_c on c.person_id = p_c.person_id" +
                        " left join room r on t.room_id = r.room_id" +
                        " left join activity a on t.activity_id = a.activity_id" +
                        " left join service s on a.service_id = s.service_id" +
                        " where t.profile_id = :profileId " +
                        "   and lower(s.service_name) = lower('rehearsal')" +
                        "   and t.start_time >= :startTime " +
                        "   and t.end_time <= :endTime " +
                        "   and decode(c_s.external_id, null, '_', c_s.external_id) != 'C' " +
                        "   and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        " order by to_char(t.start_time, 'YYYY-MM-DD'), t.start_time, p_c.first_name, p_c.last_name";
                SQLQuery query = pSession.createSQLQuery(sb);
                query.setLong("profileId", pProfileId);
                query.setTimestamp("startTime", pStartTime);
                query.setTimestamp("endTime", pEndTime);
                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("appointment_id", StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("end_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("canceled", StandardBasicTypes.STRING);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.addScalar("profile_room_name", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("customer_id", StandardBasicTypes.LONG);
                query.addScalar("c_first_name", StandardBasicTypes.STRING);
                query.addScalar("c_last_name", StandardBasicTypes.STRING);
                query.addScalar("band_name", StandardBasicTypes.STRING);
                ScrollableResults scroll = query.scroll();
                List<RehearsalScheduleDateListDTO> result = new ArrayList<RehearsalScheduleDateListDTO>();
                Map<String, List<RehearsalScheduleDTO>> cache = new HashMap<String, List<RehearsalScheduleDTO>>();
                Map<Long, RehearsalScheduleDTO> rehearsalScheduleDTOs = new HashMap<Long, RehearsalScheduleDTO>();
                Map<Long, Customer> customers = new HashMap<Long, Customer>();
                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    String date1 = (String) objects[0];
                    Long appointment_id = (Long) objects[1];
                    if (!cache.containsKey(date1)) {
                        List<RehearsalScheduleDTO> list = new ArrayList<RehearsalScheduleDTO>();
                        cache.put(date1, list);
                        result.add(new RehearsalScheduleDateListDTO(date1, list));
                    }
                    List<RehearsalScheduleDTO> rehearsalScheduleDTOList = cache.get(date1);
                    RehearsalScheduleDTO rehearsalScheduleDTO = rehearsalScheduleDTOs.get(appointment_id);
                    if (rehearsalScheduleDTO == null) {
                        rehearsalScheduleDTO = new RehearsalScheduleDTO(appointment_id, (Date) objects[2], (Date) objects[3], (String) objects[4], (Long) objects[5], (String) objects[6], (String) objects[7]);
                        rehearsalScheduleDTO.setBandName((String) objects[11]);
                        rehearsalScheduleDTOList.add(rehearsalScheduleDTO);
                        rehearsalScheduleDTOs.put(appointment_id, rehearsalScheduleDTO);
                    }
                    Long customerId = (Long) objects[8];
                    if (customerId == null) continue;
                    Customer customer = customers.get(customerId);
                    if (customer == null) {
                        customer = new Customer();
                        customer.setCustomerId(customerId);
                        Person person = new Person();
                        person.setFirstName((String) objects[9]);
                        person.setLastName((String) objects[10]);
                        customer.setPerson(person);
                        customers.put(customerId, customer);
                    }
                    rehearsalScheduleDTO.addCustomer(customer);
                }
                scroll.close();
                return result;
            }

        };

    }

    public static Criterion<Appointment, RehearsalBookingDateListDTO> findRehearsalBookingByProfileIdAndCreateTime(
            final long pProfileId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<RehearsalBookingDateListDTO>() {

            @Override
            public List<RehearsalBookingDateListDTO> search(Session pSession, int pFetchMode) {
                String sb = "select to_char(t.start_time, 'MM/DD/YYYY') as date1 ,t.appointment_id as appointment_id, " +
                        " t.start_time as start_time, t.end_time as end_time, t.canceled as canceled," +
                        " (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration," +
                        " r.profile_room_name as profile_room_name, a.activity_name as activity_name," +
                        " c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name," +
                        " t.band_name as band_name, t.appointment_series_id as series_id " +
                        " from appointment t" +
                        " left join appointment_customers a_c on t.appointment_id = a_c.appointment_id" +
                        " left join customer c on a_c.customer_id = c.customer_id" +
                        " left join customer_status c_s on c.customer_status_id = c_s.customer_status_id" +
                        " left join person p_c on c.person_id = p_c.person_id" +
                        " left join room r on t.room_id = r.room_id" +
                        " left join activity a on t.activity_id = a.activity_id" +
                        " left join service s on a.service_id = s.service_id" +
                        " where t.profile_id = :profileId " +
                        "   and lower(s.service_name) = lower('rehearsal')" +
                        "   and t.create_time >= :startTime " +
                        "   and t.create_time <= :endTime " +
                        "   and decode(c_s.external_id, null, '_', c_s.external_id) != 'C' " +
                        "   and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        " order by to_char(t.start_time, 'YYYY-MM-DD'), t.start_time, p_c.first_name, p_c.last_name";
                SQLQuery query = pSession.createSQLQuery(sb);
                query.setLong("profileId", pProfileId);
                query.setTimestamp("startTime", pStartTime);
                query.setTimestamp("endTime", pEndTime);
                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("appointment_id", StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("end_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("canceled", StandardBasicTypes.STRING);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.addScalar("profile_room_name", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("customer_id", StandardBasicTypes.LONG);
                query.addScalar("c_first_name", StandardBasicTypes.STRING);
                query.addScalar("c_last_name", StandardBasicTypes.STRING);
                query.addScalar("band_name", StandardBasicTypes.STRING);
                query.addScalar("series_id", StandardBasicTypes.LONG);
                ScrollableResults scroll = query.scroll();
                List<RehearsalBookingDateListDTO> result = new ArrayList<RehearsalBookingDateListDTO>();
                Map<String, List<RehearsalBookingDTO>> cache = new HashMap<String, List<RehearsalBookingDTO>>();
                Set<Long> seriesCache = new HashSet<Long>();
                Map<Long, RehearsalBookingDTO> rehearsalBookingDTOs = new HashMap<Long, RehearsalBookingDTO>();
                Map<Long, Customer> customers = new HashMap<Long, Customer>();
                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    Long series_id = (Long) objects[12];
                    if (seriesCache.contains(series_id)){
                        continue;
                    }
                    seriesCache.add(series_id);
                    String date1 = (String) objects[0];
                    Long appointment_id = (Long) objects[1];
                    if (!cache.containsKey(date1)) {
                        List<RehearsalBookingDTO> list = new ArrayList<RehearsalBookingDTO>();
                        cache.put(date1, list);
                        result.add(new RehearsalBookingDateListDTO(date1, list));
                    }
                    List<RehearsalBookingDTO> rehearsalBookingDTOList = cache.get(date1);
                    RehearsalBookingDTO rehearsalBookingDTO = rehearsalBookingDTOs.get(appointment_id);
                    if (rehearsalBookingDTO == null) {
                        rehearsalBookingDTO = new RehearsalBookingDTO(appointment_id, (Date) objects[2], (Date) objects[3], (String) objects[4], (Long) objects[5], (String) objects[6], (String) objects[7]);
                        rehearsalBookingDTO.setBandName((String) objects[11]);
                        rehearsalBookingDTOList.add(rehearsalBookingDTO);
                        rehearsalBookingDTOs.put(appointment_id, rehearsalBookingDTO);
                    }
                    Long customerId = (Long) objects[8];
                    if (customerId == null) continue;
                    Customer customer = customers.get(customerId);
                    if (customer == null) {
                        customer = new Customer();
                        customer.setCustomerId(customerId);
                        Person person = new Person();
                        person.setFirstName((String) objects[9]);
                        person.setLastName((String) objects[10]);
                        customer.setPerson(person);
                        customers.put(customerId, customer);
                    }
                    rehearsalBookingDTO.addCustomer(customer);
                }
                scroll.close();
                return result;
            }

        };

    }

    public static Criterion<Appointment, MasterDailyDateListDTO> findDailyMasterByProfileIdAndDateTime(final long pProfileId,
                                                                                                       final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<MasterDailyDateListDTO>() {

            @Override
            public List<MasterDailyDateListDTO> search(Session pSession, int pFetchMode) {
                String sb = "select to_char(t.start_time, 'MM/DD/YYYY') as date1 ,t.appointment_id as appointment_id, " +
                        " t.start_time as start_time, t.end_time as end_time, t.canceled as canceled," +
                        " (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration," +
                        " r.profile_room_name as profile_room_name, a.activity_name as activity_name," +
                        " i.instructor_id as instructor_id, p_i.first_name as i_first_name, p_i.last_name as i_last_name," +
                        " c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name, t.note as notes" +
                        " from appointment t" +
                        " left join appointment_customers a_c on t.appointment_id = a_c.appointment_id" +
                        " left join customer c on a_c.customer_id = c.customer_id" +
                        " left join customer_status c_s on c.customer_status_id = c_s.customer_status_id" +
                        " left join person p_c on c.person_id = p_c.person_id" +
                        " left join instructor i on t.instructor_id = i.instructor_id" +
                        " left join person p_i on i.person_id = p_i.person_id" +
                        " left join room r on t.room_id = r.room_id" +
                        " left join activity a on t.activity_id = a.activity_id" +
                        " where t.profile_id = :profileId " +
                        "   and t.start_time >= :startTime " +
                        "   and t.end_time <= :endTime " +
                        "   and decode(c_s.external_id, null, '_', c_s.external_id) != 'C' " +
                        "   and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        " order by to_char(t.start_time, 'YYYY-MM-DD'), t.start_time, p_i.first_name, p_i.last_name, p_c.first_name, p_c.last_name";
                SQLQuery query = pSession.createSQLQuery(sb);
                query.setLong("profileId", pProfileId);
                query.setTimestamp("startTime", pStartTime);
                query.setTimestamp("endTime", pEndTime);
                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("appointment_id", StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("end_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("canceled", StandardBasicTypes.STRING);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.addScalar("profile_room_name", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("instructor_id", StandardBasicTypes.LONG);
                query.addScalar("i_first_name", StandardBasicTypes.STRING);
                query.addScalar("i_last_name", StandardBasicTypes.STRING);
                query.addScalar("customer_id", StandardBasicTypes.LONG);
                query.addScalar("c_first_name", StandardBasicTypes.STRING);
                query.addScalar("c_last_name", StandardBasicTypes.STRING);
                query.addScalar("notes", StandardBasicTypes.STRING);
                ScrollableResults scroll = query.scroll();
                List<MasterDailyDateListDTO> result = new ArrayList<MasterDailyDateListDTO>();
                Map<String, List<MasterDailyDTO>> cache = new HashMap<String, List<MasterDailyDTO>>();
                Map<Long, MasterDailyDTO> masterDailyDTOs = new HashMap<Long, MasterDailyDTO>();
                Map<Long, Instructor> instructors = new HashMap<Long, Instructor>();
                Map<Long, Customer> customers = new HashMap<Long, Customer>();
                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    String date1 = (String) objects[0];
                    Long appointment_id = (Long) objects[1];
                    Long instructorId = (Long) objects[8];
                    Instructor instructor = null;
                    if (instructorId != null) {
                        instructor = instructors.get(instructorId);
                        if (instructor == null) {
                            instructor = new Instructor();
                            instructor.setInstructorId(instructorId);
                            Person person = new Person();
                            person.setFirstName((String) objects[9]);
                            person.setLastName((String) objects[10]);
                            instructor.setPerson(person);
                            instructors.put(instructorId, instructor);
                        }
                    }
                    if (!cache.containsKey(date1)) {
                        List<MasterDailyDTO> list = new ArrayList<MasterDailyDTO>();
                        cache.put(date1, list);
                        result.add(new MasterDailyDateListDTO(date1, list));
                    }
                    List<MasterDailyDTO> masterDailyDTOList = cache.get(date1);
                    MasterDailyDTO masterDailyDTO = masterDailyDTOs.get(appointment_id);
                    if (masterDailyDTO == null) {
                        masterDailyDTO = new MasterDailyDTO(appointment_id, (Date) objects[2], (Date) objects[3], (String) objects[4], (Long) objects[5], (String) objects[6], (String) objects[7]);
                        masterDailyDTOList.add(masterDailyDTO);
                        masterDailyDTOs.put(appointment_id, masterDailyDTO);
                    }
                    if (masterDailyDTO.getInstructor() == null && instructor != null) {
                        masterDailyDTO.setInstructor(instructor);
                    }

                    /**
                     * Add the queried notes to report,for gcss-576
                     */
                    masterDailyDTO.setNotes((String)objects[14]);

                    Long customerId = (Long) objects[11];
                    if (customerId == null) continue;
                    Customer customer = customers.get(customerId);
                    if (customer == null) {
                        customer = new Customer();
                        customer.setCustomerId(customerId);
                        Person person = new Person();
                        person.setFirstName((String) objects[12]);
                        person.setLastName((String) objects[13]);
                        customer.setPerson(person);
                        customers.put(customerId, customer);
                    }
                    masterDailyDTO.addCustomer(customer);
                }
                scroll.close();
                return result;
            }

        };

    }

    public static Criterion<Appointment, Map<String, List<InstructorAppointmentBusinessHours>>> findInstructorAppointmentBusinessHoursByProfileIdAndDateTime(
            final long pProfileId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<Map<String, List<InstructorAppointmentBusinessHours>>>() {

            @Override
            public Map<String, List<InstructorAppointmentBusinessHours>> get(Session pSession, int pFetchMode) {
                String sb = "select to_char(t2.date1, 'MM/DD/YYYY') as date2, t2.date1, t2.instructor_id, t2.minimum_time, t2.maximum_time, p.first_name, p.last_name" +
                        "  from (select t1.date1 ," +
                        "               t1.INSTRUCTOR_ID," +
                        "               min(t1.START_TIME) as minimum_time," +
                        "               max(t1.END_TIME) as maximum_time" +
                        "          from (select to_date(to_char(t.start_time, 'YYYY-MM-DD'),'YYYY-MM-DD') date1," +
                        "		                  t.INSTRUCTOR_ID," +
                        "				          t.START_TIME," +
                        "                       t.END_TIME" +
                        "                 from APPOINTMENT t" +
                        "                 left join appointment_customers a_c on t.appointment_id = a_c.appointment_id" +
                        "                 left join customer c on a_c.customer_id = c.customer_id" +
                        "                 left join customer_status c_s on c.customer_status_id = c_s.customer_status_id" +
                        "                 where t.instructor_id is not null" +
                        "                   and t.profile_id = :profileId" +
                        "                   and t.start_time >= :startTime" +
                        "                   and t.end_time <= :endTime" +
                        "                   and decode(c_s.external_id, null, '_', c_s.external_id) != 'C'" +
                        "                   and decode(t.canceled, null, 'N', t.canceled) != 'Y') t1" +
                        "         group by t1.date1, t1.INSTRUCTOR_ID" +
                        "         order by t1.date1, t1.INSTRUCTOR_ID) t2" +
                        "  left join INSTRUCTOR i" +
                        "    on t2.instructor_id = i.instructor_id" +
                        "  left join person p" +
                        "    on i.person_id = p.person_id" +
                        "  order by t2.date1, p.first_name, p.last_name";
                SQLQuery query = pSession.createSQLQuery(sb);
                query.setLong("profileId", pProfileId);
                query.setTimestamp("startTime", pStartTime);
                query.setTimestamp("endTime", pEndTime);
                query.addScalar("date2", StandardBasicTypes.STRING);
                query.addScalar("date1", StandardBasicTypes.DATE);
                query.addScalar("instructor_id", StandardBasicTypes.LONG);
                query.addScalar("minimum_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("maximum_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("first_name", StandardBasicTypes.STRING);
                query.addScalar("last_name", StandardBasicTypes.STRING);
                ScrollableResults scroll = query.scroll();
                Map<String, List<InstructorAppointmentBusinessHours>> result = new HashMap<String, List<InstructorAppointmentBusinessHours>>();
                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    String date1 = (String) objects[0];
                    if (!result.containsKey(date1)) {
                        result.put(date1, new ArrayList<InstructorAppointmentBusinessHours>());
                    }
                    List<InstructorAppointmentBusinessHours> list = result.get(date1);
                    InstructorAppointmentBusinessHours obj = new InstructorAppointmentBusinessHours();
                    obj.setCurrentDate((Date) objects[1]);
                    obj.setInstructorId((Long) objects[2]);
                    obj.setMinimumTime((Date) objects[3]);
                    obj.setMaximumTime((Date) objects[4]);
                    obj.setFirstName((String) objects[5]);
                    obj.setLastName((String) objects[6]);
                    list.add(obj);
                }
                scroll.close();
                return result;
            }

        };

    }

    /**
     * Return all appointments that are associated with the supplied series id.
     *
     * @param appointmentSeriesId long integer identifying the series to load
     * @return a Criterion that can search for matching appointments
     */
    public static Criterion<Appointment, Appointment> findBySeries(final long appointmentSeriesId) {
        return new AppointmentCriterion<Appointment>() {
            @SuppressWarnings("unchecked")
            @Override
            public List<Appointment> search(Session pSession, int pFetchMode) {
                Query query = pSession.createQuery(new StringBuilder(" from Appointment t ")
                        .append(getFetchScript(pFetchMode))
                        .append(" where t.appointmentSeries.id = :appointmentSeriesId ")
                        .append("  order by t.endTime ")
                        .toString());
                query.setLong("appointmentSeriesId", appointmentSeriesId);
                return query.list();
            }
        };
    }

    @Override
    protected String getFetchScript(int pFetchMode) {
        StringBuilder sb = new StringBuilder();
        sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
        sb.append(addFetchHQL(FETCH_ACTIVITY, pFetchMode, "t.activity", true));
        sb.append(addFetchHQL(FETCH_APPOINTMENT_SERIES, pFetchMode, "t.appointmentSeries", true));
        sb.append(addFetchHQL(FETCH_INSTRUCTOR, pFetchMode, "t.instructor", true));
        sb.append(addFetchHQL(FETCH_LOCATION_PROFILE, pFetchMode, "t.locationProfile", true));
        sb.append(addFetchHQL(FETCH_ROOM, pFetchMode, "t.room", true));
        sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
        sb.append(addFetchHQL(FETCH_MORE_CUSTOMERS, pFetchMode, "t.customers", true));
        return sb.toString();
    }

    @Override
    public List<E> search(Session pSession, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }

    @Override
    public E get(Session pSession, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }

    /**
     * For gcss-
     *
     * @param pProfileId
     * @param pStartTime
     * @param pEndTime
     * @return
     */
    public static Criterion<Appointment, CancelledAppointmentDateListDTO> findCancelledApptReportByProfileIdAndDateTime(final Long pProfileId,
                                                                                                                        final Date pStartTime, final Date pEndTime, boolean justLessonActivities) {
        return new AppointmentCriterion<CancelledAppointmentDateListDTO>() {
            DateFormat outputFormatter = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_HIPHEN);

            @Override
            public List<CancelledAppointmentDateListDTO> search(Session pSession, int pFetchMode) {

                final Date startTime = new Date(pStartTime.getTime());
                final Date endTime = new Date(pEndTime.getTime());

                String comparStartDate = outputFormatter.format(startTime);
                String comparEndDate = outputFormatter.format(endTime);


                StringBuilder sb = new StringBuilder("	select trunc(t.start_time) as date1 ,t.appointment_id as appointment_id, ");
                sb.append(" t.start_time as start_time, t.end_time as end_time, t.canceled as canceled,");
                //GSSP-269 changes
                sb.append(" a_cr.cancel_reason as cancel_reason,");
                sb.append(" r.profile_room_name as profile_room_name, a.activity_name as activity_name,");
                sb.append(" i.instructor_id as instructor_id, p_i.first_name as i_first_name, p_i.last_name as i_last_name,");
                sb.append(" c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name, t.note as notes, ");

                //GSSP-214 changes
                sb.append(" p_cu.FIRST_NAME as cu_first_name,p_cu.LAST_NAME as cu_last_name, to_char(t.UPDATED, 'DD-MON-YYYY HH:MI AM') as time1,t.updated_by as updated_id from appointment t");
                sb.append(" left join appointment_cancel a_ca on t.appointment_id = a_ca.appointment_id ");
                sb.append(" left join appointment_cancel_reason a_cr on a_ca.appointment_cancel_reason_id = a_cr.appointment_cancel_reason_id ");
                sb.append(" left join appointment_customers a_c on t.appointment_id = a_c.appointment_id");
                sb.append(" left join customer c on a_c.customer_id = c.customer_id");
                sb.append(" left join customer_status c_s on c.customer_status_id = c_s.customer_status_id");
                sb.append(" left join person p_c on c.person_id = p_c.person_id");
                sb.append(" left join instructor i on t.instructor_id = i.instructor_id");
                sb.append(" left join person p_i on i.person_id = p_i.person_id");
                sb.append(" left join person p_cu on t.UPDATED_BY = p_cu.PERSON_ID");
                sb.append(" left join room r on t.room_id = r.room_id");
                sb.append(" left join activity a on t.activity_id = a.activity_id");
                sb.append(" left join service s on s.service_id = a.service_id");

                sb.append(" where t.profile_id = :profileId ");
                if(comparStartDate.equals(comparEndDate))
                {
                    sb.append("	and trunc(t.start_Time) = :startTime");
                    sb.append("	and trunc(t.end_Time) = :endTime");
                }
                else
                {
                    sb.append("	and trunc(t.start_time) >= :startTime ");
                    sb.append("	and trunc(t.end_time) <= :endTime ");
                }
                sb.append("	and decode(c_s.external_id, null, '_', c_s.external_id) != 'C' ");
                sb.append("	and decode(t.canceled, null, 'N', t.canceled) = 'Y' ");
                //below filter for GCSS-598
                sb.append("	and upper(s.service_name) <> 'REHEARSAL'");
                sb.append("	and exists(select * from appointment_customers where appointment_id = t.appointment_id)");
                sb.append("	order by to_char(t.start_time, 'DD-MON-YYYY'), t.start_time, p_i.first_name, p_i.last_name, p_c.first_name, p_c.last_name");



                SQLQuery query = pSession.createSQLQuery(sb.toString());
                query.setLong("profileId", pProfileId);
                query.setDate("startTime", startTime);
                query.setDate("endTime", endTime);
                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("appointment_id", StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("end_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("canceled", StandardBasicTypes.STRING);
                query.addScalar("cancel_reason", StandardBasicTypes.STRING);
                query.addScalar("profile_room_name", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("instructor_id", StandardBasicTypes.LONG);
                query.addScalar("i_first_name", StandardBasicTypes.STRING);
                query.addScalar("i_last_name", StandardBasicTypes.STRING);
                query.addScalar("customer_id", StandardBasicTypes.LONG);
                query.addScalar("c_first_name", StandardBasicTypes.STRING);
                query.addScalar("c_last_name", StandardBasicTypes.STRING);

                /**
                 * Set the type of column 'notes'
                 */
                query.addScalar("notes", StandardBasicTypes.STRING);
                //GSSP-214 changes
                query.addScalar("cu_first_name", StandardBasicTypes.STRING);
                query.addScalar("cu_last_name", StandardBasicTypes.STRING);
                query.addScalar("time1", StandardBasicTypes.STRING);
                query.addScalar("updated_Id", StandardBasicTypes.LONG);
                ScrollableResults scroll = query.scroll();
                List<CancelledAppointmentDateListDTO> result = new ArrayList<CancelledAppointmentDateListDTO>();
                Map<String, List<CancelledApptDailyDTO>> cache = new HashMap<String, List<CancelledApptDailyDTO>>();
                Map<Long, CancelledApptDailyDTO> cancelledApptDailyDTOs = new HashMap<Long, CancelledApptDailyDTO>();
                Map<Long, Instructor> instructors = new HashMap<Long, Instructor>();
                Map<Long, Instructor> cancelledUser = new HashMap<Long, Instructor>();
                Map<Long, Customer> customers = new HashMap<Long, Customer>();
                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    String date1 = (String) objects[0];
                    Long appointment_id = (Long) objects[1];
                    Long instructorId = (Long) objects[8];
                    Long updatedId = (Long) objects[18];
                    //String cancelledUser = (String) (objects[16]+""+objects[17]);

                    Instructor instructor = null;
                    if (instructorId != null) {
                        instructor = instructors.get(instructorId);
                        if (instructor == null) {
                            instructor = new Instructor();
                            instructor.setInstructorId(instructorId);
                            Person person = new Person();
                            person.setFirstName((String) objects[9]);
                            person.setLastName((String) objects[10]);
                            instructor.setPerson(person);
                            instructors.put(instructorId, instructor);
                        }
                    }
                    if (!cache.containsKey(date1)) {
                        List<CancelledApptDailyDTO> list = new ArrayList<CancelledApptDailyDTO>();
                        cache.put(date1, list);
                        result.add(new CancelledAppointmentDateListDTO(date1, list));
                    }
                    List<CancelledApptDailyDTO> cancelledDailyDTOList = cache.get(date1);
                    CancelledApptDailyDTO cancelledDailyDTO = cancelledApptDailyDTOs.get(appointment_id);
                    if (cancelledDailyDTO == null) {
                        cancelledDailyDTO = new CancelledApptDailyDTO(appointment_id, (Date) objects[2], (Date) objects[3], (String) objects[4], (String) objects[6], (String) objects[7]);
                        cancelledDailyDTOList.add(cancelledDailyDTO);
                        cancelledApptDailyDTOs.put(appointment_id, cancelledDailyDTO);
                    }
                    if (cancelledDailyDTO.getInstructor() == null && instructor != null) {
                        cancelledDailyDTO.setInstructor(instructor);
                    }

                    /**
                     * Add notes column to report
                     */
                    cancelledDailyDTO.setNotes(null == objects[14] ? "" : objects[14].toString());
                    cancelledDailyDTO.setCancelledReason(null == objects[5] ? "" : objects[5].toString());

                    //GSSP-214 changes
                    cancelledDailyDTO.setCancelledTime(objects[17].toString());
                    //cancelledDailyDTO.setCancelledUser(objects[16].toString()+ " "+objects[17].toString());
                    if (instructorId != null) {
                        Instructor instructor1 = null;
                        if (updatedId != null) {
                            instructor1 = instructors.get(updatedId);
                            if (instructor1 == null) {
                                instructor1 = new Instructor();
                                instructor1.setInstructorId(updatedId);
                                Person person = new Person();
                                person.setFirstName((String) objects[15]);
                                person.setLastName((String) objects[16]);
                                instructor1.setPerson(person);
                                cancelledUser.put(instructorId, instructor1);
                            }
                            cancelledDailyDTO.setCancelledUser(instructor1);
                        }
                    }
                    Long customerId = (Long) objects[11];
                    if (customerId == null) continue;
                    Customer customer = customers.get(customerId);
                    if (customer == null) {
                        customer = new Customer();
                        customer.setCustomerId(customerId);
                        Person person = new Person();
                        person.setFirstName((String) objects[12]);
                        person.setLastName((String) objects[13]);
                        customer.setPerson(person);
                        customers.put(customerId, customer);
                    }
                    cancelledDailyDTO.addCustomer(customer);
                }
                scroll.close();
                return result;

            }
        };

    }

    /**
     * Added for NewInsAptReport _ June 2015 Enhancement
     *
     * @param location
     * @param startDate
     * @param endDate
     * @param inputExternalId
     * @return
     */
    // GSSP-190 added a new feild-InstructorName
    public static Criterion<Appointment, InstructorOpenAppointmentsDTO> findInstructorOpenAppointmentsByLocationIdAndDateTime(
            final long pLocationId, final Date pStartTime, final Date pEndTime, final String pInputExternalId, final String pInstructorName) {

        return new AppointmentCriterion<InstructorOpenAppointmentsDTO>() {

            @Override
            public List<InstructorOpenAppointmentsDTO> search(Session pSession, int pFetchMode) {
                String sb,firstName = null,lastName ="",check;
                check=pInputExternalId;
                SQLQuery query = null;
                sb = "select to_char(t.start_time, 'DD-Mon-YYYY') as date1 ,t.appointment_id as appointment_id, " +
                        " t.start_time as start_time, t.end_time as end_time, t.canceled as canceled," +
                        " (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration," +
                        " r.profile_room_name as profile_room_name, a.activity_name as activity_name," +
                        " i.instructor_id as instructor_id, p_i.first_name as i_first_name, p_i.last_name as i_last_name," +
                        " c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name " +
                        " from appointment t" +
                        " join location l on t.profile_id = l.profile_id" +
                        " left join appointment_customers a_c on t.appointment_id = a_c.appointment_id" +
                        " left join customer c on a_c.customer_id = c.customer_id" +
                        " left join person p_c on c.person_id = p_c.person_id" +
                        " join instructor i on t.instructor_id = i.instructor_id" +
                        " left join person p_i on i.person_id = p_i.person_id" +
                        " left join room r on t.room_id = r.room_id" +
                        " left join activity a on t.activity_id = a.activity_id" +
                        " left join availability ay on i.availability_id = ay.availability_id" +
                        " where l.location_id = :locationId " +
                        "   and i.external_id = :inputExternalId " +
                        "   and t.start_time >= :startTime " +
                        "   and t.end_time <= :endTime " +
                        "   and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        " order by to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS')";
                //GSSP-296  changes for instructor open Appointments
                String fullName=null;
                if(!(check.trim().length()>0) && null != pInstructorName &&  ! pInstructorName.equalsIgnoreCase("")){
                    fullName = pInstructorName.replaceAll("[^a-zA-Z0-9]","").toUpperCase();
                    sb= sb.replace("i.external_id = :inputExternalId" ,"  UPPER(REGEXP_REPLACE(( p_i.FIRST_name|| p_i.Last_name), '[^0-9A-Za-z]', '')) = :fullName  " );
                }
                query = pSession.createSQLQuery(sb);
                query.setLong("locationId", pLocationId);
                query.setTimestamp("startTime", pStartTime);
                query.setTimestamp("endTime", pEndTime);
                if(!(check.trim().length()>0)){
                    //GSSP-296  changes for instructor open Appointments
                    query.setString("fullName", fullName.trim());
                }
                else{
                    query.setString("inputExternalId", pInputExternalId);
                }
                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("appointment_id", StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("end_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("canceled", StandardBasicTypes.STRING);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.addScalar("profile_room_name", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("instructor_id", StandardBasicTypes.LONG);
                query.addScalar("i_first_name", StandardBasicTypes.STRING);
                query.addScalar("i_last_name", StandardBasicTypes.STRING);
                query.addScalar("customer_id", StandardBasicTypes.LONG);
                query.addScalar("c_first_name", StandardBasicTypes.STRING);
                query.addScalar("c_last_name", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();
                List<InstructorOpenAppointmentsDTO> result = new ArrayList<InstructorOpenAppointmentsDTO>();
                Map<Long, InstructorOpenAppointmentsDTO> instructorOpenAppointmentsDTOs = new HashMap<Long, InstructorOpenAppointmentsDTO>();
                Map<Long, Instructor> instructors = new HashMap<Long, Instructor>();
                Map<Long, Customer> customers = new HashMap<Long, Customer>();

                //Logic for getting the instructor details from person table
                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    String date1 = (String) objects[0];
                    Long appointment_id = (Long) objects[1];
                    Long instructorId = (Long) objects[8];
                    Instructor instructor = instructors.get(instructorId);
                    if (instructor == null) {
                        instructor = new Instructor();
                        instructor.setInstructorId(instructorId);
                        Person person = new Person();
                        person.setFirstName((String) objects[9]);
                        person.setLastName((String) objects[10]);
                        instructor.setPerson(person);
                        instructors.put(instructorId, instructor);
                    }

                    //Populating the DTO for open appointments
                    InstructorOpenAppointmentsDTO instructorOpenAppointmentsDTO = instructorOpenAppointmentsDTOs.get(appointment_id);
                    if (instructorOpenAppointmentsDTO == null) {
                        instructorOpenAppointmentsDTO = new InstructorOpenAppointmentsDTO(date1, appointment_id, (Date) objects[2], (Date) objects[3], (String) objects[4], (Long) objects[5], (String) objects[6], (String) objects[7], instructor);
                        instructorOpenAppointmentsDTOs.put(appointment_id, instructorOpenAppointmentsDTO);
                        result.add(instructorOpenAppointmentsDTO);
                    }

                    //Adding customers details to open appointments DTO
                    Long customerId = (Long) objects[11];
                    if (customerId == null) continue;
                    Customer customer = customers.get(customerId);
                    if (customer == null) {
                        customer = new Customer();
                        customer.setCustomerId(customerId);
                        Person person = new Person();
                        person.setFirstName((String) objects[12]);
                        person.setLastName((String) objects[13]);
                        customer.setPerson(person);
                        customers.put(customerId, customer);
                    }
                    instructorOpenAppointmentsDTO.addCustomer(customer);

                }
                scroll.close();
                return result;
            }
        };
    }

    /**
     * For GSSP-170, Conflicting Appointments by Instructor
     *   //Changes made for GSSP-238
     * @param location
     * @param startDate
     * @param endDate
     * @return
     */
    public static Criterion<Appointment, InstructorOpenAppointmentsDTO> findConflictAppointmentsByInstructorProfileIdAndDateTime(
            final Long pProfileId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<InstructorOpenAppointmentsDTO>() {

            @Override
            public List<InstructorOpenAppointmentsDTO> search(Session pSession, int pFetchMode) {


                StringBuilder sb = new StringBuilder("select to_char(t.start_time, 'DD-Mon-YYYY') as date1 ,t.appointment_id as appointment_id,");
                sb.append(" t.start_time as start_time, t.end_time as end_time,");
                sb.append(" (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration,");
                sb.append(" a.activity_name as activity_name," );
                sb.append(" i.instructor_id as instructor_id, p_i.first_name as i_first_name, p_i.last_name as i_last_name," );
                sb.append(" c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name," );
                sb.append(" a_s.is_recurring as is_recurring, ");
                sb.append(" l.external_id ||  ' ' || l.location_name as location_name");
                sb.append(" from appointment t" );

                // changes made for GSSP 238
                if(null != pProfileId)
                {
                    sb.append(" join appointment t2 on t.profile_id = :profileId ");
                    sb.append("  and t.profile_id = t2.profile_id" );
                }
                else
                {
                    sb.append(" join appointment t2 on t.profile_id = t2.profile_id ");

                }

                sb.append("  and t.instructor_id = t2.instructor_id" );
                sb.append("  and t.appointment_id != t2.appointment_id" );
                sb.append("  and to_date(to_char(t.start_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') >= :startTime ");
                sb.append("  and to_date(to_char(t2.start_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') >= :startTime ");
                sb.append("  and to_date(to_char(t.end_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') <= :endTime ");
                sb.append("  and to_date(to_char(t2.end_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') <= :endTime " );
                sb.append("  and (t2.start_time >= t.start_time and t2.start_time < t.end_time");
                sb.append("  or t2.end_time > t.start_time and t2.end_time <= t.end_time" );
                sb.append("  or t.start_time >= t2.start_time and t.end_time <= t2.end_time)" );
                sb.append(   " join instructor i on t.instructor_id = i.instructor_id");
                sb.append(   " join person p_i on p_i.person_id = i.person_id" );
                sb.append(   " left join appointment_customers a_c on t.appointment_id = a_c.appointment_id");
                sb.append(   " left join customer c on a_c.customer_id = c.customer_id" );
                sb.append(  " left join person p_c on c.person_id = p_c.person_id");
                sb.append(  " join activity a on a.activity_id = t.activity_id" );
                sb.append(" join location l on l.profile_id = t.profile_id" );
                sb.append( " join appointment_series a_s on a_s.appointment_series_id = t.appointment_series_id" );
                sb.append(  " where decode(t.canceled, null, 'N', t.canceled) != 'Y'" );
                sb.append( "  and decode(t2.canceled, null, 'N', t2.canceled) != 'Y' " );
                sb.append(  " order by to_date(to_char(t.start_time, 'YYYY-MM-DD'), 'YYYY-MM-DD')," );
                sb.append( " t.instructor_id,to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS'),");
                sb.append("  to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS')");

                SQLQuery query = pSession.createSQLQuery(sb.toString());

                //Changes made to GSSP-238
                if(null != pProfileId)
                {
                    query.setLong("profileId", pProfileId);
                }

                query.setTimestamp("startTime", pStartTime);
                query.setTimestamp("endTime", pEndTime);
                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("appointment_id", StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("end_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("instructor_id", StandardBasicTypes.LONG);
                query.addScalar("i_first_name", StandardBasicTypes.STRING);
                query.addScalar("i_last_name", StandardBasicTypes.STRING);
                query.addScalar("customer_id", StandardBasicTypes.LONG);
                query.addScalar("c_first_name", StandardBasicTypes.STRING);
                query.addScalar("c_last_name", StandardBasicTypes.STRING);
                query.addScalar("is_recurring", StandardBasicTypes.STRING);
                //Changes made for GSSP-238
                query.addScalar("location_name", StandardBasicTypes.STRING);
                ScrollableResults scroll = query.scroll();
                List<InstructorOpenAppointmentsDTO> result = new ArrayList<InstructorOpenAppointmentsDTO>();
                Map<Long, InstructorOpenAppointmentsDTO> instructorOpenAppointmentsDTOs = new HashMap<Long, InstructorOpenAppointmentsDTO>();
                Map<Long, Instructor> instructors = new HashMap<Long, Instructor>();
                Map<Long, Customer> customers = new HashMap<Long, Customer>();

                //Logic for getting the instructor details from person table
                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    String date1 = (String) objects[0];
                    Long appointment_id = (Long) objects[1];
                    String is_recurring = (String) objects[12];
                    Long instructorId = (Long) objects[6];
                    //Changes made for GSSP-238
                    String location_name = (String) objects[13];
                    Instructor instructor = instructors.get(instructorId);
                    if (instructor == null) {
                        instructor = new Instructor();
                        instructor.setInstructorId(instructorId);
                        Person person = new Person();
                        person.setFirstName((String) objects[7]);
                        person.setLastName((String) objects[8]);
                        instructor.setPerson(person);
                        instructors.put(instructorId, instructor);
                    }

                    //Populating the DTO for open appointments
                    InstructorOpenAppointmentsDTO instructorOpenAppointmentsDTO = instructorOpenAppointmentsDTOs.get(appointment_id);
                    if (instructorOpenAppointmentsDTO == null) {
                        //Changes made for GSSP-238
                        instructorOpenAppointmentsDTO = new InstructorOpenAppointmentsDTO(date1, appointment_id, (Date) objects[2], (Date) objects[3], (Long) objects[4], (String) objects[5], instructor, is_recurring,location_name);
                        instructorOpenAppointmentsDTOs.put(appointment_id, instructorOpenAppointmentsDTO);
                        result.add(instructorOpenAppointmentsDTO);
                    }

                    //Adding customers details to open appointments DTO
                    Long customerId = (Long) objects[9];
                    if (customerId == null) continue;
                    Customer customer = customers.get(customerId);
                    if (customer == null) {
                        customer = new Customer();
                        customer.setCustomerId(customerId);
                        Person person = new Person();
                        person.setFirstName((String) objects[10]);
                        person.setLastName((String) objects[11]);
                        customer.setPerson(person);
                        customers.put(customerId, customer);
                    }
                    instructorOpenAppointmentsDTO.addCustomer(customer);

                }
                scroll.close();
                return result;
            }
        };
    }

    /**
     * For GSSP-170, Conflicting Appointments by Room
     *
     * @param location
     * @param startDate
     * @param endDate
     * @return
     */
    public static Criterion<Appointment, InstructorOpenAppointmentsDTO> findConflictAppointmentsByRoomProfileIdAndDateTime(
            final Long pProfileId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<InstructorOpenAppointmentsDTO>() {

            @Override
            public List<InstructorOpenAppointmentsDTO> search(Session pSession, int pFetchMode) {

                StringBuilder sb = new StringBuilder("select to_char(t.start_time, 'DD-Mon-YYYY') as date1 ,t.appointment_id as appointment_id, " );
                sb.append(" t.start_time as start_time, t.end_time as end_time,");
                sb.append(" (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration,");
                sb.append(" r.profile_room_name as profile_room_name, a.activity_name as activity_name," );
                sb.append(" c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name," );
                sb.append(" a_s.is_recurring as is_recurring, ");
                sb.append(" l.external_id ||  ' ' || l.location_name as location_name");


                sb.append(" from appointment t" );

                // changes made for GSSP 238

                if(null != pProfileId)
                {
                    sb.append(" join appointment t2 on t.profile_id = :profileId ");
                    sb.append("  and t.profile_id = t2.profile_id");
                }
                else
                {
                    sb.append(" join appointment t2 on t.profile_id = t2.profile_id ");
                }

                sb.append(	   "  and t.room_id = t2.room_id" );
                sb.append(	   "  and t.appointment_id != t2.appointment_id" );
                sb.append(	   "  and to_date(to_char(t.start_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') >= :startTime " );
                sb.append(	   "  and to_date(to_char(t2.start_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') >= :startTime " );
                sb.append("  and to_date(to_char(t.end_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') <= :endTime " );
                sb.append(  "  and to_date(to_char(t2.end_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') <= :endTime ");
                sb.append("  and (t2.start_time >= t.start_time and t2.start_time < t.end_time" );
                sb.append(	   "   or t2.end_time > t.start_time and t2.end_time <= t.end_time");
                sb.append(	   "   or t.start_time >= t2.start_time and t.end_time <= t2.end_time)");
                sb.append(	   " left join appointment_customers a_c on t.appointment_id = a_c.appointment_id" );
                sb.append(	   " left join customer c on a_c.customer_id = c.customer_id" );
                sb.append(	  " left join person p_c on c.person_id = p_c.person_id" );
                sb.append (" join room r on r.room_id = t.room_id" );
                sb.append( " join activity a on a.activity_id = t.activity_id");
                sb.append(" join appointment_series a_s on a_s.appointment_series_id = t.appointment_series_id" );
                //Changes made for GSSP-238
                sb.append(" join location l on l.profile_id = t.profile_id" );

                sb.append( " where decode(t.canceled, null, 'N', t.canceled) != 'Y'" );
                sb.append( "  and decode(t2.canceled, null, 'N', t2.canceled) != 'Y' " );
                sb.append( " order by to_date(to_char(t.start_time, 'YYYY-MM-DD'), 'YYYY-MM-DD'), r.profile_room_name,");
                sb.append("to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS')," );
                sb.append( "  to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS')");
                SQLQuery query = pSession.createSQLQuery(sb.toString());
                //Changes made to GSSP-238
                if(null != pProfileId)
                {
                    query.setLong("profileId", pProfileId);
                }
                query.setTimestamp("startTime", pStartTime);
                query.setTimestamp("endTime", pEndTime);
                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("appointment_id", StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("end_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.addScalar("profile_room_name", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("customer_id", StandardBasicTypes.LONG);
                query.addScalar("c_first_name", StandardBasicTypes.STRING);
                query.addScalar("c_last_name", StandardBasicTypes.STRING);
                query.addScalar("is_recurring", StandardBasicTypes.STRING);
                //Changes made for GSSP-238
                query.addScalar("location_name", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();
                List<InstructorOpenAppointmentsDTO> result = new ArrayList<InstructorOpenAppointmentsDTO>();
                Map<Long, InstructorOpenAppointmentsDTO> instructorOpenAppointmentsDTOs = new HashMap<Long, InstructorOpenAppointmentsDTO>();
                Map<Long, Customer> customers = new HashMap<Long, Customer>();

                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    String date1 = (String) objects[0];
                    Long appointment_id = (Long) objects[1];
                    String is_recurring = (String) objects[10];
                    //Changes made for GSSP-238
                    String location_name = (String) objects[11];

                    //Populating the DTO for open appointments
                    InstructorOpenAppointmentsDTO instructorOpenAppointmentsDTO = instructorOpenAppointmentsDTOs.get(appointment_id);
                    if (instructorOpenAppointmentsDTO == null) {
                        //Changes made for GSSP-238
                        instructorOpenAppointmentsDTO = new InstructorOpenAppointmentsDTO(date1, appointment_id, (Date) objects[2], (Date) objects[3], (Long) objects[4],(String) objects[5], (String) objects[6], is_recurring,location_name);
                        instructorOpenAppointmentsDTOs.put(appointment_id, instructorOpenAppointmentsDTO);
                        result.add(instructorOpenAppointmentsDTO);
                    }


                    //Adding customers details to open appointments DTO
                    Long customerId = (Long) objects[7];
                    if (customerId == null) continue;
                    Customer customer = customers.get(customerId);
                    if (customer == null) {
                        customer = new Customer();
                        customer.setCustomerId(customerId);
                        Person person = new Person();
                        person.setFirstName((String) objects[8]);
                        person.setLastName((String) objects[9]);
                        customer.setPerson(person);
                        customers.put(customerId, customer);
                    }
                    instructorOpenAppointmentsDTO.addCustomer(customer);

                }


                scroll.close();
                return result;
            }
        };
    }

    /**
     * For GSSP-161, Added for Instructor outside availability appointments report
     *
     * @param location
     * @param startTime
     * @param endTime
     * @param inputExternalId
     * @param dayType
     * @return
     */
    public static Criterion<Appointment, InstructorOpenAppointmentsDTO> findInstructorOutsideAppointmentsByLocationIdAndDateTime(
            final Long pProfileId, final String pStartTime, final String pEndTime, final String pInputExternalId, final String pDayType) {

        return new AppointmentCriterion<InstructorOpenAppointmentsDTO>() {

            @Override
            public List<InstructorOpenAppointmentsDTO> search(Session pSession, int pFetchMode) {

                String sb2 = "select t.appointment_id as appointment_id " +
                        " from appointment t" +
                        " join onetime o on t.instructor_id = o.instructor_id" +
                        " join instructor i on t.instructor_id = i.instructor_id" +
                        " where t.profile_id = :profileId " +
                        "   and i.external_id = :inputExternalId " +
                        "   and t.start_time >= sysdate " +
                        "   and to_char(t.start_time - 1, 'd') = :dayType " +
                        "   and o.start_time <= t.start_time and o.end_time >= t.end_time";

                SQLQuery query2 = pSession.createSQLQuery(sb2);
                query2.setLong("profileId", pProfileId);
                query2.setString("inputExternalId", pInputExternalId);
                query2.setString("dayType", pDayType);
                query2.addScalar("appointment_id", StandardBasicTypes.LONG);
                ScrollableResults scroll1 = query2.scroll();

                List<Long> oneTimeAppointmentIDs = new ArrayList<Long>();

                while (scroll1.next()) {
                    Object[] objects1 = scroll1.get();
                    Long appointment_id = (Long)objects1[0];
                    oneTimeAppointmentIDs.add(appointment_id);
                }

                String sb = "select to_char(t.start_time, 'DD-Mon-YYYY') as date1 ,t.appointment_id as appointment_id, " +
                        " t.start_time as start_time, t.end_time as end_time, t.canceled as canceled," +
                        " (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration," +
                        " r.profile_room_name as profile_room_name, a.activity_name as activity_name," +
                        " i.instructor_id as instructor_id, p_i.first_name as i_first_name, p_i.last_name as i_last_name," +
                        " c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name " +
                        " from appointment t" +
                        " left join appointment_customers a_c on t.appointment_id = a_c.appointment_id" +
                        " left join customer c on a_c.customer_id = c.customer_id" +
                        " left join person p_c on c.person_id = p_c.person_id" +
                        " join instructor i on t.instructor_id = i.instructor_id" +
                        " join person p_i on i.person_id = p_i.person_id" +
                        " join room r on t.room_id = r.room_id" +
                        " join activity a on t.activity_id = a.activity_id" +
                        " where t.profile_id = :profileId " +
                        "   and i.external_id = :inputExternalId " +
                        "   and to_char(t.start_time - 1, 'd') = :dayType " +
                        "   and (to_char(t.start_time, 'HH24:MI') < :startTime " +
                        "    or to_char(t.end_time, 'HH24:MI') > :endTime )" +
                        "   and t.start_time >= sysdate " +
                        "   and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        " order by to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS')";
                SQLQuery query = pSession.createSQLQuery(sb);
                query.setLong("profileId", pProfileId);
                query.setString("startTime", pStartTime);
                query.setString("endTime", pEndTime);
                query.setString("inputExternalId", pInputExternalId);
                query.setString("dayType", pDayType);
                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("appointment_id", StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("end_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("canceled", StandardBasicTypes.STRING);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.addScalar("profile_room_name", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("instructor_id", StandardBasicTypes.LONG);
                query.addScalar("i_first_name", StandardBasicTypes.STRING);
                query.addScalar("i_last_name", StandardBasicTypes.STRING);
                query.addScalar("customer_id", StandardBasicTypes.LONG);
                query.addScalar("c_first_name", StandardBasicTypes.STRING);
                query.addScalar("c_last_name", StandardBasicTypes.STRING);
                ScrollableResults scroll = query.scroll();
                List<InstructorOpenAppointmentsDTO> result = new ArrayList<InstructorOpenAppointmentsDTO>();
                Map<Long, InstructorOpenAppointmentsDTO> instructorOpenAppointmentsDTOs = new HashMap<Long, InstructorOpenAppointmentsDTO>();
                Map<Long, Instructor> instructors = new HashMap<Long, Instructor>();
                Map<Long, Customer> customers = new HashMap<Long, Customer>();




                //Logic for getting the instructor details from person table
                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    String date1 = (String) objects[0];
                    Long appointment_id = (Long) objects[1];
                    Long instructorId = (Long) objects[8];
                    Instructor instructor = instructors.get(instructorId);
                    if (instructor == null) {
                        instructor = new Instructor();
                        instructor.setInstructorId(instructorId);
                        Person person = new Person();
                        person.setFirstName((String) objects[9]);
                        person.setLastName((String) objects[10]);
                        instructor.setPerson(person);
                        instructors.put(instructorId, instructor);
                    }


                    if(!oneTimeAppointmentIDs.contains(appointment_id)) {
                        //Populating the DTO for open appointments
                        InstructorOpenAppointmentsDTO instructorOpenAppointmentsDTO = instructorOpenAppointmentsDTOs.get(appointment_id);
                        if (instructorOpenAppointmentsDTO == null) {
                            instructorOpenAppointmentsDTO = new InstructorOpenAppointmentsDTO(date1, appointment_id, (Date) objects[2], (Date) objects[3], (String) objects[4], (Long) objects[5], (String) objects[6], (String) objects[7], instructor);
                            instructorOpenAppointmentsDTOs.put(appointment_id, instructorOpenAppointmentsDTO);
                            result.add(instructorOpenAppointmentsDTO);
                        }

                        //Adding customers details to open appointments DTO
                        Long customerId = (Long) objects[11];
                        if (customerId == null) continue;
                        Customer customer = customers.get(customerId);
                        if (customer == null) {
                            customer = new Customer();
                            customer.setCustomerId(customerId);
                            Person person = new Person();
                            person.setFirstName((String) objects[12]);
                            person.setLastName((String) objects[13]);
                            customer.setPerson(person);
                            customers.put(customerId, customer);
                        }
                        instructorOpenAppointmentsDTO.addCustomer(customer);

                    }
                }
                scroll.close();
                scroll1.close();

                return result;
            }
        };
    }


    //Added for GSSP-158
    /**
     * Returns a Criterion that can be used to find any existing employees that belong to the zone id
     *
     * @param pPersonId person identifier
     * @return Criterion instance
     */
    public static Criterion<Appointment, Map<Long,List<String>>> findByZoneID(final String locationTimezone) {

        return new AppointmentCriterion<Map<Long,List<String>>>() {

            @Override
            public List<Map<Long,List<String>>> search(Session pSession, int pFetchMode) {


                //Changes made for GSSP-230
                StringBuilder sb = new StringBuilder("select  l.location_id, p.email from employee e ");
                sb.append(" join person p on p.person_id = e.person_id ");
                sb.append(" join person_role pr on pr.person_id = e.person_id");
                sb.append(" join location l on l.location_id = pr.location_id "  );
                sb.append(" join location_profile lp on l.profile_id = lp.profile_id " );
                sb.append(" where e.enterprise_status='A' and (e.status = 'Enable')");

                //Changes made for GSSP-230
                if(! StringUtils.isEmpty(locationTimezone))
                    sb.append(" and lp.TZ = :timeZone");

                sb.append(" order by l.location_id ");


                SQLQuery query = pSession.createSQLQuery(sb.toString());


                if(! StringUtils.isEmpty(locationTimezone))
                {
                    query.setString("timeZone", locationTimezone);
                }

                query.addScalar("location_id", StandardBasicTypes.LONG);
                query.addScalar("email", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();

                List<Map<Long,List<String>>> result = new ArrayList<Map<Long,List<String>>>();
                Map<Long,List<String>> cache = new HashMap<Long,List<String>>();
                //  List<String> emailIDList = new ArrayList<String>();


                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    Long location_id = (Long) objects[0];

                    if (!cache.containsKey(location_id)) {

                        List<String> list = new ArrayList<String>();
                        cache.put(location_id, list);

                    }
                    List<String> emailIDList = cache.get(location_id);


                    String emailID = (String) objects[1];
                    if( null  !=  emailID)
                    {
                        emailIDList.add(emailID);
                    }




                }
                scroll.close();
                result.add(cache);

                return result;
            }
        };

    }


    //For Gssp-255
    public static Criterion<Appointment, Map<Long,List<String>>> findByInstructorPersonalEmail(final String locationTimezone) {

        return new AppointmentCriterion<Map<Long,List<String>>>() {

            @Override
            public List<Map<Long,List<String>>> search(Session pSession, int pFetchMode) {

                StringBuilder sb = new StringBuilder("select  l.location_id, ppd.personal_email from instructor i");
                sb.append(" join person_personal_details ppd on ppd.person_id = i.person_id");
                sb.append(" join location l on l.location_id = i.location_id");
                sb.append(" join location_profile lp on l.profile_id = lp.profile_id");
                sb.append(" where i.status = 'A'  and i.enabled = 'Y'");

                if(! StringUtils.isEmpty(locationTimezone))
                    sb.append(" and lp.TZ = :timeZone");

                sb.append(" order by l.location_id ");


                SQLQuery query = pSession.createSQLQuery(sb.toString());


                if(! StringUtils.isEmpty(locationTimezone))
                {
                    query.setString("timeZone", locationTimezone);
                }

                query.addScalar("location_id", StandardBasicTypes.LONG);
                query.addScalar("personal_email", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();

                List<Map<Long,List<String>>> result = new ArrayList<Map<Long,List<String>>>();
                Map<Long,List<String>> cache = new HashMap<Long,List<String>>();



                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    Long location_id = (Long) objects[0];

                    if (!cache.containsKey(location_id)) {

                        List<String> list = new ArrayList<String>();
                        cache.put(location_id, list);

                    }
                    List<String> emailIDList = cache.get(location_id);

//GSSP-267 Job notification

                    String personal_email = (String) objects[1];




                    if( null  != personal_email && personal_email.matches(EMAIL_REGEX))
                    {
                        String trim_personal_email=personal_email.trim();
                        String lowercase_personal_email=trim_personal_email.toLowerCase();
                        emailIDList.add(lowercase_personal_email);
                    }



                }
                scroll.close();
                result.add(cache);

                return result;
            }
        };

    }

    //For GSSP-243 send email with cc Associates/Managers/Leads

    public static AppointmentCriterion<String> findEmployeeEmailIdsbyProfileID(
            final long profileID) {

        return new AppointmentCriterion<String>() {
            @Override
            public List<String> search(Session pSession, int pFetchMode) {

                String sb = "  select p.email from EMPLOYEE e " +
                        " join person p on p.person_id = e.person_id "+
                        " join person_role pr on pr.person_id = e.person_id "+
                        " join location l on l.location_id = pr.location_id "+
                        " where   l.profile_id = :ProfileID  and e.enterprise_status='A' AND P.EMAIL is not null  and (e.status = 'Enable') and pr.role_id in (1,2,3)";


                SQLQuery query = pSession.createSQLQuery(sb);
                List<String> emailList = new ArrayList<String>();
                query.setLong("ProfileID", profileID);
                query.addScalar("email", StandardBasicTypes.STRING);
                ScrollableResults scroll = query.scroll();

                while (scroll.next()) {

                    Object[] objects = scroll.get();

                    String email = (String)objects[0];
                    emailList.add(email);

                }

                return emailList;
            }
        };

    }


    //Added for Scheduling Phase II - Month View Service
    /**
     * Returns a Criterion that can be used to find  details of appointments belonging to an appointment series for the specified month
     *
     * @param appointmentSeriesId Long
     * @param pStartTime Date
     * @param pEndTime Date
     *
     * @return Criterion instance
     */
    public static Criterion<Appointment, Lesson> findAppointmentBySeriesAndMonth(
            final Long appointmentSeriesId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<Lesson>() {

            @Override
            public List<Lesson> search(Session pSession, int pFetchMode) {
                String sb = "select t.appointment_id as appointment_id,to_char(t.start_time, 'HH:MI AM') as time1, " +
                        " to_char(t.end_time, 'HH:MI AM') as time2,"+
                        " to_char(t.start_time, 'YYYY-MM-DD') as date1, a.activity_name as activity_name,"+
                        " p_i.first_name as i_first_name, p_i.last_name as i_last_name"+
                        " from appointment t" +
                        " join activity a on a.activity_id = t.activity_id" +
                        " left join instructor i on t.instructor_id = i.instructor_id" +
                        " left join person p_i on i.person_id = p_i.person_id	" +
                        " where t.appointment_series_id= :appointmentSeriesId " +
                        " and t.start_time > :pStartTime" +
                        " and t.end_time <=  :pEndTime" +
                        " and decode(t.canceled, null, 'N', t.canceled) != 'Y'" +
                        //Changes made for LES-152
                        " order by t.start_time" ;


                SQLQuery query = pSession.createSQLQuery(sb);



                query.setLong("appointmentSeriesId", appointmentSeriesId);
                query.setTimestamp("pStartTime", pStartTime);
                query.setTimestamp("pEndTime", pEndTime);

                query.addScalar("appointment_id", StandardBasicTypes.LONG);
                query.addScalar("time1", StandardBasicTypes.STRING);
                query.addScalar("time2", StandardBasicTypes.STRING);
                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("i_first_name", StandardBasicTypes.STRING);
                query.addScalar("i_last_name", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();
                List<Lesson> result = new ArrayList<Lesson>();
                Lesson  appointmentJSON = null;
                String activityName= "", editEnable ="";


                List<String> editDisabledList  = Arrays.asList(editDisabled);

                while (scroll.next()) {

                    Object[] objects = scroll.get();

                    activityName = (String) objects[4];

                    if(editDisabledList.contains(activityName))
                    {
                        editEnable = "N";
                    }
                    else
                    {
                        editEnable = "Y";
                    }

                    appointmentJSON = new Lesson((Long) objects[0],activityName,
                            (String) objects[1],(String) objects[5],(String) objects[6],(String) objects[2],(String) objects[3],editEnable);

                    result.add(appointmentJSON);

                }
                scroll.close();

                return result;
            }
        };

    }


    //Added for Scheduling Phase II - Month View Service
    /**
     * Returns a Criterion that can be used to find  details of profile and location  of  an  appointment series
     *
     * @param appointmentSeriesId Long
     *
     * @return Criterion instance
     */
    public static Criterion<Appointment,LocationProfileInfoDTO> findProfileDetailsByAppointmentSeriesID(
            final Long appointmentSeriesId) {

        return new AppointmentCriterion<LocationProfileInfoDTO>() {

            @Override
            public List<LocationProfileInfoDTO> search(Session pSession, int pFetchMode) {
                String sb = " select l.location_name as studio_name, l.phone as phone , lp.profile_id as profile_id, "+
                        " to_char(ap.series_start_time,'YYYY-MM-DD')as series_start_date  from appointment_series ap " +
                        " join  location_profile lp on  ap.profile_id = lp.profile_id "+
                        " join  location l  on ap.profile_id = l.profile_id "+
                        " where ap.appointment_series_id=:appointmentSeriesId";


                SQLQuery query = pSession.createSQLQuery(sb);

                query.setLong("appointmentSeriesId", appointmentSeriesId);

                query.addScalar("studio_name", StandardBasicTypes.STRING);
                query.addScalar("phone", StandardBasicTypes.STRING);
                query.addScalar("profile_id", StandardBasicTypes.LONG);
                query.addScalar("series_start_date", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();



                List<LocationProfileInfoDTO> result = new ArrayList<LocationProfileInfoDTO>();

                LocationProfileInfoDTO locationProfileInfoDTO = null;

                while (scroll.next()) {

                    Object[] objects = scroll.get();

                    locationProfileInfoDTO  =  new  LocationProfileInfoDTO((Long) objects[2],(String) objects[0] ,
                            (String) objects[1],(String) objects[3]);

                    result.add(locationProfileInfoDTO);


                }
                scroll.close();

                return result;
            }
        };

    }

    //Added for Scheduling Phase II - Month View Service
    /**
     * Returns a Criterion that can be used to find  customer details based on bade Number
     *
     * @param badgenumber String
     *
     * @return Criterion instance
     */

    public static AppointmentCriterion<String> findCustomerNameByExternalId(
            final String customerMemberId) {

        return new AppointmentCriterion<String>() {

            @Override
            public String get(Session pSession, int pFetchMode) {


                String sb = " select p.first_name, p.last_name  from person p " +
                        " join customer c on c.person_id = p.person_id "+
                        " where c.EXTERNAL_ID = :customerMemberId";


                SQLQuery query = pSession.createSQLQuery(sb);

                query.setString("customerMemberId", customerMemberId);

                query.addScalar("first_name", StandardBasicTypes.STRING);
                query.addScalar("last_name", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();

                String firstName= "", lastName = "", fullName = "";;

                while (scroll.next()) {

                    Object[] objects = scroll.get();

                    firstName= (String)objects[0];
                    lastName = (String)objects[1];

                    fullName = StringUtils.isBlank(firstName)?"":firstName + " " + (StringUtils.isBlank(lastName) ? "":lastName);

                }
                scroll.close();

                return fullName;
            }
        };

    }

    //Added for Scheduling Phase II - Instructor Fetch  View Service
    /**
     * Returns a Criterion that can be used to find  details of appointments passed from the UI
     *
     * @param appointmentId Long
     *
     * @return Criterion instance
     */
    public static Criterion<Appointment, LocationProfileInfoDTO> findProfileDetailByAppointmentID(
            final Long appointmentId) {

        return new AppointmentCriterion<LocationProfileInfoDTO>() {

            @Override
            public List<LocationProfileInfoDTO> search(Session pSession, int pFetchMode) {


                String sb = " select t.profile_id as profileId, t.appointment_series_id ,l.location_id, t.activity_id, " +
                        " l.location_name as locationName, l.phone  ,to_char(ap.series_start_time,'YYYY-MM-DD')as seriesStartDate, " +
                        " to_char(t.start_time, 'YYYY-MM-DD') as appointmentDate,  " +
                        " (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration, " +
                        " to_char(t.start_time,'HH:MI AM') as start_time, to_char(t.end_time,'HH:MI AM') as end_time, "+
                        " to_char(ap.series_end_time,'YYYY-MM-DD')as seriesEndDate ," +
                        " ap.is_recurring as isRecurring" +
                        " from appointment t  " +
                        " join appointment_series ap on t.appointment_series_id = ap.appointment_series_id "+
                        " join location_profile lp on t.profile_id = lp.profile_id "+
                        " join location l on lp.profile_id = l.profile_id " +
                        " where t.appointment_id=:appointmentId";




                SQLQuery query = pSession.createSQLQuery(sb);


                query.setLong("appointmentId", appointmentId);

                query.addScalar("profileId", StandardBasicTypes.LONG);
                query.addScalar("appointment_series_id", StandardBasicTypes.LONG);
                query.addScalar("location_id", StandardBasicTypes.LONG);
                query.addScalar("activity_id", StandardBasicTypes.LONG);
                query.addScalar("locationName", StandardBasicTypes.STRING);
                query.addScalar("phone", StandardBasicTypes.STRING);
                query.addScalar("seriesStartDate", StandardBasicTypes.STRING);
                query.addScalar("appointmentDate", StandardBasicTypes.STRING);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.STRING);
                query.addScalar("seriesEndDate", StandardBasicTypes.STRING);
                query.addScalar("isRecurring", StandardBasicTypes.STRING);


                ScrollableResults scroll = query.scroll();
                List<LocationProfileInfoDTO> result = new ArrayList<LocationProfileInfoDTO>();

                LocationProfileInfoDTO locationProfileInfoDTO = null;
                while (scroll.next()) {

                    Object[] objects = scroll.get();

                    locationProfileInfoDTO  =  new  LocationProfileInfoDTO((Long) objects[0],(String) objects[4] ,
                            (String) objects[5],(String) objects[6],(Long) objects[1],(Long) objects[2],(Long) objects[3], (String)objects[7],(Long)objects[8],
                            (String)objects[9],(String)objects[10],(String)objects[11],(String)objects[12]);

                    result.add(locationProfileInfoDTO);


                }
                scroll.close();

                return result;

            }
        };

    }





    //Added for Phase2_LES-27 Changes
    /**
     * Returns a Criterion that can be used to find  details of appointments passed from the UI
     * @param instructoroneDayAvailabilityMap
     *
     * @param appointmentId Long
     *
     * @return Criterion instance
     *
     *      */

    //GSSP-287 Code changes.
    public static Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> findInstructorAvailableHours(
            final Long instructorId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<Map<String, List<InstructorAvailableHoursDTO>>>() {

            @Override
            public Map<String, List<InstructorAvailableHoursDTO>> get(Session pSession, int pFetchMode) {


                String sb = "select to_char(t.start_time, 'yyyy-MM-dd') as date1 , " +
                        "to_char(t.start_time,'HH24:Mi') as start_time, to_char(t.end_time,'HH24:Mi') as end_time from appointment t " +
                        "where t.start_time >= :pStartTime and t.end_time <= :pEndTime and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        "and t.instructor_id in (:instructorId) " +
                        "minus " +
                        "select to_char(t.start_time, 'yyyy-MM-dd') as date1 , " +
                        "to_char(t.start_time,'HH24:Mi') as start_time, " +
                        "to_char(t.end_time,'HH24:Mi') as end_time " +
                        "from appointment t ,onetime ot " +
                        "where " +
                        "t.instructor_id = ot.instructor_id " +
                        "and t.start_time > :pStartTime " +
                        "and t.end_time <= :pEndTime " +
                        "and t.start_time >= ot.start_time " +
                        "and t.end_time <= ot.end_time " +
                        "and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        "and t.instructor_id in (:instructorId) order by date1 desc " ;


                SQLQuery query = pSession.createSQLQuery(sb);


                query.setLong("instructorId", instructorId);
                query.setDate("pStartTime", pStartTime);
                query.setDate("pEndTime", pEndTime);

                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.STRING);

                Map<String, List<InstructorAvailableHoursDTO>> cache = new HashMap<String, List<InstructorAvailableHoursDTO>>();

                InstructorAvailableHoursDTO instructorAvailableHoursDTO = null;

                List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList = null;

                ScrollableResults scroll = query.scroll();

                while (scroll.next()) {

                    Object[] objects = scroll.get();
                    String instructorDate = (String) objects[0];

                    if (!cache.containsKey(instructorDate)) {

                        List<InstructorAvailableHoursDTO> list = new ArrayList<InstructorAvailableHoursDTO>();
                        cache.put(instructorDate, list);

                    }

                    instructorAvailableHoursDTOList = cache.get(instructorDate);

                    DateTimeFormatter df1 = DateTimeFormat.forPattern(DateTimeUtil.HOURS_PATTERN);

                    String startTime = (String) objects[1];
                    String endTime = (String) objects[2];
                    instructorAvailableHoursDTO = new InstructorAvailableHoursDTO(df1.parseLocalTime(startTime),df1.parseLocalTime(endTime)) ;

                    instructorAvailableHoursDTOList.add(instructorAvailableHoursDTO);

                }

                return cache;

            }
        };

    }

    //GSSP-287 Code changes.
    public static Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> findInstructorAvailableHoursInsAVL(
            final Long instructorId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<Map<String, List<InstructorAvailableHoursDTO>>>() {

            @Override
            public Map<String, List<InstructorAvailableHoursDTO>> get(Session pSession, int pFetchMode) {


                String sb = "select to_char(t.start_time, 'yyyy-MM-dd') as date1 , " +
                        "to_char(t.start_time,'HH24:Mi') as start_time, to_char(t.end_time,'HH24:Mi') as end_time from appointment t " +
                        "where t.start_time >= :pStartTime and t.end_time <= :pEndTime and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        "and t.instructor_id in (:instructorId) " +
                        "minus " +
                        "select to_char(t.start_time, 'yyyy-MM-dd') as date1 , " +
                        "to_char(t.start_time,'HH24:Mi') as start_time, " +
                        "to_char(t.end_time,'HH24:Mi') as end_time " +
                        "from appointment t ,onetime ot " +
                        "where " +
                        "t.instructor_id = ot.instructor_id " +
                        "and t.start_time > :pStartTime " +
                        "and t.end_time <= :pEndTime " +
                        "and t.start_time >= ot.start_time " +
                        "and t.end_time <= ot.end_time " +
                        "and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        "and t.instructor_id in (:instructorId) order by date1 desc " ;


                SQLQuery query = pSession.createSQLQuery(sb);


                query.setLong("instructorId", instructorId);
                query.setDate("pStartTime", pStartTime);
                query.setDate("pEndTime", pEndTime);

                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.STRING);

                Map<String, List<InstructorAvailableHoursDTO>> cache = new HashMap<String, List<InstructorAvailableHoursDTO>>();

                InstructorAvailableHoursDTO instructorAvailableHoursDTO = null;

                List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList = null;

                ScrollableResults scroll = query.scroll();

                while (scroll.next()) {

                    Object[] objects = scroll.get();
                    String instructorDate = (String) objects[0];

                    if (!cache.containsKey(instructorDate)) {

                        List<InstructorAvailableHoursDTO> list = new ArrayList<InstructorAvailableHoursDTO>();
                        cache.put(instructorDate, list);

                    }

                    instructorAvailableHoursDTOList = cache.get(instructorDate);

                    DateTimeFormatter df1 = DateTimeFormat.forPattern(DateTimeUtil.HOURS_PATTERN);

                    String startTime = (String) objects[1];
                    String endTime = (String) objects[2];
                    instructorAvailableHoursDTO = new InstructorAvailableHoursDTO(df1.parseLocalTime(startTime),df1.parseLocalTime(endTime)) ;

                    instructorAvailableHoursDTOList.add(instructorAvailableHoursDTO);

                }
                scroll.close();
                return cache;

            }
        };

    }
    //Added for Phase2_LES-27 Changes
    /**
     * Returns a Criterion that can be used to find time off details for instructors
     *
     * @param appointmentId Long
     *
     * @return Criterion instance
     */
    public static Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> getTimeOffForInstructors(
            Long[] pInstructorIds, final Date pStartTime, final Date pEndTime) {



        return new AppointmentCriterion<Map<String, List<InstructorAvailableHoursDTO>>>() {

            @Override
            public Map<String, List<InstructorAvailableHoursDTO>> get(Session pSession, int pFetchMode) {


                String sb =  "select  instructor_id, to_char(start_time,'yyyy-MM-dd') as start_date, to_char(end_time,'yyyy-MM-dd') as end_date,"
                        + "to_char(start_time,'HH24:Mi') as start_time , "+
                        " to_char(end_time,'HH24:Mi') as end_time from timeoff  " +
                        "  where ( "
                        + "(start_time  >= :pStartTime and start_time <= :pEndTime) or"
                        + "(end_time    >= :pStartTime and end_time   <= :pEndTime)   or "
                        + "(:pStartTime >= start_time  and :pEndTime  <=  end_time)"
                        + ") "
                        + "and  instructor_id in (:instructorIds) ";

                SQLQuery query = pSession.createSQLQuery(sb);


                query.setDate("pStartTime", pStartTime);
                query.setDate("pEndTime", pEndTime);
                query.setParameterList("instructorIds", pInstructorIds);

                query.addScalar("instructor_id", StandardBasicTypes.STRING);
                query.addScalar("start_date", StandardBasicTypes.STRING);
                query.addScalar("end_date", StandardBasicTypes.STRING);
                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.STRING);


                Map<String, List<InstructorAvailableHoursDTO>> cache = new HashMap<String, List<InstructorAvailableHoursDTO>>();

                InstructorAvailableHoursDTO instructorAvailableHoursDTO = null;

                List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList = null;

                ScrollableResults scroll = query.scroll();

                while (scroll.next()) {

                    Object[] objects = scroll.get();
                    String instructor = (String) objects[0] ;

                    if (!cache.containsKey(instructor)) {

                        List<InstructorAvailableHoursDTO> list = new ArrayList<InstructorAvailableHoursDTO>();
                        cache.put(instructor, list);

                    }

                    instructorAvailableHoursDTOList = cache.get(instructor);

                    DateTimeFormatter df1 = DateTimeFormat.forPattern(DateTimeUtil.HOURS_PATTERN);

                    String startTime = (String) objects[3];
                    String endTime = (String) objects[4];

                    String startDate =  (String) objects[1];
                    String endDate =  (String) objects[2];
                    instructorAvailableHoursDTO = new InstructorAvailableHoursDTO(df1.parseLocalTime(startTime),df1.parseLocalTime(endTime)
                            ,new DateTime(startDate), new DateTime(endDate)) ;

                    instructorAvailableHoursDTOList.add(instructorAvailableHoursDTO);



                }

                return cache;

            }
        };

    }

    
    /**
     * Returns a Criterion that can be used to find time off details for instructors
     *
     * @param appointmentId Long
     *
     * @return Criterion instance
     */
    public static Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> getTimeOffForInstructorsInsAVL(
            Long[] pInstructorIds, final Date pStartTime, final Date pEndTime) {



        return new AppointmentCriterion<Map<String, List<InstructorAvailableHoursDTO>>>() {

            @Override
            public Map<String, List<InstructorAvailableHoursDTO>> get(Session pSession, int pFetchMode) {


                String sb =  "select  instructor_id, to_char(start_time,'yyyy-MM-dd') as start_date, to_char(end_time,'yyyy-MM-dd') as end_date,"
                        + "to_char(start_time,'HH24:Mi') as start_time , "+
                        " to_char(end_time,'HH24:Mi') as end_time from timeoff  " +
                        "  where ( "
                        + "(start_time  >= :pStartTime and start_time <= :pEndTime) or"
                        + "(end_time    >= :pStartTime and end_time   <= :pEndTime)   or "
                        + "(:pStartTime >= start_time  and :pEndTime  <=  end_time)"
                        + ") "
                        + "and  instructor_id in (:instructorIds) ";

                SQLQuery query = pSession.createSQLQuery(sb);


                query.setDate("pStartTime", pStartTime);
                query.setDate("pEndTime", pEndTime);
                query.setParameterList("instructorIds", pInstructorIds);

                query.addScalar("instructor_id", StandardBasicTypes.STRING);
                query.addScalar("start_date", StandardBasicTypes.STRING);
                query.addScalar("end_date", StandardBasicTypes.STRING);
                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.STRING);


                Map<String, List<InstructorAvailableHoursDTO>> cache = new HashMap<String, List<InstructorAvailableHoursDTO>>();

                InstructorAvailableHoursDTO instructorAvailableHoursDTO = null;

                List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList = null;

                ScrollableResults scroll = query.scroll();

                while (scroll.next()) {

                    Object[] objects = scroll.get();
                    String instructor = (String) objects[0] ;

                    if (!cache.containsKey(instructor)) {

                        List<InstructorAvailableHoursDTO> list = new ArrayList<InstructorAvailableHoursDTO>();
                        cache.put(instructor, list);

                    }

                    instructorAvailableHoursDTOList = cache.get(instructor);

                    DateTimeFormatter df1 = DateTimeFormat.forPattern(DateTimeUtil.HOURS_PATTERN);

                    String startTime = (String) objects[3];
                    String endTime = (String) objects[4];

                    String startDate =  (String) objects[1];
                    String endDate =  (String) objects[2];
                    instructorAvailableHoursDTO = new InstructorAvailableHoursDTO(df1.parseLocalTime(startTime),df1.parseLocalTime(endTime)
                            ,new DateTime(startDate), new DateTime(endDate)) ;

                    instructorAvailableHoursDTOList.add(instructorAvailableHoursDTO);



                }
                scroll.close();
                return cache;

            }
        };

    }

    //Added for Scheduling Phase II - Validation Service
    public static Criterion<Appointment,LessonSeries> findAppointmentSeriesByCustomerId(
            final Long customerId) {

        return new AppointmentCriterion<LessonSeries>() {


            @Override
            public List<LessonSeries> search(Session pSession, int pFetchMode) {
                String sb = "select distinct(t.appointment_series_id) as seriesId,t.activity_id, " +
                        " (select min(to_char(t2.start_time,'YYYY-MM-DD')) from appointment t2 " +
                        " where t2.appointment_series_id = t.appointment_series_id and decode(t2.canceled, null, 'N', t2.canceled) != 'Y') as series_start_date,"+
                        " lp.profile_id as profile_id, a.activity_name from customer c"+
                        " join customer_appointment_series cas on c.customer_id = cas.customer_id "+
                        " join appointment_series ap on ap.appointment_series_id = cas.appointment_series_id"+
                        " join appointment t  on t.appointment_series_id = ap.appointment_series_id" +
                        " join location_profile lp on lp.profile_id = ap.profile_id" +
                        " join activity a on a.activity_id = t.activity_id" +
                        " where c.customer_id = :customerId 	" +
                        " and (select count(appointment_id) from appointment ti where t.appointment_series_id = ti.appointment_series_id  " +
                        " and to_timestamp(to_char(t.START_TIME, 'YYYY-MM-DD hh24:MI:SS'), 'YYYY-MM-DD hh24:MI:SS') " +
                        " > to_timestamp(to_char(CURRENT_TIMESTAMP AT TIME ZONE lp.TZ , 'YYYY-MM-DD hh24:MI:SS'), 'YYYY-MM-DD hh24:MI:SS') and t.canceled in ('N','H') ) >0 "
                        + " and a.service_id !=0 " ;

                SQLQuery query = pSession.createSQLQuery(sb);

                query.setLong("customerId", customerId);

                query.addScalar("seriesId", StandardBasicTypes.LONG);
                query.addScalar("activity_id", StandardBasicTypes.STRING);
                query.addScalar("series_start_date", StandardBasicTypes.STRING);
                query.addScalar("profile_id", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();
                List<LessonSeries> lessonSeriesList = new ArrayList<LessonSeries>();
                LessonSeries  lessonSeries = null;

                while (scroll.next()) {

                    Object[] objects = scroll.get();

                    lessonSeries = new LessonSeries((Long) objects[0],(String) objects[2],(String) objects[4],(String) objects[1],(String) objects[3]);
                    lessonSeriesList.add(lessonSeries)  ;

                }
                scroll.close();

                return lessonSeriesList;
            }
        };

    }


    //Added for Scheduling Phase II - update lesson service-LES 7
    /**
     * Returns a Criterion that can be used to find  person details based on bade Number
     *
     * @param external_id String
     *
     * @return Criterion instance
     */

    public static AppointmentCriterion<Long> findPersonIDByExternalId(
            final String external_id) {

        return new AppointmentCriterion<Long>() {

            @Override
            public Long get(Session pSession, int pFetchMode) {
                String sb = " select person_id from customer " +
                        " where external_id= :external_id";


                SQLQuery query = pSession.createSQLQuery(sb);

                query.setString("external_id", external_id);

                query.addScalar("person_id", StandardBasicTypes.LONG);



                ScrollableResults scroll = query.scroll();


                Long personID=null;
                while (scroll.next()) {

                    Object[] objects = scroll.get();

                    personID= (Long)objects[0];

                }
                scroll.close();

                return personID;
            }
        };

    }



    //Added for Scheduling Phase II - Lesson update service LES-7
    /**
     * Returns a Criterion that can be used to find  person details based on bade Number
     *
     * @param CustomerMemberId String
     *
     * @return Criterion instance
     */
    //GSSP- 253 removed the location and phone from the query and fetching these details from the sendEmailForModifySingleAppt methods
    public static Criterion<Appointment, List<Map<String, Object>>> getEmailIdsByCustomersExternalId(
            final String[]  customerMemberId, String customerName,Long profileId) {

        return new AppointmentCriterion <List<Map<String, Object>>>() {

            @Override
            public List<Map<String, Object>>  get(Session pSession, int pFetchMode) {

                List<Map<String, Object>> emailBodies=new ArrayList<Map<String,Object>>();

                //GSSP-253
                String sb="select Upper(p.email) as email,p.first_name as firstname,p.last_name as lastname "+
                        "from person p "+
                        "join customer c on p.person_id = c.person_id "+
                        "where c.EXTERNAL_ID in (:customerMemberId) and p.email is not null Union "+
                        "select Upper(ce.CUSTOMER_EMAIL) as email,p.first_name as firstname,p.last_name as lastname "+
                        "from person p "+
                        "join customer c on c.person_id=p.person_id "+
                        "join CUSTOMER_EMAIL ce on ce.external_customer_id=c.external_id "+
                        "where ce.EXTERNAL_CUSTOMER_ID in (:customerMemberId) and ce.CUSTOMER_EMAIL is not null";

                SQLQuery query = pSession.createSQLQuery(sb);
                query.setParameterList("customerMemberId", customerMemberId);
                query.addScalar("email", StandardBasicTypes.STRING);
                query.addScalar("firstname", StandardBasicTypes.STRING);
                query.addScalar("lastname", StandardBasicTypes.STRING);
                ScrollableResults scroll = query.scroll();
                Set<String> email=new HashSet<String>();
                while (scroll.next()) {
                    Map<String, Object> dataMap = new HashMap<String, Object>();
                    Object[] objects = scroll.get();
                    if(!email.contains((String)objects[0])){
                        email.add((String)objects[0]);

                        //GSSP-212_Prod Exception Changes :: email null check.
                        if(null != (String)objects[0])
                        {
                            dataMap.put(AppConstants.EMAIL_TYPE_TO, new String[]{(String)objects[0]});
                        }
                        dataMap.put("customerName", customerName.trim());


                        emailBodies.add(dataMap);
                    }
                }
                return emailBodies;
            }
        };
    }




    //Added for Scheduling Phase II - Update Service-LES 7
    /**
     *
     * @param InstructorId Long
     *
     * @return Criterion instance
     */

    public static AppointmentCriterion<Long> checkInstructorDBRecord(
            final Long instructorId) {

        return new AppointmentCriterion<Long>() {

            @Override
            public Long get(Session pSession, int pFetchMode) {
                String sb = " select count(*) as instructorCount from instructor " +
                        " where instructor_id = :instructorId";

                SQLQuery query =  pSession.createSQLQuery(sb);

                query.setLong("instructorId", instructorId);

                query.addScalar("instructorCount", StandardBasicTypes.LONG);

                Long count = (Long)query.uniqueResult();


                return count;
            }
        };

    }

    //Added for Scheduling Phase II - Update Service-LES 7
    /**
     *
     * @param ExternalID String
     *
     * @return Criterion instance
     */

    public static AppointmentCriterion<Long> checkCustomerExternalIdDBRecord(
            final String EXTERNAL_ID) {

        return new AppointmentCriterion<Long>() {

            @Override
            public Long get(Session pSession, int pFetchMode) {
                String sb = " select count(*) as customerCount from customer " +
                        " where EXTERNAL_ID = :EXTERNAL_ID";

                SQLQuery query = pSession.createSQLQuery(sb);

                query.setString("EXTERNAL_ID", EXTERNAL_ID);

                query.addScalar("customerCount", StandardBasicTypes.LONG);

                Long count = (Long)query.uniqueResult();

                return count;
            }
        };

    }


    //Added for Scheduling Phase II - update lesson service-LES 7
    /**
     * Returns a Criterion that can be used to find  person details based on bade Number
     *
     * @param external_id String
     *
     * @return Criterion instance
     */

    public static AppointmentCriterion<String> findTimeZoneByAptID(
            final String appointment_id) {

        return new AppointmentCriterion<String>() {

            @Override
            public String get(Session pSession, int pFetchMode) {
                String sb = " select lp.tz as timeZone from  location_profile lp " +
                        " join appointment a on a.profile_id= lp.profile_id " +
                        " where appointment_id= :appointment_id ";


                SQLQuery query = pSession.createSQLQuery(sb);

                query.setString("appointment_id", appointment_id);

                query.addScalar("timeZone", StandardBasicTypes.STRING);



                ScrollableResults scroll = query.scroll();


                String timeZone=null;
                while (scroll.next()) {

                    Object[] objects = scroll.get();

                    timeZone= (String)objects[0];

                }
                scroll.close();

                return timeZone;
            }
        };

    }


    // Code changes done for GSSP-197
    public static Criterion<Appointment, String> findEnvironment() {

        return new AppointmentCriterion<String>() {

            @Override
            public List<String> search(Session pSession, int pFetchMode) {
                String sb = "select  e.environment_name from environment e" +
                        " where e.environment_id=1";

                SQLQuery query = pSession.createSQLQuery(sb);


                ScrollableResults scroll = query.scroll();

                List<String> result = new ArrayList<String>();

                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    String environment_id = (String) objects[0];
                    result.add(environment_id);
                }
                scroll.close();

                return result;
            }
        };

    }


    // Added New report GSSP-185
    public static Criterion<Appointment, ActiveStudentsDTO> findActiveStudentsByActivityAndDateTime(
            final long pLocationId, final Date pStartTime, final Date pEndTime, final String pactivityType, final String pInstructorName) {

        return new AppointmentCriterion<ActiveStudentsDTO>() {

            @Override
            public List<ActiveStudentsDTO> search(Session pSession, int pFetchMode) {
                String firstName = null,lastName ="",check;
                check=pactivityType;
                SQLQuery query = null;


                StringBuilder sb1 = new StringBuilder("select to_char(t.start_time, 'DD-Mon-YYYY') as date1 ,t.appointment_id as appointment_id,");
                sb1.append(" t.start_time as start_time, t.end_time as end_time, t.canceled as canceled,");
                sb1.append(" (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration,");
                sb1.append(" r.profile_room_name as profile_room_name, a.activity_name as activity_name,");
                sb1.append(" i.instructor_id as instructor_id, p_i.first_name as i_first_name, p_i.last_name as i_last_name,");
                sb1.append(" c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name,p_c.email as student_email  " );
                sb1.append(" from appointment t");
                sb1.append(" join location l on t.profile_id = l.profile_id");
                sb1.append( " left join appointment_customers a_c on t.appointment_id = a_c.appointment_id");
                sb1.append(" left join customer c on a_c.customer_id = c.customer_id");
                sb1.append( " left join person p_c on c.person_id = p_c.person_id");
                sb1.append(" join instructor i on t.instructor_id = i.instructor_id");
                sb1.append(" left join person p_i on i.person_id = p_i.person_id");
                sb1.append( " left join room r on t.room_id = r.room_id");
                sb1.append(" left join activity a on t.activity_id = a.activity_id");
                sb1.append(" left join availability ay on i.availability_id = ay.availability_id");
                sb1.append(" where l.location_id = :locationId ");
                sb1.append(" and t.start_time >= :startTime ");
                sb1.append(" and t.end_time <= :endTime ");
                sb1.append( " and decode(t.canceled, null, 'N', t.canceled) != 'Y' ");


                if(null !=pactivityType && !pactivityType.equalsIgnoreCase("0") && !pactivityType.trim().equalsIgnoreCase("")  )
                {
                    sb1.append( " and t.activity_id=:activityType");
                }

                //GSSP-296 change  Instructor name compare after removal of whitespace and special character.
                String fullName=null;
                if(null != pInstructorName &&  ! pInstructorName.equalsIgnoreCase("") )
                {
                    fullName = pInstructorName.replaceAll("[^a-zA-Z0-9]","").toUpperCase();
                    sb1.append(" and UPPER(REGEXP_REPLACE(( p_i.FIRST_name|| p_i.Last_name), '[^0-9A-Za-z]', '')) = :fullName " );
                }

                sb1.append(" order by to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS')");

                query = pSession.createSQLQuery(sb1.toString());
                query.setLong("locationId", pLocationId);
                query.setTimestamp("startTime", pStartTime);
                query.setTimestamp("endTime", pEndTime);

                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("appointment_id", StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("end_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("canceled", StandardBasicTypes.STRING);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.addScalar("profile_room_name", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("instructor_id", StandardBasicTypes.LONG);
                query.addScalar("i_first_name", StandardBasicTypes.STRING);
                query.addScalar("i_last_name", StandardBasicTypes.STRING);
                query.addScalar("customer_id", StandardBasicTypes.LONG);
                query.addScalar("c_first_name", StandardBasicTypes.STRING);
                query.addScalar("c_last_name", StandardBasicTypes.STRING);
                query.addScalar("student_email", StandardBasicTypes.STRING);

                if(null != pInstructorName &&  !pInstructorName.equalsIgnoreCase("") )
                {
                    //GSSP-296 change  Full Name
                    query.setString("fullName", fullName.trim());

                }

                if(null !=pactivityType && !pactivityType.equalsIgnoreCase("0") && !pactivityType.trim().equalsIgnoreCase(""))
                {
                    query.setString("activityType", pactivityType);
                }

                ScrollableResults scroll = query.scroll();
                List<ActiveStudentsDTO> result = new ArrayList<ActiveStudentsDTO>();
                Map<Long, ActiveStudentsDTO> activeStudentsDTOs = new HashMap<Long, ActiveStudentsDTO>();
                Map<Long, Instructor> instructors = new HashMap<Long, Instructor>();
                Map<Long, Customer> customers = new HashMap<Long, Customer>();

                //Logic for getting the instructor details from person table
                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    String date1 = (String) objects[0];
                    Long appointment_id = (Long) objects[1];
                    Long instructorId = (Long) objects[8];
                    Instructor instructor = instructors.get(instructorId);
                    if (instructor == null) {
                        instructor = new Instructor();
                        instructor.setInstructorId(instructorId);
                        Person person = new Person();
                        person.setFirstName((String) objects[9]);
                        person.setLastName((String) objects[10]);
                        instructor.setPerson(person);
                        instructors.put(instructorId, instructor);
                    }

                    //Populating the DTO for Active Students
                    ActiveStudentsDTO activeStudentsDTO = activeStudentsDTOs.get(appointment_id);
                    if (activeStudentsDTO == null) {
                        activeStudentsDTO = new ActiveStudentsDTO( date1,appointment_id, (Date) objects[2], (Date) objects[3], (String) objects[4], (Long) objects[5], (String) objects[6], (String) objects[7], instructor,(String) objects[14]);
                        activeStudentsDTOs.put(appointment_id, activeStudentsDTO);
                        result.add(activeStudentsDTO);
                    }

                    //Adding customers details to Active Students DTO
                    Long customerId = (Long) objects[11];
                    if (customerId == null) continue;
                    Customer customer = customers.get(customerId);
                    if (customer == null) {
                        customer = new Customer();
                        customer.setCustomerId(customerId);
                        Person person = new Person();
                        person.setFirstName((String) objects[12]);
                        person.setLastName((String) objects[13]);
                        customer.setPerson(person);
                        customers.put(customerId, customer);
                    }
                    activeStudentsDTO.addCustomer(customer);
                  
                }                scroll.close();
                return result;
            }
        };
    }




    // Added New report GSSP-185
    public static Criterion<Appointment, ActivityDTO> findLessonTypes( final long pLocationId) {

        return new AppointmentCriterion<ActivityDTO>() {

            @Override
            public  List<ActivityDTO> search(Session pSession, int pFetchMode) {

                SQLQuery query = null;


                String sb = "select a.activity_id,a.activity_name from activity a" +
                        " join profile_activity pa on pa.activity_id = a.activity_id" +
                        " join location l on l.profile_id = pa.profile_id"		+
                        " where l.location_id=:locationId and a.service_id=1 and a.activity_id!=20 order by a.activity_name ";

                query = pSession.createSQLQuery(sb);

                query.setLong("locationId", pLocationId);

                query.addScalar("activity_id", StandardBasicTypes.LONG);
                query.addScalar("activity_name", StandardBasicTypes.STRING);


                ScrollableResults scroll = query.scroll();
                List<ActivityDTO> result = new ArrayList<ActivityDTO>();
                ActivityDTO dto = null;


                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    dto = new ActivityDTO((Long) objects[0], (String) objects[1]);
                    result.add(dto);
                }

                scroll.close();
                return result;
            }
        };
    }




    // Added New report GSSP-203
    public static Criterion<Appointment, ActiveStudentsDTO> findStudentsCheckInByDateandTime(
            final long pLocationId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<ActiveStudentsDTO>() {

            @Override
            public List<ActiveStudentsDTO> search(Session pSession, int pFetchMode) {
                SQLQuery query = null;


                StringBuilder sb1 = new StringBuilder("select to_char(t.start_time, 'DD-Mon-YYYY') as date1 ,t.appointment_id as appointment_id,");
                sb1.append(" t.start_time as start_time, t.end_time as end_time, t.canceled as canceled,");
                sb1.append(" (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration,");
                sb1.append(" r.profile_room_name as profile_room_name, a.activity_name as activity_name,");
                sb1.append(" i.instructor_id as instructor_id, p_i.first_name as i_first_name, p_i.last_name as i_last_name,");
                sb1.append(" c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name,p_c.email as student_email  " );
                sb1.append(" from appointment t");
                sb1.append(" join location l on t.profile_id = l.profile_id");
                // GSSP-333 lesson types without customers should not be visible in Student check-in sheet.
                sb1.append( " join appointment_customers a_c on t.appointment_id = a_c.appointment_id");
                sb1.append(" left join customer c on a_c.customer_id = c.customer_id");
                sb1.append( " left join person p_c on c.person_id = p_c.person_id");
                sb1.append(" join instructor i on t.instructor_id = i.instructor_id");
                sb1.append(" left join person p_i on i.person_id = p_i.person_id");
                sb1.append( " left join room r on t.room_id = r.room_id");
                sb1.append(" left join activity a on t.activity_id = a.activity_id");
                sb1.append(" left join availability ay on i.availability_id = ay.availability_id");
                sb1.append(" where l.location_id = :locationId ");
                sb1.append(" and t.start_time >= :startTime ");
                // GSSP-333 lesson types without customers should not be visible in Student check-in sheet.
                sb1.append(" and t.start_time <= :endTime ");
                sb1.append( " and decode(t.canceled, null, 'N', t.canceled) != 'Y' ");
                sb1.append(" order by to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS')");

                query = pSession.createSQLQuery(sb1.toString());
                query.setLong("locationId", pLocationId);
                query.setTimestamp("startTime", pStartTime);
                query.setTimestamp("endTime", pEndTime);

                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("appointment_id", StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("end_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("canceled", StandardBasicTypes.STRING);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.addScalar("profile_room_name", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("instructor_id", StandardBasicTypes.LONG);
                query.addScalar("i_first_name", StandardBasicTypes.STRING);
                query.addScalar("i_last_name", StandardBasicTypes.STRING);
                query.addScalar("customer_id", StandardBasicTypes.LONG);
                query.addScalar("c_first_name", StandardBasicTypes.STRING);
                query.addScalar("c_last_name", StandardBasicTypes.STRING);
                query.addScalar("student_email", StandardBasicTypes.STRING);



                ScrollableResults scroll = query.scroll();
                List<ActiveStudentsDTO> result = new ArrayList<ActiveStudentsDTO>();
                Map<Long, ActiveStudentsDTO> activeStudentsDTOs = new HashMap<Long, ActiveStudentsDTO>();
                Map<Long, Instructor> instructors = new HashMap<Long, Instructor>();
                Map<Long, Customer> customers = new HashMap<Long, Customer>();

                //Logic for getting the instructor details from person table
                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    String date1 = (String) objects[0];
                    Long appointment_id = (Long) objects[1];
                    Long instructorId = (Long) objects[8];
                    Instructor instructor = instructors.get(instructorId);
                    if (instructor == null) {
                        instructor = new Instructor();
                        instructor.setInstructorId(instructorId);
                        Person person = new Person();
                        person.setFirstName((String) objects[9]);
                        person.setLastName((String) objects[10]);
                        instructor.setPerson(person);
                        instructors.put(instructorId, instructor);
                    }

                    //Populating the DTO for Active Students
                    ActiveStudentsDTO activeStudentsDTO = activeStudentsDTOs.get(appointment_id);
                    if (activeStudentsDTO == null) {
                        activeStudentsDTO = new ActiveStudentsDTO( date1,appointment_id, (Date) objects[2], (Date) objects[3], (String) objects[4], (Long) objects[5], (String) objects[6], (String) objects[7], instructor,(String) objects[14]);
                        activeStudentsDTOs.put(appointment_id, activeStudentsDTO);
                        result.add(activeStudentsDTO);
                    }

                    //Adding customers details to Active Students DTO
                    Long customerId = (Long) objects[11];
                    if (customerId == null) continue;
                    Customer customer = customers.get(customerId);
                    if (customer == null) {
                        customer = new Customer();
                        customer.setCustomerId(customerId);
                        Person person = new Person();
                        person.setFirstName((String) objects[12]);
                        person.setLastName((String) objects[13]);
                        customer.setPerson(person);
                        customers.put(customerId, customer);
                    }
                    activeStudentsDTO.addCustomer(customer);

                }                scroll.close();
                return result;
            }
        };
    }
    //GSSP-205 added for new report
    //GSSP-271 changes made for student Inactive
    public static Criterion<Appointment, InActiveStudentsDTO> findInActiveStudentsByDate(
            final String locationId, String externalId) {

        return new AppointmentCriterion<InActiveStudentsDTO>() {

            @Override
            public List<InActiveStudentsDTO> search(Session pSession, int pFetchMode) {
                SQLQuery query = null;
                String sb = "select c2.customer_id as customer_id,p.FIRST_NAME as c_first_name,p.LAST_NAME as c_last_name,p.EMAIL as Student_Email,p.PHONE as Student_Phone from customer c2 " +
                        " join person p on p.person_id=c2.person_id " +
                        " where c2.customer_id not in " +
                        " (select distinct(c.customer_id) from customer c " +
                        " join appointment_customers ac on c.customer_id = ac.customer_id " +
                        " join appointment a on a.APPOINTMENT_ID=ac.APPOINTMENT_ID " +
                        " where a.CANCELED='N' and a.START_TIME>sysdate-180 and c.external_id like :locationId and c.customer_status_id in (0,20)) " +
                        " and c2.external_id like :locationId and c2.customer_status_id in (0,20)  order by (p.FIRST_NAME || p.last_name) ";
                query = pSession.createSQLQuery(sb);

                query.setString("locationId", locationId + "%");
                query.addScalar("customer_id", StandardBasicTypes.LONG);
                query.addScalar("c_first_name", StandardBasicTypes.STRING);
                query.addScalar("c_last_name", StandardBasicTypes.STRING);
                query.addScalar("Student_Email", StandardBasicTypes.STRING);
                query.addScalar("Student_Phone", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();
                List<InActiveStudentsDTO> result = new ArrayList<InActiveStudentsDTO>();
                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    String customerFirstName = (String) objects[1];
                    String customerLastName = (String) objects[2];
                    String customerEmail=(String) objects[3];
                    String customerPhone=(String) objects[4];


                    InActiveStudentsDTO dto = new InActiveStudentsDTO(customerFirstName, customerLastName, customerEmail,customerPhone,externalId);

                    result.add(dto);

                }

                scroll.close();

                return result;
            }
        };
    }

    public static AppointmentCriterion<String> logService(
            final ServiceLogger serviceLogger) {
        return new AppointmentCriterion<String>() {
            @Override
            public String get(Session pSession, int pFetchMode) {
                pSession.save(serviceLogger);
                return "";             }
        };

    }

    /**
     * Added for New Excel Report
     *
     *
     * @param startDate
     * @param endDate
     * @return
     */
    // GSSP-213 new report
    public static Criterion<Appointment, ExportDetailsDTO> findProfileByDate(
            final Map<String,Boolean> daysofWeek, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<ExportDetailsDTO>() {

            @Override
            public List<ExportDetailsDTO> search(Session pSession, int pFetchMode) {

                SQLQuery query = null;


                DateFormat outputFormatter = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH);

                final Date startTime = new Date(pStartTime.getTime());
                final Date endTime = new Date(pEndTime.getTime());

                String comparStartDate = outputFormatter.format(startTime);
                String comparEndDate = outputFormatter.format(endTime);

                StringBuilder sb = new StringBuilder(" select ttv.profile3 as profileId, nvl(actvc.appointment_count,0) as active_appointments,  " );
                sb.append(  " nvl(actvc.active_student_count,0) as activestudents,  nvl(ctvc.canceled_appointment_count,0) as canceled_appointments_count , " );
                sb.append(  " ttv.location_name as location,ttv.store_number as store_number,  ttv.service_count as service_Count, ttv.room_count as room_Count, " );
                sb.append(  " ttv.activity_count as activity_Count ,avl.sundayTime as sundayTime,avl.mondayTime as mondayTime,avl.tuesdayTime as tuesdayTime , " );
                sb.append(  " avl.wednesdayTime as wednesdayTime,avl.thursdayTime as thursdayTime ,avl.fridayTime as fridayTime,avl.saturdayTime as saturdayTime,  " );
                sb.append(  " nvl(tc.total_conflicts,0) as totalConflicts,nvl(tiv.total_instructor,0) as total_instructors " );
                sb.append(  " from  ( select  l.profile_id as  profile3, l.location_name as Location_name, " );
                sb.append(  " l.EXTERNAL_ID as Store_Number,  count( distinct(ps.service_id)) as service_count, count(distinct(r.room_id)) as room_count, " );
                sb.append(  " count(distinct(pa.activity_id)) as activity_count " );
                sb.append(  " from location l  left join location_profile lp  on l.profile_id = lp.profile_id  " );
                sb.append(  " left join profile_service ps on l.profile_id = ps.profile_id  left join room r on l.profile_id = r.profile_id   " );
                sb.append(  " left join profile_activity pa on l.profile_id = pa.profile_id   " );
                sb.append(  " left join availability a on lp.availability_id = a.availability_id " );
                sb.append(  " where lp.enabled='Y' " );
                sb.append(  " and r.Enabled ='Y'  group by l.profile_id,l.location_name,l.external_id order by l.location_name) ttv " );
                sb.append(  " left outer join  " );
                sb.append( " (select l.profile_id as profile4, " );
                sb.append( " to_char(a.sunday_start_time,'hh.mi am') || ' - ' || to_char(a.sunday_end_time,'hh.mi.ss am') as sundayTime, " );
                sb.append( " to_char(a.monday_start_time,'hh.mi am')  || ' - ' || to_char(a.monday_end_time,'hh.mi.ss am') as mondayTime, " );
                sb.append( " to_char(a.tuesday_start_time,'hh.mi am') || ' - ' || to_char(a.tuesday_end_time,'hh.mi.ss am') as tuesdayTime, " );
                sb.append( " to_char(a.wednesday_start_time,'hh.mi am') || ' - ' || to_char(a.wednesday_end_time,'hh.mi.ss am') as wednesdayTime, " );
                sb.append( " to_char(a.thursday_start_time,'hh.mi am') || ' - ' || to_char(a.thursday_end_time,'hh.mi.ss am') as thursdayTime, " );
                sb.append( " to_char(a.friday_start_time,'hh.mi am') || ' - ' || to_char(a.friday_end_time,'hh.mi.ss am') as fridayTime, " );
                sb.append( " to_char(a.saturday_start_time,'hh.mi am') || ' - ' || to_char(a.saturday_end_time,'hh.mi.ss am') as saturdayTime " );
                sb.append( " from location l " );
                sb.append( " join location_profile lp on l.profile_id = lp.profile_id " );
                sb.append( " join AVAILABILITY a on a.availability_id = lp.availability_id) avl " );
                sb.append( " left outer join   " );
                sb.append(  " (select l.profile_id as profile6, count(distinct(instructor_id)) as total_instructor " );
                sb.append(  " from location l " );
                sb.append(  " join instructor i on l.location_id = i.location_id " );
                sb.append(  " join availability a on a.availability_id = i.availability_id " );
                sb.append(  " where i.enabled='Y' and i.status='A' and ( " );

                if(daysofWeek.containsKey(AppConstants.WEEKEND_CHOSE[0]))
                {
                    sb.append( "a.sunday_start_time is not null or ");
                }

                if(daysofWeek.containsKey(AppConstants.WEEKEND_CHOSE[1]))
                {
                    sb.append( "a.monday_start_time is not null or ");
                }

                if(daysofWeek.containsKey(AppConstants.WEEKEND_CHOSE[2]))
                {
                    sb.append( "a.tuesday_start_time is not null or ");
                }
                if(daysofWeek.containsKey(AppConstants.WEEKEND_CHOSE[3]))
                {
                    sb.append( "a.wednesday_start_time is not null or ");
                }
                if(daysofWeek.containsKey(AppConstants.WEEKEND_CHOSE[4]))
                {
                    sb.append( "a.thursday_start_time is not null or ");
                }
                if(daysofWeek.containsKey(AppConstants.WEEKEND_CHOSE[5]))
                {
                    sb.append( "a.friday_start_time is not null or ");
                }

                if(daysofWeek.containsKey(AppConstants.WEEKEND_CHOSE[6]))
                {
                    sb.append( "a.saturday_start_time is not null or ");
                }

                int lastIndexofOr = sb.lastIndexOf("or");
                sb.delete(lastIndexofOr , lastIndexofOr+2);

                sb.append( ") group by profile_id) tiv " );
                sb.append( " left outer join  " );

                sb.append( " (select l.profile_id as profile1,  count(distinct(ac.customer_id)) as active_student_count,   " );
                sb.append( " count(ap.appointment_id) as appointment_count  from appointment ap   " );
                sb.append( " join appointment_customers ac on ap.appointment_id = ac.appointment_id  join location l on l.profile_id = ap.profile_id  " );

                if(comparStartDate.equals(comparEndDate))
                {
                    sb.append( " where trunc(ap.start_time) = :startTime and trunc(ap.end_time)  = :endTime and ap.canceled ='N'  group by l.profile_id) actvc " );
                }
                else
                {
                    sb.append( " where trunc(ap.start_time) >= :startTime and trunc(ap.end_time)  <= :endTime and ap.canceled ='N'  group by l.profile_id) actvc " );
                }
                sb.append( " left outer join   " );
                sb.append( " ( select l.profile_id as profile2,  count(ap.appointment_id) as canceled_appointment_count  from appointment ap " );
                sb.append( " join location l on l.profile_id = ap.profile_id  " );

                if(comparStartDate.equals(comparEndDate))
                {
                    sb.append( " where trunc(ap.start_time) = :startTime and trunc(ap.end_time)  = :endTime and ap.canceled ='Y'  group by l.profile_id) ctvc  " );
                }
                else
                {
                    sb.append( " where trunc(ap.start_time) >= :startTime and trunc(ap.end_time)  <= :endTime and ap.canceled ='Y'  group by l.profile_id) ctvc  " );
                }
                sb.append( " left outer join " );
                sb.append( " (select t.profile_id  as profile5, count(t.appointment_id) as total_conflicts from appointment t " );
                sb.append( "  join appointment t2 on  " );
                sb.append( "    t.profile_id = t2.profile_id and (t.room_id = t2.room_id or t.instructor_id = t2.instructor_id) " );
                sb.append( "  and t.appointment_id != t2.appointment_id " );
                if(comparStartDate.equals(comparEndDate))
                {

                    sb.append( "  and trunc(t.start_time) = :startTime" );
                    sb.append( "  and trunc(t2.start_time) = :startTime" );
                    sb.append( "  and trunc(t.end_time) = :endTime" );
                    sb.append( "  and trunc(t2.end_time)= :endTime" );

                }

                else
                {

                    sb.append( "  and trunc(t.start_time) >= :startTime" );
                    sb.append( "  and trunc(t2.start_time) >= :startTime" );
                    sb.append( "  and trunc(t.end_time) <= :endTime" );
                    sb.append( "  and trunc(t2.end_time) <= :endTime" );
                }

                sb.append( "  and (t2.start_time >= t.start_time and t2.start_time < t.end_time " );
                sb.append( "  or t2.end_time > t.start_time and t2.end_time <= t.end_time " );
                sb.append( "  or t.start_time >= t2.start_time and t.end_time <= t2.end_time) " );
                sb.append( "  where t.canceled = 'N' " );
                sb.append( "  and t2.canceled = 'N'  " );
                sb.append( "  group by t.profile_id " );
                sb.append( "  ) tc " );
                sb.append( " on tc.profile5 = ctvc.profile2 " );
                sb.append( " on ctvc.profile2 = actvc.profile1   " );
                sb.append( " on actvc.profile1  = tiv.profile6  " );
                sb.append( " on tiv.profile6     = avl.profile4  " );
                sb.append( " on avl.profile4  = ttv.profile3  ");



                query = pSession.createSQLQuery(sb.toString());
                query.setDate("startTime", pStartTime);
                query.setDate("endTime", pEndTime);



                query.addScalar("profileId", StandardBasicTypes.LONG);
                query.addScalar("active_appointments", StandardBasicTypes.LONG);
                query.addScalar("activestudents", StandardBasicTypes.LONG);
                query.addScalar("canceled_appointments_count", StandardBasicTypes.LONG);
                query.addScalar("location", StandardBasicTypes.STRING);
                query.addScalar("store_number", StandardBasicTypes.LONG);
                query.addScalar("service_Count", StandardBasicTypes.LONG);
                query.addScalar("room_Count", StandardBasicTypes.LONG);
                query.addScalar("activity_Count", StandardBasicTypes.LONG);
                query.addScalar("sundayTime",StandardBasicTypes.STRING);
                query.addScalar("mondayTime",StandardBasicTypes.STRING);
                query.addScalar("tuesdayTime",StandardBasicTypes.STRING);
                query.addScalar("wednesdayTime",StandardBasicTypes.STRING);
                query.addScalar("thursdayTime",StandardBasicTypes.STRING);
                query.addScalar("fridayTime",StandardBasicTypes.STRING);
                query.addScalar("saturdayTime",StandardBasicTypes.STRING);
                query.addScalar("totalConflicts",StandardBasicTypes.LONG);
                query.addScalar("total_instructors",StandardBasicTypes.LONG);


                ScrollableResults scroll = query.scroll();
                List<ExportDetailsDTO> result = new ArrayList<ExportDetailsDTO>();

                List<String> weekList;
                String[] weeksList ;
                while (scroll.next()) {
                    Object[] objects = scroll.get();

                    Long profileId = (Long) objects[0];

                    Long activeAppointments = (Long) objects[1];
                    Long activeStudents=(Long) objects[2];
                    Long canceledAppointmentsCount=(Long) objects[3];
                    String location=(String) objects[4];
                    Long storeNumber=(Long) objects[5];
                    Long serviceCount=(Long) objects[6];
                    Long roomCount=(Long) objects[7];
                    Long activityCount=(Long) objects[8];
                    Long totalConflicts = (Long) objects[16];
                    Long totalinstructors = (Long) objects[17];
                    weekList = new ArrayList<String>();

                    for(int i= 9 ; i < 16; i++)
                    {
                        weekList.add(i-9,(String) objects[i]);

                    }

                    weeksList = new String[weekList.size()];
                    weeksList = weekList.toArray(weeksList);


                    String studioTiming = formStudioHours(daysofWeek,weeksList);

                    ExportDetailsDTO dto = new ExportDetailsDTO(profileId,activeAppointments, activeStudents, canceledAppointmentsCount, location, storeNumber, serviceCount, roomCount,
                            activityCount,studioTiming , totalConflicts, totalinstructors);
                    result.add(dto);

                }

                scroll.close();

                return result;
            }        };
    }


    private static String formStudioHours(Map<String,Boolean> daysofWeek,String[] weekList)
    {
        StringBuffer studiHours = new StringBuffer();



        for(int i= 0 ; i < 7; i++)
        {

            if(!weekList[i].equalsIgnoreCase(" - "))
            {

                if(daysofWeek.containsKey(AppConstants.WEEKEND_CHOSE[i]))
                {
                    studiHours.append(AppConstants.WEEKEND_CHOSE[i] + ":");
                    studiHours.append(weekList[i]);
                    studiHours.append(System.lineSeparator());
                }
            }

        }

        return studiHours.toString();
    }
    public static AppointmentCriterion<String> logService1(
            final AppointmentLog appointmentLog) {
        return new AppointmentCriterion<String>() {
            @Override
            public String get(Session pSession, int pFetchMode) {
                pSession.save(appointmentLog);
                return "";             }
        };

    }
    //GSSP-250
    public static AppointmentCriterion<String> cancelService(
            final AppointmentCancel appointmentCancel) {
        return new AppointmentCriterion<String>() {
            @Override
            public String get(Session pSession, int pFetchMode) {

                pSession.save(appointmentCancel);
                return "";             }
        };

    }
    /**
     * Added for New Report GSSP-210
     *
     * @param location
     * @param startDate
     * @param endDate
     * @param inputExternalId
     * @return
     */
    public static Criterion<Appointment, AppointmentHistoryDTO> findAppointmentHistory(
            final Date pStartDate, final Date pEndDate, final String pInputExternalId) {

        return new AppointmentCriterion<AppointmentHistoryDTO>() {

            @Override
            public List<AppointmentHistoryDTO> search(Session pSession, int pFetchMode) {


                DateFormat outputFormatter = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_HIPHEN);


                String comparStartDate = outputFormatter.format(pStartDate);
                String comparEndDate = outputFormatter.format(pEndDate);
                //SQLQuery query = null;
                String sb = " select al.APPOINTMENT_ID as APPOINTMENT_ID,(pu.FIRST_NAME||' '||pu.LAST_NAME) as Updated_By,to_char(al.UPDATED,'MM/DD/YYYY hh:mi.ss am') as Updated_timestamp, " +
                        " (p_c.FIRST_NAME||' '||p_c.LAST_NAME) as customer_name,  s.SERVICE_NAME as Service, a.ACTIVITY_NAME as Lesson_Type," +
                        " to_char(al.START_TIME,'MM/DD/YYYY')as Start_Date  ,to_char(al.end_time ,'MM/DD//YYYY')as End_Date, " +
                        " ass.IS_RECURRING as IS_Recurring_Appointment,  to_char(al.START_TIME,'hh:mi am') as Time,   " +
                        " (extract(day from ((al.end_time - al.start_time) * 24 * 60 ))) as duration, " +
                        " (pi.FIRST_NAME || ' '  || pi.LAST_NAME) as instructor_name, " +
                        " rm.PROFILE_ROOM_NAME as Room,nvl(al.NOTE,'-') as Note,al.CANCELED as IS_Cancelled " +
                        "  from appointment_log al " +
                        "  left join instructor ins on al.INSTRUCTOR_ID=ins.INSTRUCTOR_ID " +
                        " left join person pi on pi.person_id=ins.person_id " +
                        " join room rm on rm.ROOM_ID=al.ROOM_ID " +
                        " join person pu on pu.PERSON_ID=al.UPDATED_BY  " +
                        " join APPOINTMENT_CUSTOMERS ac on ac.APPOINTMENT_ID=al.APPOINTMENT_ID " +
                        " join APPOINTMENT ap on ap.APPOINTMENT_ID=al.APPOINTMENT_ID " +
                        " join appointment_series ass on ap.APPOINTMENT_SERIES_ID=ass.APPOINTMENT_SERIES_ID  " +
                        " join customer c on ac.CUSTOMER_ID=c.CUSTOMER_ID " +
                        " join activity a on a.ACTIVITY_ID=al.ACTIVITY_ID " +
                        " join person p_c on c.PERSON_ID=p_c.PERSON_ID " +
                        " join SERVICE s on s.SERVICE_ID=a.SERVICE_ID " +
                        " where trunc(ap.START_TIME)>=:startDate and " +
                        " trunc(ap.END_TIME)<=:endDate and " +
                        " c.EXTERNAL_ID= :inputExternalId " +
                        " order by al.appointment_id,to_char(al.START_TIME,'DD/MM/YYYY'),to_char(al.START_TIME,'hh:mi am'),al.UPDATED ";
                SQLQuery query = pSession.createSQLQuery(sb);

                query.setString("startDate", comparStartDate);
                query.setString("endDate", comparEndDate);
                query.setString("inputExternalId", pInputExternalId);
                query.addScalar("appointment_id", StandardBasicTypes.STRING);
                query.addScalar("Updated_By", StandardBasicTypes.STRING);
                query.addScalar("Updated_timestamp", StandardBasicTypes.STRING);
                query.addScalar("customer_name", StandardBasicTypes.STRING);
                query.addScalar("Service", StandardBasicTypes.STRING);
                query.addScalar("Lesson_Type", StandardBasicTypes.STRING);
                query.addScalar("Start_Date", StandardBasicTypes.STRING);
                query.addScalar("End_Date", StandardBasicTypes.STRING);
                query.addScalar("IS_Recurring_Appointment", StandardBasicTypes.STRING);
                query.addScalar("Time", StandardBasicTypes.STRING);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.addScalar("instructor_name", StandardBasicTypes.STRING);
                query.addScalar("Room", StandardBasicTypes.STRING);
                query.addScalar("Note", StandardBasicTypes.STRING);
                query.addScalar("IS_Cancelled", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();
                List<AppointmentHistoryDTO> result = new ArrayList<AppointmentHistoryDTO>();

                //Logic for getting the instructor details from person table
                while (scroll.next()) {

                    Object[] objects = scroll.get();
                    String appointment_id = (String) objects[0];
                    String updatedBy=(String) objects[1];
                    String updatedTimestamp=(String) objects[2];
                    String customerName=(String) objects[3];
                    String serviceType=(String) objects[4];
                    String lessonType=(String) objects[5];
                    String startDate = (String) objects[6];
                    String endDate = (String) objects[7];
                    String isRecurring=(String) objects[8];
                    String time=(String) objects[9];
                    Long duration = (Long)objects[10];
                    String instructorName = (String) objects[11];
                    String room=(String) objects[12];
                    String note=(String) objects[13];
                    String isCancelled=(String) objects[14];

                    //Populating the DTO for appointment history
                    AppointmentHistoryDTO appointmentHistoryDTO = null;

                    appointmentHistoryDTO = new AppointmentHistoryDTO(appointment_id,updatedBy,updatedTimestamp, customerName,
                            serviceType,lessonType,startDate, endDate, isRecurring,time,duration,instructorName,room,note,isCancelled);

                    result.add(appointmentHistoryDTO);

                }
                scroll.close();
                return result;
            }
        };
    }


    //Added for GSSP-230
    /**
     * Returns a Criterion that can be used to find Instructors who are terminated but are disabled
     *
     *
     * @return Criterion instance
     */
    public static Criterion<Appointment, Map<Long,List<String>>> findTerminatedEnabledInstructor() {

        return new AppointmentCriterion<Map<Long,List<String>>>() {

            @Override
            public List<Map<Long,List<String>>> search(Session pSession, int pFetchMode) {



                String sb = "select i.location_id  as location_id,i.external_id as external_id, p.first_name as first_name," +
                        " p.last_name as last_name" +
                        " from instructor i " +
                        " join person p on p.person_id = i.person_id " +
                        " where status='T' and enabled='Y' order by location_id ";


                SQLQuery query = pSession.createSQLQuery(sb);

                query.addScalar("location_id", StandardBasicTypes.LONG);
                query.addScalar("external_id", StandardBasicTypes.STRING);
                query.addScalar("first_name", StandardBasicTypes.STRING);
                query.addScalar("last_name", StandardBasicTypes.STRING);



                ScrollableResults scroll = query.scroll();

                List<Map<Long,List<String>>> result = new ArrayList<Map<Long,List<String>>>();
                Map<Long,List<String>> cache = new HashMap<Long,List<String>>();
                //  List<String> emailIDList = new ArrayList<String>();


                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    Long location_id = (Long) objects[0];

                    if (!cache.containsKey(location_id)) {

                        List<String> instructorList = new ArrayList<String>();
                        cache.put(location_id, instructorList);

                    }
                    List<String> instructorList = cache.get(location_id);


                    String externalId = (String) objects[1];
                    String firstName = (String) objects[2];
                    String lastName = (String) objects[3];

                    String fullName = StringUtils.isBlank(firstName)?"":firstName + " " + (StringUtils.isBlank(lastName) ? "":lastName);

                    if( null  !=  externalId && ! StringUtils.isEmpty(fullName))
                    {
                        instructorList.add(externalId  + "  " + fullName  );
                    }




                }
                scroll.close();
                result.add(cache);

                return result;
            }
        };

    }


    //-- Phase  Changes GSSP-295 Over loaded findInstructorAvailableHours method ---------------------------
    public static Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> findInstructorAvailableHours(
            final List<String> instructorId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<Map<String, List<InstructorAvailableHoursDTO>>>() {

            @Override
            public Map<String, List<InstructorAvailableHoursDTO>> get(Session pSession, int pFetchMode) {

                String sb = "select to_char(t.start_time, 'yyyy-MM-dd') as date1 , " +
                        "to_char(t.start_time,'HH24:Mi') as start_time, to_char(t.end_time,'HH24:Mi') as end_time,t.instructor_id  AS instructor_id from appointment t " +
                        "where t.start_time >= :pStartTime and t.end_time <= :pEndTime and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        "and t.instructor_id in (:instructorId)  minus  " ;
                sb = sb+" SELECT  TO_CHAR(t.start_time, 'yyyy-MM-dd') AS date1 , TO_CHAR(t.start_time,'HH24:Mi')     AS start_time, TO_CHAR(t.end_time,'HH24:Mi') AS end_time,t.instructor_id  AS instructor_id FROM (SELECT * FROM  appointment t  WHERE  t.start_time >= :pStartTime AND  t.end_time <= :pEndTime  AND   DECODE(t.canceled, NULL, 'N', t.canceled) != 'Y' AND t.instructor_id    IN (:instructorId) ) t, (select * from   onetime st where  st.instructor_id   IN (:instructorId)   ) ot WHERE t.instructor_id = ot.instructor_id AND t.start_time between ot.start_time and TO_DATE('01-03-9999','DD-MM-YYYY') AND t.end_time between  sysdate  and  ot.end_time  ORDER BY date1 DESC";

                SQLQuery query = pSession.createSQLQuery(sb);
                query.setParameterList("instructorId", instructorId);
                query.setDate("pStartTime", pStartTime);
                query.setDate("pEndTime", pEndTime);

                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.STRING);
                query.addScalar("instructor_id", StandardBasicTypes.STRING);

                Map<String, List<InstructorAvailableHoursDTO>> cache = new HashMap<String, List<InstructorAvailableHoursDTO>>();

                InstructorAvailableHoursDTO instructorAvailableHoursDTO = null;

                List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList = null;

                ScrollableResults scroll = query.scroll();

                while (scroll.next()) {

                    Object[] objects = scroll.get();

                    //Append InstructorDate with InstructorId
                    String instructorDate = (String) objects[0]+"~and~"+objects[3] ;

                    if (!cache.containsKey(instructorDate)) {

                        List<InstructorAvailableHoursDTO> list = new ArrayList<InstructorAvailableHoursDTO>();
                        cache.put(instructorDate, list);

                    }

                    instructorAvailableHoursDTOList = cache.get(instructorDate);

                    DateTimeFormatter df1 = DateTimeFormat.forPattern(DateTimeUtil.HOURS_PATTERN);

                    String startTime = (String) objects[1];
                    String endTime = (String) objects[2];
                    instructorAvailableHoursDTO = new InstructorAvailableHoursDTO(df1.parseLocalTime(startTime),df1.parseLocalTime(endTime)) ;

                    instructorAvailableHoursDTOList.add(instructorAvailableHoursDTO);
                }

                return cache;

            }
        };

    }


    //--   Changes CRMI-338 --Method for get Map of Appointment Data -------
    Map<String,  CRMAppointmentDataFileDTO> cRMAppointmentDataMap = null;
    public static Criterion<Appointment,Map<String,  CRMAppointmentDataFileDTO>> getCRMAppointmentDataFile(String dateInString) {
        return new AppointmentCriterion<Map<String,  CRMAppointmentDataFileDTO>>() {

            @Override
            @SuppressWarnings("unchecked")
            public Map<String,  CRMAppointmentDataFileDTO> get(Session pSession, int pFetchMode) {
                cRMAppointmentDataMap =   new HashMap<String,CRMAppointmentDataFileDTO>();
                String sb = "SELECT   'GC' AS BRAND_CD, "+
                        "'POS' AS ACCT_SRC_CD, "+
                        "C.EXTERNAL_ID AS ACCT_SRC_NBR, "+
                        "A.APPOINTMENT_ID AS APPT_CD, "+
                        "B.CUSTOMER_ID AS APPT_CUST_CD, "+
                        "C.CUSTOMER_STATUS_ID AS CUST_STS_CD, "+
                        "D.STATUS_NAME AS CUST_STS_DESC, "+
                        "C.PERSON_ID AS APPT_STUDT_CD, "+
                        "E.FIRST_NAME AS STUDT_FST_NAME, "+
                        "E.LAST_NAME AS STUDT_LST_NAME, "+
                        "E.EMAIL AS STUDT_EMAIL, "+
                        "E.PHONE AS STUDT_PHN, "+
                        "A.ACTIVITY_ID AS APPT_ACTVTY_CD, "+
                        "F.ACTIVITY_NAME AS ACTVTY_DESC, "+
                        "F.MINIMUM_DURATION AS ACTVTY_MIN_DUR, "+
                        "F.MAXIMUM_DURATION AS ACTVTY_MAX_DUR, "+
                        "F.SERVICE_ID AS ACTVTY_SRVC_CD, "+
                        "G.SERVICE_NAME AS ACTVTY_SRVC_DESC, "+
                        "A.PROFILE_ID AS APPT_PROF_CD, "+
                        "H.EXTERNAL_ID AS APPT_STR_NO, "+
                        "H.LOCATION_NAME AS APPT_STR_DESC, "+
                        "A.INSTRUCTOR_ID AS APPT_INSTRUCT_CD, "+
                        "I.EXTERNAL_ID AS INSTRUCT_EMP_NO, "+
                        "J.FIRST_NAME AS INSTRUCT_FST_NAME, "+
                        "J.LAST_NAME AS INSTRUCT_LST_NAME, "+
                        "J.EMAIL AS INSTRUCT_EMAIL, "+
                        "I.STATUS AS INSTRUCT_STS, "+
                        "K.EXTERNAL_ID AS INSTRUCT_STR_NO, "+
                        "K.LOCATION_NAME AS INSTRUCT_STR_DESC, "+
                        "I.ENABLED AS INSTRUCT_ENABLED, "+
                        "TO_CHAR(A.START_TIME,'YYYY-MM-DD HH24:MI:SS') AS APPT_START_DTTM, "+
                        "TO_CHAR(A.END_TIME,'YYYY-MM-DD HH24:MI:SS') AS APPT_END_DTTM, "+
                        "A.CANCELED AS APPT_CANCL_FLG, "+
                        "L.APPOINTMENT_CANCEL_REASON_ID AS CNCL_REASON_CD, "+
                        "M.CANCEL_REASON AS CNCL_REASON_DESC, "+
                        " REPLACE(REPLACE(a.note, CHR(13), ' '), CHR(10), ' ') AS APPT_NOTE, "+
                        "A.BAND_NAME AS APPT_BAND_NAME, "+
                        "TO_CHAR(A.CREATE_TIME,'YYYY-MM-DD HH24:MI:SS') AS APPT_CREATE_DTTM, "+
                        "N.AUTH_ID AS UPDT_EMP_NO, "+
                        "TO_CHAR(A.UPDATED,'YYYY-MM-DD HH24:MI:SS') AS ACTIVITY_TS, "+
                        "A.APPOINTMENT_SERIES_ID AS APPT_SERIES_CD, "+
                        "TO_CHAR(O.SERIES_START_TIME,'YYYY-MM-DD HH24:MI:SS') AS SERIES_START_DTTM, "+
                        "TO_CHAR(O.SERIES_END_TIME,'YYYY-MM-DD HH24:MI:SS') AS SERIES_END_DTTM, "+
                        "O.IS_RECURRING AS SERIES_RECUR_FLG, "+
                        "O.ACTIVITY_ID AS SERIES_ACTVTY_CD, "+
                        "P.ACTIVITY_NAME AS SERIES_ACTVTY_DESC, "+
                        "P.SERVICE_ID AS SERIES_ACTVTY_SRVC_CD, "+
                        "Q.SERVICE_NAME AS SERIES_ACTVTY_SRVC_DESC "+
                        "FROM     APPOINTMENT A  "+
                        "JOIN APPOINTMENT_CUSTOMERS B ON A.APPOINTMENT_ID = B.APPOINTMENT_ID  "+
                        "JOIN CUSTOMER C ON B.CUSTOMER_ID = C.CUSTOMER_ID  "+
                        "LEFT JOIN CUSTOMER_STATUS D ON C.CUSTOMER_STATUS_ID = D.CUSTOMER_STATUS_ID  "+
                        "LEFT JOIN PERSON E ON C.PERSON_ID = E.PERSON_ID  "+
                        "LEFT JOIN ACTIVITY F ON A.ACTIVITY_ID = F.ACTIVITY_ID  "+
                        "LEFT JOIN SERVICE G ON F.SERVICE_ID = G.SERVICE_ID  "+
                        "LEFT JOIN LOCATION H ON A.PROFILE_ID = H.PROFILE_ID  "+
                        "LEFT JOIN INSTRUCTOR I ON A.INSTRUCTOR_ID = I.INSTRUCTOR_ID  "+
                        "LEFT JOIN PERSON J ON I.PERSON_ID = J.PERSON_ID  "+
                        "LEFT JOIN LOCATION K ON I.LOCATION_ID = K.LOCATION_ID  "+
                        "LEFT JOIN APPOINTMENT_CANCEL L ON A.APPOINTMENT_ID = L.APPOINTMENT_ID  "+
                        "LEFT JOIN APPOINTMENT_CANCEL_REASON M ON L.APPOINTMENT_CANCEL_REASON_ID = M.APPOINTMENT_CANCEL_REASON_ID  "+
                        "LEFT JOIN PERSON N ON A.UPDATED_BY = N.PERSON_ID  "+
                        "LEFT JOIN APPOINTMENT_SERIES O ON A.APPOINTMENT_SERIES_ID = O.APPOINTMENT_SERIES_ID  "+
                        "LEFT JOIN ACTIVITY P ON O.ACTIVITY_ID = P.ACTIVITY_ID "+
                        "LEFT JOIN SERVICE Q ON P.SERVICE_ID = Q.SERVICE_ID "+
                        "WHERE    TRUNC(A.UPDATED) >= '"+dateInString+"'";

                SQLQuery query = pSession.createSQLQuery(sb);
                query.addScalar("BRAND_CD", StandardBasicTypes.STRING);
                query.addScalar("ACCT_SRC_CD", StandardBasicTypes.STRING);
                query.addScalar("ACCT_SRC_NBR", StandardBasicTypes.STRING);
                query.addScalar("APPT_CD", StandardBasicTypes.INTEGER);
                query.addScalar("APPT_CUST_CD", StandardBasicTypes.INTEGER);

                query.addScalar("CUST_STS_CD", StandardBasicTypes.INTEGER);
                query.addScalar("CUST_STS_DESC", StandardBasicTypes.STRING);
                query.addScalar("APPT_STUDT_CD", StandardBasicTypes.INTEGER);
                query.addScalar("STUDT_FST_NAME", StandardBasicTypes.STRING);
                query.addScalar("STUDT_LST_NAME", StandardBasicTypes.STRING);

                query.addScalar("STUDT_EMAIL", StandardBasicTypes.STRING);
                query.addScalar("STUDT_PHN", StandardBasicTypes.STRING);
                query.addScalar("APPT_ACTVTY_CD", StandardBasicTypes.INTEGER);
                query.addScalar("ACTVTY_DESC", StandardBasicTypes.STRING);
                query.addScalar("ACTVTY_MIN_DUR", StandardBasicTypes.INTEGER);

                query.addScalar("ACTVTY_MAX_DUR", StandardBasicTypes.INTEGER);
                query.addScalar("ACTVTY_SRVC_CD", StandardBasicTypes.INTEGER);
                query.addScalar("ACTVTY_SRVC_DESC", StandardBasicTypes.STRING);
                query.addScalar("APPT_PROF_CD", StandardBasicTypes.INTEGER);
                query.addScalar("APPT_STR_NO", StandardBasicTypes.STRING);

                query.addScalar("APPT_STR_DESC", StandardBasicTypes.STRING);
                query.addScalar("APPT_INSTRUCT_CD", StandardBasicTypes.INTEGER);
                query.addScalar("INSTRUCT_EMP_NO", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCT_FST_NAME", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCT_LST_NAME", StandardBasicTypes.STRING);

                query.addScalar("INSTRUCT_EMAIL", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCT_STS", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCT_STR_NO", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCT_STR_DESC", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCT_ENABLED", StandardBasicTypes.CHARACTER);

                query.addScalar("APPT_START_DTTM", StandardBasicTypes.STRING);
                query.addScalar("APPT_END_DTTM", StandardBasicTypes.STRING);
                query.addScalar("APPT_CANCL_FLG", StandardBasicTypes.CHARACTER);
                query.addScalar("CNCL_REASON_CD", StandardBasicTypes.INTEGER);
                query.addScalar("CNCL_REASON_DESC", StandardBasicTypes.STRING);

                query.addScalar("APPT_NOTE", StandardBasicTypes.STRING);
                query.addScalar("APPT_BAND_NAME", StandardBasicTypes.STRING);
                query.addScalar("APPT_CREATE_DTTM", StandardBasicTypes.STRING);
                query.addScalar("UPDT_EMP_NO", StandardBasicTypes.STRING);
                query.addScalar("ACTIVITY_TS", StandardBasicTypes.STRING);
                query.addScalar("APPT_SERIES_CD", StandardBasicTypes.INTEGER);

                query.addScalar("SERIES_START_DTTM", StandardBasicTypes.STRING);
                query.addScalar("SERIES_END_DTTM", StandardBasicTypes.STRING);
                query.addScalar("SERIES_RECUR_FLG", StandardBasicTypes.CHARACTER);
                query.addScalar("SERIES_ACTVTY_CD", StandardBasicTypes.INTEGER);
                query.addScalar("SERIES_ACTVTY_DESC", StandardBasicTypes.STRING);
                query.addScalar("SERIES_ACTVTY_SRVC_CD", StandardBasicTypes.INTEGER);
                query.addScalar("SERIES_ACTVTY_SRVC_DESC", StandardBasicTypes.STRING);
                List<Object[]> list = query.list();
                CRMAppointmentDataFileDTO cRMAppointmentdata = null;

                for (Object[] obj : list) {
                    cRMAppointmentdata = new CRMAppointmentDataFileDTO();
                    cRMAppointmentdata.setGc(PGPUtils.getRemoveNewLineAndTilde(obj[0]));
                    cRMAppointmentdata.setPos(PGPUtils.getRemoveNewLineAndTilde(obj[1]));
                    cRMAppointmentdata.setCustomerId(PGPUtils.getRemoveNewLineAndTilde(obj[2]));
                    cRMAppointmentdata.setAppointmentId(PGPUtils.getRemoveNewLineAndTilde(obj[3]));
                    cRMAppointmentdata.setAppointmentCustomerId(PGPUtils.getRemoveNewLineAndTilde(obj[4]));
                    cRMAppointmentdata.setCustomerStatusName(PGPUtils.getRemoveNewLineAndTilde(obj[5]));
                    cRMAppointmentdata.setCustomerStatusId(PGPUtils.getRemoveNewLineAndTilde(obj[6]));
                    cRMAppointmentdata.setPersonId(PGPUtils.getRemoveNewLineAndTilde(obj[7]));
                    cRMAppointmentdata.setStudentFirstName(PGPUtils.getRemoveNewLineAndTilde(obj[8]));
                    cRMAppointmentdata.setStudentLastName(PGPUtils.getRemoveNewLineAndTilde(obj[9]));
                    cRMAppointmentdata.setStudentEmail(PGPUtils.getRemoveNewLineAndTilde(obj[10]));
                    cRMAppointmentdata.setStudentPhoneNumber(PGPUtils.getRemoveNewLineAndTilde(obj[11]));
                    cRMAppointmentdata.setAppointmentActivityId(PGPUtils.getRemoveNewLineAndTilde(obj[12]));
                    cRMAppointmentdata.setActivityName(PGPUtils.getRemoveNewLineAndTilde(obj[13]));
                    cRMAppointmentdata.setActivityMinimunDuration(PGPUtils.getRemoveNewLineAndTilde(obj[14]));
                    cRMAppointmentdata.setActivityMaxmumDuration(PGPUtils.getRemoveNewLineAndTilde(obj[15]));
                    cRMAppointmentdata.setActivityId(PGPUtils.getRemoveNewLineAndTilde(obj[16]));
                    cRMAppointmentdata.setActivityServiceName(PGPUtils.getRemoveNewLineAndTilde(obj[17]));
                    cRMAppointmentdata.setAppointmentProfileId(PGPUtils.getRemoveNewLineAndTilde(obj[18]));
                    cRMAppointmentdata.setAppointmentNumber(PGPUtils.getRemoveNewLineAndTilde(obj[19]));
                    cRMAppointmentdata.setAppointmentLocationName(PGPUtils.getRemoveNewLineAndTilde(obj[20]));
                    cRMAppointmentdata.setAppointmentInstructorId(PGPUtils.getRemoveNewLineAndTilde(obj[21]));
                    cRMAppointmentdata.setInstructorEmployeeId(PGPUtils.getRemoveNewLineAndTilde(obj[22]));
                    cRMAppointmentdata.setInstructorFirstName(PGPUtils.getRemoveNewLineAndTilde(obj[23]));
                    cRMAppointmentdata.setInstructorLastName(PGPUtils.getRemoveNewLineAndTilde(obj[24]));
                    cRMAppointmentdata.setInstructorEmail(PGPUtils.getRemoveNewLineAndTilde(obj[25]));
                    cRMAppointmentdata.setInstructorStatus(PGPUtils.getRemoveNewLineAndTilde(obj[26]));
                    cRMAppointmentdata.setInstructorId(PGPUtils.getRemoveNewLineAndTilde(obj[27]));
                    cRMAppointmentdata.setInstructionLocationName(PGPUtils.getRemoveNewLineAndTilde(obj[28]));
                    cRMAppointmentdata.setInstructorEnabled(PGPUtils.getRemoveNewLineAndTilde(obj[29]));
                    cRMAppointmentdata.setAppointmentStartTime(PGPUtils.getRemoveNewLineAndTilde(obj[30]));
                    cRMAppointmentdata.setAppointmentEndTime(PGPUtils.getRemoveNewLineAndTilde(obj[31]));;
                    cRMAppointmentdata.setAppointmentCancelled(PGPUtils.getRemoveNewLineAndTilde(obj[32]));
                    cRMAppointmentdata.setCancelReasonId(PGPUtils.getRemoveNewLineAndTilde(obj[33]));
                    cRMAppointmentdata.setCancelReasinDescription(PGPUtils.getRemoveNewLineAndTilde(obj[34]));
                    cRMAppointmentdata.setCancelReason(PGPUtils.getRemoveNewLineAndTilde(obj[35]));
                    cRMAppointmentdata.setAppointmentBandName(PGPUtils.getRemoveNewLineAndTilde(obj[36]));
                    cRMAppointmentdata.setAppointmentCreateTime(PGPUtils.getRemoveNewLineAndTilde(obj[37]));
                    cRMAppointmentdata.setAuthId(PGPUtils.getRemoveNewLineAndTilde(obj[38]));
                    cRMAppointmentdata.setAppointmentUpdateTime(PGPUtils.getRemoveNewLineAndTilde(obj[39]));
                    cRMAppointmentdata.setAppointmenSeriesId(PGPUtils.getRemoveNewLineAndTilde(obj[40]));
                    cRMAppointmentdata.setAppointmenSeriesStartTime(PGPUtils.getRemoveNewLineAndTilde(obj[41]));
                    cRMAppointmentdata.setAppointmenSeriesEndTime(PGPUtils.getRemoveNewLineAndTilde(obj[42]));
                    cRMAppointmentdata.setAppointmenSeriesFlag(PGPUtils.getRemoveNewLineAndTilde(obj[43]));
                    cRMAppointmentdata.setAppointmenSeriesActivityId(PGPUtils.getRemoveNewLineAndTilde(obj[44]));
                    cRMAppointmentdata.setAppointmenSeriesName(PGPUtils.getRemoveNewLineAndTilde(obj[45]));
                    cRMAppointmentdata.setSeriesId(PGPUtils.getRemoveNewLineAndTilde(obj[46]));
                    cRMAppointmentdata.setSeriesName(PGPUtils.getRemoveNewLineAndTilde(obj[47]));

                    cRMAppointmentDataMap.put(obj[3]+"", cRMAppointmentdata);

                }
                return cRMAppointmentDataMap;

            }

        };
    }

    //################################################351_----##############################################
    // Added New report GSSP-351
    public static Criterion<Appointment, CustomerSearchPageDTO> findAllActiveStudentsByLoction(
            final String location_id ) {

        return new AppointmentCriterion<CustomerSearchPageDTO>() {

            @Override
            public List<CustomerSearchPageDTO> search(Session pSession, int pFetchMode) {

                SQLQuery query = null;


                StringBuilder sb1 = new StringBuilder(" select "
                        + " c.external_id external_id,"
                        + " p.first_name || ' ' || p.last_name as Customer_name,"
                        + " p.email as student_email,"
                        + " p.phone as phone,"
                        + " cs.external_id as customer_status,"
                        + " c.lesson_count as lesson_count,"
                        + " c.last_booked as last_booked,"
                        + " c.customer_id as customer_id "
                        + " from location l"
                        + " join customer c on  l.external_id =c.location_external_id "
                        + " join person p on c.person_id = p.person_id"
                        + " join customer_status cs on c.customer_status_id = cs.customer_status_id "
                        + " where "
                        + " c.customer_status_id  != '2'");


                if(!"".equals(location_id)){
                    sb1.append("  and l.profile_id = :location_id    ");
                }
                sb1.append(" order by c.last_booked desc");

                query = pSession.createSQLQuery(sb1.toString());
                if(!"".equals(location_id)){
                    query.setString("location_id", location_id);
                }

                query.addScalar("external_id", StandardBasicTypes.STRING);
                query.addScalar("Customer_name", StandardBasicTypes.STRING);
                query.addScalar("student_email", StandardBasicTypes.STRING);
                query.addScalar("phone", StandardBasicTypes.STRING);
                query.addScalar("customer_status", StandardBasicTypes.STRING);
                query.addScalar("lesson_count", StandardBasicTypes.STRING);
                query.addScalar("last_booked", StandardBasicTypes.DATE);
                query.addScalar("customer_id", StandardBasicTypes.STRING);



                ScrollableResults scroll = query.scroll();
                List<CustomerSearchPageDTO> result = new ArrayList<CustomerSearchPageDTO>();
                int cnt =0;
                //Logic for getting the instructor details from person table
                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    CustomerSearchPageDTO searchDTO= new CustomerSearchPageDTO();

                    searchDTO.setCustomerExtId((String)objects[0]);
                    searchDTO.setCustomerName((String)objects[1]);
                    searchDTO.setEmail((String)objects[2]);
                    searchDTO.setPhone((String)objects[3]);
                    searchDTO.setCustomerStatus(PGPUtils.getRemoveNewLineAndTilde(objects[4]));
                    searchDTO.setLessonCount((String)objects[5]);
                    searchDTO.setLastBooked((Date)objects[6]);
                    searchDTO.setCustomerId((String)objects[7]);


                    cnt++;

                    result.add(searchDTO);

                }

                scroll.close();
                return result;
            }
        };
    }

    //########################################################################################################
    //################################################351_----##############################################
    // Added New report GSSP-351
    public static Criterion<Appointment, CustomerSearchPageDTO> findInstructorInstrumentStudentsByLoction(
            final List<String> customer_Ids) {

        return new AppointmentCriterion<CustomerSearchPageDTO>() {

            @Override
            public List<CustomerSearchPageDTO> search(Session pSession, int pFetchMode) {

                SQLQuery query = null;


                StringBuilder sb1 = new StringBuilder("select cc.EXTERNAL_ID external_id, act.ACTIVITY_NAME ACTIVITY_NAME,   "
                        + "  ip.first_name || ' ' || ip.last_name as Instructor_name,   "
                        + "  cc.LAST_BOOKED   LAST_BOOKED "
                        + "  from appointment t,appointment_customers aptcuss,customer cc,   "
                        + "  activity act,instructor i,person ip   "
                        + "  where t.appointment_id = aptcuss.appointment_id   "
                        + "  and i.instructor_id = t.instructor_id   "
                        + "  and ip.person_id = i.person_id   "
                        + "  and aptcuss.customer_id = cc.customer_id   "
                        + "  and t.ACTIVITY_ID = act.activity_id   "
                        + "  and t.appointment_id in (   "
                        + "  (select  max(apt.appointment_id)   "
                        + "  from appointment apt,appointment_customers aptcus,customer c   "
                        + "  where apt.appointment_id = aptcus.APPOINTMENT_ID   "
                        + "  and aptcus.CUSTOMER_ID = c.customer_id   "
                        + "  and apt.CANCELED != 'Y'   "
                        + "  and c.EXTERNAL_ID in (:customer_Ids)   "
                        + "  GROUP BY c.EXTERNAL_ID)) ");





                query = pSession.createSQLQuery(sb1.toString());

                String commaSeparatedCust =  "'"+customer_Ids.stream().collect(Collectors.joining("','")) +"'";


                if(!(customer_Ids.size()==0)){
                    query.setParameterList("customer_Ids", customer_Ids);
                }else{
                    query.setParameter("customer_Ids","");
                }


                query.addScalar("external_id", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("Instructor_name", StandardBasicTypes.STRING);
                query.addScalar("LAST_BOOKED", StandardBasicTypes.DATE);

                ScrollableResults scroll = query.scroll();
                List<CustomerSearchPageDTO> result = new ArrayList<CustomerSearchPageDTO>();


                int cnt=0;
                //Logic for getting the instructor details from person table
                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    CustomerSearchPageDTO searchDTO= new CustomerSearchPageDTO();

                    searchDTO.setCustomerExtId((String)objects[0]);
                    searchDTO.setActivityName((String)objects[1]);
                    searchDTO.setInstructor((String)objects[2]);
                    searchDTO.setLastBooked((Date)objects[3]);
                    ;

                    cnt++;
                    result.add(searchDTO);

                }

                scroll.close();
                return result;
            }
        };
    }

    public static Criterion<Appointment, InstructorAppointmentStatusDTO> findInstructorAppointmentStatusByLocationIdAndDateTime(
            final long pLocationId, final Date pStartTime, final Date pEndTime) {

        return new AppointmentCriterion<InstructorAppointmentStatusDTO>() {

            @Override
            public List<InstructorAppointmentStatusDTO> search(Session pSession, int pFetchMode) {
                String sb = " SELECT " +
                        " T.APPOINTMENT_ID, " +
                        " L.EXTERNAL_ID || ' ' || L.LOCATION_NAME LOCATION, " +
                        " I.EXTERNAL_ID AS INSTRUCTOR_EXTERNALID, " +
                        " P_I.FIRST_NAME  || ' ' || P_I.LAST_NAME AS INSTRUCTOR_NAME, " +
                        " TO_CHAR(T.START_TIME, 'DD-MON-YYYY') AS DATE_OF_APPOINTMENT , " +
                        " TO_CHAR( T.START_TIME, 'HH:MI AM') || '-' ||TO_CHAR( T.END_TIME, 'HH:MI AM') AS TIME_FRAME, " +
                        " (EXTRACT(DAY FROM ((T.END_TIME - T.START_TIME)*24*60))) AS DURATION, " +
                        "  A.ACTIVITY_NAME AS ACTIVITY_NAME, " +
                        "  C.EXTERNAL_ID AS CUSTOMER, " +
                        "  P_C.FIRST_NAME || ' ' || P_C.LAST_NAME AS CUSTOMER_NAME, " +
                        "  P_C.EMAIL CUSTOMER_EMAIL, " +
                        "  P_C.PHONE CUSTOMER_PHONE, " +
                        "  IAS.SHOW_STATUS, " +
                        "  IAS.COMMENTS, " +
                        "  IAS.STUDENT_NOTE " +
                        " FROM APPOINTMENT T " +
                        " JOIN APPOINTMENT_CUSTOMERS A_C ON T.APPOINTMENT_ID = A_C.APPOINTMENT_ID " +
                        " LEFT JOIN ACTIVITY A ON T.ACTIVITY_ID = A.ACTIVITY_ID " +
                        " LEFT JOIN CUSTOMER C ON A_C.CUSTOMER_ID = C.CUSTOMER_ID   " +
                        " LEFT JOIN LOCATION L  ON T.PROFILE_ID = L.PROFILE_ID        " +
                        " LEFT JOIN PERSON P_C ON C.PERSON_ID = P_C.PERSON_ID " +
                        " LEFT JOIN INSTRUCTOR I ON T.INSTRUCTOR_ID = I.INSTRUCTOR_ID " +
                        " LEFT JOIN PERSON P_I ON I.PERSON_ID = P_I.PERSON_ID " +
                        " LEFT JOIN INSTRUCTOR_APPOINTMENT_STATUS IAS  ON T.APPOINTMENT_ID = IAS.APPOINTMENT_ID " +
                        " WHERE T.START_TIME >= :STARTTIME " +
                        " AND T.START_TIME <= :ENDTIME " +
                        " AND L.LOCATION_ID = :LOCATIONID " +
                        " AND DECODE(C.EXTERNAL_ID, NULL, '_', C.EXTERNAL_ID) != 'C'  " +
                        " AND DECODE(T.CANCELED, NULL, 'N', T.CANCELED) != 'Y'  " +
                        //	" AND IAS.SHOW_STATUS IS NOT NULL " +
                        " ORDER BY TO_CHAR(T.START_TIME, 'DD-MON-YYYY'), P_I.FIRST_NAME, P_I.LAST_NAME, T.START_TIME, P_C.FIRST_NAME, P_C.LAST_NAME " ;

                SQLQuery query = pSession.createSQLQuery(sb);
                query.setTimestamp("STARTTIME", pStartTime);
                query.setTimestamp("ENDTIME", pEndTime);
                query.setLong("LOCATIONID", pLocationId);
                query.addScalar("APPOINTMENT_ID", StandardBasicTypes.LONG);
                query.addScalar("LOCATION", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_EXTERNALID", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_NAME", StandardBasicTypes.STRING);
                query.addScalar("DATE_OF_APPOINTMENT", StandardBasicTypes.STRING);
                query.addScalar("TIME_FRAME", StandardBasicTypes.STRING);
                query.addScalar("DURATION", StandardBasicTypes.STRING);
                query.addScalar("ACTIVITY_NAME", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_NAME", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_EMAIL", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_PHONE", StandardBasicTypes.STRING);
                query.addScalar("SHOW_STATUS", StandardBasicTypes.STRING);
                query.addScalar("COMMENTS", StandardBasicTypes.STRING);
                query.addScalar("STUDENT_NOTE", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();
                List<InstructorAppointmentStatusDTO> instructorAppointmentStatusDTOList = new ArrayList<InstructorAppointmentStatusDTO>();
                InstructorAppointmentStatusDTO instructorAppointmentStatusDTO =  null;

                while (scroll.next()) {

                    Object[] objects = scroll.get();
                    instructorAppointmentStatusDTO = new InstructorAppointmentStatusDTO();
                    instructorAppointmentStatusDTO.setAppointmentId((Long) objects[0]);
                    instructorAppointmentStatusDTO.setLocation(PGPUtils.setNullToEmpty((String) objects[1]));
                    instructorAppointmentStatusDTO.setInstructorExternalId(PGPUtils.setNullToEmpty((String) objects[2]));
                    instructorAppointmentStatusDTO.setInstructorName(PGPUtils.setNullToEmpty((String) objects[3]));
                    instructorAppointmentStatusDTO.setDateOfAppointment(PGPUtils.setNullToEmpty((String) objects[4]));
                    instructorAppointmentStatusDTO.setTimeFrame((PGPUtils.setNullToEmpty((String) objects[5])));
                    instructorAppointmentStatusDTO.setDuration(PGPUtils.setNullToEmpty((String) objects[6]));
                    instructorAppointmentStatusDTO.setActivityName(PGPUtils.setNullToEmpty((String) objects[7]));
                    instructorAppointmentStatusDTO.setCustomerExternalId((PGPUtils.setNullToEmpty((String) objects[8])));
                    instructorAppointmentStatusDTO.setCustomerName((PGPUtils.setNullToEmpty((String) objects[9])));
                    instructorAppointmentStatusDTO.setCustomerEmail(PGPUtils.setNullToEmpty((String) objects[10]));
                    instructorAppointmentStatusDTO.setCustomerPhone(PGPUtils.setNullToEmpty((String) objects[11]));
                    instructorAppointmentStatusDTO.setShowStatus(PGPUtils.setComletedStatusForEmpty((String) objects[12]));
                    instructorAppointmentStatusDTO.setComments(PGPUtils.hasRemarks((String) objects[13]));
                    instructorAppointmentStatusDTO.setStudentNote(PGPUtils.hasNotes((String) objects[14]));
                    instructorAppointmentStatusDTOList.add(instructorAppointmentStatusDTO);
                }

                scroll.close();

                return instructorAppointmentStatusDTOList;
            }
        };

    }


    //--363 Changes.
    public static Criterion<Appointment, InstructorLessonLinkDTO> findInstructorScheduleByLocationIdAndInstructorId(final List<String> personIdList,final Date startTime,final Date endTime) {
        return new AppointmentCriterion<InstructorLessonLinkDTO>() {

            @Override
            public List<InstructorLessonLinkDTO> search(Session pSession, int pFetchMode) {

                String sb = " SELECT "+
                		" OM.ID AS ZOOM_MEETINGS_ID, "+
                		" T.APPOINTMENT_ID AS APPOINTMENT_ID, "+
                		" T.CANCELED AS CANCELED, "+
                		"  (EXTRACT(DAY FROM ((T.END_TIME - T.START_TIME) * 24 * 60))) AS DURATION, "+
                		"  A.ACTIVITY_NAME AS ACTIVITY_NAME, "+
                		"  I.INSTRUCTOR_ID AS INSTRUCTOR_ID, "+
                		"  P_I.FIRST_NAME AS INSTRUCTOR_FIRST_NAME, "+
                		"  P_I.LAST_NAME AS INSTRUCTOR_LAST_NAME, "+
                		"  C.CUSTOMER_ID AS CUSTOMER_ID, "+
                		"  P_C.FIRST_NAME AS CUSTOMER_FIRST_NAME, "+
                		"   P_C.LAST_NAME AS CUSTOMER_LAST_NAME, "+
                		"  I.EXTERNAL_ID AS INSTRUCTOR_EXTERNAL_ID, "+
                		"  L.EXTERNAL_ID LOCATION_EXTERNAL_ID, "+
                		"  L.LOCATION_NAME LOCATION_NAME, "+
                		"   OM.START_URL ZOOM_MEETINGS_URL, "+
                		"  IAS.SHOW_STATUS SHOW_STATUS, "+
                		"  IAS.COMMENTS COMMENTS , "+
					 "  T.START_TIME AS  START_DATE , "+
					 "  TO_CHAR(T.START_TIME, 'HH:MI AM') AS START_TIME , "+
					 "   T.END_TIME AS END_TIME, "+
					 "  P_C.EMAIL AS C_EMAIL , "+
					 "   P_C.PHONE AS C_PHONE, "+
					 "  OM.NORMALIZED_UTC_END_TIME AS UTC_END_TIME , " +
					 "  IAS.STUDENT_NOTE STUDENT_NOTE ," +
					 "  OM.JOIN_URL JOIN_URL ," +
					 "  OM.NORMALIZED_UTC_START_TIME NORMALIZED_UTC_START_TIME , " +
					 "  LP.TZ TZ,  " +
					 "  a.SERVICE_ID AS SERVICE_ID, " +
					 "  r.ROOM_NUMBER_ID AS ROOM_NUMBER_ID, " +
					 "  t.Room_id AS Appointment_room_id, " +
					 "  rn.ROOM_NUMBER AS ROOM_NUMBER_Name, " +
					 " IAS.ASSIGNMENT ASSIGNMENT,IAS.PRACTICE_NOTES PRACTICE_NOTES,IAS.REMARKS REMARKS,  " +
					 "  C.EXTERNAL_ID AS EXTERNAL_ID "+
					 "  FROM APPOINTMENT T "+
					 "  LEFT JOIN APP_OLO.ZOOM_MEETINGS OM ON to_char(T.APPOINTMENT_ID) = OM.LESSON_APPOINTMENT_ID "+
					 " LEFT JOIN LOCATION L ON T.PROFILE_ID = L.PROFILE_ID "+
					 " LEFT JOIN LOCATION_PROFILE LP ON T.PROFILE_ID = LP.PROFILE_ID "+
					 " LEFT JOIN Room r ON t.room_id = r.room_id  "+
					 " LEFT JOIN   ROOM_NUMBER  rn ON rn.ROOM_NUMBER_ID = r.ROOM_NUMBER_ID "+
					 "  LEFT JOIN APPOINTMENT_CUSTOMERS A_C ON T.APPOINTMENT_ID = A_C.APPOINTMENT_ID "+
					 "  LEFT JOIN CUSTOMER C ON A_C.CUSTOMER_ID = C.CUSTOMER_ID "+
							  "  LEFT JOIN CUSTOMER_STATUS C_S ON C.CUSTOMER_STATUS_ID = C_S.CUSTOMER_STATUS_ID "+
							  "   LEFT JOIN PERSON P_C ON C.PERSON_ID = P_C.PERSON_ID "+
							  "  LEFT JOIN INSTRUCTOR I ON T.INSTRUCTOR_ID = I.INSTRUCTOR_ID "+
							  "  LEFT JOIN PERSON P_I ON I.PERSON_ID = P_I.PERSON_ID "+
							 "  LEFT JOIN ACTIVITY A ON T.ACTIVITY_ID = A.ACTIVITY_ID "+
							 "  LEFT JOIN INSTRUCTOR_APPOINTMENT_STATUS IAS ON T.APPOINTMENT_ID = IAS.APPOINTMENT_ID "+
							 "  WHERE  "+
					 "  T.INSTRUCTOR_ID IS NOT NULL "+
					  " AND A.ACTIVITY_ID NOT IN ('200','100','0','1','160','20','120','6','684','360','400','401') "+

						 "  AND T.START_TIME > :startTime " +
						 "  AND T.START_TIME <= :endTime " +
								 "   AND I.PERSON_ID in (:person_Id) " +
						  "   AND DECODE(C_S.EXTERNAL_ID, NULL, '_', C_S.EXTERNAL_ID) != 'C' "+
								  "  AND DECODE(T.CANCELED, NULL, 'N', T.CANCELED) != 'Y' "+
								 "  ORDER BY TO_CHAR(T.START_TIME, 'DD-MON-YYYY'), P_I.FIRST_NAME, P_I.LAST_NAME, T.START_TIME, P_C.FIRST_NAME, P_C.LAST_NAME";
                    SQLQuery query = pSession.createSQLQuery(sb);
                    query.setParameterList("person_Id", personIdList);
                    query.setTimestamp("startTime", startTime);
                    query.setTimestamp("endTime", endTime);
                    
                    query.addScalar("ZOOM_MEETINGS_id", StandardBasicTypes.STRING);
                    query.addScalar("appointment_id", StandardBasicTypes.STRING);
                    query.addScalar("canceled", StandardBasicTypes.STRING);
                    query.addScalar("duration", StandardBasicTypes.STRING);
                    query.addScalar("activity_name", StandardBasicTypes.STRING);
                    query.addScalar("instructor_id", StandardBasicTypes.STRING);
                    query.addScalar("instructor_first_name", StandardBasicTypes.STRING);
                    query.addScalar("instructor_last_name", StandardBasicTypes.STRING);
                    query.addScalar("customer_id", StandardBasicTypes.STRING);
                    query.addScalar("customer_first_name", StandardBasicTypes.STRING);
                    query.addScalar("customer_last_name", StandardBasicTypes.STRING);
                    query.addScalar("instructor_external_id", StandardBasicTypes.STRING);
                    query.addScalar("location_external_id", StandardBasicTypes.STRING);
                    query.addScalar("location_name", StandardBasicTypes.STRING);
                    query.addScalar("ZOOM_MEETINGS_url", StandardBasicTypes.STRING);
                    query.addScalar("Show_status", StandardBasicTypes.STRING);
                    query.addScalar("Comments", StandardBasicTypes.STRING);
                    query.addScalar("start_date", StandardBasicTypes.TIMESTAMP);
                    query.addScalar("start_time", StandardBasicTypes.STRING);
                    query.addScalar("end_time", StandardBasicTypes.TIMESTAMP);
                    query.addScalar("c_email", StandardBasicTypes.STRING);
                    query.addScalar("c_phone", StandardBasicTypes.STRING);
                    query.addScalar("UTC_END_TIME", StandardBasicTypes.TIMESTAMP);
                    query.addScalar("STUDENT_NOTE", StandardBasicTypes.STRING);
                    query.addScalar("JOIN_URL", StandardBasicTypes.STRING);
                    query.addScalar("NORMALIZED_UTC_START_TIME", StandardBasicTypes.TIMESTAMP);
                    query.addScalar("TZ", StandardBasicTypes.STRING);
                    query.addScalar("SERVICE_ID", StandardBasicTypes.STRING);
                    query.addScalar("ROOM_NUMBER_ID", StandardBasicTypes.STRING);
                    query.addScalar("Appointment_room_id", StandardBasicTypes.STRING);
                    query.addScalar("ROOM_NUMBER_Name", StandardBasicTypes.STRING);
                    //query.addScalar("LESSON_STATUS", StandardBasicTypes.STRING);            
                    //query.addScalar("NEXT_LESSON_STATUS", StandardBasicTypes.STRING);
                    query.addScalar("ASSIGNMENT", StandardBasicTypes.STRING);
                    query.addScalar("PRACTICE_NOTES", StandardBasicTypes.STRING);
                    query.addScalar("REMARKS", StandardBasicTypes.STRING);
                    //query.addScalar("RATE", StandardBasicTypes.STRING);
                    query.addScalar("EXTERNAL_ID", StandardBasicTypes.STRING);
                    
                    
                    ScrollableResults scroll = query.scroll();

                List<InstructorLessonLinkDTO> instructorScheduleDTOs = new ArrayList<InstructorLessonLinkDTO>();

                while (scroll.next()) {

                        Object[] objects = scroll.get();
                        InstructorLessonLinkDTO search = new InstructorLessonLinkDTO();
                        	search.setZoomMeetingID(PGPUtils.setNullToEmpty((String) objects[0]));
                        	search.setAppointmentId((String) objects[1]);
                        	search.setCanceled((String) objects[2]);
                        	search.setDuration((String) objects[3]);
                        	search.setActivityName((String) objects[4]);
                        	search.setInstructorId((String) objects[5]);
                        	search.setInstructorFirstName((String) objects[6]);
                        	search.setInstructorLastName((String) objects[7]);
                        	search.setCustomerId((String) objects[8]);
                        	search.setCustomerFirstName((String) objects[9]);
                        	search.setCustomerLastName((String) objects[10]);
                        	search.setInstructorExternalId((String) objects[11]);
                        	search.setLocationExternalId((String) objects[12]);
                        	search.setLocationName((String) objects[13]);
                        	search.setZoomMeetingUrl((String) objects[14]);
                        	search.setShowStatus(PGPUtils.setNullToEmpty((String) objects[15]));
                        	search.setComments(PGPUtils.setNullToEmpty((String) objects[16]));
                        	search.setStartDate(nullCheckDate(objects[17]));
                            search.setStartTime((String) objects[18]);
                        	search.setEndTime(nullCheckDate(objects[19])); 
                        	search.setCustomerEmail((String) objects[20]);
                        	search.setCustomerPhone((String) objects[21]);
                        	search.setUtcEndTime(nullCheckDate(objects[22]));
                        	search.setStudentNote(PGPUtils.setNullToEmpty((String) objects[23]));
                        	search.setJoinURL(((String) objects[24]));
                        	search.setUtcStartTime((nullCheckDate(objects[25])));
                        	search.setLocationTimezone((String) objects[26]);
                        	search.setServiceId((String) objects[27]);
                        	search.setRoomNumberId((String) objects[28]);
                        	search.setAppointmentRoomId((String) objects[29]);
                        	search.setRoomNumberName((String) objects[30]);
                        	//search.setLessonStatus(PGPUtils.setNullToEmpty((String) objects[31]));
                        	//search.setNextLessonStatus(PGPUtils.setNullToEmpty((String) objects[32]));
                        	search.setAssignment(PGPUtils.setNullToEmpty((String) objects[31]));
                        	search.setPracticeNotes(PGPUtils.setNullToEmpty((String) objects[32]));
                        	search.setRemarks(PGPUtils.setNullToEmpty((String) objects[33]));
                        	//search.setRate(PGPUtils.setNullToEmpty((String) objects[35]));
                        	search.setCustomerExternalId((String)objects[34]);
                            

                        	instructorScheduleDTOs.add(search);
                    }
                    scroll.close();
 
                    return instructorScheduleDTOs;
                }
            };

    }

    public static Date nullCheckDate(Object inputObj)
    {
        if(inputObj ==null)return null;
        Date outputStr = (Date)inputObj;
        return outputStr;

    }

    // -- 368 Changes
    public static Criterion<Appointment, AppointmentBookDTO> findAppointmentBookByDate(final Long pLocationId, final Date startTime,final Date endTime) {
        return new AppointmentCriterion<AppointmentBookDTO>() {

            @Override
            public List<AppointmentBookDTO> search(Session pSession, int pFetchMode) {
                SQLQuery query = null;
                StringBuilder sb = new StringBuilder( "select distinct " +
                        " AB.APPOINTMENT_ID " +
                        ", to_char(A.START_TIME,'MM/DD/YYYY') as START_DATE " +
                        ", to_char(A.START_TIME, 'HH:MI AM') as START_TIME " +
                        ", to_char(A.END_TIME, 'HH:MI AM') as END_TIME " +
                        ", L.EXTERNAL_ID LOCATION_EXTERNAL_ID " +
                        ", L.LOCATION_NAME " +
                        ", AB.BOOK_FLAG " +
                        ", AB.SITE  " +
                        ", ABT.DURATION " +
                        ", ABT.ORDER_ID " +
                        ", ABT.CUSTOMER_EXTERNAL_ID " +
                        ", CUP.FIRST_NAME CUSTOMER_FIRST_NAME " +
                        ", CUP.LAST_NAME CUSTOMER_LAST_NAME " +
                        ", ABT.INSTRUCTOR_EXTERNAL_ID " +
                        ", INJ.FIRST_NAME INSTRUCTOR_FIRST_NAME " +
                        ", INJ.LAST_NAME INSTRUCTOR_LAST_NAME " +
                        ", ABT.POS_REFERENCE_NUMBER " +
                        ", ABT.STATUS " +
                        ", ABT.ERROR_INFO " +
                        " from APPOINTMENT_BOOK AB " +
                        " INNER JOIN APPOINTMENT_BOOK_TRANSACTION ABT ON AB.ORDER_ID = ABT.ORDER_ID " +
                        " LEFT OUTER JOIN APPOINTMENT A ON A.APPOINTMENT_ID = AB.APPOINTMENT_ID " +
                        " LEFT OUTER JOIN INSTRUCTOR I ON I.EXTERNAL_ID = ABT.INSTRUCTOR_EXTERNAL_ID " +
                        " LEFT OUTER JOIN PERSON INJ ON INJ.PERSON_ID = I.PERSON_ID " +
                        " LEFT OUTER JOIN CUSTOMER C ON C.EXTERNAL_ID = ABT.CUSTOMER_EXTERNAL_ID " +
                        " LEFT OUTER JOIN PERSON CUP ON CUP.PERSON_ID = C.PERSON_ID " +
                        " LEFT OUTER JOIN LOCATION L ON L.PROFILE_ID = A.PROFILE_ID " +
                        " WHERE " +
                        " AB.BOOK_FLAG = 'N' " +
                        " AND DECODE(A.CANCELED, NULL, 'N', A.CANCELED) != 'Y' " +
                        " AND L.LOCATION_ID = :locationId " +
                        " AND A.START_TIME >= :startTime " +
                        " AND A.START_TIME <= :endTime " +

                        //      " and A.START_TIME >= :startTime " +
                        //      " and A.START_TIME <= :endTime " +
                        " order by to_char(A.START_TIME, 'MM/DD/YYYY') ");

                query = pSession.createSQLQuery(sb.toString());
                query.setParameter("locationId",pLocationId);
                query.setTimestamp("startTime", startTime);
                query.setTimestamp("endTime", endTime);

                query.addScalar("APPOINTMENT_ID", StandardBasicTypes.STRING);
                query.addScalar("START_DATE", StandardBasicTypes.STRING);
                query.addScalar("START_TIME", StandardBasicTypes.STRING);
                query.addScalar("END_TIME", StandardBasicTypes.STRING);
                query.addScalar("LOCATION_EXTERNAL_ID", StandardBasicTypes.STRING);
                query.addScalar("LOCATION_NAME", StandardBasicTypes.STRING);
                query.addScalar("BOOK_FLAG", StandardBasicTypes.STRING);
                query.addScalar("SITE", StandardBasicTypes.STRING);
                query.addScalar("DURATION", StandardBasicTypes.STRING);
                query.addScalar("ORDER_ID", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_EXTERNAL_ID", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_FIRST_NAME", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_LAST_NAME", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_EXTERNAL_ID", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_FIRST_NAME", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_LAST_NAME", StandardBasicTypes.STRING);
                query.addScalar("POS_REFERENCE_NUMBER", StandardBasicTypes.STRING);
                query.addScalar("STATUS", StandardBasicTypes.STRING);
                query.addScalar("ERROR_INFO", StandardBasicTypes.STRING);


                LOG.info(sb.toString());
                LOG.info(query.getQueryString());
                ScrollableResults scroll = query.scroll();
                List<AppointmentBookDTO> appointmentBookDTOs = new ArrayList<AppointmentBookDTO>();

                //DateFormat outputFormatter = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_HIPHEN);

                while (scroll.next()) {

                    Object[] objects = scroll.get();
                    AppointmentBookDTO search = new AppointmentBookDTO();
                    search.setAppointmentId((String) objects[0]);
                    search.setStartDate((String) objects[1]);
                    search.setStartTime((String) objects[2]);
                    search.setEndTime((String) objects[3]);
                    search.setLocationId((String) objects[4]);
                    search.setLocation((String) objects[5]);
                    search.setBookFlag((String)objects[6]);
                    search.setSite((String)objects[7]);
                    search.setDuration((String) objects[8]);
                    search.setOrderId((String) objects[9]);
                    search.setCustomerId((String) objects[10]);
                    search.setCustomerFirstName((String) objects[11]);
                    search.setCustomerLastName((String) objects[12]);
                    search.setInstructorId((String) objects[13]);
                    search.setInstructorFirstName((String) objects[14]);
                    search.setInstructorLastName((String) objects[15]);
                    search.setPosRefNumber((String) objects[16]);
                    search.setStatus((String) objects[17]);
                    search.setErrorInfo((String) objects[18]);
                    if (search.getBookFlag().equalsIgnoreCase("Y")) {
                        search.setShowResubmit(Boolean.TRUE);
                    } else {
                        search.setShowResubmit(Boolean.FALSE);
                    }
                    appointmentBookDTOs.add(search);
                }
                scroll.close();

                return appointmentBookDTOs;
            }
        };
    }


    //LES-624
    public static Criterion<Appointment, CustomerAppointmentDTO> findCustomerAppointmentsByCustomerIdAndDate(final Long customerId,final Date startTime,final Date endTime) {
        return new AppointmentCriterion<CustomerAppointmentDTO>() {

            @Override
            public List<CustomerAppointmentDTO> search(Session pSession, int pFetchMode) {
                SQLQuery query = null;
                StringBuilder sb = new StringBuilder( "SELECT " +
                        " T.APPOINTMENT_ID , " +
                        " to_char(T.START_TIME,'MM/DD/YYYY') as START_DATE , " +
                        " to_char(T.START_TIME, 'HH:MI AM') as START_TIME , " +
                        " to_char(T.END_TIME, 'HH:MI AM') as END_TIME , " +
                        " L.EXTERNAL_ID LOCATION_EXTERNAL_ID , " +
                        " L.LOCATION_NAME , " +
                        " A.ACTIVITY_NAME AS ACTIVITY_NAME, " +
                        " (EXTRACT(DAY FROM ((T.END_TIME - T.START_TIME) * 24 * 60))) AS DURATION , " +
                        " P_C.EXTERNAL_ID  as CUSTOMER_EXTERNAL_ID, " +
                        " P_C.FIRST_NAME CUSTOMER_FIRST_NAME , " +
                        " P_C.LAST_NAME CUSTOMER_LAST_NAME , " +
                        " P_I.EXTERNAL_ID AS INSTRUCTOR_EXTERNAL_ID , " +
                        " P_I.FIRST_NAME INSTRUCTOR_FIRST_NAME , " +
                        " P_I.LAST_NAME INSTRUCTOR_LAST_NAME , " +
                        " IAS.SHOW_STATUS STATUS, " +
                        " IAS.COMMENTS , " +
                        " IAS.STUDENT_NOTE, " +
                        " A.SERVICE_ID AS SERVICE_ID, " +
                        " OM.JOIN_URL JOIN_URL, " +
                        " OM.ID AS ZOOM_MEETINGS_ID, "+
                        " OM.NORMALIZED_UTC_START_TIME NORMALIZED_UTC_START_TIME , " +
                        " OM.NORMALIZED_UTC_END_TIME AS UTC_END_TIME , " +
                        " LP.TZ TZ,  " +
                        " T.END_TIME AS END_TIME_DATE, "+
                        " T.START_TIME AS START_TIME_DATE "+
                        ", to_Char(c.customer_id) AS CUSTOMER_ID" +
                        " FROM APPOINTMENT T " +
                        " LEFT JOIN APP_OLO.ZOOM_MEETINGS OM ON to_char(T.APPOINTMENT_ID) = OM.LESSON_APPOINTMENT_ID " +
                        " LEFT JOIN ACTIVITY A ON T.ACTIVITY_ID = A.ACTIVITY_ID "+
                        " LEFT JOIN LOCATION L ON T.PROFILE_ID = L.PROFILE_ID " +
                        " LEFT JOIN LOCATION_PROFILE LP ON T.PROFILE_ID = LP.PROFILE_ID " +
                        " LEFT JOIN APPOINTMENT_CUSTOMERS A_C ON T.APPOINTMENT_ID = A_C.APPOINTMENT_ID " +
                        " LEFT JOIN CUSTOMER C ON A_C.CUSTOMER_ID = C.CUSTOMER_ID " +
                        " LEFT JOIN CUSTOMER_STATUS C_S ON C.CUSTOMER_STATUS_ID = C_S.CUSTOMER_STATUS_ID " +
                        " LEFT JOIN PERSON P_C ON C.PERSON_ID = P_C.PERSON_ID " +
                        " LEFT JOIN INSTRUCTOR I ON T.INSTRUCTOR_ID = I.INSTRUCTOR_ID " +
                        " LEFT JOIN PERSON P_I ON I.PERSON_ID = P_I.PERSON_ID " +
                        " LEFT JOIN ACTIVITY A ON T.ACTIVITY_ID = A.ACTIVITY_ID " +
                        " LEFT JOIN INSTRUCTOR_APPOINTMENT_STATUS IAS ON T.APPOINTMENT_ID = IAS.APPOINTMENT_ID " +
                        " WHERE " +
                        " C.CUSTOMER_ID = :customerId " +
                        " AND T.INSTRUCTOR_ID IS NOT NULL " +
                        " AND A.ACTIVITY_ID NOT IN ('200','100','0','1','160','20','120','6','684','380') " +
                        " AND T.START_TIME >= :startTime " +
                        " AND T.START_TIME <= :endTime " +
                        " AND DECODE(C_S.EXTERNAL_ID, NULL, '_', C_S.EXTERNAL_ID) != 'C' " +
                        " AND DECODE(T.CANCELED, NULL, 'N', T.CANCELED) != 'Y' " +
                      //" order by to_char(T.START_TIME, 'dd-mon-yyyy:hh24:mm') ");
                        " order by T.START_TIME ");

                query = pSession.createSQLQuery(sb.toString());
                query.setTimestamp("startTime", startTime);
                query.setTimestamp("endTime", endTime);
                query.setParameter("customerId",customerId);
                
                query.addScalar("APPOINTMENT_ID", StandardBasicTypes.STRING);               
                query.addScalar("START_DATE", StandardBasicTypes.STRING);
                query.addScalar("START_TIME", StandardBasicTypes.STRING);
                query.addScalar("END_TIME", StandardBasicTypes.STRING);
                query.addScalar("LOCATION_EXTERNAL_ID", StandardBasicTypes.STRING);
                query.addScalar("LOCATION_NAME", StandardBasicTypes.STRING);
                query.addScalar("activity_name", StandardBasicTypes.STRING);
                query.addScalar("DURATION", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_EXTERNAL_ID", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_FIRST_NAME", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_LAST_NAME", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_EXTERNAL_ID", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_FIRST_NAME", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_LAST_NAME", StandardBasicTypes.STRING);
                query.addScalar("STATUS", StandardBasicTypes.STRING);
                query.addScalar("COMMENTS", StandardBasicTypes.STRING);
                query.addScalar("STUDENT_NOTE", StandardBasicTypes.STRING);
                //GCSS-373
                query.addScalar("SERVICE_ID", StandardBasicTypes.STRING);
                query.addScalar("JOIN_URL", StandardBasicTypes.STRING);
                query.addScalar("ZOOM_MEETINGS_id", StandardBasicTypes.STRING);
                query.addScalar("NORMALIZED_UTC_START_TIME", StandardBasicTypes.TIMESTAMP);
                query.addScalar("UTC_END_TIME", StandardBasicTypes.TIMESTAMP);
                query.addScalar("TZ", StandardBasicTypes.STRING);
                query.addScalar("END_TIME_DATE", StandardBasicTypes.TIMESTAMP);
                query.addScalar("START_TIME_DATE", StandardBasicTypes.TIMESTAMP);
                query.addScalar("CUSTOMER_ID", StandardBasicTypes.STRING);
                             
                LOG.info(sb.toString());
                LOG.info(query.getQueryString());
                ScrollableResults scroll = query.scroll();
                List<CustomerAppointmentDTO> customerAppointmentDTOS = new ArrayList<CustomerAppointmentDTO>();

                //DateFormat outputFormatter = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_HIPHEN);

                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    CustomerAppointmentDTO search = new CustomerAppointmentDTO();
                    search.setAppointmentId((String) objects[0]);
                    search.setStartDate((String) objects[1]);
                    search.setStartTime((String) objects[2]);
                    search.setEndTime((String) objects[3]);
                    search.setLocationId((String) objects[4]);
                    search.setLocation((String) objects[5]);
                    search.setActivityName((String)objects[6]);
                    search.setDuration((String) objects[7]);
                    search.setCustomerId((String) objects[8]);
                    search.setCustomerFirstName((String) objects[9]);
                    search.setCustomerLastName((String) objects[10]);
                    search.setInstructorId((String) objects[11]);
                    search.setInstructorFirstName((String) objects[12]);
                    search.setInstructorLastName((String) objects[13]);
                    search.setStatus((String) objects[14]);
                    search.setComments((String) objects[15]);
                    search.setStudentNotes((String) objects[16]);
                    //GCSS-373
                    search.setServiceId((String) objects[17]);
                    search.setJoinURL((String) objects[18]);
                    search.setZoomMeetingID(PGPUtils.setNullToEmpty((String) objects[19]));
                    search.setUtcStartTime((nullCheckDate(objects[20])));
                    search.setUtcEndTime((nullCheckDate(objects[21])));
                    search.setLocationTimezone((String) objects[22]);
                    search.setEndTimeDate(nullCheckDate(objects[23]));
                    search.setStartTimeDate(nullCheckDate(objects[24]));
                    search.setCustomerId((String)objects[25]);

                    if (!StringUtils.isEmpty(search.getComments())) {
                        search.setEnabledComments(true);
                    } else {
                        search.setEnabledComments(false);
                    }
                    if (!StringUtils.isEmpty(search.getStudentNotes())) {
                        search.setEnabledNotes(true);
                    } else {
                        search.setEnabledNotes(false);
                    }
                    if (StringUtils.isEmpty(search.getStatus())) {
                        search.setEnabledStatus(false);
                        search.setStatus("Pending Instructor Response");
                    } else {
                        search.setEnabledStatus(true);
                    }
                    customerAppointmentDTOS.add(search);



                }
                scroll.close();

                return customerAppointmentDTOS;
            }
        };
    }

    //-----------------For Appointment Status API  by Customer Email ID----------------
    
    public static Criterion<Appointment, AppointmentStatusDTO> findCustomerAppointmentStatusByEmailId(
            final List<String> email_Id,final Date startTime) {

        return new AppointmentCriterion<AppointmentStatusDTO>() {

            @Override
            public List<AppointmentStatusDTO> search(Session pSession, int pFetchMode) {
                String sb = "  SELECT T.APPOINTMENT_ID APPOINTMENT_ID," +
                		" L.EXTERNAL_ID || ' ' || L.LOCATION_NAME LOCATION," +
                		" I.EXTERNAL_ID AS INSTRUCTOR_EXTERNALID," +
                		"  P_I.FIRST_NAME  || ' ' || P_I.LAST_NAME AS INSTRUCTOR_NAME," +
                		"  TO_CHAR(T.START_TIME, 'DD-MON-YYYY') AS DATE_OF_APPOINTMENT ," +
                		"  TO_CHAR( T.START_TIME, 'HH:MI:SS AM') || '-' ||TO_CHAR( T.END_TIME, 'HH:MI:SS AM') AS TIME_FRAME," +
                		"  (EXTRACT(DAY FROM ((T.END_TIME - T.START_TIME)*24*60))) AS DURATION," +
                		"  A.ACTIVITY_NAME AS ACTIVITY_NAME," +
                		"  C.EXTERNAL_ID AS CUSTOMER," +
                		"  P_C.FIRST_NAME || ' ' || P_C.LAST_NAME AS CUSTOMER_NAME," +
                		"  P_C.EMAIL CUSTOMER_EMAIL," +
                		"  P_C.PHONE CUSTOMER_PHONE," +
                		"  IAS.SHOW_STATUS APPOINTMENT_COMPLETED_STATUS," +
                		"  IAS.COMMENTS INSTRUCTOR_INTERNAL_REMARKS," +
                		"  IAS.STUDENT_NOTE STUDENT_NOTES" +
                		"  FROM APPOINTMENT T" +
                		"  JOIN APPOINTMENT_CUSTOMERS A_C ON T.APPOINTMENT_ID = A_C.APPOINTMENT_ID" +
                		"  LEFT JOIN ACTIVITY A ON T.ACTIVITY_ID = A.ACTIVITY_ID" +
                		"  LEFT JOIN CUSTOMER C ON A_C.CUSTOMER_ID = C.CUSTOMER_ID  " +
                		"  LEFT JOIN LOCATION L  ON T.PROFILE_ID = L.PROFILE_ID  " +     
                		"  LEFT JOIN PERSON P_C ON C.PERSON_ID = P_C.PERSON_ID" +
                		"  LEFT JOIN INSTRUCTOR I ON T.INSTRUCTOR_ID = I.INSTRUCTOR_ID" +
                		"  LEFT JOIN PERSON P_I ON I.PERSON_ID = P_I.PERSON_ID" +
                		"  LEFT JOIN INSTRUCTOR_APPOINTMENT_STATUS IAS  ON T.APPOINTMENT_ID = IAS.APPOINTMENT_ID" +
                		"  WHERE " +
                		//"  T.START_TIME <= :startTime AND " +
                		"   P_C.EMAIL IN (:email_Id)" +
                		"  AND DECODE(C.EXTERNAL_ID, NULL, '_', C.EXTERNAL_ID) != 'C' " +
                		"  AND DECODE(T.CANCELED, NULL, 'N', T.CANCELED) != 'Y' " +
                		"   " ;

                SQLQuery query = pSession.createSQLQuery(sb);
                query.setParameterList("email_Id", email_Id);
               // query.setTimestamp("startTime", startTime);
                query.addScalar("APPOINTMENT_ID", StandardBasicTypes.LONG);
                query.addScalar("LOCATION", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_EXTERNALID", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_NAME", StandardBasicTypes.STRING);
                query.addScalar("DATE_OF_APPOINTMENT", StandardBasicTypes.STRING);
                query.addScalar("TIME_FRAME", StandardBasicTypes.STRING);
                query.addScalar("DURATION", StandardBasicTypes.STRING);
                query.addScalar("ACTIVITY_NAME", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_NAME", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_EMAIL", StandardBasicTypes.STRING);
                query.addScalar("CUSTOMER_PHONE", StandardBasicTypes.STRING);
                query.addScalar("APPOINTMENT_COMPLETED_STATUS", StandardBasicTypes.STRING);
                query.addScalar("INSTRUCTOR_INTERNAL_REMARKS", StandardBasicTypes.STRING);
                query.addScalar("STUDENT_NOTES", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();
                List<AppointmentStatusDTO> appointmentStatusDTOList = new ArrayList<AppointmentStatusDTO>();
                AppointmentStatusDTO appointmentStatusDTO =  null;

                while (scroll.next()) {

                    Object[] objects = scroll.get();
                    appointmentStatusDTO = new AppointmentStatusDTO();
                    appointmentStatusDTO.setAppointmentId((Long) objects[0]);
                    appointmentStatusDTO.setLocation(PGPUtils.setNullToEmpty((String) objects[1]));
                    appointmentStatusDTO.setInstructorExternalId(PGPUtils.setNullToEmpty((String) objects[2]));
                    appointmentStatusDTO.setInstructorName(PGPUtils.setNullToEmpty((String) objects[3]));
                    appointmentStatusDTO.setDateOfAppointment(PGPUtils.setNullToEmpty((String) objects[4]));
                    appointmentStatusDTO.setTimeFrame((PGPUtils.setNullToEmpty((String) objects[5])));
                    appointmentStatusDTO.setDuration(PGPUtils.setNullToEmpty((String) objects[6]));
                    appointmentStatusDTO.setActivityName(PGPUtils.setNullToEmpty((String) objects[7]));
                    appointmentStatusDTO.setCustomerExternalId((PGPUtils.setNullToEmpty((String) objects[8])));
                    appointmentStatusDTO.setCustomerName((PGPUtils.setNullToEmpty((String) objects[9])));
                    appointmentStatusDTO.setCustomerEmail(PGPUtils.setNullToEmpty((String) objects[10]));
                    appointmentStatusDTO.setCustomerPhone(PGPUtils.setNullToEmpty((String) objects[11]));
                    appointmentStatusDTO.setShowStatus(PGPUtils.setNullToEmpty((String) objects[12]));
                    appointmentStatusDTO.setComments(PGPUtils.setNullToEmptyAndNewLineToSpace((String) objects[13]));
                    appointmentStatusDTO.setStudentNote(PGPUtils.setNullToEmptyAndNewLineToSpace((String) objects[14]));
                    appointmentStatusDTOList.add(appointmentStatusDTO);
                }

                scroll.close();
                return appointmentStatusDTOList;
            }
        };

    }

  //Added for new Instructor Availabilty Service
    /**
     * Returns a Criterion that can be used to find  details of appointments passed from the UI
     *
     * @param appointmentId Long
     *
     * @return Criterion instance
     */
    public static Criterion<Appointment, LocationProfileInfoDTO> findProfileDetailByAppointmentIDInsAVL(
            final Long appointmentId) {

        return new AppointmentCriterion<LocationProfileInfoDTO>() {

            @Override
            public List<LocationProfileInfoDTO> search(Session pSession, int pFetchMode) {


                String sb = " select t.profile_id as profileId, t.appointment_series_id ,l.location_id, t.activity_id, " +
                        " l.location_name as locationName, l.phone  ,to_char(ap.series_start_time,'YYYY-MM-DD')as seriesStartDate, " +
                        " to_char(t.start_time, 'YYYY-MM-DD') as appointmentDate,  " +
                        " (extract(day from ((t.end_time - t.start_time) * 24 * 60 * 60 * 60))) as duration, " +
                        " to_char(t.start_time,'HH:MI AM') as start_time, to_char(t.end_time,'HH:MI AM') as end_time, "+
                        " to_char(ap.series_end_time,'YYYY-MM-DD')as seriesEndDate ," +
                        " ap.is_recurring as isRecurring" +
                        " from appointment t  " +
                        " join appointment_series ap on t.appointment_series_id = ap.appointment_series_id "+
                        " join location_profile lp on t.profile_id = lp.profile_id "+
                        " join location l on lp.profile_id = l.profile_id " +
                        " where t.appointment_id=:appointmentId";




                SQLQuery query = pSession.createSQLQuery(sb);


                query.setLong("appointmentId", appointmentId);

                query.addScalar("profileId", StandardBasicTypes.LONG);
                query.addScalar("appointment_series_id", StandardBasicTypes.LONG);
                query.addScalar("location_id", StandardBasicTypes.LONG);
                query.addScalar("activity_id", StandardBasicTypes.LONG);
                query.addScalar("locationName", StandardBasicTypes.STRING);
                query.addScalar("phone", StandardBasicTypes.STRING);
                query.addScalar("seriesStartDate", StandardBasicTypes.STRING);
                query.addScalar("appointmentDate", StandardBasicTypes.STRING);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.STRING);
                query.addScalar("seriesEndDate", StandardBasicTypes.STRING);
                query.addScalar("isRecurring", StandardBasicTypes.STRING);


                ScrollableResults scroll = query.scroll();
                List<LocationProfileInfoDTO> result = new ArrayList<LocationProfileInfoDTO>();

                LocationProfileInfoDTO locationProfileInfoDTO = null;
                while (scroll.next()) {

                    Object[] objects = scroll.get();

                    locationProfileInfoDTO  =  new  LocationProfileInfoDTO((Long) objects[0],(String) objects[4] ,
                            (String) objects[5],(String) objects[6],(Long) objects[1],(Long) objects[2],(Long) objects[3], (String)objects[7],(Long)objects[8],
                            (String)objects[9],(String)objects[10],(String)objects[11],(String)objects[12]);

                    result.add(locationProfileInfoDTO);


                }
                scroll.close();

                return result;

            }
        };

    }

    public static Criterion<Appointment, Appointment> findByActiveAppointmentbyAppSeries(final long pAppointmentSeriesId) {

        return new AppointmentCriterion<Appointment>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Appointment> search(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Appointment t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" where t.appointmentSeries.appointmentSeriesId = :appointmentSeriesId and t.canceled != 'Y' ");
                
                sb.append(" order by t.updated ");
                Query query = pSession.createQuery(sb.toString());
                query.setLong("appointmentSeriesId", pAppointmentSeriesId);
               
                return query.list();
            }

        };
    }
    
    public static Criterion<Appointment, CustomerAppointmentDetailsResultDTO> findByCustomerAndDate(final String customerId,
            final Date pStartTime, final Date pEndTime) {

    		return new AppointmentCriterion<CustomerAppointmentDetailsResultDTO>() {

						@Override
						@SuppressWarnings("unchecked")
						public List<CustomerAppointmentDetailsResultDTO> search(Session pSession, int pFetchMode) {
							StringBuilder sb = new StringBuilder("SELECT ");
							sb.append("  T.APPOINTMENT_ID as APPOINTMENTID, ");
							sb.append("  L.EXTERNAL_ID || ' ' || L.LOCATION_NAME LOCATION, ");
							sb.append("  I.EXTERNAL_ID AS INSTRUCTOR_EXTERNALID, ");
							sb.append("  P_I.FIRST_NAME  || ' ' || P_I.LAST_NAME AS INSTRUCTOR_NAME, ");
							sb.append("  TO_CHAR(T.START_TIME, 'DD-MON-YYYY') AS DATE_OF_APPOINTMENT , ");
							sb.append("  TO_CHAR( T.START_TIME, 'HH:MI AM') || '-' ||TO_CHAR( T.END_TIME, 'HH:MI AM') AS TIME_FRAME, ");
							sb.append("  (EXTRACT(DAY FROM ((T.END_TIME - T.START_TIME)*24*60))) AS DURATION, ");
							sb.append("   A.ACTIVITY_NAME AS ACTIVITY_NAME, ");
							sb.append("   C.EXTERNAL_ID AS CUSTOMER, ");
							sb.append("   P_C.FIRST_NAME || ' ' || P_C.LAST_NAME AS CUSTOMER_NAME, ");
							sb.append("   P_C.EMAIL CUSTOMER_EMAIL, ");
							sb.append("   P_C.PHONE CUSTOMER_PHONE ");
							sb.append("  FROM APPOINTMENT T ");
							sb.append("  JOIN APPOINTMENT_CUSTOMERS A_C ON T.APPOINTMENT_ID = A_C.APPOINTMENT_ID ");
							sb.append("  LEFT JOIN ACTIVITY A ON T.ACTIVITY_ID = A.ACTIVITY_ID ");
							sb.append("  LEFT JOIN CUSTOMER C ON A_C.CUSTOMER_ID = C.CUSTOMER_ID  "); 
							sb.append("  LEFT JOIN LOCATION L  ON T.PROFILE_ID = L.PROFILE_ID ");       
							sb.append("  LEFT JOIN PERSON P_C ON C.PERSON_ID = P_C.PERSON_ID ");
							sb.append("  LEFT JOIN INSTRUCTOR I ON T.INSTRUCTOR_ID = I.INSTRUCTOR_ID ");
							sb.append("  LEFT JOIN PERSON P_I ON I.PERSON_ID = P_I.PERSON_ID ");
							sb.append("  WHERE ");
						//	sb.append("  --T.START_TIME >=  sysdate ");
							sb.append("  DECODE(T.CANCELED, NULL, 'N', T.CANCELED) != 'Y' ");
						//	sb.append("  AND TO_CHAR(T.START_TIME, 'DD/MM/YYYY') = :pStartTime ");
							
							  sb.append("   and to_date(to_char(t.start_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') >= :startTime");
				                sb.append("   and to_date(to_char(t.start_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') <= :endTime");
							
							
							//sb.append("  --AND DECODE(T.CANCELED, NULL, 'N', T.CANCELED) != 'Y' ");
							sb.append("  and C.EXTERNAL_ID in (:customerId) ");
						SQLQuery query = pSession.createSQLQuery(sb.toString());
						
						 
			                query.addScalar("APPOINTMENTID", StandardBasicTypes.LONG);
			                query.addScalar("LOCATION", StandardBasicTypes.STRING);
			                query.addScalar("INSTRUCTOR_EXTERNALID", StandardBasicTypes.STRING);
			                query.addScalar("INSTRUCTOR_NAME", StandardBasicTypes.STRING);
			                query.addScalar("DATE_OF_APPOINTMENT", StandardBasicTypes.STRING);
			                query.addScalar("TIME_FRAME", StandardBasicTypes.STRING);
			                query.addScalar("DURATION", StandardBasicTypes.STRING);
			                query.addScalar("ACTIVITY_NAME", StandardBasicTypes.STRING);
			                query.addScalar("CUSTOMER", StandardBasicTypes.STRING);
			                query.addScalar("CUSTOMER_NAME", StandardBasicTypes.STRING);
			                query.addScalar("CUSTOMER_EMAIL", StandardBasicTypes.STRING);
			                query.addScalar("CUSTOMER_PHONE", StandardBasicTypes.STRING);
			                
												
						/**
						* For GCSS-544,add join to fetch AppointmentSeries
						*/
					//	query.addJoin("aps", "t.appointmentSeries");
						
						query.setString("customerId", customerId);
						query.setDate("startTime", pStartTime);
					//	 query.setDate("startTime", pStartTime);
			                query.setDate("endTime", pEndTime);
			                
						//List<Object[]> list = query.list();
						ScrollableResults scroll = query.scroll();
						
						CustomerAppointmentDetailsResultDTO customerAppointmentDetailsResultDTO = null;
						 Map<Long, CustomerAppointmentDetailsResultDTO> maps = new HashMap<Long, CustomerAppointmentDetailsResultDTO>();
						
						  while (scroll.next()) {
							  customerAppointmentDetailsResultDTO = new CustomerAppointmentDetailsResultDTO();

			                    Object[] objects = scroll.get();
			                
			                   // System.out.println(objects[4]);
			                    customerAppointmentDetailsResultDTO.setAppointmentId(((Long)objects[0]));
			                    customerAppointmentDetailsResultDTO.setLocation((String)objects[1]);
			                    customerAppointmentDetailsResultDTO.setInstructorExternalId((String)objects[2]);
			                    customerAppointmentDetailsResultDTO.setInstructorName((String)objects[3]);
			                    customerAppointmentDetailsResultDTO.setDateOfAppointment((String)objects[4]);
			                    customerAppointmentDetailsResultDTO.setTimeFrame((String)objects[5]);
			                    customerAppointmentDetailsResultDTO.setDuration((String)objects[6]);
			                    customerAppointmentDetailsResultDTO.setActivityName((String)objects[7]);
			                    customerAppointmentDetailsResultDTO.setCustomerExternalId((String)objects[8]);
			                    customerAppointmentDetailsResultDTO.setCustomerName((String)objects[9]);
			                    customerAppointmentDetailsResultDTO.setCustomerEmail((String)objects[10]);
			                    customerAppointmentDetailsResultDTO.setCustomerPhone((String)objects[11]);
			                    
			                 //   Long appointment_id = (Long) objects[1];
			                    
			                   // customerAppointmentDetailsResultDTO.setAppointmentId(objects[0]);
			                  
			                   
			                    maps.put(customerAppointmentDetailsResultDTO.getAppointmentId(), customerAppointmentDetailsResultDTO);

			                }
						  
						
						
						//Map<Long, CustomerAppointmentDetailsResultDTO> maps = new HashMap<Long, CustomerAppointmentDetailsResultDTO>();
						
						List<CustomerAppointmentDetailsResultDTO> result = new ArrayList<CustomerAppointmentDetailsResultDTO>();
						if (!maps.isEmpty()) {
						result.addAll(maps.values());
						}
						return result;
						}
						
			};
		}
    
    //OLL-3732
    
    public static Criterion<Appointment, LessonCompletionDTO> findTrailLessonForDateRange(
            final Date pStartTime, final Date pEndTime) {

    		return new AppointmentCriterion<LessonCompletionDTO>() {@Override
    			@SuppressWarnings("unchecked")
    		public List<LessonCompletionDTO> search(Session pSession, int pFetchMode) {
    		    List<LessonCompletionDTO> result = new ArrayList<>();
    		    Map<Long, LessonCompletionDTO> maps = new HashMap<>();

    		    try {
    		        LOG.info("Building SQL query for lesson completions...");

    		        StringBuilder sb = new StringBuilder("SELECT ");
    		        sb.append("  T.APPOINTMENT_ID as APPOINTMENTID, ");
    		        sb.append("  L.EXTERNAL_ID EXTERNAL_ID, ");
    		        sb.append("  L.LOCATION_NAME LOCATION_NAME, ");
    		        sb.append("  L.ADDRESS_1 ADDRESS_1, ");
    		        sb.append("  L.CITY CITY, ");
    		        sb.append("  L.STATE STATE, ");
    		        sb.append("  L.ZIP ZIP, ");
    		        sb.append("  T.NOTE NOTE, ");
    		        sb.append("  tt.transaction_id transaction_id, ");
    		        sb.append("  I.EXTERNAL_ID AS INSTRUCTOR_EXTERNALID, ");
    		        sb.append("  P_I.FIRST_NAME || ' ' || P_I.LAST_NAME AS INSTRUCTOR_NAME, ");
    		        sb.append("  TO_CHAR(T.START_TIME, 'MM-DD-YYYY') AS DATE_OF_APPOINTMENT , ");
    		        sb.append("  TO_CHAR( T.END_TIME, 'HH:MI AM') AS TIME_FRAME, ");
    		        sb.append("  (EXTRACT(DAY FROM ((T.END_TIME - T.START_TIME)*24*60))) AS DURATION, ");
    		        sb.append("  A.ACTIVITY_NAME AS ACTIVITY_NAME, ");
    		        sb.append("  C.EXTERNAL_ID AS CUSTOMER, ");
    		        sb.append("  P_C.FIRST_NAME || ' ' || P_C.LAST_NAME AS CUSTOMER_NAME, ");
    		        sb.append("  P_C.EMAIL CUSTOMER_EMAIL, ");
    		        sb.append("  P_C.PHONE CUSTOMER_PHONE ");
    		        sb.append("FROM APPOINTMENT T ");
    		        sb.append("JOIN APPOINTMENT_CUSTOMERS A_C ON T.APPOINTMENT_ID = A_C.APPOINTMENT_ID ");
    		        sb.append("LEFT JOIN ACTIVITY A ON T.ACTIVITY_ID = A.ACTIVITY_ID ");
    		        sb.append("LEFT JOIN CUSTOMER C ON A_C.CUSTOMER_ID = C.CUSTOMER_ID ");
    		        sb.append("LEFT JOIN LOCATION L ON T.PROFILE_ID = L.PROFILE_ID ");
    		        sb.append("LEFT JOIN PERSON P_C ON C.PERSON_ID = P_C.PERSON_ID ");
    		        sb.append("LEFT JOIN INSTRUCTOR I ON T.INSTRUCTOR_ID = I.INSTRUCTOR_ID ");
    		        sb.append("LEFT JOIN PERSON P_I ON I.PERSON_ID = P_I.PERSON_ID ");
    		        sb.append(" JOIN appointment_transactions tt ON t.appointment_id = tt.appointment_id ");
    		        sb.append("WHERE DECODE(T.CANCELED, NULL, 'N', T.CANCELED) != 'Y' ");
    		        sb.append("AND A.ACTIVITY_ID IN ('140','320') ");
    		        sb.append("AND to_date(to_char(t.start_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') >= :startTime ");
    		        sb.append("AND to_date(to_char(t.start_time, 'YYYY-MM-DD'), 'YYYY-MM-DD') <= :endTime ");

    		        LOG.info("Creating SQLQuery...");
    		        SQLQuery query = pSession.createSQLQuery(sb.toString());

    		        LOG.info("Adding scalar mappings...");
    		        query.addScalar("APPOINTMENTID", StandardBasicTypes.LONG);
    		        query.addScalar("EXTERNAL_ID", StandardBasicTypes.STRING);
    		        query.addScalar("LOCATION_NAME", StandardBasicTypes.STRING);
    		        query.addScalar("ADDRESS_1", StandardBasicTypes.STRING);
    		        query.addScalar("CITY", StandardBasicTypes.STRING);
    		        query.addScalar("STATE", StandardBasicTypes.STRING);
    		        query.addScalar("ZIP", StandardBasicTypes.STRING);
    		        query.addScalar("transaction_id", StandardBasicTypes.STRING);
    		        query.addScalar("INSTRUCTOR_EXTERNALID", StandardBasicTypes.STRING);
    		        query.addScalar("INSTRUCTOR_NAME", StandardBasicTypes.STRING);
    		        query.addScalar("DATE_OF_APPOINTMENT", StandardBasicTypes.STRING);
    		        query.addScalar("TIME_FRAME", StandardBasicTypes.STRING);
    		        query.addScalar("DURATION", StandardBasicTypes.STRING);
    		        query.addScalar("ACTIVITY_NAME", StandardBasicTypes.STRING);
    		        query.addScalar("CUSTOMER", StandardBasicTypes.STRING);
    		        query.addScalar("NOTE", StandardBasicTypes.STRING);
    		        query.addScalar("CUSTOMER_NAME", StandardBasicTypes.STRING);
    		        query.addScalar("CUSTOMER_EMAIL", StandardBasicTypes.STRING);
    		        query.addScalar("CUSTOMER_PHONE", StandardBasicTypes.STRING);

    		        LOG.info("Setting parameters...");
    		        query.setDate("startTime", pStartTime);
    		        query.setDate("endTime", pEndTime);

    		        LOG.info("Executing scrollable query...");
    		        ScrollableResults scroll = query.scroll();
    		        LessonCompletionDTO lessonCompletionDTO;

    		        while (scroll.next()) {
    		            try {
    		                Object[] objects = scroll.get();
    		                lessonCompletionDTO = new LessonCompletionDTO();

    		                lessonCompletionDTO.setAppointmentId(objects[0] != null ? (Long) objects[0] : null);
    		                lessonCompletionDTO.setStoreNumber(objects[1] != null ? (String) objects[1] : null);
    		                lessonCompletionDTO.setStoreStreet(objects[3] != null ? (String) objects[3] : null);
    		                lessonCompletionDTO.setStoreCity(objects[4] != null ? (String) objects[4] : null);
    		                lessonCompletionDTO.setStoreState(objects[5] != null ? (String) objects[5] : null);
    		                lessonCompletionDTO.setStoreZip(objects[6] != null ? (String) objects[6] : null);
    		                lessonCompletionDTO.setLessonsFollowupZipCode(lessonCompletionDTO.getStoreZip());
    		                lessonCompletionDTO.setComments(objects[15] != null ? (String) objects[15] : null);

    		                // extract SFCustomerID using regex if not null
    		                lessonCompletionDTO.setSfCustomerID(
    		                    objects[7] != null 
    		                        ? ((String) objects[7]).replaceFirst("^.*?_(.*?)_.*$", "$1") 
    		                        : null
    		                );

    		                lessonCompletionDTO.setLessonsCompletedDate(objects[10] != null ? (String) objects[10] : null);
    		                lessonCompletionDTO.setLessonsCompletedTime(objects[11] != null ? (String) objects[11] : null);
    		                lessonCompletionDTO.setLessonsDuration(objects[12] != null ? (String) objects[12] : null);
    		                lessonCompletionDTO.setLessonsType(objects[13] != null ? (String) objects[13] : null);
    		                lessonCompletionDTO.setLessonsFollowupName(objects[16] != null ? (String) objects[16] : null);
    		                lessonCompletionDTO.setLessonsFollowupEmail(objects[17] != null ? (String) objects[17] : null);

    		                // format phone number if valid 10-digit string
    		                lessonCompletionDTO.setLessonsFollowupPhone(
    		                    objects[18] != null && objects[18].toString().length() == 10
    		                        ? objects[18].toString().substring(0, 3) + "-" 
    		                          + objects[18].toString().substring(3, 6) + "-" 
    		                          + objects[18].toString().substring(6)
    		                        : objects[18] != null 
    		                            ? objects[18].toString() 
    		                            : null
    		                );

    		                lessonCompletionDTO.setEvent("Completed");
    		                lessonCompletionDTO.setLessonsFollowup("Y");
    		                lessonCompletionDTO.setUpdated(
    		                    java.time.LocalDateTime.now()
    		                        .format(java.time.format.DateTimeFormatter.ofPattern("MM-dd-yyyy hh:mm:ss a"))
    		                );
    		                lessonCompletionDTO.setSource("GCSS");


    		                maps.put(lessonCompletionDTO.getAppointmentId(), lessonCompletionDTO);
    		            } catch (Exception e) {
    		                LOG.error("Error processing row in scrollable result: {}", e.getMessage(), e);
    		              //  throw new RuntimeException("Failed to retrieve lesson completion data", e);
    		                
    		            }
    		        }

    		        if (!maps.isEmpty()) {
    		            result.addAll(maps.values());
    		        }

    		    } catch (Exception e) {
    		        LOG.error("Error during search(): {}", e.getMessage(), e);
    		        throw new RuntimeException("Failed to retrieve lesson completion data", e);
    		    }

    		    return result;
    		}
};
		}
    
    //end of OLL-3732

    
    public static Criterion<Appointment, EmployeeScheduleDTO> findMasterEmployeeDetails(final String pStartTime, final String pEndTime) {

    		return new AppointmentCriterion<EmployeeScheduleDTO>() {

						@Override
						@SuppressWarnings("unchecked")
						public List<EmployeeScheduleDTO> search(Session pSession, int pFetchMode) {
							StringBuilder sb = new StringBuilder("SELECT ");
							 							sb.append(" i.EXTERNAL_ID as Instructor_ID,");

							sb.append(" l.external_id storeNumber, ");
							sb.append(" to_char(t.start_time, 'YYYY-MM-DD\"T\"HH24:MI:SS') as START_DATE_OF_APPOINTMENT , ");
							sb.append(" to_char(t.end_time, 'YYYY-MM-DD\"T\"HH24:MI:SS') as END_DATE_OF_APPOINTMENT, ");
							sb.append(" a.activity_name  activity_name ");
							sb.append(" from appointment t ");
							sb.append(" join location l on t.profile_id = l.profile_id ");
							sb.append(" left join appointment_customers a_c on t.appointment_id = a_c.appointment_id ");
							sb.append("  left join LOCATION_PROFILE lp  on t.profile_id = lp.profile_id  "); 
							sb.append("  left join customer c on a_c.customer_id = c.customer_id  ");                   
							sb.append("  left join person p_c on c.person_id = p_c.person_id ");
							sb.append(" join instructor i on t.instructor_id = i.instructor_id ");
							sb.append(" left join person p_i on i.person_id = p_i.person_id ");
							sb.append(" left join PERSON_PERSONAL_DETAILS ppd on ppd.person_id = p_i.person_id ");
							sb.append(" left join room r on t.room_id = r.room_id ");
							sb.append(" left join activity a on t.activity_id = a.activity_id ");
							sb.append(" left join availability ay on i.availability_id = ay.availability_id ");
							sb.append(" where l.location_id in (select location_id from location l ,LOCATION_PROFILE lp where l.PROFILE_ID = lp.PROFILE_ID and lp.ENABLED ='Y' ) ");
							sb.append(" and t.start_time BETWEEN  to_date(:startTime, 'YYYY-DD-MM HH24:MI:SS') and to_date(:endTime, 'YYYY-DD-MM HH24:MI:SS') ");
						 	sb.append(" and decode(t.canceled, null, 'N', t.canceled) != 'Y' and l.external_id in ('101','110')");
							//sb.append(" and decode(t.canceled, null, 'N', t.canceled) != 'Y' ");
						   sb.append(" and a.ACTIVITY_ID not in ('200','100','140','0','1','160','6')  ");
							
							//sb.append(" and i.external_id in ('116603') ");
							sb.append(" order by l.external_id,to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') ");
							
							
						SQLQuery query = pSession.createSQLQuery(sb.toString());
						//System.out.println(sb.toString());
						
						//System.out.println("pStartTime  "+pStartTime);
						//System.out.println("pEndTime    "+pEndTime);
						 
			                query.addScalar("storeNumber", StandardBasicTypes.STRING);
			                query.addScalar("Instructor_ID", StandardBasicTypes.STRING);
			                query.addScalar("START_DATE_OF_APPOINTMENT", StandardBasicTypes.STRING);
			                query.addScalar("END_DATE_OF_APPOINTMENT", StandardBasicTypes.STRING);
			                query.addScalar("activity_name", StandardBasicTypes.STRING);
			                 
			                
												
						/**
						* For GCSS-544,add join to fetch AppointmentSeries
						*/
					//	query.addJoin("aps", "t.appointmentSeries");
						
						 
						query.setString("startTime", pStartTime);
					//	 query.setDate("startTime", pStartTime);
			            query.setString("endTime", pEndTime);
			                
						//List<Object[]> list = query.list();
						ScrollableResults scroll = query.scroll();
						 
						EmployeeScheduleDTO employeeScheduleDTO = null;
						  
						 List<EmployeeScheduleDTO> result = new ArrayList<EmployeeScheduleDTO>();
						  while (scroll.next()) {
							  employeeScheduleDTO = new EmployeeScheduleDTO();

			                    Object[] objects = scroll.get();
			                
			                   // System.out.println(objects[4]);
			                    employeeScheduleDTO.setStore((String)objects[0]);
			                    employeeScheduleDTO.setInstructorId((String)objects[1]);
			                    employeeScheduleDTO.setStartTime((String)objects[2]);
			                    employeeScheduleDTO.setStartDateTime(getDateFromString(employeeScheduleDTO.getStartTime()));
			                    employeeScheduleDTO.setEndTime((String)objects[3]);
			                    employeeScheduleDTO.setEndDateTime(getDateFromString(employeeScheduleDTO.getEndTime()));
			                    employeeScheduleDTO.setActvity((String)objects[4]);
			                  
			                    
			                 //   Long appointment_id = (Long) objects[1];
			                    
			                   // customerAppointmentDetailsResultDTO.setAppointmentId(objects[0]);
			                  
			                    result.add(employeeScheduleDTO); 
			                  //  maps.put(customerAppointmentDetailsResultDTO.getAppointmentId(), customerAppointmentDetailsResultDTO);

			                }
						  
						
						 
						return result;
						}
						
			};
		}
    private static Date getDateFromString(String dateStr){
    	//dateStr = "2022-11-14T12:30:00";
    	dateStr =dateStr.replace("T"," ");
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //String dateInString = "7-Jun-2013";
        Date date = new Date();

        try {

              date = formatter.parse(dateStr);

        } catch (ParseException e) {
        	LOG.error("Caught {} when check getDateFromString Appointment", e);
        }
        return date;

	}  
    
    
    public static Criterion<Appointment, InstructorAVLServiceResponseDTO> findLocationDistAndRegin() {

        return new AppointmentCriterion<InstructorAVLServiceResponseDTO>() {

            @Override
            public List<InstructorAVLServiceResponseDTO> search(Session pSession, int pFetchMode) {
                 
                SQLQuery query = null;
                String sb = "select l.external_id external_id,l.LOCATION_NAME LOCATION_NAME,ldr.location_dst_desc location_dst_desc,ldr.LOCATION_REG_DESC LOCATION_REG_DESC from location l,location_dst_reg ldr where l.location_id = ldr.location_id";
                //GSSP-296  changes for instructor open Appointments
                String fullName=null;
               
                query = pSession.createSQLQuery(sb);
                //query.setLong("locationId", pLocationId);
                
                
                query.addScalar("external_id", StandardBasicTypes.STRING);
                query.addScalar("LOCATION_NAME", StandardBasicTypes.STRING);
                query.addScalar("location_dst_desc", StandardBasicTypes.STRING);
                query.addScalar("LOCATION_REG_DESC", StandardBasicTypes.STRING);
 
                ScrollableResults scroll = query.scroll();
                List<InstructorAVLServiceResponseDTO> result = new ArrayList<InstructorAVLServiceResponseDTO>();
                
                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    
                    InstructorAVLServiceResponseDTO isrDto = new  InstructorAVLServiceResponseDTO();
 
                    isrDto.setStoreNumber((String) objects[0]);
                    isrDto.setStoreLocation((String) objects[1]);
                    isrDto.setStoreDistrict((String) objects[2]);
                    isrDto.setStoreRegion((String) objects[3]);
                    
                    if(isrDto.getStoreNumber()==null)isrDto.setStoreNumber("");
                    if(isrDto.getStoreLocation()==null)isrDto.setStoreLocation("");
                    if(isrDto.getStoreDistrict()==null)isrDto.setStoreDistrict("");
                    if(isrDto.getStoreRegion()==null)isrDto.setStoreRegion("");
                    

                    result.add(isrDto);

                }
                scroll.close();
                return result;
            }
        };
    }

   

	
    
/*    
    public static Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> findInstructorApptsExcludingBreaks(
            final Long instructorId, final Date pStartTime, final Date pEndTime, final String pstartDate) {

        return new AppointmentCriterion<Map<String, List<InstructorAvailableHoursDTO>>>() {

            @Override
            public Map<String, List<InstructorAvailableHoursDTO>> get(Session pSession, int pFetchMode) {


                String sb = "select to_char(t.start_time, 'yyyy-MM-dd') as date1 , " +
                        "to_char(t.start_time,'HH24:Mi') as start_time, to_char(t.end_time,'HH24:Mi') as end_time from appointment t " +
                        "where  TO_CHAR(t.start_time, 'yyyy-mm-dd') = :pstartDate   and decode(t.canceled, null, 'N', t.canceled) != 'Y'  " +
                        "and t.ACTIVITY_ID not in ('20','120','300','400','360','401') " +
                        "and t.instructor_id in (:instructorId) " +
                        "minus " +
                        "select to_char(t.start_time, 'yyyy-MM-dd') as date1 , " +
                        "to_char(t.start_time,'HH24:Mi') as start_time, " +
                        "to_char(t.end_time,'HH24:Mi') as end_time " +
                        "from appointment t ,onetime ot " +
                        "where " +
                        "t.ACTIVITY_ID not in ('20','120','300','400','360','401') " +
                        "and t.instructor_id = ot.instructor_id " +
                        "and TO_CHAR(t.start_time, 'yyyy-mm-dd') = :pstartDate " +
                        //--"and t.end_time <= :pEndTime " +
                        "and t.start_time >= ot.start_time " +
                        "and t.end_time <= ot.end_time " +
                        "and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        "and t.instructor_id in (:instructorId) order by date1 desc " ;


                SQLQuery query = pSession.createSQLQuery(sb);

                Long instructorId2 =20057l;
                query.setLong("instructorId", instructorId2);
                query.setString("pstartDate", pstartDate);
                //query.setDate("pEndTime", pEndTime);

                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.STRING);

                Map<String, List<InstructorAvailableHoursDTO>> cache = new HashMap<String, List<InstructorAvailableHoursDTO>>();

                InstructorAvailableHoursDTO instructorAvailableHoursDTO = null;

                List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList = null;

                ScrollableResults scroll = query.scroll();

                while (scroll.next()) {

                    Object[] objects = scroll.get();
                    String instructorDate = (String) objects[0];

                    if (!cache.containsKey(instructorDate)) {

                        List<InstructorAvailableHoursDTO> list = new ArrayList<InstructorAvailableHoursDTO>();
                        cache.put(instructorDate, list);

                    }

                    instructorAvailableHoursDTOList = cache.get(instructorDate);

                    DateTimeFormatter df1 = DateTimeFormat.forPattern(DateTimeUtil.HOURS_PATTERN);

                    String startTime = (String) objects[1];
                    String endTime = (String) objects[2];
                    instructorAvailableHoursDTO = new InstructorAvailableHoursDTO(df1.parseLocalTime(startTime),df1.parseLocalTime(endTime)) ;

                    instructorAvailableHoursDTOList.add(instructorAvailableHoursDTO);

                }
                scroll.close();
                return cache;

            }
        };

    }*/
    
    public static Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> findInstructorApptsIncludingBreaks(
            final Long instructorId, final Date pStartTime, final Date pEndTime, final String pstartDate, final String pstartTime1, final String pstartTime2) {

        return new AppointmentCriterion<Map<String, List<InstructorAvailableHoursDTO>>>() {

            @Override
            public Map<String, List<InstructorAvailableHoursDTO>> get(Session pSession, int pFetchMode) {


                String sb = "select to_char(t.start_time, 'yyyy-MM-dd') as date1 , " +
                        "to_char(t.start_time,'HH24:Mi') as start_time, to_char(t.end_time,'HH24:Mi') as end_time from appointment t " +
                        "where  to_date(to_char(t.start_time, 'YYYY-DD-MM HH24:Mi'), 'YYYY-DD-MM HH24:Mi')  >= to_date( :pstartDate1,'YYYY-DD-MM HH24:Mi') AND  to_date(to_char(t.end_time, 'YYYY-DD-MM HH24:Mi'), 'YYYY-DD-MM HH24:Mi')  <= to_date( :pstartDate2,'YYYY-DD-MM HH24:Mi')        "
                        + "and decode(t.canceled, null, 'N', t.canceled) != 'Y'  " +
                        "and t.ACTIVITY_ID not in ('300','400','401','20','360','120') " +
                        "and t.instructor_id in (:instructorId) " +
                        "minus " +
                        "select to_char(t.start_time, 'yyyy-MM-dd') as date1 , " +
                        "to_char(t.start_time,'HH24:Mi') as start_time, " +
                        "to_char(t.end_time,'HH24:Mi') as end_time " +
                        "from appointment t ,onetime ot " +
                        "where " +
                        "t.ACTIVITY_ID not in ('300','400','401','20','360','120') " +
                        "and t.instructor_id = ot.instructor_id " +
                        "and TO_CHAR(t.start_time, 'yyyy-mm-dd') = :pstartDate " +
                        //--"and t.end_time <= :pEndTime " +
                        "and t.start_time >= ot.start_time " +
                        "and t.end_time <= ot.end_time " +
                        "and decode(t.canceled, null, 'N', t.canceled) != 'Y' " +
                        "and t.instructor_id in (:instructorId) order by date1 desc " ;


                SQLQuery query = pSession.createSQLQuery(sb);

                //Long instructorId2 =20057l;
                query.setLong("instructorId", instructorId);
                query.setString("pstartDate", pstartDate);
                query.setString("pstartDate1", pstartTime1);
                query.setString("pstartDate2", pstartTime2);
                
                //query.setDate("pEndTime", pEndTime);

                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.STRING);

                Map<String, List<InstructorAvailableHoursDTO>> cache = new HashMap<String, List<InstructorAvailableHoursDTO>>();

                InstructorAvailableHoursDTO instructorAvailableHoursDTO = null;

                List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList = null;

                ScrollableResults scroll = query.scroll();

                while (scroll.next()) {

                    Object[] objects = scroll.get();
                    String instructorDate = (String) objects[0];

                    if (!cache.containsKey(instructorDate)) {

                        List<InstructorAvailableHoursDTO> list = new ArrayList<InstructorAvailableHoursDTO>();
                        cache.put(instructorDate, list);

                    }

                    instructorAvailableHoursDTOList = cache.get(instructorDate);

                    DateTimeFormatter df1 = DateTimeFormat.forPattern(DateTimeUtil.HOURS_PATTERN);

                    String startTime = (String) objects[1];
                    String endTime = (String) objects[2];
                    instructorAvailableHoursDTO = new InstructorAvailableHoursDTO(df1.parseLocalTime(startTime),df1.parseLocalTime(endTime)) ;

                    instructorAvailableHoursDTOList.add(instructorAvailableHoursDTO);

                }
                scroll.close();
                return cache;

            }
        };

    }    
    public static Criterion<Appointment, List<InstructorServiceResponseDTO>> findInstructorMaxAndMinTime(
            final Long instructorId, final Date pStartTime, final Date pEndTime, final String pstartDate) {

        return new AppointmentCriterion<List<InstructorServiceResponseDTO>>() {

            @Override
            public List<InstructorServiceResponseDTO> get(Session pSession, int pFetchMode) {


                String sb = "SELECT to_char(MIN(start_time),'YYYY-DD-MM HH24:Mi') as start_time, "
                		+ "to_char(MAX(end_time),'YYYY-DD-MM HH24:Mi') as  end_time FROM appointment  "
                		+ "WHERE  TO_CHAR( start_time,'yyyy-mm-dd') =:pstartDate and canceled = 'N'  and instructor_id = :instructorId "
                		+ "AND activity_id not IN (20,120,360) group by TO_CHAR(start_time,'yyyy-MM-dd')  " ;


                SQLQuery query = pSession.createSQLQuery(sb);

               //Long instructorId2 =20057l;
                query.setLong("instructorId", instructorId);
                query.setString("pstartDate", pstartDate);
             
                
                //query.setDate("pEndTime", pEndTime);

                //query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.STRING);

                Map<String, List<InstructorServiceResponseDTO>> cache = new HashMap<String, List<InstructorServiceResponseDTO>>();

                InstructorServiceResponseDTO instructorServiceResponseDTO = null;

                List<InstructorServiceResponseDTO> instructorServiceResponseDTOList = new ArrayList<InstructorServiceResponseDTO>();

                ScrollableResults scroll = query.scroll();

                while (scroll.next()) {

                    Object[] objects = scroll.get();
                   /* String instructorDate = (String) objects[0];

                    if (!cache.containsKey(instructorDate)) {

                        List<InstructorAvailableHoursDTO> list = new ArrayList<InstructorAvailableHoursDTO>();
                        cache.put(instructorDate, list);

                    }

                    instructorAvailableHoursDTOList = cache.get(instructorDate);*/
                    
                    instructorServiceResponseDTO = new InstructorServiceResponseDTO();

                    DateTimeFormatter df1 = DateTimeFormat.forPattern(DateTimeUtil.HOURS_PATTERN);

                    String startTime = (String) objects[0];
                    String endTime = (String) objects[1];
                    instructorServiceResponseDTO.setSeriesStartDate(startTime);
                    instructorServiceResponseDTO.setSeriesEndDate(endTime);
                    //instructorAvailableHoursDTO = new InstructorAvailableHoursDTO(df1.parseLocalTime(startTime),df1.parseLocalTime(endTime)) ;

                    instructorServiceResponseDTOList.add(instructorServiceResponseDTO);

                }
                scroll.close();
                return instructorServiceResponseDTOList;

            }
        };

    } 
    
    public static Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> findBreakMoreThan30(
            final Long instructorId, final Date pStartTime, final Date pEndTime, final String pstartDate, final String pstartTime1, final String pstartTime2) {

        return new AppointmentCriterion<Map<String, List<InstructorAvailableHoursDTO>>>() {

            @Override
            public Map<String, List<InstructorAvailableHoursDTO>> get(Session pSession, int pFetchMode) {


                String sb = "select to_char(t.start_time, 'yyyy-MM-dd') as date1 , " +
                        "to_char(t.start_time,'HH24:Mi') as start_time, to_char(t.end_time,'HH24:Mi') as end_time from appointment t " +
                        "where  to_date(to_char(t.start_time, 'YYYY-DD-MM HH24:Mi'), 'YYYY-DD-MM HH24:Mi')  >= to_date( :pstartDate1,'YYYY-DD-MM HH24:Mi') AND  to_date(to_char(t.end_time, 'YYYY-DD-MM HH24:Mi'), 'YYYY-DD-MM HH24:Mi')  <= to_date( :pstartDate2,'YYYY-DD-MM HH24:Mi')        "
                        + "and decode(t.canceled, null, 'N', t.canceled) != 'Y'  " +
                        "and t.ACTIVITY_ID IN (20,120,360)  " +
                        "and t.instructor_id in (:instructorId) " +                     
                        " AND round (( CAST( t.end_time AS DATE ) - CAST( t.start_time AS DATE ) ) * 1441)  >= 0 " ;


                SQLQuery query = pSession.createSQLQuery(sb);

                //Long instructorId2 =20057l;
                query.setLong("instructorId", instructorId);
                //query.setString("pstartDate", pstartDate);
                query.setString("pstartDate1", pstartTime1);
                query.setString("pstartDate2", pstartTime2);
                
                //query.setDate("pEndTime", pEndTime);

                query.addScalar("date1", StandardBasicTypes.STRING);
                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.STRING);

                Map<String, List<InstructorAvailableHoursDTO>> cache = new HashMap<String, List<InstructorAvailableHoursDTO>>();

                InstructorAvailableHoursDTO instructorAvailableHoursDTO = null;

                List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList = null;

                ScrollableResults scroll = query.scroll();

                while (scroll.next()) {

                    Object[] objects = scroll.get();
                    String instructorDate = (String) objects[0];

                    if (!cache.containsKey(instructorDate)) {

                        List<InstructorAvailableHoursDTO> list = new ArrayList<InstructorAvailableHoursDTO>();
                        cache.put(instructorDate, list);

                    }

                    instructorAvailableHoursDTOList = cache.get(instructorDate);

                    DateTimeFormatter df1 = DateTimeFormat.forPattern(DateTimeUtil.HOURS_PATTERN);

                    String startTime = (String) objects[1];
                    String endTime = (String) objects[2];
                    instructorAvailableHoursDTO = new InstructorAvailableHoursDTO(df1.parseLocalTime(startTime),df1.parseLocalTime(endTime)) ;
                    //instructorAvailableHoursDTO.setActvityID(25l);
                    instructorAvailableHoursDTOList.add(instructorAvailableHoursDTO);

                }
                scroll.close();
                return cache;

            }
        };

    }

    /**
     * Retrieves transactionId from AppointmentTransactions table based on appointmentId
     *
     * @param appointmentId Long - the appointment ID to search for
     * @return Criterion instance that returns the transactionId as String
     */
    public static AppointmentCriterion<String> findTransactionIdByAppointmentId(final Long appointmentId) {

        return new AppointmentCriterion<String>() {

            @Override
            public String get(Session pSession, int pFetchMode) {
                String sb = "select transaction_id from appointment_transactions " +
                        "where appointment_id = :appointmentId";

                SQLQuery query = pSession.createSQLQuery(sb);
                query.setLong("appointmentId", appointmentId);
                query.addScalar("transaction_id", StandardBasicTypes.STRING);

                ScrollableResults scroll = query.scroll();

                String transactionId = null;
                while (scroll.next()) {
                    Object[] objects = scroll.get();
                    transactionId = (String) objects[0];
                }
                scroll.close();

                return transactionId;
            }
        };
    }
}

