/**
 * @Title: TimeoffCriterion.java
 * @Package com.guitarcenter.scheduler.dao.criterion
 * Copyright: Copyright (c) 2014 
 * Company:
 * 
 * <AUTHOR>
 * @date Mar 10, 2014 4:16:30 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_INSTRUCTOR;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.type.StandardBasicTypes;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dto.TimeLineDTO;
import com.guitarcenter.scheduler.dto.TimeoffDateDTO;
import com.guitarcenter.scheduler.model.Timeoff;

public abstract class TimeoffCriterion<E> extends AbstractCriterion<Timeoff, E> implements Criterion<Timeoff, E> {

	private static final Criterion<Timeoff, Timeoff> DEFAULT_INSTANCE = new TimeoffCriterion<Timeoff>() {};

	private TimeoffCriterion() {
		super(Timeoff.class);
	}

	@Override
	protected String getFetchScript(int pFetchMode) {
		StringBuilder sb = new StringBuilder();
		sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
        sb.append(addFetchHQL(FETCH_INSTRUCTOR, pFetchMode, "t.instructor", true));
		return sb.toString();
	}

	@Override
	public List<E> search(Session pSession, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}
	
	@Override
	public E get(Session pSession, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}

	public static Criterion<Timeoff, Timeoff> getInstance() {
		return DEFAULT_INSTANCE;
	}
	
	 public static TimeoffCriterion<TimeoffDateDTO> findAllTimeoffListByDate(final Long pLocationId, final Date pDate) {
		 
		 return new TimeoffCriterion<TimeoffDateDTO>() {
			 @SuppressWarnings("unchecked")
			public List<TimeoffDateDTO> search(Session pSession, int pFetchMode) {

				 DateTimeFormatter formatter = DateTimeFormat.forPattern(DateTimeUtil.YYYY_MM_DD);
				 String queryDate = new DateTime(pDate).toString(formatter);
				 
				 StringBuilder sb = new StringBuilder();
				 sb.append(" select t3.instructor_id, to_char(min(t3.min_1), 'YYYY-MM-DD HH24:MI:SS') start_time, to_char(t3.max_1, 'YYYY-MM-DD HH24:MI:SS') end_time  ");
				 sb.append("  from (select t2.instructor_id, t2.min_1, max(t2.max_1) max_1                       ");
				 sb.append("          from (select t1.*,                                                         ");
				 sb.append("                       (select min(sub_t.start_time)                                 ");
				 sb.append("                          from timeoff sub_t                                         ");
				 sb.append("                          left join instructor i on sub_t.instructor_id=i.instructor_id  ");
				 sb.append("                         where i.location_id=:locationId                             ");
				 sb.append("                            and (to_char(sub_t.start_time, 'YYYY-MM-DD') =                 ");
				 sb.append("                               :queryDate or                                       ");
				 sb.append("                               to_char(sub_t.end_time, 'YYYY-MM-DD') =               ");
				 sb.append("                               :queryDate or                                       ");
				 sb.append("                               (to_char(sub_t.start_time, 'YYYY-MM-DD') <=           ");
				 sb.append("                               :queryDate and                                      ");
				 sb.append("                               to_char(sub_t.end_time, 'YYYY-MM-DD') >=              ");
				 sb.append("                               :queryDate))                                        ");
				 sb.append("                           and sub_t.instructor_id = t1.instructor_id                ");
				 sb.append("                           and ((sub_t.start_time <= t1.min_ and                     ");
				 sb.append("                               sub_t.end_time >= t1.min_) or                         ");
				 sb.append("                               (sub_t.start_time <= t1.max_ and                      ");
				 sb.append("                               sub_t.end_time >= t1.max_) or                         ");
				 sb.append("                               (sub_t.start_time >= t1.min_ and                      ");
				 sb.append("                               sub_t.end_time <= t1.max_))) min_1,                   ");
				 sb.append("                       (select max(sub_t.end_time)                                   ");
				 sb.append("                          from timeoff sub_t                                         ");
				 sb.append("                          left join instructor i on sub_t.instructor_id=i.instructor_id  ");
				 sb.append("                         where i.location_id=:locationId                             ");
				 sb.append("                            and (to_char(sub_t.start_time, 'YYYY-MM-DD') =                 ");
				 sb.append("                               :queryDate or                                       ");
				 sb.append("                               to_char(sub_t.end_time, 'YYYY-MM-DD') =               ");
				 sb.append("                               :queryDate or                                       ");
				 sb.append("                               (to_char(sub_t.start_time, 'YYYY-MM-DD') <=           ");
				 sb.append("                               :queryDate and                                      ");
				 sb.append("                               to_char(sub_t.end_time, 'YYYY-MM-DD') >=              ");
				 sb.append("                               :queryDate))                                        ");
				 sb.append("                           and sub_t.instructor_id = t1.instructor_id                ");
				 sb.append("                           and ((sub_t.start_time <= t1.min_ and                     ");
				 sb.append("                               sub_t.end_time >= t1.min_) or                         ");
				 sb.append("                               (sub_t.start_time <= t1.max_ and                      ");
				 sb.append("                               sub_t.end_time >= t1.max_) or                         ");
				 sb.append("                               (sub_t.start_time >= t1.min_ and                      ");
				 sb.append("                               sub_t.end_time <= t1.max_))) max_1                    ");
				 sb.append("                  from (select t.*,                                                  ");
				 sb.append("                               (select min(sub_t.start_time)                         ");
				 sb.append("                                  from timeoff sub_t                                 ");
				 sb.append("                          left join instructor i on sub_t.instructor_id=i.instructor_id  ");
				 sb.append("                         where i.location_id=:locationId                             ");
				 sb.append("                            and (to_char(sub_t.start_time, 'YYYY-MM-DD') =                 ");
				 sb.append("                                       :queryDate or                               ");
				 sb.append("                                       to_char(sub_t.end_time, 'YYYY-MM-DD') =       ");
				 sb.append("                                       :queryDate or                               ");
				 sb.append("                                       (to_char(sub_t.start_time, 'YYYY-MM-DD') <=   ");
				 sb.append("                                       :queryDate and                              ");
				 sb.append("                                       to_char(sub_t.end_time, 'YYYY-MM-DD') >=      ");
				 sb.append("                                       :queryDate))                                ");
				 sb.append("                                   and sub_t.instructor_id = t.instructor_id         ");
				 sb.append("                                   and ((sub_t.start_time <= t.start_time and        ");
				 sb.append("                                       sub_t.end_time >= t.start_time) or            ");
				 sb.append("                                       (sub_t.start_time <= t.end_time and           ");
				 sb.append("                                       sub_t.end_time >= t.end_time) or              ");
				 sb.append("                                       (sub_t.start_time >= t.start_time and         ");
				 sb.append("                                       sub_t.end_time <= t.end_time))) min_,         ");
				 sb.append("                               (select max(sub_t.end_time)                           ");
				 sb.append("                                  from timeoff sub_t                                 ");
				 sb.append("                          left join instructor i on sub_t.instructor_id=i.instructor_id  ");
				 sb.append("                         where i.location_id=:locationId                             ");
				 sb.append("                            and (to_char(sub_t.start_time, 'YYYY-MM-DD') =                 ");
				 sb.append("                                       :queryDate or                                 ");
				 sb.append("                                       to_char(sub_t.end_time, 'YYYY-MM-DD') =       ");
				 sb.append("                                       :queryDate or                                 ");
				 sb.append("                                       (to_char(sub_t.start_time, 'YYYY-MM-DD') <=   ");
				 sb.append("                                       :queryDate and                                ");
				 sb.append("                                       to_char(sub_t.end_time, 'YYYY-MM-DD') >=      ");
				 sb.append("                                       :queryDate))                                  ");
				 sb.append("                                   and sub_t.instructor_id = t.instructor_id         ");
				 sb.append("                                   and ((sub_t.start_time <= t.start_time and        ");
				 sb.append("                                       sub_t.end_time >= t.start_time) or            ");
				 sb.append("                                       (sub_t.start_time <= t.end_time and           ");
				 sb.append("                                       sub_t.end_time >= t.end_time) or              ");
				 sb.append("                                       (sub_t.start_time >= t.start_time and         ");
				 sb.append("                                       sub_t.end_time <= t.end_time))) max_          ");
				 sb.append("                          from timeoff t                                            ");
				 sb.append("                          left join instructor i on t.instructor_id=i.instructor_id  ");
				 sb.append("                         where i.location_id=:locationId                             ");
				 sb.append("                            and (to_char(t.start_time, 'YYYY-MM-DD') =                 ");
				 sb.append("                               :queryDate                                            ");
				 sb.append("                            or to_char(t.end_time, 'YYYY-MM-DD') =                   ");
				 sb.append("                               :queryDate                                            ");
				 sb.append("                            or (to_char(t.start_time, 'YYYY-MM-DD') <=               ");
				 sb.append("                                :queryDate and                                      ");
				 sb.append("                                to_char(t.end_time, 'YYYY-MM-DD') >=                 ");
				 sb.append("                                :queryDate))) t1) t2                                  ");
				 sb.append("         group by t2.instructor_id, t2.min_1) t3                                     ");
				 sb.append(" group by t3.instructor_id, t3.max_1                                                ");
				 SQLQuery query = pSession.createSQLQuery(sb.toString());
				 query.setString("queryDate", queryDate);
				 query.setLong("locationId", pLocationId);
				 query.addScalar("instructor_id", StandardBasicTypes.LONG);
				 query.addScalar("start_time", StandardBasicTypes.STRING);
				 query.addScalar("end_time", StandardBasicTypes.STRING);
				 List<Object[]> list = query.list();
				 List<TimeoffDateDTO> result = new ArrayList<TimeoffDateDTO>();
				 if(null != list && !list.isEmpty()) {
					 DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN);
					 for(Object[] objs : list) {
						 Long instructorId = (Long) objs[0];
						 String startTimeStr = (String) objs[1];
						 String endTimeStr = (String)objs[2];
						 Date startTime = DateTime.parse(startTimeStr, dateTimeFormatter).toDate();
						 Date endTime = DateTime.parse(endTimeStr, dateTimeFormatter).toDate();
						 result.add(new TimeoffDateDTO(instructorId, startTime, endTime));
					 }
				 }
				 return result;
			 }                                                                                                  
		};
	 }

    public static Criterion<Timeoff, TimeLineDTO> findByInstructorAndDateTime(final Long pInstructorId, final Date pDateTime) {
        return new TimeoffCriterion<TimeLineDTO>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<TimeLineDTO> search(Session pSession, int pFetchMode) {
                DateTime dateTime = new DateTime(pDateTime);
                String sql = "select t3.instructor_id," +
                        "       to_char(min(t3.min_1), 'YYYY-MM-DD HH24:MI:SS') start_time," +
                        "       to_char(t3.max_1, 'YYYY-MM-DD HH24:MI:SS') end_time" +
                        "  from (select t2.instructor_id, t2.min_1, max(t2.max_1) max_1" +
                        "          from (select t1.*," +
                        "                       (select min(sub_t.start_time)" +
                        "                          from timeoff sub_t" +
                        "                         where sub_t.instructor_id = :instructorId" +
                        "                           and (to_char(sub_t.start_time, 'YYYY-MM-DD') <=" +
                        "                               :queryDate and" +
                        "                               to_char(sub_t.end_time, 'YYYY-MM-DD') >=" +
                        "                               :queryDate)" +
                        "                           and ((sub_t.start_time <= t1.min_ and" +
                        "                               sub_t.end_time >= t1.min_) or" +
                        "                               (sub_t.start_time <= t1.max_ and" +
                        "                               sub_t.end_time >= t1.max_) or" +
                        "                               (sub_t.start_time >= t1.min_ and" +
                        "                               sub_t.end_time <= t1.max_))) min_1," +
                        "                       (select max(sub_t.end_time)" +
                        "                          from timeoff sub_t" +
                        "                         where sub_t.instructor_id = :instructorId" +
                        "                           and (to_char(sub_t.start_time, 'YYYY-MM-DD') <=" +
                        "                               :queryDate and" +
                        "                               to_char(sub_t.end_time, 'YYYY-MM-DD') >=" +
                        "                               :queryDate)" +
                        "                           and ((sub_t.start_time <= t1.min_ and" +
                        "                               sub_t.end_time >= t1.min_) or" +
                        "                               (sub_t.start_time <= t1.max_ and" +
                        "                               sub_t.end_time >= t1.max_) or" +
                        "                               (sub_t.start_time >= t1.min_ and" +
                        "                               sub_t.end_time <= t1.max_))) max_1" +
                        "                  from (select t.*," +
                        "                               (select min(sub_t.start_time)" +
                        "                                  from timeoff sub_t" +
                        "                                 where sub_t.instructor_id = :instructorId" +
                        "                                   and (to_char(sub_t.start_time, 'YYYY-MM-DD') <=" +
                        "                                       :queryDate and" +
                        "                                       to_char(sub_t.end_time, 'YYYY-MM-DD') >=" +
                        "                                       :queryDate)" +
                        "                                   and ((sub_t.start_time <= t.start_time and" +
                        "                                       sub_t.end_time >= t.start_time) or" +
                        "                                       (sub_t.start_time <= t.end_time and" +
                        "                                       sub_t.end_time >= t.end_time) or" +
                        "                                       (sub_t.start_time >= t.start_time and" +
                        "                                       sub_t.end_time <= t.end_time))) min_," +
                        "                               (select max(sub_t.end_time)" +
                        "                                  from timeoff sub_t" +
                        "                                 where sub_t.instructor_id = :instructorId" +
                        "                                   and (to_char(sub_t.start_time, 'YYYY-MM-DD') <=" +
                        "                                       :queryDate and" +
                        "                                       to_char(sub_t.end_time, 'YYYY-MM-DD') >=" +
                        "                                       :queryDate)" +
                        "                                   and ((sub_t.start_time <= t.start_time and" +
                        "                                       sub_t.end_time >= t.start_time) or" +
                        "                                       (sub_t.start_time <= t.end_time and" +
                        "                                       sub_t.end_time >= t.end_time) or" +
                        "                                       (sub_t.start_time >= t.start_time and" +
                        "                                       sub_t.end_time <= t.end_time))) max_" +
                        "                          from timeoff t" +
                        "                         where t.instructor_id = :instructorId" +
                        "                           and (to_char(t.start_time, 'YYYY-MM-DD') <=" +
                        "                               :queryDate and" +
                        "                               to_char(t.end_time, 'YYYY-MM-DD') >=" +
                        "                               :queryDate)) t1) t2" +
                        "         group by t2.instructor_id, t2.min_1) t3" +
                        " group by t3.instructor_id, t3.max_1";
                SQLQuery query = pSession.createSQLQuery(sql);
                query.setString("queryDate", dateTime.toString("yyyy-MM-dd"));
                query.setLong("instructorId", pInstructorId);
                query.addScalar("instructor_id", StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.STRING);
                List<Object[]> list = query.list();
                List<TimeLineDTO> result = new ArrayList<TimeLineDTO>();
                if(null != list && !list.isEmpty()) {
                    DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN);
                    for(Object[] objs : list) {
                        String startTimeStr = (String)objs[1];
                        String endTimeStr = (String)objs[2];
                        DateTime startTime = DateTime.parse(startTimeStr, dateTimeFormatter);
                        DateTime endTime = DateTime.parse(endTimeStr, dateTimeFormatter);
                        result.add(new TimeLineDTO(startTime, endTime));
                    }
                }
                return result;
            }
        };
    }
	 
}
