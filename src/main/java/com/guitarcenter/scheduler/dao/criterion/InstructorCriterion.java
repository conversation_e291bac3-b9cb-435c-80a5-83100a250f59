package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_AVAILABILITY;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.ScrollableResults;
import org.hibernate.Session;
import org.hibernate.type.StandardBasicTypes;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorAppointmentStatusDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorSortedDTO;
import com.guitarcenter.scheduler.dao.criterion.util.InstructorSortedComparator;
import com.guitarcenter.scheduler.dto.DailySubscriptionReportDTO;
import com.guitarcenter.scheduler.dto.InstructorActivitiesAndAvailabilityDTO;
import com.guitarcenter.scheduler.dto.InstructorActivitiesDTO;
import com.guitarcenter.scheduler.dto.TimeIntervalDTO;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ServiceMode;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.webservice.dto.InstructorAvailableHoursDTO;


public abstract class InstructorCriterion<E> extends AbstractCriterion<Instructor, E> implements
        Criterion<Instructor, E> {

	//added for GSSP-240, for debugging purpose
	private static final Logger		Log	= LoggerFactory.getLogger(InstructorCriterion.class);

    private static final Criterion<Instructor, Instructor> DEFAULT_INSTANCE = new InstructorCriterion<Instructor>() {
    };


    private InstructorCriterion() {
        super(Instructor.class);
    }


    @Override
    protected String getFetchScript(int pFetchMode) {
        StringBuilder sb = new StringBuilder();
        sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
        sb.append(addFetchHQL(FETCH_AVAILABILITY, pFetchMode, "t.availability", true));
        sb.append(addFetchHQL(FETCH_LOCATION, pFetchMode, "t.location", true));
        sb.append(addFetchHQL(FETCH_PERSON, pFetchMode, "t.person", true));
        sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
        return sb.toString();
    }


    @Override
    public List<E> search(Session pSession, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }


    @Override
    public E get(Session pSession, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }


    public static Criterion<Instructor, Instructor> getInstance() {
        return DEFAULT_INSTANCE;
    }


    public static Criterion<Instructor, Instructor> findByLocation(final long pLocationId) {

        InstructorCriterion<Instructor> instance = new InstructorCriterion<Instructor>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Instructor t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append("where t.location.locationId = :locationId order by t.instructorId");
                Query query = pSession.createQuery(sb.toString());
                query.setLong("locationId", pLocationId);
                return query.list();
            }

        };
        return instance;
    }


    public static Criterion<Instructor, Instructor> findByLocation(final long pLocationId, final Enabled pEnabled) {

        InstructorCriterion<Instructor> instance = new InstructorCriterion<Instructor>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Instructor t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append("where t.location.locationId = :locationId");
                sb.append(" and t.enabled = :enabled");
                sb.append("  order by t.instructorId");
                Query query = pSession.createQuery(sb.toString());
                query.setLong("locationId", pLocationId);
                query.setString("enabled", pEnabled.toString());
                return query.list();
            }

        };
        return instance;
    }


    public static Criterion<Instructor, Instructor> findByActivityId(final long pActivityId) {

        InstructorCriterion<Instructor> instance = new InstructorCriterion<Instructor>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Instructor t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" left join fetch t.activities a ");
                sb.append("where a.activityId = :activityId order by t.instructorId");
                Query query = pSession.createQuery(sb.toString());
                query.setLong("activityId", pActivityId);
                return query.list();
            }

        };
        return instance;
    }


    public static Criterion<Instructor, Instructor> findByLocationIdAndActivityId(final long pLocationId,
                                                                                  final long pActivityId, final Enabled pEnabled) {

        InstructorCriterion<Instructor> instance = new InstructorCriterion<Instructor>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Instructor t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" left join fetch t.activities a ");
                sb.append("where a.activityId = :activityId");
                sb.append(" and t.location.locationId = :locationId");
                sb.append(" and t.enabled = :enabled");
                sb.append("  order by t.instructorId");
                Query query = pSession.createQuery(sb.toString());
                query.setLong("activityId", pActivityId);
                query.setLong("locationId", pLocationId);
                query.setString("enabled", pEnabled.toString());
                return query.list();
            }

        };
        return instance;
    }


    public static Criterion<Instructor, Instructor> findByLocationIdAndActivityIds(final long pLocationId,
                                                                                   final Long... pActivityIds) {

        InstructorCriterion<Instructor> instance = new InstructorCriterion<Instructor>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(Session pSession, int pFetchMode) {
                if (pActivityIds.length == 0) {
                    return Collections.emptyList();
                }
                StringBuilder sb = new StringBuilder("select distinct t from Instructor t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" left join fetch t.activities a ");
                sb.append("where a.activityId in (:activityIds)");
                sb.append(" and t.location.locationId = :locationId");
                sb.append(" and t.enabled = :enabled");
                sb.append("  order by t.instructorId");
                Query query = pSession.createQuery(sb.toString());
                query.setParameterList("activityIds", pActivityIds);
                query.setLong("locationId", pLocationId);
                query.setString("enabled", Enabled.Y.toString());
                return query.list();
            }

        };
        return instance;
    }
    

    public static Criterion<Instructor, Instructor> findByLocationIdAndActivityIdsInsAVL(final long instructorId) {

        InstructorCriterion<Instructor> instance = new InstructorCriterion<Instructor>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(Session pSession, int pFetchMode) {
           
                StringBuilder sb = new StringBuilder("select distinct t from Instructor t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" left join fetch t.activities a ");
                sb.append("where  ");
                sb.append("   t.instructorId = :instructorId");
                sb.append(" and t.enabled = :enabled");
                sb.append("  order by t.instructorId");
                Query query = pSession.createQuery(sb.toString());
                
                query.setLong("instructorId", instructorId);
                query.setString("enabled", Enabled.Y.toString());
                return query.list();
            }

        };
        return instance;
    }
    
    
    
    public static Criterion<Instructor, Instructor> findByInstructorIds(final Long... pInstructorIds) {

        InstructorCriterion<Instructor> instance = new InstructorCriterion<Instructor>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(Session pSession, int pFetchMode) {
                List<Long> instructorIds;
                if (null == pInstructorIds || 0 == pInstructorIds.length) {
                    return Collections.EMPTY_LIST;
                } else {
                    instructorIds = Arrays.asList(pInstructorIds);
                }
                StringBuilder sb = new StringBuilder("from Instructor t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append("where t.instructorId in (:instructorIds) order by t.instructorId");
                Query query = pSession.createQuery(sb.toString());
                query.setParameterList("instructorIds", instructorIds);
                return query.list();
            }

        };
        return instance;
    }

    public static Criterion<Instructor, Instructor> findByInstructorId(final long pInstructorId) {

        InstructorCriterion<Instructor> instance = new InstructorCriterion<Instructor>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Instructor t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append("where t.instructorId = :instructorId order by t.instructorId");
                Query query = pSession.createQuery(sb.toString());
                query.setLong("instructorId", pInstructorId);
                return query.list();
            }

        };
        return instance;
    }
    /**
     * Criterion to return all Instructors that are associated with the site
     * provided.
     *
     * @param siteId the Site identifier to use for search
     * @return Criterion
     */
    public static Criterion<Instructor, Instructor> findBySiteId(final long siteId) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(Session pSession, int pFetchMode) {
                Query query = pSession.createQuery(new StringBuilder(" from Instructor t ")
                        .append(getFetchScript(pFetchMode))
                        .append(" where t.site.siteId = :siteId ")
                        .toString());
                query.setLong("siteId", siteId);
                return query.list();
            }
        };
    }

    /**
     * Returns a Criterion that can be used to find any existing instructors in
     * the supplied site with a matching external id.
     *
     * @param pSiteId     site identifier
     * @param pExternalId String containing the external id to match
     * @return Criterion instance
     */
    public static Criterion<Instructor, Instructor> findByExternalId(final long pSiteId, final String pExternalId) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(Session pSession, int pFetchMode) {
                Query query = pSession.createQuery(new StringBuilder(" from Instructor t ")
                        .append(getFetchScript(pFetchMode))
                        .append(" where t.site.siteId = :siteId ")
                        .append("   and t.externalId = :externalId ")
                        .toString());
                query.setLong("siteId", pSiteId);
                query.setString("externalId", pExternalId);
                return query.list();
            }
        };
    }

    
 //GSSP-275 Changed made for verifying the instructor external id in the instructor table
    
    public static Criterion<Instructor, Instructor> findinstructorByExternal(final String pExternalId) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(Session pSession, int pFetchMode) {
            	StringBuilder sb = new StringBuilder("select t.person_id ,t.site_id");
                sb.append("  from instructor t");
                sb.append("  left join person a");
                sb.append("    on t.person_id = a.person_id");               
                sb.append(" where t.external_id = :externalId");                
                SQLQuery query = pSession.createSQLQuery(sb.toString());
                query.addScalar("person_id",StandardBasicTypes.LONG );
                query.addScalar("site_id", StandardBasicTypes.STRING);                
                query.setString("externalId", pExternalId);
                ScrollableResults scroll = query.scroll();
            	List<Instructor> result = new ArrayList<Instructor>();
            	Instructor dto = null;               
               
                while (scroll.next()) {
                	Object[] objects = scroll.get();
                	Long person_id = (Long) objects[0];
                	String site_id = (String) objects[1];
                	Person pObj=new Person();
                	pObj.setPersonId(person_id);
                	Site sObj=new Site();
                	sObj.setSiteId(Long.parseLong(site_id));
                	dto = new Instructor(pObj,sObj );
                	result.add(dto);
                }        
                
                scroll.close();
                return result;
        }
            
        };
    }



    public static Criterion<Instructor, Boolean> checkAppointmentDateTimeByInstructorAndDateTime(final Long pInstructorId, final Date pStartDate, final Date pEndDate, final Integer pDayOfWeek) {
        return new InstructorCriterion<Boolean>() {
            @Override
            @SuppressWarnings("unchecked")
            public Boolean get(Session pSession, int pFetchMode) {
                Boolean result = true;
                DateTime startTime = new DateTime(pStartDate);
                DateTime endTime = new DateTime(pEndDate);
                String dayOfWeek = String.valueOf(pDayOfWeek);
                String sql = "select t.instructor_id," +
                        "        to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') as min_time," +
                        "        to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') as max_time" +
                        " from appointment t" +
                        "    where to_char(t.start_time - 1, 'd') = :dayOfWeek" +
                        "      and t.instructor_id = :instructorId" +
                        "      and t.start_time >= sysdate" +
                        "      and (t.canceled is null or t.canceled = 'N' or t.canceled = 'H')";
                SQLQuery query = pSession.createSQLQuery(sql);
                query.setLong("instructorId", pInstructorId);
                query.setString("dayOfWeek", dayOfWeek);
                query.addScalar("instructor_id", StandardBasicTypes.LONG);
                query.addScalar("min_time", StandardBasicTypes.TIMESTAMP);
                query.addScalar("max_time", StandardBasicTypes.TIMESTAMP);
                List<Object[]> list = query.list();
                if (list == null || list.isEmpty()) {
                    return result;
                }
                String checkOntime=" select to_char(ot.start_time, 'YYYY-MM-DD HH24:MI:SS') as sTime, to_char(ot.end_time, 'YYYY-MM-DD HH24:MI:SS')as eTime"+
						 " from onetime ot"+
						 " where ot.INSTRUCTOR_ID=:instructorId"+
						 " and ot.start_time >=sysdate"+
						 " and to_char(ot.start_time - 1, 'd') = :dayOfWeek"+
						 " order by sTime";
				SQLQuery checkTimeQuery = pSession.createSQLQuery(checkOntime);
				checkTimeQuery.setLong("instructorId", pInstructorId);
				checkTimeQuery.setString("dayOfWeek", dayOfWeek);
				checkTimeQuery.addScalar("sTime", StandardBasicTypes.TIMESTAMP);
				checkTimeQuery.addScalar("eTime", StandardBasicTypes.TIMESTAMP);
				List<Object[]> timeList = checkTimeQuery.list();
				List<TimeIntervalDTO> sortList=new ArrayList<TimeIntervalDTO>();
				if (timeList != null && !timeList.isEmpty()) {
					for (Object[] objectTime : timeList) {
						TimeIntervalDTO timeInterval=new TimeIntervalDTO();
						timeInterval.setStartTime(new DateTime(objectTime[0]));
						timeInterval.setEndTime(new DateTime(objectTime[1]));
						sortList.add(timeInterval);
						TimeIntervalDTO availabilityTime=new TimeIntervalDTO();
						availabilityTime.setStartTime(timeInterval
								.getStartTime()
								.withMillisOfSecond(0)
								.withTime(startTime.getHourOfDay(),
										startTime.getMinuteOfHour(),
										startTime.getSecondOfMinute(),
										startTime.getMillisOfSecond()));
						availabilityTime.setEndTime(timeInterval
								.getEndTime()
								.withMillisOfSecond(0)
								.withTime(endTime.getHourOfDay(),
										endTime.getMinuteOfHour(),
										endTime.getSecondOfMinute(),
										endTime.getMillisOfSecond()));
						sortList.add(availabilityTime);
					}
					sortList=DateTimeUtil.concatTime(sortList);
				}
                for (Object[] colunms : list) {
                	result = false;
                    DateTime start_time = new DateTime(colunms[1]);
                    DateTime end_time = new DateTime(colunms[2]);
					if (sortList != null && !sortList.isEmpty()) {
                    	for (TimeIntervalDTO sort : sortList) {
    						DateTime sTime=sort.getStartTime();
    						DateTime eTime=sort.getEndTime();
    						if ((sTime.isBefore(start_time) || sTime.isEqual(start_time)) && (eTime.isAfter(end_time) || eTime.isEqual(end_time))) {
    							result = true;
    	                        break;
    	                    }
    					}
                    }
					if(!result){
						DateTime availabilityStartTime=start_time.withMillisOfSecond(0).withTime(startTime.getHourOfDay(),startTime.getMinuteOfHour(),startTime.getSecondOfMinute(),startTime.getMillisOfSecond());
						DateTime availabilityEndTime=end_time.withMillisOfSecond(0).withTime(endTime.getHourOfDay(),endTime.getMinuteOfHour(),endTime.getSecondOfMinute(),endTime.getMillisOfSecond());
						if((availabilityStartTime.isBefore(start_time) || availabilityStartTime.isEqual(start_time)) && (availabilityEndTime.isAfter(end_time) || availabilityEndTime.isEqual(end_time))){
							result=true;
						}		
					}
					if (!result) break;
                }
                return result;
            }
        };
    }
    
    //GCSS-715
    public static Criterion<Instructor, Boolean> checkAppointmentDateTimeByAvailabilityAndOntime(final Long pInstructorId, final Date pStartDate, final Date pEndDate) {
        return new InstructorCriterion<Boolean>() {
            @SuppressWarnings("unchecked")
			@Override
            public Boolean get(Session pSession, int pFetchMode) {
                Boolean result = false;
                DateTime startTime = new DateTime(pStartDate);
                DateTime endTime = new DateTime(pEndDate);
                int dayOfWeek = startTime.getDayOfWeek();
                DayOfWeek dayOfWeek_enum=DayOfWeek.values(dayOfWeek);
                String weekDay=dayOfWeek_enum.getDayofWeek();
                String checkTime="select to_char(ay."+weekDay+"_start_time, 'HH24:MI:SS') as sTime, to_char(ay."+weekDay+"_end_time, 'HH24:MI:SS')as eTime"+
						 " from  availability ay,INSTRUCTOR t"+
						 " where ay.availability_id=t.availability_id"+
						 " and t.instructor_id=:instructorId"+
						 " and ay."+weekDay+"_start_time is not null"+
						 " union"+
						 " select to_char(ot.start_time, 'HH24:MI:SS') as sTime, to_char(ot.end_time, 'HH24:MI:SS')as eTime"+
						 " from onetime ot"+
						 " where ot.INSTRUCTOR_ID=:instructorId"+
						 " and ot.start_time >=sysdate"+
						 " and to_char(ot.start_time, 'YYYY-MM-DD')=to_char(:startTime, 'YYYY-MM-DD')"+
						 " order by sTime";
                SQLQuery checkTimeQuery = pSession.createSQLQuery(checkTime);
				checkTimeQuery.setLong("instructorId", pInstructorId);
				checkTimeQuery.setTimestamp("startTime", pStartDate);
				checkTimeQuery.addScalar("sTime", StandardBasicTypes.TIME);
				checkTimeQuery.addScalar("eTime", StandardBasicTypes.TIME);
				List<Object[]> timeList = checkTimeQuery.list();
				if (timeList == null || timeList.isEmpty()) {
					return result;
				}
				List<TimeIntervalDTO> sortList=new ArrayList<TimeIntervalDTO>();
				for (Object[] objectTime : timeList) {
					TimeIntervalDTO timeInterval=new TimeIntervalDTO();
					timeInterval.setStartTime(new DateTime(objectTime[0]));
					timeInterval.setEndTime(new DateTime(objectTime[1]));
					sortList.add(timeInterval);
				}
		    	sortList=DateTimeUtil.concatTime(sortList);
				for (TimeIntervalDTO sort : sortList) {
					DateTime start_time = sort.getStartTime();
					DateTime end_time = sort.getEndTime();
					start_time = startTime.withMillisOfSecond(0).withTime(start_time.getHourOfDay(),start_time.getMinuteOfHour(),start_time.getSecondOfMinute(),start_time.getMillisOfSecond());
					end_time = endTime.withMillisOfSecond(0).withTime(end_time.getHourOfDay(),end_time.getMinuteOfHour(), end_time.getSecondOfMinute(),end_time.getMillisOfSecond());
					if ((startTime.withMillisOfSecond(0).isAfter(start_time) || startTime.withMillisOfSecond(0).isEqual(start_time))&& (endTime.withMillisOfSecond(0).isBefore(end_time) || endTime.withMillisOfSecond(0).isEqual(end_time))) {
						result = true;
						break;
					}
				}
                return result;
            }
        };
    }
    public static Criterion<Instructor, Boolean> checkOneTimeHasAppointment(final Long pInstructorId, final Date pStartDate, final Date pEndDate) {
        return new InstructorCriterion<Boolean>() {
            @Override
            public Boolean get(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select count(t.appointment_id) as counts");
                sb.append(" from appointment t");
                sb.append("      where t.instructor_id = :instructorId");
                sb.append("      and ((t.start_time <= :startTime and t.end_time >= :endTime) or (t.start_time >= :startTime and t.end_time < :endTime and t.start_time < :endTime) or (t.start_time < :startTime and t.end_time <= :endTime and t.end_time > :startTime)");
                sb.append(" or (t.start_time >= :startTime and t.start_time < :endTime and t.end_time > :endTime))");
                sb.append("      and (t.canceled is null or t.canceled = 'N' or t.canceled = 'H')");
                SQLQuery query = pSession.createSQLQuery(sb.toString());
                query.setLong("instructorId", pInstructorId);
                query.setTimestamp("startTime", pStartDate);
                query.setTimestamp("endTime", pEndDate);
                query.addScalar("counts", StandardBasicTypes.LONG);
                Long count = (Long) query.uniqueResult();
                return (count > 0);
            }
        };
    }
    
    public static Criterion<Instructor, Boolean> hasAppointmentByInstructorAndDayOfWeek(final Long pInstructorId, final Integer pDayOfWeek) {
        return new InstructorCriterion<Boolean>() {
            @Override
            @SuppressWarnings("unchecked")
            public Boolean get(Session pSession, int pFetchMode) {
            	String dayOfWeek = String.valueOf(pDayOfWeek);
            	Boolean result = true;
            	StringBuilder sb = new StringBuilder("select t.start_time as sTime,t.end_time as eTime");
				sb.append(" from appointment t");
				sb.append("    where to_char(t.start_time - 1, 'd') = :dayOfWeek");
				sb.append("      and t.instructor_id = :instructorId");
				sb.append("      and t.start_time >= sysdate");
				sb.append("      and (t.canceled is null or t.canceled = 'N' or t.canceled = 'H')");
                SQLQuery query = pSession.createSQLQuery(sb.toString());
                query.setLong("instructorId", pInstructorId);
                query.setString("dayOfWeek", dayOfWeek);
                query.addScalar("sTime", StandardBasicTypes.TIMESTAMP);
                query.addScalar("eTime", StandardBasicTypes.TIMESTAMP);
				List<Object[]> list =query.list();
				if (list == null || list.isEmpty()) {
                	return !result;
                }
                String checkOntime=" select to_char(ot.start_time, 'YYYY-MM-DD HH24:MI:SS') as sTime, to_char(ot.end_time, 'YYYY-MM-DD HH24:MI:SS')as eTime"+
									 " from onetime ot"+
									 " where ot.INSTRUCTOR_ID=:instructorId"+
									 " and ot.start_time >=sysdate"+
									 " order by sTime";
                SQLQuery checkTimeQuery = pSession.createSQLQuery(checkOntime);
				checkTimeQuery.setLong("instructorId", pInstructorId);
				checkTimeQuery.addScalar("sTime", StandardBasicTypes.TIMESTAMP);
				checkTimeQuery.addScalar("eTime", StandardBasicTypes.TIMESTAMP);
				List<Object[]> timeList = checkTimeQuery.list();
				if (timeList == null || timeList.isEmpty()) {
					return result;
				}
				List<TimeIntervalDTO> sortList=new ArrayList<TimeIntervalDTO>();
				for (Object[] objectTime : timeList) {
					TimeIntervalDTO timeInterval=new TimeIntervalDTO();
					timeInterval.setStartTime(new DateTime(objectTime[0]));
					timeInterval.setEndTime(new DateTime(objectTime[1]));
					sortList.add(timeInterval);
				}
				sortList=DateTimeUtil.concatTime(sortList);
				for (Object[] colunms : list) {
					result = true;
					DateTime start_time = new DateTime(colunms[0]);
                    DateTime end_time = new DateTime(colunms[1]);
                    for (TimeIntervalDTO sort : sortList) {
                    	DateTime startTime=sort.getStartTime();
                    	DateTime endTime=sort.getEndTime();
						if ((startTime.isBefore(start_time)||startTime.isEqual(start_time))&&(endTime.isAfter(end_time)||endTime.isEqual(end_time))) {
							result = false;
							break;
						}
					}
                    if(result) break;
				}
            	return result;
            }
        };
    }

    /**
     * To checked availability of instructor.
     * @return if the result is false, since the instructor is unavailability, and vice versa.
     */
    public static Criterion<Instructor, Boolean> checkTimeAvailability(final Long pInstructorId, final Date pStartDateTime, final Date pEndDateTime) {
        return new InstructorCriterion<Boolean>() {
            @Override
            public Boolean get(Session pSession, int pFetchMode) {
                DateTime startTime = new DateTime(pStartDateTime);
                int dayOfWeek = startTime.getDayOfWeek();
                StringBuilder sb = new StringBuilder("select count(*) as counts");
                sb.append("  from INSTRUCTOR t");
                sb.append("  left join availability ay");
                sb.append("    on t.availability_id = ay.availability_id");
                sb.append("  left join onetime ot ");
                sb.append("    on t.instructor_id = ot.instructor_id");
                sb.append("  where t.instructor_id = :instructorId");
                DayOfWeek dayOfWeek_enum = DayOfWeek.values(dayOfWeek);
                if (dayOfWeek_enum == null) return false;
                sb.append("    and ( ");
                dayOfWeek_enum.fillCondition(sb);
                sb.append("         or ");
                sb.append("            ((to_char(ot.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS'))");
                sb.append("             and (to_char(ot.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS')))");
                sb.append("         )");
                SQLQuery query = pSession.createSQLQuery(sb.toString());
                query.addScalar("counts", StandardBasicTypes.LONG);
                query.setLong("instructorId", pInstructorId);
                query.setTimestamp("startTime", pStartDateTime);
                query.setTimestamp("endTime", pEndDateTime);
                Long count = (Long) query.uniqueResult();
                return (count > 0);
            }
        };
    }

    public static Criterion<Instructor, Instructor> findByProfileIdAndActivityIdAndDateTime(final long pProfileId, final long pActivityId, final Date pStartDate, final Date pEndDate, final Long pExcludeAppointmentId) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(Session pSession, int pFetchMode) {
                DateTime startTime = new DateTime(pStartDate);
                DateTime endTime = new DateTime(pEndDate);
                int dayOfWeek = startTime.getDayOfWeek();
                StringBuilder sb = new StringBuilder("select {t.*}, {p.*}");
                sb.append("  from INSTRUCTOR t");
                sb.append("  left join person p");
                sb.append("    on t.person_id = p.person_id");
                sb.append("  left join location l");
                sb.append("    on t.location_id = l.location_id");
                sb.append("  left join availability ay");
                sb.append("    on t.availability_id = ay.availability_id");
                sb.append("  left join instructor_activities a_i ");
                sb.append("    on t.instructor_id = a_i.instructor_id");
                sb.append("  left join onetime ot ");
                sb.append("    on t.instructor_id = ot.instructor_id");
                sb.append("  where l.profile_id = :profileId");
                sb.append("    and a_i.activity_id = :activityId");
                sb.append("    and t.enabled = 'Y'");
                DayOfWeek dayOfWeek_enum = DayOfWeek.values(dayOfWeek);
                if (dayOfWeek_enum == null) return Collections.EMPTY_LIST;
                /*sb.append("    and ( ");
                dayOfWeek_enum.fillCondition(sb);
                sb.append("         or ");
                sb.append("            ((to_char(ot.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS'))");
                sb.append("             and (to_char(ot.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS')))");
                sb.append("         )");*/
                SQLQuery query = pSession.createSQLQuery(sb.toString());
                query.addEntity("t", Instructor.class);
                query.addJoin("p", "t.person");
                query.setLong("profileId", pProfileId);
                query.setLong("activityId", pActivityId);
                //query.setTimestamp("startTime", pStartDate);
                //query.setTimestamp("endTime", pEndDate);
                List<Object[]> list = query.list();
                Map<Long, Instructor> maps = new HashMap<Long, Instructor>();
                sb = new StringBuilder();
                sb.append("select count(*) as counts");
                sb.append("  from appointment t");
                sb.append(" where to_char(t.start_time, 'YYYY-MM-DD') = to_char(:targetDate, 'YYYY-MM-DD')");
                sb.append("   and (t.canceled = 'H' or t.canceled = 'N' or t.canceled is null)");
                sb.append("   and t.profile_id = :profileId");
                sb.append("   and ((to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                sb.append("        or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                sb.append("        or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS'))");
                sb.append("        or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                sb.append("        )");
                sb.append("   and t.instructor_id = :instructorId");
                if (pExcludeAppointmentId != null) {
					sb.append(" and t.appointment_id != :excludeAppId ");
				}
                String weekDay=dayOfWeek_enum.getDayofWeek();
                String checkTime="select to_char(ay."+weekDay+"_start_time, 'HH24:MI:SS') as sTime, to_char(ay."+weekDay+"_end_time, 'HH24:MI:SS')as eTime"+
						 " from  availability ay,INSTRUCTOR t"+
						 " where ay.availability_id=t.availability_id"+
						 " and t.instructor_id=:instructorId"+
						 " and ay."+weekDay+"_start_time is not null"+
						 " union"+
						 " select to_char(ot.start_time, 'HH24:MI:SS') as sTime, to_char(ot.end_time, 'HH24:MI:SS')as eTime"+
						 " from onetime ot"+
						 " where ot.INSTRUCTOR_ID=:instructorId"+
						 " and ot.start_time >=sysdate"+
						 " and to_char(ot.start_time, 'YYYY-MM-DD')=to_char(:startTime, 'YYYY-MM-DD')"+
						 " order by sTime";
				String checkTimeOff = "select count(*) as counts" +
                        "  from timeoff t" +
                        "  where t.instructor_id = :instructorId" +
                        "    and ((to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))" +
                        "         or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))" +
                        "         or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS'))" +
                        "         or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))" +
                        "         )";
                for (Object[] objects : list) {
                    Instructor instructor = (Instructor) objects[0];
                    Long instructorId = instructor.getInstructorId();
                    if (!maps.containsKey(instructorId)) {
						SQLQuery checkTimeQuery = pSession.createSQLQuery(checkTime);
						checkTimeQuery.setLong("instructorId", instructorId);
						checkTimeQuery.setTimestamp("startTime", pStartDate);
						checkTimeQuery.addScalar("sTime", StandardBasicTypes.TIME);
						checkTimeQuery.addScalar("eTime", StandardBasicTypes.TIME);
						List<Object[]> timeList = checkTimeQuery.list();
						if (timeList == null || timeList.isEmpty()) continue;
						Boolean result = false;
						List<TimeIntervalDTO> sortList=new ArrayList<TimeIntervalDTO>();
						for (Object[] objectTime : timeList) {
							TimeIntervalDTO timeInterval=new TimeIntervalDTO();
							timeInterval.setStartTime(new DateTime(objectTime[0]));
							timeInterval.setEndTime(new DateTime(objectTime[1]));
							sortList.add(timeInterval);
						}
				    	sortList=DateTimeUtil.concatTime(sortList);
						for (TimeIntervalDTO sort : sortList) {
							DateTime start_time = sort.getStartTime();
							DateTime end_time = sort.getEndTime();
							start_time = startTime.withMillisOfSecond(0).withTime(start_time.getHourOfDay(),start_time.getMinuteOfHour(),start_time.getSecondOfMinute(),start_time.getMillisOfSecond());
							end_time = endTime.withMillisOfSecond(0).withTime(end_time.getHourOfDay(),end_time.getMinuteOfHour(), end_time.getSecondOfMinute(),end_time.getMillisOfSecond());
							if ((startTime.withMillisOfSecond(0).isAfter(start_time) || startTime.withMillisOfSecond(0).isEqual(start_time))&& (endTime.withMillisOfSecond(0).isBefore(end_time) || endTime.withMillisOfSecond(0).isEqual(end_time))) {
								result = true;
								break;
							}
						}
						if(!result) continue;
                        SQLQuery checkTimeOffQuery = pSession.createSQLQuery(checkTimeOff);
                        checkTimeOffQuery.addScalar("counts", StandardBasicTypes.LONG);
                        checkTimeOffQuery.setLong("instructorId", instructorId);
                        checkTimeOffQuery.setTimestamp("startTime", pStartDate);
                        checkTimeOffQuery.setTimestamp("endTime", pEndDate);
                        Long count = (Long) checkTimeOffQuery.uniqueResult();
                        if (count != 0) continue; //current instructor has time off recode.
                        query = pSession.createSQLQuery(sb.toString());
                        query.addScalar("counts", StandardBasicTypes.LONG);
                        query.setDate("targetDate", pStartDate);
                        query.setLong("profileId", pProfileId);
                        query.setLong("instructorId", instructorId);
                        query.setTimestamp("startTime", pStartDate);
                        query.setTimestamp("endTime", pEndDate);
                        if (pExcludeAppointmentId != null) {
                        	query.setLong("excludeAppId", pExcludeAppointmentId);
        				}
                        count = (Long) query.uniqueResult();
                        if (count == 0){
                            maps.put(instructorId, instructor);
                        }
                    }
                }
                if (maps.size() == 0) {
                    return Collections.EMPTY_LIST;
                }
                String sql = "select instructor_id, duration" +
                        " from (select t.instructor_id," +
                        "        abs(((to_date(:endTimeStr, 'YYYY-MM-DD HH24:MI:SS') -" +
                        "                to_date(to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS')," +
                        "                        'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 * 60)) as duration" +
                        "        from appointment t" +
                        "        where t.profile_id = :profileId" +
                        "        and t.instructor_id in (:instructorIds)" +
                        "        and (t.canceled is null or t.canceled = 'N' or t.canceled = 'H')" +
                        " and to_char(t.start_time, 'YYYY-MM-DD') >= :startDateStr" +
                        " and to_char(t.end_time, 'YYYY-MM-DD') <= :endDateStr" +
                        " union" +
                        " select t.instructor_id," +
                        "        abs(((to_date(:startTimeStr, 'YYYY-MM-DD HH24:MI:SS') -" +
                        "                to_date(to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS')," +
                        "                        'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 * 60)) as duration" +
                        " from appointment t" +
                        " where t.profile_id = :profileId" +
                        " and t.instructor_id in (:instructorIds)" +
                        " and (t.canceled is null or t.canceled = 'N' or t.canceled = 'H')" +
                        " and to_char(t.start_time, 'YYYY-MM-DD') >= :startDateStr" +
                        " and to_char(t.end_time, 'YYYY-MM-DD') <= :endDateStr)" +
                        " order by duration";
                query = pSession.createSQLQuery(sql);
                query.addScalar("instructor_id", StandardBasicTypes.LONG);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.setLong("profileId", pProfileId);
                query.setParameterList("instructorIds", maps.keySet());
                DateTime startDate = new DateTime(pStartDate);
                DateTime endDate = new DateTime(pEndDate);
                query.setString("startTimeStr", startDate.toString("yyyy-MM-dd HH:mm:ss"));
                query.setString("startDateStr", startDate.minusDays(7).toString("yyyy-MM-dd"));
                query.setString("endTimeStr", endDate.toString("yyyy-MM-dd HH:mm:ss"));
                query.setString("endDateStr", endDate.plusDays(7).toString("yyyy-MM-dd"));
                list = query.list();
                List<InstructorSortedDTO> instructorSortedDTOList = new LinkedList<InstructorSortedDTO>();
                Map<Long, InstructorSortedDTO> instructorSortedDTOCache = new HashMap<Long, InstructorSortedDTO>();
                for (Object[] objects : list) {
                    Long instructor_id = (Long) objects[0];
                    Long duration = (Long) objects[1];
                    if (!instructorSortedDTOCache.containsKey(instructor_id)) {
                        InstructorSortedDTO instructorSortedDTO = new InstructorSortedDTO(instructor_id, duration);
                        instructorSortedDTOList.add(instructorSortedDTO);
                        instructorSortedDTOCache.put(instructor_id, instructorSortedDTO);
                    }
                }
                for (Map.Entry<Long, Instructor> entry : maps.entrySet()) {
                    Long instructor_id = entry.getKey();
                    if (!instructorSortedDTOCache.containsKey(instructor_id)) {
                        InstructorSortedDTO instructorSortedDTO = new InstructorSortedDTO(instructor_id, Long.MAX_VALUE);
                        instructorSortedDTOList.add(instructorSortedDTO);
                        instructorSortedDTOCache.put(instructor_id, instructorSortedDTO);
                    }
                }
                Collections.sort(instructorSortedDTOList, new InstructorSortedComparator());
                ArrayList<Instructor> instructors = new ArrayList<Instructor>();
                for (InstructorSortedDTO instructorSortedDTO : instructorSortedDTOList) {
                    instructors.add(maps.get(instructorSortedDTO.getInstructorId()));
                }
                return instructors;
            }

        };
    }
    private static enum DayOfWeek {
        Mon {
            @Override
            public void fillCondition(StringBuilder sb) {
                sb.append("        (ay.monday_start_time is not null");
                sb.append("    and (to_char(ay.monday_start_time, 'HH24:MI:SS') <= to_char(:startTime, 'HH24:MI:SS'))");
                sb.append("    and (to_char(ay.monday_end_time, 'HH24:MI:SS') >= to_char(:endTime, 'HH24:MI:SS')))");
            }

			@Override
			public String getDayofWeek() {
				return "monday";
			}
        }, Tue {
            @Override
            public void fillCondition(StringBuilder sb) {
                sb.append("        (ay.tuesday_start_time is not null");
                sb.append("    and (to_char(ay.tuesday_start_time, 'HH24:MI:SS') <= to_char(:startTime, 'HH24:MI:SS'))");
                sb.append("    and (to_char(ay.tuesday_end_time, 'HH24:MI:SS') >= to_char(:endTime, 'HH24:MI:SS')))");
            }

			@Override
			public String getDayofWeek() {
				return "tuesday";
			}
        }, Wed {
            @Override
            public void fillCondition(StringBuilder sb) {
                sb.append("        (ay.wednesday_start_time is not null");
                sb.append("    and (to_char(ay.wednesday_start_time, 'HH24:MI:SS') <= to_char(:startTime, 'HH24:MI:SS'))");
                sb.append("    and (to_char(ay.wednesday_end_time, 'HH24:MI:SS') >= to_char(:endTime, 'HH24:MI:SS')))");
            }

			@Override
			public String getDayofWeek() {
				return "wednesday";
			}
        }, Thu {
            @Override
            public void fillCondition(StringBuilder sb) {
                sb.append("        (ay.thursday_start_time is not null");
                sb.append("    and (to_char(ay.thursday_start_time, 'HH24:MI:SS') <= to_char(:startTime, 'HH24:MI:SS'))");
                sb.append("    and (to_char(ay.thursday_end_time, 'HH24:MI:SS') >= to_char(:endTime, 'HH24:MI:SS')))");
            }

			@Override
			public String getDayofWeek() {
				return "thursday";
			}
        }, Fri {
            @Override
            public void fillCondition(StringBuilder sb) {
                sb.append("        (ay.friday_start_time is not null");
                sb.append("    and (to_char(ay.friday_start_time, 'HH24:MI:SS') <= to_char(:startTime, 'HH24:MI:SS'))");
                sb.append("    and (to_char(ay.friday_end_time, 'HH24:MI:SS') >= to_char(:endTime, 'HH24:MI:SS')))");
            }

			@Override
			public String getDayofWeek() {
				return "friday";
			}
        }, Sat {
            @Override
            public void fillCondition(StringBuilder sb) {
                sb.append("        (ay.saturday_start_time is not null");
                sb.append("    and (to_char(ay.saturday_start_time, 'HH24:MI:SS') <= to_char(:startTime, 'HH24:MI:SS'))");
                sb.append("    and (to_char(ay.saturday_end_time, 'HH24:MI:SS') >= to_char(:endTime, 'HH24:MI:SS')))");
            }

			@Override
			public String getDayofWeek() {
				return "saturday";
			}
        }, Sun {
            @Override
            public void fillCondition(StringBuilder sb) {
                sb.append("        (ay.sunday_start_time is not null");
                sb.append("    and (to_char(ay.sunday_start_time, 'HH24:MI:SS') <= to_char(:startTime, 'HH24:MI:SS'))");
                sb.append("    and (to_char(ay.sunday_end_time, 'HH24:MI:SS') >= to_char(:endTime, 'HH24:MI:SS')))");
            }

			@Override
			public String getDayofWeek() {
				return "sunday";
			}
        };

        public abstract void fillCondition(StringBuilder sb);
        public abstract String getDayofWeek();
        public static DayOfWeek values(int dayOfWeek) {
            switch (dayOfWeek) {
                case 1:
                    return Mon;
                case 2:
                    return Tue;
                case 3:
                    return Wed;
                case 4:
                    return Thu;
                case 5:
                    return Fri;
                case 6:
                    return Sat;
                case 7:
                    return Sun;
                default:
                    return null;
            }
        }
    }
    
    
    //Changes made for GSSP-199
    /**
     * Returns a Criterion that can be used to find any existing instructor that
     * match the supplied person id.
     * 
     * @param pPersonId person identifier
     * @return Criterion instance
     */
    public static Criterion<Instructor, Instructor> findByPersonId(final long pPersonId) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(Session pSession, int pFetchMode) {
                Query query = pSession.createQuery(new StringBuilder(" from Instructor t ")
                                                       .append(getFetchScript(pFetchMode))
                                                       .append(" where t.person.personId = :personId ")
                                                       .toString());
                query.setLong("personId", pPersonId);
                return query.list();
            }
        };
    }
	
    //GSSP-240 
    public static Criterion<Instructor, InstructorActivitiesDTO> findInstructorActivities(
	            ) {
	        return new InstructorCriterion<InstructorActivitiesDTO>() {
	
	            @Override
	            public List<InstructorActivitiesDTO> search(Session pSession) {            	
	            	SQLQuery query = null;
	            	  Log.info("entered into InstructorCriterion.findInstructorActivities() {}");
	            	 

						StringBuilder sb = new StringBuilder("select l.EXTERNAL_ID,P.FIRST_NAME,P.LAST_NAME,l.LOCATION_NAME,A.ACTIVITY_NAME");  
						sb.append(" from location l");
						sb.append(" join LOCATION_PROFILE lp on l.PROFILE_ID = lp.PROFILE_ID");
						sb.append(" join INSTRUCTOR I on l.LOCATION_ID = I.LOCATION_ID"); 
						sb.append(" join PERSON P on P.PERSON_ID = I.PERSON_ID"); 
						sb.append(" join INSTRUCTOR_ACTIVITIES IA on I.INSTRUCTOR_ID = IA.INSTRUCTOR_ID"); 
						sb.append(" join ACTIVITY A on IA.ACTIVITY_ID = A.ACTIVITY_ID"); 
						sb.append(" where lp.ENABLED = 'Y' and I.ENABLED = 'Y' and I.STATUS = 'A'");
						sb.append(" order by l.EXTERNAL_ID,P.FIRST_NAME,P.LAST_NAME");
							
						query = pSession.createSQLQuery(sb.toString());
						
						query.addScalar("EXTERNAL_ID", StandardBasicTypes.STRING);	        		
						query.addScalar("FIRST_NAME", StandardBasicTypes.STRING);
						query.addScalar("LAST_NAME", StandardBasicTypes.STRING);
						query.addScalar("LOCATION_NAME", StandardBasicTypes.STRING);
						query.addScalar("ACTIVITY_NAME", StandardBasicTypes.STRING);
						
						ScrollableResults scroll = query.scroll();
						List<InstructorActivitiesDTO> resultList = new ArrayList<InstructorActivitiesDTO>();
						
						while (scroll.next()) {
							Object[] objects = scroll.get();
							String externalId = (String) objects[0];
							 String firstName = (String) objects[1];
							String lastName = (String) objects[2];
							String locationName=(String) objects[3];
							String activityName=(String) objects[4];
							
							InstructorActivitiesDTO dto = new InstructorActivitiesDTO(externalId, locationName, firstName,
									lastName, activityName);
							resultList.add(dto);
	
						}        
						scroll.close();
		            	  Log.info("InstructorCriterion.findInstructorActivities(), after retrieving DB values");
						
						return resultList;	   
	        }
	    };  
	}
	
	
	//GSSP-240 
    public static Criterion<Instructor, InstructorActivitiesDTO> getRecipientEmailIds() {
	        return new InstructorCriterion<InstructorActivitiesDTO>() {
	
	            @Override
	            public List<InstructorActivitiesDTO> search(Session pSession) {            	
	            	SQLQuery query = null;
					StringBuilder sb = new StringBuilder("select recipientId");  
					sb.append(" from EMAIL_LOOKUP");
					sb.append(" where description='Instructor Activities Report'");
						
					query = pSession.createSQLQuery(sb.toString());
					query.addScalar("RECIPIENTID", StandardBasicTypes.STRING);	        		
					
					ScrollableResults scroll = query.scroll();
					List<InstructorActivitiesDTO> resultList = new ArrayList<InstructorActivitiesDTO>();
					
					InstructorActivitiesDTO dto = null;
					
					while (scroll.next()) {
						Object[] objects = scroll.get();
						String recipientId = (String) objects[0];
						dto = new InstructorActivitiesDTO(recipientId);
						resultList.add(dto);

					}        
					scroll.close();
					return resultList;	   
	        }
	    };  
	}

    
  //GSSP-298 
    public static Criterion<Instructor, InstructorActivitiesAndAvailabilityDTO> findInstructorActivitiesAndAvailability(
	            ) {
	        return new InstructorCriterion<InstructorActivitiesAndAvailabilityDTO>() {
	
	            @Override
	            public List<InstructorActivitiesAndAvailabilityDTO> search(Session pSession) {            	
	            	SQLQuery query = null;
	            	  Log.info("entered into InstructorCriterion.findInstructorActivitiesAndAvailability() {}");
	            	  StringBuilder sb = new StringBuilder("select distinct P.FIRST_NAME || ' ' || P.LAST_NAME AS INSTRUCTOR_NAME, "
	            	  		+ "l.EXTERNAL_ID || ' ' || l.LOCATION_NAME AS STORE, AC.ACTIVITY_NAME   AS ACTIVITY, ");
	            	  sb.append(" TO_CHAR(av.monday_start_time, 'HH12:MI:SS AM') MONDAY_START_TIME, ");
	            	  sb.append(" TO_CHAR(av.monday_end_time, 'HH12:MI:SS AM') MONDAY_END_TIME, ");
	            	  sb.append(" TO_CHAR(av.tuesday_start_time, 'HH12:MI:SS AM') TUESDAY_START_TIME, ");
	            	  sb.append(" TO_CHAR(av.tuesday_end_time, 'HH12:MI:SS AM') TUESDAY_END_TIME, ");
	            	  sb.append(" TO_CHAR(av.wednesday_start_time, 'HH12:MI:SS AM') WED_START_TIME, ");
	            	  sb.append(" TO_CHAR(av.wednesday_end_time, 'HH12:MI:SS AM') WED_END_TIME, ");
	            	  sb.append(" TO_CHAR(av.thursday_start_time, 'HH12:MI:SS AM') THURS_START_TIME, ");
	            	  sb.append(" TO_CHAR(av.thursday_end_time, 'HH12:MI:SS AM') THURS_END_TIME, ");
	            	  sb.append(" TO_CHAR(av.friday_start_time, 'HH12:MI:SS AM') FRI_START_TIME, ");
	            	  sb.append(" TO_CHAR(av.friday_end_time, 'HH12:MI:SS AM') FRI_END_TIME, ");
	            	  sb.append(" TO_CHAR(av.saturday_start_time, 'HH12:MI:SS AM') SAT_START_TIME, ");
	            	  sb.append(" TO_CHAR(av.saturday_end_time, 'HH12:MI:SS AM') SAT_END_TIME, ");
	            	  sb.append(" TO_CHAR(av.sunday_start_time, 'HH12:MI:SS AM') SUN_START_TIME, ");
	            	  sb.append(" TO_CHAR(av.sunday_end_time, 'HH12:MI:SS AM') SUN_END_TIME ");
	            	  sb.append(" from location l join LOCATION_PROFILE lp on l.PROFILE_ID = lp.PROFILE_ID ");
	            	  sb.append(" join INSTRUCTOR I on l.LOCATION_ID = I.LOCATION_ID ");
	            	  sb.append(" join PERSON P on P.PERSON_ID = I.PERSON_ID ");
	            	  sb.append(" join INSTRUCTOR_ACTIVITIES IA on I.INSTRUCTOR_ID = IA.INSTRUCTOR_ID ");
	            	  sb.append(" join ACTIVITY A on IA.ACTIVITY_ID = A.ACTIVITY_ID ");
	            	  sb.append(" join AVAILABILITY av on i.availability_id = av.availability_id ");
	            	  sb.append(" left join ");
	            	  sb.append(" ( select distinct  P.FIRST_NAME || ' ' || P.LAST_NAME AS INSTRUCTOR_NAME, ");
	            	  sb.append(" LISTAGG(  A.ACTIVITY_NAME, ',') WITHIN GROUP (ORDER BY A.ACTIVITY_NAME) AS Activity_Name ");
	            	  sb.append(" from PERSON P ");
	            	  sb.append(" join INSTRUCTOR I on P.PERSON_ID = I.PERSON_ID ");
	            	  sb.append(" join INSTRUCTOR_ACTIVITIES IA on I.INSTRUCTOR_ID = IA.INSTRUCTOR_ID ");
	            	  sb.append(" join ACTIVITY A on IA.ACTIVITY_ID = A.ACTIVITY_ID ");
	            	//GSSP-322 adding Guitar center university to avoid cancel reason
	            	  sb.append(" where a.ACTIVITY_ID NOT IN (120,100,140,320,20,200,400) ");
	            	  sb.append(" group by P.FIRST_NAME || ' ' || P.LAST_NAME) AC ");
	            	  sb.append(" on AC.INSTRUCTOR_NAME = P.FIRST_NAME || ' ' || P.LAST_NAME ");
	            	  sb.append(" where lp.ENABLED = 'Y' and I.ENABLED = 'Y' and I.STATUS = 'A' ");
	            	  sb.append(" group by AC.ACTIVITY_NAME,I.LOCATION_ID,l.EXTERNAL_ID,l.LOCATION_NAME,I.INSTRUCTOR_ID,IA.ACTIVITY_ID,P.FIRST_NAME,P.LAST_NAME, ");
	            	  sb.append(" av.monday_start_time,av.monday_end_time, ");
	            	  sb.append(" av.tuesday_start_time,av.tuesday_end_time, ");
	            	  sb.append(" av.wednesday_start_time,av.wednesday_end_time, ");
	            	  sb.append(" av.thursday_start_time,av.thursday_end_time, ");
	            	  sb.append(" av.friday_start_time,av.friday_end_time, ");
	            	  sb.append(" av.saturday_start_time,av.saturday_end_time, ");
	            	  sb.append(" av.sunday_start_time,av.sunday_end_time ");
	            	  sb.append(" ORDER BY STORE,INSTRUCTOR_NAME ");
						query = pSession.createSQLQuery(sb.toString());
						query.addScalar("STORE", StandardBasicTypes.STRING);	        		
						query.addScalar("INSTRUCTOR_NAME", StandardBasicTypes.STRING);
						query.addScalar("ACTIVITY", StandardBasicTypes.STRING);
						query.addScalar("MONDAY_START_TIME", StandardBasicTypes.STRING);
						query.addScalar("MONDAY_END_TIME", StandardBasicTypes.STRING);
						query.addScalar("TUESDAY_START_TIME", StandardBasicTypes.STRING);
						query.addScalar("TUESDAY_END_TIME", StandardBasicTypes.STRING);
						query.addScalar("WED_START_TIME", StandardBasicTypes.STRING);
						query.addScalar("WED_END_TIME", StandardBasicTypes.STRING);
						query.addScalar("THURS_START_TIME", StandardBasicTypes.STRING);
						query.addScalar("THURS_END_TIME", StandardBasicTypes.STRING);
						query.addScalar("FRI_START_TIME", StandardBasicTypes.STRING);
						query.addScalar("FRI_END_TIME", StandardBasicTypes.STRING);
						query.addScalar("SAT_START_TIME", StandardBasicTypes.STRING);
						query.addScalar("SAT_END_TIME", StandardBasicTypes.STRING);
						query.addScalar("SUN_START_TIME", StandardBasicTypes.STRING);
						query.addScalar("SUN_END_TIME", StandardBasicTypes.STRING);
															
						@SuppressWarnings("unchecked")
						List<Object[]> list = query.list();
						List<InstructorActivitiesAndAvailabilityDTO> resultList = new ArrayList<InstructorActivitiesAndAvailabilityDTO>();
						InstructorActivitiesAndAvailabilityDTO dto = null;
						for (Object[] obj : list) {
							dto = new InstructorActivitiesAndAvailabilityDTO();
							dto.setStore((String) obj[0]);
							dto.setInstructorName((String) obj[1]);
							dto.setActivityName((String) obj[2]);
							dto.setMondayStartTime((String) obj[3]);
							dto.setMondayEndTime((String) obj[4]);
							dto.setTuesdayStartTime((String) obj[5]);
							dto.setTuesdayendTime((String) obj[6]);
							dto.setWednesdayStartTime((String) obj[7]);
							dto.setWednesdayEndTime((String) obj[8]);
							dto.setThursdayStartTime((String) obj[9]);
							dto.setThursdayEndTime((String) obj[10]);
							dto.setFridayStartTime((String) obj[11]);
							dto.setFridayEndTime((String) obj[12]);
							dto.setSaturdayStarTime((String) obj[13]);
							dto.setSaturdayEndTime((String) obj[14]);
							dto.setSundayStartTime((String) obj[15]);
							dto.setSundayEndTime((String) obj[16]);
							resultList.add(dto);
						}
						return  resultList;	
	        }
	    };  
	}
	
	
	//GSSP-298
    public static Criterion<Instructor, InstructorActivitiesAndAvailabilityDTO> getInstructorActivitiesAndAvailabilityRecipientEmailIds() {
	        return new InstructorCriterion<InstructorActivitiesAndAvailabilityDTO>() {
	
	            @Override
	            public List<InstructorActivitiesAndAvailabilityDTO> search(Session pSession) {            	
	            	SQLQuery query = null;
					StringBuilder sb = new StringBuilder("select recipientId");  
					sb.append(" from EMAIL_LOOKUP");
					sb.append(" where description='Instructor Activities And Availability Report'");
						
					query = pSession.createSQLQuery(sb.toString());
					query.addScalar("RECIPIENTID", StandardBasicTypes.STRING);	        		
					
					ScrollableResults scroll = query.scroll();
					List<InstructorActivitiesAndAvailabilityDTO> resultList = new ArrayList<InstructorActivitiesAndAvailabilityDTO>();
					
					InstructorActivitiesAndAvailabilityDTO dto = null;
					
					while (scroll.next()) {
						Object[] objects = scroll.get();
						String recipientId = (String) objects[0];
						dto = new InstructorActivitiesAndAvailabilityDTO(recipientId);
						resultList.add(dto);

					}        
					scroll.close();
					return resultList;	   
	        }
	    };  
	}
    
    public static Criterion<Instructor, InstructorActivitiesDTO> getRecipientDailySubcEmailIds() {
        return new InstructorCriterion<InstructorActivitiesDTO>() {

            @Override
            public List<InstructorActivitiesDTO> search(Session pSession) {            	
            	SQLQuery query = null;
				StringBuilder sb = new StringBuilder("select recipientId");  
				sb.append(" from EMAIL_LOOKUP");
				sb.append(" where description='Daily Subscription Report'");
					
				query = pSession.createSQLQuery(sb.toString());
				query.addScalar("RECIPIENTID", StandardBasicTypes.STRING);	        		
				
				ScrollableResults scroll = query.scroll();
				List<InstructorActivitiesDTO> resultList = new ArrayList<InstructorActivitiesDTO>();
				
				InstructorActivitiesDTO dto = null;
				
				while (scroll.next()) {
					Object[] objects = scroll.get();
					String recipientId = (String) objects[0];
					dto = new InstructorActivitiesDTO(recipientId);
					resultList.add(dto);

				}        
				scroll.close();
				return resultList;	   
        }
    };  
}

	//############################################################################
  //---GSSP Instructor Mode update changes
    //  Code changes for get Map object of instructor mode for an InstructorID.
    public static Criterion<Instructor, Map<Long, ServiceMode>> findInstructorServiveModeMap(
            final List<Long> instructorIdList) {

        return new InstructorCriterion<Map<Long, ServiceMode>>() {

            @Override
            public Map<Long, ServiceMode> get(Session pSession, int pFetchMode) {


                String sb = "select im.INSTRUCTOR_ID,sm.SERVICE_MODE_NAME,sm.SERVICE_MODE_ID from Instructor_mode im,SERVICE_MODE sm where im.SERVICE_MODE_ID = sm.SERVICE_MODE_ID and  im.Instructor_id in (:Instructor_id)  " ;

                SQLQuery query = pSession.createSQLQuery(sb);

                query.setParameterList("Instructor_id", instructorIdList);

                query.addScalar("INSTRUCTOR_ID", StandardBasicTypes.LONG);
                query.addScalar("SERVICE_MODE_NAME", StandardBasicTypes.STRING);
                query.addScalar("SERVICE_MODE_ID", StandardBasicTypes.LONG);

                Map<Long, ServiceMode> cache = new HashMap<Long, ServiceMode>();

                ScrollableResults scroll = query.scroll();

                while (scroll.next()) {

                    Object[] objects = scroll.get();
                    Long instructorId =   (Long) objects[0];
                    ServiceMode sm= new ServiceMode();
                    sm.setServiceModeName((String)objects[1]);
                    sm.setServiceModeId((Long) objects[2]);
                    if (sm!=null) {
 
                    	cache.put(instructorId, sm);

                    }      

                }

                return cache;

            }
        };

    }
    //###########################################################################################
    //###########################################################################################
    
    public static Criterion<Instructor, Instructor> findinstructorByChanges(final String updatedDate) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(Session pSession, int pFetchMode) {
            	StringBuffer sb = new StringBuffer("select  TO_CHAR(instructor_id) instructor_id,STATUS,ENABLED,EXTERNAL_ID from INSTRUCTOR where Instructor_id in ((select distinct INSTRUCTOR_ID from appointment where to_char(START_TIME, 'YYYY-MM-DD') <= :after30day and  to_char(START_TIME, 'YYYY-MM-DD') >= :yesterday and  to_char(UPDATED, 'YYYY-MM-DD HH24:MI') >= :updatedDate) ");
				sb.append(" union (select distinct ins.INSTRUCTOR_ID from Availability av,Instructor ins where av.AVAILABILITY_ID = ins.AVAILABILITY_ID and to_char(av.UPDATED, 'YYYY-MM-DD HH24:MI') >= :updatedDate)");
				sb.append(" union (select distinct INSTRUCTOR_ID from Instructor_Activities  where to_char(UPDATED, 'YYYY-MM-DD HH24:MI') >= :updatedDate)");
				//sb.append(" union (select distinct INSTRUCTOR_ID from Instructor where to_char(UPDATED, 'YYYY-MM-DD HH24:MI') >= :updatedDate) )");
				sb.append(" union (select distinct ins.INSTRUCTOR_ID from ONLINE_AVAILABILITY ov,Instructor ins where ov.INSTRUCTOR_ID = ins.INSTRUCTOR_ID and to_char(ov.UPDATED, 'YYYY-MM-DD HH24:MI') >= :updatedDate)");
				sb.append(" union (select distinct ins.INSTRUCTOR_ID from INSTORE_AVAILABILITY iv,Instructor ins where iv.INSTRUCTOR_ID = ins.INSTRUCTOR_ID and to_char(iv.UPDATED, 'YYYY-MM-DD HH24:MI') >= :updatedDate)");
				sb.append("   )");
				
				 //###########################
				Calendar c = Calendar.getInstance();
				c.add(Calendar.DATE, -1);
				Date yesterday01= c.getTime();
				c.add(Calendar.DATE, 31);
				Date after_30day= c.getTime();
				DateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
				String yesterday = dateFormatter.format(yesterday01);
				String after30day = dateFormatter.format(after_30day);

				//#########################################
                SQLQuery query = pSession.createSQLQuery(sb.toString());

                System.out.println("old"+sb);
                 
                
                
                query.addScalar("instructor_id", StandardBasicTypes.STRING); 
                query.addScalar("STATUS", StandardBasicTypes.STRING); 
                query.addScalar("ENABLED", StandardBasicTypes.STRING); 
                query.addScalar("EXTERNAL_ID", StandardBasicTypes.STRING); 
                query.setString("after30day", after30day);
                query.setString("yesterday", yesterday);
                query.setString("updatedDate", updatedDate);
                ScrollableResults scroll = query.scroll();
            	List<Instructor> result = new ArrayList<Instructor>();
            	Instructor dto = null;               
   
                while (scroll.next()) {
                	Object[] objects = scroll.get();
                	String ins_id = (String) objects[0];
                	dto = new Instructor();
                	if(ins_id != null){
                		dto.setInstructorId(Long.valueOf(ins_id));
                		dto.setStatus((String)objects[1]);
                		if("Y".equals((String)objects[2]))dto.setEnabled(Enabled.Y);
                		if("N".equals((String)objects[2]))dto.setEnabled(Enabled.N); 
                		dto.setExternalId((String)objects[3]);
                	result.add(dto);
                	}
                }        
                
                scroll.close();
                return result;
        }
            
        };
    }
    
    //#############################################################################################
    
//###########################################################################################
    
    public static Criterion<Instructor, Instructor> findFullinstructorByChanges() {
    	        return new InstructorCriterion<Instructor>() {
    	            @Override
    	            @SuppressWarnings("unchecked")
    	            public List<Instructor> search(Session pSession, int pFetchMode) {
    	            	StringBuffer sb = new StringBuffer(" ");
    					sb.append(" select   instructor_id,STATUS,ENABLED from Instructor  where  status = 'A' and enabled = 'Y' ");
    					
    					
    					  
    	
    					//#########################################
    	                SQLQuery query = pSession.createSQLQuery(sb.toString());
    	                 
    	                query.addScalar("instructor_id", StandardBasicTypes.STRING); 
    	                query.addScalar("STATUS", StandardBasicTypes.STRING); 
    	                query.addScalar("ENABLED", StandardBasicTypes.STRING); 
    	                
    	                ScrollableResults scroll = query.scroll();
    	            	List<Instructor> result = new ArrayList<Instructor>();
    	            	Instructor dto = null;               
    	   
    	                while (scroll.next()) {
    	                	Object[] objects = scroll.get();
    	                	String ins_id = (String) objects[0];
    	                	dto = new Instructor();
    	                	if(ins_id != null){
    	                		dto.setInstructorId(Long.valueOf(ins_id));
    	                		dto.setStatus((String)objects[1]);
    	                		if("Y".equals((String)objects[2]))dto.setEnabled(Enabled.Y);
    	                		if("N".equals((String)objects[2]))dto.setEnabled(Enabled.N); 
    	                	result.add(dto);
    	                	}
    	                }        
    	                
    	                scroll.close();
    	                return result;
    	        }
    	            
    	        };
    	    }
    	    
    	    //#############################################################################################
    	 
    
    //#############################################################################################
	//###########################################################################################
    
    public static Criterion<Instructor, Instructor> findDisabledInstructorByChanges(final String updatedDate) {
        return new InstructorCriterion<Instructor>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Instructor> search(Session pSession, int pFetchMode) {
            	StringBuffer sb = new StringBuffer("select  TO_CHAR(instructor_id) instructor_id,STATUS,ENABLED from INSTRUCTOR where Instructor_id in (");
				sb.append(" (SELECT DISTINCT    instructor_id  FROM instructor   WHERE status = 'T' and TO_CHAR(updated,'YYYY-MM-DD HH24:MI') >= :updatedDate)");
				sb.append("union   (SELECT DISTINCT instructor_id FROM instructor    WHERE status = 'A' and enabled = 'N' and  TO_CHAR(updated,'YYYY-MM-DD HH24:MI') >= :updatedDate))");
 
                SQLQuery query = pSession.createSQLQuery(sb.toString());
                 
                query.addScalar("instructor_id", StandardBasicTypes.STRING); 
                query.addScalar("STATUS", StandardBasicTypes.STRING); 
                query.addScalar("ENABLED", StandardBasicTypes.STRING); 
 
                query.setString("updatedDate", updatedDate);
   
                ScrollableResults scroll = query.scroll();
            	List<Instructor> result = new ArrayList<Instructor>();
            	Instructor dto = null;               
   
                while (scroll.next()) {
                	Object[] objects = scroll.get();
                	String ins_id = (String) objects[0];
                	dto = new Instructor();
                	if(ins_id != null){
                		dto.setInstructorId(Long.valueOf(ins_id));
                		dto.setStatus((String)objects[1]);
                		if("Y".equals((String)objects[2]))dto.setEnabled(Enabled.Y);
                		if("N".equals((String)objects[2]))dto.setEnabled(Enabled.N); 
                	result.add(dto);
                	}
                }        
                
                scroll.close();
                return result;
        }
            
        };
    }
    
    //#############################################################################################
    
    //#############################################################################################
 	//###########################################################################################
     
     public static Criterion<Instructor, Instructor> findDisabledFullInstructorByChanges(final String updatedDate) {
         return new InstructorCriterion<Instructor>() {
             @Override
             @SuppressWarnings("unchecked")
             public List<Instructor> search(Session pSession, int pFetchMode) {
             	StringBuffer sb = new StringBuffer("select  TO_CHAR(instructor_id) instructor_id,STATUS,ENABLED from INSTRUCTOR where Instructor_id in (");
 				sb.append(" (SELECT DISTINCT    instructor_id  FROM instructor   WHERE status = 'T' and TO_CHAR(updated,'YYYY-MM-DD HH24:MI') >= :updatedDate)");
 				sb.append("union   (SELECT DISTINCT instructor_id FROM instructor    WHERE status = 'A' and enabled = 'N' and  TO_CHAR(updated,'YYYY-MM-DD HH24:MI') >= :updatedDate))");
  
                 SQLQuery query = pSession.createSQLQuery(sb.toString());
                  
                 query.addScalar("instructor_id", StandardBasicTypes.STRING); 
                 query.addScalar("STATUS", StandardBasicTypes.STRING); 
                 query.addScalar("ENABLED", StandardBasicTypes.STRING); 
  
                 query.setString("updatedDate", updatedDate);
    
                 ScrollableResults scroll = query.scroll();
             	List<Instructor> result = new ArrayList<Instructor>();
             	Instructor dto = null;               
    
                 while (scroll.next()) {
                 	Object[] objects = scroll.get();
                 	String ins_id = (String) objects[0];
                 	dto = new Instructor();
                 	if(ins_id != null){
                 		dto.setInstructorId(Long.valueOf(ins_id));
                 		dto.setStatus((String)objects[1]);
                 		if("Y".equals((String)objects[2]))dto.setEnabled(Enabled.Y);
                 		if("N".equals((String)objects[2]))dto.setEnabled(Enabled.N); 
                 	result.add(dto);
                 	}
                 }        
                 
                 scroll.close();
                 return result;
         }
             
         };
     }
       //attendancestatus
         public static InstructorCriterion<InstructorAppointmentStatusDTO> getInstructorDetailsByExternalId(
         		String instructorExternalId, String appointmentId) {
         	
         	return new InstructorCriterion<InstructorAppointmentStatusDTO>() {
                 @Override
                 @SuppressWarnings("unchecked")
                 public List<InstructorAppointmentStatusDTO> search(Session pSession, int pFetchMode) {
                 	StringBuffer sb = new StringBuffer(" SELECT i.person_id, i.version, i.site_id " );
         			sb.append(" FROM  instructor  i " );
         			sb.append(" WHERE  i.external_id =:external_id " );
           			//System.out.println("33333333333333333333"+sb.toString());
         			SQLQuery query = pSession.createSQLQuery(sb.toString());
          			query.setString("external_id", instructorExternalId);
         			query.addScalar("person_id", StandardBasicTypes.STRING);
         		    query.addScalar("version", StandardBasicTypes.STRING);
         		    query.addScalar("site_id", StandardBasicTypes.STRING);
         		             		    
                     ScrollableResults scroll = query.scroll();
                 	List<InstructorAppointmentStatusDTO> result = new ArrayList<InstructorAppointmentStatusDTO>();
                 	InstructorAppointmentStatusDTO dto = null;               
                     while (scroll.next()) {
                     	Object[] objects = scroll.get();                  	
                     	dto = new InstructorAppointmentStatusDTO();
                     	dto.setUpdatedBy((String)objects[0]);
                     	dto.setVersion((String)objects[1]);
                     	dto.setSite((String)objects[2]);
                  
                        result.add(dto);
                     }
                    // System.out.println(query + "\n 4444"+result);
                     
                        return result ;
                     		
         			
         }
                 
             };
         
     }
     
     //#############################################################################################
 //#############################################################################################
         
         public static Criterion<Instructor, DailySubscriptionReportDTO> findCustomerReportRecords() {
 	        return new InstructorCriterion<DailySubscriptionReportDTO>() {
 	
 	            @Override
 	            public List<DailySubscriptionReportDTO> search(Session pSession) {            	
 	            	SQLQuery query = null;
 	            	  Log.info("entered into InstructorCriterion.findInstructorActivities() {}");
  
 	            	  
 	            	  String startTime = getCustStartTime();
 	                  String endTime = getCustEndTime();
 	                  
 						/*StringBuilder sb = new StringBuilder("select l.EXTERNAL_ID,P.FIRST_NAME,P.LAST_NAME,l.LOCATION_NAME,A.ACTIVITY_NAME");  
 						sb.append(" from location l");
 						sb.append(" join LOCATION_PROFILE lp on l.PROFILE_ID = lp.PROFILE_ID");
 						sb.append(" join INSTRUCTOR I on l.LOCATION_ID = I.LOCATION_ID"); 
 						sb.append(" join PERSON P on P.PERSON_ID = I.PERSON_ID"); 
 						sb.append(" join INSTRUCTOR_ACTIVITIES IA on I.INSTRUCTOR_ID = IA.INSTRUCTOR_ID"); 
 						sb.append(" join ACTIVITY A on IA.ACTIVITY_ID = A.ACTIVITY_ID"); 
 						sb.append(" where lp.ENABLED = 'Y' and I.ENABLED = 'Y' and I.STATUS = 'A'");
 						sb.append(" order by l.EXTERNAL_ID,P.FIRST_NAME,P.LAST_NAME");*/
 						
 						StringBuilder sb = new StringBuilder("select c.external_id customer#,p.first_name,p.last_name,p.email,l.external_id || ' ' || l.location_name location,cs.status_name,");
 						sb.append("p.phone phone, to_char(c.customer_created, 'MM-DD-YYYY') SIGN_UP_DATE");
 						sb.append(" from customer c,person p,location l,customer_status cs ");
 						sb.append(" where c.person_id = p.person_id ");
 						sb.append(" and c.location_external_id = l.external_id ");
 						sb.append(" and c.customer_status_id =  cs.customer_status_id ");
 						sb.append(" and c.customer_status_id != '2' ");
 						sb.append(" and l.external_id not in ('998','999') ");
 						sb.append(" and c.customer_created BETWEEN to_date(?, 'YYYY-DD-MM HH24:MI:SS') and to_date(?, 'YYYY-DD-MM HH24:MI:SS') ");
 							
 						query = pSession.createSQLQuery(sb.toString());
 						
 						query.setParameter(0, startTime);
 						query.setParameter(1, endTime);
 						
 						query.addScalar("customer#", StandardBasicTypes.STRING);	        		
 						query.addScalar("FIRST_NAME", StandardBasicTypes.STRING);
 						query.addScalar("LAST_NAME", StandardBasicTypes.STRING);
 						query.addScalar("EMAIL", StandardBasicTypes.STRING);
 						query.addScalar("location", StandardBasicTypes.STRING);
 						query.addScalar("status_name", StandardBasicTypes.STRING);
 						query.addScalar("phone", StandardBasicTypes.STRING);
 						query.addScalar("SIGN_UP_DATE", StandardBasicTypes.STRING);
 						
 						ScrollableResults scroll = query.scroll();
 						List<DailySubscriptionReportDTO> resultList = new ArrayList<DailySubscriptionReportDTO>();
 						
 						while (scroll.next()) {
 							Object[] objects = scroll.get();
 							String customerNo = (String) objects[0];
 							 String firstName = (String) objects[1];
 							String lastName = (String) objects[2];
 							String email=(String) objects[3];
 							String location=(String) objects[4];
 							String statusName=(String) objects[5];
 							String phone=(String) objects[6];
 							String signUpDate=(String) objects[7];
 							
 							DailySubscriptionReportDTO dto = new DailySubscriptionReportDTO(customerNo, firstName, lastName,
 									email, location,statusName,phone,signUpDate);
 							resultList.add(dto);
 	
 						}        
 						scroll.close();
 		            	  Log.info("InstructorCriterion.findInstructorActivities(), after retrieving DB values");
 						
 						return resultList;	   
 	        }
 	    };  
 	}
 	
         
         public static Criterion<Instructor, DailySubscriptionReportDTO> findDailySubcribtionLocation() {
  	        return new InstructorCriterion<DailySubscriptionReportDTO>() {
  	
  	            @Override
  	            public List<DailySubscriptionReportDTO> search(Session pSession) {            	
  	            	SQLQuery query = null;
  	            	  Log.info("entered into InstructorCriterion.findInstructorActivities() {}");
   
  	            	  
  	            	  String startTime = getCustStartTime();
  	                  String endTime = getCustEndTime();
  	                  
  						/*StringBuilder sb = new StringBuilder("select l.EXTERNAL_ID,P.FIRST_NAME,P.LAST_NAME,l.LOCATION_NAME,A.ACTIVITY_NAME");  
  						sb.append(" from location l");
  						sb.append(" join LOCATION_PROFILE lp on l.PROFILE_ID = lp.PROFILE_ID");
  						sb.append(" join INSTRUCTOR I on l.LOCATION_ID = I.LOCATION_ID"); 
  						sb.append(" join PERSON P on P.PERSON_ID = I.PERSON_ID"); 
  						sb.append(" join INSTRUCTOR_ACTIVITIES IA on I.INSTRUCTOR_ID = IA.INSTRUCTOR_ID"); 
  						sb.append(" join ACTIVITY A on IA.ACTIVITY_ID = A.ACTIVITY_ID"); 
  						sb.append(" where lp.ENABLED = 'Y' and I.ENABLED = 'Y' and I.STATUS = 'A'");
  						sb.append(" order by l.EXTERNAL_ID,P.FIRST_NAME,P.LAST_NAME");*/
  						
  					StringBuilder sb = new StringBuilder("select lo.external_id || ' ' || lo.location_name LOCATION ");
						sb.append("from location lo where  lo.external_id not in ('998','999') and lo.location_id not in ( ");
						sb.append(" select distinct l.location_id   ");
						sb.append(" from customer c,person p,location l,customer_status cs  ");
						sb.append(" where c.person_id = p.person_id  ");
						sb.append(" and c.location_external_id = l.external_id  ");
						sb.append(" and c.customer_status_id =  cs.customer_status_id  ");
						sb.append(" and c.customer_status_id != '2'   ");
						sb.append(" and l.external_id not in ('998','999')  ");
						sb.append(" and c.customer_created BETWEEN to_date(?, 'YYYY-DD-MM HH24:MI:SS') and to_date(?, 'YYYY-DD-MM HH24:MI:SS')) ");
  							
  						query = pSession.createSQLQuery(sb.toString());
  						
  						query.setParameter(0, startTime);
  						query.setParameter(1, endTime);
  						
  						query.addScalar("LOCATION", StandardBasicTypes.STRING);	        		
  						 
  						
  						ScrollableResults scroll = query.scroll();
  						List<DailySubscriptionReportDTO> resultList = new ArrayList<DailySubscriptionReportDTO>();
  						
  						while (scroll.next()) {
  							Object[] objects = scroll.get();
  							String location = (String) objects[0];
  						 
  							
  							DailySubscriptionReportDTO dto = new DailySubscriptionReportDTO();
  							dto.setLocation(location);
  							resultList.add(dto);
  	
  						}        
  						scroll.close();
  		            	  Log.info("InstructorCriterion.findInstructorActivities(), after retrieving DB values");
  						
  						return resultList;	   
  	        }
  	    };  
  	}
        
         
         private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-dd-MM HH:mm:ss");
         private static String getCustStartTime() {

           	 Calendar day = Calendar.getInstance();
           	
        		day.set(Calendar.MILLISECOND, 0);
        		day.set(Calendar.SECOND, 0);
        		day.set(Calendar.MINUTE, 0);
        		day.set(Calendar.HOUR_OF_DAY, 0);
        		 day.add(Calendar.DATE, -1);
           
               return simpleDateFormat.format(day.getTime());
           }

           private static String getCustEndTime() {

         	  Calendar day = Calendar.getInstance();
        	   	
        		day.set(Calendar.MILLISECOND, 0);
        		day.set(Calendar.SECOND, 0);
        		day.set(Calendar.MINUTE, 59);
        		day.set(Calendar.HOUR_OF_DAY, 23);
        		 day.add(Calendar.DATE, -1);
          
              return simpleDateFormat.format(day.getTime());
           }
           

           
           
           
}
