package com.guitarcenter.scheduler.dao.criterion.dto;

/**
 * Created by jose<PERSON><PERSON> on 1/8/14
 */
public class RoomSortedDTO {

    private Long roomId;
    private Long duration;
    private Long priority;

    public RoomSortedDTO(Long pRoomId, Long pDuration) {
        roomId = pRoomId;
        duration = pDuration;
    }

    public Long getRoomId() {
        return roomId;
    }

    public void setRoomId(Long pRoomId) {
        roomId = pRoomId;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long pDuration) {
        duration = pDuration;
    }

    public Long getPriority() {
        return priority;
    }

    public void setPriority(Long pPriority) {
        if (pPriority == null) return;
        if (priority == null) {
            priority = pPriority;
        } else if (priority < pPriority) {
            priority = pPriority;
        }
    }
}
