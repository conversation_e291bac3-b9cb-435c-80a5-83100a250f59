package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION_PROFILE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_PARENT_ROOM;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM_NUMBER;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM_SIZE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM_TEMPLATE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM_TYPE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringTokenizer;

import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.ScrollableResults;
import org.hibernate.Session;
import org.hibernate.type.StandardBasicTypes;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.dao.criterion.dto.InstructorOpenAppointmentsDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.RoomDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.RoomSortedDTO;
import com.guitarcenter.scheduler.dao.criterion.util.RoomSortedComparator;
import com.guitarcenter.scheduler.dto.ConflictingAppointmentListDTO;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.NativeString;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.enums.Enabled;

public abstract class RoomCriterion<E> extends AbstractCriterion<Room, E> implements Criterion<Room, E> {
	
	private static final Logger		LOG					= LoggerFactory.getLogger(AppointmentCriterion.class);
	
	public static String newline = System.getProperty("line.separator");

    private static final Criterion<Room, Room> DEFAULT_INSTANCE = new RoomCriterion<Room>() {
    };


    private RoomCriterion() {
        super(Room.class);
    }


    @Override
    protected String getFetchScript(int pFetchMode) {
        StringBuilder sb = new StringBuilder();
        sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
        sb.append(addFetchHQL(FETCH_LOCATION_PROFILE, pFetchMode, "t.locationProfile", true));
        sb.append(addFetchHQL(FETCH_PARENT_ROOM, pFetchMode, "t.parentRoom", true));
        sb.append(addFetchHQL(FETCH_ROOM_NUMBER, pFetchMode, "t.roomNumber", true));
        sb.append(addFetchHQL(FETCH_ROOM_SIZE, pFetchMode, "t.roomSize", true));
        sb.append(addFetchHQL(FETCH_ROOM_TEMPLATE, pFetchMode, "t.Room", true));
        sb.append(addFetchHQL(FETCH_ROOM_TYPE, pFetchMode, "t.roomType", true));
        sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
        return sb.toString();
    }


    @Override
    public List<E> search(Session pSession, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }


    @Override
    public E get(Session pSession, int pFetchMode) {
        throw new UnsupportedOperationException("current criteria object unsupport this operation.");
    }


    public static Criterion<Room, Room> getInstance() {
        return DEFAULT_INSTANCE;
    }


    public static Criterion<Room, Room> findByLocationProfileId(final long pLocaltionProfileId) {
        RoomCriterion<Room> instance = new RoomCriterion<Room>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Room> search(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Room t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append("where t.locationProfile.profileId = :localtionProfileId order by t.roomId");
                Query query = pSession.createQuery(sb.toString());
                query.setLong("localtionProfileId", pLocaltionProfileId);
                return query.list();
            }

        };
        return instance;
    }


    public static Criterion<Room, Room> findByProfileIdAndEnabled(final long pLocaltionProfileId, final Enabled pEnabled) {
        RoomCriterion<Room> instance = new RoomCriterion<Room>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Room> search(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Room t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append("where t.locationProfile.profileId = :localtionProfileId");
                sb.append(" and t.enabled = :enabled");
                sb.append(" order by t.roomId");
                Query query = pSession.createQuery(sb.toString());
                query.setLong("localtionProfileId", pLocaltionProfileId);
                query.setString("enabled", pEnabled.toString());
                return query.list();
            }

        };
        return instance;
    }


    public static Criterion<Room, Room> findByRoomIds(final Long... pRoomIds) {
        RoomCriterion<Room> instance = new RoomCriterion<Room>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Room> search(Session pSession, int pFetchMode) {
                List<Long> roomIds;
                if (null == pRoomIds || 0 == pRoomIds.length) {
                    return Collections.EMPTY_LIST;
                } else {
                    roomIds = Arrays.asList(pRoomIds);
                }
                StringBuilder sb = new StringBuilder("from Room t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append("where t.roomId in(:roomId) order by t.roomId");
                Query query = pSession.createQuery(sb.toString());
                query.setParameterList("roomId", roomIds);
                return query.list();
            }

        };
        return instance;
    }


    public static Criterion<Room, Room> findByServiceId(final long pServiceId) {
        RoomCriterion<Room> instance = new RoomCriterion<Room>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Room> search(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Room t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" left join fetch t.services s ");
                sb.append("where s.serviceId = :serviceId order by t.roomId");
                Query query = pSession.createQuery(sb.toString());
                query.setLong("serviceId", pServiceId);
                return query.list();
            }

        };
        return instance;
    }


    public static Criterion<Room, Room> findByActivityIdAndEnabled(final long pActivityId, final Enabled pEnabled) {
        RoomCriterion<Room> instance = new RoomCriterion<Room>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Room> search(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Room t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" left join fetch t.activities s ");
                sb.append("where s.activityId = :activityId");
                sb.append(" and t.enabled = :enabled");
                sb.append(" order by t.roomId");
                Query query = pSession.createQuery(sb.toString());
                query.setLong("activityId", pActivityId);
                query.setString("enabled", pEnabled.toString());
                return query.list();
            }

        };
        return instance;
    }


    public static Criterion<Room, Room> findByProfileIdAndActivityIds(final long pProfileId, final Long... pActivityIds) {
        RoomCriterion<Room> instance = new RoomCriterion<Room>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Room> search(Session pSession, int pFetchMode) {
                if (pActivityIds.length == 0) {
                    return Collections.emptyList();
                }
                StringBuilder sb = new StringBuilder("select distinct t from Room t ");
                sb.append(getFetchScript(pFetchMode));
                sb.append(" left join fetch t.activities s ");
                sb.append("where s.activityId in (:activityIds)");
                sb.append(" and t.locationProfile.profileId = :profileId ");
                sb.append(" and t.enabled = :enabled");
                sb.append(" order by t.roomId");
                Query query = pSession.createQuery(sb.toString());
                query.setLong("profileId", pProfileId);
                query.setParameterList("activityIds", pActivityIds);
                query.setString("enabled", Enabled.Y.toString());
                return query.list();
            }

        };
        return instance;
    }


    public static Criterion<Room, Room> findByRoomTemplateIdAndEnabled(final long pRoomTemplateId,
                                                                       final Enabled pEnabled) {
        RoomCriterion<Room> instance = new RoomCriterion<Room>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Room> search(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Room t ");
                sb.append(" left join fetch t.roomSize ");
                sb.append(" left join fetch t.site ");
                sb.append(" left join fetch t.roomType ");
                sb.append(" left join fetch t.roomNumber ");
                sb.append(" left join fetch t.roomNumber ");
                sb.append(" left join fetch t.activities ");
                sb.append(" left join fetch t.services ");
                sb.append("where t.roomTemplate.roomTemplateId = :roomTemplateId");
                sb.append(" and t.enabled = :enabled");
                sb.append(" order by t.roomId");
                Query query = pSession.createQuery(sb.toString());
                query.setLong("roomTemplateId", pRoomTemplateId);
                query.setString("enabled", pEnabled.toString());
                return query.list();
            }

        };
        return instance;
    }


    public static Criterion<Room, Room> findByRoomTemplateId(final long pRoomTemplateId) {
        RoomCriterion<Room> instance = new RoomCriterion<Room>() {

            @Override
            @SuppressWarnings("unchecked")
            public List<Room> search(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("from Room t ");
                sb.append(" left join fetch t.roomSize ");
                sb.append(" left join fetch t.site ");
                sb.append(" left join fetch t.roomType ");
                sb.append(" left join fetch t.roomNumber ");
                sb.append(" left join fetch t.roomNumber ");
                sb.append(" left join fetch t.activities ");
                sb.append(" left join fetch t.services ");
                sb.append("where t.roomTemplate.roomTemplateId = :roomTemplateId");
                sb.append(" order by t.roomId");
                Query query = pSession.createQuery(sb.toString());
                query.setLong("roomTemplateId", pRoomTemplateId);
                return query.list();
            }

        };
        return instance;
    }

    public static Criterion<Room, Room> findByProfileIdAndActivityIdAndDateTime(final long pProfileId, final long pActivityId, final Date pStartDate, final Date pEndDate, final Long pExcludeAppointmentId) {
        return new RoomCriterion<Room>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Room> search(Session pSession, int pFetchMode) {
                StringBuilder sb = new StringBuilder("select {t.*}, {sub_room.*}");
                sb.append("  from room t");
                sb.append("  left join room_activities r_s");
                sb.append("    on t.room_id = r_s.room_id");
                sb.append("  left join room sub_room");
                sb.append("    on t.room_id = sub_room.parent_id");
                sb.append(" where t.profile_id = :profileId");
                sb.append(" and r_s.activity_id = :activityId");
                sb.append(" and (t.enabled = 'Y' or t.enabled is null)");
                SQLQuery query = pSession.createSQLQuery(sb.toString());
                query.addEntity("t", Room.class);
                query.addEntity("sub_room", Room.class);
                query.setLong("profileId", pProfileId);
                query.setLong("activityId", pActivityId);
                List<Object[]> list = query.list();
                Map<Long, RoomDTO> cache = new HashMap<Long, RoomDTO>();
                for (Object[] rooms : list) {
                    Room room = (Room) rooms[0];
                    Room subRoom = (Room) rooms[1];
                    Long roomId = room.getRoomId();
                    if (!cache.containsKey(roomId)) {
                        cache.put(roomId, new RoomDTO(room, subRoom));
                    }
                    RoomDTO roomDTO = cache.get(roomId);
                    roomDTO.addSubRoom(subRoom);
                }
                Map<Long, Room> maps = new HashMap<Long, Room>();
                for (Map.Entry<Long, RoomDTO> entry : cache.entrySet()) {
                    RoomDTO roomDTO = entry.getValue();
                    Room room = roomDTO.getRoom();
                    Set<Room> subRooms = roomDTO.getSubRooms();
                    Long[] subRoomIds = null;
                    int index = 0;
                    for (Room subRoom : subRooms) {
                        if (subRoomIds == null) subRoomIds = new Long[subRooms.size()];
                        subRoomIds[index] = subRoom.getRoomId();
                        index++;
                    }
                    sb = new StringBuilder();
                    sb.append("select count(*) as counts");
                    sb.append("  from appointment t");
                    sb.append(" where to_char(t.start_time, 'YYYY-MM-DD') = to_char(:targetDate, 'YYYY-MM-DD')");
                    sb.append("   and (t.canceled = 'H' or t.canceled = 'N' or t.canceled is null)");
                    sb.append("   and t.profile_id = :profileId");
                    sb.append("   and ((to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                    sb.append("        or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                    sb.append("        or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS'))");
                    sb.append("        or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                    sb.append("        )");
                    if (pExcludeAppointmentId != null) {
                        sb.append(" and t.appointment_id != :excludeAppId ");
                    }
                    if (room.getParentRoom() == null && subRoomIds == null) {
                        sb.append("   and t.room_id = :roomId");
                    } else if (room.getParentRoom() != null && subRoomIds == null) {
                        sb.append("   and (t.room_id = :roomId or t.room_id = :parentRoomId)");
                    } else if (room.getParentRoom() == null && subRoomIds != null) {
                        sb.append("   and (t.room_id = :roomId or t.room_id in (:subRoomIds))");
                    }
                    query = pSession.createSQLQuery(sb.toString());
                    query.addScalar("counts", StandardBasicTypes.LONG);
                    query.setDate("targetDate", pStartDate);
                    query.setLong("profileId", pProfileId);
                    query.setLong("roomId", room.getRoomId());
                    query.setTimestamp("startTime", pStartDate);
                    query.setTimestamp("endTime", pEndDate);
                    if (room.getParentRoom() != null && subRoomIds == null) {
                        query.setLong("parentRoomId", room.getParentRoom().getRoomId());
                    } else if (room.getParentRoom() == null && subRoomIds != null) {
                        query.setParameterList("subRoomIds", subRoomIds);
                    }
                    if (pExcludeAppointmentId != null) {
                        query.setLong("excludeAppId", pExcludeAppointmentId);
                    }
                    Long counts = (Long) query.uniqueResult();
                    if (counts == 0) {
                        if (!maps.containsKey(room.getRoomId())) {
                            maps.put(room.getRoomId(), room);
                        }
                    } else if (maps.containsKey(room.getRoomId())) {
                        maps.remove(room.getRoomId());
                    }
                }
                if (maps.size() == 0) {
                    return Collections.EMPTY_LIST;
                }
                String sql = "select room_id, duration" +
                        " from (select t.room_id," +
                        "        abs(((to_date(:endTimeStr, 'YYYY-MM-DD HH24:MI:SS') -" +
                        "                to_date(to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS')," +
                        "                        'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 * 60)) as duration" +
                        "        from appointment t" +
                        "        where t.profile_id = :profileId" +
                        "        and t.room_id in (:rooms)" +
                        "        and (t.canceled is null or t.canceled = 'N' or t.canceled = 'H')" +
                        " and to_char(t.start_time, 'YYYY-MM-DD') >= :startDateStr" +
                        " and to_char(t.end_time, 'YYYY-MM-DD') <= :endDateStr" +
                        " union" +
                        " select t.room_id," +
                        "        abs(((to_date(:startTimeStr, 'YYYY-MM-DD HH24:MI:SS') -" +
                        "                to_date(to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS')," +
                        "                        'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 * 60)) as duration" +
                        " from appointment t" +
                        " where t.profile_id = :profileId" +
                        " and t.room_id in (:rooms)" +
                        " and (t.canceled is null or t.canceled = 'N' or t.canceled = 'H')" +
                        " and to_char(t.start_time, 'YYYY-MM-DD') >= :startDateStr" +
                        " and to_char(t.end_time, 'YYYY-MM-DD') <= :endDateStr)" +
                        " order by duration";
                query = pSession.createSQLQuery(sql);
                query.addScalar("room_id", StandardBasicTypes.LONG);
                query.addScalar("duration", StandardBasicTypes.LONG);
                query.setLong("profileId", pProfileId);
                query.setParameterList("rooms", maps.keySet());
                DateTime startDate = new DateTime(pStartDate);
                DateTime endDate = new DateTime(pEndDate);
                query.setString("startTimeStr", startDate.toString("yyyy-MM-dd HH:mm:ss"));
                query.setString("startDateStr", startDate.minusDays(7).toString("yyyy-MM-dd"));
                query.setString("endTimeStr", endDate.toString("yyyy-MM-dd HH:mm:ss"));
                query.setString("endDateStr", endDate.plusDays(7).toString("yyyy-MM-dd"));
                list = query.list();
                List<RoomSortedDTO> roomSortedDTOList = new LinkedList<RoomSortedDTO>();
                Map<Long, RoomSortedDTO> roomSortedDTOCache = new HashMap<Long, RoomSortedDTO>();
                for (Object[] objects : list) {
                    Long room_id = (Long) objects[0];
                    Long duration = (Long) objects[1];
                    if (!roomSortedDTOCache.containsKey(room_id)) {
                        RoomSortedDTO roomSortedDTO = new RoomSortedDTO(room_id, duration);
                        roomSortedDTOList.add(roomSortedDTO);
                        roomSortedDTOCache.put(room_id, roomSortedDTO);
                    }
                }
                for (Map.Entry<Long, Room> entry : maps.entrySet()) {
                    Long room_id = entry.getKey();
                    if (!roomSortedDTOCache.containsKey(room_id)) {
                        RoomSortedDTO roomSortedDTO = new RoomSortedDTO(room_id, Long.MAX_VALUE);
                        roomSortedDTOList.add(roomSortedDTO);
                        roomSortedDTOCache.put(room_id, roomSortedDTO);
                    }
                }
                for (Map.Entry<Long, Room> entry : maps.entrySet()) {
                    Long room_id = entry.getKey();
                    RoomDTO roomDTO = cache.get(room_id);
                    RoomSortedDTO roomSortedDTO = roomSortedDTOCache.get(room_id);
                    if (roomDTO.getRoom().getParentRoom() == null && roomDTO.getSubRooms().isEmpty()) {
                        roomSortedDTO.setPriority(1L);
                    } else if (roomDTO.getRoom().getParentRoom() != null && roomDTO.getSubRooms().isEmpty()) {
                        roomSortedDTO.setPriority(0L);
                        if (roomSortedDTOCache.containsKey(roomDTO.getRoom().getParentRoom().getRoomId())){
                            roomSortedDTO.setPriority(2L);
                            roomSortedDTOCache.get(roomDTO.getRoom().getParentRoom().getRoomId()).setPriority(2L);
                        }
                    } else if (roomDTO.getRoom().getParentRoom() == null && !roomDTO.getSubRooms().isEmpty()) {
                        roomSortedDTO.setPriority(2L);
                        Set<Room> subRooms = roomDTO.getSubRooms();
                        for (Room subRoom : subRooms) {
                            if (roomSortedDTOCache.containsKey(subRoom.getRoomId())){
                                roomSortedDTO.setPriority(4L);
                                roomSortedDTOCache.get(subRoom.getRoomId()).setPriority(3L);
                            }
                        }
                    }
                }
                Collections.sort(roomSortedDTOList, new RoomSortedComparator());
                ArrayList<Room> rooms = new ArrayList<Room>();
                for (RoomSortedDTO roomSortedDTO : roomSortedDTOList) {
                    rooms.add(maps.get(roomSortedDTO.getRoomId()));
                }
                return rooms;
            }
        };
    }
    
    
    //Added for LES_27 Method to find Rooms based on profile and activity
    //LES_7
    /**
     * @param pProfileId
     * @param pActivityId
     * 
     * @return
     */
    public static Criterion<Room, Map<Long, RoomDTO>> findByProfileIdAndActivityId(final long pProfileId, final long pActivityId) {
        return new RoomCriterion<Map<Long, RoomDTO>>() {
            @Override
            @SuppressWarnings("unchecked")
            public Map<Long, RoomDTO> get(Session pSession, int pFetchMode) {
            	
                StringBuilder sb = new StringBuilder("select {t.*}, {sub_room.*}");
                sb.append("  from room t");
                sb.append("  left join room_activities r_s");
                sb.append("    on t.room_id = r_s.room_id");
                sb.append("  left join room sub_room");
                sb.append("    on t.room_id = sub_room.parent_id");
                sb.append(" where t.profile_id = :profileId");
                sb.append(" and r_s.activity_id = :activityId");
                sb.append(" and (t.enabled = 'Y' or t.enabled is null)");
                SQLQuery query = pSession.createSQLQuery(sb.toString());
 
                query.addEntity("t", Room.class);
                query.addEntity("sub_room", Room.class);
                query.setLong("profileId", pProfileId);
                query.setLong("activityId", pActivityId);
                
                List<Object[]> list = query.list();
                Map<Long, RoomDTO> cache = new HashMap<Long, RoomDTO>();
                
                for (Object[] rooms : list) {
                    Room room = (Room) rooms[0];
                    Room subRoom = (Room) rooms[1];
                    Long roomId = room.getRoomId();
                    if (!cache.containsKey(roomId)) {
                        cache.put(roomId, new RoomDTO(room, subRoom));
                    }
                    RoomDTO roomDTO = cache.get(roomId);
                    roomDTO.addSubRoom(subRoom);
                }
               
                return cache;
                 
            }
        };
    }
    
    
    /**
     * @param pProfileId
     * @param pActivityId
     * 
     * @return
     */
    public static Criterion<Room, Map<Long, RoomDTO>> findByProfileIdAndActivityIdInsAVL(final long pProfileId, final long pActivityId) {
        return new RoomCriterion<Map<Long, RoomDTO>>() {
            @Override
            @SuppressWarnings("unchecked")
            public Map<Long, RoomDTO> get(Session pSession, int pFetchMode) {
            	
                StringBuilder sb = new StringBuilder("select {t.*}, {sub_room.*}");
                sb.append("  from room t");
                sb.append("  left join room_activities r_s");
                sb.append("    on t.room_id = r_s.room_id");
                sb.append("  left join room sub_room");
                sb.append("    on t.room_id = sub_room.parent_id");
                sb.append(" where t.profile_id = :profileId");
                sb.append(" and r_s.activity_id = :activityId");
                sb.append(" and (t.enabled = 'Y' or t.enabled is null)");
                SQLQuery query = pSession.createSQLQuery(sb.toString());
 
                query.addEntity("t", Room.class);
                query.addEntity("sub_room", Room.class);
                query.setLong("profileId", pProfileId);
                query.setLong("activityId", pActivityId);
                
                List<Object[]> list = query.list();
                Map<Long, RoomDTO> cache = new HashMap<Long, RoomDTO>();
                
                for (Object[] rooms : list) {
                    Room room = (Room) rooms[0];
                    Room subRoom = (Room) rooms[1];
                    Long roomId = room.getRoomId();
                    if (!cache.containsKey(roomId)) {
                        cache.put(roomId, new RoomDTO(room, subRoom));
                    }
                    RoomDTO roomDTO = cache.get(roomId);
                    roomDTO.addSubRoom(subRoom);
                }
               
                return cache;
                 
            }
        };
    }
    
      
    //Added for LES_27 Method to find Rooms based on profile ,activity and date time   
    /**
     * @param cache
     * @param pProfileId
     * @param pStartDate
     * @param pEndDate
     * @return
     */
    public static Criterion<Room, Boolean> findByProfileIdAndActivityIdAndDate(final Map<Long, RoomDTO> cache, final long pProfileId,  final Date pStartDate, final Date pEndDate) {
        return new RoomCriterion<Boolean>() {
            @Override
            @SuppressWarnings("unchecked")
            public Boolean get(Session pSession, int pFetchMode) {
            	
            	boolean roomAvailable = false;
            	
                for (Map.Entry<Long, RoomDTO> entry : cache.entrySet()) {
                    RoomDTO roomDTO = entry.getValue();
                    Room room = roomDTO.getRoom();
                    Set<Room> subRooms = roomDTO.getSubRooms();
                    Long[] subRoomIds = null;
                    int index = 0;
                    for (Room subRoom : subRooms) {
                        if (subRoomIds == null) subRoomIds = new Long[subRooms.size()];
                        subRoomIds[index] = subRoom.getRoomId();
                        index++;
                    }
                    
                    StringBuilder sb = new StringBuilder();
                    sb.append("select count(*) as counts");
                    sb.append("  from appointment t");
                    sb.append(" where to_char(t.start_time, 'YYYY-MM-DD') = to_char(:targetDate, 'YYYY-MM-DD')");
                    sb.append("   and (t.canceled = 'H' or t.canceled = 'N' or t.canceled is null)");
                    sb.append("   and t.profile_id = :profileId");
                    sb.append("   and ((to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                    sb.append("        or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                    sb.append("        or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS'))");
                    sb.append("        or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                    sb.append("        )");
                   
                    if (room.getParentRoom() == null && subRoomIds == null) {
                        sb.append("   and t.room_id = :roomId");
                    } else if (room.getParentRoom() != null && subRoomIds == null) {
                        sb.append("   and (t.room_id = :roomId or t.room_id = :parentRoomId)");
                    } else if (room.getParentRoom() == null && subRoomIds != null) {
                        sb.append("   and (t.room_id = :roomId or t.room_id in (:subRoomIds))");
                    }
                    
                    SQLQuery query = pSession.createSQLQuery(sb.toString());
 
                    query = pSession.createSQLQuery(sb.toString());
                    query.addScalar("counts", StandardBasicTypes.LONG);
                    query.setDate("targetDate", pStartDate);
                    query.setLong("profileId", pProfileId);
                    query.setLong("roomId", room.getRoomId());
                    query.setTimestamp("startTime", pStartDate);
                    query.setTimestamp("endTime", pEndDate);
                    
                    if (room.getParentRoom() != null && subRoomIds == null) {
                        query.setLong("parentRoomId", room.getParentRoom().getRoomId());
                    } else if (room.getParentRoom() == null && subRoomIds != null) {
                        query.setParameterList("subRoomIds", subRoomIds);
                    }

                    Long counts = (Long) query.uniqueResult();
                    
                    if (counts == 0) {
                    	roomAvailable = true;
                    	break;  
                    } 
                }
                
                return roomAvailable;
               
            }
        };
    }
    
    
  //Added for Update Lesson service Phase II LES 7
    /**
     * @param cache
     * @param pProfileId
     * @param pStartDate
     * @param pEndDate
     * @return
     */
    public static Criterion<Room, Room> findRoomByProfileIdAndActivityIdAndDate(final Map<Long, RoomDTO> cache, final long pProfileId,  final Date pStartDate, final Date pEndDate) {
        return new RoomCriterion<Room>() {
            @Override
            @SuppressWarnings("unchecked")
            public Room get(Session pSession, int pFetchMode) {
            	
            	Room availableRoom=new Room();
            	
                for (Map.Entry<Long, RoomDTO> entry : cache.entrySet()) {
                    RoomDTO roomDTO = entry.getValue();
                    Room room = roomDTO.getRoom();
                    Set<Room> subRooms = roomDTO.getSubRooms();
                    Long[] subRoomIds = null;
                    int index = 0;
                    for (Room subRoom : subRooms) {
                        if (subRoomIds == null) subRoomIds = new Long[subRooms.size()];
                        subRoomIds[index] = subRoom.getRoomId();
                        index++;
                    }
                    StringBuilder sb = new StringBuilder();
                    sb.append("select count(*) as counts");
                    sb.append("  from appointment t");
                    sb.append(" where to_char(t.start_time, 'YYYY-MM-DD') = to_char(:targetDate, 'YYYY-MM-DD')");
                    sb.append("   and (t.canceled = 'H' or t.canceled = 'N' or t.canceled is null)");
                    sb.append("   and t.profile_id = :profileId");
                    sb.append("   and ((to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                    sb.append("        or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                    sb.append("        or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:startTime, 'YYYY-MM-DD HH24:MI:SS'))");
                    sb.append("        or (to_char(t.start_time, 'YYYY-MM-DD HH24:MI:SS') <= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS') and to_char(t.end_time, 'YYYY-MM-DD HH24:MI:SS') >= to_char(:endTime, 'YYYY-MM-DD HH24:MI:SS'))");
                    sb.append("        )");
                   
                    if (room.getParentRoom() == null && subRoomIds == null) {
                        sb.append("   and t.room_id = :roomId");
                    } else if (room.getParentRoom() != null && subRoomIds == null) {
                        sb.append("   and (t.room_id = :roomId or t.room_id = :parentRoomId)");
                    } else if (room.getParentRoom() == null && subRoomIds != null) {
                        sb.append("   and (t.room_id = :roomId or t.room_id in (:subRoomIds))");
                    }
                    
                    SQLQuery query = pSession.createSQLQuery(sb.toString());
                    
                    query = pSession.createSQLQuery(sb.toString());
                    query.addScalar("counts", StandardBasicTypes.LONG);
                    query.setDate("targetDate", pStartDate);
                    query.setLong("profileId", pProfileId);
                    query.setLong("roomId", room.getRoomId());
                    query.setTimestamp("startTime", pStartDate);
                    query.setTimestamp("endTime", pEndDate);
                    if (room.getParentRoom() != null && subRoomIds == null) {
                        query.setLong("parentRoomId", room.getParentRoom().getRoomId());
                    } else if (room.getParentRoom() == null && subRoomIds != null) {
                        query.setParameterList("subRoomIds", subRoomIds);
                    }
                    
                    Long counts = (Long) query.uniqueResult();
                    if (counts == 0) {
                    	availableRoom.setRoomId(room.getRoomId());
                    	return availableRoom ;  
                    } 
                }
                
                return null;
               
            }
        };
    }
    
    
    
    //Added for LES_27  Optimization  -  Calls stored procedure to get non available slots
    /**
     * @param cache
     * @param pProfileId
     * @param pStartDate
     * @param pEndDate
     * @return
     */
    public static Criterion<Room, Map<String,List<String>>> findNonAvailableSlots(final long pProfileId, 
    		final Date pStartDate, final long activityID,final long duration) {
        return new RoomCriterion<Map<String,List<String>>>() {
           
        	@Override           
            public Map<String,List<String>> get(Session pSession, int pFetchMode) {
        		
        		
        		
            	            	            	
            	double durations = duration/60.0;
            	            	          	
            	int timeIndex =0;
            	
            	NativeString concurrentString;
            	
            	
            	String consolidatedSlots ="",concurrentSlots = "",dateTimeStrings ="'",date ="", time ="" , parentChildSlots = "";
            	
            	Map<String,List<String>> availTimeSlots = new HashMap<String,List<String>>();
            	
            	List<String> timeList = null;
            	
            	 SQLQuery query = (SQLQuery) pSession.getNamedQuery("callNASlotsFunc");
            		
            		query.setParameter(0, pStartDate);
                    query.setParameter(2, pProfileId);
                    query.setParameter(1, activityID);
                    //query.setParameter(3, durations);
                    
            
                  
                    
                    ScrollableResults scroll =  query.scroll();
                  
                    
                    
                   while (scroll.next()) 
                    {
                    	 Object[] objects = scroll.get();
                    	                    	 
                    	 if(null != objects)
                       	 {	
                    	 
                    		 concurrentString = (NativeString) objects[0];
                    		 
	                    	 if( null != concurrentString)
	                    		concurrentSlots = concurrentString.getNoSlotsStr();
                       	 }	 
                    }
                   
                  
           
                   SQLQuery queryParetnChild = (SQLQuery) pSession.getNamedQuery("callSpltFunc");
           		
                   queryParetnChild.setParameter(0, pStartDate);
                   queryParetnChild.setParameter(2, pProfileId);
                   queryParetnChild.setParameter(1, activityID);
                   //query.setParameter(3, durations);
                   
           
                  
                   
                   
                   ScrollableResults scrollParentChild =  queryParetnChild.scroll();
                 
                   
                  while (scrollParentChild.next()) 
                   {
                   	 Object[] objects = scrollParentChild.get();
                   	
                   	 if(null != objects)
                   	 {	 
                   		 concurrentString = (NativeString) objects[0];
                   	 
                   	 
	                   	 if( null != concurrentString)
	                   		parentChildSlots = concurrentString.getNoSlotsStr();
	                   	 
                   	 } 	 
                   }
                  
                  
                   if( !parentChildSlots.equalsIgnoreCase("") )
                   {
                	   consolidatedSlots =  concurrentSlots.concat(parentChildSlots);
                   }
                   else
                   {
                	   consolidatedSlots =  concurrentSlots;
                	   
                   }
                  
                   
                   if(!consolidatedSlots.equalsIgnoreCase(""))
                   { 
	                   StringTokenizer multiTokenizer = new StringTokenizer(consolidatedSlots, "~");
	                   while (multiTokenizer.hasMoreTokens())
	                   {
	                	   dateTimeStrings = multiTokenizer.nextToken();
	                	   StringTokenizer multiTokenizerDtTm = new StringTokenizer(dateTimeStrings, "*");
	                	   if (multiTokenizerDtTm.hasMoreTokens())
		                   {
	                	   
	                	   date = multiTokenizerDtTm.nextToken();
	                	   timeIndex = dateTimeStrings.indexOf("*");
	                	   time =  dateTimeStrings.substring(timeIndex+1);
	                	   
	                	   StringTokenizer multiTokenizerTm = new StringTokenizer(time, "#");
	                	   while (multiTokenizerTm.hasMoreTokens()) {
	                		   
	                		   String availSlot = multiTokenizerTm.nextToken();
	                		   
		                	   
		                	   if(!availTimeSlots.containsKey(date))
		                	   {
		                		   timeList = new ArrayList<String>();
		                	   } else {
		                		   timeList = availTimeSlots.get(date);
		                	   }
		                	   
		                	   timeList.add(availSlot);
		                	   availTimeSlots.put(date, timeList);

	                	   }
	                	   

	                	   
	                }
                   }   
                   }
                   
                                        
                   
                   return availTimeSlots;
               
            }
        };
    }
    
    //Added for LES_27  Optimization  -  Calls stored procedure to get non available slots
    /**
     * @param cache
     * @param pProfileId
     * @param pStartDate
     * @param pEndDate
     * @return
     */
    public static Criterion<Room, Map<String,List<String>>> findNonAvailableSlotsInsAVL(final long pProfileId, 
    		final Date pStartDate, final long activityID,final long duration) {
        return new RoomCriterion<Map<String,List<String>>>() {
           
        	@Override           
            public Map<String,List<String>> get(Session pSession, int pFetchMode) {
        		
        		
        		
            	            	            	
            	double durations = duration/60.0;
            	            	          	
            	int timeIndex =0;
            	
            	NativeString concurrentString;
            	
            	
            	String consolidatedSlots ="",concurrentSlots = "",dateTimeStrings ="'",date ="", time ="" , parentChildSlots = "";
            	
            	Map<String,List<String>> availTimeSlots = new HashMap<String,List<String>>();
            	
            	List<String> timeList = null;
            	
            	 SQLQuery query = (SQLQuery) pSession.getNamedQuery("callNASlotsFunc");
            		
            		query.setParameter(0, pStartDate);
                    query.setParameter(2, pProfileId);
                    query.setParameter(1, activityID);
                    //query.setParameter(3, durations);
                    
            
                  
                    
                    ScrollableResults scroll =  query.scroll();
                  
                    
                    
                   while (scroll.next()) 
                    {
                    	 Object[] objects = scroll.get();
                    	                    	 
                    	 if(null != objects)
                       	 {	
                    	 
                    		 concurrentString = (NativeString) objects[0];
                    		 
	                    	 if( null != concurrentString)
	                    		concurrentSlots = concurrentString.getNoSlotsStr();
                       	 }	 
                    }
                   
                  
           
                   SQLQuery queryParetnChild = (SQLQuery) pSession.getNamedQuery("callSpltFunc");
           		
                   queryParetnChild.setParameter(0, pStartDate);
                   queryParetnChild.setParameter(2, pProfileId);
                   queryParetnChild.setParameter(1, activityID);
                   //query.setParameter(3, durations);
                   
           
                  
                   
                   
                   ScrollableResults scrollParentChild =  queryParetnChild.scroll();
                 
                   
                  while (scrollParentChild.next()) 
                   {
                   	 Object[] objects = scrollParentChild.get();
                   	
                   	 if(null != objects)
                   	 {	 
                   		 concurrentString = (NativeString) objects[0];
                   	 
                   	 
	                   	 if( null != concurrentString)
	                   		parentChildSlots = concurrentString.getNoSlotsStr();
	                   	 
                   	 } 	 
                   }
                  
                  
                   if( !parentChildSlots.equalsIgnoreCase("") )
                   {
                	   consolidatedSlots =  concurrentSlots.concat(parentChildSlots);
                   }
                   else
                   {
                	   consolidatedSlots =  concurrentSlots;
                	   
                   }
                  
                   
                   if(!consolidatedSlots.equalsIgnoreCase(""))
                   { 
	                   StringTokenizer multiTokenizer = new StringTokenizer(consolidatedSlots, "~");
	                   while (multiTokenizer.hasMoreTokens())
	                   {
	                	   dateTimeStrings = multiTokenizer.nextToken();
	                	   StringTokenizer multiTokenizerDtTm = new StringTokenizer(dateTimeStrings, "*");
	                	   if (multiTokenizerDtTm.hasMoreTokens())
		                   {
	                	   
	                	   date = multiTokenizerDtTm.nextToken();
	                	   timeIndex = dateTimeStrings.indexOf("*");
	                	   time =  dateTimeStrings.substring(timeIndex+1);
	                	   
	                	   StringTokenizer multiTokenizerTm = new StringTokenizer(time, "#");
	                	   while (multiTokenizerTm.hasMoreTokens()) {
	                		   
	                		   String availSlot = multiTokenizerTm.nextToken();
	                		   
		                	   
		                	   if(!availTimeSlots.containsKey(date))
		                	   {
		                		   timeList = new ArrayList<String>();
		                	   } else {
		                		   timeList = availTimeSlots.get(date);
		                	   }
		                	   
		                	   timeList.add(availSlot);
		                	   availTimeSlots.put(date, timeList);

	                	   }
	                	   

	                	   
	                }
                   }   
                   }
                   
                                        
                   
                   return availTimeSlots;
               
            }
        };
    }
    /**
     * For GSSP-241, Conflicting Appointments by Room
     * 
     * @param location
	 * @param startDate
	 * @param endDate
     * @return
     */
   
    public static Criterion<Room, ConflictingAppointmentListDTO> findConflictAppointmentsByRoom(
    		 final long pProfileId,final String pStartDate,final String pEndDate,final String pStartTime,final String pEndTime,final String pCanceled,final long proom_id) {

        return new RoomCriterion<ConflictingAppointmentListDTO>() {

            @Override
            public ConflictingAppointmentListDTO get(Session pSession, int pFetchMode) {
            	String sb = "SELECT  to_char(a.start_time, 'DD-Mon-YYYY') as date1 ," +
                "to_char(a.start_time, 'HH:MI AM') as start_time,to_char(a.end_time, 'HH:MI AM') as end_time," +
                "(extract(day from ((a.end_time - a.start_time) * 24 * 60 * 60 * 60))) as duration," +
                "r.profile_room_name as profile_room_name, t.activity_name as activity_name," +                        
               "c.customer_id as customer_id, p_c.first_name as c_first_name, p_c.last_name as c_last_name " +                  
               "FROM appointment a " +
                "left join appointment_customers a_c on a.appointment_id = a_c.appointment_id " +
               " left join customer c on a_c.customer_id = c.customer_id" +
               " left join person p_c on c.person_id = p_c.person_id" +
               " left join room r on r.room_id = a.room_id" +
               " left join activity t on t.activity_id = a.activity_id" +
                                                               " WHERE TO_CHAR(start_time,'d') = TO_CHAR(to_date(:pStartDate,'MM/dd/yyyy'),'d')" +
                                                               "  AND TO_TIMESTAMP_TZ(TO_CHAR(start_time, 'MM/dd/yyyy'), 'MM/dd/yyyy') BETWEEN TO_TIMESTAMP_TZ(:pStartDate,'MM/dd/yyyy') AND TO_TIMESTAMP_TZ(:pEndDate,'MM/dd/yyyy')"+
                                                               "  AND (a.start_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,'MM/dd/yyyy')||' '||:pStartTime,'MM/dd/yyyy HH24:MI')"+
                                                               " 	   AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,'MM/dd/yyyy')||' '||:pEndTime,'MM/dd/yyyy HH24:MI')"+
                                                               " 	   OR a.end_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,'MM/dd/yyyy')||' '|| :pStartTime,'MM/dd/yyyy HH24:MI')"+
                                                               " 	   AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,'MM/dd/yyyy')||' '||:pEndTime,'MM/dd/yyyy HH24:MI')"+
                                                               " 	   OR TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,'MM/dd/yyyy')||' '||:pStartTime,'MM/dd/yyyy HH24:MI') BETWEEN a.start_time AND a.end_time"+
                                                               " 	   OR TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,'MM/dd/yyyy')||' '||:pEndTime,'MM/dd/yyyy HH24:MI') BETWEEN a.start_time AND a.end_time)"+
                                                               " 	   AND a.profile_id =:pProfileId"+
                                                               " 	   AND a.canceled = :pCanceled"+
                                                               " 	   AND a.room_id =:proom_id ORDER BY a.start_time";                                                              


   
                 SQLQuery query = pSession.createSQLQuery(sb);
                 query.setLong("pProfileId", pProfileId);
                 query.setString("pStartDate", pStartDate);
                 query.setString("pEndDate", pEndDate);
                 query.setString("pStartTime", pStartTime);
                 query.setString("pEndTime", pEndTime);
                 query .setString("pCanceled", pCanceled);
                 query.setLong("proom_id", proom_id);
                 query.addScalar("date1", StandardBasicTypes.STRING);
                 query.addScalar("start_time",  StandardBasicTypes.STRING);
                 query.addScalar("end_time",  StandardBasicTypes.STRING);
                 query.addScalar("duration",  StandardBasicTypes.LONG);
                 query.addScalar("profile_room_name", StandardBasicTypes.STRING);
                 query.addScalar("activity_name", StandardBasicTypes.STRING);
                 query.addScalar("customer_id", StandardBasicTypes.LONG);
                 query.addScalar("c_first_name", StandardBasicTypes.STRING);
                 query.addScalar("c_last_name", StandardBasicTypes.STRING);
                                  
                 ScrollableResults scroll = query.scroll();
                 List<InstructorOpenAppointmentsDTO> result = new ArrayList<InstructorOpenAppointmentsDTO>(), conflictingList = null;                 
                 InstructorOpenAppointmentsDTO instructorOpenAppointmentsDTO = null;                                
                 Map<Long, Customer> customers = new HashMap<Long, Customer>();
                            
                 while (scroll.next()) {
                     Object[] objects = scroll.get();
                     String date1 = (String) objects[0];
                     Long pDuration=(Long) objects[3];
                     String roomName =(String)objects[4];
                     String pActivityName =(String)objects[5];
                 	
                     //Populating the DTO for open appointments	                  
                        
						instructorOpenAppointmentsDTO = new InstructorOpenAppointmentsDTO(roomName, date1, (String) objects[1],  (String)objects[2], pDuration, pActivityName);
						
						//Adding customers details to open appointments DTO	
	                     Long customerId = (Long) objects[6];
	                     if (customerId == null) continue;
	                     Customer customer = customers.get(customerId);
	                     if (customer == null) {
	                         customer = new Customer();
	                         customer.setCustomerId(customerId);
	                         Person person = new Person();
	                         person.setFirstName((String) objects[7]);
	                         person.setLastName((String) objects[8]);
	                         customer.setPerson(person);
	        							customers.put(customerId, customer);
	                     }
	                     instructorOpenAppointmentsDTO.addCustomer(customer);
                         					
                         result.add(instructorOpenAppointmentsDTO);
                     
                 }       
                 
                 
                 ConflictingAppointmentListDTO conflictingAppointmentListDTO = null;
         		if(null != result &&  result.size() > 0){
         			if(result.size() >= 5){
         				conflictingList = result.subList(0, 5);
         			}
         			else{
         				conflictingList = result;
         			}         			
         			conflictingAppointmentListDTO = new ConflictingAppointmentListDTO(result.size(),conflictingList);
         			
         		}      		
                 
        
                 scroll.close();
                 return conflictingAppointmentListDTO;
             }
         };
    }
    /**
     * For GSSP-241, Conflicting Appointments by Room
     * 
     * @param location
	 * @param startDate
	 * @param endDate
     * @return
     */
    
    public static Criterion<Room, ConflictingAppointmentListDTO> findConflictAppointmentsByInstructor(
    		 final long pProfileId,final String pStartDate,final String pEndDate,final String pStartTime,final String pEndTime,final Long pinstructor_id) {

        return new RoomCriterion<ConflictingAppointmentListDTO>() {

            @Override
            public ConflictingAppointmentListDTO get(Session pSession, int pFetchMode) {
            	String sb =" SELECT TO_CHAR(a.start_time, 'DD-Mon-YYYY') AS date1 ," +
            		    " to_char(a.start_time, 'HH:MI AM') as start_time, to_char(a.end_time, 'HH:MI AM') as end_time, " +
            			" (extract(DAY FROM ((a.end_time - a.start_time) * 24 * 60 * 60 * 60))) AS duration," +
            			"  p_i.first_name AS i_first_name," +
            			" p_i.last_name AS i_last_name," +
            			" t.activity_name AS activity_name," +
            			" c.customer_id AS customer_id," +
            			" p_c.first_name AS c_first_name," +
            			" p_c.last_name AS c_last_name" +
            			" FROM appointment a left" +
            			" JOIN appointment_customers a_c" +
            			" ON a.appointment_id = a_c.appointment_id" +
            			" LEFT JOIN customer c" +
            			" ON a_c.customer_id = c.customer_id left" +
            			" JOIN person p_c" +
            			" ON c.person_id = p_c.person_id LEFT" +
            			" JOIN instructor i" +
            			" ON i.instructor_id = a.instructor_id left" +
            			" JOIN person p_i" +
            			" ON i.person_id = p_i.person_id left" +
            			" JOIN activity t" +
            			" ON t.activity_id = a.activity_id" +
            			" WHERE TO_CHAR(start_time,'d') = TO_CHAR(to_date(:pStartDate,'MM/dd/yyyy'),'d')" +
            			" AND TO_TIMESTAMP_TZ(TO_CHAR(start_time, 'MM/dd/yyyy'), 'MM/dd/yyyy') BETWEEN TO_TIMESTAMP_TZ(:pStartDate,'MM/dd/yyyy') AND TO_TIMESTAMP_TZ(:pEndDate,'MM/dd/yyyy')" +
            			" AND (a.start_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,'MM/dd/yyyy')||' '||:pStartTime,'MM/dd/yyyy HH24:MI')" +
            			" AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,'MM/dd/yyyy')||' '||:pEndTime,'MM/dd/yyyy HH24:MI')" +
            			" OR a.end_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,'MM/dd/yyyy')||' '||:pStartTime,'MM/dd/yyyy HH24:MI')" +
            			" AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,'MM/dd/yyyy')||' '||:pEndTime,'MM/dd/yyyy HH24:MI')" +
            			" OR TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,'MM/dd/yyyy')||' '||:pStartTime,'MM/dd/yyyy HH24:MI') BETWEEN a.start_time AND a.end_time" +
            			" OR TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,'MM/dd/yyyy')||' '||:pEndTime,'MM/dd/yyyy HH24:MI') BETWEEN a.start_time AND a.end_time)" +
            			" AND a.profile_id =:pProfileId" +
            			" AND a.canceled = 'N'" +
            			" AND a.instructor_id=:pinstructor_id  ORDER BY a.start_time" ;                                                          


   
                 SQLQuery query = pSession.createSQLQuery(sb);
                 query.setLong("pProfileId", pProfileId);
                 query.setString("pStartDate", pStartDate);
                 query.setString("pEndDate", pEndDate);
                 query.setString("pStartTime", pStartTime);
                 query.setString("pEndTime", pEndTime);
                 query.setLong("pinstructor_id", pinstructor_id);
                 query.addScalar("date1", StandardBasicTypes.STRING);
                 query.addScalar("start_time",  StandardBasicTypes.STRING);
                 query.addScalar("end_time",  StandardBasicTypes.STRING);
                 query.addScalar("duration",  StandardBasicTypes.LONG);
                 query.addScalar("i_first_name", StandardBasicTypes.STRING);
                 query.addScalar("i_last_name", StandardBasicTypes.STRING);
                 query.addScalar("activity_name", StandardBasicTypes.STRING);
                 query.addScalar("customer_id", StandardBasicTypes.LONG);
                 query.addScalar("c_first_name", StandardBasicTypes.STRING);
                 query.addScalar("c_last_name", StandardBasicTypes.STRING);
                 
                 
                 ScrollableResults scroll = query.scroll();
                 List<InstructorOpenAppointmentsDTO> result = new ArrayList<InstructorOpenAppointmentsDTO>(), conflictingList = null;                 
                 InstructorOpenAppointmentsDTO instructorOpenAppointmentsDTO = null;                                
                 Map<Long, Customer> customers = new HashMap<Long, Customer>();
                            
                 while (scroll.next()) {
                     Object[] objects = scroll.get();
                     String date1 = (String) objects[0];
                     Long pDuration=(Long) objects[3];
                     String pActivityName =(String)objects[6];
                 	
                     //Populating the DTO for open appointments	                  
                        					
						instructorOpenAppointmentsDTO = new InstructorOpenAppointmentsDTO((String) objects[4]+' '+(String)objects[5], date1,(String) objects[1],  (String)objects[2], pDuration, pActivityName);
						
						//Adding customers details to open appointments DTO	
	                     Long customerId = (Long) objects[7];
	                     if (customerId == null) continue;
	                     Customer customer = customers.get(customerId);
	                     if (customer == null) {
	                         customer = new Customer();
	                         customer.setCustomerId(customerId);
	                         Person person = new Person();
	                         person.setFirstName((String) objects[8]);
	                         person.setLastName((String) objects[9]);
	                         customer.setPerson(person);
	        							customers.put(customerId, customer);
	                     }
	                     instructorOpenAppointmentsDTO.addCustomer(customer);
                         					
                         result.add(instructorOpenAppointmentsDTO);
                     
                 }        
                 
                 
                 ConflictingAppointmentListDTO conflictingAppointmentListDTO = null;
         		if(null != result &&  result.size() > 0){
         			if(result.size() >= 5){
         				conflictingList = result.subList(0, 5);
         			}
         			else{
         				conflictingList = result;
         			}
         			
         			conflictingAppointmentListDTO = new ConflictingAppointmentListDTO(result.size(),conflictingList);
         			
         		}        		               
        
                 scroll.close();
                 return conflictingAppointmentListDTO;
             }
         };
    }

    

}
