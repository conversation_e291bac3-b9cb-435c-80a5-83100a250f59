package com.guitarcenter.scheduler.dao.criterion;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.hibernate.SQLQuery;
import org.hibernate.ScrollableResults;
import org.hibernate.Session;
import org.hibernate.type.StandardBasicTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.dto.InstructorFloorTimeDTO;
import com.guitarcenter.scheduler.model.Instructor;

//New class created for GSSP - 272
public class InstructorFloorTimeCriterion<E> extends AbstractCriterion<Instructor, E> implements
        Criterion<Instructor, E> {
	
	private static final Logger		Log	= LoggerFactory.getLogger(InstructorFloorTimeCriterion.class);
	private InstructorFloorTimeCriterion()
	{
		super(Instructor.class);
	}
	
	@Override
	public List<E> search(Session pSession, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}

	@Override
	public E get(Session pSession, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}

	@Override
	protected String getFetchScript(int pFetchMode) {
		// TODO Auto-generated method stub
		return null;
	}

	public static Criterion<Instructor, InstructorFloorTimeDTO> findInstructorFloorTime(final Date startDate, final Date endDate)
    {
    return new InstructorFloorTimeCriterion<InstructorFloorTimeDTO>()
   		 {
   	 @Override
        public List<InstructorFloorTimeDTO> search(Session pSession) {            	
        	SQLQuery query = null;
        	  Log.info("entered into InstructorFloorTimeCriterion.findInstructorFloorTime() {}");
        	  StringBuilder sb = new StringBuilder("select  l.external_id as location_id,l.location_name  as location_external_id,i.external_id as instructor_id,");
        	  sb.append(" p.first_name as first_name,p.last_name as last_name, sum(extract(day from ((t.end_time - t.start_time) * 24 * 60))) dur ");
        	  sb.append(" from appointment t,instructor i, location l,activity a, person p ");
        	  sb.append(" where t.instructor_id =  i.instructor_id ");
        	  sb.append(" and t.profile_id = l.profile_id ");
        	  sb.append(" and t.activity_id = a.activity_id ");
			  sb.append(" and p.person_id = i.person_id ");
			  sb.append(" and t.instructor_id is not null "); 
              sb.append(" and t.start_time >=  :startDate ");
              sb.append(" and t.end_time <= :endDate ");
              sb.append(" and decode(t.canceled, null, 'N', t.canceled) != 'Y' ");
              sb.append(" and t.activity_id = '100' ");
              sb.append(" group by  i.external_id,l.external_id,p.first_name,p.last_name,l.location_name ");
              sb.append(" order by l.external_id ");
              
                query = pSession.createSQLQuery(sb.toString());
				
               query.setTime("startDate", startDate);
               query.setTime("endDate", endDate);
				query.addScalar("location_id", StandardBasicTypes.STRING);
				query.addScalar("location_external_id", StandardBasicTypes.STRING);
				query.addScalar("instructor_id", StandardBasicTypes.INTEGER);
				query.addScalar("first_name", StandardBasicTypes.STRING);
				query.addScalar("last_name", StandardBasicTypes.STRING);
				query.addScalar("dur", StandardBasicTypes.INTEGER);
			
				ScrollableResults scroll = query.scroll();
				
				List<InstructorFloorTimeDTO> resultList = new ArrayList<InstructorFloorTimeDTO>();
				
				while (scroll.next()) {
					Object[] objects = scroll.get();
					String locationID = (String) objects[0];
					String locationName = (String) objects[1];
					int instructorID = (int) objects[2];
					String firstName=(String) objects[3];
					String lastName=(String) objects[4];
					int duration=(int) objects[5];
					InstructorFloorTimeDTO dto = new InstructorFloorTimeDTO(locationID, locationName,instructorID, firstName,
							lastName,duration );
					resultList.add(dto);

				}        
				scroll.close();
           	  Log.info("InstructorFloorTimeCriterion.findInstructorFloorTime(), after retrieving DB values");
				
				return resultList;	 
   	 
   	 }
 	    };  
 	}
	public static Criterion<Instructor, InstructorFloorTimeDTO> getRecipientEmailIds() {
        return new InstructorFloorTimeCriterion<InstructorFloorTimeDTO>() {

            @Override
            public List<InstructorFloorTimeDTO> search(Session pSession) {            	
            	SQLQuery query = null;
				StringBuilder sb = new StringBuilder("select recipientId");  
				sb.append(" from EMAIL_LOOKUP");
				sb.append(" where description='Instructor Floor Time Report'");
					
				query = pSession.createSQLQuery(sb.toString());
				query.addScalar("RECIPIENTID", StandardBasicTypes.STRING);	        		
				
				ScrollableResults scroll = query.scroll();
				List<InstructorFloorTimeDTO> resultList = new ArrayList<InstructorFloorTimeDTO>();
				
				InstructorFloorTimeDTO dto = null;
				
				while (scroll.next()) {
					Object[] objects = scroll.get();
					String recipientId = (String) objects[0];
					dto = new InstructorFloorTimeDTO(recipientId);
					resultList.add(dto);

				}        
				scroll.close();
				return resultList;	   
        }
    };  
}

	
	
}
