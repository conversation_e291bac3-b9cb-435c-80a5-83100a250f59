/**
 * @Title: OnetimeCriterion.java
 * @Package com.guitarcenter.scheduler.dao.criterion
 * @Description: 
 * Copyright: Copyright (c) 2014 
 * Company:
 * 
 * <AUTHOR>
 * @date May 29, 2014 3:30:36 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_INSTRUCTOR;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.type.StandardBasicTypes;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dto.TimeLineDTO;
import com.guitarcenter.scheduler.model.Onetime;

/**
 * @ClassName: OnetimeCriterion
 * @Description: 
 * <AUTHOR>
 * @date May 29, 2014 3:30:36 PM
 *
 */
public abstract class OnetimeCriterion<E> extends AbstractCriterion<Onetime, E> implements Criterion<Onetime, E> {

	private static final Criterion<Onetime, Onetime> DEFAULT_INSTANCE = new OnetimeCriterion<Onetime>() {};
	
	/**
	  * OnetimeCriterion. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  */
	private OnetimeCriterion() {
		super(Onetime.class);
	}
	
	public static Criterion<Onetime, Onetime> getInstance() {
		return DEFAULT_INSTANCE;
	}

	/**
	  * <p>Title: search</p>
	  * <p>Description: </p>
	  * @param pSession
	  * @param pFetchMode
	  * @return
	  * @see com.guitarcenter.scheduler.dao.criterion.Criterion#search(org.hibernate.Session, int)
	  */
	@Override
	public List<E> search(Session pSession, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}

	/**
	  * <p>Title: get</p>
	  * <p>Description: </p>
	  * @param pSession
	  * @param pFetchMode
	  * @return
	  * @see com.guitarcenter.scheduler.dao.criterion.Criterion#get(org.hibernate.Session, int)
	  */
	@Override
	public E get(Session pSession, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}

	/**
	  * <p>Title: getFetchScript</p>
	  * <p>Description: </p>
	  * @param pFetchMode
	  * @return
	  * @see com.guitarcenter.scheduler.dao.criterion.AbstractCriterion#getFetchScript(int)
	  */
	@Override
	protected String getFetchScript(int pFetchMode) {
		StringBuilder sb = new StringBuilder();
		sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
        sb.append(addFetchHQL(FETCH_INSTRUCTOR, pFetchMode, "t.instructor", true));
		return sb.toString();
	}

    public static Criterion<Onetime, Onetime> findByInstructorsAndDate(final String pDate, final Long... pInstructorIds) {
        return new OnetimeCriterion<Onetime>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<Onetime> search(Session pSession, int pFetchMode) {
				if (pInstructorIds == null || pInstructorIds.length == 0) {
                    return Collections.emptyList();
                }
                if (!Pattern.matches("\\d{2}/\\d{2}/\\d{4}", pDate)){
                    return Collections.emptyList();
                }
                DateTime dateTime = DateTime.parse(pDate, DateTimeFormat.forPattern("MM/dd/yyyy"));
                String date = dateTime.toString("yyyy/MM/dd");
                Query query = pSession.createQuery(new StringBuilder(" from Onetime t ")
                        .append(getFetchScript(pFetchMode))
                        .append(" where t.instructor.instructorId in(:instructorIds) ")
                        .append("   and to_char(t.startTime,'YYYY/MM/DD') <= :queryDate")
                        .append("   and to_char(t.endTime,'YYYY/MM/DD') >= :queryDate")
                        .toString());
                query.setParameterList("instructorIds", pInstructorIds);
                query.setString("queryDate", date);
                return query.list();
            }
        };
    }

    public static Criterion<Onetime, TimeLineDTO> findByInstructorAndDateTime(final Long pInstructorId, final Date pDateTime) {
        return new OnetimeCriterion<TimeLineDTO>() {
            @Override
            @SuppressWarnings("unchecked")
            public List<TimeLineDTO> search(Session pSession, int pFetchMode) {
                DateTime dateTime = new DateTime(pDateTime);
                String sql = "select t3.instructor_id," +
                        "       to_char(min(t3.min_1), 'YYYY-MM-DD HH24:MI:SS') start_time," +
                        "       to_char(t3.max_1, 'YYYY-MM-DD HH24:MI:SS') end_time" +
                        "  from (select t2.instructor_id, t2.min_1, max(t2.max_1) max_1" +
                        "          from (select t1.*," +
                        "                       (select min(sub_t.start_time)" +
                        "                          from onetime sub_t" +
                        "                         where sub_t.instructor_id = :instructorId" +
                        "                           and (to_char(sub_t.start_time, 'YYYY-MM-DD') <=" +
                        "                               :queryDate and" +
                        "                               to_char(sub_t.end_time, 'YYYY-MM-DD') >=" +
                        "                               :queryDate)" +
                        "                           and ((sub_t.start_time <= t1.min_ and" +
                        "                               sub_t.end_time >= t1.min_) or" +
                        "                               (sub_t.start_time <= t1.max_ and" +
                        "                               sub_t.end_time >= t1.max_) or" +
                        "                               (sub_t.start_time >= t1.min_ and" +
                        "                               sub_t.end_time <= t1.max_))) min_1," +
                        "                       (select max(sub_t.end_time)" +
                        "                          from onetime sub_t" +
                        "                         where sub_t.instructor_id = :instructorId" +
                        "                           and (to_char(sub_t.start_time, 'YYYY-MM-DD') <=" +
                        "                               :queryDate and" +
                        "                               to_char(sub_t.end_time, 'YYYY-MM-DD') >=" +
                        "                               :queryDate)" +
                        "                           and ((sub_t.start_time <= t1.min_ and" +
                        "                               sub_t.end_time >= t1.min_) or" +
                        "                               (sub_t.start_time <= t1.max_ and" +
                        "                               sub_t.end_time >= t1.max_) or" +
                        "                               (sub_t.start_time >= t1.min_ and" +
                        "                               sub_t.end_time <= t1.max_))) max_1" +
                        "                  from (select t.*," +
                        "                               (select min(sub_t.start_time)" +
                        "                                  from onetime sub_t" +
                        "                                 where sub_t.instructor_id = :instructorId" +
                        "                                   and (to_char(sub_t.start_time, 'YYYY-MM-DD') <=" +
                        "                                       :queryDate and" +
                        "                                       to_char(sub_t.end_time, 'YYYY-MM-DD') >=" +
                        "                                       :queryDate)" +
                        "                                   and ((sub_t.start_time <= t.start_time and" +
                        "                                       sub_t.end_time >= t.start_time) or" +
                        "                                       (sub_t.start_time <= t.end_time and" +
                        "                                       sub_t.end_time >= t.end_time) or" +
                        "                                       (sub_t.start_time >= t.start_time and" +
                        "                                       sub_t.end_time <= t.end_time))) min_," +
                        "                               (select max(sub_t.end_time)" +
                        "                                  from onetime sub_t" +
                        "                                 where sub_t.instructor_id = :instructorId" +
                        "                                   and (to_char(sub_t.start_time, 'YYYY-MM-DD') <=" +
                        "                                       :queryDate and" +
                        "                                       to_char(sub_t.end_time, 'YYYY-MM-DD') >=" +
                        "                                       :queryDate)" +
                        "                                   and ((sub_t.start_time <= t.start_time and" +
                        "                                       sub_t.end_time >= t.start_time) or" +
                        "                                       (sub_t.start_time <= t.end_time and" +
                        "                                       sub_t.end_time >= t.end_time) or" +
                        "                                       (sub_t.start_time >= t.start_time and" +
                        "                                       sub_t.end_time <= t.end_time))) max_" +
                        "                          from onetime t" +
                        "                         where t.instructor_id = :instructorId" +
                        "                           and (to_char(t.start_time, 'YYYY-MM-DD') <=" +
                        "                               :queryDate and" +
                        "                               to_char(t.end_time, 'YYYY-MM-DD') >=" +
                        "                               :queryDate)) t1) t2" +
                        "         group by t2.instructor_id, t2.min_1) t3" +
                        " group by t3.instructor_id, t3.max_1";
                SQLQuery query = pSession.createSQLQuery(sql);
                query.setString("queryDate", dateTime.toString("yyyy-MM-dd"));
                query.setLong("instructorId", pInstructorId);
                query.addScalar("instructor_id", StandardBasicTypes.LONG);
                query.addScalar("start_time", StandardBasicTypes.STRING);
                query.addScalar("end_time", StandardBasicTypes.STRING);
                List<Object[]> list = query.list();
                List<TimeLineDTO> result = new ArrayList<TimeLineDTO>();
                if(null != list && !list.isEmpty()) {
                    DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN);
                    for(Object[] objs : list) {
                        String startTimeStr = (String)objs[1];
                        String endTimeStr = (String)objs[2];
                        DateTime startTime = DateTime.parse(startTimeStr, dateTimeFormatter);
                        DateTime endTime = DateTime.parse(endTimeStr, dateTimeFormatter);
                        result.add(new TimeLineDTO(startTime, endTime));
                    }
                }
                return result;
            }
        };
    }
}
