package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.List;

import org.hibernate.Query;
import org.hibernate.Session;

import com.guitarcenter.scheduler.model.CustomerStatus;

public abstract class CustomerStatusCriterion<E> extends AbstractCriterion<CustomerStatus, E> implements
		Criterion<CustomerStatus, E> {

	private static final Criterion<CustomerStatus, CustomerStatus>	DEFAULT_INSTANCE	= new CustomerStatusCriterion<CustomerStatus>() {
																						};



	private CustomerStatusCriterion() {
		super(CustomerStatus.class);
	}



	@Override
	protected String getFetchScript(int pFetchMode) {
		StringBuilder sb = new StringBuilder();
		sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
		sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
		return sb.toString();
	}



	@Override
	public List<E> search(Session pSession, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	@Override
	public E get(Session pSession, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	public static Criterion<CustomerStatus, CustomerStatus> getInstance() {
		return DEFAULT_INSTANCE;
	}
	
	/**
     * Returns a Criterion that can be used to find any existing customer status
     * records in the supplied site with a matching external id.
     * 
     * @param pSiteId site identifier
     * @param pExternalId String containing the external id to match
     * @return Criterion instance
     */
	public static Criterion<CustomerStatus, CustomerStatus> findByExternalId(final Long pSiteId, final String pExternalId) {
	    return new CustomerStatusCriterion<CustomerStatus>() {
	        @Override
	        @SuppressWarnings("unchecked")
	        public List<CustomerStatus> search(Session pSession, int pFetchMode) {
	            Query query = pSession.createQuery(new StringBuilder(" from CustomerStatus t ")
	                                                   .append(getFetchScript(pFetchMode))
	                                                   .append(" where coalesce(t.externalId, '_prospect_') = coalesce(:externalId, '_prospect_') ")
	                                                   .append("   and t.site.siteId = :siteId ")
	                                                   .toString());
	            query.setString("externalId", pExternalId);
	            query.setLong("siteId", pSiteId);
	            return query.list();
	        }
        };
	}
}
