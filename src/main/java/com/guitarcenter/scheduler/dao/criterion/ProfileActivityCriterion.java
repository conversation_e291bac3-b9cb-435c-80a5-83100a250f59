package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ACTIVITY;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION_PROFILE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.type.StandardBasicTypes;

import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.ProfileActivity;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.enums.Enabled;

public abstract class ProfileActivityCriterion<E> extends AbstractCriterion<ProfileActivity, E> implements
		Criterion<ProfileActivity, E> {

	private static final Criterion<ProfileActivity, ProfileActivity>	DEFAULT_INSTANCE	= new ProfileActivityCriterion<ProfileActivity>() {
																							};



	private ProfileActivityCriterion() {
		super(ProfileActivity.class);
	}



	@Override
	protected String getFetchScript(int pFetchMode) {
		StringBuilder sb = new StringBuilder();
		sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
		sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
		sb.append(addFetchHQL(FETCH_LOCATION_PROFILE, pFetchMode, "t.locationProfile", true));
		sb.append(addFetchHQL(FETCH_ACTIVITY, pFetchMode, "t.activity", true));
		return sb.toString();
	}



	@Override
	public List<E> search(Session pSession, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	@Override
	public E get(Session pSession, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	public static Criterion<ProfileActivity, ProfileActivity> getInstance() {
		return DEFAULT_INSTANCE;
	}



	public static Criterion<ProfileActivity, ProfileActivity> findByProfileId(final long pProfileId) {
		ProfileActivityCriterion<ProfileActivity> instance = new ProfileActivityCriterion<ProfileActivity>() {

			@Override
			@SuppressWarnings("unchecked")
			public List<ProfileActivity> search(Session pSession, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from ProfileActivity t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append(" left join fetch t.activity.service ");
				sb.append("where t.locationProfile.profileId = :profileId");
				sb.append(" order by t.activity.activityId ");
				Query query = pSession.createQuery(sb.toString());
				query.setLong("profileId", pProfileId);
				return query.list();
			}

		};
		return instance;
	}



	public static Criterion<ProfileActivity, ProfileActivity> findByProfileIdAndEnabled(final long pProfileId,
			final Enabled pEnabled) {
		ProfileActivityCriterion<ProfileActivity> instance = new ProfileActivityCriterion<ProfileActivity>() {

			@Override
			@SuppressWarnings("unchecked")
			public List<ProfileActivity> search(Session pSession, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from ProfileActivity t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append(" left join fetch t.activity.service ");
				sb.append("where t.locationProfile.profileId = :profileId");
				sb.append(" and t.enabled = :enabled");
				sb.append(" order by t.activity.activityId ");
				Query query = pSession.createQuery(sb.toString());
				query.setLong("profileId", pProfileId);
				query.setString("enabled", pEnabled.toString());
				return query.list();
			}

		};
		return instance;
	}



	public static Criterion<ProfileActivity, ProfileActivity> getByProfileIdAndActivityId(final long pProfileId,
			final long pActivityId) {
		ProfileActivityCriterion<ProfileActivity> instance = new ProfileActivityCriterion<ProfileActivity>() {

			@Override
			public ProfileActivity get(Session pSession, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from ProfileActivity t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append(" left join fetch t.activity.service ");
				sb.append("where t.locationProfile.profileId = :profileId");
				sb.append(" and t.activity.activityId = :activityId");
				sb.append(" order by t.activity.activityId ");
				Query query = pSession.createQuery(sb.toString());
				query.setLong("profileId", pProfileId);
				query.setLong("activityId", pActivityId);
				query.setMaxResults(1);
				return (ProfileActivity) query.uniqueResult();
			}

		};
		return instance;
	}



	public static Criterion<ProfileActivity, ProfileActivity> findByProfileIdAndServiceIdAndEnabled(
			final long pProfileId, final long pServiceId, final Enabled pEnabled) {
		ProfileActivityCriterion<ProfileActivity> instance = new ProfileActivityCriterion<ProfileActivity>() {

			@Override
			@SuppressWarnings("unchecked")
			public List<ProfileActivity> search(Session pSession, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from ProfileActivity t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append(" left join fetch t.activity.service ");
				sb.append("where t.locationProfile.profileId = :profileId");
				sb.append(" and t.activity.service.serviceId = :serviceId");
				sb.append(" and t.enabled = :enabled");
				sb.append(" order by t.activity.activityId ");
				Query query = pSession.createQuery(sb.toString());
				query.setLong("profileId", pProfileId);
				query.setLong("serviceId", pServiceId);
				query.setString("enabled", pEnabled.toString());
				return query.list();
			}

		};
		return instance;
	}



	public static Criterion<ProfileActivity, ProfileActivity> findByProfileIdAndServiceIds(final long pProfileId,
			final Long... pServiceIds) {
		ProfileActivityCriterion<ProfileActivity> instance = new ProfileActivityCriterion<ProfileActivity>() {

			@Override
			@SuppressWarnings("unchecked")
			public List<ProfileActivity> search(Session pSession, int pFetchMode) {
				if (pServiceIds.length == 0) {
					return Collections.emptyList();
				}
				StringBuilder sb = new StringBuilder("from ProfileActivity t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append(" left join fetch t.activity.service ");
				sb.append("where t.locationProfile.profileId = :profileId");
				sb.append(" and t.activity.service.serviceId in (:serviceIds)");
				sb.append(" and t.enabled = :enabled");
				sb.append(" order by t.activity.activityId ");
				Query query = pSession.createQuery(sb.toString());
				query.setLong("profileId", pProfileId);
				query.setParameterList("serviceIds", pServiceIds);
				query.setString("enabled", Enabled.Y.toString());
				return query.list();
			}

		};
		return instance;
	}



	public static Criterion<ProfileActivity, ProfileActivity> findByActivityIdAndSiteIdAndEnabled(
			final long pActivityId, final long pSiteId, final Enabled pEnabled) {
		ProfileActivityCriterion<ProfileActivity> instance = new ProfileActivityCriterion<ProfileActivity>() {

			@Override
			@SuppressWarnings("unchecked")
			public List<ProfileActivity> search(Session pSession, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from ProfileActivity t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append("where t.site.siteId = :siteId");
				sb.append(" and t.activity.activityId = :activityId");
				sb.append(" and t.enabled = :enabled");
				Query query = pSession.createQuery(sb.toString());
				query.setLong("siteId", pSiteId);
				query.setLong("activityId", pActivityId);
				query.setString("enabled", pEnabled.toString());
				return query.list();
			}

		};
		return instance;
	}



	public static Criterion<ProfileActivity, Boolean> hasActivityByActivityIds(final Long... pActivityIds) {
		ProfileActivityCriterion<Boolean> instance = new ProfileActivityCriterion<Boolean>() {

			@Override
			public Boolean get(Session pSession, int pFetchMode) {
				if (pActivityIds.length == 0) {
					return Boolean.FALSE;
				}
				StringBuilder sb = new StringBuilder("select count(t.profileActivityId) from ProfileActivity t ");
				sb.append(" where t.activity.activityId in(:activityIds)");
				Query query = pSession.createQuery(sb.toString());
				query.setParameterList("activityIds", pActivityIds);
				query.setMaxResults(1);
				Long result = (Long) query.uniqueResult();
				if (result > 0) {
					return Boolean.TRUE;
				}
				return Boolean.FALSE;
			}

		};
		return instance;
	}



	public static Criterion<ProfileActivity, Boolean> hasByProfileIdAndServiceIdAndEnabled(final long pProfileId,
			final long pServiceId, final Enabled pEnabled) {
		ProfileActivityCriterion<Boolean> instance = new ProfileActivityCriterion<Boolean>() {

			@Override
			public Boolean get(Session pSession, int pFetchMode) {
				StringBuilder sb = new StringBuilder("select count(t) from ProfileActivity t ");
				sb.append(" where t.locationProfile.profileId = :profileId");
				sb.append(" and t.activity.service.serviceId = :serviceId ");
				sb.append(" and t.enabled = :enabled");
				Query query = pSession.createQuery(sb.toString());
				query.setMaxResults(1);
				query.setLong("profileId", pProfileId);
				query.setLong("serviceId", pServiceId);
				query.setString("enabled", pEnabled.toString());
				Long count = (Long) query.uniqueResult();
				return count > 0 ? Boolean.TRUE : Boolean.FALSE;
			}

		};
		return instance;
	}



	public static Criterion<ProfileActivity, Boolean> hasBySiteIdAndActivityId(final long pSiteId,
			final long pActivityId) {
		ProfileActivityCriterion<Boolean> instance = new ProfileActivityCriterion<Boolean>() {

			@Override
			public Boolean get(Session pSession, int pFetchMode) {
				StringBuilder sb = new StringBuilder("select count(*) as numbers from profile_activity t");
				sb.append("   left join activity a");
				sb.append("     on t.activity_id = a.activity_id");
				sb.append("   left join profile_service ps");
				sb.append("     on a.service_id = ps.service_id");
				sb.append(" where t.site_id = :siteId");
				sb.append(" and t.activity_id = :activityId ");
				sb.append(" and ps.enabled = :enabled");
				SQLQuery query = pSession.createSQLQuery(sb.toString());
				query.setMaxResults(1);
				query.addScalar("numbers", StandardBasicTypes.LONG);
				query.setLong("siteId", pSiteId);
				query.setLong("activityId", pActivityId);
				query.setString("enabled", Enabled.N.toString());
				Long count = (Long) query.uniqueResult();
				return count > 0 ? Boolean.TRUE : Boolean.FALSE;
			}

		};
		return instance;
	}
	
	/**
	 * For gcss-578, Find the activity list by profile and service that is enabled
	 * 
	 * @param pProfileId
	 * @return
	 */
    @SuppressWarnings("unchecked")
	public static Criterion<ProfileActivity, ProfileActivity> findAcitivitiesEnabledServiceAndByProfile(final long pProfileId) {
    	ProfileActivityCriterion<ProfileActivity> instance = new ProfileActivityCriterion<ProfileActivity>() {
			@Override
			public List<ProfileActivity> search(Session pSession) {
				StringBuilder sb = new StringBuilder(" select {pa.*}, {a.*}, {s.*} from profile_activity pa, activity a, service s, profile_service ps ");
				sb.append(" where pa.activity_id = a.activity_id ");
				sb.append(" and a.service_id      = s.service_id ");
				sb.append(" and s.service_id = ps.service_id ");
				sb.append(" and ps.enabled='Y' ");
				/**
				 * For GCSS-597,add profile_id to filter out other profile_service records
				 */
				sb.append(" and ps.profile_id=pa.profile_id ");
				sb.append(" and pa.profile_id = :profileId ");
				sb.append(" order by a.activity_id asc ");
				SQLQuery query = pSession.createSQLQuery(sb.toString());
				query.setLong("profileId", pProfileId);
				query.addEntity("pa", ProfileActivity.class);
				query.addJoin("a", "pa.activity");
				query.addJoin("s", "a.service");
				
				List<ProfileActivity> result = new ArrayList<ProfileActivity>();
				
				List<Object[]> list = query.list();
 				for(Object[] o : list) {
					ProfileActivity pa = (ProfileActivity) o[0];
					Activity a = (Activity) o[1];
					Service s = (Service) o[2];
					a.setService(s);
					pa.setActivity(a);
					result.add(pa);
				}
				return result;
			}
		};
		return instance;
	}
}
