package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.List;

import org.hibernate.Query;
import org.hibernate.Session;

import com.guitarcenter.scheduler.model.Site;

public abstract class SiteCriterion<E> extends AbstractCriterion<Site, E> implements Criterion<Site, E> {

	private static final Criterion<Site, Site>	DEFAULT_INSTANCE	= new SiteCriterion<Site>() {
																	};



	private SiteCriterion() {
		super(Site.class);
	}



	@Override
	protected String getFetchScript(int pFetchMode) {
		StringBuilder sb = new StringBuilder();
		sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
		return sb.toString();
	}



	@Override
	public List<E> search(Session pSession, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	@Override
	public E get(Session pSession, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	public static Criterion<Site, Site> getInstance() {
		return DEFAULT_INSTANCE;
	}

	
	/**
	 * Returns a Criterion that can be used to find Site entities matching a
	 * supplied external id.
	 * 
	 * @param externalId String containing the external identifier to use for
	 *                   lookup
	 * @return a Criterion for lookup
	 */
	public static Criterion<Site, Site> findByExternalId(final String pExternalId) {
	    return new SiteCriterion<Site>() {
	        @Override
	        @SuppressWarnings("unchecked")
            public List<Site> search(Session pSession, int pFetchMode) {
	            Query query = pSession.createQuery(new StringBuilder("from Site t ")
	                                                   .append(getFetchScript(pFetchMode))
	                                                   .append(" where t.externalId = :externalId ")
	                                                   .toString());
	            query.setString("externalId", pExternalId);
	            return query.list();
	        }
	    };
	}
}
