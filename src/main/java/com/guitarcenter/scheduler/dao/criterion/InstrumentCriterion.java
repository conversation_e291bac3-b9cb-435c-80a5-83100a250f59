package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.List;

import org.hibernate.Query;
import org.hibernate.Session;

import com.guitarcenter.scheduler.model.Instrument;

public abstract class InstrumentCriterion<E> extends AbstractCriterion<Instrument, E> implements
		Criterion<Instrument, E> {

	private static final Criterion<Instrument, Instrument>	DEFAULT_INSTANCE	= new InstrumentCriterion<Instrument>() {
																				};



	private InstrumentCriterion() {
		super(Instrument.class);
	}



	@Override
	protected String getFetchScript(int pFetchMode) {
		StringBuilder sb = new StringBuilder();
		sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
		sb.append(addFetchHQL(FETCH_SITE, pFetchMode, "t.site", true));
		return sb.toString();
	}



	@Override
	public List<E> search(Session pSession, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	@Override
	public E get(Session pSession, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	public static Criterion<Instrument, Instrument> getInstance() {
		return DEFAULT_INSTANCE;
	}

	/**
     * Returns a Criterion that can be used to find any existing Instruments in
     * the supplied site with a matching external id.
     * 
     * @param pSiteId site identifier
     * @param pExternalId String containing the external id to match
     * @return Criterion instance
     */
	public static Criterion<Instrument, Instrument> findByExternalId(final Long pSiteId, final String pExternalId) {
	    return new InstrumentCriterion<Instrument>() {
	        @Override
	        @SuppressWarnings("unchecked")
	        public List<Instrument> search(Session pSession, int pFetchMode) {
	            Query query = pSession.createQuery(new StringBuilder(" from Instrument t ")
	                                                   .append(getFetchScript(pFetchMode))
	                                                   .append(" where t.externalId = :externalId ")
	                                                   .append("   and t.site.siteId = :siteId ")
	                                                   .toString());
	            query.setString("externalId", pExternalId);
	            query.setLong("siteId", pSiteId);
	            return query.list();
	        }
	    };
	}
}
