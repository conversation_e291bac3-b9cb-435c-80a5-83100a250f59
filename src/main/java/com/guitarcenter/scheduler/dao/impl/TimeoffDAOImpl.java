/**
 * @Title: TimeoffDAOImpl.java
 * @Package com.guitarcenter.scheduler.dao.impl
 * @Description: TODO
 * Copyright: Copyright (c) 2014 
 * Company:
 * 
 * <AUTHOR>
 * @date Mar 10, 2014 4:06:04 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_INSTRUCTOR;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.type.StandardBasicTypes;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.TimeoffDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.TimeoffCriterion;
import com.guitarcenter.scheduler.dto.TimeoffDateDTO;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Timeoff;

/**
 * @ClassName: TimeoffDAOImpl
 * @Description: TODO
 * <AUTHOR>
 * @date Mar 10, 2014 4:06:04 PM
 *
 */
@Repository("timeoffDAO")
public class TimeoffDAOImpl extends AbstractDAOImpl<Timeoff> implements TimeoffDAO {

	/**
	  * TimeoffDAOImpl. 
	  * <p>Title: </p>
	  * <p>Description: </p>
	  * @param pTClass
	  */
	public TimeoffDAOImpl() {
		super(Timeoff.class);
	}

	/**
	  * <p>Title: updateAuditor</p>
	  * <p>Description: </p>
	  * @param pT
	  * @param pUpdatedBy
	  * @see com.guitarcenter.scheduler.dao.impl.AbstractDAOImpl#updateAuditor(java.lang.Object, com.guitarcenter.scheduler.model.Person)
	  */
	@Override
	protected void updateAuditor(Timeoff pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}

	/**
	  * <p>Title: fetchOne</p>
	  * <p>Description: </p>
	  * @param pResult
	  * @param pFetchMode
	  * @see com.guitarcenter.scheduler.dao.impl.AbstractDAOImpl#fetchOne(java.lang.Object, int)
	  */
	@Override
	protected void fetchOne(Timeoff pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_INSTRUCTOR, pFetchMode, pResult.getInstructor());
	}

	/**
	  * <p>Title: fetchOne</p>
	  * <p>Description: </p>
	  * @param pCriteria
	  * @param pFetchMode
	  * @see com.guitarcenter.scheduler.dao.impl.AbstractDAOImpl#fetchOne(org.hibernate.Criteria, int)
	  */
	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_INSTRUCTOR, pFetchMode, "instructor", FetchMode.JOIN, pCriteria);
	}

	/**
	  * <p>Title: fetchMany</p>
	  * <p>Description: </p>
	  * @param pResult
	  * @param pFetchMode
	  * @see com.guitarcenter.scheduler.dao.impl.AbstractDAOImpl#fetchMany(java.lang.Object, int)
	  */
	@Override
	protected void fetchMany(Timeoff pResult, int pFetchMode) {
	}

	/**
	  * <p>Title: getCriterionInstance</p>
	  * <p>Description: </p>
	  * @return
	  * @see com.guitarcenter.scheduler.dao.impl.AbstractDAOImpl#getCriterionInstance()
	  */
	@Override
	protected Criterion<Timeoff, Timeoff> getCriterionInstance() {
		return TimeoffCriterion.getInstance();
	}

	/**
	  * <p>Title: getTimeoffByInstructorId</p>
	  * <p>Description: </p>
	  * @param instructorId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.TimeoffDAO#getTimeoffByInstructorId(long)
	  */
	@SuppressWarnings("unchecked")
	@Override
	public List<Timeoff> getTimeoffByInstructorId(long instructorId) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.createQuery("from Timeoff where trunc(end_time) >= trunc(sysdate) and instructor.instructorId = ? order by startTime asc");
		query.setParameter(0, instructorId);
		return query.list();
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<Timeoff> getDisplayTimeoffByInstructorId(long instructorId) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.createQuery("from Timeoff where trunc(end_time) >= trunc(sysdate) and instructor.instructorId = ? order by updated desc");
		query.setParameter(0, instructorId);
		return query.list();
	}
	
	@Override
	@SuppressWarnings("unchecked")
	public List<Timeoff> getTimeoffByDateInstructorId(long instructorId, String date) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.createQuery("from Timeoff where instructor.instructorId = ? and trunc(TO_TIMESTAMP_TZ(?, ?)) between trunc(start_time) and trunc(end_time)");
		query.setParameter(0, instructorId);
		query.setParameter(1, date);
		query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);
		return query.list();
	}

	/**
	  * <p>Title: checkTimeoffByTime</p>
	  * <p>Description: </p>
	  * @param startDate
	  * @param startTime
	  * @param endTime
	  * @param instructorId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.TimeoffDAO#checkTimeoffByTime(java.lang.String, java.lang.String, java.lang.String, long)
	 */
	@Override
	public boolean checkTimeoffByTime(String startDate, String startTime,
			String endTime, long instructorId) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.getNamedQuery("timeoff.getTimeoffByTime");
		query.setParameter(0, startDate+" "+startTime);
		query.setParameter(1, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(2, startDate+" "+endTime);
		query.setParameter(3, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(4, startDate+" "+startTime);
		query.setParameter(5, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(6, startDate+" "+endTime);
		query.setParameter(7, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(8, startDate+" "+startTime);
		query.setParameter(9, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(10, startDate+" "+endTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, instructorId);
		@SuppressWarnings("unchecked")
        List<Timeoff> l = query.list();
		return l.size()==0?true:false;
	}

	/**
	  * <p>Title: checkTimeoffByRecurringTime</p>
	  * <p>Description: </p>
	  * @param startDate
	  * @param startTime
	  * @param endTime
	  * @param instructorId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.TimeoffDAO#checkTimeoffByRecurringTime(java.util.List, java.lang.String, java.lang.String, long)
	 */
	@Override
	public boolean checkTimeoffByRecurringTime(List<String> startDate, String startTime, String endTime, long instructorId) {
		Session session = super.getSessionFactory().getCurrentSession();
		String sql = session.getNamedQuery("timeoff.getTimeoffByRecurringTime").getQueryString();
		StringBuilder tempScrpts = new StringBuilder();
		for (int i = 0; i < startDate.size(); i++) {
			tempScrpts.append(SystemUtil.REPLACE_TEMP_SCRIPTS.replace("&PARAMETER1", "'"+ startDate.get(i)+ " " + startTime+ "' ").replace("&PARAMETER2", "'"+ startDate.get(i) + " " + endTime+ "'"));
			if(i<startDate.size()-1){
				tempScrpts.append(" UNION ");
			}
		}
		Query query = session.createSQLQuery(sql.replace("&REPLACECONDITION", tempScrpts.toString()));
		query.setParameter(0, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(1, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(2, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(3, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(4, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(5, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(6, instructorId);
		Object o = query.uniqueResult();
		return Boolean.parseBoolean(o.toString());
	}

	/**
	  * getTimeoffDTOByAvailabilityTime
	  * 
	  *
	  * @Title: getTimeoffDTOByAvailabilityTime
	  * @Description: 
	  * @param @param date
	  * @param @param locationId
	  * @param @return
	  * @return List<TimeoffDateDTO>
	  * @throws
	 */
	@Override
	@SuppressWarnings("unchecked")
	public List<TimeoffDateDTO> getTimeoffDateDTOByAvailabilityTime(String date,
			long locationId) {
		List<TimeoffDateDTO> l = new ArrayList<TimeoffDateDTO>();
		Session session = super.getSessionFactory().getCurrentSession();
		String sql = session.getNamedQuery("timeoff.getTimeoffDTOByAvailabilityTime").getQueryString();
		SQLQuery query = session.createSQLQuery(sql);
		query.setString("date", date);
		query.setString("format", DateTimeUtil.DATE_PATTERN_SLASH);
		query.setLong("location", locationId);
		query.addScalar("start_time", StandardBasicTypes.DATE);
		query.addScalar("end_time", StandardBasicTypes.DATE);
		query.addScalar("instructor_id", StandardBasicTypes.LONG);
		List<Object[]> list = query.list();
		for (Object[] objects : list) {
			TimeoffDateDTO dto = new TimeoffDateDTO();
			dto.setStartTime((Date)objects[0]);
			dto.setEndTime((Date)objects[1]);
			dto.setInstructorId((Long)objects[2]);
			l.add(dto);
		}
		return l;
	}
}
