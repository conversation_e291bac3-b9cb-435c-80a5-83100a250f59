package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM_TYPE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;
import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.hibernate.Query;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.RoomSizeDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.RoomSizeCriterion;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.RoomSize;

@Repository("roomSizeDAO")
public class RoomSizeDAOImpl extends AbstractDAOImpl<RoomSize> implements RoomSizeDAO {

	public RoomSizeDAOImpl() {
		super(RoomSize.class);
	}



	@Override
	protected void updateAuditor(RoomSize pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(RoomSize pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_ROOM_TYPE, pFetchMode, pResult.getRoomType());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_ROOM_TYPE, pFetchMode, "roomType", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_SITE, pFetchMode, "site", FetchMode.JOIN, pCriteria);
	}



	@Override
	protected void fetchMany(RoomSize pResult, int pFetchMode) {
	}



	@Override
	protected Criterion<RoomSize, RoomSize> getCriterionInstance() {
		return RoomSizeCriterion.getInstance();
	}



	/*
	  * <p>Title: getRoomSizeList</p>
	  * <p>Description: </p>
	  * @param siteId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.RoomSizeDAO#getRoomSizeList(long)
	  */
	@SuppressWarnings("unchecked")
    @Override
	public List<RoomSize> getRoomSizeList(long siteId) {
		String hql = "from RoomSize where site.siteId = ?";
		Query q = super.getSessionFactory().getCurrentSession().createQuery(hql);
		q.setParameter(0, siteId);
		return q.list();
	}
}
