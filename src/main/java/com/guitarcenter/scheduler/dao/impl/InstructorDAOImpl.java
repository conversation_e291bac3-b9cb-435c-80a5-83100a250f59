package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_AVAILABILITY;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_MORE_ACTIVITIES;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.InstructorCriterion;
import com.guitarcenter.scheduler.dto.ProfileTimeOffDTO;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileTimeoff;
import com.guitarcenter.scheduler.model.enums.Canceled;
import com.guitarcenter.scheduler.model.enums.Enabled;

@Repository("instructorDAO")
public class InstructorDAOImpl extends AbstractDAOImpl<Instructor> implements InstructorDAO {

	public InstructorDAOImpl() {
		super(Instructor.class);
	}


	@Override
	protected void updateAuditor(Instructor pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(Instructor pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_AVAILABILITY, pFetchMode, pResult.getAvailability());
		addFetchCriteria(FETCH_LOCATION, pFetchMode, pResult.getLocation());
		addFetchCriteria(FETCH_PERSON, pFetchMode, pResult.getPerson());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_AVAILABILITY, pFetchMode, "availability", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_LOCATION, pFetchMode, "location", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_PERSON, pFetchMode, "person", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_SITE, pFetchMode, "site", FetchMode.JOIN, pCriteria);
	}



	@Override
	protected void fetchMany(Instructor pResult, int pFetchMode) {
		addFetchCriteria(FETCH_MORE_ACTIVITIES, pFetchMode, pResult.getActivities());
	}



	@Override
	protected Criterion<Instructor, Instructor> getCriterionInstance() {
		return InstructorCriterion.getInstance();
	}
	
	@Override
	public Instructor getInstructorByTime(long instructorId, String startDate,
			String startTime, String endTime) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.getNamedQuery("instructor.checkInstructorTime");
		query.setParameter(0, startDate);
		query.setParameter(1, DateTimeUtil.DATE_PATTERN_SLASH);
		//steve 20130912 the date format only check HH24:MI
		query.setParameter(2, startTime);
		query.setParameter(3, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(4, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(5, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(6, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(7, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(8, endTime);
		query.setParameter(9, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(10, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(12, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(13, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(14, instructorId);
		@SuppressWarnings("unchecked")
        List<Instructor> l = query.list();
		return l.size()==0?null:l.get(0);
	}
	/*
	  * <p>Title: checkInstructorByAppointmentTime</p>
	  * <p>Description: </p>
	  * @param instructorId
	  * @param startDate
	  * @param startTime
	  * @param endTime
	  * @return
	  * @see com.guitarcenter.scheduler.dao.InstructorDAO#checkInstructorByAppointmentTime(long, java.lang.String, java.lang.String, java.lang.String)
	  */
	@Override
	public Instructor checkInstructorByAppointmentTime(long instructorId,
			String startDate, String startTime, String endTime) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.getNamedQuery("instructor.checkInstructorAppointmentTime");
		query.setParameter(0, startDate+" "+startTime);
		query.setParameter(1, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(2, startDate+" "+endTime);
		query.setParameter(3, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(4, startDate+" "+startTime);
		query.setParameter(5, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(6, startDate+" "+endTime);
		query.setParameter(7, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(8, startDate+" "+startTime);
		query.setParameter(9, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(10, startDate+" "+endTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, Canceled.N.toString());
		query.setParameter(13, Canceled.H.toString());
		query.setParameter(14, instructorId);
		@SuppressWarnings("unchecked")
        List<Instructor> l = query.list();
		return l.size()==0?null:l.get(0);
	}



	/*
	  * <p>Title: checkInstructorByAppointmentRecurringTime</p>
	  * <p>Description: </p>
	  * @param instructorId
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @return
	  * @see com.guitarcenter.scheduler.dao.InstructorDAO#checkInstructorByAppointmentRecurringTime(long, java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	  */
	@Override
	public Instructor checkInstructorByAppointmentRecurringTime(
			long instructorId, String startDate, String endDate,
			String startTime, String endTime) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.getNamedQuery("instructor.checkInstructorAppointmentRecurringTime");
		query.setParameter(0, startDate);
		query.setParameter(1, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(3, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(4, startDate);
		query.setParameter(5, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(6, endDate);
		query.setParameter(7, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(8, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(9, startTime);
		query.setParameter(10, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(11, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(12, endTime);
		query.setParameter(13, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(14, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(15, startTime);
		query.setParameter(16, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(17, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(18, endTime);
		query.setParameter(19, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(20, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(21, startTime);
		query.setParameter(22, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(23, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(24, endTime);
		query.setParameter(25, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(26, Canceled.N.toString());
		query.setParameter(27, Canceled.H.toString());
		query.setParameter(28, instructorId);
		@SuppressWarnings("unchecked")
        List<Instructor> l = query.list();
		return l.size()==0?null:l.get(0);
	}



	@Override
	public boolean checkUpdateInstructorByAppointmentTime(long instructorId,
			String startDate, String startTime, String endTime,
			String excludeAppointmentIdParam) {
		Session session = super.getSessionFactory().getCurrentSession();
		String queryString = session.getNamedQuery("instructor.checkUpdateInstructorAppointmentTime").getQueryString();
		String replaceParameter = SystemUtil.REPLACE_CONDITION.replace("&PARAMETER", excludeAppointmentIdParam);
		String sql = excludeAppointmentIdParam.length()==0?queryString.replace("&REPLACECONDITION", ""):queryString.replace("&REPLACECONDITION", replaceParameter);
		Query query = session.createSQLQuery(sql);
		query.setParameter(0, startDate+" "+startTime);
		query.setParameter(1, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(2, startDate+" "+endTime);
		query.setParameter(3, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(4, startDate+" "+startTime);
		query.setParameter(5, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(6, startDate+" "+endTime);
		query.setParameter(7, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(8, startDate+" "+startTime);
		query.setParameter(9, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(10, startDate+" "+endTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, Canceled.N.toString());
		query.setParameter(13, Canceled.H.toString());
		query.setParameter(14, instructorId);
		Object o = query.uniqueResult();
		return Boolean.parseBoolean(o.toString());
	}



	@Override
	public boolean checkUpdateInstructorByAppointmentRecurringTime(
			long instructorId, String startDate, String endDate,
			String startTime, String endTime, String excludeAppointmentIdParam) {
		Session session = super.getSessionFactory().getCurrentSession();
		String queryString = session.getNamedQuery("instructor.checkUpdateInstructorAppointmentRecurringTime").getQueryString();
		String replaceParameter = SystemUtil.REPLACE_CONDITION.replace("&PARAMETER", excludeAppointmentIdParam);
		String sql = excludeAppointmentIdParam.length()==0?queryString.replace("&REPLACECONDITION", ""):queryString.replace("&REPLACECONDITION", replaceParameter);
		Query query = session.createSQLQuery(sql);
		query.setParameter(0, startDate);
		query.setParameter(1, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(3, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(4, startDate);
		query.setParameter(5, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(6, endDate);
		query.setParameter(7, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(8, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(9, startTime);
		query.setParameter(10, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(11, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(12, endTime);
		query.setParameter(13, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(14, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(15, startTime);
		query.setParameter(16, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(17, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(18, endTime);
		query.setParameter(19, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(20, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(21, startTime);
		query.setParameter(22, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(23, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(24, endTime);
		query.setParameter(25, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(26, Canceled.N.toString());
		query.setParameter(27, Canceled.H.toString());
		query.setParameter(28, instructorId);
		Object o = query.uniqueResult();
		return Boolean.parseBoolean(o.toString());
	}

	@Override
	public boolean checkInstructorByProfileActivityId(long instructorId,
			long activityId, long profileId) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.getNamedQuery("instructor.checkInstructorByProfileActivityId");
		query.setParameter(0, instructorId);
		query.setParameter(1, activityId);
		query.setParameter(2, profileId);
		query.setParameter(3, Enabled.Y.toString());
		Object o = query.uniqueResult();
		return Boolean.parseBoolean(o.toString());
	}
	
	//GCSS-590
	@Override
	public Instructor checkInstructorByAppointmentTime(long instructorId,
			String startDate, String endDate, String startTime, String endTime) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.getNamedQuery("instructor.checkInstructorAppointmentTime");
		query.setParameter(0, startDate+" "+startTime);
		query.setParameter(1, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(2, endDate+" "+endTime);
		query.setParameter(3, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(4, startDate+" "+startTime);
		query.setParameter(5, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(6, endDate+" "+endTime);
		query.setParameter(7, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(8, startDate+" "+startTime);
		query.setParameter(9, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(10, endDate+" "+endTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, Canceled.N.toString());
		query.setParameter(13, Canceled.H.toString());
		query.setParameter(14, instructorId);
		@SuppressWarnings("unchecked")
        List<Instructor> l = query.list();
		return l.size()==0?null:l.get(0);
	}
	
	//---GSSP Instructor Mode update changes
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void saveOrUpdateInstructor(Instructor pT,Person pUpdatedBy) {

		Session session = getSessionFactory().getCurrentSession();
		updateAuditor(pT, pUpdatedBy);
		session.saveOrUpdate(pT);
 
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public List<Instructor> getInstructorsAffectRecently(String updateDate){
			Session session = super.getSessionFactory().getCurrentSession();
			 //###########################
			Calendar c = Calendar.getInstance();
			c.add(Calendar.DATE, -1);
			Date yesterday01= c.getTime();
			c.add(Calendar.DATE, 31);
			Date after_30day= c.getTime();
			DateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
			String yesterday = dateFormatter.format(yesterday01 );
			String after30day = dateFormatter.format(after_30day );

			//#########################################
			
			StringBuffer sb = new StringBuffer("select  TO_CHAR(instructor_id) from ((select distinct INSTRUCTOR_ID from appointment where  to_char(START_TIME, 'YYYY-MM-DD') <= ? and  to_char(START_TIME, 'YYYY-MM-DD') >= ? and   to_char(UPDATED, 'YYYY-MM-DD HH24:MI') >= ?) ");
					sb.append(" union (select distinct INSTRUCTOR_ID from Availability av,Instructor ins where av.AVAILABILITY_ID = ins.AVAILABILITY_ID and to_char(av.UPDATED, 'YYYY-MM-DD HH24:MI') >= ?)");
					sb.append(" union (select distinct INSTRUCTOR_ID from Instructor where to_char(UPDATED, 'YYYY-MM-DD HH24:MI') >= ?) )");
 
			SQLQuery query = session.createSQLQuery(sb.toString());
			List<Instructor> profList= new ArrayList<Instructor>();
			query.setParameter(0, after30day);
			query.setParameter(1, yesterday);
			query.setParameter(2, updateDate);
			query.setParameter(3, updateDate);
			query.setParameter(4, updateDate);
			@SuppressWarnings("unchecked")
			List<String> list = query.list();
			for (String row : list) {
				Instructor profDTO = new Instructor();
				if(null!= row) {
					profDTO.setInstructorId(Long.parseLong(row));
					profList.add(profDTO);
				
				}
			}
			
			return profList;
		}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public List<Instructor> getFullInstructorsAffectRecently(String updateDate){
			Session session = super.getSessionFactory().getCurrentSession();
			 //###########################
			Calendar c = Calendar.getInstance();
			c.add(Calendar.DATE, -1);
			Date yesterday01= c.getTime();
			c.add(Calendar.DATE, 31);
			Date after_30day= c.getTime();
			DateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
			String yesterday = dateFormatter.format(yesterday01 );
			String after30day = dateFormatter.format(after_30day );

			//#########################################
			
			StringBuffer sb = new StringBuffer("select  TO_CHAR(instructor_id) from ((select distinct INSTRUCTOR_ID from appointment where  to_char(START_TIME, 'YYYY-MM-DD') <= ? and  to_char(START_TIME, 'YYYY-MM-DD') >= ? and   to_char(UPDATED, 'YYYY-MM-DD HH24:MI') >= ?) ");
					sb.append(" union (select distinct INSTRUCTOR_ID from Availability av,Instructor ins where av.AVAILABILITY_ID = ins.AVAILABILITY_ID and to_char(av.UPDATED, 'YYYY-MM-DD HH24:MI') >= ?)");
					sb.append(" union (select distinct INSTRUCTOR_ID from Instructor where to_char(UPDATED, 'YYYY-MM-DD HH24:MI') >= ?) )");
 
			SQLQuery query = session.createSQLQuery(sb.toString());
			List<Instructor> profList= new ArrayList<Instructor>();
			query.setParameter(0, after30day);
			query.setParameter(1, yesterday);
			query.setParameter(2, updateDate);
			query.setParameter(3, updateDate);
			query.setParameter(4, updateDate);
			@SuppressWarnings("unchecked")
			List<String> list = query.list();
			for (String row : list) {
				Instructor profDTO = new Instructor();
				if(null!= row) {
					profDTO.setInstructorId(Long.parseLong(row));
					profList.add(profDTO);
				
				}
			}
			
			return profList;
		}
}
