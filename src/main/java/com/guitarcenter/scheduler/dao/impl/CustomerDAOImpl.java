package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_CUSTOMER_STATUS;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_MORE_INSTRUMENTS;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.CustomerDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.CustomerCriterion;
import com.guitarcenter.scheduler.dao.util.PGPUtils;
import com.guitarcenter.scheduler.dto.CustomerDetailDTO;
import com.guitarcenter.scheduler.dto.ProfileTimeOffDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.ParentDetails;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.enums.Canceled;

@Repository("customerDAO")
public class CustomerDAOImpl extends AbstractDAOImpl<Customer> implements CustomerDAO {

	public CustomerDAOImpl() {
		super(Customer.class);
	}



	@Override
	protected void updateAuditor(Customer pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(Customer pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_CUSTOMER_STATUS, pFetchMode, pResult.getCustomerStatus());
		addFetchCriteria(FETCH_PERSON, pFetchMode, pResult.getPerson());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_CUSTOMER_STATUS, pFetchMode, "customerStatus", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_PERSON, pFetchMode, "person", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_SITE, pFetchMode, "site", FetchMode.JOIN, pCriteria);
	}



	@Override
	protected void fetchMany(Customer pResult, int pFetchMode) {
		addFetchCriteria(FETCH_MORE_INSTRUMENTS, pFetchMode, pResult.getInstruments());
	}



	@Override
	protected Criterion<Customer, Customer> getCriterionInstance() {
		return CustomerCriterion.getInstance();
	}



	/**
	 * 
	  * <p>Title: checkCustomerByAppointmentTime</p>
	  * <p>Description: </p>
	  * @param customerId
	  * @param startDate
	  * @param startTime
	  * @param endTime
	  * @param profileId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.CustomerDAO#checkCustomerByAppointmentTime(long, java.lang.String, java.lang.String, java.lang.String, long)
	 */
	@Override
	public boolean checkCustomerByAppointmentTime(long customerId,
			String startDate, String startTime, String endTime, long profileId) {
		Session session = super.getSessionFactory().getCurrentSession();
		String sql = session.getNamedQuery("customer.checkCustomerAppointmentTime").getQueryString();
		Query query = session.createSQLQuery(sql);
		query.setParameter(0, startDate+" "+startTime);
		query.setParameter(1, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(2, startDate+" "+endTime);
		query.setParameter(3, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(4, startDate+" "+startTime);
		query.setParameter(5, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(6, startDate+" "+endTime);
		query.setParameter(7, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(8, startDate+" "+startTime);
		query.setParameter(9, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(10, startDate+" "+endTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, profileId);
		query.setParameter(13, Canceled.N.toString());
		query.setParameter(14, Canceled.H.toString());
		query.setParameter(15, customerId);
		Object o = query.uniqueResult();
		return Boolean.parseBoolean(o.toString());
	}



	/**
	 * 
	  * <p>Title: checkCustomerByAppointmentRecurringTime</p>
	  * <p>Description: </p>
	  * @param customerId
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @param profileId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.CustomerDAO#checkCustomerByAppointmentRecurringTime(long, java.lang.String, java.lang.String, java.lang.String, java.lang.String, long)
	 */
	@Override
	public boolean checkCustomerByAppointmentRecurringTime(
			long customerId, String startDate, String endDate,
			String startTime, String endTime, long profileId) {
		Session session = super.getSessionFactory().getCurrentSession();
		String sql = session.getNamedQuery("customer.checkCustomerAppointmentRecurringTime").getQueryString();
		Query query = session.createSQLQuery(sql);
		query.setParameter(0, startDate);
		query.setParameter(1, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(3, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(4, startDate);
		query.setParameter(5, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(6, endDate);
		query.setParameter(7, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(8, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(9, startTime);
		query.setParameter(10, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(11, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(12, endTime);
		query.setParameter(13, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(14, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(15, startTime);
		query.setParameter(16, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(17, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(18, endTime);
		query.setParameter(19, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(20, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(21, startTime);
		query.setParameter(22, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(23, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(24, endTime);
		query.setParameter(25, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(26, profileId);
		query.setParameter(27, Canceled.N.toString());
		query.setParameter(28, Canceled.H.toString());
		query.setParameter(29, customerId);
		Object o = query.uniqueResult();
		return Boolean.parseBoolean(o.toString());
	}



	/**
	 * 
	  * checkUpdateCustomerByAppointmentTime
	  * TODO
	  *
	  * @Title: checkUpdateCustomerByAppointmentTime
	  * @Description: TODO
	  * @param @param customerIdParam
	  * @param @param customerId
	  * @param @param startTime
	  * @param @param endTime
	  * @param @param profileId
	  * @param @param excludeAppointmentIdParam
	  * @param @return
	  * @return boolean
	  * @throws
	 */
	@Override
	public boolean checkUpdateCustomerByAppointmentTime(long customerId,
			String startDate, String startTime, String endTime, long profileId,
			String excludeAppointmentIdParam) {
		Session session = super.getSessionFactory().getCurrentSession();
		String sql = session.getNamedQuery("customer.checkUpdateCustomerAppointmentTime").getQueryString();
		String replaceParameter = SystemUtil.REPLACE_CONDITION.replace("&PARAMETER", excludeAppointmentIdParam);
		Query query = session.createSQLQuery(excludeAppointmentIdParam.length()==0?sql.replace("&REPLACECONDITION", ""):sql.replace("&REPLACECONDITION", replaceParameter));
		query.setParameter(0, startDate+" "+startTime);
		query.setParameter(1, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(2, startDate+" "+endTime);
		query.setParameter(3, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(4, startDate+" "+startTime);
		query.setParameter(5, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(6, startDate+" "+endTime);
		query.setParameter(7, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(8, startDate+" "+startTime);
		query.setParameter(9, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(10, startDate+" "+endTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, profileId);
		query.setParameter(13, Canceled.N.toString());
		query.setParameter(14, Canceled.H.toString());
		query.setParameter(15, customerId);
		Object o = query.uniqueResult();
		return Boolean.parseBoolean(o.toString());
	}



	/**
	 * 
	  * <p>Title: checkUpdateCustomerByAppointmentRecurringTime</p>
	  * <p>Description: </p>
	  * @param customerId
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @param profileId
	  * @param excludeAppointmentIdParam
	  * @return
	  * @see com.guitarcenter.scheduler.dao.CustomerDAO#checkUpdateCustomerByAppointmentRecurringTime(long, java.lang.String, java.lang.String, java.lang.String, java.lang.String, long, java.lang.String)
	 */
	@Override
	public boolean checkUpdateCustomerByAppointmentRecurringTime(
			long customerId, String startDate, String endDate,
			String startTime, String endTime, long profileId,
			String excludeAppointmentIdParam) {
		Session session = super.getSessionFactory().getCurrentSession();
		String sql = session.getNamedQuery("customer.checkUpdateCustomerAppointmentRecurringTime").getQueryString();
		String replaceParameter = SystemUtil.REPLACE_CONDITION.replace("&PARAMETER", excludeAppointmentIdParam);
		Query query = session.createSQLQuery(excludeAppointmentIdParam.length()==0?sql.replace("&REPLACECONDITION", ""):sql.replace("&REPLACECONDITION", replaceParameter));
		query.setParameter(0, startDate);
		query.setParameter(1, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(3, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(4, startDate);
		query.setParameter(5, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(6, endDate);
		query.setParameter(7, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(8, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(9, startTime);
		query.setParameter(10, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(11, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(12, endTime);
		query.setParameter(13, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(14, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(15, startTime);
		query.setParameter(16, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(17, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(18, endTime);
		query.setParameter(19, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(20, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(21, startTime);
		query.setParameter(22, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(23, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(24, endTime);
		query.setParameter(25, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(26, profileId);
		query.setParameter(27, Canceled.N.toString());
		query.setParameter(28, Canceled.H.toString());
		query.setParameter(29, customerId);
		Object o = query.uniqueResult();
		return Boolean.parseBoolean(o.toString());
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public ParentDetails  getParentIdFromCustomerTable(Long customerId) {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("select c.PARENT_id  PARENT_id,pd.version version from PARENT_DETAILs pd,customer c where pd.PARENT_id = c.PARENT_id and c.customer_id = ? ");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, customerId);
		Long parentId = null;
		//Object o = query.uniqueResult();
		@SuppressWarnings("unchecked")
		List<Object[]> list = query.list();
		ParentDetails pd= new ParentDetails();

	/*	for (Object row : list) {
			if(row !=null){	
				Long ab=Long.valueOf(row+"");
				parentId = ab;
			}
 
		}*/
		//@SuppressWarnings("unchecked")
		//List<Object[]> list = query.list();
		//ProfileTimeOffDTO profDTO = new ProfileTimeOffDTO();
		if(list != null){
			Stream<Object[]> lm = list.stream().limit(1);
	         lm.forEach(row->{
	        	// System.out.println("row[0]   "+row[0]);
	        	// System.out.println("row[1]   "+row[1]);
	        	 if(row[0]!=null)pd.setParentId(Long.valueOf(row[0]+""));
	        	 pd.setVersion(Long.valueOf(row[1]+""));  
	         });
		}
			//System.out.println("getParentId   "+pd.getParentId());
			//System.out.println("getVersion  "+pd.getVersion());
		return pd;
	}
	

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public CustomerDetailDTO  getCustomerDetailsById(Long customerId) {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("SELECT   c.parent_id parent_id,p.FIRST_NAME,p.LAST_NAME,p.EMAIL,pd.SECONDARY_EMAIL,pd.FULL_NAME,p.PHONE, c.EXTERNAL_ID customer_ext_id, cs.EXTERNAL_ID status, c.LESSON_COUNT,c.customer_id,pd.version version FROM  person p JOIN customer c  ON p.person_id = c.person_id left JOIN parent_details pd ON pd.parent_id = c.parent_id left join CUSTOMER_STATUS cs on cs.CUSTOMER_STATUS_ID = c.CUSTOMER_STATUS_ID where  c.customer_id = ? ");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, customerId);
		Long parentId = null;
		//Object o = query.uniqueResult();
		@SuppressWarnings("unchecked")
		List<Object[]> list = query.list();
		CustomerDetailDTO pd= new CustomerDetailDTO();

		if(list != null){
			Stream<Object[]> lm = list.stream().limit(1);
	         lm.forEach(row->{
	        	 pd.setCustomerFullName(row[1]+" "+row[2]); 
	        	 pd.setEmail(PGPUtils.setNullToEmpty(row[3])); 
	        	 pd.setSecondaryEmail(PGPUtils.setNullToEmpty(row[4])); 
	        	 pd.setPhoneNumber(PGPUtils.setNullToEmpty(row[6])); 			        	 
	        	 pd.setCustomerExternalId(PGPUtils.setNullToEmpty(row[7])); 
	        	 pd.setCustomerStatus(PGPUtils.setNullToEmpty(row[8]));
	        	 if(row[9] !=null)  	 pd.setLessonsCount(Integer.valueOf(row[9]+""));
	        	 if(row[9] ==null)  	 pd.setLessonsCount(0);
	        	 pd.setGcId(Long.valueOf(row[10]+""));
	        	 
	         });
		}
		return pd;
	}
	
			
			@Override
			@Transactional(propagation = Propagation.REQUIRED)
			public void saveOrUpdateInstructor(Customer ct) {

				Session session = getSessionFactory().getCurrentSession();
				session.saveOrUpdate(ct);
		 
			}
			
			@Override
			@Transactional(propagation = Propagation.REQUIRED)
			public Customer  getCustomerById(Long customerId){
				Session session = super.getSessionFactory().getCurrentSession();
				Customer customer1 = (Customer)session.get(Customer.class, customerId); 
				return customer1;
			}
}
