package com.guitarcenter.scheduler.dao.impl;

import java.util.Date;

import org.hibernate.Criteria;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.CustomerEmailDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.CustomerEmail;
import com.guitarcenter.scheduler.model.Person;


//Code Added for Phase 2 - Get Lesson Series

@Repository("customerEmailDAO")
public class CustomerEmailDAOImpl extends AbstractDAOImpl<CustomerEmail> implements CustomerEmailDAO {

	
	public CustomerEmailDAOImpl() {
		super(CustomerEmail.class);
	}

	@Override
	protected void updateAuditor(CustomerEmail pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}

	@Override
	protected void fetchOne(CustomerEmail pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchMany(CustomerEmail pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected Criterion<CustomerEmail, CustomerEmail> getCriterionInstance() {
		// TODO Auto-generated method stub
		return null;
	}
}
