package com.guitarcenter.scheduler.dao.impl;

import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Example;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.AbstractDAO;
import com.guitarcenter.scheduler.dao.criterion.AbstractCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.util.HibernateAwareObjectMapper;
import com.guitarcenter.scheduler.model.Person;

abstract class AbstractDAOImpl<T> implements AbstractDAO<T> {

	private static final Logger			LOGGER	= LoggerFactory.getLogger(AbstractDAOImpl.class);

	@Autowired
	@Qualifier("sessionFactory")
	private SessionFactory				mSessionFactory;
	private final Class<T>				mTClass;

	@Autowired
	@Qualifier("JSONMapper")
	private HibernateAwareObjectMapper	mObjectMapper;



	public SessionFactory getSessionFactory() {
		return mSessionFactory;
	}



	public Class<T> getTClass() {
		return mTClass;
	}



	protected AbstractDAOImpl(final Class<T> pTClass) {
		super();
		mTClass = pTClass;
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public long save(T pT, Person pUpdatedBy) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pT.getClass().getSimpleName(),
					mObjectMapper.toJSON(pT), pUpdatedBy };
			LOGGER.debug("{}.save({} pT, Person pUpdatedBy) : entry, pT is [{}] pUpdatedBy is [{}]", objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		updateAuditor(pT, pUpdatedBy);
		long id = (Long) session.save(pT);

		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pT.getClass().getSimpleName() };
			LOGGER.debug("{}.save({} pT, Person pUpdatedBy) : exit", objects);
		}
		return id;
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public long save(T pT, long pUpdatedById) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pT.getClass().getSimpleName(),
					mObjectMapper.toJSON(pT), pUpdatedById };
			LOGGER.debug("{}.save({} pT, long pUpdatedById) : entry, pT is [{}] pUpdatedById is [{}]", objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		updateAuditor(session, pT, pUpdatedById);
		long id = (Long) session.save(pT);
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pT.getClass().getSimpleName() };
			LOGGER.debug("{}.save({} pT, long pUpdatedById) : exit", objects);
		}
		return id;
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void update(T pT, Person pUpdatedBy) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pT.getClass().getSimpleName(),
					mObjectMapper.toJSON(pT), pUpdatedBy };
			LOGGER.debug("{}.update({} pT, Person pUpdatedBy) : entry, pT is [{}] pUpdatedBy is [{}]", objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		updateAuditor(pT, pUpdatedBy);
		session.update(pT);
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pT.getClass().getSimpleName() };
			LOGGER.debug("{}.update({} pT, Person pUpdatedBy) : exit", objects);
		}
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void update(T pT, long pUpdatedById) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pT.getClass().getSimpleName(),
					mObjectMapper.toJSON(pT), pUpdatedById };
			LOGGER.debug("{}.update({} pT, long pUpdatedById) : entry, pT is [{}] pUpdatedById is [{}]", objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		updateAuditor(session, pT, pUpdatedById);
		session.update(pT);
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pT.getClass().getSimpleName() };
			LOGGER.debug("{}.update({} pT, long pUpdatedById) : exit", objects);
		}
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	@SuppressWarnings("unchecked")
	public T get(long pId) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pId };
			LOGGER.debug("{}.get(long pId) : entry, pId is [{}]", objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		T t = (T) session.get(getTClass(), pId);
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), t };
			LOGGER.debug("{}.get(long pId) : returning [{}]", objects);
		}
		return t;
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void delete(T pT) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pT.getClass().getSimpleName(),
					mObjectMapper.toJSON(pT) };
			LOGGER.debug("{}.delete({} pT) : entry, pT is [{}]", objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		session.delete(pT);
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pT.getClass().getSimpleName() };
			LOGGER.debug("{}.delete({} pT) : exit", objects);
		}
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	@SuppressWarnings("unchecked")
	public T merge(T pT) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pT.getClass().getSimpleName(),
					mObjectMapper.toJSON(pT) };
			LOGGER.debug("{}.merge({} pT) : entry, pT is [{}]", objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		T t = (T) session.merge(pT);
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pT.getClass().getSimpleName(), t };
			LOGGER.debug("{}.merge({} pT) : returning [{}]", objects);
		}
		return t;
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public <E> E get(Criterion<T, E> pCriterion) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pCriterion };
			LOGGER.debug("{}.get(Criterion<T, E> pCriterion) : entry, pCriterion is [{}]", objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		E t = pCriterion.get(session);
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), t };
			LOGGER.debug("{}.get(Criterion<T, E> pCriterion) : returning [{}]", objects);
		}
		return t;
	}



	@Override
	@SuppressWarnings("unchecked")
	@Transactional(propagation = Propagation.REQUIRED)
	public T get(T pExample) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pExample.getClass().getSimpleName(),
					mObjectMapper.toJSON(pExample) };
			LOGGER.debug("{}.get({} pExample) : entry, pExample is [{}]", objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		Criteria criteria = session.createCriteria(getTClass());
		criteria.add(Example.create(pExample));
		T t = (T) criteria.uniqueResult();
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pExample.getClass().getSimpleName(), t };
			LOGGER.debug("{}.get({} pExample) : returning [{}]", objects);
		}
		return t;
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public <E> List<E> search(Criterion<T, E> pCriterion) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pCriterion };
			LOGGER.debug("{}.search(Criterion<T, E> pCriterion) : entry, pCriterion is [{}]", objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		List<E> t = pCriterion.search(session);
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), t, t.size() };
			LOGGER.debug("{}.search(Criterion<T, E> pCriterion) : returning [{}] size is [{}]", objects);
		}
		return t;
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public List<T> searchAll() {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName() };
			LOGGER.debug("{}.searchAll() : entry", objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		Criterion<T, T> criterion = AbstractCriterion.getAbstractInstance(getTClass());
		List<T> t = criterion.searchAll(session);
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), t, t.size() };
			LOGGER.debug("{}.searchAll() : returning [{}] size is [{}]", objects);
		}
		return t;
	}



	@Override
	@SuppressWarnings("unchecked")
	@Transactional(propagation = Propagation.REQUIRED)
	public List<T> search(T pExample) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pExample.getClass().getSimpleName(),
					mObjectMapper.toJSON(pExample) };
			LOGGER.debug("{}.search({} pExample) : entry, pExample is [{}]", objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		Criteria criteria = session.createCriteria(getTClass());
		criteria.add(Example.create(pExample));
		List<T> t = criteria.list();
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pExample.getClass().getSimpleName(), t, t.size() };
			LOGGER.debug("{}.search({} pExample) : returning [{}] size is [{}]", objects);
		}
		return t;
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public T get(long pId, int pFetchMode) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pId, pFetchMode };
			LOGGER.debug("{}.get(long pId, int pFetchMode) : entry, pId is [{}] pFetchMode is [{}]", objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		@SuppressWarnings("unchecked")
		T result = (T) session.get(getTClass(), pId);
		if (null != result && pFetchMode > 0) {
			fetchOne(result, pFetchMode);
			fetchMany(result, pFetchMode);
		}
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), result };
			LOGGER.debug("{}.get(long pId, int pFetchMode) : returning [{}]", objects);
		}
		return result;
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public T merge(T pT, int pFetchMode) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pT.getClass().getSimpleName(),
					mObjectMapper.toJSON(pT), pFetchMode };
			LOGGER.debug("{}.merge({} pT, int pFetchMode) : entry, pT is [{}] pFetchMode is [{}]", objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		@SuppressWarnings("unchecked")
		T merge = (T) session.merge(pT);
		if (null != merge && pFetchMode > 0) {
			fetchOne(merge, pFetchMode);
			fetchMany(merge, pFetchMode);
		}
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pT.getClass().getSimpleName(), merge };
			LOGGER.debug("{}.merge({} pT, int pFetchMode) : returning [{}]", objects);
		}
		return merge;
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public <E> E get(Criterion<T, E> pCriterion, int pFetchMode) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pCriterion, pFetchMode };
			LOGGER.debug(
					"{}.get(Criterion<T, E> pCriterion, int pFetchMode) : entry, pCriterion is [{}] pFetchMode is [{}]",
					objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		E obj = pCriterion.get(session, pFetchMode);
		if (null != obj && pFetchMode > 0) {
			if (!getTClass().isAssignableFrom(obj.getClass())) {
				return obj;
			}
			@SuppressWarnings("unchecked")
			T t = (T) obj;
			fetchMany(t, pFetchMode);
		}
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), obj };
			LOGGER.debug("{}.get(Criterion<T, E> pCriterion, int pFetchMode) : returning [{}]", objects);
		}
		return obj;
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public <E> List<E> search(Criterion<T, E> pCriterion, int pFetchMode) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pCriterion, pFetchMode };
			LOGGER.debug(
					"{}.search(Criterion<T, E> pCriterion, int pFetchMode) : entry, pCriterion is [{}] pFetchMode is [{}]",
					objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		List<E> list = pCriterion.search(session, pFetchMode);
		
		if (!list.isEmpty() && pFetchMode > 0) {
			E e = list.get(0);
			if (!getTClass().isAssignableFrom(e.getClass())) {
				return list;
			}
			@SuppressWarnings("unchecked")
			List<T> foreachList = (List<T>) list;
			for (T t : foreachList) {
				fetchMany(t, pFetchMode);
			}
		}
		
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), list, list.size() };
			LOGGER.debug("{}.search(Criterion<T, E> pCriterion, int pFetchMode) : returning [{}] size is [{}]", objects);
		}
		return list;
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public List<T> searchAll(int pFetchMode) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pFetchMode };
			LOGGER.debug("{}.searchAll(int pFetchMode) : entry, pFetchMode is [{}]", objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		Criterion<T, T> criterion = getCriterionInstance();
		List<T> list = criterion.searchAll(session, pFetchMode);
		if (!list.isEmpty() && pFetchMode > 0) {
			for (T t : list) {
				fetchMany(t, pFetchMode);
			}
		}
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), list, list.size() };
			LOGGER.debug("{}.searchAll(int pFetchMode) : returning [{}] size is [{}]", objects);
		}
		return list;
	}



	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	@SuppressWarnings("unchecked")
	public List<T> search(T pExample, int pFetchMode) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pExample.getClass().getSimpleName(),
					mObjectMapper.toJSON(pExample), pFetchMode };
			LOGGER.debug("{}.search({} pExample, int pFetchMode) : entry, pExample is [{}] pFetchMode is [{}]", objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		Criteria criteria = session.createCriteria(getTClass());
		criteria.add(Example.create(pExample));
		if (pFetchMode > 0) {
			fetchOne(criteria, pFetchMode);
		}
		List<T> list = criteria.list();
		if (!list.isEmpty() && pFetchMode > 0) {
			for (T t : list) {
				fetchMany(t, pFetchMode);
			}
		}
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pExample.getClass().getSimpleName(), list,
					list.size() };
			LOGGER.debug("{}.search({} pExample, int pFetchMode) : returning [{}] size is [{}]", objects);
		}
		return list;
	}



	@Override
	@SuppressWarnings("unchecked")
	@Transactional(propagation = Propagation.REQUIRED)
	public T get(T pExample, int pFetchMode) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pExample.getClass().getSimpleName(),
					mObjectMapper.toJSON(pExample), pFetchMode };
			LOGGER.debug("{}.get({} pExample, int pFetchMode) : entry, pExample is [{}] pFetchMode is [{}]", objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		Criteria criteria = session.createCriteria(getTClass());
		criteria.add(Example.create(pExample));
		if (pFetchMode > 0) {
			fetchOne(criteria, pFetchMode);
		}
		T t = (T) criteria.uniqueResult();
		if (null != t && pFetchMode > 0) {
			fetchMany(t, pFetchMode);
		}
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pExample.getClass().getSimpleName(), t };
			LOGGER.debug("{}.get({} pExample, int pFetchMode) : returning [{}]", objects);
		}
		return t;
	}



	private void updateAuditor(Session pSession, T pT, long pUpdatedById) {
		Person updatedBy = new Person();
		updatedBy.setPersonId(pUpdatedById);
		updateAuditor(pT, updatedBy);
	};



	protected abstract void updateAuditor(T pT, Person pUpdatedBy);



	protected abstract void fetchOne(T pResult, int pFetchMode);



	protected abstract void fetchOne(Criteria pCriteria, int pFetchMode);



	protected abstract void fetchMany(T pResult, int pFetchMode);



	protected abstract Criterion<T, T> getCriterionInstance();

}
