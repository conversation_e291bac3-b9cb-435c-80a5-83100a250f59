package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION_PROFILE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.LocationDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.LocationCriterion;
import com.guitarcenter.scheduler.dto.LocationDistRegDTO;
import com.guitarcenter.scheduler.dto.ProfileTimeOffDTO;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.Person;

@Repository("locationDAO")
public class LocationDAOImpl extends AbstractDAOImpl<Location> implements LocationDAO {

	public LocationDAOImpl() {
		super(Location.class);
	}



	@Override
	protected void updateAuditor(Location pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(Location pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_LOCATION_PROFILE, pFetchMode, pResult.getLocationProfile());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_LOCATION_PROFILE, pFetchMode, "locationProfile", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_SITE, pFetchMode, "site", FetchMode.JOIN, pCriteria);
	}



	@Override
	protected void fetchMany(Location pResult, int pFetchMode) {
	}



	@Override
	protected Criterion<Location, Location> getCriterionInstance() {
		return LocationCriterion.getInstance();
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public LocationDistRegDTO  getLocationDistrictReg(String extrenalId) {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("select  to_char(l.LOCATION_ID) LOCATION_ID,l.EXTERNAL_ID,l.LOCATION_NAME,to_char(l.PROFILE_ID) PROFILE_ID,l.STATE,l.CITY,lr.LOCATION_DST_DESC,lr.LOCATION_REG_DESC from  ");
		sb.append("  LOCATION l,LOCATION_DST_REG lr where lr.LOCATION_ID = l.LOCATION_ID  and l.external_id = ? ");
 
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, extrenalId);
		 
		@SuppressWarnings("unchecked")
		List<Object[]> list = query.list();
		LocationDistRegDTO profDTO = new LocationDistRegDTO();
		
		Stream<Object[]> lm = list.stream().limit(1);
        lm.forEach(row->{
			profDTO.setLocationId((String) row[0]);
			profDTO.setExternalId((String) row[1]);
			profDTO.setLocationName((String) row[2]);
			profDTO.setProfileId((String) row[3]);
			profDTO.setState((String) row[4]);
			profDTO.setCity((String) row[5]);
			profDTO.setLocDistrict((String) row[6]);
			profDTO.setLocRegin((String) row[7]);
        });
		return profDTO;
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public LocationDistRegDTO  getLocationDistrictReg(long locationId) {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("select  to_char(l.LOCATION_ID) LOCATION_ID,l.EXTERNAL_ID,l.LOCATION_NAME,to_char(l.PROFILE_ID) PROFILE_ID,l.STATE,l.CITY,lr.LOCATION_DST_DESC,lr.LOCATION_REG_DESC from  ");
		sb.append("  LOCATION l,LOCATION_DST_REG lr where lr.LOCATION_ID = l.LOCATION_ID  and l.location_id = ? ");
 
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, locationId);
		 
		@SuppressWarnings("unchecked")
		List<Object[]> list = query.list();
		LocationDistRegDTO profDTO = new LocationDistRegDTO();
		
		Stream<Object[]> lm = list.stream().limit(1);
        lm.forEach(row->{
			profDTO.setLocationId((String) row[0]);
			profDTO.setExternalId((String) row[1]);
			profDTO.setLocationName((String) row[2]);
			profDTO.setProfileId((String) row[3]);
			profDTO.setState((String) row[4]);
			profDTO.setCity((String) row[5]);
			profDTO.setLocDistrict((String) row[6]);
			profDTO.setLocRegin((String) row[7]);
        });
		return profDTO;
	}
}
