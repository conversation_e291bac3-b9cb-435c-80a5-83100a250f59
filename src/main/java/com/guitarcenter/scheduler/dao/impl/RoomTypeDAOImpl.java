package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;
import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.hibernate.Query;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.RoomTypeDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.RoomTypeCriterion;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.RoomType;

@Repository("roomTypeDAO")
public class RoomTypeDAOImpl extends AbstractDAOImpl<RoomType> implements RoomTypeDAO {

	public RoomTypeDAOImpl() {
		super(RoomType.class);
	}



	@Override
	protected void updateAuditor(RoomType pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(RoomType pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_SITE, pFetchMode, "site", FetchMode.JOIN, pCriteria);
	}



	@Override
	protected void fetchMany(RoomType pResult, int pFetchMode) {
	}



	@Override
	protected Criterion<RoomType, RoomType> getCriterionInstance() {
		return RoomTypeCriterion.getInstance();
	}



	/**
	  * <p>Title: getRoomTypeList</p>
	  * <p>Description: </p>
	  * @param siteId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.RoomTypeDAO#getRoomTypeList(long)
	  */
	@SuppressWarnings("unchecked")
    @Override
	public List<RoomType> getRoomTypeList(long siteId) {
		String hql = "from RoomType where site.siteId = ?";
		Query q = super.getSessionFactory().getCurrentSession().createQuery(hql);
		q.setParameter(0, siteId);
		return q.list();
	}
}
