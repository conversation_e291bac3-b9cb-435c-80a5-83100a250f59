package com.guitarcenter.scheduler.dao.impl;

import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

import org.hibernate.Criteria;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.ParentDetailsDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.ParentDetails;
import com.guitarcenter.scheduler.model.Person;

@Repository("parentDetailsDAO")
public class ParentDetailsDAOImpl extends AbstractDAOImpl<ParentDetails> implements ParentDetailsDAO {

	public ParentDetailsDAOImpl() {
		super(ParentDetails.class);
	}

	@Override
	protected void updateAuditor(ParentDetails pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}


	@Override
	protected void fetchOne(ParentDetails pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchMany(ParentDetails pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected Criterion<ParentDetails, ParentDetails> getCriterionInstance() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public ParentDetails  getParentDetailsFromCustomerTable(Long customerId) {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("select c.PARENT_id  PARENT_id,pd.full_name full_name, pd.version version from PARENT_DETAILS pd,customer c where pd.PARENT_id = c.PARENT_id and c.customer_id = ? ");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, customerId);
		@SuppressWarnings("unchecked")
		List<Object[]> list = query.list();
		ParentDetails pd= new ParentDetails();

		if(list != null){
			Stream<Object[]> lm = list.stream().limit(1);
	         lm.forEach(row->{
	        	// System.out.println("row[0]   "+row[0]);
	        	// System.out.println("row[1]   "+row[1]);
	        	 if(row[0]!=null)pd.setParentId(Long.valueOf(row[0]+""));
	        	 if(row[1]!=null)pd.setFullName(String.valueOf(row[1])+"");
	        	 pd.setVersion(Long.valueOf(row[2]+""));  
	         });
		}
			
		return pd;
	}
 
}
