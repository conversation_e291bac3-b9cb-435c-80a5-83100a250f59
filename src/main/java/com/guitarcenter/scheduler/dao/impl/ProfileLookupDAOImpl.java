package com.guitarcenter.scheduler.dao.impl;

import org.hibernate.Criteria;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.ProfileLookupDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileLookup;

@Repository("profileLookupDAO")
public class ProfileLookupDAOImpl extends AbstractDAOImpl<ProfileLookup> implements ProfileLookupDAO {

	public ProfileLookupDAOImpl() {
		super(ProfileLookup.class);
	}

	



	@Override
	protected void fetchOne(ProfileLookup pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}



	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}



	@Override
	protected void fetchMany(ProfileLookup pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}



	@Override
	protected Criterion<ProfileLookup, ProfileLookup> getCriterionInstance() {
		// TODO Auto-generated method stub
		return null;
	}





	@Override
	protected void updateAuditor(ProfileLookup pT, Person pUpdatedBy) {
		// TODO Auto-generated method stub
		
	}






	}