package com.guitarcenter.scheduler.dao.impl;

import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.InstoreAvailabilityDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.Onetime;
import com.guitarcenter.scheduler.model.InstoreAvailability;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileTimeoff;

@Repository("instoreAvailabilityDAO")
public class InstoreAvailabilityDAOImpl extends AbstractDAOImpl<InstoreAvailability> implements InstoreAvailabilityDAO {
 
	public InstoreAvailabilityDAOImpl() {
		super(InstoreAvailability.class);
	}

	@Override
	public List<InstoreAvailability> getInstoreAvailabilityByInstructorId(long instructorId) {

		Session session = super.getSessionFactory().getCurrentSession();;
		Criteria crit = session.createCriteria(InstoreAvailability.class);
		crit.add(Restrictions.eq("instructor.instructorId",instructorId));
		List<InstoreAvailability> list = crit.list();
		return list;
	}

	@Override
	public List<InstoreAvailability> getDisplayInstoreAvailabilityeByInstructorId(long instructorId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public boolean checkInstoreAvailabilityByProfileId(String startDate, String startTime, String endTime,
			long profileId) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public boolean checkInstoreAvailabilityByTime(String startDate, String startTime, String endDate, String endTime,
			long instructorId) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public List<Onetime> getInstoreAvailabilityByTime(String startDate, String startTime, String endTime,
			long instructorId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void saveOrUpdate(InstoreAvailability pT, Person pUpdatedBy) {
		Session session = getSessionFactory().getCurrentSession();
		updateAuditor(pT, pUpdatedBy);
		session.saveOrUpdate(pT);
	}

	@Override
	protected void updateAuditor(InstoreAvailability pT, Person pUpdatedBy) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchOne(InstoreAvailability pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchMany(InstoreAvailability pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected Criterion<InstoreAvailability, InstoreAvailability> getCriterionInstance() {
		// TODO Auto-generated method stub
		return null;
	}

	 

}
