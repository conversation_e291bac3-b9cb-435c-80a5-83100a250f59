package com.guitarcenter.scheduler.dao.impl;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.AppointmentTransactionsDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.util.HibernateAwareObjectMapper;
import com.guitarcenter.scheduler.model.AppointmentTransactions;
import com.guitarcenter.scheduler.model.Person;

@Repository("appointmentTransactionsDAO")
public class AppointmentTransactionsDAOImpl extends AbstractDAOImpl<AppointmentTransactions> implements AppointmentTransactionsDAO {

	public AppointmentTransactionsDAOImpl() {
		super(AppointmentTransactions.class);
	}
	
	private static final Logger			LOGGER	= LoggerFactory.getLogger(AbstractDAOImpl.class);

	@Autowired
	@Qualifier("JSONMapper")
	private HibernateAwareObjectMapper	mObjectMapper;
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void saveEntity(AppointmentTransactions pT, Person pUpdatedBy) {
		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pT.getClass().getSimpleName(),
					mObjectMapper.toJSON(pT), pUpdatedBy };
			LOGGER.debug("{}.save({} pT, Person pUpdatedBy) : entry, pT is [{}] pUpdatedBy is [{}]", objects);
		}
		Session session = getSessionFactory().getCurrentSession();
		updateAuditor(pT, pUpdatedBy);
		session.save(pT);

		if (LOGGER.isDebugEnabled()) {
			Object[] objects = { this.getClass().getSimpleName(), pT.getClass().getSimpleName() };
			LOGGER.debug("{}.save({} pT, Person pUpdatedBy) : exit", objects);
		}
		//return id;
	}
	@Override
	protected void updateAuditor(AppointmentTransactions pT, Person pUpdatedBy) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchOne(AppointmentTransactions pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchMany(AppointmentTransactions pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected Criterion<AppointmentTransactions, AppointmentTransactions> getCriterionInstance() {
		// TODO Auto-generated method stub
		return null;
	}

	
}
