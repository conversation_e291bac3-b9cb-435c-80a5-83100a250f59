package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ACTIVITY;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_APPOINTMENT_SERIES;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_INSTRUCTOR;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION_PROFILE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_MORE_CUSTOMERS;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;
import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.AppointmentCancelReason;
import com.guitarcenter.scheduler.model.AppointmentSeries;
import com.guitarcenter.scheduler.model.AppointmentTransactions;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.enums.Canceled;

@Repository("appointmentDAO")
public class AppointmentDAOImpl extends AbstractDAOImpl<Appointment> implements AppointmentDAO {

	public AppointmentDAOImpl() {
		super(Appointment.class);
	}



	@Override
	protected void updateAuditor(Appointment pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(Appointment pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_ACTIVITY, pFetchMode, pResult.getActivity());
		addFetchCriteria(FETCH_APPOINTMENT_SERIES, pFetchMode, pResult.getAppointmentSeries());
		addFetchCriteria(FETCH_INSTRUCTOR, pFetchMode, pResult.getInstructor());
		addFetchCriteria(FETCH_LOCATION_PROFILE, pFetchMode, pResult.getLocationProfile());
		addFetchCriteria(FETCH_ROOM, pFetchMode, pResult.getRoom());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_ACTIVITY, pFetchMode, "activity", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_APPOINTMENT_SERIES, pFetchMode, "appointmentSeries", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_INSTRUCTOR, pFetchMode, "instructor", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_LOCATION_PROFILE, pFetchMode, "locationProfile", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_ROOM, pFetchMode, "room", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_SITE, pFetchMode, "site", FetchMode.JOIN, pCriteria);
	}



	@Override
	protected void fetchMany(Appointment pResult, int pFetchMode) {
		addFetchCriteria(FETCH_MORE_CUSTOMERS, pFetchMode, pResult.getCustomers());
	}



	@Override
	protected Criterion<Appointment, Appointment> getCriterionInstance() {
		return AppointmentCriterion.getInstance();
	}



	/**
	  * <p>Title: checkAppointmentByRoomActivity</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @param activityId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.AppointmentDAO#checkAppointmentByRoomActivity(long, long)
	  */
	@Override
	public boolean checkAppointmentByRoomActivity(long roomId, long activityId, long profileId) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.getNamedQuery("appointment.getAppointmentByRoomActivityProfile");
		query.setParameter(0, SystemUtil.VALIDATION_SCOPE_MONTH);
		query.setParameter(1, roomId);
		query.setParameter(2, activityId);
		query.setParameter(3, profileId);
		
		//Changes made for GSSP-151 
		query.setParameter(4, 'N');
		query.setParameter(5, 'H');
		
		return query.list().size()==0?true:false;
	}



	/**
	  * <p>Title: checkAppointmentByRoom</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.AppointmentDAO#checkAppointmentByRoom(long)
	  */
	@Override
	public boolean checkAppointmentByRoom(long roomId, long profileId) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.getNamedQuery("appointment.getAppointmentByRoom");
		query.setParameter(0, SystemUtil.VALIDATION_SCOPE_MONTH);
		query.setParameter(1, roomId);
		query.setParameter(2, profileId);
		//Changes made for GSSP-151
		query.setParameter(3, 'N');
		query.setParameter(4, 'H');
		return query.list().size()==0?true:false;
	}



	/**
	  * <p>Title: checkAppointmentByProfile</p>
	  * <p>Description: </p>
	  * @param profileId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.AppointmentDAO#checkAppointmentByProfile(long)
	  */
	@Override
	public boolean checkAppointmentByProfile(long profileId) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.getNamedQuery("appointment.getAppointmentByProfile");
		query.setParameter(0, SystemUtil.VALIDATION_SCOPE_MONTH);
		query.setParameter(1, profileId);
		query.setParameter(2, Canceled.N.toString());
		query.setParameter(3, Canceled.H.toString());
		return query.list().size()==0?true:false;
	}



	/**
	  * <p>Title: checkStartTime</p>
	  * <p>Description: </p>
	  * @param startDate
	  * @param startTime
	  * @return
	  * @see com.guitarcenter.scheduler.dao.AppointmentDAO#checkStartTime(java.lang.String, java.lang.String)
	  */
	@Override
	public boolean checkStartTime(String startDate, String startTime) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.getNamedQuery("common.checkStartTime");
		query.setParameter(0, startDate+" "+startTime);
		query.setParameter(1, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		Object o = query.uniqueResult();
		return Boolean.parseBoolean(o.toString());
	}



	/**
	  * <p>Title: checkAppointmentByRoom</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.AppointmentDAO#checkAppointmentByRoom(long)
	  */
	@Override
	public boolean checkAppointmentByRoom(long roomId) {
		Session session = super.getSessionFactory().getCurrentSession();
		//Changes made for GSSP-151
		Query query = session.createQuery("from Appointment where room.roomId = ? and canceled in ('N','H') ");
		query.setParameter(0, roomId);
	
		return query.list().size()==0?true:false;
	}



	/**
	  * <p>Title: checkAppointmentByRoomTemplateIdActivity</p>
	  * <p>Description: </p>
	  * @param roomTemplateId
	  * @param activityId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.AppointmentDAO#checkAppointmentByRoomTemplateIdActivity(long, long)
	  */
	@Override
	public boolean checkAppointmentByRoomTemplateIdActivity(long roomTemplateId, long activityId) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.getNamedQuery("appointment.getAppointmentByRoomTemplateActivity");
		query.setParameter(0, SystemUtil.VALIDATION_SCOPE_MONTH);
		query.setParameter(1, roomTemplateId);
		query.setParameter(2, activityId);
		return query.list().size()==0?true:false;
	}
	
	/**
	 * <p>Title: checkAppointmentByActivityId</p>
	 * <p>Description: </p>
	 * @param activityId
	 * @return
	 * @see com.guitarcenter.scheduler.dao.AppointmentDAO#checkAppointmentByActivityId(long)
	 */
	@Override
	@Transactional
	public boolean checkAppointmentByActivityId(long activityId) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.createQuery("from Appointment where activity.activityId=?");
		query.setParameter(0, activityId);
		@SuppressWarnings("unchecked")
		List<Appointment> list = query.list();
		return list.size()>0?true:false;
	}



	/**
	 * <p>Title: checkAppointmentByServiceId</p>
	 * <p>Description: </p>
	 * @param serviceId
	 * @return
	 * @see com.guitarcenter.scheduler.dao.AppointmentDAO#checkAppointmentByServiceId(long)
	 */
	@Override
	@Transactional
	public boolean checkAppointmentByServiceId(long serviceId) {
		Session session = super.getSessionFactory().getCurrentSession();
		SQLQuery query = session.createSQLQuery("select 1 from appointment a, activity at where a.activity_id = at.activity_id and at.service_id = ?");
		query.setParameter(0, serviceId);
		@SuppressWarnings("unchecked")
		List<Object[]> list = query.list();
		return list.size()>0?true:false;
	}



	/**
	 * <p>Title: checkAppointmentByProfileIdServiceId</p>
	 * <p>Description: </p>
	 * @param profileId
	 * @param serviceId
	 * @return
	 * @see com.guitarcenter.scheduler.dao.AppointmentDAO#checkAppointmentByProfileIdServiceId(long, long)
	 */
	@Override
	public boolean checkAppointmentByProfileIdServiceId(long profileId,
			long serviceId) {
		Session session = super.getSessionFactory().getCurrentSession();
		SQLQuery query = session.createSQLQuery("select 1 from appointment a, activity at where a.activity_id = at.activity_id and at.service_id = ? and a.profile_id=?");
		query.setParameter(0, serviceId);
		query.setParameter(1, profileId);
		@SuppressWarnings("unchecked")
		List<Object[]> list = query.list();
		return list.size()>0?true:false;
	}



	/**
	 * <p>Title: checkAppointmentByProfileIdActivityId</p>
	 * <p>Description: </p>
	 * @param profileId
	 * @param activityId
	 * @return
	 * @see com.guitarcenter.scheduler.dao.AppointmentDAO#checkAppointmentByProfileIdActivityId(long, long)
	 */
	@Override
	public boolean checkAppointmentByProfileIdActivityId(long profileId,
			long activityId) {
		Session session = super.getSessionFactory().getCurrentSession();
		SQLQuery query = session.createSQLQuery("select 1 from appointment a where a.activity_id = ? and a.profile_id=?");
		query.setParameter(0, activityId);
		query.setParameter(1, profileId);
		@SuppressWarnings("unchecked")
		List<Object[]> list = query.list();
		return list.size()>0?true:false;
	}
	
	
	 //GSSP-250 changes
	@Override
	@SuppressWarnings("unchecked")
		public List<AppointmentCancelReason> getCancelReason(String isRecuring) {
		
			Session session = super.getSessionFactory().getCurrentSession();
			String hql = "from AppointmentCancelReason where isRecuring in (?,'B') and enabled in ('Y') ";
			Query q = session.createQuery(hql);
			q.setParameter(0, isRecuring);
			return q.list();
		}
	//GSSP-278 changes.
	/*@Override
	@SuppressWarnings("unchecked")
	public List<AppointmentCancelReason> getCancelReasonCode(long cancleReasonId) {
		
		Session session= super.getSessionFactory().getCurrentSession();
				Criteria criteria = session.createCriteria(AppointmentCancelReason.class);
				criteria.add(Restrictions.eq("appointmentcancelreasonID",cancleReasonId));
					
				return criteria.list();
		}*/
	
	@SuppressWarnings("unchecked")
	@Override
	@Transactional
	public  List<Appointment> findAppointmentbyId(Long appointmentId) {
		
		Session session = super.getSessionFactory().getCurrentSession();
		List<Appointment> list = null;
			Criteria crit = session.createCriteria(Appointment.class);
			crit.add(Restrictions.eq("appointmentId",appointmentId));
			list = crit.list();
 
		return list;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	@Transactional
	public  List<Appointment> findHoldAppointments(Date startTime) {
		
		Session session = super.getSessionFactory().getCurrentSession();
		List<Appointment> list = null;
			Criteria crit = session.createCriteria(Appointment.class);
			crit.add(Restrictions.eq("canceled",Canceled.H));
			crit.add(Restrictions.lt("createTime",startTime));
			list = crit.list();
 
		return list;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	@Transactional
	public  List<AppointmentTransactions> findHoldAppointmentIdByTransactionId(String transcationId) {
		
		Session session = super.getSessionFactory().getCurrentSession();
		List<AppointmentTransactions> list = null;
			Criteria crit = session.createCriteria(AppointmentTransactions.class);
			crit.add(Restrictions.eq("transactionId",transcationId));
			//crit.add(Restrictions.lt("createTime",startTime));
			list = crit.list();
 
		return list;
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void saveOrUpdate(Appointment pT) {

		Session session = getSessionFactory().getCurrentSession();
		session.saveOrUpdate(pT);
 
	}
}