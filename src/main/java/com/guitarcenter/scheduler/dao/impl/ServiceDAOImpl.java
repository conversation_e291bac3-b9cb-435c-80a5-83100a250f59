package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.ServiceDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.ServiceCriterion;
import com.guitarcenter.scheduler.dto.ServiceDTO;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.enums.Enabled;

@Repository("serviceDAO")
public class ServiceDAOImpl extends AbstractDAOImpl<Service> implements ServiceDAO {

	public ServiceDAOImpl() {
		super(Service.class);
	}



	@Override
	protected void updateAuditor(Service pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(Service pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_SITE, pFetchMode, "site", FetchMode.JOIN, pCriteria);
	}



	@Override
	protected void fetchMany(Service pResult, int pFetchMode) {
	}



	@Override
	protected Criterion<Service, Service> getCriterionInstance() {
		return ServiceCriterion.getInstance();
	}



	/**
	  * <p>Title: getServiceListBySiteId</p>
	  * <p>Description: </p>
	  * @param siteId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.ServiceDAO#getServiceListBySiteId(long)
	  */
	@SuppressWarnings("unchecked")
    @Override
	public List<Service> getServiceListBySiteId(long siteId) {
		String hql = "from Service where site.siteId = ? and enabled = ?";
		Query q = super.getSessionFactory().getCurrentSession().createQuery(hql);
		q.setParameter(0, siteId);
		q.setParameter(1, Enabled.Y);
		return q.list();
	}



	/**
	  * <p>Title: getServiceDTOByProfileRoom</p>
	  * <p>Description: </p>
	  * @param profileId
	  * @param roomId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.ServiceDAO#getServiceDTOByProfileRoom(long, long)
	  */
	@Override
	public List<ServiceDTO> getServiceDTOByProfileRoom(long profileId,
			long roomId) {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("SELECT s.service_id, s.service_name");
		sb.append(" FROM room_services rs, profile_service ps, service s");
		sb.append(" WHERE rs.service_id = ps.service_id AND rs.service_id   = s.service_id");
		sb.append(" AND rs.room_id = ? AND ps.profile_id = ? AND ps.enabled = ?");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, roomId);
		query.setParameter(1, profileId);
		query.setParameter(2, Enabled.Y.toString());
		@SuppressWarnings("unchecked")
		List<Object[]> list = query.list();
		List<ServiceDTO> dtoList = new ArrayList<ServiceDTO>();
		for (Object[] row : list) {
			ServiceDTO dto = new ServiceDTO(Long.parseLong(row[0].toString()), row[1].toString());
			dtoList.add(dto);
		}
		return dtoList;
	}



	/**
	 * <p>Title: deleteProfileServiceByServiceId</p>
	 * <p>Description: </p>
	 * @param serviceId
	 * @throws Exception
	 * @see com.guitarcenter.scheduler.dao.ServiceDAO#deleteProfileServiceByServiceId(long)
	 */
	@Override
	public void deleteProfileServiceByServiceId(long serviceId)
			throws Exception {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("delete profile_service where service_id=?");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, serviceId);
		try {
			query.executeUpdate();
		} catch (Exception e) {
			throw new Exception(e.getMessage());
		}
	}



	/**
	 * <p>Title: deleteRoomTemplateServiceByServiceId</p>
	 * <p>Description: </p>
	 * @param serviceId
	 * @throws Exception
	 * @see com.guitarcenter.scheduler.dao.ServiceDAO#deleteRoomTemplateServiceByServiceId(long)
	 */
	@Override
	public void deleteRoomTemplateServiceByServiceId(long serviceId)
			throws Exception {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("delete room_template_services where service_id=?");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, serviceId);
		try {
			query.executeUpdate();
		} catch (Exception e) {
			throw new Exception(e.getMessage());
		}
	}



	/**
	 * <p>Title: deleteRoomServiceByServiceId</p>
	 * <p>Description: </p>
	 * @param serviceId
	 * @throws Exception
	 * @see com.guitarcenter.scheduler.dao.ServiceDAO#deleteRoomServiceByServiceId(long)
	 */
	@Override
	public void deleteRoomServiceByServiceId(long serviceId) throws Exception {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("delete room_services where service_id=?");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, serviceId);
		try {
			query.executeUpdate();
		} catch (Exception e) {
			throw new Exception(e.getMessage());
		}
	}
}
