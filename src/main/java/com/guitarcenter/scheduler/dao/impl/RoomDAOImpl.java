package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION_PROFILE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_MORE_ACTIVITIES;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_MORE_SERVICES;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_PARENT_ROOM;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM_NUMBER;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM_SIZE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM_TEMPLATE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM_TYPE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;
import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.hibernate.Query;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.RoomDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.RoomCriterion;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.enums.Canceled;

@Repository("roomDAO")
public class RoomDAOImpl extends AbstractDAOImpl<Room> implements RoomDAO {

	public RoomDAOImpl() {
		super(Room.class);
	}



	@Override
	protected void updateAuditor(Room pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(Room pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_LOCATION_PROFILE, pFetchMode, pResult.getLocationProfile());
		addFetchCriteria(FETCH_PARENT_ROOM, pFetchMode, pResult.getParentRoom());
		addFetchCriteria(FETCH_ROOM_NUMBER, pFetchMode, pResult.getRoomNumber());
		addFetchCriteria(FETCH_ROOM_SIZE, pFetchMode, pResult.getRoomSize());
		addFetchCriteria(FETCH_ROOM_TEMPLATE, pFetchMode, pResult.getRoomTemplate());
		addFetchCriteria(FETCH_ROOM_TYPE, pFetchMode, pResult.getRoomType());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_LOCATION_PROFILE, pFetchMode, "locationProfile", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_PARENT_ROOM, pFetchMode, "parentRoom", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_ROOM_NUMBER, pFetchMode, "roomNumber", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_ROOM_SIZE, pFetchMode, "roomSize", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_ROOM_TEMPLATE, pFetchMode, "roomTemplate", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_ROOM_TYPE, pFetchMode, "roomType", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_SITE, pFetchMode, "site", FetchMode.JOIN, pCriteria);
	}



	@Override
	protected void fetchMany(Room pResult, int pFetchMode) {
		addFetchCriteria(FETCH_MORE_SERVICES, pFetchMode, pResult.getServices());
		addFetchCriteria(FETCH_MORE_ACTIVITIES, pFetchMode, pResult.getActivities());
	}



	@Override
	protected Criterion<Room, Room> getCriterionInstance() {
		return RoomCriterion.getInstance();
	}

	@Override
	public Room getRoomByAppointmentTime(long roomId, String startDate, String startTime, String endTime, long profileId) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.getNamedQuery("room.getRoomByAppointmentTime");
		query.setParameter(0, startDate+" "+startTime);
		query.setParameter(1, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(2, startDate+" "+endTime);
		query.setParameter(3, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(4, startDate+" "+startTime);
		query.setParameter(5, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(6, startDate+" "+endTime);
		query.setParameter(7, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(8, startDate+" "+startTime);
		query.setParameter(9, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(10, startDate+" "+endTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, profileId);
		//String  canceleds = Canceled.H.toString()+","+Canceled.N.toString();
		query.setParameter(13, Canceled.N.toString());
		query.setParameter(14, Canceled.H.toString());
		//query.setParameter(13, Canceled.N.toString());
		query.setParameter(15, roomId);
		@SuppressWarnings("unchecked")
        List<Room> l = query.list();
		return l.size()==0?null:l.get(0);
	}



	/*
	  * <p>Title: getRoomByAppointmentRecurringTime</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @return
	  * @see com.guitarcenter.scheduler.dao.RoomDAO#getRoomByAppointmentRecurringTime(long, java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	  */
	@Override
	public Room getRoomByAppointmentRecurringTime(long roomId, String startDate,
			String endDate, String startTime, String endTime, long profileId) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.getNamedQuery("room.getRoomByAppointmentRecurringTime");
		query.setParameter(0, startDate);
		query.setParameter(1, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(3, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(4, startDate);
		query.setParameter(5, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(6, endDate);
		query.setParameter(7, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(8, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(9, startTime);
		query.setParameter(10, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(11, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(12, endTime);
		query.setParameter(13, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(14, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(15, startTime);
		query.setParameter(16, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(17, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(18, endTime);
		query.setParameter(19, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(20, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(21, startTime);
		query.setParameter(22, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(23, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(24, endTime);
		query.setParameter(25, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(26, profileId);
		//String  canceleds = Canceled.H.toString()+","+Canceled.N.toString();
		//query.setParameter(27, canceleds);
		query.setParameter(27, Canceled.N.toString());
		query.setParameter(28, Canceled.H.toString());
		query.setParameter(29, roomId);
		@SuppressWarnings("unchecked")
        List<Room> l = query.list();
		return l.size()==0?null:l.get(0);
	}
	
	/**
	  * <p>Title: getSplitRoomsByParentId</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.RoomDAO#getSplitRoomsByParentId(long)
	  */
	@SuppressWarnings("unchecked")
    @Override
	public List<Room> getSplitRoomsByParentId(long roomId) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.createQuery("from Room where parentRoom.roomId = ?");
		query.setParameter(0, roomId);
		return query.list();
	}



	/**
	  * <p>Title: getSplitRoomByTime</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @param startDate
	  * @param startTime
	  * @param endTime
	  * @return
	  * @see com.guitarcenter.scheduler.dao.RoomDAO#getSplitRoomByTime(long, java.lang.String, java.lang.String, java.lang.String)
	  */
	@Override
	public List<Room> getSplitRoomByTime(long roomId, String startDate,
			String startTime, String endTime) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.getNamedQuery("room.getSplitRoomByTime");
		query.setParameter(0, startDate+" "+startTime);
		query.setParameter(1, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(2, startDate+" "+endTime);
		query.setParameter(3, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(4, startDate+" "+startTime);
		query.setParameter(5, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(6, startDate+" "+endTime);
		query.setParameter(7, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(8, startDate+" "+startTime);
		query.setParameter(9, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(10, startDate+" "+endTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, roomId);
		@SuppressWarnings("unchecked")
        List<Room> l = query.list();
		return l;
	}



	/**
	  * <p>Title: getSplitRoomByRecurringTime</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @return
	  * @see com.guitarcenter.scheduler.dao.RoomDAO#getSplitRoomByRecurringTime(long, java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	  */
	@Override
	public List<Room> getSplitRoomByRecurringTime(long roomId,
			String startDate, String endDate, String startTime, String endTime) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.getNamedQuery("room.getSplitRoomByRecurringTime");
		query.setParameter(0, startDate);
		query.setParameter(1, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(2, startDate);
		query.setParameter(3, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(4, endDate);
		query.setParameter(5, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(6, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(7, startTime);
		query.setParameter(8, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(9, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(10, endTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(13, startTime);
		query.setParameter(14, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(15, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(16, endTime);
		query.setParameter(17, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(18, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(19, startTime);
		query.setParameter(20, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(21, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(22, endTime);
		query.setParameter(23, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(24, roomId);
		@SuppressWarnings("unchecked")
        List<Room> l = query.list();
		return l;
	}



	@Override
	public boolean getUpdateRoomByAppointmentTime(long roomId, String startDate,
			String startTime, String endTime, long profileId,
			String excludeAppointmentIdParam) {
		Session session = super.getSessionFactory().getCurrentSession();
		String queryString = session.getNamedQuery("room.getUpdateRoomByAppointmentTime").getQueryString();
		String replaceParameter = SystemUtil.REPLACE_CONDITION.replace("&PARAMETER", excludeAppointmentIdParam);
		String sql = excludeAppointmentIdParam.length()==0?queryString.replace("&REPLACECONDITION", ""):queryString.replace("&REPLACECONDITION", replaceParameter);
		Query query = session.createSQLQuery(sql);
		query.setParameter(0, startDate+" "+startTime);
		query.setParameter(1, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(2, startDate+" "+endTime);
		query.setParameter(3, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(4, startDate+" "+startTime);
		query.setParameter(5, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(6, startDate+" "+endTime);
		query.setParameter(7, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(8, startDate+" "+startTime);
		query.setParameter(9, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(10, startDate+" "+endTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, profileId);
		query.setParameter(13, Canceled.N.toString());
		query.setParameter(14, Canceled.H.toString());
		query.setParameter(15, roomId);
		Object o = query.uniqueResult();
		return Boolean.parseBoolean(o.toString());
	}



	@Override
	public boolean getUpdateRoomByAppointmentRecurringTime(long roomId,
			String startDate, String endDate, String startTime, String endTime,
			long profileId, String excludeAppointmentIdParam) {
		Session session = super.getSessionFactory().getCurrentSession();
		String queryString = session.getNamedQuery("room.getUpdateRoomByAppointmentRecurringTime").getQueryString();
		String replaceParameter = SystemUtil.REPLACE_CONDITION.replace("&PARAMETER", excludeAppointmentIdParam);
		String sql = excludeAppointmentIdParam.length()==0?queryString.replace("&REPLACECONDITION", ""):queryString.replace("&REPLACECONDITION", replaceParameter);
		Query query = session.createSQLQuery(sql);
		query.setParameter(0, startDate);
		query.setParameter(1, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(3, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(4, startDate);
		query.setParameter(5, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(6, endDate);
		query.setParameter(7, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(8, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(9, startTime);
		query.setParameter(10, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(11, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(12, endTime);
		query.setParameter(13, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(14, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(15, startTime);
		query.setParameter(16, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(17, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(18, endTime);
		query.setParameter(19, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(20, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(21, startTime);
		query.setParameter(22, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(23, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(24, endTime);
		query.setParameter(25, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(26, profileId);
		query.setParameter(27, Canceled.N.toString());
		query.setParameter(28, Canceled.H.toString());
		query.setParameter(29, roomId);
		Object o = query.uniqueResult();
		return Boolean.parseBoolean(o.toString());
	}
}
