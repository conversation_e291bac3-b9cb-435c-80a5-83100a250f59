package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SERVICE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.ActivityDAO;
import com.guitarcenter.scheduler.dao.criterion.ActivityCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.enums.Canceled;
import com.guitarcenter.scheduler.model.enums.Enabled;

@Repository("activityDAO")
public class ActivityDAOImpl extends AbstractDAOImpl<Activity> implements ActivityDAO {

	public ActivityDAOImpl() {
		super(Activity.class);
	}



	@Override
	protected void updateAuditor(Activity pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(Activity pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_SERVICE, pFetchMode, pResult.getService());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_SERVICE, pFetchMode, "service", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_SITE, pFetchMode, "site", FetchMode.JOIN, pCriteria);
	}



	@Override
	protected void fetchMany(Activity pResult, int pFetchMode) {
	}



	@Override
	protected Criterion<Activity, Activity> getCriterionInstance() {
		return ActivityCriterion.getInstance();
	}



	/**
	 * 
	  * <p>Title: getActivityByAppointmentTime</p>
	  * <p>Description: </p>
	  * @param activityId
	  * @param startDate
	  * @param startTime
	  * @param endTime
	  * @return
	  * @see com.guitarcenter.scheduler.dao.ActivityDAO#getActivityByAppointmentTime(long, java.lang.String, java.lang.String, java.lang.String)
	 */
	@Override
	public Activity getActivityByAppointmentTime(long activityId, String startDate,
			String startTime, String endTime, long profileId) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.getNamedQuery("activity.getActivityByAppointmentTime");
		query.setParameter(0, startDate+" "+startTime);
		query.setParameter(1, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(2, startDate+" "+endTime);
		query.setParameter(3, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(4, startDate+" "+startTime);
		query.setParameter(5, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(6, startDate+" "+endTime);
		query.setParameter(7, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(8, startDate+" "+startTime);
		query.setParameter(9, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(10, startDate+" "+endTime);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(12, profileId);
		query.setParameter(13, Canceled.N.toString());
		query.setParameter(14, activityId);
		@SuppressWarnings("unchecked")
        List<Activity> l = query.list();
		return l.size()==0?null:l.get(0);
	}



	/**
	 * 
	  * <p>Title: getActivityByAppointmentRecurringTime</p>
	  * <p>Description: </p>
	  * @param activityId
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @return
	  * @see com.guitarcenter.scheduler.dao.ActivityDAO#getActivityByAppointmentRecurringTime(long, java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	 */
	@Override
	public Activity getActivityByAppointmentRecurringTime(long activityId,
			String startDate, String endDate, String startTime, String endTime, long profileId) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.getNamedQuery("activity.getActivityByAppointmentRecurringTime");
		query.setParameter(0, startDate);
		query.setParameter(1, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(2, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(3, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(4, startDate);
		query.setParameter(5, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(6, endDate);
		query.setParameter(7, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(8, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(9, startTime);
		query.setParameter(10, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(11, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(12, endTime);
		query.setParameter(13, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(14, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(15, startTime);
		query.setParameter(16, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(17, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(18, endTime);
		query.setParameter(19, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(20, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(21, startTime);
		query.setParameter(22, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(23, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(24, endTime);
		query.setParameter(25, DateTimeUtil.TIME_FORMAT_MM_DD_YYYY_HH24_MI);
		query.setParameter(26, profileId);
		query.setParameter(27, Canceled.N.toString());
		query.setParameter(28, activityId);
		@SuppressWarnings("unchecked")
        List<Activity> l = query.list();
		return l.size()==0?null:l.get(0);
	}



	/**
	  * <p>Title: getActivityDTOByProfileRoom</p>
	  * <p>Description: </p>
	  * @param profileId
	  * @param roomId
	  * @return
	  * @see com.guitarcenter.scheduler.dao.ActivityDAO#getActivityDTOByProfileRoom(long, long)
	  */
	@Override
	public List<ActivityDTO> getActivityDTOByProfileRoom(long profileId,
			long roomId) {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("SELECT ra.activity_id, a.activity_name");
		sb.append(" FROM room_activities ra, profile_activity pa, activity a");
		sb.append(" WHERE ra.activity_id = pa.activity_id AND ra.activity_id   = a.activity_id");
		sb.append(" AND ra.room_id = ? AND pa.profile_id = ? AND pa.enabled = ?");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, roomId);
		query.setParameter(1, profileId);
		query.setParameter(2, Enabled.Y.toString());
		@SuppressWarnings("unchecked")
		List<Object[]> list = query.list();
		List<ActivityDTO> dtoList = new ArrayList<ActivityDTO>();
		for (Object[] row : list) {
			ActivityDTO dto = new ActivityDTO(Long.parseLong(row[0].toString()), row[1].toString());
			dtoList.add(dto);
		}
		return dtoList;
	}



	/**
	 * <p>Title: deleteInstructorAvtivityByActivityId</p>
	 * <p>Description: </p>
	 * @param activityId
	 * @throws Exception 
	 * @see com.guitarcenter.scheduler.dao.ActivityDAO#deleteInstructorAvtivityByActivityId(long)
	 */
	@Override
	public void deleteInstructorAvtivityByActivityId(long activityId) throws Exception {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("delete instructor_activities where activity_id=?");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, activityId);
		try {
			query.executeUpdate();
		} catch (Exception e) {
			throw new Exception(e.getMessage());
		}
	}



	/**
	 * <p>Title: deleteProfileAvtivityByActivityId</p>
	 * <p>Description: </p>
	 * @param activityId
	 * @throws Exception
	 * @see com.guitarcenter.scheduler.dao.ActivityDAO#deleteProfileAvtivityByActivityId(long)
	 */
	@Override
	public void deleteProfileAvtivityByActivityId(long activityId)
			throws Exception {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("delete profile_activity where activity_id=?");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, activityId);
		try {
			query.executeUpdate();
		} catch (Exception e) {
			throw new Exception(e.getMessage());
		}
	}



	/**
	 * <p>Title: deleteRoomTemplateAvtivityByActivityId</p>
	 * <p>Description: </p>
	 * @param activityId
	 * @throws Exception
	 * @see com.guitarcenter.scheduler.dao.ActivityDAO#deleteRoomTemplateAvtivityByActivityId(long)
	 */
	@Override
	public void deleteRoomTemplateAvtivityByActivityId(long activityId)
			throws Exception {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("delete room_template_activities where activity_id=?");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, activityId);
		try {
			query.executeUpdate();
		} catch (Exception e) {
			throw new Exception(e.getMessage());
		}
	}



	/**
	 * <p>Title: deleteRoomAvtivityByActivityId</p>
	 * <p>Description: </p>
	 * @param activityId
	 * @throws Exception
	 * @see com.guitarcenter.scheduler.dao.ActivityDAO#deleteRoomAvtivityByActivityId(long)
	 */
	@Override
	public void deleteRoomAvtivityByActivityId(long activityId)
			throws Exception {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("delete room_activities where activity_id=?");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, activityId);
		try {
			query.executeUpdate();
		} catch (Exception e) {
			throw new Exception(e.getMessage());
		}
	}



	/**
	 * <p>Title: deleteInstructorAvtivityByServiceId</p>
	 * <p>Description: </p>
	 * @param serviceId
	 * @throws Exception
	 * @see com.guitarcenter.scheduler.dao.ActivityDAO#deleteInstructorAvtivityByServiceId(long)
	 */
	@Override
	public void deleteInstructorAvtivityByServiceId(long serviceId)
			throws Exception {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("delete instructor_activities i where exists(select * from activity a where a.activity_id = i.activity_id and a.service_id=?)");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, serviceId);
		try {
			query.executeUpdate();
		} catch (Exception e) {
			throw new Exception(e.getMessage());
		}
	}



	/**
	 * <p>Title: deleteProfileAvtivityByServiceId</p>
	 * <p>Description: </p>
	 * @param serviceId
	 * @throws Exception
	 * @see com.guitarcenter.scheduler.dao.ActivityDAO#deleteProfileAvtivityByServiceId(long)
	 */
	@Override
	public void deleteProfileAvtivityByServiceId(long serviceId)
			throws Exception {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("delete profile_activity p where exists(select * from activity a where a.activity_id = p.activity_id and a.service_id=?)");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, serviceId);
		try {
			query.executeUpdate();
		} catch (Exception e) {
			throw new Exception(e.getMessage());
		}
	}



	/**
	 * <p>Title: deleteRoomTemplateAvtivityByServiceId</p>
	 * <p>Description: </p>
	 * @param serviceId
	 * @throws Exception
	 * @see com.guitarcenter.scheduler.dao.ActivityDAO#deleteRoomTemplateAvtivityByServiceId(long)
	 */
	@Override
	public void deleteRoomTemplateAvtivityByServiceId(long serviceId)
			throws Exception {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("delete room_template_activities r where exists(select * from activity a where a.activity_id = r.activity_id and a.service_id=?)");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, serviceId);
		try {
			query.executeUpdate();
		} catch (Exception e) {
			throw new Exception(e.getMessage());
		}
	}



	/**
	 * <p>Title: deleteRoomAvtivityByServiceId</p>
	 * <p>Description: </p>
	 * @param serviceId
	 * @throws Exception
	 * @see com.guitarcenter.scheduler.dao.ActivityDAO#deleteRoomAvtivityByServiceId(long)
	 */
	@Override
	public void deleteRoomAvtivityByServiceId(long serviceId) throws Exception {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("delete room_activities r where exists(select * from activity a where a.activity_id = r.activity_id and a.service_id=?)");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, serviceId);
		try {
			query.executeUpdate();
		} catch (Exception e) {
			throw new Exception(e.getMessage());
		}
	}

}
