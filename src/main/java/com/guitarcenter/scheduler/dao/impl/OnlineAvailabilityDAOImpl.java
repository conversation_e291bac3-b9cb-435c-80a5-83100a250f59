package com.guitarcenter.scheduler.dao.impl;

import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.OnlineAvailabilityDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.Onetime;
import com.guitarcenter.scheduler.model.OnlineAvailability;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileTimeoff;

@Repository("onlineAvailabilityDAO")
public class OnlineAvailabilityDAOImpl extends AbstractDAOImpl<OnlineAvailability> implements OnlineAvailabilityDAO {
 
	public OnlineAvailabilityDAOImpl() {
		super(OnlineAvailability.class);
	}

	@Override
	public List<OnlineAvailability> getOnlineAvailabilityByInstructorId(long instructorId) {

		Session session = super.getSessionFactory().getCurrentSession();;
		Criteria crit = session.createCriteria(OnlineAvailability.class);
		crit.add(Restrictions.eq("instructor.instructorId",instructorId));
		List<OnlineAvailability> list = crit.list();
		return list;
	}

	@Override
	public List<OnlineAvailability> getDisplayOnlineAvailabilityeByInstructorId(long instructorId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public boolean checkOnlineAvailabilityByProfileId(String startDate, String startTime, String endTime,
			long profileId) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public boolean checkOnlineAvailabilityByTime(String startDate, String startTime, String endDate, String endTime,
			long instructorId) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public List<Onetime> getOnlineAvailabilityByTime(String startDate, String startTime, String endTime,
			long instructorId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void saveOrUpdate(OnlineAvailability pT, Person pUpdatedBy) {
		Session session = getSessionFactory().getCurrentSession();
		updateAuditor(pT, pUpdatedBy);
		session.saveOrUpdate(pT);
	}

	@Override
	protected void updateAuditor(OnlineAvailability pT, Person pUpdatedBy) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchOne(OnlineAvailability pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchMany(OnlineAvailability pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected Criterion<OnlineAvailability, OnlineAvailability> getCriterionInstance() {
		// TODO Auto-generated method stub
		return null;
	}

	 

}
