package com.guitarcenter.scheduler.dao.impl;

import java.util.HashMap;
import java.util.Map;
import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.ScrollableResults;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import com.guitarcenter.scheduler.dao.DataBackUpMonthlyDAO;
import com.guitarcenter.scheduler.model.NativeString;
import com.guitarcenter.scheduler.service.impl.AppointmentServiceImpl;


@Repository("dataBackUpMonthlyDAO")
public class DataBackUpMonthlyDAOImpl implements DataBackUpMonthlyDAO {

	@Autowired
	@Qualifier("sessionFactory")
	private SessionFactory				mSessionFactory;
	
	public SessionFactory getSessionFactory() {
		return mSessionFactory;
	}
	
	private static final Logger		LOG					= LoggerFactory.getLogger(AppointmentServiceImpl.class);
	
	@Override
	@Transactional
	public Map<String,String> doBackUpForAppointmentTable() {

		Map<String,String> mdata = new HashMap<>();
		Session session =null;;
		try {
			
			session = getSessionFactory().getCurrentSession();
			 NativeString concurrentString1 = null;
			 NativeString concurrentString2 = null;
			 NativeString concurrentString3 = null;
			 NativeString concurrentString4 = null;
			 
 
			 
			 SQLQuery query1 = (SQLQuery) session.getNamedQuery("callAppTabRecProcedure");
			 query1.setParameter(0, 1003);
			 ScrollableResults scroll1 =  query1.scroll();

			 SQLQuery query2 = (SQLQuery) session.getNamedQuery("callAppTabDropProcedure");
			 query2.setParameter(0, 1003);
			 ScrollableResults scroll2 = query2.scroll();

			 SQLQuery query3 = (SQLQuery) session.getNamedQuery("callAppCustTabRecProcedure");
			 query3.setParameter(0, 1003);
			 ScrollableResults scroll3 =  query3.scroll();
 			 
			 SQLQuery query4 = (SQLQuery) session.getNamedQuery("callAppCustTabDropProcedure");
			 query4.setParameter(0, 1003);
        	 ScrollableResults scroll4 = query4.scroll();

			 
             while (scroll1.next()) 
             {
             	 Object[] objects = scroll1.get();
             	if(null != objects)concurrentString1 = (NativeString) objects[0]; 
             }
             
             while (scroll2.next()) 
             {
             	Object[] objects = scroll2.get();
             	if(null != objects)concurrentString2 = (NativeString) objects[0];
             }
             while (scroll3.next()) 
             {
             	 Object[] objects = scroll3.get();
             	if(null != objects)concurrentString3 = (NativeString) objects[0]; 
             }
             
             while (scroll4.next()) 
             {
             	Object[] objects = scroll4.get();
             	if(null != objects)concurrentString4 = (NativeString) objects[0];
             }
             

             mdata.put("APPTMNT_BKP_TBL_CREATED", concurrentString1.getNoSlotsStr());
             mdata.put("APPTMNT_BKP_TBL_DROP", concurrentString2.getNoSlotsStr());
             mdata.put("APPTMNT_CUST_BKP_TBL_CREATED", concurrentString3.getNoSlotsStr());
             mdata.put("APPTMNT_CUST_BKP_TBL_DROP", concurrentString4.getNoSlotsStr());
             
             
		} catch (HibernateException e) {
			LOG.error("Caught an exception  " + "HibernateException" + " {}", e);
		}
		catch (Exception e) {
			LOG.error("Caught an exception  " + " Exception" + " {}", e);
		}
		 
		return  mdata;
	} 
 
}
