package com.guitarcenter.scheduler.dao.impl;

//---GSSP Instructor Mode update changes
import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.ServiceModeDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ServiceMode;

@Repository("serviceModeDAO")
public class ServiceModeDAOImpl extends AbstractDAOImpl<ServiceMode> implements ServiceModeDAO {
	
	private static final Logger	 LOGGER	= LoggerFactory.getLogger(ServiceModeDAOImpl.class);
	
	public ServiceModeDAOImpl() {
		super(ServiceMode.class);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	@SuppressWarnings("unchecked")
	public List<ServiceMode> getServiceModeList() throws Exception{
		
		Session session = super.getSessionFactory().getCurrentSession();
		Criteria crit = session.createCriteria(ServiceMode.class);
		List<ServiceMode> list = crit.list();
		return list;

		}
	
	@SuppressWarnings("unchecked")
	@Override
	public ServiceMode getServiceMode(long serviceModeId) {
		
		Session session = super.getSessionFactory().getCurrentSession();;
		Criteria crit = session.createCriteria(ServiceMode.class);
		crit.add(Restrictions.eq("serviceModeId",serviceModeId));
		List<ServiceMode> list = crit.list();
		ServiceMode pto = list.get(0);
		return pto;
	}

	@Override
	protected void updateAuditor(ServiceMode pT, Person pUpdatedBy) {
	
	}

	@Override
	protected void fetchOne(ServiceMode pResult, int pFetchMode) {		
	}


	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
	}


	@Override
	protected void fetchMany(ServiceMode pResult, int pFetchMode) {
		
	}


	@Override
	protected Criterion<ServiceMode, ServiceMode> getCriterionInstance() {
		return null;
	}
	

	 
}
