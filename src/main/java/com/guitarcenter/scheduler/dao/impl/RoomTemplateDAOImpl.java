package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_MORE_ACTIVITIES;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_MORE_SERVICES;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM_SIZE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROOM_TYPE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;
import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.hibernate.Query;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.RoomTemplateDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.RoomTemplateCriterion;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.RoomTemplate;

@Repository(value="roomTemplateDAO")
public class RoomTemplateDAOImpl extends AbstractDAOImpl<RoomTemplate> implements RoomTemplateDAO {

	public RoomTemplateDAOImpl() {
		super(RoomTemplate.class);
	}



	@Override
	protected void updateAuditor(RoomTemplate pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(RoomTemplate pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_ROOM_SIZE, pFetchMode, pResult.getRoomSize());
		addFetchCriteria(FETCH_ROOM_TYPE, pFetchMode, pResult.getRoomType());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_ROOM_SIZE, pFetchMode, "roomSize", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_ROOM_TYPE, pFetchMode, "roomType", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_SITE, pFetchMode, "site", FetchMode.JOIN, pCriteria);
	}



	@Override
	protected void fetchMany(RoomTemplate pResult, int pFetchMode) {
		addFetchCriteria(FETCH_MORE_SERVICES, pFetchMode, pResult.getServices());
		addFetchCriteria(FETCH_MORE_ACTIVITIES, pFetchMode, pResult.getActivities());
	}



	@Override
	protected Criterion<RoomTemplate, RoomTemplate> getCriterionInstance() {
		return RoomTemplateCriterion.getInstance();
	}



	/*
	  * <p>Title: getRoomTemplateList</p>
	  * <p>Description: </p>
	  * @param site
	  * @return
	  * @see com.guitarcenter.scheduler.dao.RoomTemplateDAO#getRoomTemplateList(long)
	  */
	@SuppressWarnings("unchecked")
    @Override
	public List<RoomTemplate> getRoomTemplateList(long siteId) {
		String hql = "from RoomTemplate where site.siteId = ?";
		Query q = super.getSessionFactory().getCurrentSession().createQuery(hql);
		q.setParameter(0, siteId);
		return q.list();
	}
}
