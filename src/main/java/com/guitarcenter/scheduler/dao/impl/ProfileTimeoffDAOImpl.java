package com.guitarcenter.scheduler.dao.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

import org.hibernate.Criteria;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.ProfileTimeoffDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dto.ProfileTimeOffDTO;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileTimeoff;

@Repository("profileTimeoffDAO")
public class ProfileTimeoffDAOImpl extends AbstractDAOImpl<ProfileTimeoff> implements ProfileTimeoffDAO {
	
	private static final Logger	 LOGGER	= LoggerFactory.getLogger(ProfileTimeoffDAOImpl.class);
	
	public ProfileTimeoffDAOImpl() {
		super(ProfileTimeoff.class);
	}

	@SuppressWarnings("unchecked")
	public List<ProfileTimeoff> getTimeoffByProfileId(long profileId,Date startDate,Date endDate ) {
		
		Session session = super.getSessionFactory().getCurrentSession();
		Criteria crit = session.createCriteria(ProfileTimeoff.class);
		crit.add(Restrictions.eq("profileId",profileId));
		crit.add(Restrictions.ge("startTime",startDate));
		crit.add(Restrictions.le("startTime",endDate));
		List<ProfileTimeoff> list = crit.list();
		return list;

		}

	@SuppressWarnings("unchecked")
	public List<ProfileTimeoff> getTimeoffByProfileIdInsAVL(long profileId,Date startDate,Date endDate ) {
		
		Session session = super.getSessionFactory().getCurrentSession();
		Criteria crit = session.createCriteria(ProfileTimeoff.class);
		crit.add(Restrictions.eq("profileId",profileId));
		crit.add(Restrictions.ge("startTime",startDate));
		crit.add(Restrictions.le("startTime",endDate));
		List<ProfileTimeoff> list = crit.list();
		return list;

		}
	
	@Override
	protected void updateAuditor(ProfileTimeoff pT, Person pUpdatedBy) {
	
	}

	@Override
	protected void fetchOne(ProfileTimeoff pResult, int pFetchMode) {		
	}


	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
	}


	@Override
	protected void fetchMany(ProfileTimeoff pResult, int pFetchMode) {
		
	}


	@Override
	protected Criterion<ProfileTimeoff, ProfileTimeoff> getCriterionInstance() {
		return null;
	}
	

	@Override
	public List<ProfileTimeoff> getUpcomingTimeOffByProfileId(long profileId, Date startDate) throws Exception {
		Session session = super.getSessionFactory().getCurrentSession();
		Criteria crit = session.createCriteria(ProfileTimeoff.class);
		crit.add(Restrictions.eq("profileId",profileId));
		crit.add(Restrictions.ge("startTime",startDate));
		crit.addOrder(Order.asc("startTime"));
		 crit.setMaxResults(2);
		@SuppressWarnings("unchecked")
		List<ProfileTimeoff> list = crit.list();
		
		return list;

	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<ProfileTimeoff> getDisplayProfileTimeoffById(long profileId) {
		
		Date startDate = new Date();
		Session session = super.getSessionFactory().getCurrentSession();
		Criteria crit = session.createCriteria(ProfileTimeoff.class);
		crit.add(Restrictions.eq("profileId",profileId));
		crit.add(Restrictions.ge("startTime",startDate));
		crit.addOrder(Order.asc("startTime"));

		@SuppressWarnings("unchecked")
		List<ProfileTimeoff> list = crit.list();

		return list;
		
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public ProfileTimeoff getProfileTimeoffByProfileTimeoffId(long profileTimeoffId) {
		
		Session session = super.getSessionFactory().getCurrentSession();;
		Criteria crit = session.createCriteria(ProfileTimeoff.class);
		crit.add(Restrictions.eq("profiletimeoffId",profileTimeoffId));
		List<ProfileTimeoff> list = crit.list();
		ProfileTimeoff pto = list.get(0);
		return pto;
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void saveOrUpdateProfileTimeOff(ProfileTimeoff pT) {

		Session session = getSessionFactory().getCurrentSession();
		session.saveOrUpdate(pT);
 
	}
 
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public  ProfileTimeOffDTO getAppointmentTimeForProfile(long profileId,String profileTimeOffDate) {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("SELECT  min(TO_CHAR(START_TIME,'hh24:mi:ss')) FIRST_START_TIME,Max(TO_CHAR(END_TIME,'hh24:mi:ss')) LAST_END_TIME ");
		sb.append(" from Appointment  where ");
		sb.append("to_char(START_TIME,'MM/DD/YYYY') = ? and PROFILE_ID = ?  and CANCELED in ('N','H') ");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, profileTimeOffDate);
		query.setParameter(1, profileId);
		@SuppressWarnings("unchecked")
		List<Object[]> list = query.list();
		ProfileTimeOffDTO dtoList = new ProfileTimeOffDTO();
		Stream<Object[]> lm = list.stream().limit(1);
        lm.forEach(row->{
			dtoList.setFromTime((String) row[0]);
			dtoList.setToTime((String) row[1]); 
        });
		return dtoList;
	}
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public  List<ProfileTimeOffDTO>  getAppointmentTimeListForProfileAndDate(long profileId,String profileTimeOffDate) {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("SELECT TO_CHAR(START_TIME,'hh24:mi') FIRST_START_TIME,TO_CHAR(END_TIME,'hh24:mi') LAST_END_TIME, ");
		sb.append(" to_char(START_TIME,'MM/DD/YYYY') profile_date from PROFILE_TIMEOFF  where ");
		sb.append("  PROFILE_ID = ?  ");
		SQLQuery query = session.createSQLQuery(sb.toString());
		List<ProfileTimeOffDTO> profList= new ArrayList<ProfileTimeOffDTO>();
		query.setParameter(0, profileId);
		@SuppressWarnings("unchecked")
		List<Object[]> list = query.list();
	
		for (Object[] row : list) {
			ProfileTimeOffDTO profDTO = new ProfileTimeOffDTO();
			profDTO.setFromTime((String) row[0]);
			profDTO.setToTime((String) row[1]);
			profDTO.setFromDate((String) row[2]);
			profList.add(profDTO);
		}
		return profList;
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public ProfileTimeOffDTO  verifySingleAppointmentWithProfileTimeOff(long profileId,String profileTimeOffDate) {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("SELECT TO_CHAR(START_TIME,'hh24:mi') FIRST_START_TIME,TO_CHAR(END_TIME,'hh24:mi') LAST_END_TIME, ");
		sb.append(" to_char(START_TIME,'MM/DD/YYYY') profile_date from PROFILE_TIMEOFF  where ");
		sb.append(" to_char(START_TIME,'MM/DD/YYYY') = ? and   PROFILE_ID = ?  ");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, profileTimeOffDate);
		query.setParameter(1, profileId);
		@SuppressWarnings("unchecked")
		List<Object[]> list = query.list();
		ProfileTimeOffDTO profDTO = new ProfileTimeOffDTO();
		
		Stream<Object[]> lm = list.stream().limit(1);
        lm.forEach(row->{
			profDTO.setFromTime((String) row[0]);
			profDTO.setToTime((String) row[1]);
			profDTO.setFromDate((String) row[2]); 
        });
		return profDTO;
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public ProfileTimeOffDTO  getProfileTimeOffIdbyDate(long profileId,String profileTimeOffDate) {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("SELECT PROFILE_TIMEOFF_ID ProfileId, ");
		sb.append(" to_char(START_TIME,'MM/DD/YYYY') profile_date from PROFILE_TIMEOFF  where ");
		sb.append(" to_char(START_TIME,'MM/DD/YYYY') = ? and PROFILE_ID = ?  ");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, profileTimeOffDate);
		query.setParameter(1, profileId);

		@SuppressWarnings("unchecked")
		List<Object[]> list = query.list();
		ProfileTimeOffDTO profDTO = new ProfileTimeOffDTO();
		if(list != null){
			Stream<Object[]> lm = list.stream().limit(1);
	         lm.forEach(row->{
	        	 profDTO.setProfiletimeoffId(((Number) row[0]).longValue());
					profDTO.setFromDate((String) row[1]);  
	         });
		}
		return profDTO;
	}
}
