package com.guitarcenter.scheduler.dao.impl;

import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.InstructorAppointmentStatusDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.InstructorAppointmentStatus;
import com.guitarcenter.scheduler.model.Person;

@Repository("instructorAppointmentStatusDAO")
public class InstructorAppointmentStatusDAOImpl extends AbstractDAOImpl<InstructorAppointmentStatus> implements InstructorAppointmentStatusDAO {

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void saveOrUpdateInstructorAppointmentStatus(InstructorAppointmentStatus pT) {

		Session session = getSessionFactory().getCurrentSession();
		session.saveOrUpdate(pT);
 
	}
 
	
	public InstructorAppointmentStatusDAOImpl() {
		super(InstructorAppointmentStatus.class);
	}
	
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public List<InstructorAppointmentStatus> getInstructorAppointmentStatus(long appointmentId ) {
		/*
		 * String hql = "from InstructorAppointmentStatus where appointmentId = ? ";
		 * Query q = super.getSessionFactory().getCurrentSession().createQuery(hql);
		 * q.setParameter(0, appointmentId); return q.list();
		 */
		Session session = super.getSessionFactory().getCurrentSession();
		//Changes made for GSSP-151
		Query query = session.createQuery("from InstructorAppointmentStatus where appointmentId = ? ");
		query.setParameter(0, appointmentId);
	
		return query.list();
	
	}

	@Override
	protected void updateAuditor(InstructorAppointmentStatus pT, Person pUpdatedBy) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchOne(InstructorAppointmentStatus pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected void fetchMany(InstructorAppointmentStatus pResult, int pFetchMode) {
		// TODO Auto-generated method stub
		
	}

	@Override
	protected Criterion<InstructorAppointmentStatus, InstructorAppointmentStatus> getCriterionInstance() {
		// TODO Auto-generated method stub
		return null;
	}

	 
	 
	
}
