package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ACTIVITY;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_LOCATION_PROFILE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;

import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.ProfileActivityDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.ProfileActivityCriterion;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileActivity;

@Repository("profileActivityDAO")
public class ProfileActivityDAOImpl extends AbstractDAOImpl<ProfileActivity> implements ProfileActivityDAO {

	public ProfileActivityDAOImpl() {
		super(ProfileActivity.class);
	}



	@Override
	protected void updateAuditor(ProfileActivity pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(ProfileActivity pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
		addFetchCriteria(FETCH_LOCATION_PROFILE, pFetchMode, pResult.getLocationProfile());
		addFetchCriteria(FETCH_ACTIVITY, pFetchMode, pResult.getActivity());

	}



	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_SITE, pFetchMode, "site", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_LOCATION_PROFILE, pFetchMode, "locationProfile", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_ACTIVITY, pFetchMode, "activity", FetchMode.JOIN, pCriteria);
	}



	@Override
	protected void fetchMany(ProfileActivity pResult, int pFetchMode) {
	}



	@Override
	protected Criterion<ProfileActivity, ProfileActivity> getCriterionInstance() {
		return ProfileActivityCriterion.getInstance();
	}
	
	/**
	 * <p>Title: deleteProfileActivityByProfileIdServiceId</p>
	 * <p>Description: </p>
	 * @param profileId
	 * @param serviceId
	 * @throws Exception
	 * @see com.guitarcenter.scheduler.dao.ProfileActivityDAO#deleteProfileActivityByProfileIdServiceId(java.lang.Long, java.lang.Long)
	 */
	@Override
	public void deleteProfileActivityByProfileIdServiceId(Long profileId, Long serviceId)
			throws Exception {
		Session session = super.getSessionFactory().getCurrentSession();
		StringBuffer sb = new StringBuffer("delete profile_activity p where p.profile_id = ? and exists(select * from activity a where a.activity_id = p.activity_id and a.service_id=?)");
		SQLQuery query = session.createSQLQuery(sb.toString());
		query.setParameter(0, profileId);
		query.setParameter(1, serviceId);
		try {
			query.executeUpdate();
		} catch (Exception e) {
			throw new Exception(e.getMessage());
		}
	}
}
