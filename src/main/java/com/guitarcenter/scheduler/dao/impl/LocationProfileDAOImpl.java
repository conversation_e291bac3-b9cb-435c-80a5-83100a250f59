package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_AVAILABILITY;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;
import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.hibernate.Query;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.LocationProfileDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.LocationProfileCriterion;
import com.guitarcenter.scheduler.model.LocationProfile;
import com.guitarcenter.scheduler.model.Person;

@Repository("locationProfileDAO")
public class LocationProfileDAOImpl extends AbstractDAOImpl<LocationProfile> implements LocationProfileDAO {

	public LocationProfileDAOImpl() {
		super(LocationProfile.class);
	}



	@Override
	protected void updateAuditor(LocationProfile pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(LocationProfile pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_AVAILABILITY, pFetchMode, pResult.getAvailability());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_AVAILABILITY, pFetchMode, "availability", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_SITE, pFetchMode, "site", FetchMode.JOIN, pCriteria);
	}



	@Override
	protected void fetchMany(LocationProfile pResult, int pFetchMode) {
	}



	@Override
	protected Criterion<LocationProfile, LocationProfile> getCriterionInstance() {
		return LocationProfileCriterion.getInstance();
	}



	/**
	  * <p>Title: getLocationProfileByTime</p>
	  * <p>Description: </p>
	  * @param profileId
	  * @param startDate
	  * @param startTime
	  * @param endTime
	  * @return
	  * @see com.guitarcenter.scheduler.dao.LocationProfileDAO#getLocationProfileByTime(long, java.lang.String, java.lang.String, java.lang.String)
	  */
	@Override
	public LocationProfile getLocationProfileByTime(long profileId, String startDate,
			String startTime, String endTime) {
		Session session = super.getSessionFactory().getCurrentSession();
		Query query = session.getNamedQuery("locationProfile.checkLocationProfileTime");
		query.setParameter(0, startDate);
		query.setParameter(1, DateTimeUtil.DATE_PATTERN_SLASH);
		query.setParameter(2, startTime);
		query.setParameter(3, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(4, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(5, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(6, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(7, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(8, endTime);
		query.setParameter(9, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(10, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(11, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(12, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(13, DateTimeUtil.TIME_FORMAT_HH24_MI);
		query.setParameter(14, profileId);
		@SuppressWarnings("unchecked")
        List<LocationProfile> l = query.list();
		return l.size()==0?null:l.get(0);
	}
}
