package com.guitarcenter.scheduler.dao.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;

import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.CustomerStatusDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.CustomerStatusCriterion;
import com.guitarcenter.scheduler.model.CustomerStatus;
import com.guitarcenter.scheduler.model.Person;

@Repository("customerStatusDAO")
public class CustomerStatusDAOImpl extends AbstractDAOImpl<CustomerStatus> implements CustomerStatusDAO {

	public CustomerStatusDAOImpl() {
		super(CustomerStatus.class);
	}



	@Override
	protected void updateAuditor(CustomerStatus pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
	}



	@Override
	protected void fetchOne(CustomerStatus pResult, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
	}



	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, "updatedBy", FetchMode.JOIN, pCriteria);
		addFetchCriteria(FETCH_SITE, pFetchMode, "site", FetchMode.JOIN, pCriteria);
	}



	@Override
	protected void fetchMany(CustomerStatus pResult, int pFetchMode) {
	}



	@Override
	protected Criterion<CustomerStatus, CustomerStatus> getCriterionInstance() {
		return CustomerStatusCriterion.getInstance();
	}
}
