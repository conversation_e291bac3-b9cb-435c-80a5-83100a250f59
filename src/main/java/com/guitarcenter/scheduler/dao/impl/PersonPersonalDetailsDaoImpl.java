package com.guitarcenter.scheduler.dao.impl;


import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_SITE;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchCriteria;

import java.util.Date;

import org.hibernate.Criteria;
import org.springframework.stereotype.Repository;

import com.guitarcenter.scheduler.dao.PersonPersonalDetailsDao;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.PersonPersonalDetails;

//New class created for GSSP-275
@Repository("personPersonalDetailsDao")
public class PersonPersonalDetailsDaoImpl extends AbstractDAOImpl<PersonPersonalDetails> implements PersonPersonalDetailsDao {
	
	private PersonPersonalDetailsDaoImpl()
	{
		super(PersonPersonalDetails.class);
	}

	@Override
	protected void updateAuditor(PersonPersonalDetails pT, Person pUpdatedBy) {
		pT.setUpdated(new Date());
		pT.setUpdatedBy(pUpdatedBy);
		
	}

	@Override
	protected void fetchOne(PersonPersonalDetails pResult, int pFetchMode) {
		
		addFetchCriteria(FETCH_PERSON, pFetchMode, pResult.getPersonId());
		addFetchCriteria(FETCH_UPDATEBY_PERSON, pFetchMode, pResult.getUpdatedBy());
		addFetchCriteria(FETCH_SITE, pFetchMode, pResult.getSite());
		
		
	}

	@Override
	protected void fetchOne(Criteria pCriteria, int pFetchMode) {
		throw new UnsupportedOperationException();
	}

	@Override
	protected void fetchMany(PersonPersonalDetails pResult, int pFetchMode) {	
	}

	@Override
	protected Criterion<PersonPersonalDetails, PersonPersonalDetails> getCriterionInstance() {
		// TODO Auto-generated method stub
		return null;
	}

}
