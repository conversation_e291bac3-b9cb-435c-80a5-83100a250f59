package com.guitarcenter.scheduler.dao;

import java.util.List;

import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.model.Person;

public interface AbstractDAO<T> {

	public long save(T t, Person updatedBy);



	public long save(T t, long updatedById);



	public void update(T t, Person updatedBy);



	public void update(T t, long updatedById);



	public T get(long id);



	public void delete(T t);



	public T merge(T t);



	public <E> List<E> search(Criterion<T, E> criterion);



	public List<T> search(T example);



	public List<T> searchAll();



	public T get(long id, int fetchMode);



	public T merge(T t, int fetchMode);



	public <E> List<E> search(Criterion<T, E> criterion, int fetchMode);



	public List<T> search(T example, int fetchMode);



	public List<T> searchAll(int fetchMode);



	public <E> E get(Criterion<T, E> criterion, int fetchMode);



	public <E> E get(Criterion<T, E> criterion);



	public T get(T example);



	public T get(T example, int fetchMode);

}
