package com.guitarcenter.scheduler.dao.util;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.hibernate4.Hibernate4Module;

@Component("JSONMapper")
public class HibernateAwareObjectMapper extends ObjectMapper {

	private static final long	serialVersionUID	= 1896103568989745314L;



	public HibernateAwareObjectMapper() {
		registerModule(new Hibernate4Module());
	}



	public String toJSON(Object pObj) {
		String json = "";
		try {
			json = writeValueAsString(pObj);
		} catch (JsonProcessingException e) {
			json = pObj.toString();
		}
		return json;
	}
}
