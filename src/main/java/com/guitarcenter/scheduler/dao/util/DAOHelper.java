package com.guitarcenter.scheduler.dao.util;

import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.hibernate.Hibernate;

public final class DAOHelper {

	private DAOHelper() {
	}



	public static boolean hasCriteria(final int pExpect, final int pActual) {
		return (pActual & pExpect) == pExpect;
	}



	public static String addFetchHQL(final int pExpect, final int pActual, String pPropertyName, boolean isLeftJoin) {
		StringBuilder sb = new StringBuilder();
		if (hasCriteria(pExpect, pActual)) {
			if (isLeftJoin) {
				sb.append(" left ");
			}
			sb.append(" join fetch ");
			sb.append(pPropertyName);
			sb.append(" ");
		}
		return sb.toString();
	}



	public static void addFetchCriteria(int pExpect, int pActual, String pPropertyName, FetchMode pFetchMode,
			Criteria pCriteria) {
		if (hasCriteria(pExpect, pActual)) {
			pCriteria.setFetchMode(pPropertyName, pFetchMode);
		}
	}



	public static void addFetchCriteria(int pExpect, int pActual, Object pProperty) {
		if (hasCriteria(pExpect, pActual)) {
			Hibernate.initialize(pProperty);
		}
	}

	public static final int	FETCH_NONE					= 0;
	public static final int	FETCH_UPDATEBY_PERSON		= 1;
	public static final int	FETCH_MORE_INSTRUMENTS		= 1 << 1;
	public static final int	FETCH_MORE_CUSTOMERS		= 1 << 2;
	public static final int	FETCH_MORE_SERVICES			= 1 << 3;
	public static final int	FETCH_MORE_ACTIVITIES		= 1 << 4;
	public static final int	FETCH_SERVICE				= 1 << 5;
	public static final int	FETCH_INSTRUCTOR			= 1 << 6;
	public static final int	FETCH_SITE					= 1 << 7;
	public static final int	FETCH_ROOM					= 1 << 8;
	public static final int	FETCH_LOCATION_PROFILE		= 1 << 9;
	public static final int	FETCH_APPOINTMENT_SERIES	= 1 << 10;
	public static final int	FETCH_ACTIVITY				= 1 << 11;
	public static final int	FETCH_CUSTOMER_STATUS		= 1 << 12;
	public static final int	FETCH_PERSON				= 1 << 13;
	public static final int	FETCH_LOCATION				= 1 << 14;
	public static final int	FETCH_AVAILABILITY			= 1 << 15;
	public static final int	FETCH_ROLE					= 1 << 16;
	public static final int	FETCH_ROOM_SIZE				= 1 << 17;
	public static final int	FETCH_ROOM_TYPE				= 1 << 18;
	public static final int	FETCH_ROOM_NUMBER			= 1 << 19;
	public static final int	FETCH_ROOM_TEMPLATE			= 1 << 20;
	public static final int	FETCH_PARENT_ROOM			= 1 << 21;

}
