package com.guitarcenter.scheduler.controller;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import org.apache.solr.client.solrj.SolrServerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import com.guitarcenter.scheduler.common.util.ActivityAndServiceUtil;
import com.guitarcenter.scheduler.common.util.ActivityComparator;
import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.AvailabilityUtil;
import com.guitarcenter.scheduler.common.util.ServiceComparator;
import com.guitarcenter.scheduler.dto.ActivityFilterDTO;
import com.guitarcenter.scheduler.dto.InstructorInfoDTO;
import com.guitarcenter.scheduler.dto.RoomDTO;
import com.guitarcenter.scheduler.dto.SearchDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.LocationProfile;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileActivity;
import com.guitarcenter.scheduler.model.ProfileService;
import com.guitarcenter.scheduler.model.RoomTemplate;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.service.ActivityService;
import com.guitarcenter.scheduler.service.AvailabilityService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.LocationManagerService;
import com.guitarcenter.scheduler.service.LocationProfileService;
import com.guitarcenter.scheduler.service.ProfileLookupService;
import com.guitarcenter.scheduler.service.RoomService;
import com.guitarcenter.scheduler.service.RoomTemplateService;
import com.guitarcenter.scheduler.service.SearchService;
import com.guitarcenter.scheduler.service.ServiceService;
import com.guitarcenter.scheduler.service.SiteService;

/**
 * 
 * Controller for Location
 * 
 * <AUTHOR>
 * 
 */
@Controller
public class LocationController implements AppConstants {
	@Autowired
	private SiteService siteService;
	@Autowired
	private ActivityService activityService;	
	@Autowired
	private SearchService			searchService;

	@Autowired
	private LocationProfileService	locationProfileService;
	@Autowired
	private ServiceService	serviceService;

	@Autowired
	private LocationManagerService locationService;
	
	@Autowired
	private AvailabilityService availabilityService;
	
	@Autowired
	private InstructorService instructorService;
	
	@Autowired
	private RoomService roomService;
	
	@Autowired
	private RoomTemplateService roomTemplateService;
	//GSSP-228 Changes
	@Autowired
	private AvailabilityUtil availabilityUtil;
	
	@Autowired
	private ActivityAndServiceUtil activityAndServiceUtil;
	
	@Autowired
	ProfileService profileservice;
	
	//Added for GSSP-228
	@Autowired
	ProfileLookupService profileLookupService;
	
	@Autowired
	ProfileActivity profileActivity;

	private static final Logger		LOG										= LoggerFactory
																					.getLogger(LocationController.class);

	// Request Mapping constants
	private static final String		GO_ADMIN_PAGE_MAPPING					= "/adminPage.htm";
	private static final String		FIND_ALL_SITE_STUDIOS_MAPPING			= "location/findAllStudios.htm";
	private static final String		LOCATION_SEARCH_MAPPING					= "location/locationSearch.htm";
	private static final String		CREATE_PROFILE_MAPPING					= "createProfileFromLocation.htm";

	// Forward contants
	private static final String		ADMIN_PAGE_FORWARD						= "/studio_manage";
	private static final String FORWARD_TO_SCHEDULER_MAPPING 				= "forwardToScheduler.htm";

	// Redirect contants
	private static final String		CREATE_PROFILE_REDIRECT					= "beforeGoCalendar.htm";

	// massage constants
	private static final String		MESSAGE_KEY								= "msg";

	private static final String		SPACE									= " ";
	private static final String LOCATION_PROFILE_KEY   						= "locationProfileInf";

	private static final String ROOMS_KEY              = "roomList";
	private static final String ACTIVITIES_KEY 		   = "activityList";
	private static final String SERVICES_KEY 		   = "serviceList";
	private static final String INSTRUCTORS_KEY 	   = "instructorList";
	private static final String LIST_MAP_KEY 		   = "listMap";
	private static final String LOCATION_KEY		   = "locationInfo";
// Added for gssp-228
	private static final String STUDIO_HOURS_KEY	   = "studioHours";
	/**
	 * Just for page forward
	 * 
	 * @return
	 */
	@RequestMapping(GO_ADMIN_PAGE_MAPPING)
	//For GSSP-181, Pass the session
	public String forwardAdminPage(HttpSession session) {
		
		//For GSSP-181
		if(!session.isNew())
		{
			if(null != session.getAttribute(LOCATION_ID_SESSION_KEY))
			{	
				session.setAttribute(LOCATION_ID_SESSION_KEY, new Location());
			}
		}
		return ADMIN_PAGE_FORWARD;
	}

	@RequestMapping(FIND_ALL_SITE_STUDIOS_MAPPING)
	@ResponseBody
	public List<SearchDTO> findSiteLocationList() {
		String searchCriteria = "";
		return this.locationSearch(searchCriteria);
	}

	/**
	 * Search Location
	 * 
	 * @param searchCriteria
	 * @return
	 */
	@RequestMapping(LOCATION_SEARCH_MAPPING)
	@ResponseBody
	public List<SearchDTO> locationSearch(String searchCriteria) {
		long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY,
				RequestAttributes.SCOPE_SESSION);
		if (LOG.isDebugEnabled()) {
			LOG.debug("Began to search Locations by {} from LocationController.existLocationSearch", searchCriteria);
		}
		List<SearchDTO> dtos = new LinkedList<SearchDTO>();
		try {
			dtos = searchService.locationSearch(siteId, searchCriteria);
		} catch (Exception e) {
			LOG.error("Caught {} from LocationController.existLocationSearch", e);
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("Got search result {} from LocationController.existLocationSearch", dtos);
		}
		return dtos;
	}

	/**
	 * Create a LocationProfile from a specific Location
	 * 
	 * @param session
	 *            HttpSession
	 * @param locationId
	 *            id of the location
	 * @param locationName
	 *            name of the location
	 * @param externalId
	 *            externalId of the location
	 * @param map
	 *            Internal object of String MVC
	 * @return
	 * @throws ParseException 
	 */
	@RequestMapping(CREATE_PROFILE_MAPPING)
	public ModelAndView createProfileFromLocation(HttpSession session, String locationId, String locationName,
			String externalId, ModelMap map){
		long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(
				AppConstants.SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		Person person = (Person) RequestContextHolder.currentRequestAttributes().getAttribute(
				AppConstants.PERSON_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		if (LOG.isDebugEnabled()) {
			LOG.debug("Began to createProfile with locationId:{} and siteId:{} from LocationController.createProfileFromLocation", locationId, siteId);
		}
		LocationProfile l = locationProfileService.createProfileFromLocation(Long.parseLong(locationId), siteId, person);

		if (null != l) {
			Site site = new Site();
			site.setSiteId(siteId);

			Location location = new Location();
			location.setSite(site);
			location.setUpdatedBy(person);
			location.setUpdated(new Date());
			location.setLocationId(Long.parseLong(locationId));
			location.setLocationName(locationName);
			location.setExternalId(externalId);
			location.setLocationProfile(l);

			try {
				searchService.updateRecord(location);// Update the document of location when created the  locationProfile
			} catch (SolrServerException e) {
				LOG.error("Caught {} when updating solr document of location:{}", e, location);
			} catch (IOException e) {
				LOG.error("Caught {} when updating solr document of location:{}", e, location);
			}
			location = locationService.findById(Long.parseLong(locationId));
			session.setAttribute(LOCATION_ID_SESSION_KEY, location);
			session.setAttribute(LOCATION_PROFILE_ID_SESSION_KEY, l.getProfileId());
			session.setAttribute(AppConstants.SITE_ID_SESSION_KEY, siteId);

			map.addAttribute("locationInfo", location);
		}

		String msgValue = externalId + SPACE + locationName + SPACE + "studio profile created with default values";
		
		Map<String, Object> listMap = new HashMap<String, Object>();
		long profileId = l.getProfileId();
		map = availabilityService.getMap(profileId, map);
		//added for GSSP-228
		Map<String,List<String>> studioTemplateMap = profileLookupService.getProfileLookUpValues();
		
		List<Availability> dtow = new LinkedList<Availability>();
		Site site = siteService.getSiteById(siteId);
		Availability updateAvailability = null;
		try {
			
			if(studioTemplateMap.containsKey("studioTimings"))
				updateAvailability = availabilityUtil.getAvailabiltiyDefaultTemplate(l,site,siteService,person,studioTemplateMap.get("studioTimings"));
		} catch (ParseException e) {
			LOG.error("Caught {} when getAvailabiltiyDefaultTemplate");
		}
		if(null != updateAvailability)
		availabilityService.update(updateAvailability , person.getPersonId());
		
		if(null != site && null != person && null != locationProfileService && null != serviceService && null != profileservice
				&& studioTemplateMap.containsKey("serviceList") )
		activityAndServiceUtil.saveServiceDefaultTemplate(profileId,site,person,locationProfileService,serviceService,profileservice,studioTemplateMap.get("serviceList"));
		
		List<Service> sList = locationProfileService.findServiceByProfileIdAndEnabled(profileId, Enabled.Y);
		Collections.sort(sList, new ServiceComparator());
		if(null != site && null != person && null != locationProfileService && null != activityService && null != profileservice
				&&  studioTemplateMap.containsKey("activityList"))
		activityAndServiceUtil.saveActivitesDefaultTemplate(profileId,site,locationProfileService,activityService,person,profileActivity, studioTemplateMap.get("activityList"));
		//added for GSSP-228 ended
		
		// Sort the service list by serviceId
		List<Activity> aList = locationProfileService.findActivityByProfileIdAndEnabled(profileId, Enabled.Y);
		List<ActivityFilterDTO> dtos = new LinkedList<ActivityFilterDTO>();
        List<Long> ids = new ArrayList<Long>();
		if(null != aList && !aList.isEmpty()) {
			Collections.sort(aList, new ActivityComparator());//Sort the activity list by activityId
			for(Activity a : aList) {
				ActivityFilterDTO dto = new ActivityFilterDTO();
				dto.setActivityId(a.getActivityId());
				dto.setActivityName(a.getActivityName());
				dto.setExternalId(a.getExternalId());
				dtos.add(dto);
                ids.add(a.getActivityId());
			}
		}

        Long[] activityIds = ids.toArray(new Long[0]);

		//As the dao has changed the method of querying instructor and room
		List<InstructorInfoDTO> iList = instructorService.findByLocationIdAndActivityIds(Long.parseLong(locationId), activityIds);
		List<RoomDTO> rList = roomService.findByProfileIdAndActivityIds(profileId, activityIds);
		List<RoomTemplate> rtList = roomTemplateService.getRoomTemplateListBySiteId(siteId);
		Location locationInfo = locationService.findById(Long.parseLong(locationId));

		//Deliver profileInformation to page when created profile
		LocationProfile profile = locationProfileService.getLocationProfile(profileId);
		map.addAttribute(LOCATION_PROFILE_KEY, profile);
		
		listMap.put(ROOMS_KEY, rList);
		listMap.put(INSTRUCTORS_KEY, iList);
		listMap.put(ACTIVITIES_KEY, dtos);
		listMap.put(SERVICES_KEY, sList);
		listMap.put(STUDIO_HOURS_KEY, dtow);
		map.addAttribute(LIST_MAP_KEY, listMap);
		map.addAttribute("roomTemplateList", rtList);
		map.addAttribute(LOCATION_KEY, locationInfo);
		map.addAttribute(MESSAGE_KEY, msgValue);
		map.addAttribute("showClass", "show");
		//return new ModelAndView("/calendar", map);
		
		session.setAttribute(MESSAGE_KEY, msgValue);
		session.setAttribute("showClass", "show");
		
		RedirectView rv = new RedirectView();
		rv.setExposeModelAttributes(false);
		rv.setUrl(CREATE_PROFILE_REDIRECT);
		return new ModelAndView(rv, map);
	}
	
	@RequestMapping(FORWARD_TO_SCHEDULER_MAPPING)
	public ModelAndView forwardToScheduler(HttpSession session, long locationId) {
		Location db = locationService.findById(locationId);
		
		Location location = new Location();
		location.setLocationId(db.getLocationId());
		location.setLocationName(db.getLocationName());
		
		session.setAttribute(LOCATION_ID_SESSION_KEY, location);
		session.setAttribute(LOCATION_PROFILE_ID_SESSION_KEY, db.getLocationProfile().getProfileId());
		session.setAttribute(STORE_STRING, db.getExternalId());
		
		RedirectView rv = new RedirectView();
		rv.setExposeModelAttributes(false);
		rv.setUrl(CREATE_PROFILE_REDIRECT);
		return new ModelAndView(rv);
	}

}
