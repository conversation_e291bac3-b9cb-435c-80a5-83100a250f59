

package com.guitarcenter.scheduler.controller;


import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.SessionAttributes;
import org.springframework.web.servlet.ModelAndView;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.criterion.dto.AppointmentHistoryDTO;
import com.guitarcenter.scheduler.dto.CancelledAppointmentReportDTO;
import com.guitarcenter.scheduler.dto.CancelledAppointmentReportPagingDTO;
import com.guitarcenter.scheduler.dto.InstructorReportPagingDTO;
import com.guitarcenter.scheduler.dto.RehearsalBookingReportDTO;
import com.guitarcenter.scheduler.dto.RehearsalBookingReportParentDTO;
import com.guitarcenter.scheduler.dto.RehearsalScheduleReportDTO;
import com.guitarcenter.scheduler.dto.RehearsalScheduleReportParentDTO;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.service.ReportService;


@SessionAttributes({AppConstants.LOCATION_ID_SESSION_KEY, AppConstants.LOCATION_PROFILE_ID_SESSION_KEY, AppConstants.STORE_STRING})
@Controller

public class ExcelController implements AppConstants {
	
	 private static final Logger LOG = LoggerFactory.getLogger(ExcelController.class);
	 
	 @Autowired
	    private ReportService reportService;
	 
	 //REQUEST MAPPING
	 private static final String GENERATE_INSTRUCTOR_SCHEDULE_EXCEL_REPORT_MAPPING = "excel/instructorScheduleReport";
	 
	 private static final String GENERATE_INSTRUCTOR_APPOINTMENT_STATUS_SCHEDULE_EXCEL_REPORT_MAPPING = "excel/instructorAppointmentStatusScheduleReport";
	 
	 private static final String GENERATE_CANCELLED_APPOINTMENTS_EXCEL_REPORT_MAPPING = "excel/cancelledAppointments";
	 
	 private static final String GENERATE_INSTRUCTOR_OPEN_APPOINTMENTS_EXCEL_REPORT_MAPPING = "excel/instructorOpenAppointmentsReport";
	 
	 private static final String GENERATE_CONFLICTING_APPOINTMENTS_EXCEL_REPORT_BY_INSTRUCTOR_MAPPING = "excel/conflictingAppointmentsReportByInsrtuctor";
	 
	private static final String GENERATE_CONFLICTING_APPOINTMENTS_EXCEL_REPORT_BY_ROOM_MAPPING = "excel/conflictingAppointmentsReportByRoom";
	
	private static final String GENERATE_INSTRUCTOR_OUTSIDE_APPOINTMENTS_EXCEL_REPORT_MAPPING = "excel/instructorOutsideAppointmentsReport";
	
	private static final String GENERATE_REHEARSAL_BOOKING_EXCEL_REPORT_MAPPING = "excel/rehearsalBookingReport";
	
	private static final String GENERATE_REHEARSAL_SCHEDULE_EXCEL_REPORT_MAPPING = "excel/rehearsalScheduleReport";
	 
	//Added new active students report  GSSP-185
	private static final String GENERATE_ACTIVE_STUDENTS_EXCEL_REPORT_MAPPING = "excel/activeStudentsReport";
	//Added new active students report  GSSP-203
	private static final String GENERATE_STUDENT_CHECK_IN_EXCEL_REPORT_MAPPING = "excel/studentCheckInReport";
	//Added new iactive students report  GSSP-205
		private static final String GENERATE_INACTIVE_STUDENTS_REPORT_MAPPING = "excel/inActveStudentsReport";
		//Added new  report  GSSP-213
				private static final String GENERATE_PROFILE_DETAILS_EXCEL_REPORT_MAPPING = "excel/excelProfileReport";
	//Added new  report  GSSP-210
	private static final String GENERATE_APPOINTMENT_HISTORY_REPORT_MAPPING = "excel/appointmentHistoryReport";
		
	//EXCEL MAPPING
	 private static final String INSTRUCTOR_SCHEDULE_REPORT_EXCEL_URL = "instructorScheduleReport";
	 
	 private static final String INSTRUCTOR_SCHEDULE_STATUS_REPORT_EXCEL_URL = "instructorAppointmentStatusScheduleReport";
	 
	 private static final String CANCELLED_APPOINTMENTS_REPORT_EXCEL_URL = "cancelledAppointments";
	 
	 private static final String INSTRUCTOR_OPEN_APPOINTMENTS_REPORT_EXCEL_URL = "instructorOpenAppointmentsReport";
	 
	 private static final String CONFLICT_APPOINTMENTS_BY_INSTRUCTOR_REPORT_EXCEL_URL = "conflictAppointmentsReportByInsrtuctor";
	 
	  private static final String CONFLICT_APPOINTMENTS_BY_ROOM_REPORT_EXCEL_URL = "conflictAppointmentsReportByRoom";
	  
	  private static final String INSTRUCTOR_OUTSIDE_APPOINTMENTS_REPORT_EXCEL_URL = "instructorOutsideAppointmentsReport";
	  
	  private static final String REHEARSAL_BOOKING_REPORT_EXCEL_URL = "rehearsalBookingReport";
	  
	  private static final String REHEARSAL_SCHEDULE_REPORT_EXCEL_URL = "rehearsalScheduleReport";
	//Added new active students report  GSSP-185
	  private static final String ACTIVE_STUDENTS_REPORT_EXCEL_URL = "activeStudentsReport";
	//Added new active students report  GSSP-203
	  private static final String STUDENT_CHECK_IN_EXCEL_URL = "studentCheckInReport";
	//Added new active students report  GSSP-205
	  private static final String INACTIVE_STUDENTS_EXCEL_URL = "inActveStudentsReport";

	//ADDED FOR GSSP-213
	  private static final String PROFILES_DETAIL_EXCEL_URL = "profileExcelReport";
	 
	//ADDED FOR GSSP-210
	  private static final String APPOINTMENT_HISTORY_EXCEL_URL = "appointmentHistoryReport"; 
	 
	 
	 
	 /********** EXCEL REPORT FOR INSTRUCTOR SCHEDULE *******************/
	  

		@RequestMapping(GENERATE_INSTRUCTOR_APPOINTMENT_STATUS_SCHEDULE_EXCEL_REPORT_MAPPING)
		@ResponseBody
		public ModelAndView generateInstructorAppointmentStatusExcelReport(
				@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,HttpServletRequest request,HttpServletResponse response,String startDate, String endDate) {
			
			if (LOG.isDebugEnabled()) {
				LOG.debug("query enabled instructor list by locationId:{}",
						location.getLocationId());
			}
			
			Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
			Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

			List<InstructorReportPagingDTO> parent=reportService.generateInstructorAppointmentStatusReport(
					location.getLocationId(), queryStartDate, queryEndDate);
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("reportType", INSTRUCTOR_SCHEDULE_STATUS_REPORT_EXCEL_URL);
			paramMap.put("instructorAppointmentStatusScheduleReport", parent);
			return new ModelAndView("excelView",paramMap);
		
		}
		
		@RequestMapping(GENERATE_INSTRUCTOR_SCHEDULE_EXCEL_REPORT_MAPPING)
		@ResponseBody
		public ModelAndView generateMasterScheduleExcelReport(
				@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,HttpServletRequest request,HttpServletResponse response,String startDate, String endDate) {
			
			if (LOG.isDebugEnabled()) {
				LOG.debug("query enabled instructor list by locationId:{}",
						location.getLocationId());
			}
			
			Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
			Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

			List<InstructorReportPagingDTO> parent=reportService.generateInstructorReportPDF(
					location.getLocationId(), queryStartDate, queryEndDate, true);
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("reportType", INSTRUCTOR_SCHEDULE_REPORT_EXCEL_URL);
			paramMap.put("instructorScheduleReport", parent);
			return new ModelAndView("excelView",paramMap);
		
		}
		
		/********** EXCEL REPORT FOR CANCELLED APPOINTEMENTS *******************/
		@SuppressWarnings("unchecked")
		@RequestMapping(GENERATE_CANCELLED_APPOINTMENTS_EXCEL_REPORT_MAPPING)
	    public ModelAndView cancelledAppointments(@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long profileId,
	            String startDate, String endDate, HttpServletRequest request) {
	    	 
	         
	    	 Map<String, Object> paramMap = new HashMap<String, Object>();
	         
	         Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
	         Date queryEndDate = DateTimeUtil.handleEndDate(endDate);
	         
	         List<CancelledAppointmentReportPagingDTO> parent = new ArrayList<CancelledAppointmentReportPagingDTO>();
	         
	         List<Map<String, Object>> dtos = reportService.findCancelledAppointmentReport(profileId, queryStartDate, queryEndDate, false);
	         for(Map<String, Object> dto : dtos) {
	        	 CancelledAppointmentReportPagingDTO child = new CancelledAppointmentReportPagingDTO();
	        	 child.setDate1((String)dto.get(ReportService.REPORT_DATE_KEY));
	        	 child.setList((List<CancelledAppointmentReportDTO>) dto.get(ReportService.REPORT_DTO_LIST_KEY));
	        	 parent.add(child);
	         }
	         
	         	paramMap.put("reportType", CANCELLED_APPOINTMENTS_REPORT_EXCEL_URL);
				paramMap.put("cancelledAppointmentReport", parent);
	        
				return new ModelAndView("excelView",paramMap);
	    }
		
		/************ EXCEL REPORT FOR INSTRUCTOR OPEN APPOINTMENTS *****************/
		  //GSSP-190 New field added-InstructorName
		 @RequestMapping(GENERATE_INSTRUCTOR_OPEN_APPOINTMENTS_EXCEL_REPORT_MAPPING)
		    public ModelAndView printInstructorOpenAppointmentsReportToPDF(@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,
		                                                           String startDate, String endDate, String inputExternalId, String instructorName, HttpServletRequest request) {
		        

		        if (LOG.isDebugEnabled()) {
		            LOG.debug("Try to generate InstructorScheduleReportToPDF between startDate {} and endDate {} for inputExternalId", startDate, endDate, inputExternalId);
		        }

		        Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
		        Date queryEndDate = DateTimeUtil.handleEndDate(endDate);
		        String queryinputExternalId = SystemUtil.nameRegular(inputExternalId);
		        
		        //GSSP-190 New field added-InstructorName

		        List<InstructorReportPagingDTO> parent = reportService.generateInstructorOpenAppointmentsReportPDF(location.getLocationId(), queryStartDate, queryEndDate, queryinputExternalId, true, instructorName);

		        Map<String, Object> paramMap = new HashMap<String, Object>();
		        
		        paramMap.put("reportType", INSTRUCTOR_OPEN_APPOINTMENTS_REPORT_EXCEL_URL);
		        paramMap.put("instructorOpenAppointmentsReport", parent);

		        return new ModelAndView("excelView", paramMap);
		    }
		 
		 /************ EXCEL REPORT FOR CONFLICT APPOINTMENTS BY INSTRUCTOR *****************/
		 
		 @RequestMapping(GENERATE_CONFLICTING_APPOINTMENTS_EXCEL_REPORT_BY_INSTRUCTOR_MAPPING)
		    public ModelAndView printConflictAppointmentsByInstructorReportToPDF(@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long profileId,
		                                                           String startDate, String endDate, HttpServletRequest request) {
		       

		        if (LOG.isDebugEnabled()) {
		            LOG.debug("Try to generate ConflictAppointmentsByInstructorReportToPDF between startDate {} and endDate {}", startDate, endDate);
		        }

		        Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
		        Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

		        List<InstructorReportPagingDTO> parent = reportService.generateConflictAppointmentsReportByInstructor(profileId, queryStartDate, queryEndDate);

		        Map<String, Object> paramMap = new HashMap<String, Object>();
		        paramMap.put("reportType", CONFLICT_APPOINTMENTS_BY_INSTRUCTOR_REPORT_EXCEL_URL);
		        paramMap.put("conflictingAppointmentsReportByInsrtuctor", parent);

		        return new ModelAndView("excelView", paramMap);
		    }
		    
		 /************ EXCEL REPORT FOR CONFLICT APPOINTMENTS BY ROOM *****************/
		    
		    @RequestMapping(GENERATE_CONFLICTING_APPOINTMENTS_EXCEL_REPORT_BY_ROOM_MAPPING)
		    public ModelAndView printConflictAppointmentsByRoomrReportToPDF(@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long profileId,
		                                                           String startDate, String endDate, HttpServletRequest request) {
		       

		        if (LOG.isDebugEnabled()) {
		            LOG.debug("Try to generate ConflictAppointmentsByRoomrReportToPDF between startDate {} and endDate {}", startDate, endDate);
		        }

		        Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
		        Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

		        List<InstructorReportPagingDTO> parent = reportService.generateConflictAppointmentsReportByRoom(profileId, queryStartDate, queryEndDate);

		        Map<String, Object> paramMap = new HashMap<String, Object>();
		        paramMap.put("reportType", CONFLICT_APPOINTMENTS_BY_ROOM_REPORT_EXCEL_URL);
		        paramMap.put("conflictingAppointmentsReportByRoom", parent);

		        return new ModelAndView("excelView", paramMap);
		    }
		
		    /************ EXCEL REPORT FOR OUTSIDE APPOINTMENTS *****************/
		    
		    @RequestMapping(GENERATE_INSTRUCTOR_OUTSIDE_APPOINTMENTS_EXCEL_REPORT_MAPPING)
		    public ModelAndView printInstructorOutsideAppointmentsReportToPDF(@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long profileId,
		    													String tFrom, String tTo, String inputExternalId, String dayType, HttpServletRequest request) {
		       

		        if (LOG.isDebugEnabled()) {
		            LOG.debug("Try to generate InstructorScheduleReportToPDF between startTime {} and endTime {} for inputExternalId {} for dayType {}", tFrom, tTo, inputExternalId, dayType);
		        }

		        String queryStartTime = SystemUtil.nameRegular(tFrom);
				String queryEndTime = SystemUtil.nameRegular(tTo);
				String queryinputExternalId = SystemUtil.nameRegular(inputExternalId);
				String querydayType = SystemUtil.nameRegular(dayType);
				
				
		        List<InstructorReportPagingDTO> parent = reportService.generateInstructorOutsideAppointmentsReportPDF(profileId, queryStartTime, queryEndTime, queryinputExternalId, querydayType);

		        Map<String, Object> paramMap = new HashMap<String, Object>();
		       
		        paramMap.put("reportType", INSTRUCTOR_OUTSIDE_APPOINTMENTS_REPORT_EXCEL_URL);
		        paramMap.put("instructorOutsideAppointmentsReport", parent);

		        return new ModelAndView("excelView", paramMap);
		    }
		    
		    /************ EXCEL REPORT FOR REHAERSAL BOOKING  APPOINTMENTS *****************/
		    
		    @RequestMapping(GENERATE_REHEARSAL_BOOKING_EXCEL_REPORT_MAPPING)
		    @SuppressWarnings("unchecked")
		    public ModelAndView rehearsalBooking(@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long profileId,
		                                         String startDate, String endDate, HttpServletRequest request) {
		        
		        Map<String, Object> modelMap = new HashMap<String, Object>();

		        List<RehearsalBookingReportParentDTO> rehearsalBookingReportParentDTOs = new ArrayList<RehearsalBookingReportParentDTO>();
		        Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
		        Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

		        List<Map<String, Object>> dtos = reportService.generateRehearsalBookingReport(profileId, queryStartDate, queryEndDate);

		        for (Map<String, Object> dto : dtos) {
		            RehearsalBookingReportParentDTO child = new RehearsalBookingReportParentDTO();
		            child.setCurrentDate((String) dto.get(ReportService.REPORT_DATE_KEY));
		            child.setList((List<RehearsalBookingReportDTO>) dto.get(ReportService.REPORT_DTO_LIST_KEY));
		            rehearsalBookingReportParentDTOs.add(child);
		        }
		        
		        modelMap.put("reportType", REHEARSAL_BOOKING_REPORT_EXCEL_URL);
		        modelMap.put("rehearsalBookingReport", rehearsalBookingReportParentDTOs);
		        
		        return new ModelAndView("excelView", modelMap);
		    }
		
		    /************ EXCEL REPORT FOR REHAERSAL SHEDULE  APPOINTMENTS *****************/
		    
		    @RequestMapping(GENERATE_REHEARSAL_SCHEDULE_EXCEL_REPORT_MAPPING)
		    @SuppressWarnings("unchecked")
		    public ModelAndView rehearsalScheduleReport(@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long profileId,
		                                                String startDate, String endDate, HttpServletRequest request) {
		        Map<String, Object> modelMap = new HashMap<String, Object>();

		        List<RehearsalScheduleReportParentDTO> rehearsalScheduleReportParentDTOs = new ArrayList<RehearsalScheduleReportParentDTO>();
		        Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
		        Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

		        List<Map<String, Object>> dtos = reportService.generateRehearsalScheduleReport(profileId, queryStartDate, queryEndDate);

		        for (Map<String, Object> dto : dtos) {
		            RehearsalScheduleReportParentDTO child = new RehearsalScheduleReportParentDTO();
		            child.setCurrentDate((String) dto.get(ReportService.REPORT_DATE_KEY));
		            child.setList((List<RehearsalScheduleReportDTO>) dto.get(ReportService.REPORT_DTO_LIST_KEY));
		            rehearsalScheduleReportParentDTOs.add(child);
		        }
		        
		        modelMap.put("reportType", REHEARSAL_SCHEDULE_REPORT_EXCEL_URL);
		        modelMap.put("rehearsalScheduleReport", rehearsalScheduleReportParentDTOs);
		        
		        return new ModelAndView("excelView", modelMap);
		    }

		    /************ EXCEL REPORT FOR ACTIVE STUDENTS GSSP-185*****************/
			  //New ACTIVE STUDENTS Report GSSP-185
			 @RequestMapping(GENERATE_ACTIVE_STUDENTS_EXCEL_REPORT_MAPPING)
			 public ModelAndView generateActiveStudentsReport(
						@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,
						String startDate, String endDate, String activityType, String instructorName) {
			        if (LOG.isDebugEnabled()) {
			            LOG.debug("Try to generate ActiveStudentsReport between startDate {} and endDate {} for inputExternalId", startDate, endDate, activityType);
			        }

			        Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
			        Date queryEndDate = DateTimeUtil.handleEndDate(endDate);
					String queryactivityType = SystemUtil.nameRegular(activityType);
			        
			        List<InstructorReportPagingDTO> parent = reportService.generateActiveStudentsReport(location.getLocationId(), queryStartDate, queryEndDate,queryactivityType,  true, instructorName);
					
			        Map<String, Object> paramMap = new HashMap<String, Object>();
			        
			        paramMap.put("reportType", ACTIVE_STUDENTS_REPORT_EXCEL_URL);
			        paramMap.put("activeStudentsReport", parent);

			        return new ModelAndView("excelView", paramMap);
			    }
			  /************ EXCEL REPORT FOR  STUDENT CHECK IN SHEET GSSP-203*****************/
			  //New STUDENT Report GSSP-203
			 @RequestMapping(GENERATE_STUDENT_CHECK_IN_EXCEL_REPORT_MAPPING)
			 public ModelAndView generateStudentCheckInReport(
						@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,HttpServletRequest request,HttpServletResponse response,
						String startDate, String endDate) {
			        if (LOG.isDebugEnabled()) {
			            LOG.debug("Try to generate StudentCheckInReport between startDate {} and endDate {} ", startDate, endDate);
			        }

			        Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
			        Date queryEndDate = DateTimeUtil.handleEndDate(endDate);
					
			        
			        List<InstructorReportPagingDTO> parent = reportService.generateStudentCheckInReport(location.getLocationId(), queryStartDate, queryEndDate, true);
					
			        Map<String, Object> paramMap = new HashMap<String, Object>();
			        
			        paramMap.put("reportType", STUDENT_CHECK_IN_EXCEL_URL);
			        paramMap.put("studentCheckInReport", parent);

			        return new ModelAndView("excelView", paramMap);
			    }
			 /************ EXCEL REPORT FOR  INACTIVE STUDENT GSSP-205*****************/
			  //New INACTIVE STUDENT Report GSSP-205
			 @RequestMapping(GENERATE_INACTIVE_STUDENTS_REPORT_MAPPING)
			 public ModelAndView generateInActiveStudentsReport(
						@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,HttpServletRequest request,HttpServletResponse response,@ModelAttribute(AppConstants.STORE_STRING)
						String storeNumber) {
			        if (LOG.isDebugEnabled()) {
			            LOG.debug("Try to generate InActiveStudentsReport ",location);
			        }

			       
			        
			        List<InstructorReportPagingDTO> parent = reportService.generateInActiveStudentReport(storeNumber,location.getLocationId().toString(),true);
					
			        Map<String, Object> paramMap = new HashMap<String, Object>();
			        
			        paramMap.put("reportType", INACTIVE_STUDENTS_EXCEL_URL);
			        paramMap.put("inActveStudentsReport", parent);

			        return new ModelAndView("excelView", paramMap);
			    }
			 /********** EXCEL REPORT FOR PROFILE DETAILS *******************/
				
				@RequestMapping(GENERATE_PROFILE_DETAILS_EXCEL_REPORT_MAPPING)
			
				public ModelAndView generateAppointmentHistoryReport(
						@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,String startDate, String endDate, String Ex) {
					
					if (LOG.isDebugEnabled()) {
						LOG.debug("query enabled instructor list by locationId:{}",
								location.getLocationId());
					}
					
					Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
					Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

					List<InstructorReportPagingDTO> parent=reportService.generateProfileDetailsReport(queryStartDate, queryEndDate);
					Map<String, Object> paramMap = new HashMap<String, Object>();
					paramMap.put("reportType", PROFILES_DETAIL_EXCEL_URL);
					paramMap.put("profileExcelReport", parent);
					return new ModelAndView("excelView",paramMap);
				
				}
				/********** EXCEL REPORT FOR APPOINTMENT HISTORY GSSP-213 *******************/
				
				@RequestMapping(GENERATE_APPOINTMENT_HISTORY_REPORT_MAPPING)
			
				public ModelAndView generateProfileDetailsReport(
						@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,String startDate, String endDate,String externalId,HttpServletRequest request) {
					
					if (LOG.isDebugEnabled()) {
						LOG.debug("query enabled instructor list by locationId:{}",
								location.getLocationId());
					}
					
					
					
					Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
					Date queryEndDate = DateTimeUtil.handleEndDate(endDate);
					String queryinputExternalId = SystemUtil.nameRegular(externalId);
					List<AppointmentHistoryDTO> parent=reportService.generateAppointmentHistoryReport(queryStartDate, queryEndDate, queryinputExternalId);
					Map<String, Object> paramMap = new HashMap<String, Object>();
					paramMap.put("reportType", APPOINTMENT_HISTORY_EXCEL_URL);
					paramMap.put("appointmentHistoryReport", parent);
					return new ModelAndView("excelView",paramMap);
				
				}

}


