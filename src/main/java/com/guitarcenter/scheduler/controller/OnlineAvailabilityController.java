package com.guitarcenter.scheduler.controller;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.poi.util.SystemOutLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.SessionAttributes;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.AvailabilityUtil;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.AvailabilityUtil;
import com.guitarcenter.scheduler.dao.AvailabilityDAO;
import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.criterion.AvailabilityCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.InstructorCriterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dto.AvailabilityDTO;
import com.guitarcenter.scheduler.dto.EditHourShowDTO;
import com.guitarcenter.scheduler.dto.EditHourShowListDTO;
import com.guitarcenter.scheduler.dto.InstoreAvailableDTO;
import com.guitarcenter.scheduler.dto.OnLineAvailableDTO;
import com.guitarcenter.scheduler.dto.OnLineInstoreAvailableDTO;
import com.guitarcenter.scheduler.dto.OnetimeDTO;
import com.guitarcenter.scheduler.dto.StudioHourDTO;
import com.guitarcenter.scheduler.dto.UpdateMessageDTO;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.Onetime;
import com.guitarcenter.scheduler.model.OnlineAvailability;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.service.AvailabilityService;
import com.guitarcenter.scheduler.service.InstoreAvailabilityService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.OnetimeService;
import com.guitarcenter.scheduler.service.OnlineAvailabilityService;
import com.guitarcenter.scheduler.service.SiteService;
import com.guitarcenter.scheduler.service.ValidationService;

@SessionAttributes({"person","locationProfileId","siteId"})
@RequestMapping("/")
@Controller
public class OnlineAvailabilityController implements AppConstants {

	private static final Logger LOG = LoggerFactory.getLogger(OnlineAvailabilityController.class);
 
	@Autowired
	private OnlineAvailabilityService onlineAvailabilityService;
	
	@Autowired
	private InstoreAvailabilityService instoreAvailabilityService;
	
	private static final String MESSAGE_KEY = "message";
	private static final String STATUS_KEY = "status";
 
	@Autowired
	private InstructorService instructorService;
	//GSSP-211 CHANGES
	@Resource
	private AvailabilityService availabilityService;
	
	@Resource
	private SiteService siteService;
	
	@ResponseBody
	//@RequestMapping(value = "/onlineavailablity/onlineavailablitytimes.htm")
	@RequestMapping(value = "/onetime/loadonetimes.htm")
	public Map<String, Object> getOnlineAvaiablity(long instructorId) {
		
		Map<String, Object> map = new HashMap<String, Object>();
		//instructorId = 535L;
		//535
		//List<OnlineAvailabilityService> onlineAvailabilities = onlineAvailabilityService.getOnlineAvailabilityByInstructorId(instructorId);
		onlineAvailabilityService.getOnlineAvailabilityByInstructorId(instructorId);
		
		
		return map;
	
	}
	
	@ResponseBody
	@RequestMapping(value = "/onetime/loadOnLineTimes.htm")
	public Map<String, Object> getOnetimeDtos(long instructorId) {
		Map<String, Object> map = new HashMap<String, Object>();
		List<OnlineAvailability> onetimes = onlineAvailabilityService.getOnlineAvailabilityByInstructorId(instructorId);
		List<OnLineAvailableDTO> onetimeDtos = new ArrayList<OnLineAvailableDTO>();
		for (OnlineAvailability dto : onetimes) {
			OnLineAvailableDTO onetimeDto = OnlineAvlDateToString(dto);
			onetimeDtos.add(onetimeDto);
		}
		map.put("onlinetimes", onetimeDtos);
		return map;
	}
	
	
	@RequestMapping("/onetime/updateOnLine.htm")
	@ResponseBody
	public Map<String, Object> updateOnlineAvailability(@RequestBody OnlineAvailability dto) throws RuntimeException, ParseException{
		if(LOG.isDebugEnabled()){
			LOG.debug("OnetimeController.updateOnetime: start");
		}
		Map<String, Object> map = new HashMap<String, Object>();
		Person person = (Person) RequestContextHolder.currentRequestAttributes().getAttribute("person", RequestAttributes.SCOPE_SESSION);
		//GSSP-211 CHANGES
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		Location location = (Location) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		
		
		Instructor instructor1 = instructorService.getInstructor(dto.getInstructor().getInstructorId());
		
		Availability availability =availabilityService.getAvailability(Long.valueOf(instructor1.getAvailability().getAvailabilityId()));
	    
        try {
			dto.setUpdatedBy(person);
			onlineAvailabilityService.saveOnlineAvailablity(dto);
			
		} catch (Exception e) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Create Online Availability Failed");
			LOG.error("Caught an exception from OnlineAvailability.updateOnlineAvailabliity: {}", e);
			return map;
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("OnlineAvailability.updateOnlineAvailabliity: end");
		}
		return map;
	}
 
	@ResponseBody
	@RequestMapping(value = "onetime/deleteOnLineTime.htm")
	public Map<String, Object> deleteOnlineTimeById(long onLineTimeId) {
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			if(!onlineAvailabilityService.deleteOnLineTime(onLineTimeId)){
				map.put(STATUS_KEY, false);
				map.put(MESSAGE_KEY, "One time can't be deleted due to existing appointments");
				return map;
			}
			map.put(STATUS_KEY, true);
		} catch (Exception e) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Delete one time availablity Failed");
			LOG.error("Caught an exception from OnetimeController.deleteOneTimeById: {}", e);
			return map;
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("OnetimeController.deleteOneTimeById: end");
		}
		return map;
	}
	
   
	private static final String RETURN_DTO = "dto";

			@RequestMapping("/onetime/updateOnLineAvailable.htm")
			@ResponseBody
			public Map<String, Object> updateOnLineAvailabl(@RequestBody OnLineAvailableDTO dto) throws RuntimeException, ParseException
			{
				if(LOG.isDebugEnabled()){
					LOG.debug("OnetimeController.updateOnetime: start");
				}
				Map<String, Object> map = new HashMap<String, Object>();
				Person person = (Person) RequestContextHolder.currentRequestAttributes().getAttribute("person", RequestAttributes.SCOPE_SESSION);
				//GSSP-211 CHANGES
				long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
				Location location = (Location) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
				long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
				Site site = new Site();
				site.setSiteId(siteId);
				Instructor instructor1 = instructorService.getInstructor(dto.getInstructorId());
				
				Availability availability = instructor1.getAvailability();
				String dayStr = dto.getWeekDay();
				OnLineAvailableDTO onl = null;
				 try {
					onl = getDayAvailabilityStr(dto.getWeekDay(),availability);
				} catch (Exception e2) {
					map.put(STATUS_KEY, false);
					map.put(MESSAGE_KEY,"Online Availabilty will be create after the Instructor Availabilty");
					LOG.error("Caught an exception from OnlineAvailability Exception {}", e2);
					return map;
				}
				 
				List<OnLineAvailableDTO>  onlineAvlList = onlineAvailabilityService.getOnlineAvlFormatByInstructorId(dto.getInstructorId());
				List<InstoreAvailableDTO>  instoreAvlList = instoreAvailabilityService.getInstoreAvlFormatByInstructorId(dto.getInstructorId());
				
				List<OnLineInstoreAvailableDTO>  alllist = new ArrayList<OnLineInstoreAvailableDTO>();
				for(OnLineAvailableDTO onls: onlineAvlList){
					if(dayStr.equals(onls.getWeekDay())){
					OnLineInstoreAvailableDTO allDto= new OnLineInstoreAvailableDTO();
					 allDto.setFromTime(onls.getFromTime());
					 allDto.setToTime(onls.getToTime());
					 allDto.setServiceMode("Online");
					 alllist.add(allDto);
					}
				}
				for(InstoreAvailableDTO onls: instoreAvlList){
					if(dayStr.equals(onls.getWeekDay())){
					OnLineInstoreAvailableDTO allDto= new OnLineInstoreAvailableDTO();
					 allDto.setFromTime(onls.getFromTime());
					 allDto.setToTime(onls.getToTime());
					 allDto.setServiceMode("Instore");
					 alllist.add(allDto);
					}
				}
				
				
				
				Collections.sort(alllist);
				alllist.stream().map(s -> s.getFromTime()).forEach(System.out::print);


		        
				
				//List<InstoreAvailableDTO>  instoreAvlList = instoreAvailabilityService.getInstoreAvlFormatByInstructorId(dto.getInstructorId());
				boolean flag=false;
				try {
					 //flag= checkSlotUnderInstructorAvailabily(dto.getFromTime(),dto.getToTime(),onl.getFromTime(),onl.getToTime());
					 flag= checkSlotUnderInstructorAvlty(dto.getFromTime(),dto.getToTime(),onl.getFromTime(),onl.getToTime());
					 if(!flag)map.put(MESSAGE_KEY, "Online Availability conflicts with Instructor's availability !");
					int cnt =1;
					String fromTime ="";

					//---++++++++++++++++++++++++++++++++++++++++++
				 
					int listVal =alllist.size();
					if(listVal>0){
					for(OnLineInstoreAvailableDTO onls: alllist){
						if(cnt == 1){
						flag= checkSlotUnderInstructorAvlty(dto.getFromTime(),dto.getToTime(),onl.getFromTime(),onls.getFromTime());
						if(!flag)map.put(MESSAGE_KEY, "Online Availability conflicts with Instructor's In-Store availability !");
						fromTime =onls.getToTime();
						}else{
							flag= checkSlotUnderInstructorAvlty(dto.getFromTime(),dto.getToTime(),fromTime,onls.getFromTime());	
							fromTime =onls.getToTime();
							String msg = getDayStr(onls.getWeekDay())+" "+onls.getFromTime()+" "+onls.getToTime()+"("+onls.getServiceMode()+")";
							if(!flag)map.put(MESSAGE_KEY, "Online Availability conflicts with Online/In-Store availability ");
					 
						}
						 
						cnt++;
						if(flag)break;
					}
					}
					
					listVal =1+listVal;
					if(cnt == listVal){
						if(!flag){
						flag= checkSlotUnderInstructorAvlty(dto.getFromTime(),dto.getToTime(),fromTime,onl.getToTime());
						if(!flag)map.put(MESSAGE_KEY, "Online Availability conflicts with Instructor's Online/In-Store availability !");
						}
					}
 
					
				} catch (Exception e1) {
					// TODO Auto-generated catch block
					 LOG.error("Caught an exception from onlineAvailability service {}", e1);
				}
				
				OnlineAvailability onlineAvailability =null;
				try {
					  onlineAvailability = getOnetime(dto, person);
				} catch (Exception e) {
					// TODO Auto-generated catch block
					 LOG.error("Caught an exception from onlineAvailability service {}", e);
				}
				 try {
						 
					 onlineAvailability.setSite(site);
					 if(flag){
						 onlineAvailabilityService.saveOnlineAvailablity(onlineAvailability); 
						 List<OnLineAvailableDTO> onlineAvailabilityList = onlineAvailabilityService.getOnlineAvailabilityDtoByInstructorId(dto.getInstructorId());
						 
						    map.put(RETURN_DTO, onlineAvailabilityList);
							map.put(STATUS_KEY, true);
							map.put(MESSAGE_KEY, "Create One Time Availability Successfully!");
							
							
						}else{
							List<OnLineAvailableDTO> onlineAvailabilityList1 = onlineAvailabilityService.getOnlineAvailabilityDtoByInstructorId(dto.getInstructorId());
							 
							map.put(RETURN_DTO, onlineAvailabilityList1);
							map.put(STATUS_KEY, false);
							//map.put(MESSAGE_KEY, "Create On Line Availability Failed!");	
						}
					
					 
					 
					} catch (Exception e) {
						map.put(STATUS_KEY, false);
						 LOG.error("Caught an exception from search service {}", e);
						map.put(MESSAGE_KEY, "Create Online Availability Failed");
						LOG.error("Caught an exception from OnlineAvailability.updateOnlineAvailabliity: {}", e);
						return map;
					}
				Availability availability2 =availabilityService.getAvailability(Long.valueOf(instructor1.getAvailability().getAvailabilityId()));
			    
				UpdateMessageDTO updateMessageDTO = null;
			    
			   
			    Availability studioAvailability = availabilityService.findByProfileId(profileId);	
			    
			    Long studioAvailabilityID  = studioAvailability.getAvailabilityId();
			    
			    String extId  = "";
			    //GSSP-223 CHANGES
			    long secondaryInstructorId = -1;
			    
			    List<Availability> secondaryAvailability = null;
			    
				if (LOG.isDebugEnabled()) {
					LOG.debug("OnetimeController.updateOnetime: end");
				}
				return map;
			}

			private String getDayStr(String num){
				Map<String,String> mb = new HashMap<String,String>();
				String output = "";
				mb.put("0", "Sun");
				mb.put("1", "Mon");
				mb.put("2", "Tue");
				mb.put("3", "Wed");
				mb.put("4", "Thu");
				mb.put("5", "Fri");
				mb.put("6", "Sat");
				output = mb.get(num);
				return output;
			 
			}
			private OnLineAvailableDTO OnlineAvlDateToString(OnlineAvailability onetime){
				String fromDate = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_DATE).format(onetime.getStartTime());
				String fromTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_TIME).format(onetime.getStartTime());
				String toTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_TIME).format(onetime.getEndTime());
				OnLineAvailableDTO onetimeDto = new OnLineAvailableDTO();
				onetimeDto.setStartDate(fromDate);
				onetimeDto.setFromTime(fromTime);
				onetimeDto.setToTime(toTime);
//				String startTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_FORMAT).format(onetime.getStartTime());
//				String endTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_FORMAT).format(onetime.getEndTime());
				onetimeDto.setInstructorId(onetime.getInstructor().getInstructorId());
				onetimeDto.setOnlineAvailabilityId(onetime.getOnlineAvailabilityId());
				onetimeDto.setOnetimeStartToEnd(getDayStr(onetime.getDay()) +": "+"" +fromTime+" - "+toTime);
				return onetimeDto;
			}			
			
			private OnlineAvailability getOnetime(OnLineAvailableDTO dto, Person person) throws Exception {
				OnlineAvailability onetime = new OnlineAvailability();
				Long instrctorId = dto.getInstructorId();
				Instructor instructor = instructorService.getInstructor(instrctorId);
				onetime.setInstructor(instructor);
				person.setPersonId(1l);
				SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
				Date date = new Date();
				String startDate = dateFormat.format(date);
				String startTime = dto.getFromTime();
				String endTime = dto.getToTime();
				Date start = null;
				Date end = null;
				try {
					start = new SimpleDateFormat(DateTimeUtil.DATETIME_PATTERN_SLASH).parse(startDate + " " + startTime);
					end = new SimpleDateFormat(DateTimeUtil.DATETIME_PATTERN_SLASH).parse(startDate + " " + endTime);
				} catch (ParseException e) {
					LOG.error("OnetimeController.getOnetime: parse date error");
					throw new Exception("Date parse error");
				}
 
				onetime.setStartTime(start);
				onetime.setEndTime(end);
				onetime.setUpdated(new Date());
				onetime.setUpdatedBy(person);
				onetime.setDay(dto.getWeekDay());
				return onetime;
			}
			
			
			public OnlineAvailability getDayAvailability(String day,Availability avl){
				OnlineAvailability onl = new OnlineAvailability();
				if("0".equals(day)){
					onl.setStartTime(avl.getSundayStartTime());
					onl.setEndTime(avl.getSundayEndTime());	
				}
				if("1".equals(day)){
					onl.setStartTime(avl.getMondayStartTime());
					onl.setEndTime(avl.getMondayEndTime());					
								}
				if("2".equals(day)){
					onl.setStartTime(avl.getTuesdayStartTime());
					onl.setEndTime(avl.getTuesdayEndTime());	
				}
				if("3".equals(day)){
					onl.setStartTime(avl.getWednesdayStartTime());
					onl.setEndTime(avl.getWednesdayEndTime());	
				}
				if("4".equals(day)){
					onl.setStartTime(avl.getThursdayStartTime());
					onl.setEndTime(avl.getThursdayEndTime());	
				}
				if("5".equals(day)){
					onl.setStartTime(avl.getFridayStartTime());
					onl.setEndTime(avl.getFridayEndTime());	
				}
				if("6".equals(day)){
					onl.setStartTime(avl.getSaturdayStartTime());
					onl.setEndTime(avl.getSaturdayStartTime());	
				}
				
				
				return onl;
			}
			
			public OnLineAvailableDTO getDayAvailabilityStr(String day,Availability avl){
				OnLineAvailableDTO onl = new OnLineAvailableDTO();
				if("0".equals(day)){
					onl.setFromTime(AvailabilityUtil.format24(avl.getSundayStartTime()));
					onl.setToTime(AvailabilityUtil.format24(avl.getSundayEndTime()));	
				}
				if("1".equals(day)){
					onl.setFromTime(AvailabilityUtil.format24(avl.getMondayStartTime()));
					onl.setToTime(AvailabilityUtil.format24(avl.getMondayEndTime()));					
								}
				if("2".equals(day)){
					onl.setFromTime(AvailabilityUtil.format24(avl.getTuesdayStartTime()));
					onl.setToTime(AvailabilityUtil.format24(avl.getTuesdayEndTime()));	
				}
				if("3".equals(day)){
					onl.setFromTime(AvailabilityUtil.format24(avl.getWednesdayStartTime()));
					onl.setToTime(AvailabilityUtil.format24(avl.getWednesdayEndTime()));	
				}
				if("4".equals(day)){
					onl.setFromTime(AvailabilityUtil.format24(avl.getThursdayStartTime()));
					onl.setToTime(AvailabilityUtil.format24(avl.getThursdayEndTime()));	
				}
				if("5".equals(day)){
					onl.setFromTime(AvailabilityUtil.format24(avl.getFridayStartTime()));
					onl.setToTime(AvailabilityUtil.format24(avl.getFridayEndTime()));	
				}
				if("6".equals(day)){
					onl.setFromTime(AvailabilityUtil.format24(avl.getSaturdayStartTime()));
					onl.setToTime(AvailabilityUtil.format24(avl.getSaturdayEndTime()));	
				}
				
				
				return onl;
			}
 	
		public static boolean checkSlotUnderInstructorAvlty(String onlinTimeStart,String onlinTimeEnd,String instAvailStart,String instAvailEnd) throws Exception{
				
				boolean flag = false;
				
				try {
					DateFormat sdf = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN);
					Date insAvlStart = sdf.parse(instAvailStart);
					Date insAvlEnd = sdf.parse(instAvailEnd);
					Date onlineStart = sdf.parse(onlinTimeStart);
					Date onlineEnd = sdf.parse(onlinTimeEnd);
					//System.out.println("instAvailStart      ->    "+insAvlStart);
					//System.out.println("instAvailEnd      ->    "+insAvlEnd);
					//System.out.println("onlinTimeStart      ->    "+onlineStart);
					//System.out.println("onlinTimeEnd      ->    "+onlineEnd);
					//System.out.println(" "); 
					if(onlineStart.before(onlineEnd) && insAvlStart.before(insAvlEnd)){
					 
						if(onlineStart.after(insAvlStart) && onlineEnd.before(insAvlEnd)){
							//System.out.println(" 3 ");
							flag = true;
						}
						
					/*	if(onlineStart.after(insAvlStart) && onlineEnd.equals(insAvlEnd)){
							System.out.println(" 3 ");
							flag = true;
						}*/
						
						if(onlineStart.equals(insAvlStart) && onlineEnd.before(insAvlEnd)){
							//System.out.println(" 4 ");
							flag = true;
						}
						if(onlineEnd.equals(insAvlEnd) && onlineStart.after(insAvlStart)){
							//System.out.println(" 5 ");
							flag = true;
						}
						if(onlineStart.equals(insAvlStart) && onlineEnd.equals(insAvlEnd)){
							 //System.out.println(" 6 ");
							flag = true;
						}
					 
					}

				} catch (ParseException e) {
					throw  e;	 
				}
		  
		    return flag;
		} 
}