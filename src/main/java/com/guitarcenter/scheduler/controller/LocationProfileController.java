package com.guitarcenter.scheduler.controller;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.SessionAttributes;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.service.LocationProfileService;
import com.guitarcenter.scheduler.service.ValidationService;

@SessionAttributes({ AppConstants.LOCATION_ID_SESSION_KEY, AppConstants.LOCATION_PROFILE_ID_SESSION_KEY, AppConstants.PERSON_SESSION_KEY })
@Controller
public class LocationProfileController {

	@Autowired
	private LocationProfileService locationProfileService;
	
	@Autowired
	private ValidationService validationService;
	
	private static final Logger LOG = LoggerFactory.getLogger(LocationProfileController.class);
	
	private static final String DISABLE_PROFILE_MAPPING		= "locationProfile/disableProfile.htm";
	private static final String ENABLE_PROFILE_MAPPING		= "locationProfile/enableProfile.htm";
	
	private static final String MESSAGE_KEY		= "msg";
	private static final String STATUS_KEY		= "status";
	
	//message constants
	private static final String DISABLE_PROFILE_SUCCESS_MESSAGE_VALUE	= "Disable Profile Successfully!";
	private static final String DISABLE_PROFILE_ERROR_MESSAGE_VALUE		= "Disable Profile Error!";
	
	private static final String VALIDATION_PROFILE_MESSAGE_VALUE		= "LocationProfile can't be disabled!";
	
	private static final String ENABLE_PROFILE_SUCCESS_MESSAGE_VALUE	= "Enable Profile Successfully!";
	private static final String ENABLE_PROFILE_ERROR_MESSAGE_VALUE		= "Enable Profile Error!";
	
	@RequestMapping(DISABLE_PROFILE_MAPPING)
	@ResponseBody
	public Map<String, Object> disableProfile(Long profileId, @ModelAttribute(AppConstants.PERSON_SESSION_KEY) Person person) {
		long personId = person.getPersonId();
		Map<String, Object> map = new HashMap<String, Object>();

		//validate if there was future active appointment created in current profile
		if(validationService.checkAppointmentByProfileId(profileId)) {
			try {
				locationProfileService.disableProfile(profileId, personId);
			} catch (Exception e) {//disable profile error
				LOG.error("Disabling profile error", e);
				map.put(STATUS_KEY, false);
				map.put(MESSAGE_KEY, DISABLE_PROFILE_ERROR_MESSAGE_VALUE);
			}
			//disable profile successfully
			map.put(STATUS_KEY, true);
			map.put(MESSAGE_KEY, DISABLE_PROFILE_SUCCESS_MESSAGE_VALUE);
		} else {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, VALIDATION_PROFILE_MESSAGE_VALUE);
		}
		
		return map;
	}
	
	@RequestMapping(ENABLE_PROFILE_MAPPING)
	@ResponseBody
	public Map<String, Object> enableProfile(Long profileId, @ModelAttribute(AppConstants.PERSON_SESSION_KEY) Person person) {
		long personId = person.getPersonId();
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			locationProfileService.enableProfile(profileId, personId);
		} catch (Exception e) {
			LOG.error("Enabling profile error", e);
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, ENABLE_PROFILE_ERROR_MESSAGE_VALUE);
		}
		
		//enable profile successfully
		map.put(STATUS_KEY, true);
		map.put(MESSAGE_KEY, ENABLE_PROFILE_SUCCESS_MESSAGE_VALUE);
		
		return map;
	}
	
}
