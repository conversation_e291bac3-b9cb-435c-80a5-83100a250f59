package com.guitarcenter.scheduler.controller;

import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ACTIVITY_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_APPOINTMENT_TIMEOFF;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ATTENDEES;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_CUSTOMER_APPOINTMENT;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_CUSTOMER_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_DATE_ILLEGAL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_INSTRUCTOR_ACTIVITY;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_INSTRUCTOR_APPOINTMENT;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_INSTRUCTOR_AVAILABILITY;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_INSTRUCTOR_REQUIRED;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_JUMP_START;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_JUMP_START_RECURRENT;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_JUMP_START_UPDATE;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ONLINE_TRIAL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ONLINE_TRIAL_RECURRENT;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ONLINE_TRIAL_UPDATE;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_MINIMUM_DURATION;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_PROFILELOCATION_AVAILABILITY;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ROOM_ACTIVITY;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ROOM_AVAILABILITY;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ROOM_DISABLED;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ROOM_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_SERVICE_ACTIVITY;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_SERVICE_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_START_DATE_ILLEGAL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_START_DATE_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_START_DATE_ONE_YEAR;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_START_TIME_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_TIME_ILLEGAL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_TIME_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_TO_PROFILE_DATE_WITH_APPPOINTMENT;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_TO_PROFILE_DATE_WITH_APPPOINTMENT_UPDATE;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_TO_PROFILE_DATE_WITH_APPPOINTMENT_CREATION;

import java.io.IOException;
import java.text.DateFormat;
import java.text.Format;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import com.guitarcenter.scheduler.service.*;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.SessionAttributes;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.ModelAndView;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Function;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.primitives.Longs;
import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.AvailabilityUtil;
import com.guitarcenter.scheduler.common.util.CalendarUtil;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.ServiceComparator;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.common.util.editor.CalendarDateEditor;
import com.guitarcenter.scheduler.dao.ParentDetailsDAO;
import com.guitarcenter.scheduler.dao.ProfileTimeoffDAO;
import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.dto.CalendarViewUnavailableHourDTO;
import com.guitarcenter.scheduler.dto.ConflictingAppointmentListDTO;
import com.guitarcenter.scheduler.dto.CreateAppointmentDTO;
import com.guitarcenter.scheduler.dto.CustomerDTO;
import com.guitarcenter.scheduler.dto.DurationBuilder;
import com.guitarcenter.scheduler.dto.DurationDTO;
import com.guitarcenter.scheduler.dto.InstoreAvailableDTO;
import com.guitarcenter.scheduler.dto.InstructorInfoDTO;
import com.guitarcenter.scheduler.dto.InstructorViewUnavailableHourDTOWrapper;
import com.guitarcenter.scheduler.dto.OnLineAvailableDTO;
import com.guitarcenter.scheduler.dto.OnLineInstoreAvailableDTO;
import com.guitarcenter.scheduler.dto.ProfileTimeOffDTO;
import com.guitarcenter.scheduler.dto.RoomDTO;
import com.guitarcenter.scheduler.dto.SearchDTO;
import com.guitarcenter.scheduler.dto.ServiceDTO;
import com.guitarcenter.scheduler.dto.StartTimeDTO;
import com.guitarcenter.scheduler.dto.StudioHourDTO;
import com.guitarcenter.scheduler.dto.WeekViewUnavailableHourDTOWrapper;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.Appointment2;
import com.guitarcenter.scheduler.model.AppointmentCancelReason;
import com.guitarcenter.scheduler.model.AppointmentSeries;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.LocationProfile;
import com.guitarcenter.scheduler.model.ParentDetails;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Role;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.RoomTemplate;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.AllowBandName;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.IsRecurring;
import com.guitarcenter.scheduler.model.enums.RequiresInstructor;
import com.guitarcenter.scheduler.webservice.LessonCancelService;
//import org.apache.poi.util.SystemOutLogger;

@RequestMapping("/")
@SessionAttributes({"role"})
@Controller
public class CalendarController implements AppConstants {
	
	private static final Logger LOG = LoggerFactory.getLogger(CalendarController.class);
	
	@Autowired
	ProfileTimeOffService profileTimeOffService;
	
	@Autowired
	private AvailabilityService availabilityService;
	
	@Autowired
	private AppointmentService appointmentService;
	
	@Autowired
	private RoomService roomService;
	
	@Autowired
	private InstructorService instructorService;
	
	@Autowired
	private OnlineAvailabilityService onlineAvailabilityService;
	
	@Autowired
	private InstoreAvailabilityService instoreAvailabilityService;
	
	@Autowired
	private LocationProfileService locationProfileService;
	
	@Autowired
	private AppointmentSeriesService appointmentSeriesService;
	
	@Autowired
	private ActivityService activityService;
	
	@Autowired
	private SearchService searchService;
	
	@Autowired
	private CustomerService customerService;
	
	@Autowired
	private ServiceService serviceService;
	
	@Autowired
	private AppointmentEmailService appointmentEmailService;
	
	@Autowired
	private LocationManagerService locationsService;
	
	@Resource(name="roomTemplateService")
	private RoomTemplateService roomTemplateService;
	
	@Autowired
	private ValidationService validationService;
	
	@Autowired
	private LessonCancelService lessonCancelService;
	
	@Autowired
	@Qualifier("profileTimeoffDAO")
	private ProfileTimeoffDAO profileTimeoffDAO;
	
	@Autowired
	private ParentDetailsDAO parentDetailsDAO;

	@Autowired
	private AsyncEmailService asyncEmailService;

	private static final String RESULT_MAP_KEY 		   = "resultMap";
	private static final String DATE_MAP_KEY 		   = "dateMap";
	private static final String INSTRUCTORS_KEY 	   = "instructorList";
	private static final String cancel_Reason_List 	   = "cancelReasonList";
	private static final String APPOINTMENTS_KEY 	   = "appointmentList";
	private static final String ROOMS_KEY              = "roomList";
	private static final String ACTIVITIES_KEY 		   = "activityList";
	private static final String SERVICES_KEY 		   = "serviceList";
	private static final String SINGLE_APP_KEY 		   = "appointment";
	private static final String ACTIVITY_DTOS_KEY 	   = "activityDTOs";
	private static final String ROOM_DTOS_KEY 	       = "roomDTOs";
	private static final String SERVICE_KEY 		   = "serviceDTO";
	private static final String INSTRUCTOR_DTOS_KEY    = "instructorDTOs";
	private static final String DURATION_DTOS_KEY 	   = "durationDTOs";
	private static final String START_TIME_DTOS_KEY	   = "startTimeDTOs";
	private static final String PROFILE_TIME_DTOS_KEY	   = "profileTimeDTOs";
	private static final String APPOINTMENT_ID_KEY	   = "appointmentID";
	private static final String ROOM_VIEW_UNAVAILABLE_HOUR_DTOS_KEY			   = "roomViewUnavailableHourDTOs";
	private static final String INSTRUCTOR_VIEW_UNAVAILABLE_HOUR_DTOS_KEY      = "instructorViewUnavailableHourDTOs";
	private static final String WEEK_VIEW_UNAVAILABLE_HOUR_DTOS_KEY			   = "weekViewUnavailableHourDTOs";
	
	private static final String LIST_MAP_KEY 		   = "listMap";
	//GSSP-249
	private static final String UPDATED_BY_CUSTOMER	   ="upadtedByCustomerList";
	private static final String MESSAGE_KEY            = "message";
	private static final String MESSAGE_VALUE		   = "Create Appointment Success!";
	private static final String STATUS_KEY			   = "status";
	private static final String CUSTOMER_KEY		   = "customer";
	private static final String selected_cancel_reason = "cancelReason";
	private static final String CURR_DATE_KEY          = "currDate";
	private static final String PREVIOUS_YEAR_KEY      = "preYear";
	private static final String NEXT_YEAR_KEY 		   = "nextYear";
	private static final String CURR_MONTH_KEY 		   = "month";
	private static final String CURR_YEAR_KEY 		   = "year";
	private static final String PRE_MONTH_KEY 	   	   = "preMonth";
	private static final String NEXT_MONTH_KEY     	   = "nextMonth";
	private static final String SUN_TO_SAT_KEY 		   = "sun2satList";
	private static final String CUSTOMER_LIST_KEY	   = "customerList";
	private static final String CANCEL_LIST_KEY ="cancelList";
	private static final String LOCATION_KEY		   = "locationInfo";
	private static final String LOCATION_PROFILE_KEY   = "locationProfileInf";
	
	private static final String PROFILE_TIMEOFF_KEY	   = "loadProfileTimeOff";
	
	//if query by day,the startTime and endTime are the same but differ in minite and hour, so set the minute and second of endTime to 59,hour to 23
	private static final int[] END_TIME				   = new int[]{23, 59};
	private static final int TWELVE 				   = 12; //month 12
	private static final int ZERO_TIME				   = 0;
	
	//Split token for handle string
	private static final String SPLITOR_COMMA		   = ",";
	private static final String SPLITOR_SPACE		   = " ";
	private static final String ROOM_FILTER_TYPE	   = "room";
	private static final String INSTRUCTOR_FILTER_TYPE = "instructor";
	//For GSSP-98
	private static final String	CANCEL_SINGLE_APPOINTMENT = "single";
	
	//mapping constants
	private static final String BEFORE_GO_CALENDAR_MAPPING 			 				= "/beforeGoCalendar.htm";
	private static final String LOAD_ON_DEFAULT_MAPPING 			 				= "calendar/loadOnDefault.htm";
	private static final String GET_APP_BY_ID_MAPPING 				 				= "calendar/getAppointmentById.htm";
	private static final String LOAD_APP_BY_ROOM_MAPPING 			 				= "calendar/loadByRoom.htm";
	private static final String LOAD_APP_BY_WEEK_MAPPING 			 				= "calendar/loadByWeek.htm";
	private static final String LOAD_APP_BY_MONTH_MAPPING 			 				= "calendar/loadByMonth.htm";
	private static final String CREATE_APP_MAPPING 									= "calendar/createAppointment.htm";
	private static final String QUICK_SEARCH_MAPPING 	     		 				= "calendar/quickSearch.htm";
	private static final String LOAD_APP_BY_CUSTORMER_MAPPING 		 				= "calendar/loadAppointmentByCustomer.htm";
	private static final String LOAD_APP_BY_CUSTOMR_ON_WEEK_MAPPING  				= "calendar/loadAppointmentsByCustomerOnWeek.htm";
	private static final String LOAD_APP_BY_CUSTOMR_ON_MONTH_MAPPING 				= "calendar/loadAppointmentsByCustomerOnMonth.htm";
	private static final String LOAD_INSTRUCTOR_BY_ACTIVITY_MAPPING	 				= "calendar/loadInstructorByActivity.htm";
	private static final String UPDATE_APP_MAPPING 					 				= "calendar/updateAppointment.htm";
	private static final String LOAD_SERVICE_LIST_MAPPING 			 				= "calendar/loadService.htm";
	private static final String GET_SERVICE_BY_ID_MAPPING			 				= "calendar/getServiceById.htm";
	private static final String GENERATE_DYNAMIC_FILTER_FROM_SERVICE_MAPPING		= "calendar/generateDynamicFilterFromService.htm";
	private static final String GENERATE_DYNAMIC_FILTER_FROM_ACTIVITY_MAPPING		= "calendar/generateDynamicFilterFromActivity.htm";
	private static final String CANCEL_APPOINTMENT_MAPPING							= "calendar/cancelAppointment.htm";
	private static final String LOAD_CANCEL_REASON_LIST	                            = "calendar/cancelReasonList.htm";
	private static final String FIND_LATEST_FILTER_MAPPING							= "calendar/findLatestFilter.htm";
	public static final String LOAD_SERVICE_LIST_BY_ROOM_OR_INSTRUCTOR				= "calendar/loadServiceListByRoomOrInstructor.htm";
	public static final String LOAD_ACTIVITY_LIST_BY_APPOINTMENTMODEL 				= "calendar/loadActivityListByAppointmentModel.htm";
	public static final String LOAD_INSTRUCTOR_LIST_BY_APPOINTMENTMODEL 			= "calendar/loadInstructorListByAppointmentmodel.htm";
	public static final String LOAD_ROOM_LIST_BY_APPOINTMENTMODEL					= "calendar/loadRoomListByAppointmentmodel.htm";
	public static final String LOAD_INSTRUCTOR_OR_ROOM_LIST_ON_CHANGE 				= "calendar/loadInstructorOrRoomListOnChange.htm";
	private static final String FIND_STORED_FILTER_MAPPING							= "findStoredFilter.htm";
	
	private static final String GRAYEDOUT_APPOINTMENT_LIST_KEY						= "unselectedAppointments";
	public static final String VALIDATION_INSTRUCTOR = "Instructor is Terminated. Select Different Instructor.";

//	private static final String GRAYOUT_APPOINTMENT_MAP_KEY							= "grayoutAppointmentMap";
	

	
	@InitBinder
	public void initBinder(WebDataBinder binder, WebRequest request) {
		binder.registerCustomEditor(Date.class, new CalendarDateEditor());
	}
	
	/**
	 * Before forward to schedule page, need to prepare some object that will be shown in UI
	 * 
	 * @param map Internal object of Spring MVC
	 * @return
	 */
	@RequestMapping(BEFORE_GO_CALENDAR_MAPPING)
	public ModelAndView beforeGoCalendar(ModelMap map) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.beforeGoCalendar: start");
		}		
		Location location = (Location) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute("siteId", RequestAttributes.SCOPE_SESSION);
		Map<String, Object> listMap = new HashMap<String, Object>();

		map = availabilityService.getMap(profileId, map);

		
		//GSSP-334 Changes :: Get the Upcoming Time off for the Profile to display in Scheduler tab.
		
		Date startDateInput = new Date();
	    Set<StudioHourDTO> profileTimeDetails = profileTimeOffService.getUpcomingTimeOffByProfileId(profileId,startDateInput);
		map.put(PROFILE_TIMEOFF_KEY, profileTimeDetails);
	
		
		// Get service list by services
		List<Service> sList = locationProfileService.findServiceByProfileIdAndEnabled(profileId, Enabled.Y);
		Collections.sort(sList, new ServiceComparator());// Sort the service list by serviceId

		// Get activity list from LocationProfile
        List<Long> ids = new ArrayList<Long>();
        for(Service service:sList){
            ids.add(service.getServiceId());
        }
        Long[] serviceIds = ids.toArray(new Long[0]);

		List<ActivityDTO> aList = locationProfileService.findByProfileIdAndServiceIds(profileId, serviceIds);
		ids.clear();
        for (ActivityDTO activityDTO : aList) {
            ids.add(activityDTO.getActivityId());
        }
        Long[] activityIds = ids.toArray(new Long[0]);

		//Dynamiclly loading the instructor list and room list by activity id array
		List<InstructorInfoDTO> iList = instructorService.findByLocationIdAndActivityIds(location.getLocationId(), activityIds);
		List<RoomDTO> rList = roomService.findByProfileIdAndActivityIds(profileId, activityIds);
		
		List<RoomTemplate> rtList = roomTemplateService.getRoomTemplateListBySiteId(siteId);
		Location locationInfo = locationsService.findById(location.getLocationId());

		LocationProfile profile = locationProfileService.getLocationProfile(profileId);
		
		listMap.put(ROOMS_KEY, rList);
		listMap.put(INSTRUCTORS_KEY, iList);
		listMap.put(ACTIVITIES_KEY, aList);
		listMap.put(SERVICES_KEY, sList);
		
		map.put(LIST_MAP_KEY, listMap);
		map.put("roomTemplateList", rtList);
		map.put(LOCATION_KEY, locationInfo);
		map.put(LOCATION_PROFILE_KEY, profile);
		
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.beforeGoCalendar: end");
		}
		return new ModelAndView("/calendar", map);
	}
	
	/**
	 * When page loading, load appointment list by instructors
	 * 
	 * @param date query date of selected day
	 * @param services serviceIds that selected
	 * @param instructors instructorIds that selected
	 * @param rooms roomIds that selected
	 * @param appointments activityIds that selected
	 * @return
	 */
	@RequestMapping(LOAD_ON_DEFAULT_MAPPING)
	@ResponseBody
	public Map<String, Object> loadOnDefault(String date, String services, String instructors, String rooms, String appointments, String unselectedServices, String unselectedActivities, String unselectedRooms, String unselectedInstructors){
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.loadOnDefault: start");
		}
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		Location location = (Location) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);

		String[] st = date.split(SPLITOR_SLASH);
		Date startTime = new DateTime(Integer.parseInt(st[2]), Integer.parseInt(st[0]), Integer.parseInt(st[1]), ZERO_TIME, ZERO_TIME, ZERO_TIME).toDate();
		Date endTime = new DateTime(Integer.parseInt(st[2]), Integer.parseInt(st[0]), Integer.parseInt(st[1]), END_TIME[0], END_TIME[1], END_TIME[1]).toDate();
		
		Map<String, Object> map = new HashMap<String, Object>();
		List<Appointment> list = appointmentService.loadAppointmentList(siteId, profileId, startTime, endTime, services, instructors, rooms, appointments);
		//249 customer updated appointments
		customerService.changeToCustomerUpdated(list);
		//249 end
		Long[] instructorIds = SystemUtil.getIdArray(instructors);
		List<Instructor> instructorList = instructorService.getInstructorListByIds(instructorIds);
		
		//For GCSS-482
//		List<InstructorViewUnavailableHourDTOWrapper> instructorViewUnavailableHourDTOWrappers = appointmentService.getInstructorViewUnavailableHourList(profileId, instructorIds, startTime);
		/**
		 * For GCSS-614,add timoff and unavailability hours together 
		 */
		List<InstructorViewUnavailableHourDTOWrapper> instructorViewUnavailableHourDTOWrappers = appointmentService.getInstructorViewUnavailableHourAndTimeoffList(location.getLocationId(), profileId, instructorIds, startTime);
		map.put(INSTRUCTOR_VIEW_UNAVAILABLE_HOUR_DTOS_KEY, instructorViewUnavailableHourDTOWrappers);
		
		
		/**
		 * For GCSS-525.
		 * Return the appointments that the associated filter is not selected in instructor view.
		 */
		map.putAll(this.getGrayedoutAppointmentMap(profileId, location, siteId, startTime, endTime));
		
		List<Appointment2> list2 = new ArrayList<Appointment2>();
		for(Appointment app:list){
			Appointment2 app2 = new Appointment2();
			app2.setAppointmentId(app.getAppointmentId());
			app2.setAppointmentSeries(app.getAppointmentSeries());
			app2.setCanceled(app.getCanceled());
			app2.setActivity(app.getActivity());
			app2.setBandName(app.getBandName());
			app2.setCreateTime(app.getCreateTime());
			app2.setCustomers(app.getCustomers());
			app2.setIsUpdatedByCustomer(app.getIsUpdatedByCustomer());
			app2.setDuration(app.getDuration());
			app2.setEndTime(app.getEndTime());
			app2.setExternalId(app.getExternalId());
			app2.setInstructor(app.getInstructor());
			app2.setSite(app.getSite());
			app2.setStartTime(app.getStartTime());
			app2.setRoom(app.getRoom());
			app2.setLocationProfile(app.getLocationProfile());
			app2.setNote(app.getNote());
			app2.setUpdatedBy(app.getUpdatedBy());
			app2.setUpdated(app.getUpdated());
			app2.setVersion(app.getVersion());
			 
			 
		 
			
			list2.add(app2);
		}
		map.put(INSTRUCTORS_KEY, instructorList);
		map.put(APPOINTMENTS_KEY, list2);
		
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.loadOnDefault: end");
		}
		return map;
	}
	
	/**
	 * For GCSS-525.
	 * Load the appointments that will be grayed out in calendar jsp
	 * 
	 * @param profileId
	 * @param location
	 * @param siteId
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	
	private Map<String, Object> getGrayedoutAppointmentMap(long profileId, Location location, long siteId, Date startTime, Date endTime) {
		//serviceIds
		List<Service> sList = locationProfileService.findServiceByProfileIdAndEnabled(profileId, Enabled.Y);
		String serviceIdsString = SystemUtil.getServiceIdsString(sList);
		Long[] serviceIds = SystemUtil.getIdArray(serviceIdsString);
		//activityIds
		List<ActivityDTO> aList = locationProfileService.findByProfileIdAndServiceIds(profileId, serviceIds);
        Long[] activityIds = SystemUtil.getIdArray(SystemUtil.getActivityIdsString(aList));
        String activitesIdsString = SystemUtil.getActivityIdsString(aList); 
		//instructorIds
        List<InstructorInfoDTO> iList = instructorService.findByLocationIdAndActivityIds(location.getLocationId(), activityIds);
        String allInstructorIdsString = SystemUtil.getInstructorIdsString(iList);
		//roomIds
        List<RoomDTO> rList = roomService.findByProfileIdAndActivityIds(profileId, activityIds);
		String allRoomIdsString = SystemUtil.getRoomIdsString(rList);
		//the appointment list to be grayed out
		List<Appointment> unselectedAppointments = appointmentService.loadAppointmentList(siteId, profileId, startTime, endTime, serviceIdsString, allInstructorIdsString, allRoomIdsString, activitesIdsString);
		List<Appointment2> list2 = new ArrayList<Appointment2>();
		for(Appointment app:unselectedAppointments){
			Appointment2 app2 = new Appointment2();
			app2.setAppointmentId(app.getAppointmentId());
			app2.setAppointmentSeries(app.getAppointmentSeries());
			app2.setCanceled(app.getCanceled());
			app2.setActivity(app.getActivity());
			app2.setBandName(app.getBandName());
			app2.setCreateTime(app.getCreateTime());
			app2.setCustomers(app.getCustomers());
			app2.setIsUpdatedByCustomer(app.getIsUpdatedByCustomer());
			app2.setDuration(app.getDuration());
			app2.setEndTime(app.getEndTime());
			app2.setExternalId(app.getExternalId());
			app2.setInstructor(app.getInstructor());
			app2.setSite(app.getSite());
			app2.setStartTime(app.getStartTime());
			app2.setRoom(app.getRoom());
			app2.setLocationProfile(app.getLocationProfile());
			app2.setNote(app.getNote());
			app2.setUpdatedBy(app.getUpdatedBy());
			app2.setUpdated(app.getUpdated());
			app2.setVersion(app.getVersion());
			 
 
			list2.add(app2);
		}
		Map<String, Object> map = new HashMap<String, Object>();
		map.put(GRAYEDOUT_APPOINTMENT_LIST_KEY, list2);
		
		return map;
	}
	
	/**
	 * Get appointmnt by Id
	 * 
	 * @param appointmentId appintmentId
	 * @return
	 */
	@RequestMapping(GET_APP_BY_ID_MAPPING)
	@ResponseBody
	public Map<String, Object> getAppointmentById(long appointmentId) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.getAppointmentById: start");
		}
//		Location location = (Location) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		//long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		
		Map<String, Object> map = new HashMap<String, Object>();
		Appointment appInfo = appointmentService.getAppointment(appointmentId);
		AppointmentSeries series = appInfo.getAppointmentSeries();
		CreateAppointmentDTO dto = new CreateAppointmentDTO();
		dto.setAppointmentSeriesId(String.valueOf(series.getAppointmentSeriesId()));
		
		if(null != appInfo.getActivity() && null != appInfo.getActivity().getService()) {
			dto.setServiceId(appInfo.getActivity().getService().getServiceId());
		}
		
		Activity ay = appInfo.getActivity();
		if(null != ay) {
			dto.setActivityId(appInfo.getActivity().getActivityId().toString());
			dto.setActivityName(ay.getActivityName());
		}
		
		dto.setBandName(appInfo.getBandName());

        StringBuilder customerId = new StringBuilder("");
		if(null != appInfo.getCustomers() && !appInfo.getCustomers().isEmpty()) {
			for(Customer c : appInfo.getCustomers()) {
                customerId.append(c.getCustomerId());
                customerId.append(SPLITOR_COMMA);
			}
            customerId.deleteCharAt(customerId.length() - 1);
		}
		dto.setCustomerId(customerId.toString());
		
		Calendar calendar = Calendar.getInstance();
		
		Date ed = series.getSeriesEndTime();
		if(null != ed) {
			calendar.setTime(ed);
			String endDate = ((calendar.get(Calendar.MONTH)+1)<10?("0"+(calendar.get(Calendar.MONTH)+1)):(calendar.get(Calendar.MONTH)+1)) 
					+ SPLITOR_SLASH 
					+ (calendar.get(Calendar.DATE)<10?("0"+calendar.get(Calendar.DATE)):calendar.get(Calendar.DATE))
					+ SPLITOR_SLASH
					+ calendar.get(Calendar.YEAR)  ;
			dto.setEndDate(endDate);
		}
		
		Date et = appInfo.getEndTime();
		calendar.setTime(et);
		if(null != appInfo.getInstructor()) {
			dto.setInstructorId(appInfo.getInstructor().getInstructorId());
			String instructorFirstName = appInfo.getInstructor().getPerson().getFirstName();
			String instructorLastName = appInfo.getInstructor().getPerson().getLastName();
			//GCSS-499,added the Validation of null value on firstName or lastName
			String instructorName = (StringUtils.isEmpty(instructorFirstName) ? "" : instructorFirstName) 
									+ SPLITOR_SPACE 
									+ (StringUtils.isEmpty(instructorLastName) ? "" : instructorLastName);
			dto.setInstructorName(instructorName);
		}
		
		dto.setIsRecurring(IsRecurring.Y.equals(series.getIsRecurring()) ? "true" : "false");
		
		dto.setNote(appInfo.getNote());
		
		dto.setRoomId(appInfo.getRoom().getRoomId());
		
		dto.setServiceId(appInfo.getActivity().getService().getServiceId());
		
		Date sd = appInfo.getStartTime();
		calendar.setTime(sd);
		String startDate = ((calendar.get(Calendar.MONTH)+1)<10?("0"+(calendar.get(Calendar.MONTH)+1)):(calendar.get(Calendar.MONTH)+1)) 
				+ SPLITOR_SLASH 
				+ (calendar.get(Calendar.DATE)<10?("0"+calendar.get(Calendar.DATE)):calendar.get(Calendar.DATE))
				+ SPLITOR_SLASH
				+ calendar.get(Calendar.YEAR);
		dto.setStartDate(startDate);
		
		Date st = appInfo.getStartTime();
		calendar.setTime(st);
		String startTime = (calendar.get(Calendar.HOUR_OF_DAY)<10?"0"+calendar.get(Calendar.HOUR_OF_DAY):calendar.get(Calendar.HOUR_OF_DAY)) 
				+ SPLITOR_COLON + (calendar.get(Calendar.MINUTE)<10?"0"+calendar.get(Calendar.MINUTE):calendar.get(Calendar.MINUTE));
		dto.setStartTime(startTime);
		
		dto.setDuration(String.valueOf(appInfo.getDuration()));
		
		dto.setAppointmentId(String.valueOf(appInfo.getAppointmentId()));
		
		Set<Customer> customers = appInfo.getCustomers();
		List<CustomerDTO> customerDTOs = new LinkedList<CustomerDTO>();
		if(null != customers && !customers.isEmpty()) {
			for(Customer c : customers) {
				CustomerDTO customerDto = new CustomerDTO();
				customerDto.setExternalId(c.getExternalId());
				customerDto.setRecordId(c.getCustomerId());
				customerDto.setEmail(c.getPerson().getEmail());
				
				dto.setEmail(c.getPerson().getEmail());
				//Added the Validation of null value on firstName or lastName,GCSS-499
				customerDto.setFullName(
						(StringUtils.isEmpty(c.getPerson().getFirstName()) ? "" : c.getPerson().getFirstName()),
						(StringUtils.isEmpty(c.getPerson().getLastName()) ? ""
								: c.getPerson().getLastName() + "(" + c.getLessonCounts() + ")"));
				customerDto.setPhone(c.getPerson().getPhone());
				

				String parentFullName= null;
				ParentDetails prntId= parentDetailsDAO.getParentDetailsFromCustomerTable(c.getCustomerId());				
				if(prntId!=null) 				 
					parentFullName = prntId.getFullName();				 
				 	customerDto.setParentFullName(parentFullName);
				 
				customerDTOs.add(customerDto);
				dto.setCustomerDetails(customerDto);
			}
		}
		
		//List<ServiceDTO> serviceList = serviceService.getServiceDTOListBySite(siteId);
		List<Service> serviceList = locationProfileService.findServiceByProfileIdAndEnabled(profileId, Enabled.Y);

		//List<ActivityDTO> activityList = activityService.loadActivityDTOsByService(dto.getServiceId());
		List<ActivityDTO> activityList = locationProfileService.getActivityListByProfileAndService(profileId, dto.getServiceId());
		
		//check whecher the activity require instructor
		Activity acy = activityService.getActivityByActivityId(Long.parseLong(dto.getActivityId()));
		List<InstructorInfoDTO> instruList = new LinkedList<InstructorInfoDTO>();
		
		Date st_ = new Date();
		if((null != dto.getStartDate() && 0 != dto.getStartDate().length())) {
			st_ = DateTime.parse(dto.getStartDate() + " " + "00:00", DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
			if(null != dto.getStartTime() && 0 != dto.getStartTime().length()) {
				st_ = DateTime.parse(dto.getStartDate() + " " + dto.getStartTime(), DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
			}
		}
		String durationString = dto.getDuration();
		int duration = 0;
		if(null != durationString && 0 != durationString.length()) {
			duration = Integer.parseInt(durationString);
		}
		Date et_ = new DateTime(st_).plusMinutes(duration).toDate();
		et_ = SystemUtil.getEndTimeIfOnMidnight(et_);
		
		if(!RequiresInstructor.N.equals(acy.getRequiresInstructor())) {
			//instruList = instructorService.loadByActivityAndLocation(Long.parseLong(dto.getActivityId()), location.getLocationId());
			instruList = instructorService.loadInstructorListByProfileIdAndActivityIdAndDateTime(profileId, dto.getServiceId(), 
					Long.parseLong(dto.getActivityId()), dto.getInstructorId(), new DateTime(st_).plusSeconds(1).toDate(), 
					new DateTime(et_).minusSeconds(1).toDate(), appInfo.getAppointmentId());
			//Customer info maybe null if the RequireInstructor is optional
			if(null != appInfo.getInstructor()) {
				boolean hasCurrentInstructor = false;
				for(InstructorInfoDTO i : instruList) {
					if(appInfo.getInstructor().getInstructorId().equals(Long.parseLong(i.getInstructorId()))) {
						hasCurrentInstructor = true;
						break;
					}
				}
				if(!hasCurrentInstructor) {
					instruList.add(new InstructorInfoDTO(String.valueOf(dto.getInstructor()), dto.getInstructorName()));
				}
			}
		}
		
		//For GCSS-454
//		List<RoomDTO> roomList = roomService.findByProfileIdAndActivityIds(profileId, Long.parseLong(dto.getActivityId()));
		List<RoomDTO> roomList = roomService.
				loadRoomListByProfileIdAndActivityIdAndDateTime(profileId, dto.getServiceId(), appInfo.getActivity().getActivityId(), 
						dto.getInstructorId(), new DateTime(st_).plusSeconds(1).toDate(), 
						new DateTime(et_).minusSeconds(1).toDate(), appInfo.getAppointmentId());
		boolean hasCurrentRoom = false;
		for(RoomDTO r : roomList) {
			if(r.getRoomId().equals(appInfo.getRoom().getRoomId())) {
				hasCurrentRoom = true;
				break;
			}
		}
		if(!hasCurrentRoom) {
			roomList.add(new RoomDTO(appInfo.getRoom().getRoomId(), appInfo.getRoom().getProfileRoomName()));
		}
		
		
		Service tmp = serviceService.getServiceById(dto.getServiceId());
		
		//Build the appointment duration based on the duration configured in activity
		Long minDuration = ay.getMinimumDuration();
		Long maxDuration = null == ay.getMaximumDuration() ? null : ay.getMaximumDuration();
		List<DurationDTO> durationDTOs = new DurationBuilder().buildDurationListByMinAndMaxDuration(minDuration, maxDuration);
		map.put(DURATION_DTOS_KEY, durationDTOs);

		//For GCSS-412,no startTime
		List<StartTimeDTO> startTimeDTOs = new ArrayList<StartTimeDTO>();
	/*	if(null != appInfo.getInstructor()) {
			//Loading by profile and instructor
			startTimeDTOs = appointmentService.generateStartTimeListByProfileAndDate(profileId, appInfo.getInstructor().getInstructorId(), appInfo.getStartTime());
		} else {*/
			//Loading by profile
		//For GCSS-729
		startTimeDTOs = appointmentService.generateStartTimeListByProfileAndDate(profileId, null, appInfo.getStartTime());
		//}
		map.put("selectedStartTime", new DateTime(appInfo.getStartTime()).toString(DateTimeUtil.TIME_FORMAT_HH_MM));
		map.put("selectedStartTimeLabel", new DateTime(appInfo.getStartTime()).toString("hh:mm a"));
		map.put(START_TIME_DTOS_KEY, startTimeDTOs);
		//End of //For GCSS-412,no startTime
		
		map.put(SERVICE_KEY, tmp);
		map.put(SINGLE_APP_KEY, dto);
		map.put(CUSTOMER_LIST_KEY, customerDTOs);
		map.put(ACTIVITIES_KEY, activityList);
		map.put(INSTRUCTORS_KEY, instruList);
		map.put(SERVICES_KEY, serviceList);
		map.put(ROOMS_KEY, roomList);
		
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.getAppointmentById: end");
		}
		return map;
	}
	
	/**
	 * Load appointment list by room
	 * 
	 * @param date query date of specific day
	 * @param services serviceIds that selected
	 * @param instructors instructorIds that selected
	 * @param rooms roomIds that selected
	 * @param appointments activityIds that selected
	 * @return
	 */
	@RequestMapping(LOAD_APP_BY_ROOM_MAPPING)
	@ResponseBody
	public Map<String, Object> loadByRoom(String date, String services, String instructors, String rooms, String appointments, String unselectedServices, String unselectedActivities, String unselectedRooms, String unselectedInstructors) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.loadRoom: start");
		}
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		Location location = (Location) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		
		Map<String, Object> map = new HashMap<String, Object>();
		
		String[] st = date.split(SPLITOR_SLASH);
		Date startTime = new DateTime(Integer.parseInt(st[2]), Integer.parseInt(st[0]), Integer.parseInt(st[1]), ZERO_TIME, ZERO_TIME, ZERO_TIME).toDate();
		Date endTime = new DateTime(Integer.parseInt(st[2]), Integer.parseInt(st[0]), Integer.parseInt(st[1]), END_TIME[0], END_TIME[1], END_TIME[1]).toDate();
		
		List<Appointment> list = appointmentService.loadAppointmentList(siteId, profileId, startTime, endTime, services, instructors, rooms, appointments);
		//249 customer updated appointments
		customerService.changeToCustomerUpdated(list);
		//249 end
		Long[] roomIds = SystemUtil.getIdArray(rooms);
		List<Room> roomList = roomService.getRoomListByIds(roomIds);

		//For GCSS-482
		Format fr =  new SimpleDateFormat("MM/dd/yyyy");
        String pStrDate = fr.format(startTime);
		ProfileTimeOffDTO profileTimeOffDTO = profileTimeoffDAO.verifySingleAppointmentWithProfileTimeOff(profileId,pStrDate);
		List<CalendarViewUnavailableHourDTO> roomViewUnavailableHourDTOs = appointmentService.getRoomViewUnavailableHourList(profileId, startTime);
		//GSSP-344 Changes to get the ProfileTimeOff and add to the room view un-avaiablitity object.
		CalendarViewUnavailableHourDTO cDto = CalendarUtil.getProfileTimeOffUnavaialableHours(profileTimeOffDTO);
		if(null != cDto)		roomViewUnavailableHourDTOs.add(cDto);
		//GSSP-344 End of the Logic
		map.put(ROOM_VIEW_UNAVAILABLE_HOUR_DTOS_KEY, roomViewUnavailableHourDTOs);

		/**
		 * For GCSS-525.
		 * Return the appointments that the associated filter is not selected in room view
		 */
		map.putAll(this.getGrayedoutAppointmentMap(profileId, location, siteId, startTime, endTime));
		
		List<Appointment2> list2 = new ArrayList<Appointment2>();
		for(Appointment app:list){
			Appointment2 app2 = new Appointment2();
			app2.setAppointmentId(app.getAppointmentId());
			app2.setAppointmentSeries(app.getAppointmentSeries());
			app2.setCanceled(app.getCanceled());
			app2.setActivity(app.getActivity());
			app2.setBandName(app.getBandName());
			app2.setCreateTime(app.getCreateTime());
			app2.setCustomers(app.getCustomers());
			app2.setIsUpdatedByCustomer(app.getIsUpdatedByCustomer());
			app2.setDuration(app.getDuration());
			app2.setEndTime(app.getEndTime());
			app2.setExternalId(app.getExternalId());
			app2.setInstructor(app.getInstructor());
			app2.setSite(app.getSite());
			app2.setStartTime(app.getStartTime());
			app2.setRoom(app.getRoom());
			app2.setLocationProfile(app.getLocationProfile());
			app2.setNote(app.getNote());
			app2.setUpdatedBy(app.getUpdatedBy());
			app2.setUpdated(app.getUpdated());
			app2.setVersion(app.getVersion());
			 
			 
		 
			
			list2.add(app2);
		}
		
		map.put(ROOMS_KEY, roomList);
		map.put(APPOINTMENTS_KEY, list2);
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.loadRoom: end");
		}
		return map;
	}
	
	/**
	 * loading appointment list by week
	 * 
	 * @param date the first day of this week
	 * @param services service type that selected
	 * @param instructors intructors that selected
	 * @param appointments appointment types that selected
	 * @param model
	 * @return
	 */
	@RequestMapping(LOAD_APP_BY_WEEK_MAPPING)
	@ResponseBody
	public Map<String, Object> loadByWeek(String date, String services, String instructors, String rooms, String appointments, String unselectedServices, String unselectedActivities, String unselectedRooms, String unselectedInstructors) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.loadByWeek: start");
		}
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		Location location = (Location) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);

		String[] dateArray = date.split(SPLITOR_DASH);
		String[] st = dateArray[0].split(SPLITOR_SLASH);
		String[] et = dateArray[1].split(SPLITOR_SLASH);
		Date startTime = new DateTime(Integer.parseInt(st[2]), Integer.parseInt(st[0]), Integer.parseInt(st[1]), ZERO_TIME, ZERO_TIME, ZERO_TIME).toDate();
		Date endTime = new DateTime(Integer.parseInt(et[2]), Integer.parseInt(et[0]), Integer.parseInt(et[1]), END_TIME[0], END_TIME[1], END_TIME[1]).toDate();
		
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> map = appointmentService.loadByWeek(null, siteId, profileId, startTime, endTime, services, instructors, rooms, appointments, date);
		
		
		Long[] instructorIds = SystemUtil.getIdArray(instructors);
		List<WeekViewUnavailableHourDTOWrapper> weekViewUnavailableHourDTOWrappers=new ArrayList<WeekViewUnavailableHourDTOWrapper>();
		if (instructorIds == null || instructorIds.length > 1) {
			//For GCSS-482
			weekViewUnavailableHourDTOWrappers = appointmentService.getWeekViewUnavailableHourDTOMap(profileId, date);
		}
		if (instructorIds != null && instructorIds.length == 1) {
			weekViewUnavailableHourDTOWrappers = appointmentService.getWeekViewUnavailableHourDTOMapByOneInstructor(location.getLocationId(),profileId,instructorIds,date);
		}
		resultMap.put(WEEK_VIEW_UNAVAILABLE_HOUR_DTOS_KEY, weekViewUnavailableHourDTOWrappers);
//		/**
//		 * For GCSS-566,show grayout layour in week only when a single instructor is selected
//		 */
//		if(null != instructors && 0 != instructors.length() &&  1 == instructors.length()) {
//			Map<String, Object> grayoutMap = appointmentService.loadByWeek(null, siteId, profileId, startTime, endTime, services, unselectedInstructors, rooms, appointments, date);
//			resultMap.put(GRAYOUT_APPOINTMENT_MAP_KEY, grayoutMap);
//		}

		resultMap.put(CUSTOMER_KEY, null);
		resultMap.put(RESULT_MAP_KEY, map);
		
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.loadByWeek: end");
		}
		return resultMap;
	}
	
	/**
	 * Loading appointments of month view
	 * 
	 * @param profileId profileId from session
	 * @param date query date
	 * @param services serviceIds that selected
	 * @param instructors instructorIds that selected
	 * @param appointments activityIds that selected
	 * @param model
	 * @return
	 */
	@RequestMapping(LOAD_APP_BY_MONTH_MAPPING)
	public String loadByMonth(String date, String services, String instructors, String rooms, String appointments, Model model) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.loadByMonth: start");
		}
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);

		Map<String, Object> pMap = appointmentService.getDeliveryMonthDateParam(date);
		String firstDate = (String) pMap.get("firstDate");
		String lastDate = (String) pMap.get("lastDate");
		String[] st = firstDate.split(SPLITOR_SLASH);
		String[] et = lastDate.split(SPLITOR_SLASH);
		
		Date startTime = new DateTime(Integer.parseInt(st[2]), Integer.parseInt(st[0]), Integer.parseInt(st[1]), ZERO_TIME, ZERO_TIME, ZERO_TIME).toDate();
		Date endTime = new DateTime(Integer.parseInt(et[2]), Integer.parseInt(et[0]), Integer.parseInt(et[1]), END_TIME[0], END_TIME[1], END_TIME[1]).toDate();
		
		Map<String, Object> monthMap = appointmentService.loadByMonth(null, siteId, profileId, date, startTime, endTime, services, instructors, rooms, appointments);
		Map<String, Object> dateMap = new HashMap<String, Object>();
		if(date.contains(SPLITOR_DASH)) {
			String startDate = date.split(SPLITOR_DASH)[0];//split the date duration by "-"
			String year = startDate.split(SPLITOR_SLASH)[2];
			String month = startDate.split(SPLITOR_SLASH)[0];
			
			int yearInt = Integer.parseInt(year);
			int monthInt = Integer.parseInt(month);
			int preYear = yearInt; 
			int nextYear = yearInt;
			int nextMonth = monthInt + 1;
			int previousMonth = monthInt - 1;
			if(12 == monthInt) {//if current month is Dec, the next month will be Jan of next year
				nextMonth = 1;
				nextYear = yearInt + 1;
			}
			if(1 == monthInt) {//if current month is Jan,the previous month will be Dec of previous year
				previousMonth = TWELVE;
				preYear = yearInt - 1;
			}
			String currDate = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(new Date());
			dateMap.put(CURR_DATE_KEY, currDate);
			dateMap.put(PREVIOUS_YEAR_KEY, preYear);
			dateMap.put(NEXT_YEAR_KEY, nextYear);
			dateMap.put(CURR_MONTH_KEY, 1 == month.length() ? "0" + month : month);
			dateMap.put(CURR_YEAR_KEY, year);
			dateMap.put(PRE_MONTH_KEY, previousMonth < 10 ? "0" + previousMonth : previousMonth);
			dateMap.put(NEXT_MONTH_KEY, nextMonth < 10 ? "0" + nextMonth : nextMonth);
		}
		model.addAttribute(RESULT_MAP_KEY, monthMap);
		model.addAttribute(DATE_MAP_KEY, dateMap);
		model.addAttribute(SUN_TO_SAT_KEY, DateTimeUtil.getDaysOfWeekList());
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.loadByMonth: end");
		}
		return "includes/result_month";
	}
	
	/**
	 * Create an appointment
	 * 
	 * @param data Posted data from UI form as JSON data
	 * @return
	 */
	@RequestMapping(CREATE_APP_MAPPING)
	@ResponseBody
	public Map<String, Object> createAppointment(String data) {
		if(LOG.isDebugEnabled()) {
		   LOG.debug("CalendarController.createAppointment: start");
		}
		Map<String, Object> map = new HashMap<String, Object>();

		CreateAppointmentDTO dto = this.buildAppointmentDTO(data);
		if(null == dto) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Build appointmentDTO error!");
			return map;
		}
		   		
//Log for conflict appointment in create appointment -GSSp 299
		if(dto != null)	{
			LOG.error("Before Creating Appointment -  log underHostAddress: " +SystemUtil.getHostAddress() != null ? SystemUtil.getHostAddress() : "Invalide Host Name");
		}
		
		Person person = (Person) RequestContextHolder.currentRequestAttributes().getAttribute(PERSON_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		LocationProfile locationProfile = new LocationProfile();
		locationProfile.setProfileId(profileId);
		Site site = new Site();
		site.setSiteId(siteId);
		
		
		try {
			map = this.checkAppointment(dto);
			if((Boolean)map.get(STATUS_KEY)){
				AppointmentSeries series = dto.getAppointmentSeries();
				series.setSite(site);
				series.setUpdatedBy(person);
				series.setLocationProfile(locationProfile);
				
				//Changes made for GSSP-234
				if(null != dto.getNote())
					series.setNote(dto.getNote());
				
				AppointmentSeries appointmentSeries = appointmentSeriesService.createAppointmentSeries(series, person);
				Appointment appointment = dto.getAppointment();
				
				appointment.setSite(site);
				appointment.setAppointmentSeries(appointmentSeries);
				appointment.setUpdatedBy(person);
				appointment.setLocationProfile(locationProfile);
				CreateAppointmentDTO caDto = new CreateAppointmentDTO();
    			appointmentService.createAppointment(appointment,caDto);
				//fix the bug GCSS-411
				if(appointment.getCustomers() != null){
					//GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-starts
										
					Room room = roomService.getRoom(dto.getRoomId());
					Activity  activityName  = activityService.getActivityByActivityId(Long.parseLong(dto.getActivityId()));
				    String instructorName=appointmentService.findInstructorName(dto);
					
				 
					//For GSSP-243 send email with cc Associates/Managers/Leads
					//changes made for 237 method name changed
					//appointmentEmailService.sendEmailForCreateAppt(appointment,dto.getIsRecurring(),Integer.parseInt(dto.getDuration()),instructorName, room,activityName, profileId);
					//OLL-3811
					asyncEmailService.sendEmailForCreateApptAsync(appointment, dto.getIsRecurring(), Integer.parseInt(dto.getDuration()), instructorName, room, activityName, profileId);
				}//GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-ends
				map.put(STATUS_KEY, true);
				map.put(MESSAGE_KEY, MESSAGE_VALUE);
				//Log for conflict appointment in create appointment -GSSP-299
				if(appointment != null && appointment.getAppointmentSeries().getAppointmentSeriesId() != null &&  appointment.getUpdated() != null ){
				LOG.error("After creating Appointment -"
						+ "Appointment created with the appointmentSeriesId " + appointment.getAppointmentSeries().getAppointmentSeriesId() 
						+ " Appointment created - timeStamp " + appointment.getUpdated());
				}
			}
		} catch (Exception e) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Create Appointment Failed");
			LOG.error("Caught an exception from CalendarController.createAppointment: {}", e);
		}
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.createAppointment: end");
		}
		return map;
	}
	
	/**
	 * Customer search in calendar view
	 * 
	 * @param name customer name
	 * @return
	 */
	@RequestMapping(QUICK_SEARCH_MAPPING)
	@ResponseBody
	public List<SearchDTO> quickSearch(String name, String customers) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.quickSearch: start");
		}
		long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		List<SearchDTO> dtos = new LinkedList<SearchDTO>();
		List<String> solrfilter = new ArrayList<String>();
		if(StringUtils.hasText(customers)){
			solrfilter.add("-" + SearchDTO.SOLR_RECORD_ID_FIELD +":(" + customers.replace(",", " ") + ")");
		}
        String studioNumber = (String) RequestContextHolder.currentRequestAttributes().getAttribute(STORE_STRING, RequestAttributes.SCOPE_SESSION);
        if (StringUtils.hasText(studioNumber)) {
        	
        	//GSSP-220 changes        	
        	solrfilter.add("+(" + SearchDTO.SOLR_SEARCH_EXTERNAL_ID_FIELD + ":" + studioNumber + " OR " +  SearchDTO.SOLR_SEARCH_LOCATION_EXTERNAL_ID_FIELD + ":" + studioNumber + ")*");
        	//GSSP-220 changes
        	
        	//solrfilter.add("+" + SearchDTO.SOLR_SEARCH_LOCATION_EXTERNAL_ID_FIELD + ":" + studioNumber + "*");
        }
        
        
		try {
		    dtos.addAll(this.searchService.quickSearch(siteId, name, solrfilter));
		} catch (Exception e) {
		    LOG.error("Caught an exception from search service {}", e);
		}
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.quickSearch: end");
		}
		if(null != dtos && !dtos.isEmpty()) {
			for(SearchDTO o : dtos) {
			    o.setFullName(o.getFirstName(), o.getLastName());
			    if(null != o.getRecordId()) {
			    	String instrumentName = customerService.getCustomerDetail(o.getRecordId());
			    	o.setInstrumentType(instrumentName);
			    }
			}
		}
		return dtos;
	}
	
	/**
	 * validation method for check create appointment
	  * checkAppointment
	  *
	  * @Title: checkAppointment
	  * @param @param dto
	  * @param @return
	  * @param @throws RuntimeException
	  * @return Map<String,Object>
	  * @throws
	 */
	public Map<String, Object> checkAppointment(CreateAppointmentDTO dto) throws RuntimeException {
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.checkAppointment: start");
		}
 
		//Date dateVal = getDateFromString(dto.getStartDate());
		//Bug Logic
		//Date dateVal = getDateFromString(dto.getStartDate());
		 // int dateval1 = dateVal.getDay();

		//Corrected Logic.
		Date st_1 = new Date();
		if((null != dto.getStartDate() && 0 != dto.getStartDate().length())) {
			st_1 = DateTime.parse(dto.getStartDate() + " " + "00:00", DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
			if(null != dto.getStartTime() && 0 != dto.getStartTime().length()) {
				st_1 = DateTime.parse(dto.getStartDate() + " " + dto.getStartTime(), DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
			}
		}
		 Calendar cal = Calendar.getInstance(); 
		 cal.setTime(st_1); 
		 int dateval1 = cal.get(Calendar.DAY_OF_WEEK);
		 
		 Map<String, Object> map = new HashMap<String, Object>();
		 
		 if(dto.getDuration() == null || "".equals((dto.getDuration().trim()))){
				map.put(MESSAGE_KEY, VALIDATION_TIME_NULL);
				map.put(STATUS_KEY, false);
				return map;
			} 
		 
		String endTimeStr = CalendarUtil.getEndTime(dto.getStartTime(), dto.getDuration());
		
		//********************************appese changes ***************************
		 
		boolean verifyValidInstructor =false;
		
		try {
			//verifyAppointmentServiceMode = verifyAppointmentServiceModeStatus(dto.getInstructorId(),dateVal.getDay(),dto.getServiceId(),dto.getStartTime(),endTimeStr);
			verifyValidInstructor = verifyValidInstructor(dto.getInstructorId(),dto.getCustomerId());
		} catch (Exception e1) {
			LOG.error("Caught {} when check UI verifyAppointmentServiceModeStatus Appointment", e1);
		}
		
		if(verifyValidInstructor){
			map.put(MESSAGE_KEY, VALIDATION_INSTRUCTOR);
			map.put(STATUS_KEY, false);
			return map;
		}
		

		//*************************************************************************
		boolean verifyAppointmentServiceMode =false;
		String service_msg="";
		try {
			//verifyAppointmentServiceMode = verifyAppointmentServiceModeStatus(dto.getInstructorId(),dateVal.getDay(),dto.getServiceId(),dto.getStartTime(),endTimeStr);
			verifyAppointmentServiceMode = verifyAppointmentServiceModeStatus(dto.getInstructorId(),dateval1,dto.getServiceId(),dto.getStartTime(),endTimeStr);
		} catch (Exception e1) {
			LOG.error("Caught {} when check UI verifyAppointmentServiceModeStatus Appointment", e1);
		}
		if(dto.getServiceId() == 1){
			service_msg ="The In-store Appointment conflict with Online Availability";
		}
		if(dto.getServiceId() == 20){
			service_msg ="The Online Appointment conflict with In-store Availability"; 
		}
		
		if(verifyAppointmentServiceMode){
			map.put(MESSAGE_KEY, service_msg);
			map.put(STATUS_KEY, false);
			return map;
		}
		String startDate = dto.getStartDate();
		String endDate = dto.getEndDate();
		String startTime = dto.getStartTime();
		String duration =  dto.getDuration();
		String activityDTOId = dto.getActivityId();
		Set<Customer> cs = dto.getCustomers();
		boolean recurring = dto.getIsRecurring()==null?false:Boolean.parseBoolean(dto.getIsRecurring());
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		if(null==dto.getServiceId()){
			map.put(MESSAGE_KEY, VALIDATION_SERVICE_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		if(activityDTOId==null || "".equals(activityDTOId.trim())){
			long serviceId = dto.getServiceId();
			Service service = serviceService.getServiceById(serviceId);
			String message = VALIDATION_ACTIVITY_NULL.replace("&REPLASE", service.getServiceName());
			map.put(MESSAGE_KEY, message);
			map.put(STATUS_KEY, false);
			return map;
		}
		if(startDate==null || "".equals(startDate.trim())){
			map.put(MESSAGE_KEY, VALIDATION_START_DATE_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		//279 GSSP-   pop -up message, while Creating an appointment to 140with recurring
		if("140".equals(dto.getActivityId())&&recurring){
			
				map.put(MESSAGE_KEY, VALIDATION_JUMP_START_RECURRENT);
				map.put(STATUS_KEY, false);
				return map;
			}
		if("320".equals(dto.getActivityId())&&recurring){
			
			map.put(MESSAGE_KEY, VALIDATION_ONLINE_TRIAL_RECURRENT);
			map.put(STATUS_KEY, false);
			return map;
		}
			
		//GSSP-233 Changes
		if(CalendarUtil.isDateafterAnYear(startDate)){
			map.put(MESSAGE_KEY, VALIDATION_START_DATE_ONE_YEAR);
			map.put(STATUS_KEY, false);
			return map;
		}
		if(endDate != null && !"".equals(endDate.trim()) && !CalendarUtil.checkDate(startDate, endDate)){
			map.put(MESSAGE_KEY, VALIDATION_DATE_ILLEGAL);
			map.put(STATUS_KEY, false);
			return map;
		}
		if(startTime==null || "".equals(startTime.trim())){
			map.put(MESSAGE_KEY, VALIDATION_START_TIME_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		if(duration == null || "".equals(duration.trim())){
			map.put(MESSAGE_KEY, VALIDATION_TIME_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
				
		if(!CalendarUtil.checkTime(startTime, duration)){
			map.put(MESSAGE_KEY, VALIDATION_TIME_ILLEGAL);
			map.put(STATUS_KEY, false);
			return map;
		}
		//check the end date if input value is null
		if(recurring && (endDate==null || "".equals(endDate.trim()))){
			endDate = CalendarUtil.getDefaultEndDate(startDate);
		}
		
		//264 start //777
		if(recurring &&(endDate!=null && !"".equals(endDate.trim()))&&!validationService.validateStartEndDiff(startDate, endDate))
		{
			map.put(MESSAGE_KEY, "The end date can not be later than one year from the start date!!");
			map.put(STATUS_KEY, false);
			return map;
		}
		//264 end
		
		if(!validationService.checkStartTime(startDate, startTime)){
			map.put(MESSAGE_KEY, VALIDATION_START_DATE_ILLEGAL);
			map.put(STATUS_KEY, false);
			return map;
		}
		String endTime = CalendarUtil.getEndTime(startTime, duration);
		if(StringUtils.isEmpty(dto.getRoomId())){
			map.put(MESSAGE_KEY, VALIDATION_ROOM_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		long roomId= dto.getRoomId();
		Room room = roomService.getRoom(roomId);
		if(Enabled.N.equals(room.getEnabled())){
			map.put(MESSAGE_KEY, VALIDATION_ROOM_DISABLED);
			map.put(STATUS_KEY, false);
			return map;
		}
		boolean checkProfileLocation = validationService.checkLocationProfileByTime(profileId, startDate, endDate, startTime, endTime);
		if(!checkProfileLocation){
			map.put(MESSAGE_KEY, VALIDATION_PROFILELOCATION_AVAILABILITY);
			map.put(STATUS_KEY, false);
			return map;
		}
		//check room - time
		boolean checkRoomByTime = validationService.checkRoomByAppointmentTime(recurring, roomId, startDate, endDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1), profileId);
		if(!checkRoomByTime){
			map.put(MESSAGE_KEY, VALIDATION_ROOM_AVAILABILITY);
			map.put(STATUS_KEY, false);
			
			//Changes made for GSSP-241
			map.put("isConflicting", true);			
			
			return map;
		}
		long serviceId = dto.getServiceId();
		long activityId = Long.parseLong(activityDTOId);
		Activity activity = activityService.getActivityByActivityId(activityId);
		if(serviceId != activity.getService().getServiceId()){
			map.put(MESSAGE_KEY, VALIDATION_SERVICE_ACTIVITY);
			map.put(STATUS_KEY, false);
			return map;
		}
		if(Long.parseLong(duration)<activity.getMinimumDuration()){
			map.put(MESSAGE_KEY, VALIDATION_MINIMUM_DURATION);
			map.put(STATUS_KEY, false);
			return map;
		}
		int inputAttendees = 0;
		if(cs != null ){
			inputAttendees = cs.size();
		}
		//GCSS-437 User tries to save an appointment without selecting a customer
		if(inputAttendees==0 && activity.getMinimumAttendees()>0){
			map.put(MESSAGE_KEY, VALIDATION_CUSTOMER_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		if((activity.getMinimumAttendees() >0 && inputAttendees<activity.getMinimumAttendees())||(activity.getMaximumAttendees()!=null 
				&& !"".equals(activity.getMaximumAttendees().toString().trim()) && inputAttendees>activity.getMaximumAttendees())){
			map.put(MESSAGE_KEY, VALIDATION_ATTENDEES);
			map.put(STATUS_KEY, false);
			return map;
		}
		//GCSS-417 check the customer with appointment parameters
		List<Long> customerIds = getCustomerIdList(cs);
		boolean checkCustomers = validationService.checkCustomerByAppointmentTime(recurring, customerIds, startDate, endDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1), profileId);
		if(!checkCustomers){
			map.put(MESSAGE_KEY, VALIDATION_CUSTOMER_APPOINTMENT);
			map.put(STATUS_KEY, false);
			return map;
		}
		//fix the bug GCSS-506
		if(RequiresInstructor.R.equals(activity.getRequiresInstructor())||(RequiresInstructor.O.equals(activity.getRequiresInstructor()) && !org.springframework.util.StringUtils.isEmpty(dto.getInstructor()))){
			
			//For GCSS-193,instructor is required but with unavailible instructor
			if(org.springframework.util.StringUtils.isEmpty(dto.getInstructor())) {
				map.put(MESSAGE_KEY, VALIDATION_INSTRUCTOR_REQUIRED);
				map.put(STATUS_KEY, false);
				return map;
			}
			
			long instructorId = dto.getInstructorId();
			
			//GCSS-590
			boolean timeOff = validationService.checkTimeoffByTime(recurring, startDate, endDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1), instructorId);
			if(!timeOff){
				map.put(MESSAGE_KEY, VALIDATION_APPOINTMENT_TIMEOFF);
				map.put(STATUS_KEY, false);
				return map;
			}
			//---GSSP-334	Req 3 Recurring Appointment with profile Timeoff------------------
			try {
				if(!recurring && !validationService.checkProfileTimeoffByTime(profileId,startDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1))){
					map.put(MESSAGE_KEY, VALIDATION_TO_PROFILE_DATE_WITH_APPPOINTMENT);
					map.put(STATUS_KEY, false);
					return map;
				}
			} catch (Exception e) {
				map.put(MESSAGE_KEY, VALIDATION_TO_PROFILE_DATE_WITH_APPPOINTMENT_CREATION);
				map.put(STATUS_KEY, true);
				return map;
			}
			//---GSSP-334	Req 3 ------------------------------------------------------------
			
			//check instructor - activity
			boolean checkInstructorByActivityId = validationService.checkInstructorByProfileActivityId(instructorId, activityId, profileId);
			if(!checkInstructorByActivityId){
				map.put(MESSAGE_KEY, VALIDATION_INSTRUCTOR_ACTIVITY);
				map.put(STATUS_KEY, false);
				return map;
			}
			//check instructor - availability- GSSP-286
			boolean checkInstructorByTime = validationService.checkInstructorByTime(instructorId, startDate, endDate, startTime, endTime);
			if(!checkInstructorByTime){
				//GCSS-670
				boolean checkOneTime = validationService.checkOnetime(recurring, startDate, endDate, startTime, endTime, instructorId);
				if(!checkOneTime){
							List<String> startDateList = CalendarUtil.getPeriodWeekDate(startDate, endDate);
							for(String date : startDateList){
								Date st_ = DateTime.parse(date + " " + startTime, DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
								Date et_ = DateTime.parse(date + " " + endTime, DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
								boolean checkAvailabilityAndOntime=validationService.checkAvailabilityAndOntime(instructorId, st_, et_);
								if(!checkAvailabilityAndOntime){
									map.put(MESSAGE_KEY, VALIDATION_INSTRUCTOR_AVAILABILITY);
									map.put(STATUS_KEY, false);
									return map;
								}
							}
					
				}
			}
			//check instructor-appointment
			boolean checkInstructorByAppointmentTime = validationService.checkInstructorByAppointmentTime(recurring, instructorId, startDate, endDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1));
			if(!checkInstructorByAppointmentTime){
				map.put(MESSAGE_KEY, VALIDATION_INSTRUCTOR_APPOINTMENT);
				map.put(STATUS_KEY, false);
				
				// Changes made for GSSP-241
				map.put("isConflicting", true);
				
				return map;
			}
		}
		//check room - activity
		boolean checkRoomByActivity = validationService.checkRoomByActivity(roomId, activityId);
		if(!checkRoomByActivity){
			map.put(MESSAGE_KEY, VALIDATION_ROOM_ACTIVITY);
			map.put(STATUS_KEY, false);
			
			return map;
		}
		map.put(STATUS_KEY, true);
		
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.checkAppointment: end");
		}
		return map;
	}

	private boolean verifyValidInstructor(Long instructorId, String CustomerId) {
		
		Instructor instructor1 = instructorService.getInstructor(instructorId);
		
		if((null != CustomerId || "".equals(CustomerId)) && null != instructor1 && null != instructor1.getInstructorId() && null != instructor1.getStatus()) {
			if(!instructor1.getStatus().equals("A")) {
				return true;
			}
		}
		return false;
	}

	
	private List<Long> getCustomerIdList(Set<Customer> cs){
		List<Long> customerIds = new ArrayList<Long>();
		if(cs != null){
			for(Customer c : cs){
				customerIds.add(c.getCustomerId());
			}
		}
		return customerIds;
	}

	public Map<String, Object> checkAppointmentForUpdate(CreateAppointmentDTO dto, Appointment ui, Appointment db, Appointment currentApp) throws RuntimeException {
		
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.checkAppointment: start");
		}
		Map<String, Object> map = new HashMap<String, Object>();
		long serviceId = dto.getServiceId();
		String startDate = dto.getStartDate();
		String endDate = dto.getEndDate();
		String startTime = dto.getStartTime();
		String duration =  dto.getDuration();		
		String activityDTOId = dto.getActivityId();
		//GSSP 279 changes
		String	endTime=null;
		try {
				endTime = CalendarUtil.getEndTime(startTime, duration);
		} catch (NullPointerException e) {
			LOG.error("Caught CreateAppointmentDto.getAppointment {} when loading CalendarUtil.getEndTime in CalendarController", e);
		} 
		Set<Customer> cs = dto.getCustomers();
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		boolean recurring = dto.getIsRecurring()==null?false:Boolean.parseBoolean(dto.getIsRecurring());
		

		//---GSSP-334 Bug fix Recurring Appointment with profile Timeoff------------------
		try {
			if(!recurring && !validationService.checkProfileTimeoffByTime(profileId,startDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1))){
				map.put(MESSAGE_KEY, VALIDATION_TO_PROFILE_DATE_WITH_APPPOINTMENT_UPDATE);
				map.put(STATUS_KEY, false);
				return map;
			}
		} catch (Exception e) {
			map.put(MESSAGE_KEY, VALIDATION_TO_PROFILE_DATE_WITH_APPPOINTMENT_CREATION);
			map.put(STATUS_KEY, true);
			return map;
		}
		//---GSSP-334	Req 3 ------------------------------------------------------------
		
		//customer can not be null
//		if(cs == null || cs.isEmpty()){
//			map.put(MESSAGE_KEY, VALIDATION_CUSTOMERS_NULL);
//			map.put(STATUS_KEY, false);
//			return map;
//		}
		
		//startDate can not be null
		if(startDate==null || "".equals(startDate.trim())){
			map.put(MESSAGE_KEY, VALIDATION_START_DATE_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}

		if(startTime==null || "".equals(startTime.trim())){
			map.put(MESSAGE_KEY, VALIDATION_START_TIME_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		//build startDate and startTime from startTime of appointment queried from database
		Calendar cr = Calendar.getInstance();
		cr.setTime(db.getStartTime());
		int year = cr.get(Calendar.YEAR);
		int month = cr.get(Calendar.MONTH)+1;
		int date = cr.get(Calendar.DATE);
		int hour = cr.get(Calendar.HOUR_OF_DAY);
		int minute = cr.get(Calendar.MINUTE);
		String dbStartDate = (month<10?("0"+month):String.valueOf(month)) + SPLITOR_SLASH 
				+ (date<10?("0"+date):String.valueOf(date)) + SPLITOR_SLASH + year;
		String dbStartTime = (hour<10?("0"+hour):String.valueOf(hour)) + SPLITOR_COLON + (minute<10?("0"+minute):String.valueOf(minute));
		
		//validate if the appointment time queried from database is before today, make it disable for update in validation
		if(!validationService.checkStartTime(dbStartDate.trim(), dbStartTime.trim())){
			map.put(MESSAGE_KEY, VALIDATION_START_DATE_ILLEGAL);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		//startDate can't be earlier then today
		if(!validationService.checkStartTime(startDate, startTime)){
			map.put(MESSAGE_KEY, VALIDATION_START_DATE_ILLEGAL);
			map.put(STATUS_KEY, false);
			return map;
		}
		//264 start //777
				if(recurring &&(endDate!=null && !"".equals(endDate.trim()))&&!validationService.validateStartEndDiff(startDate, endDate))
				{
					map.put(MESSAGE_KEY, "The end date can not be later than one year from the start date!!");
					map.put(STATUS_KEY, false);
					return map;
				}
				//264 end
		//GSSP-268 Issue fix 2: Start time can't be later than the end date of the series. start //777
		if(dto.getIsRecurring().equals("false"))
		{
			if(dto.getRecurringStatus().equals("3")&&!validationService.validateStartTime(startDate,db.getAppointmentSeries().getSeriesEndTime(),currentApp))
			{
				if(db.getAppointmentSeries().getSeriesEndTime()==null)//ProdIssue added an additional parameter of the type appointment
				{
					map.put(MESSAGE_KEY, "The start date cannot be later than one year from the current date!");
				}
				else 
				{
					map.put(MESSAGE_KEY, "Please enter a date that comes before the series end date!");
				}
				map.put(STATUS_KEY, false);
				return map;
				
			}
		}//Issue fix 2: Start time can't be later than the end date of the series. end
		//279 GSSP-   pop -up message, while updating an appointment to 140 with recurring
		if("140".equals(dto.getActivityId())&&(db.getActivity().getActivityId())!=140){
			
			map.put(MESSAGE_KEY, VALIDATION_JUMP_START_UPDATE);
			map.put(STATUS_KEY, false);
			return map;
			
		}
		if("320".equals(dto.getActivityId())&&(db.getActivity().getActivityId())!=320){
			
			map.put(MESSAGE_KEY, VALIDATION_ONLINE_TRIAL_UPDATE);
			map.put(STATUS_KEY, false);
			return map;
			
		}
		
		
		//279 GSSP-   pop -up message, while updating an appointment to 140 to other activity
        if(140 == (db.getActivity().getActivityId())&&!dto.getActivityId().equals("140")){
			map.put(MESSAGE_KEY, VALIDATION_JUMP_START);
			map.put(STATUS_KEY, false);
			return map;
	        
		}
        if(320 == (db.getActivity().getActivityId())&&!dto.getActivityId().equals("320")){
			map.put(MESSAGE_KEY, VALIDATION_ONLINE_TRIAL);
			map.put(STATUS_KEY, false);
			return map;
	        
		}        
		
		//If isRecurring is checked with no endDate, set date after 3 months to endDate 
		if(recurring && (endDate==null || "".equals(endDate.trim()))){
			endDate = CalendarUtil.getDefaultEndDate(startDate);
		}
		
		//duration can not be null
		if(duration == null || "".equals(duration.trim())){
			map.put(MESSAGE_KEY, VALIDATION_TIME_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		
		//room can not be null
		if(StringUtils.isEmpty(dto.getRoomId())){
			map.put(MESSAGE_KEY, VALIDATION_ROOM_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		//activity can not be null
		if(StringUtils.isEmpty(activityDTOId) || 0 == activityDTOId.length()) {
			map.put(MESSAGE_KEY, VALIDATION_ACTIVITY_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		//validate whether the room is disabled
		long roomId= dto.getRoomId();
		Room room = roomService.getRoom(roomId);
		if(Enabled.N.equals(room.getEnabled())){
			map.put(MESSAGE_KEY, VALIDATION_ROOM_DISABLED);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		//validate profile time
		boolean checkProfileLocation = validationService.checkLocationProfileByTime(profileId, startDate, endDate, startTime, endTime);
		if(!checkProfileLocation){
			map.put(MESSAGE_KEY, VALIDATION_PROFILELOCATION_AVAILABILITY);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		long activityId = Long.parseLong(activityDTOId);
		Activity activity = activityService.getActivityByActivityId(activityId);
		
		//validate whether the selected activity match it's service type
		if(serviceId != activity.getService().getServiceId()){
			map.put(MESSAGE_KEY, VALIDATION_SERVICE_ACTIVITY);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		//validate whether the duration meet the minimumDuration of current activity
		if(Long.parseLong(duration)<activity.getMinimumDuration()){
			map.put(MESSAGE_KEY, VALIDATION_MINIMUM_DURATION);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		//If customer is changed, validate whether the selected customer size is less than activity minimumAttendees
		int inputAttendees = 0;
		if(cs != null ){
			inputAttendees = cs.size();
		}
		//GCSS-437 User tries to save an appointment without selecting a customer
		if(inputAttendees==0 && activity.getMinimumAttendees()>0){
			map.put(MESSAGE_KEY, VALIDATION_CUSTOMER_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		if((activity.getMinimumAttendees()>0 && inputAttendees < activity.getMinimumAttendees()) 
				|| (null != activity.getMaximumAttendees() && !"".equals(activity.getMaximumAttendees().toString().trim()) && inputAttendees > activity.getMaximumAttendees())) {
			map.put(MESSAGE_KEY, VALIDATION_ATTENDEES);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		if(endDate != null && !"".equals(endDate.trim()) && !CalendarUtil.checkDate(startDate, endDate)){
			map.put(MESSAGE_KEY, VALIDATION_DATE_ILLEGAL);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		//validate whether the duration overflow to next day
		if(!CalendarUtil.checkTime(startTime, duration)){
			map.put(MESSAGE_KEY, VALIDATION_TIME_ILLEGAL);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		//Get the appointments that will be ignored in appointment update validation
		String ignoredIds = this.getIgnoredAppointments(dto.getIsRecurring(), db);
		
		//GCSS-417 check the customer with appointment parameters
		List<Long> customerIds = getCustomerIdList(cs);
		//fix the bug of GCSS-478 use start date from dto
		boolean checkCustomers = validationService.checkUpdateCustomerByAppointmentTime(recurring, customerIds, startDate, endDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1), profileId, ignoredIds);
		if(!checkCustomers){
			map.put(MESSAGE_KEY, VALIDATION_CUSTOMER_APPOINTMENT);
			map.put(STATUS_KEY, false);
			return map;
		}
		//fix the bug GCSS-506
		if(RequiresInstructor.R.equals(activity.getRequiresInstructor())||(RequiresInstructor.O.equals(activity.getRequiresInstructor()) && !org.springframework.util.StringUtils.isEmpty(dto.getInstructor()))){
			//For GCSS-193,instructor is required but with unavailable instructor
			if(org.springframework.util.StringUtils.isEmpty(dto.getInstructor())) {
				map.put(MESSAGE_KEY, VALIDATION_INSTRUCTOR_REQUIRED);
				map.put(STATUS_KEY, false);
				return map;
			}
			
			//If instructor or activity changed, check instructor - activity
			long instructorId = dto.getInstructorId();
			
			//GCSS-590
			boolean timeOff = validationService.checkTimeoffByTime(recurring, startDate, endDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1), instructorId);
			if(!timeOff){
				map.put(MESSAGE_KEY, VALIDATION_APPOINTMENT_TIMEOFF);
				map.put(STATUS_KEY, false);
				return map;
			}
			
			if(!validationService.checkInstructorByProfileActivityId(instructorId, activityId, profileId)){
				map.put(MESSAGE_KEY, VALIDATION_INSTRUCTOR_ACTIVITY);
				map.put(STATUS_KEY, false);
				return map;
			}

			//check instructor - availability- GSSP-286
			//If instructor or time changed,check instructor - availability and instructor - appointment
			if(!validationService.checkInstructorByTime(instructorId, startDate, endDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1))){
				//GCSS-670
				boolean checkOneTime = validationService.checkOnetime(recurring, startDate, endDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1), instructorId);
				if(!checkOneTime){				
							List<String> startDateList = CalendarUtil.getPeriodWeekDate(startDate, endDate);
							for(String datelist : startDateList){
								Date st_ = DateTime.parse(datelist + " " + startTime, DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
								Date et_ = DateTime.parse(datelist + " " + endTime, DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
								boolean checkAvailabilityAndOntime=validationService.checkAvailabilityAndOntime(instructorId, st_, et_);
								if(!checkAvailabilityAndOntime){
									map.put(MESSAGE_KEY, VALIDATION_INSTRUCTOR_AVAILABILITY);
									map.put(STATUS_KEY, false);
									return map;
								}
							}
						}
			}
			//If instructor or time changed instructor - appointment
			boolean isInstructorConflictWithAppointmentTime = validationService.checkUpdateInstructorByAppointmentTime(recurring, instructorId, startDate, endDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1), ignoredIds);
			if(!isInstructorConflictWithAppointmentTime) {
				map.put(MESSAGE_KEY, VALIDATION_INSTRUCTOR_APPOINTMENT);
				map.put(STATUS_KEY, false);
				return map;
			}
		}
		
		//If room or time changed, check room - time
		boolean isRoomConflictWithAppointmentTime = validationService.checkUpdateRoomByAppointmentTime(
				recurring, db.getRoom().getRoomId(), roomId, startDate, endDate, 
				CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1), profileId, ignoredIds);
		if(!isRoomConflictWithAppointmentTime) {
			map.put(MESSAGE_KEY, VALIDATION_ROOM_AVAILABILITY);
			map.put(STATUS_KEY, false);
			return map;
		}
		
		map.put(STATUS_KEY, true);
		
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.checkAppointmentForUpdate: passed");
		}
		return map;
	}
	
	/**
	 * Get the appointment list by specific time and in appointmentseries
	 * 
	 * @param db Appointment information from database
	 * @return
	 */
	private String getIgnoredAppointments(String isReString, Appointment db) {
		StringBuilder ignoredIds = new StringBuilder();
		if("true".equals(isReString.toLowerCase())) {
			List<Appointment> list = appointmentService.loadAppointmentListBySeriesAndStartTime(null, db);
			if(null != list && !list.isEmpty()) {
				for(Appointment a : list) {
                    ignoredIds.append(a.getAppointmentId());
                    ignoredIds.append(",");
				}
                ignoredIds.deleteCharAt(ignoredIds.length() - 1);
			}
		} else {
			ignoredIds.append(db.getAppointmentId());
		}
		return ignoredIds.toString();
	}
	
	/**
	 * Show apponitments of searched customer day view after customer search
	 * 
	 * @param customer customerId
	 * @param date query date
	 * @param services serviceIds that selected
	 * @param instructors instructorIds that selected
	 * @param rooms roomIds that selected
	 * @param appointments activityIds that selected
	 * @return
	 */
	@RequestMapping(LOAD_APP_BY_CUSTORMER_MAPPING)
	@ResponseBody
	public Map<String, Object> loadAppointmensByCustomer(long customer, String date, String services, String instructors, String rooms, String appointments, String type) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.loadAppointmensByCustomer: start");
		}
		Map<String, Object> map = new HashMap<String, Object>();
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);

		String[] st = date.split(SPLITOR_SLASH);
		Date startTime = new DateTime(Integer.parseInt(st[2]), Integer.parseInt(st[0]), Integer.parseInt(st[1]), ZERO_TIME, ZERO_TIME, ZERO_TIME).toDate();
		Date endTime = new DateTime(Integer.parseInt(st[2]), Integer.parseInt(st[0]), Integer.parseInt(st[1]), END_TIME[0], END_TIME[1], END_TIME[1]).toDate();
		List<Appointment> list = appointmentService.loadByCustomer(customer, startTime, endTime, profileId);
		
		Long[] instructorIds = SystemUtil.getIdArray(instructors);
		List<Instructor> instructorList = instructorService.getInstructorListByIds(instructorIds);
		Long[] roomIds = SystemUtil.getIdArray(rooms);
		List<Room> roomList = roomService.getRoomListByIds(roomIds);
		CustomerDTO dto = customerService.getCustomerById(customer);
		
		//For GCSS-482
		if(null != type && 0 != type.length()) {
			if(ROOM_FILTER_TYPE.equals(type.toLowerCase().trim())) {
				List<CalendarViewUnavailableHourDTO> roomViewUnavailableHourDTOs = appointmentService.getRoomViewUnavailableHourList(profileId, startTime);
				map.put(ROOM_VIEW_UNAVAILABLE_HOUR_DTOS_KEY, roomViewUnavailableHourDTOs);
			} else {
				List<InstructorViewUnavailableHourDTOWrapper> instructorViewUnavailableHourDTOWrappers = appointmentService.getInstructorViewUnavailableHourList(profileId, instructorIds, startTime);
				map.put(INSTRUCTOR_VIEW_UNAVAILABLE_HOUR_DTOS_KEY, instructorViewUnavailableHourDTOWrappers);
			}
		}
		
		map.put(CUSTOMER_KEY, dto);
		map.put(INSTRUCTORS_KEY, instructorList);
		map.put(ROOMS_KEY, roomList);
		map.put(APPOINTMENTS_KEY, list);
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.loadAppointmensByCustomer: end");
		}
		return map;
	}
	
	/**
	 * Show apponitments of searched customer week view after customer search
	 * 
	 * @param customer customerId
	 * @param date query date
	 * @param services serviceIds that selected
	 * @param instructors instructorIds that selected
	 * @param rooms roomIds that selected
	 * @param appointments activityIds that selected
	 * @return
	 */
	@RequestMapping(LOAD_APP_BY_CUSTOMR_ON_WEEK_MAPPING)
	@ResponseBody
	public Map<String, Object> loadAppointmentsByCustomerOnWeek(long customer, String date, String services, String instructors, String rooms, String appointments) {
		
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.loadAppointmentsByCustomerOnWeek: start");
		}
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);

		String[] dateArray = date.split(SPLITOR_DASH);
		String[] st = dateArray[0].split(SPLITOR_SLASH);
		String[] et = dateArray[1].split(SPLITOR_SLASH);
		Date startTime = new DateTime(Integer.parseInt(st[2]), Integer.parseInt(st[0]), Integer.parseInt(st[1]), ZERO_TIME, ZERO_TIME, ZERO_TIME).toDate();
		Date endTime = new DateTime(Integer.parseInt(et[2]), Integer.parseInt(et[0]), Integer.parseInt(et[1]), END_TIME[0], END_TIME[1], END_TIME[1]).toDate();
		
		Map<String, Object> returnMap = new HashMap<String, Object>();
		Map<String, Object> map = appointmentService.loadByWeek(customer, siteId, profileId, startTime, endTime, services, instructors, rooms, appointments, date);
		CustomerDTO dto = customerService.getCustomerById(customer);
		
		//For GCSS-482
		List<WeekViewUnavailableHourDTOWrapper> weekViewUnavailableHourDTOWrappers = appointmentService.getWeekViewUnavailableHourDTOMap(profileId, date);
		//map.put(WEEK_VIEW_UNAVAILABLE_HOUR_DTOS_KEY, weekViewUnavailableHourDTOWrappers);
		
		returnMap.put(WEEK_VIEW_UNAVAILABLE_HOUR_DTOS_KEY, weekViewUnavailableHourDTOWrappers);
		returnMap.put(RESULT_MAP_KEY, map);
		returnMap.put(CUSTOMER_KEY, dto);
		
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.loadAppointmentsByCustomerOnWeek: end");
		}
		return returnMap;
	}
	
	/**
	 * Show apponitments of searched customer month view after customer search
	 * 
	 * @param customer cutomerId
	 * @param date query date
	 * @param services serviceIds that selected
	 * @param instructors instructorIds that selected
	 * @param rooms roomIds that selected
	 * @param appointments activityIds that selected
	 * @param model internal object of Spring MVC
	 * @return
	 */
	@RequestMapping(LOAD_APP_BY_CUSTOMR_ON_MONTH_MAPPING)
	public String loadAppointmentsByCustomerOnMonth(long customer, String date, String services, String instructors, String rooms, String appointments, Model model) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.loadAppointmentsByCustomerOnMonth: start");
		}
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);

		Map<String, Object> pMap = appointmentService.getDeliveryMonthDateParam(date);
		String firstDate = (String) pMap.get("firstDate");
		String lastDate = (String) pMap.get("lastDate");
		String[] st = firstDate.split(SPLITOR_SLASH);
		String[] et = lastDate.split(SPLITOR_SLASH);
		
		Date startTime = new DateTime(Integer.parseInt(st[2]), Integer.parseInt(st[0]), Integer.parseInt(st[1]), ZERO_TIME, ZERO_TIME, ZERO_TIME).toDate();
		Date endTime = new DateTime(Integer.parseInt(et[2]), Integer.parseInt(et[0]), Integer.parseInt(et[1]), END_TIME[0], END_TIME[1], END_TIME[1]).toDate();
		
		Map<String, Object> monthMap = appointmentService.loadByMonth(customer, siteId, profileId, date, startTime, endTime, services, instructors, rooms, appointments);
		Map<String, Object> dateMap = new HashMap<String, Object>();
		if(date.contains(SPLITOR_DASH)) {
			String startDate = date.split(SPLITOR_DASH)[0];//split the date duration by "-"
			String year = startDate.split(SPLITOR_SLASH)[2];
			String month = startDate.split(SPLITOR_SLASH)[0];
			
			int yearInt = Integer.parseInt(year);
			int monthInt = Integer.parseInt(month);
			int preYear = yearInt; 
			int nextYear = yearInt;
			int nextMonth = monthInt + 1;
			int previousMonth = monthInt - 1;
			if(12 == monthInt) {//if current month is Dec, the next month will be Jan of next year
				nextMonth = 1;
				nextYear = yearInt + 1;
			}
			if(1 == monthInt) {//if current month is Jan,the previous month will be Dec of previous year
				previousMonth = TWELVE;
				preYear = yearInt - 1;
			}
			String currDate = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(new Date());
			dateMap.put(CURR_DATE_KEY, currDate);
			dateMap.put(PREVIOUS_YEAR_KEY, preYear);
			dateMap.put(NEXT_YEAR_KEY, nextYear);
			dateMap.put(CURR_MONTH_KEY, 1 == month.length() ? "0" + month : month);
			dateMap.put(CURR_YEAR_KEY, year);
			dateMap.put(PRE_MONTH_KEY, previousMonth < 10 ? "0" + previousMonth : previousMonth);
			dateMap.put(NEXT_MONTH_KEY, nextMonth < 10 ? "0" + nextMonth : nextMonth);
		}
		model.addAttribute(RESULT_MAP_KEY, monthMap);
		model.addAttribute(DATE_MAP_KEY, dateMap);
		model.addAttribute(SUN_TO_SAT_KEY, DateTimeUtil.getDaysOfWeekList());
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.loadAppointmentsByCustomerOnMonth: end");
		}
		return "includes/result_month";
	}
	
	@RequestMapping(LOAD_INSTRUCTOR_BY_ACTIVITY_MAPPING)
	@ResponseBody
	public Map<String, Object> loadInstructorByActivity(long activityId) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("CalendarController.loadInstructorByActivity: start");
		}
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		Location location = (Location) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		Map<String, Object> map = new HashMap<String, Object>();
		
		
		Activity activity = activityService.getActivityByActivityId(activityId);
		RequiresInstructor ri = activity.getRequiresInstructor();
		Appointment a = appointmentService.getClosestScheduledAppByActivity(activityId);
		
		List<InstructorInfoDTO> instructorDTOs = null;
		if(!RequiresInstructor.N.equals(ri)) {
			instructorDTOs = instructorService.loadByActivityAndLocation(activityId, location.getLocationId());
			if(LOG.isDebugEnabled()) {
				LOG.debug("Get instructor List {} from CalendarController.loadInstructorByActivity", instructorDTOs);
			}
			//For GCSS-55,Post Conditions:4
			//If the returned instructor list is more than 1 elemenet,
			//try to get the to get the instructor info from the closest schecudued appointment by activityId
			if(null != a) {
				if(null != instructorDTOs && !instructorDTOs.isEmpty()) {
					for(int i=0; i<instructorDTOs.size(); i++) {
						InstructorInfoDTO dto = instructorDTOs.get(i);
						Instructor instructor = a.getInstructor();
						if(null != instructor && a.getInstructor().getInstructorId().equals(Long.parseLong(dto.getInstructorId()))) {
							dto.setIsClosestScheduled("true");
							Collections.swap(instructorDTOs, 0, i);
							break;
						}
					}
				}
			}
		}
		
		List<RoomDTO> roomDTOs = null;
		try {
			roomDTOs = roomService.findByProfileIdAndActivityIds(profileId, activity.getActivityId());
		} catch (Exception e) {
			LOG.error("Caught {} when loading room list in CalendarController.loadActivitiesByService", e);
		}
		if(null != a) {
			if(null != roomDTOs && !roomDTOs.isEmpty()) {
				for(int i=0; i<roomDTOs.size(); i++) {
					RoomDTO r = roomDTOs.get(i);
					if(r.getRoomId().equals(a.getRoom().getRoomId())) {
						Collections.swap(roomDTOs, 0, i);
						break;
					}
				}
			}
		}
		
		//For Bug-267
		//Loading the duration by current activity dynamically
		Long minDuration = activity.getMinimumDuration();
		Long maxDuration = null == activity.getMaximumDuration() ? null : activity.getMaximumDuration();
		List<DurationDTO> durationDTOs = new DurationBuilder().buildDurationListByMinAndMaxDuration(minDuration, maxDuration);
		
		map.put(DURATION_DTOS_KEY, durationDTOs);
		map.put(ROOM_DTOS_KEY, roomDTOs);
		map.put(INSTRUCTOR_DTOS_KEY, instructorDTOs);
		return map;
	}
	
	/**
	 * Update appointment controller method
	 * 
	 * @param data Posted data from  UI form as JSON data
	 * @return
	 */
	@RequestMapping(UPDATE_APP_MAPPING)
	@ResponseBody
	public Map<String, Object> updateAppointment(String data) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("Began to update Appoinment by submitted data{}", data);
		}
		Map<String, Object> map = new HashMap<String, Object>();

		CreateAppointmentDTO dto = this.buildAppointmentDTO(data);
		if(null == dto) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Build appointmentDTO error!");
			return map;
		}
		//##############################################
		boolean verifyStatus= false;
		//Date dateVal = getDateFromString(dto.getStartDate());
		//Bug Logic
				//Date dateVal = getDateFromString(dto.getStartDate());
				 // int dateval1 = dateVal.getDay();

				//Corrected Logic.
				Date st_1 = new Date();
				if((null != dto.getStartDate() && 0 != dto.getStartDate().length())) {
					st_1 = DateTime.parse(dto.getStartDate() + " " + "00:00", DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
					if(null != dto.getStartTime() && 0 != dto.getStartTime().length()) {
						st_1 = DateTime.parse(dto.getStartDate() + " " + dto.getStartTime(), DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
					}
				}
				 Calendar cal = Calendar.getInstance(); 
				 cal.setTime(st_1); 
				 int dateval1 = cal.get(Calendar.DAY_OF_WEEK);
				 
		 if(dto.getDuration() == null || "".equals((dto.getDuration().trim()))){
				map.put(MESSAGE_KEY, VALIDATION_TIME_NULL);
				map.put(STATUS_KEY, false);
				return map;
		} 
				 
		String endTimeStr = CalendarUtil.getEndTime(dto.getStartTime(), dto.getDuration());
		boolean verifyAppointmentServiceMode =false;
		String service_msg="";
		try {
			//verifyAppointmentServiceMode = verifyAppointmentServiceModeStatus(dto.getInstructorId(),dateVal.getDay(),dto.getServiceId(),dto.getStartTime(),endTimeStr);
			verifyAppointmentServiceMode = verifyAppointmentServiceModeStatus(dto.getInstructorId(),dateval1,dto.getServiceId(),dto.getStartTime(),endTimeStr);
		} catch (Exception e1) {
			// TODO Auto-generated catch block
			LOG.error("Caught {} when check verifyAppointmentServiceModeStatus  update Appointment", e1);
		}
		if(dto.getServiceId() == 1){
			service_msg ="The In-store Appointment update conflict with Online Availability slot";
		}
		if(dto.getServiceId() == 20){
			service_msg ="The Online Appointment update conflict with In-store Availability slot";
		}
 
		if(verifyAppointmentServiceMode){
			map.put(MESSAGE_KEY, service_msg);
			map.put(STATUS_KEY, false);
			return map;
		}
		//###################################################
		//Log for conflict appointment in Update appointment -GSSp 299
		if(dto != null && dto.getAppointmentId() != null && dto.getAppointmentSeriesId() != null)	{
			LOG.error("Before Creating Appointment -  log underHostAddress: " +SystemUtil.getHostAddress() == null ?  "Invalide Host Name - " :SystemUtil.getHostAddress() 
					+ " Appointment updated with the appointment id: " + dto.getAppointmentId() 
							+ " Appointment updated with the appointmentseriesId "+ dto.getAppointmentSeriesId());
		}
		//Attributes in session
		Person person = (Person) RequestContextHolder.currentRequestAttributes().getAttribute(AppConstants.PERSON_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		
		//Construct some LocationProfile object and Site object that needed
		LocationProfile locationProfile = new LocationProfile();
		locationProfile.setProfileId(profileId);
		Site site = new Site();
		site.setSiteId(siteId);
		
		try {
				
			//Set attributes of AppointmentSeries
			AppointmentSeries series = dto.getAppointmentSeries();
			series.setUpdatedBy(person);
			series.setLocationProfile(locationProfile);
			series.setSite(site);
			
			//Set attributes of Appointment
			Appointment appointment = dto.getAppointment();
			appointment.setLocationProfile(locationProfile);
			//GSSP-279 changes
			
			try {
				appointment.setDuration(Integer.parseInt(dto.getDuration()));
			} catch (NumberFormatException e) {
				LOG.error("CreateAppointmentDto.getAppointment: number format error");
			}
		    //For GSSP-243 send email with cc Associates/Managers/Leads

			appointment.setUpdatedBy(person);
			appointment.setSite(site);
			
			appointment.setAppointmentSeries(series);//appointment object from UI
			Appointment appFromDb = appointmentService.getAppointment(appointment.getAppointmentId());//appointment object from database
			//ProdIssue start
			Appointment appObj=appointmentService.loadAppointmentListBySeries(dto, appFromDb).get(0); //prodIssue end
			//Add validation to this appointment,ONLY can update update appontment and send mail after VALIDATION SUCCESS
			map = this.checkAppointmentForUpdate(dto, appointment, appFromDb, appObj);
			if((Boolean)map.get(STATUS_KEY)){
				//For bug GCSS-348
				boolean flagForEmail = checkApptForEmail(dto, appointment, appFromDb);
				
				//Changes made for GSSP-229
				
				
				/*if(!flagForEmail){*/
					//GCSS-577
					//boolean isModifiedUI = isModifiedUI(appointment, appFromDb);
				    Set<Customer> cancelSet = cancelCustomer(appointment.getCustomers(),appFromDb.getCustomers());
					/*if(isModifiedUI && cancelSet.size() > 0){
						appointmentEmailService.sendEmailForCancelAppt(appointment, dto.getRecurringStatus(), Integer.parseInt(dto.getDuration()), cancelSet);
					}*/
					
					boolean isOnlyAddCustomer = isOnlyAddCustomer(appointment, appFromDb);//For bug GCSS-546
					/*if(isOnlyAddCustomer){
						//Check if nothing changed but adding customers
						//Build a customer with value of the new customer to appointment
						//Invoke the mothod of createAppontment to send mail to the new added customer
						appointmentEmailService.sendEmailForCreateAppt(appointment, dto.getIsRecurring(), Integer.parseInt(dto.getDuration()));
					}*/
					
					//Changes made for GSSP-229
					if(cancelSet.size() > 0  || isOnlyAddCustomer)
					{
						map.put(STATUS_KEY, false);
						map.put(MESSAGE_KEY, "Customers cannot be modified");
						return map;
					}
						
					
					
				//}
					
					//End of Changes made for GSSP-229
								
				if((Boolean)map.get(STATUS_KEY)){	
					//Changes made for GSSP-225 
					boolean isModified = validationCheck(appointment, appFromDb);
					if(isModified){				
						
				//appointmentService.updateAppointment(series, appFromDb);												
				appointmentService.updateAppointment(dto, appFromDb, person,appObj);} //added appointment object prodIssue2
					else{
						map.put(STATUS_KEY, false);
						map.put(MESSAGE_KEY, "No Changes Found!");
						return map;
					}
				}
				//For bug GCSS-348
				if(flagForEmail){
					//For bug GCSS-411
					if(appointment.getCustomers() != null){
						
						//Changes made for LES-119
						if(dto.getRecurringStatus().equalsIgnoreCase("2"))
						{		
							//For GSSP-243 send email with cc Associates/Managers/Leads
							//GSSP-278 Changes Made for Modify Single appt-starts
							 String instructorName=appointmentService.findInstructorName(dto);//This part newly created for adding instructor name
							 Room room = roomService.getRoom(dto.getRoomId());
								Activity  activityName  = activityService.getActivityByActivityId(Long.parseLong(dto.getActivityId()));
							
							appointmentEmailService.sendEmailForModifyAppt(appointment, dto.getRecurringStatus(), 
									Integer.parseInt(dto.getDuration()),instructorName,room,activityName,profileId);
						}	
						else
						{	//Existing Instructor name adding is starts here
							String instructorName = "Not Applicable";
							
							 if (null != dto.getInstructorId())
							 {
						
								Instructor instructor = instructorService.getInstructor(dto.getInstructorId());	
								
								 instructorName =( org.apache.commons.lang.StringUtils.isBlank(instructor.getPerson().getFirstName())?
										"":instructor.getPerson().getFirstName()) + " " 									
										+ (org.apache.commons.lang.StringUtils.isBlank(instructor.getPerson().getLastName())?"":
											instructor.getPerson().getLastName());
								
							
							 }//Existing Instructor name adding is Ends here
							 Room room = roomService.getRoom(dto.getRoomId());
								Activity  activityName  = activityService.getActivityByActivityId(Long.parseLong(dto.getActivityId()));
							
							//For GSSP-243 send email with cc Associates/Managers/Leads
						//	appointmentEmailService.sendEmailForModifySingleAppt(appointment, dto.getRecurringStatus(),
						//			Integer.parseInt(dto.getDuration()),instructorName,room,activityName,profileId);
							//OLL-3188
							asyncEmailService.sendEmailForModifySingleApptAsync(appointment, dto.getRecurringStatus(),
									Integer.parseInt(dto.getDuration()),instructorName,room,activityName,profileId);
						}	//GSSP-278 Changes Made for Modify Single appt-ends
						
					}
				}
				map.put(STATUS_KEY, true);
				map.put(MESSAGE_KEY, "Update Appointment Success!");
			}
		} catch (Exception e) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Update Appointment Failed!");
			LOG.error("Caught {} when updating Appointment in CanlendarController.updateAppointment", e);
		}
		
		return map;
	}
		//GSSP-225 changes
		private boolean validationCheck(Appointment apptUI, Appointment appFromDb) {
			
			if( ! apptUI.getActivity().getActivityId().equals(appFromDb.getActivity().getActivityId()) ||
				  !  apptUI.getStartTime().equals(appFromDb.getStartTime()) ||
				  !  apptUI.getEndTime().equals(appFromDb.getEndTime()) ||	
				 	//(StringUtils.isEmpty(apptUI.getAppointmentSeries().getSeriesStartTime()) == !StringUtils.isEmpty(appFromDb.getAppointmentSeries().getSeriesStartTime())) ||
				    //(!StringUtils.isEmpty(apptUI.getAppointmentSeries().getSeriesStartTime()) && !StringUtils.isEmpty(appFromDb.getAppointmentSeries().getSeriesStartTime())  && ! apptUI.getAppointmentSeries().getSeriesStartTime().equals(appFromDb.getAppointmentSeries().getSeriesStartTime()))||
				    (StringUtils.isEmpty(apptUI.getAppointmentSeries().getSeriesEndTime()) == !StringUtils.isEmpty(appFromDb.getAppointmentSeries().getSeriesEndTime())) ||
				    (!StringUtils.isEmpty(apptUI.getAppointmentSeries().getSeriesEndTime()) && !StringUtils.isEmpty(appFromDb.getAppointmentSeries().getSeriesEndTime())  && ! apptUI.getAppointmentSeries().getSeriesEndTime().equals(appFromDb.getAppointmentSeries().getSeriesEndTime()))||				  
				    (StringUtils.isEmpty(apptUI.getBandName()) == !StringUtils.isEmpty(appFromDb.getBandName())) ||
				    (!StringUtils.isEmpty(apptUI.getBandName()) == StringUtils.isEmpty(appFromDb.getBandName())) ||
				    (!StringUtils.isEmpty(apptUI.getBandName()) && !StringUtils.isEmpty(appFromDb.getBandName())  && ! apptUI.getBandName().equals(appFromDb.getBandName()))||
				    (null != apptUI.getInstructor() && null != appFromDb.getInstructor() && ! apptUI.getInstructor().getInstructorId().equals( appFromDb.getInstructor().getInstructorId()))||				  
				  	(! apptUI.getRoom().getRoomId().equals(appFromDb.getRoom().getRoomId())) ||
				  	(StringUtils.isEmpty(apptUI.getNote()) == !StringUtils.isEmpty(appFromDb.getNote())) ||
				  	(!StringUtils.isEmpty(apptUI.getNote()) == StringUtils.isEmpty(appFromDb.getNote())) ||
				  	(!StringUtils.isEmpty(apptUI.getNote()) && !StringUtils.isEmpty(appFromDb.getNote()) && ! apptUI.getNote().equals(appFromDb.getNote())))
			{
		       
						return true;
			}
		
		    return false;
		}
	
	//For bug GCSS-546
	private boolean isOnlyAddCustomer(Appointment apptUI, Appointment appFromDb) {
		if(!apptUI.getActivity().getActivityId().equals(appFromDb.getActivity().getActivityId())){
	        return false;
	    }
	    if(!StringUtils.isEmpty(apptUI.getBandName()) && !apptUI.getBandName().equals(appFromDb.getBandName())){
	        return false;
	    }
	    if(apptUI.getInstructor()!=null && appFromDb.getInstructor()!=null && !apptUI.getInstructor().getInstructorId().equals(appFromDb.getInstructor().getInstructorId())){
	        return false;
	    }
	    if(apptUI.getRoom()!=null && !apptUI.getRoom().getRoomId().equals(appFromDb.getRoom().getRoomId())){
	        return false;
	    }
	    if(!StringUtils.isEmpty(apptUI.getNote()) && !apptUI.getNote().equals(appFromDb.getNote())){
	       return false;
	    }
	    Set<Customer> set = addCustomer(apptUI.getCustomers(),appFromDb.getCustomers());
	    if(set.size() > 0){
	       apptUI.setCustomers(set);
	       return true;
	    }
	    return false;
	}

	//For bug GCSS-546
	private Set<Customer> addCustomer(Set<Customer> customers, Set<Customer> pCustomers){
	  Set<Customer> addCustomer = new HashSet<Customer>();
	  if(customers!=null){
	      Iterator<Customer> itr = customers.iterator();
	      Customer cm;
	      while(itr.hasNext()){
	         cm = itr.next();
	         if(!pCustomers.contains(cm))
	            addCustomer.add(cm);
	      }
	  }
	  return addCustomer;
	  
    }
	
	//GCSS-577
	private Set<Customer> cancelCustomer(Set<Customer> customers, Set<Customer> pCustomers){
	  Set<Customer> cancelCustomer = new HashSet<Customer>();
	    
	  if(pCustomers!=null){
	      Iterator<Customer> itr = pCustomers.iterator();
	      Customer cm;
	      while(itr.hasNext()){
	         cm = itr.next();         
	        
	         //Fix for Null Pointer - Jira undecided
	         if(customers != null)
	         {    
		         if(!customers.contains(cm))
		        	 cancelCustomer.add(cm);
		         
	         }
	         
	         else
	         {
	        	 cancelCustomer.add(cm);	
	         }
	      }
	  }
	  return cancelCustomer;
	  
    }
	
	//GCSS-577
	private boolean isModifiedUI(Appointment apptUI, Appointment appFromDb) {
		if(!apptUI.getActivity().getActivityId().equals(appFromDb.getActivity().getActivityId())){
	        return false;
	    }
	    if(!StringUtils.isEmpty(apptUI.getBandName()) && !apptUI.getBandName().equals(appFromDb.getBandName())){
	        return false;
	    }
	    if(apptUI.getInstructor()!=null && appFromDb.getInstructor()!=null && !apptUI.getInstructor().getInstructorId().equals(appFromDb.getInstructor().getInstructorId())){
	        return false;
	    }
	    if(apptUI.getRoom()!=null && !apptUI.getRoom().getRoomId().equals(appFromDb.getRoom().getRoomId())){
	        return false;
	    }
	    if(!StringUtils.isEmpty(apptUI.getNote()) && !apptUI.getNote().equals(appFromDb.getNote())){
	       return false;
	    }
	    return true;
	}
	
	//For bug GCSS-348
	private boolean checkApptForEmail(CreateAppointmentDTO appointmentDTO, Appointment apptUI, Appointment appFromDb) {
		//For  GSSP-153 Changes
		Long instructorUi=null, instructorDb = null;
		
		if(null != appointmentDTO.getInstructorId() && null != appFromDb.getInstructor() && null != appFromDb.getInstructor().getInstructorId())
		{	
		 instructorUi=appointmentDTO.getInstructorId();
		 instructorDb=appFromDb.getInstructor().getInstructorId();
		} 
		boolean isAppointmentDatesChanged = false;
		
		if(Boolean.parseBoolean(appointmentDTO.getIsRecurring())) {
			// For GCSS-606,send mail to customer if endDate changed
			Date endDateOfUi = null;
			try {
				if(null != appointmentDTO.getEndDate() && 0 != appointmentDTO.getEndDate().length()) {
					endDateOfUi = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).parse(appointmentDTO.getEndDate());
				}
			} catch (ParseException e) {
				LOG.error("Parse date error {}", e);
			}
			
			Date endDateOfDb = appFromDb.getAppointmentSeries().getSeriesEndTime();
			
			//For  GSSP-153 Changes
			if(null == endDateOfUi && null == endDateOfDb) {
				if(apptUI.getStartTime().equals(appFromDb.getStartTime()) && Integer.parseInt(appointmentDTO.getDuration()) == appFromDb.getDuration()) {//For bug GCSS-447
					//return false;
					isAppointmentDatesChanged = false;
				} else {
					//return true;
					isAppointmentDatesChanged = true;
				}
				
				
			} else {
				boolean isEndDateChanged = false;
				if(null != endDateOfUi) {
					isEndDateChanged = endDateOfUi.equals(endDateOfDb);
				} else if(null != endDateOfDb) {
					isEndDateChanged = endDateOfDb.equals(endDateOfUi);
				}
				if(apptUI.getStartTime().equals(appFromDb.getStartTime()) && isEndDateChanged && Integer.parseInt(appointmentDTO.getDuration()) == appFromDb.getDuration()) {//For bug GCSS-447
					//return false;
					isAppointmentDatesChanged = false;
				} else {
					//return true;
					isAppointmentDatesChanged = true;
				}
				
			} 
			
			
		} else {
			//For bug GCSS-475
			//For  GSSP-153 Changes
		
			if((apptUI.getStartTime().equals(appFromDb.getStartTime()) && Integer.parseInt(appointmentDTO.getDuration()) == appFromDb.getDuration())) {//For bug GCSS-447
				isAppointmentDatesChanged = false;
			} else {
				isAppointmentDatesChanged = true;
			}
		}
	
		//For  GSSP-153 Changes
		if(isAppointmentDatesChanged){
			return isAppointmentDatesChanged;
		}
		else if(null!=instructorUi && null!=instructorDb){
			if(apptUI.getInstructor().getInstructorId().equals(appFromDb.getInstructor().getInstructorId()) && appointmentDTO.getInstructorId().equals(appFromDb.getInstructor().getInstructorId())) {
				
				return false;
			} else {
				return true;
			}
		}
		else {
			boolean isInstructorChanged = false;
			if(null != instructorUi) {
				isInstructorChanged = instructorUi.equals(instructorDb);
			} else if(null != instructorDb) {
				isInstructorChanged = instructorDb.equals(instructorUi);
			}
			
				return isInstructorChanged;			
		}
	}

	private CreateAppointmentDTO buildAppointmentDTO(String data) {
		ObjectMapper mapper = new ObjectMapper();
		CreateAppointmentDTO dto = null;
		try {
			dto = mapper.readValue(data, CreateAppointmentDTO.class);
		} catch (JsonParseException e1) {
			LOG.error("Caught a JsonParseException from CalendarController.createAppointment: {}", e1);
		} catch (JsonMappingException e1) {
			LOG.error("Caught a JsonMappingException from CalendarController.createAppointment: {}", e1);
		} catch (IOException e1) {
			LOG.error("Caught a IOException from CalendarController.createAppointment: {}", e1);
		}
		return dto;
	}
	
	@RequestMapping(LOAD_SERVICE_LIST_MAPPING)
	@ResponseBody
	public List<ServiceDTO> getServiceList() {
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		//List<Service> list = serviceService.findServiceBySite(siteId);
		List<Service> list = locationProfileService.findServiceByProfileIdAndEnabled(profileId, Enabled.Y);
		List<ServiceDTO> dtos = new LinkedList<ServiceDTO>();
		if(null != list && !list.isEmpty()) {
			for(Service s : list) {
				dtos.add(new ServiceDTO(s.getServiceId(), s.getServiceName()));
			}
		}
		return dtos;
	}
	
	@RequestMapping(GET_SERVICE_BY_ID_MAPPING)
	@ResponseBody
	public Map<String, Object> getServiceById(long serviceId) {
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		Map<String, Object> map = new HashMap<String, Object>();
		Service service = serviceService.getServiceById(serviceId);
		ServiceDTO dto = new ServiceDTO();
		if(null != service) {
			dto.setServiceId(service.getServiceId());
			dto.setServiceName(service.getServiceName());
			AllowBandName allowBandName = service.getAllowBandName();
			if(AllowBandName.Y.equals(allowBandName)) {
				dto.setAllowBandName(true);
			} else if(AllowBandName.N.equals(allowBandName)) {
				dto.setAllowBandName(false);
			}
		}
		List<ActivityDTO> activityDTOs = new LinkedList<ActivityDTO>();
		try {
			//activityDTOs = activityService.loadActivityDTOsByService(serviceI);
			activityDTOs = locationProfileService.getActivityListByProfileAndService(profileId, serviceId);
		} catch (Exception e) {
			LOG.error("Caught {} when loading activity list in CalendarController.loadActivitiesByService", e);
		}
		
		map.put(ACTIVITY_DTOS_KEY, activityDTOs);
		map.put(SERVICE_KEY, dto);
		return map;
	}
	
	/**
	 * Load the fitler list of calendar page
	 * 
	 * @param services
	 * @param instructors
	 * @param rooms
	 * @param acitivities
	 * @return
	 */
	@RequestMapping(GENERATE_DYNAMIC_FILTER_FROM_SERVICE_MAPPING)
	@ResponseBody
	public Map<String, Object> generateDynamicFilterListMapFromService(String services, String instructors, String rooms, String appointments , String unselectedActivitiesFromCache) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("Start to load calendar page fiter list by serviceIds {} & instructorIds {} & roomIds {} activityIds {} ", services,instructors, rooms, appointments ,unselectedActivitiesFromCache);
		}
		//get profileId & locationId from session
		
		Location location = (Location) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long locationId = location.getLocationId();
		
		Map<String, Object> map = new HashMap<String, Object>();
		
		Long[] serviceIds = SystemUtil.getIdArray(services);
		
		List<String> list = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(unselectedActivitiesFromCache);
		
		List<Long> unselectedActivitesCache = Lists.transform(list, new Function<String, Long>(){
			@Override
			public Long apply(String input) {
				return Longs.tryParse(input);
			}});
		Set<Long> unselectedActivitesCacheSet = Sets.newHashSet(unselectedActivitesCache);
		
		//activity list
		List<ActivityDTO> activityDTOs = new LinkedList<ActivityDTO>();
		
		if(null != serviceIds && 0 != serviceIds.length) {
			activityDTOs = locationProfileService.findByProfileIdAndServiceIds(profileId, serviceIds);
			if(LOG.isDebugEnabled()) {
				LOG.debug("Got Activity list {}", activityDTOs);
			}
		}
		
		//instructor list
		List<InstructorInfoDTO> instructorDTOs = new LinkedList<InstructorInfoDTO>();
		//room list
		List<RoomDTO> roomDTOs = new LinkedList<RoomDTO>();
		
		if(null != activityDTOs && !activityDTOs.isEmpty()) {
			List<Long> activityIds = new LinkedList<Long>();
			for(int i=0; i<activityDTOs.size(); i++) {
				activityIds.add(activityDTOs.get(i).getActivityId());
			}
			Set<Long> activitySet = Sets.newHashSet(activityIds);
			
			Set<Long> shownSelectedActivity = Sets.difference(activitySet, unselectedActivitesCacheSet);
			
			instructorDTOs = instructorService.findByLocationIdAndActivityIds(locationId, shownSelectedActivity.toArray(new Long[0]));
			if(LOG.isDebugEnabled()) {
				LOG.debug("Got instructor list {}", instructorDTOs);
			}
			roomDTOs = roomService.findByProfileIdAndActivityIds(profileId, shownSelectedActivity.toArray(new Long[0]));
			if(LOG.isDebugEnabled()) {
				LOG.debug("Got Room list {}", roomDTOs);
			}
		}
		
		map.put(INSTRUCTOR_DTOS_KEY, instructorDTOs);
		map.put(ROOM_DTOS_KEY, roomDTOs);
		map.put(ACTIVITY_DTOS_KEY, activityDTOs);
		
		return map;
	}
	
	@RequestMapping(GENERATE_DYNAMIC_FILTER_FROM_ACTIVITY_MAPPING)
	@ResponseBody
	public Map<String, Object> generateDynamicFilterListMapFromActivity(String appointments) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("Start to load calendar page fiter list by activityIds {} ", appointments);
		}
		//get profileId & locationId from session
		Location location = (Location) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long locationId = location.getLocationId();
		
		Map<String, Object> map = new HashMap<String, Object>();
		
		Long[] activityIds = SystemUtil.getIdArray(appointments);
		
		//instructor list
		List<InstructorInfoDTO> instructorDTOs = new LinkedList<InstructorInfoDTO>();
		//room list
		List<RoomDTO> roomDTOs = new LinkedList<RoomDTO>();
		
		if(null != activityIds && 0 != activityIds.length) {
			instructorDTOs = instructorService.findByLocationIdAndActivityIds(locationId, activityIds);
			if(LOG.isDebugEnabled()) {
				LOG.debug("Got instructor list {}", instructorDTOs);
			}
			roomDTOs = roomService.findByProfileIdAndActivityIds(profileId, activityIds);
			if(LOG.isDebugEnabled()) {
				LOG.debug("Got Room list {}", roomDTOs);
			}
		}
		
		map.put(INSTRUCTOR_DTOS_KEY, instructorDTOs);
		map.put(ROOM_DTOS_KEY, roomDTOs);
		
		return map;
	}
	
	@RequestMapping(CANCEL_APPOINTMENT_MAPPING)
	@ResponseBody
	public Map<String, Object> cancelAppointment(String data, String cancelType, long cancelReason, @ModelAttribute("role")Role role) {
		ObjectMapper mapper = new ObjectMapper();
		CreateAppointmentDTO dto = null;
		String cancelReasonCode=null;
		
		try {
			dto = mapper.readValue(data, CreateAppointmentDTO.class);
		} catch (JsonParseException e1) {
			LOG.error("Caught a JsonParseException from CalendarController.cancelAppointment: {}", e1);
		} catch (JsonMappingException e1) {
			LOG.error("Caught a JsonMappingException from CalendarController.cancelAppointment: {}", e1);
		} catch (IOException e1) {
			LOG.error("Caught a IOException from CalendarController.cancelAppointment: {}", e1);
		}
		if(LOG.isDebugEnabled()) {
			LOG.debug("Cancel appointment from DTO {} ", dto);
		}
		Person person = (Person) RequestContextHolder.currentRequestAttributes().getAttribute(PERSON_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		
		Map<String, Object> map = new HashMap<String, Object>();
		map.put(STATUS_KEY, true);
		map.put(MESSAGE_KEY, "Cancel appointment successfully!");
		
		
		try {
//			boolean isCancelled = appointmentService.cancelAppointment(dto, person,role);
			//Appointment db = appointmentService.getAppointment(Long.parseLong());
			boolean isCancelled = appointmentService.cancelAppointment(Long.parseLong(dto.getAppointmentId()), cancelType,cancelReason, person,dto.getActivityId(),dto.getServiceId());
			if(!isCancelled) {
				map.put(STATUS_KEY, false);
				map.put(MESSAGE_KEY, "Cancel appointment failed!");
			}
			if(isCancelled){
				long profileId = (Long)RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY,
						RequestAttributes.SCOPE_SESSION);
				LocationProfile locationProfile = new LocationProfile();
				locationProfile.setProfileId(profileId);
				Appointment appointment = dto.getAppointment();
			    //For GSSP-243 send email with Bcc Associates/Managers/Leads

				appointment.setUpdatedBy(person);
				appointment.setLocationProfile(locationProfile);
				//For GSSP-98, email for Cancel single appointment in a series
				Appointment appInfo = appointmentService.getAppointment(Long.parseLong(dto.getAppointmentId()));
				String recStatus="3";
				if(CANCEL_SINGLE_APPOINTMENT.equals(cancelType.toString().trim().toLowerCase()) && 
							IsRecurring.Y.equals(appInfo.getAppointmentSeries().getIsRecurring())) {
					recStatus = "3";
				} else if(CANCEL_SINGLE_APPOINTMENT.equals(cancelType.toString().trim().toLowerCase())) {
					recStatus = "1";
				} else {
					recStatus = "2";
				}
				//GCSS-577
				//For GSSP-98, pass recStatus as second parameter
				
				//Changes made for LES-120
				if(recStatus.equalsIgnoreCase("2"))//GSSP-278 Changes Made for Cancel  appt-Starts
				{								 
				String instructorName=appointmentService.findInstructorName(dto);//This part newly created for adding instructor name
				Activity  activityName  = activityService.getActivityByActivityId(Long.parseLong(dto.getActivityId()));
				//Cancel Reason message will not dispaly for Rehearsal,Floor,Jumpstart in online lesson services
			/*	if(dto.getServiceId()== 0 || dto.getActivityId().equals("100")|| dto.getActivityId().equals("140")||dto.getActivityId().equals("20")|| dto.getActivityId().equals("120")){
					cancelReasonCode= null;
				}
				else{
					List<AppointmentCancelReason> CancelCodeList= appointmentService.getCancelReasonCode(cancelReason);
					cancelReasonCode=CancelCodeList.get(0).getCancelReason();
					
				}
				*/
					//For GSSP-243 send email with cc Associates/Managers/Leads
					appointmentEmailService.sendEmailForCancelAppt(appointment, recStatus, Integer.parseInt(dto.getDuration()),
							appointment.getCustomers(),instructorName,activityName,profileId);
				}	//GSSP-278 Changes Made for Cancel Single appt-ends
				else
				{	
					String instructorName = "Not Applicable";
				 if (null != dto.getInstructorId())
				 {	 
					
					Instructor instructor = instructorService.getInstructor(dto.getInstructorId());

					 instructorName =( org.apache.commons.lang.StringUtils.isBlank(instructor.getPerson().getFirstName())?
							"":instructor.getPerson().getFirstName()) + " " 									
							+ (org.apache.commons.lang.StringUtils.isBlank(instructor.getPerson().getLastName())?"":
								instructor.getPerson().getLastName());
									
				 }  
				 //GSSP-278 Changes Made for Cancel Single appt-starts
				 
				 Room room = roomService.getRoom(dto.getRoomId());
					Activity  activityName  = activityService.getActivityByActivityId(Long.parseLong(dto.getActivityId()));
					//Cancel Reason message will not dispaly for Rehearsal,Floor,Jumpstart in online lesson services
					/*if(dto.getServiceId()== 0 || dto.getActivityId().equals("100")|| dto.getActivityId().equals("140")||dto.getActivityId().equals("20")|| dto.getActivityId().equals("120")){
						cancelReasonCode= null;
					}
					else{
					List<AppointmentCancelReason> CancelCodeList= appointmentService.getCancelReasonCode(cancelReason);
											 cancelReasonCode=CancelCodeList.get(0).getCancelReason();
					
					}*/
					
					
					//Changes made for GSSP-243
					//OLL-8311
					//appointmentEmailService.sendEmailForCancelSingleAppt(appointment, recStatus, Integer.parseInt(dto.getDuration()),
					//		appointment.getCustomers(),instructorName,activityName,profileId);
					asyncEmailService.sendEmailForCancelSingleApptAsync(appointment, recStatus, Integer.parseInt(dto.getDuration()),
							appointment.getCustomers(),instructorName,activityName,profileId);
					 //GSSP-278 Changes Made for Cancel Single appt-ends
				 	
				}	
				
			}
		} catch (Exception e) {
			LOG.error("Caught an exception {} in CalendarController.cancelAppointment " + dto.getAppointmentId(), e);
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Cancel appointment failed!");
		}
		return map;
	}
//Changes made for GSSP-250 
	@RequestMapping(LOAD_CANCEL_REASON_LIST)
	@ResponseBody
	public  Map<String, Object>  cancelReasonList(String recStatus) {
	
		Map<String, Object> map = new HashMap<String, Object>();
		List<AppointmentCancelReason> cancelReasonList = new ArrayList<AppointmentCancelReason>();
		if(recStatus.equalsIgnoreCase("more")){
			cancelReasonList=appointmentService.getcancelReason("Y");
		}else{
			 cancelReasonList=appointmentService.getcancelReason("N");
		}
		String cancelReason="";
		for (int i = 0; i < cancelReasonList.size(); i++) {
			 cancelReason=cancelReasonList.get(i).getCancelReason();
		}
		map.put("selected_cancel_reason", cancelReason);
		map.put(cancel_Reason_List, cancelReasonList);
		return  map;
	}
	@RequestMapping(FIND_LATEST_FILTER_MAPPING)
	@ResponseBody
	public Map<String, Object> findLatestFilter() {
		
		Location location = (Location) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);

		//1.load service list
		List<Service> sList = locationProfileService.findServiceByProfileIdAndEnabled(profileId, Enabled.Y);
		
		//2.load activity list
		// Get activity list from LocationProfile
        List<Long> ids = new ArrayList<Long>();
        for(Service service:sList){
            ids.add(service.getServiceId());
        }
		Long[] serviceIds = ids.toArray(new Long[0]);

		List<ActivityDTO> aList = locationProfileService.findByProfileIdAndServiceIds(profileId, serviceIds);

        ids.clear();
        for (ActivityDTO activityDTO : aList) {
            ids.add(activityDTO.getActivityId());
        }
		Long[] activityIds = ids.toArray(new Long[0]);
		
		//3.load instructor list by acitivity
		List<InstructorInfoDTO> iList = instructorService.findByLocationIdAndActivityIds(location.getLocationId(), activityIds);
		
		//4.load room list by activity
		List<RoomDTO> rList = roomService.findByProfileIdAndActivityIds(profileId, activityIds);
		
		Map<String, Object> map = new HashMap<String, Object>();
		map.put(ROOMS_KEY, rList);
		map.put(INSTRUCTORS_KEY, iList);
		map.put(ACTIVITIES_KEY, aList);
		map.put(SERVICES_KEY, sList);
		
		return map;
	}
	
	/**
	 * For gcss-575,store the selected filter list and query appointments that associated with those filter list.
	 * 
	 * @param services
	 * @param instructors
	 * @param rooms
	 * @param appointments
	 * @return
	 */
	@RequestMapping(FIND_STORED_FILTER_MAPPING)
	@ResponseBody
	public Map<String, Object> findStoredFilter(String services, String instructors, String rooms, String appointments) {
		
		Location location = (Location) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);

		String activities = appointments;
		
		Long[] selectedServiceIds = SystemUtil.getIdArray(services);
		Long[] selectedInstructorIds = SystemUtil.getIdArray(instructors);
		Long[] selectedRoomIds = SystemUtil.getIdArray(rooms);
		Long[] selectedActivityIds = SystemUtil.getIdArray(activities);
		
		Map<String, Object> map = new HashMap<String, Object>();
		List<ServiceDTO> sList = new LinkedList<ServiceDTO>();
		List<ActivityDTO> aList = new LinkedList<ActivityDTO>();
		List<InstructorInfoDTO> iList = new LinkedList<InstructorInfoDTO>();
		List<RoomDTO> rList = new LinkedList<RoomDTO>();
		
		//1.load service list
		if(null != selectedServiceIds && 0 != selectedServiceIds.length) {
			/**
			 * Load the service list by parameters from page of selected services
			 */
			sList = locationProfileService.findServiceDTOListByProfileAndEnabled(profileId, Enabled.Y);
			for(Long id : selectedServiceIds) {
				for(ServiceDTO dto : sList) {
					if(id.toString().equals(dto.getServiceId().toString())) {
						dto.setIsSelectedService("Y");
					}
				}
			}
		
			//2.load activity list
			aList = locationProfileService.findByProfileIdAndServiceIds(profileId, selectedServiceIds);
			if(null != selectedActivityIds && 0 != selectedActivityIds.length) {
				for(Long id : selectedActivityIds) {
					for(ActivityDTO dto : aList) {
						if(id.toString().equals(dto.getActivityId().toString())) {
							dto.setIsSelectedActivity("Y");
						}
					}
				}
				
				//3.load instructor list by acitivity
				iList = instructorService.findByLocationIdAndActivityIds(location.getLocationId(), selectedActivityIds);
				if(null != selectedInstructorIds && 0 != selectedInstructorIds.length) {
					for(Long id : selectedInstructorIds) {
						for(InstructorInfoDTO dto : iList) {
							if(id.toString().equals(dto.getInstructorId().toString())) {
								dto.setIsSelectedInstructor("Y");
							}
						}
					}
				}
				
				//4.load room list by activity
				rList = roomService.findByProfileIdAndActivityIds(profileId, selectedActivityIds);
				if(null != selectedRoomIds && 0 != selectedRoomIds.length) {
					for(Long id : selectedRoomIds) {
						for(RoomDTO dto : rList) {
							if(id.toString().equals(dto.getRoomId().toString())) {
								dto.setIsSelectedRoom("Y");
							}
						}
					}
				}
			}
		}
		map.put(ROOMS_KEY, rList);
		map.put(INSTRUCTORS_KEY, iList);
		map.put(ACTIVITIES_KEY, aList);
		map.put(SERVICES_KEY, sList);
		return map;
	}
	
	@RequestMapping(LOAD_SERVICE_LIST_BY_ROOM_OR_INSTRUCTOR)
	@ResponseBody
	public Map<String, Object> loadServiceListByRoomOrInstructor(String data, String type) {
		
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		
		Map<String, Object> map = new HashMap<String, Object>();
		
		
		List<ServiceDTO> serviceList = new ArrayList<ServiceDTO>();
		Date startTime = new Date();
		List<StartTimeDTO> startTimeDTOs = new LinkedList<StartTimeDTO>();
		
		CreateAppointmentDTO dto=null;
		
		
		if(null != data && 0 != data.length()) {
			dto = this.buildAppointmentDTO(data);
			
			if(null == dto) {
				map.put(STATUS_KEY, false);
				map.put(MESSAGE_KEY, "Build appointmentDTO error!");
				return map;
			}
			
			
			
			
			
			if((null != dto.getStartDate() && 0 != dto.getStartDate().length())) {
				startTime = DateTime.parse(dto.getStartDate() + " " + "00:00", DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
				if(null != dto.getStartTime() && 0 != dto.getStartTime().length()) {
					startTime = DateTime.parse(dto.getStartDate() + " " + dto.getStartTime(), DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
				}
			}
//			startTime = DateTime.parse(dto.getStartDate() + " " + dto.getStartTime(), DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
			startTimeDTOs = appointmentService.generateStartTimeListByProfileAndDate(profileId, null, startTime);
			
			if(null != type && 0 != type.length()) {
				if(ROOM_FILTER_TYPE.equals(type.toLowerCase())) {
					serviceList = serviceService.loadByProfileAndRoomAndEnabled(profileId, dto.getRoomId(), Enabled.Y);
				} else if(INSTRUCTOR_FILTER_TYPE.equals(type.toLowerCase())) {
					serviceList = serviceService.loadByProfileAndInstructorAndEnabled(profileId, dto.getInstructorId(), Enabled.Y);
					startTimeDTOs = appointmentService.generateStartTimeListByProfileAndDate(profileId, dto.getInstructorId(), startTime);
				} 
			} else {
				List<Service> list = locationProfileService.findServiceByProfileIdAndEnabled(profileId, Enabled.Y);
				if(null != list && !list.isEmpty()) {
					for(Service s : list) {
						serviceList.add(new ServiceDTO(s.getServiceId(), s.getServiceName()));
					}
				}
			}
		} else {
			List<Service> list = locationProfileService.findServiceByProfileIdAndEnabled(profileId, Enabled.Y);
			if(null != list && !list.isEmpty()) {
				for(Service s : list) {
					serviceList.add(new ServiceDTO(s.getServiceId(), s.getServiceName()));
				}
			}
			startTimeDTOs = appointmentService.generateStartTimeListByProfileAndDate(profileId, null, startTime);
		}
		
		
		//Set the service list
		map.put(SERVICES_KEY, serviceList);
		//Set the time list filtered by profile
		map.put(START_TIME_DTOS_KEY, startTimeDTOs);
		
		return map;
	}
	
	@RequestMapping(LOAD_ACTIVITY_LIST_BY_APPOINTMENTMODEL)
	@ResponseBody
	public Map<String, Object> loadActivityListByAppointmentModel(String data, String type, @ModelAttribute("role")Role role) {
//System.out.println(role.getRoleId());

		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);

		Map<String, Object> map = new HashMap<String, Object>();
		
		CreateAppointmentDTO appDTO = this.buildAppointmentDTO(data);
		if(null == appDTO) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Build appointmentDTO error!");
			return map;
		}
		//System.out.println("appDTO;"+ appDTO.get);
		Service service = serviceService.getServiceById(appDTO.getServiceId());
		ServiceDTO serviceDTO = new ServiceDTO();
		//serviceDTO.setRoleId(role.getRoleId());
		if(null != service) {
			serviceDTO.setServiceId(service.getServiceId());
			serviceDTO.setServiceName(service.getServiceName());
			AllowBandName allowBandName = service.getAllowBandName();
			if(AllowBandName.Y.equals(allowBandName)) {
				serviceDTO.setAllowBandName(true);
			} else if(AllowBandName.N.equals(allowBandName)) {
				serviceDTO.setAllowBandName(false);
			}
		}
		List<ActivityDTO> activityDTOs = new LinkedList<ActivityDTO>();
		if(null != type && 0 != type.length()) {
			if(ROOM_FILTER_TYPE.equals(type.toLowerCase())) {
				activityDTOs = activityService.loadByProfileAndServiceAndRoom(profileId, appDTO.getServiceId(), appDTO.getRoomId());
			} else if(INSTRUCTOR_FILTER_TYPE.equals(type.toLowerCase())) {
				activityDTOs = activityService.loadByProfileAndServiceAndInstructor(profileId, appDTO.getServiceId(), appDTO.getInstructorId());
			} else {
				activityDTOs = locationProfileService.getActivityListByProfileAndService(profileId, appDTO.getServiceId());
			}
		} else {
			activityDTOs = locationProfileService.getActivityListByProfileAndService(profileId, appDTO.getServiceId());
		}
		map.put("role",role);
		map.put(SERVICE_KEY, serviceDTO);
		map.put(ACTIVITY_DTOS_KEY, activityDTOs);
		
		return map;
	}
	
	/**
	 * Reloading the instructor list or room list if activity is changed or startDate is changed 
	 * or time is changed or duration is changed(which will affect on endTime)
	 * 
	 * @param dto CreateAppointmentDTO dto
	 * @return
	 */
	@RequestMapping(LOAD_INSTRUCTOR_OR_ROOM_LIST_ON_CHANGE)
	@ResponseBody
	public Map<String, Object> loadInstructorOrRoomListOnChange(String data, String type, Long appId) {
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		
		Map<String, Object> map = new HashMap<String, Object>();
		
		CreateAppointmentDTO dto = this.buildAppointmentDTO(data);
		Long serviceId = null;
		
		Date startTime = new Date();
		if((null != dto.getStartDate() && 0 != dto.getStartDate().length())) {
			startTime = DateTime.parse(dto.getStartDate() + " " + "00:00", DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
			if(null != dto.getStartTime() && 0 != dto.getStartTime().length()) {
				startTime = DateTime.parse(dto.getStartDate() + " " + dto.getStartTime(), DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN_SLASH)).toDate();
			}
		} else {
			startTime = new DateTime(startTime).withTime(0, 0, 0, 0).toDate();
		}
		
		String durationString = dto.getDuration();
		int duration;
		if(null != durationString && 0 != durationString.length()) {
			duration = Integer.parseInt(durationString);
		} else {
			duration = 1440;
		}
		Date endTime = SystemUtil.getEndTimeIfOnMidnight(new DateTime(startTime).plusMinutes(duration).toDate());
		
        List<StartTimeDTO> startTimeDTOs = null;
        List<DurationDTO> durationDTOs = null;

        if (null != dto.getActivityId() && null != dto.getServiceId()) {
        	serviceId = dto.getServiceId();
            Activity activity = activityService.getActivityByActivityId(Long.parseLong(dto.getActivityId()));
            Long minDuration = activity.getMinimumDuration();
            Long maxDuration = null == activity.getMaximumDuration() ? null : activity.getMaximumDuration();
            durationDTOs = new DurationBuilder().buildDurationListByMinAndMaxDuration(minDuration, maxDuration);
            RequiresInstructor ri = activity.getRequiresInstructor();
            if (!RequiresInstructor.N.equals(ri)) {
                List<InstructorInfoDTO> instructorList = instructorService.loadInstructorListByProfileIdAndActivityIdAndDateTime(profileId, dto.getServiceId(),
                        Long.parseLong(dto.getActivityId()), dto.getRoomId(), new DateTime(startTime).plusSeconds(1).toDate(), new DateTime(endTime).minusSeconds(1).toDate(), appId);
                Long instructorId = null;
                if (!instructorList.isEmpty()){
                    instructorId = Long.parseLong(instructorList.get(0).getInstructorId());
                }
                if (dto.getInstructorId() != null) {
                    instructorId = dto.getInstructorId();
                    for (int i = 0, size = instructorList.size(); i < size; i++) {
                        InstructorInfoDTO instructorInfoDTO = instructorList.get(i);
                        if (instructorInfoDTO.getInstructorId().equalsIgnoreCase(String.valueOf(instructorId))){
                            Collections.swap(instructorList, 0, i);
                            break;
                        }
                    }
                }
                startTimeDTOs = appointmentService.generateStartTimeListByProfileAndDate(profileId, instructorId, startTime);
                map.put(INSTRUCTOR_DTOS_KEY, instructorList);
            }
            List<RoomDTO> roomList = roomService.loadRoomListByProfileIdAndActivityIdAndDateTime(profileId, dto.getServiceId(),
                    ((null == dto.getActivityId() || 0 == dto.getActivityId().length())) ? null : Long.parseLong(dto.getActivityId()),
                    (null == dto.getInstructorId() ? null : dto.getInstructorId()), new DateTime(startTime).plusSeconds(1).toDate(), new DateTime(endTime).minusSeconds(1).toDate(), appId);
            map.put(ROOM_DTOS_KEY, roomList);
        }

        if (startTimeDTOs == null){
            //Loading the common time range between profile and instructor
            startTimeDTOs = appointmentService.generateStartTimeListByProfileAndDate(profileId, dto.getInstructorId(), startTime);
        }
        //For GCSS-729
        if(appId!=null){
        	startTimeDTOs = appointmentService.generateStartTimeListByProfileAndDate(profileId, null, startTime);
        }

        if (durationDTOs == null){
            durationDTOs = Collections.emptyList();
        }

		map.put(APPOINTMENT_ID_KEY, appId);
		map.put(DURATION_DTOS_KEY, durationDTOs);
		map.put(START_TIME_DTOS_KEY, startTimeDTOs);
		map.put("serviceId", serviceId);
		return map;
	}
	
	@RequestMapping(LOAD_INSTRUCTOR_LIST_BY_APPOINTMENTMODEL)
	@ResponseBody
	public List<InstructorInfoDTO> loadInstructorListByProfileAndServiceAndActivityAndTime(Long profileId, Long serviceId, Long activityId, Long roomId, Date startTime, Date endTime, Long appId) {
        startTime = new DateTime(startTime).plusSeconds(1).toDate();
        endTime = new DateTime(endTime).minusSeconds(1).toDate();
		return instructorService.loadInstructorListByProfileIdAndActivityIdAndDateTime(profileId, serviceId, activityId, roomId, startTime, endTime, appId);
	}

	@RequestMapping(LOAD_ROOM_LIST_BY_APPOINTMENTMODEL)
	@ResponseBody
	public List<RoomDTO> loadRoomListByProfileAndServiceAndActivityAndTime(Long profileId, Long serviceId, Long activityId, Long instructorId, Date startTime, Date endTime, Long appId) {
        startTime = new DateTime(startTime).plusSeconds(1).toDate();
        endTime = new DateTime(endTime).minusSeconds(1).toDate();
		return roomService.loadRoomListByProfileIdAndActivityIdAndDateTime(profileId, serviceId, activityId, instructorId, startTime, endTime, appId);
	}
	
	//Method added for GSSP-188 - Fetches the time for setting timeoff based on instructor availability	
	@ResponseBody
	@RequestMapping(value = "/timeoff/getStartTimeByInstructors.htm")
	public Map<String, Object> getStartTimeByInstructor(Long instructorId, String startDate) {
		
		Map<String, Object> map = new HashMap<String, Object>();
		
		List<StartTimeDTO> startTimeDTO = new ArrayList<StartTimeDTO>();
		
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		
		Date currentDate = new Date();
		
		try {
				 currentDate = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).parse(startDate);			 
				 DateTime today = new DateTime(currentDate);
			     int dayOfWeek = today.getDayOfWeek();
			     startTimeDTO = appointmentService.getStartTimeByInstructor(profileId, instructorId, currentDate);
			     
			     if(null != startTimeDTO  && startTimeDTO.size() > 0)
			     {
			    	 map.put(START_TIME_DTOS_KEY, startTimeDTO);
			     }
			     else
			     {
			    		 map.put(MESSAGE_KEY, INSTRUC_WEEKEND_CHOSE[dayOfWeek-1] +" time off cannot be created, as instructor is already not available.");
			    
			     }
 
			     
		     
		} catch (ParseException e) {
			LOG.error("Caught an exception from CalendarController.getStartTimeByInstructor: {}", e);
		}
		
		
		
		
		return map;
	
	}
	//GSSP 241
		@ResponseBody
		@RequestMapping(value = "/conflicts/getConflictingAppointments.htm")
		public ConflictingAppointmentListDTO checkRoomByConflictingAppointmentRecurringTime( long instructorId, long roomId, String startDate, String endDate, String startTime, String duration, boolean isRecurring){
	        			
			ConflictingAppointmentListDTO conflictingAppointmentListDTO = new ConflictingAppointmentListDTO();
			try
			{						
				Integer newDuration = Integer.parseInt(duration)-1;
				long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
				//check the end date if input value is null
				if(endDate==null || "".equals(endDate.trim())){
					
					 if(isRecurring)
					 {
						 endDate = CalendarUtil.getDefaultEndDate(startDate);
					 } 
					 else
					 {
						 endDate = startDate ;
					 }	 
				}
					
				String endTime = CalendarUtil.getEndTime(startTime, newDuration.toString());		
				
				startTime =  CalendarUtil.getEndTime(startTime,"1");	
								
				conflictingAppointmentListDTO = roomService.checkRoomByConflictingAppointmentRecurringTime(roomId, startDate, endDate, startTime, endTime, profileId);
				
				if(null ==  conflictingAppointmentListDTO )
				{
					conflictingAppointmentListDTO = roomService.checkInstructorByConflictingAppointmentRecurringTime(profileId, startDate, endDate, startTime, endTime, instructorId);
					
					if(null != conflictingAppointmentListDTO )
					{
						conflictingAppointmentListDTO.setRoomConflict(false);
						conflictingAppointmentListDTO.setStatus(true);
					}
				}
				else
				{
					conflictingAppointmentListDTO.setStatus(true);
				}
			
			}
			catch(Exception e)
			{
				LOG.error("Caught an exception from CalendarController.checkRoomByConflictingAppointmentRecurringTime: {}", e);
			}
			if(null != conflictingAppointmentListDTO)
				return conflictingAppointmentListDTO;
			else
				return new ConflictingAppointmentListDTO();
		}
			
		//------------------------------------GSSP-334--------------------------------------
		//Method added for GSSP-334 - Get Profile time for up coming two days in Front screen on location	
		@ResponseBody
		@RequestMapping(value = "/profileTimeoff/getStartTimeByProfile.htm")
		public Map<String, Object> getStartTimeByProfileId(Long profileId, String startDate) {
			
			Map<String, Object> map = new HashMap<String, Object>();
			profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
			List<StartTimeDTO> startTimeDTO = new ArrayList<StartTimeDTO>();
			long profileId_timeoff = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
			Date currentDate = new Date();
			
			try {
					 currentDate = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).parse(startDate);			 
					 DateTime today = new DateTime(currentDate);
				     int dayOfWeek = today.getDayOfWeek();
				     startTimeDTO = profileTimeOffService.getStartTimeByProfileId(profileId_timeoff, currentDate);
				     
				     if(null != startTimeDTO  && startTimeDTO.size() > 0){
				    	 map.put(START_TIME_DTOS_KEY, startTimeDTO);
				     }
				     else
				     {		 
				    		 map.put(MESSAGE_KEY, "Profile time off cannot be created, as store is already closed.");
				    
				     }
				     
			     
			} catch (ParseException e) {
				LOG.error("Caught an exception from CalendarController.getStartTimeByInstructor: {}", e);
			}

			return map;
		
		}
		//----334 pop-up screen cancel window and button
		@ResponseBody
		@RequestMapping(value = "profileTimeoff/loadCancelProfileTimeoffs.htm")
		public Map<String, Object> getCancelStartTimeByProfileId(Long profileId) {
			profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
			Map<String, Object> map = new HashMap<String, Object>();
			Date startDateInput = new Date();
			Set<StudioHourDTO> profileTimeDetails = profileTimeOffService.getUpcomingTimeOffByProfileId(profileId,startDateInput);
			map.put(STATUS_KEY, true);
			map.put(PROFILE_TIMEOFF_KEY, profileTimeDetails);
			return map;
		}
	
		private static Date getDateFromString(String dateStr){
			//SimpleDateFormat formatter = new SimpleDateFormat("mm/dd/yyyy");
			SimpleDateFormat formatter = new SimpleDateFormat("MM/dd/yyyy");
	        //String dateInString = "7-Jun-2013";
	        Date date = new Date();

	        try {

	              date = formatter.parse(dateStr);
 
	        } catch (ParseException e) {
	        	LOG.error("Caught {} when check getDateFromString Appointment", e);
	        }
	        return date;
		}
		
		
		public boolean verifyAppointmentServiceModeStatus(long instructorId,int day,Long mode,String startTimeStr,String endTimeStr){ 
			
			Instructor instructor1 = instructorService.getInstructor(instructorId);
			boolean flag=false;
			Availability availability = instructor1.getAvailability();
			String dayStr = day+"";
			//int getDay = day-
			OnLineAvailableDTO onl = getDayAvailabilityStr(dayStr,availability);
			String dayStrUtil = onl.getWeekDay();
			List<OnLineAvailableDTO>  onlineAvlList = null;
			List<InstoreAvailableDTO>  instoreAvlList = null;
			List<OnLineInstoreAvailableDTO>  alllist = new ArrayList<OnLineInstoreAvailableDTO>();
			if(mode == 1){
				 onlineAvlList = onlineAvailabilityService.getOnlineAvlFormatByInstructorId(instructorId);
			
			for(OnLineAvailableDTO onls: onlineAvlList){
				if(dayStrUtil.equals(onls.getWeekDay())){
				OnLineInstoreAvailableDTO allDto= new OnLineInstoreAvailableDTO();
				 allDto.setFromTime(onls.getFromTime());
				 allDto.setToTime(onls.getToTime());
				 allDto.setServiceMode("Online");
				 alllist.add(allDto);
				}
			}
			}
			if(mode == 20){
			instoreAvlList = instoreAvailabilityService.getInstoreAvlFormatByInstructorId(instructorId);
			for(InstoreAvailableDTO onls: instoreAvlList){
				if(dayStrUtil.equals(onls.getWeekDay())){
				OnLineInstoreAvailableDTO allDto= new OnLineInstoreAvailableDTO();
				 allDto.setFromTime(onls.getFromTime());
				 allDto.setToTime(onls.getToTime());
				 allDto.setServiceMode("Instore");
				 alllist.add(allDto);
				}
			}
			}
			Collections.sort(alllist);
			alllist.stream().map(s -> s.getFromTime()).forEach(System.out::print);
 
			
			//List<InstoreAvailableDTO>  instoreAvlList = instoreAvailabilityService.getInstoreAvlFormatByInstructorId(dto.getInstructorId());
			
			try {
				 
				int cnt =1;
				String fromTime ="";

				//---++++++++++++++++++++++++++++++++++++++++++
			 
				int listVal =alllist.size();
				if(listVal>0){
				for(OnLineInstoreAvailableDTO onls: alllist){
				 
					flag= checkSlotUnderInstructorAvlty(startTimeStr,endTimeStr,onls.getFromTime(),onls.getToTime());
					
					if(flag)break;
				}
				}
			 

				
			} catch (Exception e1) {
				LOG.error("Caught {} when   checkSlotUnderInstructor Avlty  ", e1);
			}
			
		return flag;
		}
		
		private OnLineAvailableDTO getDayAvailabilityStr(String day,Availability avl){
			OnLineAvailableDTO onl = new OnLineAvailableDTO();
			if("1".equals(day)){
				onl.setFromTime(AvailabilityUtil.format24(avl.getSundayStartTime()));
				onl.setToTime(AvailabilityUtil.format24(avl.getSundayEndTime()));
				onl.setWeekDay("0");
			}
			if("2".equals(day)){
				onl.setFromTime(AvailabilityUtil.format24(avl.getMondayStartTime()));
				onl.setToTime(AvailabilityUtil.format24(avl.getMondayEndTime()));	
				onl.setWeekDay("1");
							}
			if("3".equals(day)){
				onl.setFromTime(AvailabilityUtil.format24(avl.getTuesdayStartTime()));
				onl.setToTime(AvailabilityUtil.format24(avl.getTuesdayEndTime()));	
				onl.setWeekDay("2");
			}
			if("4".equals(day)){
				onl.setFromTime(AvailabilityUtil.format24(avl.getWednesdayStartTime()));
				onl.setToTime(AvailabilityUtil.format24(avl.getWednesdayEndTime()));	
				onl.setWeekDay("3");
			}
			if("5".equals(day)){
				onl.setFromTime(AvailabilityUtil.format24(avl.getThursdayStartTime()));
				onl.setToTime(AvailabilityUtil.format24(avl.getThursdayEndTime()));	
				onl.setWeekDay("4");
			}
			if("6".equals(day)){
				onl.setFromTime(AvailabilityUtil.format24(avl.getFridayStartTime()));
				onl.setToTime(AvailabilityUtil.format24(avl.getFridayEndTime()));	
				onl.setWeekDay("5");
			}
			if("7".equals(day)){
				onl.setFromTime(AvailabilityUtil.format24(avl.getSaturdayStartTime()));
				onl.setToTime(AvailabilityUtil.format24(avl.getSaturdayEndTime()));	
				onl.setWeekDay("6");
			}
			 
			
			return onl;
		}
	
	public static boolean checkSlotUnderInstructorAvlty(String onlinTimeStart,String onlinTimeEnd,String instAvailStart,String instAvailEnd) throws Exception{
			
			boolean flag = false;
			
			try {
				DateFormat sdf = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN);
				Date insAvlStart = sdf.parse(instAvailStart);
				Date insAvlEnd = sdf.parse(instAvailEnd);
				Date onlineStart = sdf.parse(onlinTimeStart);
				Date onlineEnd = sdf.parse(onlinTimeEnd);
				/*System.out.println("instAvailStart      ->    "+insAvlStart);
				System.out.println("instAvailEnd      ->    "+insAvlEnd);
				System.out.println("onlinTimeStart      ->    "+onlineStart);
				System.out.println("onlinTimeEnd      ->    "+onlineEnd);
				System.out.println(" "); */
				if(onlineStart.before(onlineEnd) && insAvlStart.before(insAvlEnd)){
				 
					if(onlineStart.after(insAvlStart) && onlineEnd.before(insAvlEnd)){
						//System.out.println(" 3 ");
						flag = true;
					}
					

					if(onlineStart.equals(insAvlStart) && onlineEnd.before(insAvlEnd)){
						//System.out.println(" 4 ");
						flag = true;
					}
					if(onlineEnd.equals(insAvlEnd) && onlineStart.after(insAvlStart)){
						//System.out.println(" 5 ");
						flag = true;
					}
					if( onlineStart.before(insAvlStart) &&  onlineEnd.before(insAvlEnd) &&  onlineEnd.after(insAvlStart)){
						//System.out.println(" 5! ");
						flag = true;
					}
					if( onlineStart.before(insAvlEnd) &&  onlineEnd.after(insAvlEnd) &&  onlineStart.after(insAvlStart)){
						//System.out.println(" 5!! ");
						flag = true;
					}
					if(onlineStart.equals(insAvlStart) && onlineEnd.equals(insAvlEnd)){
						// System.out.println(" 6 ");
						flag = true;
					}
				 
				}

			} catch (ParseException e) {
				throw  e;	 
			}
	  
	    return flag;
	} 
}
