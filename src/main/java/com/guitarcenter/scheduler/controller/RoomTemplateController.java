/**
 * @Title: RoomTemplateController.java
 * @Package com.guitarcenter.scheduler.controller
 * @Description: TODO
 * Copyright: Copyright (c) 2013 
 * Company:
 * 
 * <AUTHOR>
 * @date Sep 11, 2013 10:35:13 AM
 * @version V1.0
 */
package com.guitarcenter.scheduler.controller;

import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ROOMTEMPLATE_NAME_EXISTS;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ROOMTEMPLATE_NAME_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ROOMTEMPLATE_SIZE_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ROOMTEMPLATE_TYPE_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_ROOMTYPE_CAN_SPLIT;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.SessionAttributes;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.servlet.ModelAndView;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.dto.RoomTemplateDTO;
import com.guitarcenter.scheduler.dto.RoomTemplateDetailDTO;
import com.guitarcenter.scheduler.dto.ServiceDTO;
import com.guitarcenter.scheduler.dto.UpdateMessageDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.RoomSize;
import com.guitarcenter.scheduler.model.RoomTemplate;
import com.guitarcenter.scheduler.model.RoomType;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.SplitRoom;
import com.guitarcenter.scheduler.service.ActivityService;
import com.guitarcenter.scheduler.service.RoomSizeService;
import com.guitarcenter.scheduler.service.RoomTemplateService;
import com.guitarcenter.scheduler.service.RoomTypeService;
import com.guitarcenter.scheduler.service.ServiceService;
import com.guitarcenter.scheduler.service.ValidationService;
import com.guitarcenter.scheduler.service.impl.ServiceAndActivityValidate;

/**
 * @ClassName: RoomTemplateController
 * @Description: TODO
 * <AUTHOR>
 * @date Sep 11, 2013 10:35:13 AM
 *
 */
@SessionAttributes({AppConstants.LOCATION_STRING,AppConstants.SITE_ID_SESSION_KEY,AppConstants.PERSON_SESSION_KEY})
@Controller
public class RoomTemplateController implements AppConstants {

	private static final Logger LOG = LoggerFactory.getLogger(RoomController.class);
	
	private static final String MESSAGE_KEY            = "message";
	private static final String STATUS_KEY			   = "status";
	private static final String SELECT_ON			   = "on";
	
	@Resource(name="roomTemplateService")
	private RoomTemplateService roomTemplateService;
	
	@Resource(name="serviceService")
	private ServiceService serviceService;
	
	@Resource(name="roomSizeService")
	private RoomSizeService roomSizeService;
	
	@Resource(name="roomTypeService")
	private RoomTypeService roomTypeService;
	
	@Resource
	private ValidationService validationService;
	
	@Resource(name="activityService")
	private ActivityService activityService;
	
	@Resource
	private ServiceAndActivityValidate serviceAndActivityValidate;
	
	@RequestMapping("/roomTemplateManagePage.htm")
	public ModelAndView forwardRoomTemplateManagePage() {
		if(LOG.isDebugEnabled()) {
			LOG.debug("RoomTemplateController.forwardRoomTemplateManagePage: start");
		}
		Map<String, Object> map = new HashMap<String, Object>();
		long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		List<RoomType> tList = roomTypeService.getRoomTypeList(siteId);
		map.put("roomTypeList", tList);
		if(LOG.isDebugEnabled()) {
			LOG.debug("RoomTemplateController.forwardRoomTemplateManagePage: end");
		}
		return new ModelAndView("/room_template_manage", map);
	}

	@RequestMapping("/roomTemplate/loadRoomTemplate.htm")
	@ResponseBody
	public Object loadRoomTemplate(){
		if(LOG.isDebugEnabled()){
			LOG.debug("RoomTemplateController.loadRoomTemplate: start");
		}
		long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		List<RoomTemplateDTO> list = roomTemplateService.getRoomTemplateList(siteId);
		Collections.sort(list);
		if(LOG.isDebugEnabled()) {
			LOG.debug("RoomTemplateController.loadRoomTemplate: end");
		}
		return list;
	}
	
	@RequestMapping("/roomTemplate/loadRoomTemplateServices.htm")
	@ResponseBody
	public Map<String, Object> loadRoomTemplateServices(String serviceString,@ModelAttribute(SITE_ID_SESSION_KEY)Long siteId){
		if(LOG.isDebugEnabled()){
			LOG.debug("RoomTemplateController.loadRoomTemplateServices: start");
		}
		Map<String, Object> map = new HashMap<String, Object>();
		List<ServiceDTO> sList = new ArrayList<ServiceDTO>();
		List<Service> serviceList = serviceService.getServiceListBySiteId(siteId);
		List<Long> serviceId = SystemUtil.getIdList(serviceString);
		for(Service s: serviceList){
			if(!serviceId.contains(s.getServiceId()) && Enabled.Y.equals(s.getEnabled())){
				ServiceDTO dto =new ServiceDTO(s.getServiceId(), s.getServiceName());
				sList.add(dto);
			}
		}
		Collections.sort(sList);
		map.put("serviceList", sList);

		if(LOG.isDebugEnabled()) {
			LOG.debug("RoomTemplateController.loadRoomTemplateServices: end");
		}
		return map;
	}
	
	@RequestMapping("/roomTemplate/preDelService.htm")
	@ResponseBody
	public Map<String, Object> preDelService(String activityString, String serviceString){
		if(LOG.isDebugEnabled()){
			LOG.debug("RoomController.preDelService: start");
		}
		List<Activity> activityList = getActivitiesByServiceString(serviceString);
		List<ActivityDTO> aList = getIncludeActivityDTOList(activityList, activityString);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("activityList", aList);
		if(LOG.isDebugEnabled()) {
			LOG.debug("RoomController.preDelService: end");
		}
		return map;
	}
	
	@RequestMapping("/roomTemplate/loadRoomTemplateActivities.htm")
	@ResponseBody
	public Map<String, Object> loadRoomTemplateActivities(String activityString, String serviceString){
		if(LOG.isDebugEnabled()){
			LOG.debug("RoomTemplateController.loadRoomTemplateActivities: start");
		}
		List<Activity> activityList = getActivitiesByServiceString(serviceString);
		List<ActivityDTO> aList = getActivityDTOList(activityList, activityString);
		List<ActivityDTO> activitiesList = getIncludeActivityDTOList(activityList, activityString);
		
		Map<String, Object> map = new HashMap<String, Object>();
		Collections.sort(aList);
		Collections.sort(activitiesList);
		map.put("activityList", aList);
		map.put("selectedActivityList", activitiesList);
		if(LOG.isDebugEnabled()) {
			LOG.debug("RoomTemplateController.loadRoomTemplateActivities: end");
		}
		return map;
	}
	
	private List<Activity> getActivitiesByServiceString(String serviceString){
		List<Activity> activities = new ArrayList<Activity>();
		List<Long> serviceIds = SystemUtil.getIdList(serviceString);
		for(Long serviceId : serviceIds){
			List<Activity> activityList = activityService.loadActivitiesByService(serviceId);
			activities.addAll(activityList);
		}
		return activities;
	}
	
	private List<ActivityDTO> getActivityDTOList(List<Activity> activityList, String activityString){
		List<ActivityDTO> asList = new ArrayList<ActivityDTO>();
		List<Long> activityId = SystemUtil.getIdList(activityString);
		for(Activity a: activityList){
			if(!activityId.contains(a.getActivityId()) && Enabled.Y.equals(a.getEnabled())){
				ActivityDTO dto = new ActivityDTO(a.getActivityId(), a.getActivityName());
				asList.add(dto);
			}
		}
		return asList;
	}
	
	private List<ActivityDTO> getIncludeActivityDTOList(Collection<Activity> activityList, String activityString){
		List<Long> activityId = SystemUtil.getIdList(activityString);
		List<ActivityDTO> asList = new ArrayList<ActivityDTO>();
		for(Activity a: activityList){
			if(activityId.contains(a.getActivityId())){
				ActivityDTO dto = new ActivityDTO(a.getActivityId(), a.getActivityName());
				asList.add(dto);
			}
		}
		return asList;
	}
	
	@RequestMapping("/roomTemplate/createTemplateRoom.htm")
	@ResponseBody
	public Map<String, Object> createRoomTemplate(@RequestBody RoomTemplateDTO dto){
		if(LOG.isDebugEnabled()){
			LOG.debug("RoomTemplateController.createRoomTemplate: start");
		}

		Person person = (Person) RequestContextHolder.currentRequestAttributes().getAttribute(PERSON_STRING, RequestAttributes.SCOPE_SESSION);
		long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			map = this.checkRoomTemplate(dto, siteId);
			if((Boolean)map.get(STATUS_KEY)){
				RoomTemplate roomTemplate = new RoomTemplate();
				roomTemplate.setRoomTemplateName(SystemUtil.createName(dto.getRoomTemplateName()));
				roomTemplate.setEnabled(SELECT_ON.equals(dto.getEnabled())?Enabled.Y:Enabled.N);
				roomTemplate.setIsSplitRoom(SELECT_ON.equals(dto.getIsSplitRoom())?SplitRoom.Y:SplitRoom.N);
				if(null!=dto.getRoomSize()&&!"".equals(dto.getRoomSize())){
					RoomSize roomSize = new RoomSize();
					roomSize.setRoomSizeId(Long.parseLong(dto.getRoomSize()));
					roomTemplate.setRoomSize(roomSize);
				}
				RoomType roomType = new RoomType();
				roomType.setRoomTypeId(Long.parseLong(dto.getRoomType()));
				roomTemplate.setRoomType(roomType);
				Site site = new Site();
				site.setSiteId(siteId);
				roomTemplate.setSite(site);
				roomTemplate.setUpdatedBy(person);
				roomTemplate.setUpdated(new Date());
				List<Long> serviceIds = SystemUtil.getIdList(dto.getServices());
				Set<Service> services = new HashSet<Service>(0);
				for(Long serviceId : serviceIds){
					Service service = new Service();
					service.setServiceId(serviceId);
					services.add(service);
				}
				List<Long> activityIds = SystemUtil.getIdList(dto.getActivities());
				Set<Activity> activities = new HashSet<Activity>(0);
				for(Long activityId : activityIds){
					Activity activity = new Activity();
					activity.setActivityId(activityId);
					activities.add(activity);
				}
				roomTemplate.setServices(services);
				roomTemplate.setActivities(activities);
				
				roomTemplateService.createRoomTemplate(roomTemplate);
				map.put(STATUS_KEY, true);
				map.put(MESSAGE_KEY, "Create Room Template Success!");
			}
		} catch (Exception e) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Create Room Template Failed");
			LOG.error("Caught an exception from RoomTemplateController.createRoomTemplate: {}", e);
		}
		if(LOG.isDebugEnabled()) {
			LOG.debug("RoomTemplateController.createRoomTemplate: end");
		}
		return map;
	}
	
	
	private Map<String, Object> checkRoomTemplate(RoomTemplateDTO dto, long siteId) throws RuntimeException {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put(STATUS_KEY, true);
		if(null ==dto.getRoomTemplateName() || "".equals(dto.getRoomTemplateName().trim())){
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, VALIDATION_ROOMTEMPLATE_NAME_NULL);
			return map;
		}
		if(!roomTemplateService.checkRoomTemplateName(siteId, dto.getRoomTemplateName().trim())){
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, VALIDATION_ROOMTEMPLATE_NAME_EXISTS);
			return map;
		}
		if(null ==dto.getRoomType() || "".equals(dto.getRoomType().trim())){
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, VALIDATION_ROOMTEMPLATE_TYPE_NULL);
			return map;
		}
		RoomType roomType = roomTypeService.getRoomType(Long.parseLong(dto.getRoomType()));
		if(SplitRoom.N.equals(roomType.getCanSplitRoom())){
			dto.setIsSplitRoom(null);
		}
		//GCSS-363 fix the bug, null value input of room size
		List<RoomSize> rsList = roomSizeService.getRoomSizeListByRoomTypeSiteId(siteId, roomType.getRoomTypeId());
		if(rsList.size()!=0 && (null ==dto.getRoomSize() || "".equals(dto.getRoomSize().trim()))){
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, VALIDATION_ROOMTEMPLATE_SIZE_NULL);
			return map;
		}
		if(rsList.size() == 0){
			dto.setRoomSize(null);
		}
		if("Y".equals(dto.getIsSplitRoom()) && SplitRoom.N.equals(roomType.getCanSplitRoom())){
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, VALIDATION_ROOMTYPE_CAN_SPLIT);
			return map;
		}
		return map;
	}
	
	@RequestMapping("/roomTemplate/deleteRoomTemplate.htm")
	@ResponseBody
	public UpdateMessageDTO deleteRoomTemplate(long roomTemplateId){
		UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO(DELETE_SUCCESS, true);
		Boolean result = serviceAndActivityValidate.roomtemplateCanDelete(roomTemplateId);
		if(!result){
			updateMessageDTO.setMessage("RoomTemplate has been used,it cannot be deleted!");
			updateMessageDTO.setStatus(false);
			return updateMessageDTO;
		}
		try{
			roomTemplateService.deleteRoomTemplate(roomTemplateId);
		}catch(Exception e){
			LOG.error("Caught an exception from RoomTemplateController.deleteRoomTemplate: {}", e.getMessage());
			updateMessageDTO.setMessage(e.getMessage());
			updateMessageDTO.setStatus(false);
			return updateMessageDTO;
		}
		return updateMessageDTO;
	}
	
	
	@RequestMapping("roomTemplate/loadRoomTempDetail.htm")
	@ResponseBody
	public Object loadRoomTempDetail(Long roomTemplateId,@ModelAttribute(SITE_ID_SESSION_KEY)Long siteId){
		RoomTemplate template = roomTemplateService.getRoomTemplate(roomTemplateId);
		
		if(template == null){
			return new UpdateMessageDTO("RoomTemplate does not exist or has been deleted!",false);
		}
		
		RoomTemplateDetailDTO roomTemplateDetailDTO = new RoomTemplateDetailDTO(roomTemplateId);
		roomTemplateDetailDTO.setRoomTemplateId(roomTemplateId);
		roomTemplateDetailDTO.setVersion(template.getVersion());
		
		if(template.getRoomSize() != null){
			roomTemplateDetailDTO.setRoomSizeId(template.getRoomSize().getRoomSizeId());
		}
		if(template.getRoomType() != null){
			roomTemplateDetailDTO.setRoomTypeId(template.getRoomType().getRoomTypeId());
		}
		roomTemplateDetailDTO.setRoomTemplateName(template.getRoomTemplateName());
		
		roomTemplateDetailDTO.setEnabled(Enabled.Y.equals(template.getEnabled())?"Y":"N");
		if(template.getIsSplitRoom() != null){
			roomTemplateDetailDTO.setIsSplitRoom(SplitRoom.Y.equals(template.getIsSplitRoom())?"Y":"N");
		}
		
		List<RoomType> roomTypes = roomTypeService.getRoomTypeList(siteId);
		roomTemplateDetailDTO.setRoomTypeList(roomTypes);
		
		List<RoomSize> roomSizes = roomSizeService.getRoomSizeListByRoomTypeSiteId(siteId, template.getRoomType().getRoomTypeId());
		roomTemplateDetailDTO.setRoomSizeList(roomSizes);

		Set<Service> serviceSet = template.getServices();
		List<Service> services = new ArrayList<Service>();
		services.addAll(serviceSet);
		
		for (Service service : serviceSet) {
			if(Enabled.N.equals(service.getEnabled())){
				services.remove(service);
			}
		}
		List<Service> notSelectedServices = serviceService.findServiceBySite(siteId);
		List<Service> notSelectedS = new ArrayList<Service>(notSelectedServices);
		
		for (Service service : notSelectedS) {
			if(Enabled.N.equals(service.getEnabled())){
				notSelectedServices.remove(service);
			}
		}
		for (Service service : services) {
			if(notSelectedServices.contains(service)){
				notSelectedServices.remove(service);
			}
		}
		roomTemplateDetailDTO.setSelectedServices(services);
		roomTemplateDetailDTO.setUnSelectedServices(notSelectedServices);
		
		
		
		
		Set<Activity> activitySet = template.getActivities();
		List<Activity> activities = new ArrayList<Activity>();
		activities.addAll(activitySet);
		for (Activity activity : activitySet) {
			if(Enabled.N.equals(activity.getEnabled())){
				activities.remove(activity);
			}
		}
		
		List<Activity> notSelectedActivities = new ArrayList<Activity>();
		for (Service service : services) {
			 notSelectedActivities.addAll(activityService.loadActivitiesByService(service.getServiceId()));
		}
		
		List<Activity> notSelected = new ArrayList<Activity>(notSelectedActivities);
		
		for (Activity activity : notSelected) {
			if(Enabled.N.equals(activity.getEnabled())){
				notSelectedActivities.remove(activity);
			}
		}
		
		for (Activity activity : activities) {
			if(notSelectedActivities.contains(activity)){
				notSelectedActivities.remove(activity);
			}
		}
		roomTemplateDetailDTO.setSelectedActivities(activities);
		roomTemplateDetailDTO.setUnSelectedActivities(notSelectedActivities);
		
		return roomTemplateDetailDTO;
	}
	
	@RequestMapping("/roomTemplate/getUnselectedActivities.htm") 
	@ResponseBody
	public List<Activity> getUnselectedActivities(Long id){
		 List<Activity> activities = activityService.loadActivitiesByService(id);
		 List<Activity> unselectedActivities = new ArrayList<Activity>();
		 for (Activity activity : activities) {
			if(Enabled.Y.equals(activity.getEnabled())){
				Service service = new Service();
				service.setServiceId(id);
				activity.setService(service);
				unselectedActivities.add(activity);
			}
		}
		return unselectedActivities;
	}
	
	
	@RequestMapping("/roomTemplate/updateRoomTemplate.htm") 
	@ResponseBody
	public UpdateMessageDTO updateRoomTemplate(@RequestBody RoomTemplateDetailDTO roomTemplateDetailDTO,@ModelAttribute(PERSON_SESSION_KEY) Person person,@ModelAttribute(SITE_ID_SESSION_KEY)Long siteId){
		
		RoomTemplate roomTemplate = roomTemplateService.getRoomTemplate(roomTemplateDetailDTO.getRoomTemplateId());
		UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO(UPDATE_SUCCESS, true);
		RoomType updateRoomType = roomTypeService.getRoomType(roomTemplateDetailDTO.getRoomTypeId());
		
		updateMessageDTO = checkRoomTemplateUpdate(siteId,roomTemplateDetailDTO, roomTemplate,
				updateMessageDTO,updateRoomType);
		if(!updateMessageDTO.getStatus()){
			return updateMessageDTO;
		}
		
		Boolean globalChange = roomTemplateDetailDTO.getGlobalChange();
		Enabled enable = roomTemplate.getEnabled();
		RoomType roomType = roomTemplate.getRoomType();
		

		roomTemplate.setRoomTemplateName(SystemUtil.createName(roomTemplateDetailDTO.getRoomTemplateName().trim()));
		
		roomTemplate.setRoomType(updateRoomType);
		Long roomSizeId = roomTemplateDetailDTO.getRoomSizeId();
		if(roomSizeId != null){
			RoomSize roomSize = roomSizeService.getRoomSize(roomSizeId);
			roomTemplate.setRoomSize(roomSize);
		}else{
		    roomTemplate.setRoomSize(null);
		}
		roomTemplate.setIsSplitRoom(StringUtils.isNotBlank(roomTemplateDetailDTO.getIsSplitRoom())?SplitRoom.Y:SplitRoom.N);
		if(SplitRoom.N.equals(updateRoomType.getCanSplitRoom())){
		   roomTemplate.setIsSplitRoom(SplitRoom.N);
		}
		roomTemplate.setEnabled(StringUtils.isNotBlank(roomTemplateDetailDTO.getEnabled())?Enabled.Y:Enabled.N);
		List<Service> services = roomTemplateDetailDTO.getSelectedServices();
		
		List<Service> nowServices = new ArrayList<Service>(roomTemplate.getServices());
		roomTemplate.setServices(new HashSet<Service>(services));
		
		//deleted service and added service only get enabled service
		List<Service> addServices = addedServices(nowServices, services,globalChange);
		List<Service> deletedServices = deletedServices(nowServices, services,globalChange);
		
		List<Activity> activities = roomTemplateDetailDTO.getSelectedActivities();
		
		List<Activity> nowActivities = new ArrayList<Activity>(roomTemplate.getActivities());
		roomTemplate.setActivities(new HashSet<Activity>(activities));

		//deletedActivity and addActivity only get enabled activity
		List<Activity> addActivities = addedActivities(nowActivities, activities,globalChange);
		List<Activity> deletedActivities = deletedActivities(nowActivities, activities,globalChange);
		
		//check room can delete activity
		//activity checked then service has been checked
		updateMessageDTO = checkRoomDelActivity(deletedActivities,roomTemplateDetailDTO.getRoomTemplateId(),globalChange);
        if(!updateMessageDTO.getStatus()){
            return updateMessageDTO;
        }
		
		try {
			roomTemplateService.updateRoomTemplate(roomTemplate, person.getPersonId(), enable, globalChange,roomType,addServices,deletedServices,addActivities,deletedActivities);
		} catch (Exception e) {
			updateMessageDTO.setMessage(e.getMessage());
			updateMessageDTO.setStatus(false);
			return updateMessageDTO; 
		}
		
		//get new activities String  and new services String
		Set<Activity> hasActivities = roomTemplate.getActivities();
		StringBuilder activitySb = new StringBuilder();
		for (Activity activity : hasActivities) {
			activitySb.append(activity.getActivityName()).append(",");
		}
		roomTemplateDetailDTO.setActivities(activitySb.length()>0?activitySb.substring(0,activitySb.length()-1):"");
		Set<Service> hasServices = roomTemplate.getServices();
		StringBuilder serviceSb = new StringBuilder();
		for (Service service : hasServices) {
			serviceSb.append(service.getServiceName()).append(",");
		}
		roomTemplateDetailDTO.setServices(serviceSb.length()>0?serviceSb.substring(0, serviceSb.length()-1):"");
		
		//get new roomtype String  and new roomsize String
		if(roomSizeId != null){
			roomTemplateDetailDTO.setRoomSize(roomTemplate.getRoomSize().getRoomSizeName());
		}
		roomTemplateDetailDTO.setRoomType(roomTemplate.getRoomType().getRoomType());
		roomTemplateDetailDTO.setEnabled(StringUtils.isBlank(roomTemplateDetailDTO.getEnabled())?"N":roomTemplateDetailDTO.getEnabled());
		updateMessageDTO.setObject(roomTemplateDetailDTO);
		return updateMessageDTO;
	}

	/**
	 * if global change , update room template's service and activity
	 * should update room's activity and service,check if room can remove
	 * activity only.
	 * 
	 * @param deletedActivities
	 * @param roomTemplateId
	 * @return
	 */
	private UpdateMessageDTO checkRoomDelActivity(final List<Activity> deletedActivities,final Long roomTemplateId,final Boolean globalChange) {
	   final UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO(null, true);
	   
	   if(globalChange){
	       final List<Long> activityIds = new ArrayList<Long>();
	       for (final Activity activity : deletedActivities) {
	           if(activity != null){
	               activityIds.add(activity.getActivityId());
	           }
	       }
	       final Boolean result =  validationService.checkRoomActivities(roomTemplateId, activityIds);
	       if(!result){
	           updateMessageDTO.setMessage("activity has been assigned to appointment,it cannot be deleted from room!");
	           updateMessageDTO.setStatus(false);
	       }
	   }
	   
        return updateMessageDTO;
    }

	/**
	 * check if room template can update,except service and activity 
	 * can remove from room template when global change.
	 * @param roomTemplateDetailDTO
	 * @param roomTemplate
	 * @param updateMessageDTO
	 * @return
	 */
    private UpdateMessageDTO checkRoomTemplateUpdate(
            Long siteId,
			RoomTemplateDetailDTO roomTemplateDetailDTO,
			RoomTemplate roomTemplate, UpdateMessageDTO updateMessageDTO,RoomType updateRoomType) {
        
        List<RoomSize> roomSizes = roomSizeService.getRoomSizeListByRoomTypeSiteId(siteId, updateRoomType.getRoomTypeId());
       
        //if roomType not null,roomSize cannot be null
        if(roomSizes != null && !roomSizes.isEmpty() && roomTemplateDetailDTO.getRoomSizeId() == null){
            updateMessageDTO.setMessage(VALIDATION_ROOMTEMPLATE_SIZE_NULL);
            updateMessageDTO.setStatus(false);
            return updateMessageDTO;   
        }
		
        if(roomTemplate == null){
			updateMessageDTO.setMessage("RoomTemplate does not exist or it has been deleted!");
			updateMessageDTO.setStatus(false);
			return updateMessageDTO; 
			
		}
		
		if(roomTemplate.getVersion() != roomTemplateDetailDTO.getVersion()){
			updateMessageDTO.setMessage("RoomTemplate has been updated!");
			updateMessageDTO.setStatus(false);
			return updateMessageDTO;
		}
		
		if(roomTemplateDetailDTO.getGlobalChange() && StringUtils.isBlank(roomTemplateDetailDTO.getEnabled())){
			Boolean result = validationService.checkRoomupdateByTemplateId(roomTemplate.getRoomTemplateId());
			if(!result){
				updateMessageDTO.setMessage("Room has been assigned to appointment,roomTemplate cannot be disabled!");
				updateMessageDTO.setStatus(false);
				return updateMessageDTO; 
			}
		}
		
		if(StringUtils.isBlank(roomTemplateDetailDTO.getRoomTemplateName())){
			updateMessageDTO.setMessage(VALIDATION_ROOMTEMPLATE_NAME_NULL);
			updateMessageDTO.setStatus(false);
			return updateMessageDTO; 
		}
		if(!roomTemplate.getRoomTemplateName().trim().equals(SystemUtil.createName(roomTemplateDetailDTO.getRoomTemplateName()))){
			//Bug GCSS-652
			Boolean result = roomTemplateService.checkRoomTemplateName(siteId, SystemUtil.createName(roomTemplateDetailDTO.getRoomTemplateName()));
			if(!result){
				updateMessageDTO.setMessage(VALIDATION_ROOMTEMPLATE_NAME_EXISTS);
				updateMessageDTO.setStatus(false);
				return updateMessageDTO; 
			}
		}
		return updateMessageDTO;
	}
	
	/**
	 * get added service.
	 * @param nowServices
	 * @param updateServices
	 * @param globalChange
	 * @return
	 */
	private List<Service> addedServices(final List<Service> nowServices,final List<Service> updateServices,final Boolean globalChange){
		final List<Service> list = new ArrayList<Service>();
		if(globalChange){
		    for (final Service service : updateServices) {
		        if(!nowServices.contains(service) && Enabled.Y.equals(service.getEnabled())){
		            list.add(service);
		        }
		    }
		}
		return list;
	}
	
	//deleted service
	private List<Service> deletedServices(List<Service> nowServices,List<Service> updateServices,Boolean globalChange){
		final List<Service> list = new ArrayList<Service>();
		if(globalChange){
		    for (final Service service : nowServices) {
		        if(!updateServices.contains(service)  && Enabled.Y.equals(service.getEnabled())){
		            list.add(service);
		        }
		    }
		}
		return list;
	}
	
	//added activities
	private List<Activity> addedActivities(List<Activity> nowActivities,List<Activity> updateActivities,Boolean globalChange){
		List<Activity> activities = new ArrayList<Activity>();
		if(globalChange){
    		for (Activity activity : updateActivities) {
    			if(!nowActivities.contains(activity) && Enabled.Y.equals(activity.getEnabled())){
    				 activities.add(activity);
    			}
    		}
		}
		return activities;
	}
	
	//added activities
	private List<Activity> deletedActivities(List<Activity> nowActivities,List<Activity> updateActivities,Boolean globalChange){
		List<Activity> activities = new ArrayList<Activity>();
		if(globalChange){
    		for (Activity activity : nowActivities) {
    			if(!updateActivities.contains(activity)  && Enabled.Y.equals(activity.getEnabled())){
    				activities.add(activity);
    			}
    		}
		}
		return activities;
	}
	
	@RequestMapping("/roomTemplate/selectRoomType.htm")
	@ResponseBody
	public Map<String, Object> selectRoomType(long roomTypeId){
		if(LOG.isDebugEnabled()){
			LOG.debug("RoomTemplateController.selectRoomType: start");
		}
		long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		List<RoomSize> rsList = roomSizeService.getRoomSizeListByRoomTypeSiteId(siteId, roomTypeId);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("roomSizeList", rsList);

		if(LOG.isDebugEnabled()) {
			LOG.debug("RoomTemplateController.selectRoomType: end");
		}
		return map;
	}
}
