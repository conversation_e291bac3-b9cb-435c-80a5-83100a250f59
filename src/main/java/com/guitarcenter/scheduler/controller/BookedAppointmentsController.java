/**
 * @Title: RoomController.java
 * @Package com.guitarcenter.scheduler.controller
 * @Description: TODO
 * Copyright: Copyright (c) 2013 
 * Company:
 * 
 * <AUTHOR>
 * @date Sep 10, 2013 2:37:09 PM
 * @version V1.0
 */

package com.guitarcenter.scheduler.controller;

import com.guitarcenter.scheduler.common.util.JsonUtil;
import com.guitarcenter.scheduler.dao.criterion.dto.AppointmentBookDTO;
import com.guitarcenter.scheduler.dto.ListBookedAppointmentsDTO;
import com.guitarcenter.scheduler.dto.BookedAppointmentQueryDTO;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.service.AppointmentBookService;
import com.guitarcenter.scheduler.service.LocationProfileService;
import com.guitarcenter.scheduler.service.ValidationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.util.List;

import static com.guitarcenter.scheduler.common.util.AppConstants.LOCATION_STRING;

/**
 * @ClassName: RoomController
 * @Description: TODO
 * <AUTHOR>
 * @date Sep 10, 2013 2:37:09 PM
 *
 */
@Controller
@SessionAttributes({"siteId"})
public class BookedAppointmentsController {
	private static final Logger LOG = LoggerFactory.getLogger(BookedAppointmentsController.class);
	
	private static final String MESSAGE_KEY            = "message";
	private static final String STATUS_KEY			   = "status";
	private static final String RETURN_DTO			   = "dto";
	private static final long DEFAULT_SPLIT_ROOM_TYPE =1;
	
	@Resource(name="appointmentBookService")
	private AppointmentBookService appointmentBookService;
	
	@Autowired
	private LocationProfileService locationProfileService;
	
	@Autowired
	private ValidationService validationService;
	
	
	@RequestMapping("/bookedAppointments/loadBookedAppointments.htm")
	@ResponseBody
	public ListBookedAppointmentsDTO loadBookedAppointmentsDTO(@RequestParam("params") String params,HttpSession session){
		BookedAppointmentQueryDTO queryModel = JsonUtil.stringToObject(params, BookedAppointmentQueryDTO.class);
		Location location = (Location) session.getAttribute(LOCATION_STRING);
		queryModel.setLocationId(location.getLocationId());
		if(LOG.isDebugEnabled()){
			LOG.debug("BookedAppointmentsController.loadBookedAppointmentsDTO: start");
		}

		List<AppointmentBookDTO> bookedAppointmentsDTOlist = appointmentBookService.getBookedAppointmentsList(queryModel);
		
		if(LOG.isDebugEnabled()) {
			LOG.debug("BookedAppointmentsController.loadBookedAppointmentsDTO: end");
		}

		ListBookedAppointmentsDTO listBookedAppointmentsDTO = null;
		if(null!= bookedAppointmentsDTOlist) {
			listBookedAppointmentsDTO = new ListBookedAppointmentsDTO();
			listBookedAppointmentsDTO.setAppointmentBookDTO(bookedAppointmentsDTOlist);
		}

		return listBookedAppointmentsDTO;
	}
	
	@RequestMapping(value = "/bookedAppointments/reSubmitBookedAppointments.htm",method = RequestMethod.POST)
	@ResponseBody
	public AppointmentBookDTO resubmitBookedAppointments(@RequestParam("params") String params){
		AppointmentBookDTO dto = JsonUtil.stringToObject(params,AppointmentBookDTO.class);
		return appointmentBookService.reSubmitAppointmentBook(dto);
	}



}
