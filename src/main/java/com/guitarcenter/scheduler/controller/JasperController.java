package com.guitarcenter.scheduler.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.SessionAttributes;
import org.springframework.web.servlet.ModelAndView;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
/* Added for NewInsAptReport _ June 2015 Enhancement */
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dto.CancelledAppointmentReportDTO;
import com.guitarcenter.scheduler.dto.CancelledAppointmentReportPagingDTO;
import com.guitarcenter.scheduler.dto.InstructorReportPagingDTO;
import com.guitarcenter.scheduler.dto.MasterReportPagingDTO;
import com.guitarcenter.scheduler.dto.MasterScheduleReportDTO;
import com.guitarcenter.scheduler.dto.RehearsalBookingReportDTO;
import com.guitarcenter.scheduler.dto.RehearsalBookingReportParentDTO;
import com.guitarcenter.scheduler.dto.RehearsalScheduleReportDTO;
import com.guitarcenter.scheduler.dto.RehearsalScheduleReportParentDTO;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.dto.InstructorAppointmentBusinessHours;
import com.guitarcenter.scheduler.service.ReportService;

import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;


@SessionAttributes({AppConstants.LOCATION_ID_SESSION_KEY, AppConstants.LOCATION_PROFILE_ID_SESSION_KEY, AppConstants.STORE_STRING})
@Controller
public class JasperController implements AppConstants {

    private static final Logger LOG = LoggerFactory.getLogger(JasperController.class);

    private static final String SUBREPORT_DIR = "WEB-INF/views/reports/";

    @Autowired
    private ReportService reportService;

    //requestMapping
    private static final String PRINT_INSTRUCTOR_SCHEDULE_REPORT_MAPPING = "printInstructorScheduleReport.htm";
    //added for Instructor Scheduler report
    private static final String PRINT_INSTRUCTOR_STATUS_SCHEDULE_REPORT_MAPPING = "printInstructorStatusScheduleReport.htm";
    
    private static final String PRINT_MASTER_SCHEDULE_REPORT_MAPPING = "printMasterScheduleReport.htm";
    private static final String PRINT_REHEARSAL_BOOKING_REPORT_MAPPING = "printRehearsalBookingReport.htm";
    private static final String PRINT_REHEARSAL_SCHEDULE_REPORT_MAPPING = "printRehearsalScheduleReport.htm";
    private static final String PRINT_CANCELLED_APPOINTMENTS_MAPPING = "printCacnelledAppointments.htm";
	/* Added for NewInsAptReport _ June 2015 Enhancement */
    private static final String PRINT_INSTRUCTOR_OPEN_APPOINTMENTS_REPORT_MAPPING = "printInstructorOpenAppointmentsReport.htm";
    //Added new active students report  GSSP-185
    private static final String PRINT_ACTIVE_STUDENTS_REPORT_MAPPING = "printActiveStudentsReport.htm";
    //For GSSP-203
    private static final String PRINT_STUDENT_CHECK_IN_REPORT_MAPPING = "printStudentCheckInReport.htm";
    //For GSSP-205
    private static final String PRINT_INACTIVE_STUDENTS_REPORT_MAPPING = "printInActiveStudentsReport.htm";
   
    //For GSSP-170
    private static final String PRINT_CONFLICT_APPOINTMENTS_BY_INSTRUCTOR_REPORT_MAPPING = "printConflictAppointmentsReportByInsrtuctor.htm";
    private static final String PRINT_CONFLICT_APPOINTMENTS_BY_ROOM_REPORT_MAPPING = "printConflictAppointmentsReportByRoom.htm";
    /* For GSSP-161, Added */
    private static final String PRINT_INSTRUCTOR_OUTSIDE_APPOINTMENTS_REPORT_MAPPING = "printInstructorOutsideAppointmentsReport.htm";
    
    //jasperMapping
    private static final String INSTRUCTOR_STATUS_SCHEDULE_REPORT_JASPER_URL = "instructorStatusScheduleReport";
    private static final String INSTRUCTOR_SCHEDULE_REPORT_JASPER_URL = "instructorScheduleReport";
    private static final String REHEARSAL_BOOKING_REPORT_JASPER_URL = "rehearsalBookingReport";
    private static final String REHEARSAL_SCHEDULE_REPORT_JASPER_URL = "rehearsalScheduleReport";
    private static final String MASTER_SCHEDULE_REPORT_JASPER_URL = "masterScheduleReport";
    private static final String CANCELLED_APPOINTMENTS_JASPER_URL = "cancelledAppointmentReport";
    //Added new active students report  GSSP-185
    private static final String ACTIVE_STUDENTS_REPORT_JASPER_URL = "activeStudentsReport";
    //For GSSP-203
    private static final String STUDENT_CHECK_IN_REPORT_JASPER_URL = "studentCheckInReport";
    //For GSSP-205
    private static final String INACTIVE_STUDENTS_REPORT_JASPER_URL = "inActiveStudentsReport";
	/* Added for NewInsAptReport _ June 2015 Enhancement */
    private static final String INSTRUCTOR_OPEN_APPOINTMENTS_REPORT_JASPER_URL = "instructorOpenAppointmentsReport";
    //For GSSP-170
    private static final String CONFLICT_APPOINTMENTS_BY_INSTRUCTOR_REPORT_JASPER_URL = "conflictAppointmentsReportByInsrtuctor";
    private static final String CONFLICT_APPOINTMENTS_BY_ROOM_REPORT_JASPER_URL = "conflictAppointmentsReportByRoom";
    /* For GSSP-161, Added */
    private static final String INSTRUCTOR_OUTSIDE_APPOINTMENTS_REPORT_JASPER_URL = "instructorOutsideAppointmentsReport";
    

    @RequestMapping(PRINT_INSTRUCTOR_SCHEDULE_REPORT_MAPPING)
    public ModelAndView printInstructorScheduleReportToPDF(@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,
                                                           String startDate, String endDate, HttpServletRequest request,HttpServletResponse response) {
        String rootPath = request.getServletContext().getRealPath("/");

        if (LOG.isDebugEnabled()) {
            LOG.debug("Try to generate InstructorScheduleReportToPDF between startDate {} and endDate {}", startDate, endDate);
        }

        Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
        Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

        List<InstructorReportPagingDTO> result = reportService.generateInstructorReportPDF(location.getLocationId(), queryStartDate, queryEndDate, false);

        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("FROM_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryStartDate));
        paramMap.put("TO_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryEndDate));
        paramMap.put("SUBREPORT_DIR", rootPath + SUBREPORT_DIR);
        paramMap.put("instructorReport", result);

        return new ModelAndView(INSTRUCTOR_SCHEDULE_REPORT_JASPER_URL, paramMap);
    }
    
    @RequestMapping(PRINT_INSTRUCTOR_STATUS_SCHEDULE_REPORT_MAPPING)
    public ModelAndView printInstructorStatusScheduleReportToPDF(@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,
        String startDate, String endDate, HttpServletRequest request,HttpServletResponse response) {
        String rootPath = request.getServletContext().getRealPath("/"); 
        if (LOG.isDebugEnabled()) {
            LOG.debug("Try to generate InstructorScheduleReportToPDF between startDate {} and endDate {}", startDate, endDate);
        }

        Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
        Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

        List<InstructorReportPagingDTO> result = reportService.generateInstructorAppointmentStatusReport(location.getLocationId(), queryStartDate, queryEndDate);

        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("FROM_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryStartDate));
        paramMap.put("TO_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryEndDate));
        paramMap.put("SUBREPORT_DIR", rootPath + SUBREPORT_DIR);
        paramMap.put("instructorStatusReport", result); 
        return new ModelAndView(INSTRUCTOR_STATUS_SCHEDULE_REPORT_JASPER_URL, paramMap);
    }
    
    @RequestMapping(PRINT_MASTER_SCHEDULE_REPORT_MAPPING)
    @SuppressWarnings("unchecked")
    public ModelAndView printMasterScheduleReportToPDF(@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) long profileId,
                                                       String startDate, String endDate, HttpServletRequest request) {
        String rootPath = request.getServletContext().getRealPath("/");
        if (LOG.isDebugEnabled()) {
            LOG.debug("Try to generate MasterScheduleReportToPDF between startDate {} and endDate {}", startDate, endDate);
        }

        Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
        Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

        List<Map<String, Object>> list = reportService.generateMasterScheduleReport(profileId, queryStartDate, queryEndDate);

        Map<String, Object> paramMap = new HashMap<String, Object>();
        List<MasterReportPagingDTO> parent = new LinkedList<MasterReportPagingDTO>();

        for (Map<String, Object> map : list) {
            MasterReportPagingDTO child = new MasterReportPagingDTO();
            child.setCurrentDate((String) map.get(ReportService.REPORT_DATE_KEY));
            child.setEmployees((List<InstructorAppointmentBusinessHours>) map.get(ReportService.EMPLOYEE_LIST_KEY));
            child.setMasters((List<MasterScheduleReportDTO>) map.get(ReportService.REPORT_DTO_LIST_KEY));
            parent.add(child);
        }

        paramMap.put("FROM_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryStartDate));
        paramMap.put("TO_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryEndDate));
        paramMap.put("SUBREPORT_DIR", rootPath + SUBREPORT_DIR);
        paramMap.put("masterReport", new JRBeanCollectionDataSource(parent));

        return new ModelAndView(MASTER_SCHEDULE_REPORT_JASPER_URL, paramMap);
    }

    @RequestMapping(PRINT_REHEARSAL_SCHEDULE_REPORT_MAPPING)
    @SuppressWarnings("unchecked")
    public ModelAndView rehearsalScheduleReport(@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long profileId,
                                                String startDate, String endDate, HttpServletRequest request) {
        String rootPath = request.getServletContext().getRealPath("/");
        Map<String, Object> modelMap = new HashMap<String, Object>();

        List<RehearsalScheduleReportParentDTO> rehearsalScheduleReportParentDTOs = new ArrayList<RehearsalScheduleReportParentDTO>();
        Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
        Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

        List<Map<String, Object>> dtos = reportService.generateRehearsalScheduleReport(profileId, queryStartDate, queryEndDate);

        for (Map<String, Object> dto : dtos) {
            RehearsalScheduleReportParentDTO child = new RehearsalScheduleReportParentDTO();
            child.setCurrentDate((String) dto.get(ReportService.REPORT_DATE_KEY));
            child.setList((List<RehearsalScheduleReportDTO>) dto.get(ReportService.REPORT_DTO_LIST_KEY));
            rehearsalScheduleReportParentDTOs.add(child);
        }

        modelMap.put("parent_map", rehearsalScheduleReportParentDTOs);
        modelMap.put("FROM_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryStartDate));
        modelMap.put("TO_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryEndDate));
        modelMap.put("SUBREPORT_DIR", rootPath + SUBREPORT_DIR);
        return new ModelAndView(REHEARSAL_SCHEDULE_REPORT_JASPER_URL, modelMap);
    }

    @RequestMapping(PRINT_REHEARSAL_BOOKING_REPORT_MAPPING)
    @SuppressWarnings("unchecked")
    public ModelAndView rehearsalBooking(@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long profileId,
                                         String startDate, String endDate, HttpServletRequest request) {
        String rootPath = request.getServletContext().getRealPath("/");
        Map<String, Object> modelMap = new HashMap<String, Object>();

        List<RehearsalBookingReportParentDTO> rehearsalBookingReportParentDTOs = new ArrayList<RehearsalBookingReportParentDTO>();
        Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
        Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

        List<Map<String, Object>> dtos = reportService.generateRehearsalBookingReport(profileId, queryStartDate, queryEndDate);

        for (Map<String, Object> dto : dtos) {
            RehearsalBookingReportParentDTO child = new RehearsalBookingReportParentDTO();
            child.setCurrentDate((String) dto.get(ReportService.REPORT_DATE_KEY));
            child.setList((List<RehearsalBookingReportDTO>) dto.get(ReportService.REPORT_DTO_LIST_KEY));
            rehearsalBookingReportParentDTOs.add(child);
        }

        modelMap.put("parent_map", rehearsalBookingReportParentDTOs);
        modelMap.put("FROM_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryStartDate));
        modelMap.put("TO_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryEndDate));
        modelMap.put("SUBREPORT_DIR", rootPath + SUBREPORT_DIR);

        return new ModelAndView(REHEARSAL_BOOKING_REPORT_JASPER_URL, modelMap);
    }
    
    @SuppressWarnings("unchecked")
	@RequestMapping(PRINT_CANCELLED_APPOINTMENTS_MAPPING)
    public ModelAndView cancelledAppointments(@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long profileId,
            String startDate, String endDate, HttpServletRequest request) {
    	 
    	String rootPath = request.getServletContext().getRealPath("/");
         
    	 Map<String, Object> modelMap = new HashMap<String, Object>();
         
         Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
         Date queryEndDate = DateTimeUtil.handleEndDate(endDate);
         
         List<CancelledAppointmentReportPagingDTO> parent = new ArrayList<CancelledAppointmentReportPagingDTO>();
         
         List<Map<String, Object>> dtos = reportService.findCancelledAppointmentReport(profileId, queryStartDate, queryEndDate, false);
         for(Map<String, Object> dto : dtos) {
        	 CancelledAppointmentReportPagingDTO child = new CancelledAppointmentReportPagingDTO();
        	 child.setDate1((String)dto.get(ReportService.REPORT_DATE_KEY));
        	 child.setList((List<CancelledAppointmentReportDTO>) dto.get(ReportService.REPORT_DTO_LIST_KEY));
        	 parent.add(child);
         }
         
         modelMap.put("cancelledAppointmentReport", new JRBeanCollectionDataSource(parent));
         modelMap.put("FROM_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryStartDate));
         modelMap.put("TO_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryEndDate));
         modelMap.put("SUBREPORT_DIR", rootPath + SUBREPORT_DIR);
         
         return new ModelAndView(CANCELLED_APPOINTMENTS_JASPER_URL, modelMap);
    }
    
	/** Added for NewInsAptReport _ June 2015 Enhancement */
  //GSSP-190 new field added-InstrutorName
    @RequestMapping(PRINT_INSTRUCTOR_OPEN_APPOINTMENTS_REPORT_MAPPING)
    public ModelAndView printInstructorOpenAppointmentsReportToPDF(@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,
                                                           String startDate, String endDate, String inputExternalId, String instructorName,HttpServletRequest request) {
        String rootPath = request.getServletContext().getRealPath("/");

        if (LOG.isDebugEnabled()) {
            LOG.debug("Try to generate InstructorScheduleReportToPDF between startDate {} and endDate {} for inputExternalId", startDate, endDate, inputExternalId);
        }

        Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
        Date queryEndDate = DateTimeUtil.handleEndDate(endDate);
        String queryinputExternalId = SystemUtil.nameRegular(inputExternalId);
        //GSSP-190 new field added-InstrutorName
        List<InstructorReportPagingDTO> result = reportService.generateInstructorOpenAppointmentsReportPDF(location.getLocationId(), queryStartDate, queryEndDate, queryinputExternalId, true,instructorName);

        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("FROM_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryStartDate));
        paramMap.put("TO_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryEndDate));
        paramMap.put("SUBREPORT_DIR", rootPath + SUBREPORT_DIR);
        paramMap.put("openAppointmentsReport", result);

        return new ModelAndView(INSTRUCTOR_OPEN_APPOINTMENTS_REPORT_JASPER_URL, paramMap);
    }
    
    /** For GSSP-170, Conflicting Appointments by Instructor */
    @RequestMapping(PRINT_CONFLICT_APPOINTMENTS_BY_INSTRUCTOR_REPORT_MAPPING)
    public ModelAndView printConflictAppointmentsByInstructorReportToPDF(@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long profileId,
                                                           String startDate, String endDate, HttpServletRequest request) {
        String rootPath = request.getServletContext().getRealPath("/");

        if (LOG.isDebugEnabled()) {
            LOG.debug("Try to generate ConflictAppointmentsByInstructorReportToPDF between startDate {} and endDate {}", startDate, endDate);
        }

        Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
        Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

        List<InstructorReportPagingDTO> result = reportService.generateConflictAppointmentsReportByInstructor(profileId, queryStartDate, queryEndDate);

        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("FROM_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryStartDate));
        paramMap.put("TO_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryEndDate));
        paramMap.put("SUBREPORT_DIR", rootPath + SUBREPORT_DIR);
        paramMap.put("conflictAppointmentsByInstructor", result);

        return new ModelAndView(CONFLICT_APPOINTMENTS_BY_INSTRUCTOR_REPORT_JASPER_URL, paramMap);
    }
    
    /** For GSSP-170, Conflicting Appointments by Room */
    @RequestMapping(PRINT_CONFLICT_APPOINTMENTS_BY_ROOM_REPORT_MAPPING)
    public ModelAndView printConflictAppointmentsByRoomrReportToPDF(@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long profileId,
                                                           String startDate, String endDate, HttpServletRequest request) {
        String rootPath = request.getServletContext().getRealPath("/");

        if (LOG.isDebugEnabled()) {
            LOG.debug("Try to generate ConflictAppointmentsByRoomrReportToPDF between startDate {} and endDate {}", startDate, endDate);
        }

        Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
        Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

        List<InstructorReportPagingDTO> result = reportService.generateConflictAppointmentsReportByRoom(profileId, queryStartDate, queryEndDate);

        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("FROM_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryStartDate));
        paramMap.put("TO_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryEndDate));
        paramMap.put("SUBREPORT_DIR", rootPath + SUBREPORT_DIR);
        paramMap.put("conflictAppointmentsByRoom", result);

        return new ModelAndView(CONFLICT_APPOINTMENTS_BY_ROOM_REPORT_JASPER_URL, paramMap);
    }
    
    /** For GSSP-161, Instructor outside availability appointments report */
    @RequestMapping(PRINT_INSTRUCTOR_OUTSIDE_APPOINTMENTS_REPORT_MAPPING)
    public ModelAndView printInstructorOutsideAppointmentsReportToPDF(@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long profileId,
    													String tFrom, String tTo, String inputExternalId, String dayType, HttpServletRequest request) {
        String rootPath = request.getServletContext().getRealPath("/");

        if (LOG.isDebugEnabled()) {
            LOG.debug("Try to generate InstructorScheduleReportToPDF between startTime {} and endTime {} for inputExternalId {} for dayType {}", tFrom, tTo, inputExternalId, dayType);
        }

        String queryStartTime = SystemUtil.nameRegular(tFrom);
		String queryEndTime = SystemUtil.nameRegular(tTo);
		String queryinputExternalId = SystemUtil.nameRegular(inputExternalId);
		String querydayType = SystemUtil.nameRegular(dayType);
		
		DateTime startTime =  DateTime.parse(queryStartTime, DateTimeFormat.forPattern("HH:mm"));
		String From_Time = startTime.toString("hh:mm a");
		String To_Time;
		if("23:59".equals(queryEndTime)) {
			To_Time = "12:00 AM";
		}
		else {
			DateTime endTime =  DateTime.parse(queryEndTime, DateTimeFormat.forPattern("HH:mm"));
			To_Time = endTime.toString("hh:mm a"); 
		}

        List<InstructorReportPagingDTO> result = reportService.generateInstructorOutsideAppointmentsReportPDF(profileId, queryStartTime, queryEndTime, queryinputExternalId, querydayType);

        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("FROM_DATE", From_Time);
        paramMap.put("TO_DATE", To_Time);
        paramMap.put("SUBREPORT_DIR", rootPath + SUBREPORT_DIR);
        paramMap.put("outsideAppointmentsReport", result);

        return new ModelAndView(INSTRUCTOR_OUTSIDE_APPOINTMENTS_REPORT_JASPER_URL, paramMap);
    }

    /** Added new active students report  GSSP-185 */
   
      @RequestMapping(PRINT_ACTIVE_STUDENTS_REPORT_MAPPING)
     public ModelAndView generateActiveStudentsReport(
				@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,
				String startDate, String endDate, String activityType, String instructorName,HttpServletRequest request) {
          String rootPath = request.getServletContext().getRealPath("/");

          if (LOG.isDebugEnabled()) {
              LOG.debug("Try to generate generateActiveStudentsReport between startDate {} and endDate {} for activityType", startDate, endDate, activityType);
          }

          Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
          Date queryEndDate = DateTimeUtil.handleEndDate(endDate);
          String queryactivityType = SystemUtil.nameRegular(activityType);
	        
	        List<InstructorReportPagingDTO> result = reportService.generateActiveStudentsReport(location.getLocationId(), queryStartDate, queryEndDate,queryactivityType,  true, instructorName);
			
          Map<String, Object> paramMap = new HashMap<String, Object>();
          paramMap.put("FROM_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryStartDate));
          paramMap.put("TO_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryEndDate));
          paramMap.put("SUBREPORT_DIR", rootPath + SUBREPORT_DIR);
          paramMap.put("activeStudentsReport", result);

          return new ModelAndView(ACTIVE_STUDENTS_REPORT_JASPER_URL, paramMap);
      }
      
      /** Added new student check in report  GSSP-203 */
      
      @RequestMapping(PRINT_STUDENT_CHECK_IN_REPORT_MAPPING)
     public ModelAndView generateStudentCheckInReport(
				@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,
				String startDate, String endDate,HttpServletRequest request) {
          String rootPath = request.getServletContext().getRealPath("/");

          if (LOG.isDebugEnabled()) {
              LOG.debug("Try to generate generateStudentCheckInReport between startDate {} and endDate {} ", startDate, endDate);
          }

          Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
          Date queryEndDate = DateTimeUtil.handleEndDate(endDate);
	        
	        List<InstructorReportPagingDTO> result = reportService.generateStudentCheckInReport(location.getLocationId(), queryStartDate, queryEndDate,true);
			
          Map<String, Object> paramMap = new HashMap<String, Object>();
          paramMap.put("FROM_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryStartDate));
          paramMap.put("TO_DATE", new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH).format(queryEndDate));
          paramMap.put("SUBREPORT_DIR", rootPath + SUBREPORT_DIR);
          paramMap.put("studentCheckInReport", result);

          return new ModelAndView(STUDENT_CHECK_IN_REPORT_JASPER_URL, paramMap);
      }
      
      /** Added new InActive students report  GSSP-205 */
      
      @RequestMapping(PRINT_INACTIVE_STUDENTS_REPORT_MAPPING)
     public ModelAndView generateInActiveStudentsReport(
				@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,HttpServletRequest request,@ModelAttribute(AppConstants.STORE_STRING)
				String storeNumber) {
          String rootPath = request.getServletContext().getRealPath("/");

          if (LOG.isDebugEnabled()) {
              LOG.debug("Try to generate generateInActiveStudentsReport between startDate {} and endDate {} ", location);
          }

            
	        List<InstructorReportPagingDTO> result = reportService.generateInActiveStudentReport(storeNumber,location.getLocationId().toString(),true);
			
          Map<String, Object> paramMap = new HashMap<String, Object>();
          paramMap.put("SUBREPORT_DIR", rootPath + SUBREPORT_DIR);
          paramMap.put("inActiveStudentsReport", result);

          return new ModelAndView(INACTIVE_STUDENTS_REPORT_JASPER_URL, paramMap);
      }

}
