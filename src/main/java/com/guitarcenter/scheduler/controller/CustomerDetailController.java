package com.guitarcenter.scheduler.controller;

import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_PHONE;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_SEC_EMAIL;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import com.guitarcenter.scheduler.common.util.JsonUtil;
import com.guitarcenter.scheduler.common.util.PageInfo;
import com.guitarcenter.scheduler.common.util.PageQueryParams;
import com.guitarcenter.scheduler.dao.criterion.dto.CustomerAppointmentDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorLessonLinkDTO;
import com.guitarcenter.scheduler.dto.CustomerAppointmentsQueryDTO;
import com.guitarcenter.scheduler.dto.CustomerDetailDTO;
import com.guitarcenter.scheduler.dto.CustomerLessonsHistoryDTO;
import com.guitarcenter.scheduler.dto.InstructorScheduleDetailDTO;
import com.guitarcenter.scheduler.dto.InternalRemarkDTO;
import com.guitarcenter.scheduler.dto.UpdateInstructorScheduleMessageDTO;
import com.guitarcenter.scheduler.model.InstructorAppointmentStatus;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.service.CustomerService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.common.util.AppConstants;
/**
 * @Date 4/23/2020 6:08 PM
 * <AUTHOR>
 **/
@RestController
public class CustomerDetailController implements AppConstants{


	private static final String MESSAGE_KEY = "message";
	private static final String STATUS_KEY = "status";
	private static final String TIMEOFF_KEY = "timeoffs";
	private static final String MESSAGE_VALUE_APP		    = "Appointment Id is not valid!";
	//private static final String MESSAGE_VALUE_CMNT		    = "Internal Remarks should not exceeds 2048 characters";
	private static final String MESSAGE_VALUE_STS		    = "Show status should not exceeds 256 characters";
	//private static final String MESSAGE_VALUE_STU_CMNT		    = "Student Notes should not exceeds 2048 characters";
	
    @Autowired
    private CustomerService customerService;
    
    @Resource
	private InstructorService		instructorService;

    @RequestMapping(value = "/customer/info.htm",method = RequestMethod.GET)
    public CustomerDetailDTO getCustomerInfo(@RequestParam(value = "customerId")Long customerId){
        return  customerService.getCustomerDetailById(customerId);
    }


    @RequestMapping(value = "/customer/lessons/history.htm",method = RequestMethod.GET)
    public PageInfo<CustomerLessonsHistoryDTO> getCustomerLessonsHistory(@RequestParam(value = "params",required = false) String params){
        PageQueryParams pageQueryParams = null;
        if(!StringUtils.isEmpty(params)){
            pageQueryParams = JsonUtil.stringToObject(params,PageQueryParams.class);
        }else{
            pageQueryParams = PageQueryParams.generateFirstPage(5);
        }
        PageQueryParams.PageSqlQueryParams sqlQueryParams = pageQueryParams.buildPageSqlQueryParams();

        //TODO sql query

//        CustomerHistoryLessonsQueryModel queryModel = JsonUtil.stringToObject(params, CustomerHistoryLessonsQueryModel.class);
//        PageQueryParams queryParams = queryModel.getPageQueryParams();
//        PageQueryParams.PageSqlQueryParams sqlQueryParams = queryParams.buildPageSqlQueryParams();

        PageInfo pageInfo = new PageInfo(6,5,50);
        List<CustomerLessonsHistoryDTO> list = new ArrayList<>();
        CustomerLessonsHistoryDTO dto = new CustomerLessonsHistoryDTO();
        dto.setLessonName("Online Guitar");
        dto.setLessonDuration("30 Minutes");
        Calendar date2 = Calendar.getInstance();
        date2.setTimeInMillis(new Date().getTime() - 400000l);
        dto.setDate1(new Date());
        dto.setDate2(date2.getTime());
        dto.setInstructorFullName("Jason Steinmetz");
        dto.setLessonStatusName("Completed");
        dto.setLessonStatusCode("completed");
        list.add(dto);
        pageInfo.setData(list);
        return pageInfo;
    }

    @RequestMapping(value = "/customer/appointments.htm", method = RequestMethod.GET)
    public List getCustomerAppointmentsInfo(@RequestParam(value = "params",required = true) String params, HttpSession session){
        CustomerAppointmentsQueryDTO queryModel = JsonUtil.stringToObject(params,CustomerAppointmentsQueryDTO.class);
        return customerService.getCustomerAppointments(queryModel);
    }

    @ResponseBody
    @RequestMapping(value = "/customer/sendReminder.htm",method = RequestMethod.POST)
    public boolean sendReminderToCustomer(@RequestParam(value = "params",required = true) String params){
        CustomerAppointmentDTO dto = JsonUtil.stringToObject(params, CustomerAppointmentDTO.class);
        return customerService.sendEmailReminderToCustomer(dto);
    }


    @RequestMapping(value = "/customer/internal_remarks.htm",method = RequestMethod.GET)
    public PageInfo<InternalRemarkDTO> getCustomerInternalRemarks(@RequestParam(value = "params",required = false) String params){
        PageQueryParams pageQueryParams = null;
        if(!StringUtils.isEmpty(params)){
            pageQueryParams = JsonUtil.stringToObject(params,PageQueryParams.class);
        }else{
            pageQueryParams = PageQueryParams.generateFirstPage(5);
        }
        PageQueryParams.PageSqlQueryParams sqlQueryParams = pageQueryParams.buildPageSqlQueryParams();

        //TODO sql query....


        PageInfo pageInfo =  new PageInfo(5,1,50);
        List<InternalRemarkDTO> list = new ArrayList<>();
        InternalRemarkDTO dto = new InternalRemarkDTO();

        dto.setInstructorFullName("Jeff Steinmetz");
        dto.setRemark("Since there were connectivity issues, we have added the lessons credit back into your account. Make sure you reschedule using the....");
        dto.setRemarkDate(new Date());
        list.add(dto);
        pageInfo.setData(list);
        return pageInfo;
    }

    @RequestMapping(value = "/customer/getCustomerEditDetailsInfo.htm", method = RequestMethod.GET)
    public CustomerDetailDTO getCustomerEditDetailsInfo(long customerId){
        CustomerDetailDTO customerDetailDTO = customerService.getCustomerEditDetails(customerId);
        return customerDetailDTO;
    }
    
    @ResponseBody
    @RequestMapping(value ="/updateInstructorScheduleByStaff.htm", method= RequestMethod.POST)
    public UpdateInstructorScheduleMessageDTO updateInstructorSchedule(@RequestParam(value = "params",required = true) String params){
    	    	
    	Map<String, Object> map = new HashMap<String, Object>();
    	InstructorLessonLinkDTO dto = JsonUtil.stringToObject(params, InstructorLessonLinkDTO.class);
        
    	UpdateInstructorScheduleMessageDTO updateMessage = new UpdateInstructorScheduleMessageDTO(UPDATE_SUCCESS, true);
    	 System.out.println("updateInstructorSchedule  $$ ");
    	 
    	 Person person = (Person) RequestContextHolder.currentRequestAttributes().getAttribute(PERSON_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
         System.out.println("updateInstructorSchedule  $$ person "+person.getPersonId());
         
         Long site = (Long)RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
         		 
    	 InstructorScheduleDetailDTO  instructorScheduleDetailDTO = this.buildInstructorLessonLinkDTO(dto);
    	 
    	 map = this.checkInstructorScheduleAppt(instructorScheduleDetailDTO);
 		 if((Boolean)map.get(STATUS_KEY)){
    	 
    	 InstructorAppointmentStatus instructorAppSts = new InstructorAppointmentStatus();
    	 	
    	 	long appId = Long.parseLong(instructorScheduleDetailDTO.getAppointmentId());
   
 				instructorAppSts.setAppointmentId(appId); 				
 				instructorAppSts.setStatus(instructorScheduleDetailDTO.getShowStatus());
 				instructorAppSts.setStudentNote(null);
 				instructorAppSts.setSiteId(site);
 				instructorAppSts.setVersion(1L);
 				instructorAppSts.setUpdated(new Date());
 				instructorAppSts.setUpdatedBy(person.getPersonId());
 				instructorAppSts.setStudentNote(instructorScheduleDetailDTO.getStudentNote());
 				instructorAppSts.setRemarks(instructorScheduleDetailDTO.getRemarks());
 							
 				 
 				try{
 					instructorService.updateInstructorSchedule(instructorAppSts);
 					
 				}catch(Exception e){
 					updateMessage.setMessage("update failed!");
 					updateMessage.setStatus(false);
 					return updateMessage;
 				}
 		}
 		  	return updateMessage;
    }
    private InstructorScheduleDetailDTO buildInstructorLessonLinkDTO(InstructorLessonLinkDTO instructorLessonLinkDTO) {
		
	    InstructorScheduleDetailDTO instructorScheduleDetailDTO = null;
	    
	    if(null != instructorLessonLinkDTO){
	    	 instructorScheduleDetailDTO = new InstructorScheduleDetailDTO();
	    	 instructorScheduleDetailDTO.setAppointmentId(instructorLessonLinkDTO.getAppointmentId());
	    	 instructorScheduleDetailDTO.setComments((instructorLessonLinkDTO.getComments()));
	    	 instructorScheduleDetailDTO.setShowStatus(instructorLessonLinkDTO.getShowStatus());
	    	 instructorScheduleDetailDTO.setStudentNote((instructorLessonLinkDTO.getStudentNote()));
	    	 //instructorScheduleDetailDTO.setLessonStatus(instructorLessonLinkDTO.getLessonStatus());
	    	 //instructorScheduleDetailDTO.setNextLessonStatus(instructorLessonLinkDTO.getNextLessonStatus());
	    	 instructorScheduleDetailDTO.setAssignment(instructorLessonLinkDTO.getAssignment());
	    	 instructorScheduleDetailDTO.setPracticeNotes(instructorLessonLinkDTO.getPracticeNotes());
	    	 instructorScheduleDetailDTO.setRemarks(instructorLessonLinkDTO.getRemarks());
	    	 //instructorScheduleDetailDTO.setRate(instructorLessonLinkDTO.getRate());
		
	    }
	    System.out.println(instructorScheduleDetailDTO);
	return instructorScheduleDetailDTO;
}
    
	private static final String RETURN_DTO = "dto";
    @RequestMapping("/customer/updateCustomerEditDetailsInfo.htm")
	@ResponseBody
	public Map<String, Object> updateCustomerEditDetailsInfo(@RequestBody CustomerDetailDTO dto)throws RuntimeException {

		Map<String, Object> map = new HashMap<String, Object>();
		map.put(STATUS_KEY, false);
		map = this.checkEditCustomer(dto);
		
		if ((Boolean) map.get(STATUS_KEY)) {
		try{	
			customerService.saveEditCustomerDetails(dto);
			map.put(RETURN_DTO, "yes");
		} catch (Exception e) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Customer edit update Failed");
			return map;
		}
		}
		return map;
	}
    
    public Map<String, Object> checkInstructorScheduleAppt(InstructorScheduleDetailDTO dto) throws RuntimeException {
		  
		Map<String, Object> map = new HashMap<String, Object>();
		if(dto.getAppointmentId() == null){
			map.put(MESSAGE_KEY, MESSAGE_VALUE_APP);
			map.put(STATUS_KEY, false);
			return map; 
		}
		if(dto.getAppointmentId() != null && checkValidNumber(dto.getAppointmentId())){
			map.put(STATUS_KEY,true);
			
		}else{
			map.put(MESSAGE_KEY, MESSAGE_VALUE_APP);
			map.put(STATUS_KEY, false);
			return map; 
		}
		
		if(dto.getShowStatus() == null){
			map.put(MESSAGE_KEY, MESSAGE_VALUE_STS);
			map.put(STATUS_KEY, false);
			return map; 
		}
		if(dto.getShowStatus() != null && dto.getShowStatus().length()> 256){
			map.put(MESSAGE_KEY, MESSAGE_VALUE_STS);
			map.put(STATUS_KEY, false);
			return map; 
		}else {
				
		map.put(STATUS_KEY, true);
		return map;	}
	}
    
    public static boolean checkValidNumber(Object obj) throws RuntimeException {
    	 
		boolean flag=false;
		try {
			// checking Long float using parseInt() method
			Long.parseLong((String) obj);
			flag = true;
		} catch (NumberFormatException e) {
			//LOGGER.info("Number validation failed :"+e);
			System.out.println("Error"+e);
		}
		
		
		return flag;
	}
    
    private Map<String, Object> checkEditCustomer(CustomerDetailDTO dto) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put(STATUS_KEY, true);
		String secondaryEmail = dto.getSecondaryEmail();
		
		if (secondaryEmail != null && !"".equals(secondaryEmail.trim())) {
			String regex = "^(.+)@(.+)$";
			Pattern pattern = Pattern.compile(regex);
			Matcher matcher = pattern.matcher(secondaryEmail);
			 if(matcher.matches()){
				 map.put(STATUS_KEY, matcher.matches()); 
			 }else{
			map.put(MESSAGE_KEY, VALIDATION_SEC_EMAIL);
			map.put(STATUS_KEY, matcher.matches());
			 }
			 return map; 
		}
		String phone = dto.getPhoneNumber();
		if (phone != null || !"".equals(phone.trim())) {
			 
			 if(isNumeric(phone)){
				 map.put(STATUS_KEY,isNumeric(phone)); 
			 }else{
			map.put(MESSAGE_KEY, VALIDATION_PHONE);
			map.put(STATUS_KEY,isNumeric(phone));
			 }
			 return map; 
		}
		return map;
    }
    public static boolean isNumeric(String strNum) {
        if (strNum == null) {
            return false;
        }
        try {
            double d = Double.parseDouble(strNum);
        } catch (NumberFormatException nfe) {
            return false;
        }
        return true;
    }
}
