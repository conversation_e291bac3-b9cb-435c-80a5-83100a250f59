package com.guitarcenter.scheduler.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.SessionAttributes;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.supercsv.io.CsvBeanWriter;
import org.supercsv.io.ICsvBeanWriter;
import org.supercsv.prefs.CsvPreference;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.criterion.dto.ExportAppointmentDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorAppointmentStatusListDTO;
import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.dto.InstructorReportPagingDTO;
import com.guitarcenter.scheduler.dto.RehearsalBookingReportDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.AppointmentCancelReason;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.LocationProfile;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.enums.IsRecurring;
import com.guitarcenter.scheduler.service.ActivityService;
import com.guitarcenter.scheduler.service.AppointmentEmailService;
import com.guitarcenter.scheduler.service.AppointmentService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.ReportService;
import com.guitarcenter.scheduler.service.RoomService;
import com.guitarcenter.scheduler.service.ServiceService;

@SessionAttributes({ AppConstants.LOCATION_ID_SESSION_KEY,
		AppConstants.LOCATION_PROFILE_ID_SESSION_KEY,AppConstants.STORE_STRING  })
@Controller
public class ReportController implements AppConstants {

	private static final Logger LOG = LoggerFactory
			.getLogger(ReportController.class);

	// mapping constants
	private static final String REPORT_MANAGE_MAPPING = "/reportManage.htm";
	private static final String GENERATE_INSTRUCTOR_SCHEDULE_REPORT_MAPPING = "report/instructorScheduleReport.htm";
	private static final String GENERATE_INSTRUCTOR_APPOINTMENT_STATUS_SCHEDULE_REPORT_MAPPING = "report/instructorAppointmentStatusScheduleReport.htm";
	private static final String GENERATE_REHEARSAL_BOOKING_REPORT_MAPPING = "report/rehearsalBookingReport.htm";
	private static final String GENERATE_REHEARSAL_SCHEDULE_REPORT_MAPPING = "report/rehearsalScheduleReport.htm";
	private static final String GENERATE_MASTER_SCHEDULE_REPORT_MAPPING = "report/masterScheduleReport.htm";
	private static final String CANCELLED_APPOINTMENT_MAPPING = "report/cancelledAppointments.htm";
	/* Added for NewInsAptReport _ June 2015 Enhancement */
	private static final String GENERATE_INSTRUCTOR_OPEN_APPOINTMENTS_REPORT_MAPPING = "report/instructorOpenAppointmentsReport.htm";
	//Added for GSSP-185 
	private static final String GENERATE_ACTIVE_STUDENTS_REPORT_MAPPING = "report/activeStudentsReport.htm";
	//Added for GSSP-203 
		private static final String GENERATE_STUDENTS_CHECK_IN_MAPPING = "report/StudentCheckInReport.htm";
	//Added for GSSP-205
		private static final String GENERATE_INACTIVE_STUDENTS_REPORT_MAPPING="report/inActiveStudents.htm";
	//For GSSP-170
	private static final String GENERATE_CONFLICTING_APPOINTMENTS_REPORT_BY_INSTRUCTOR_MAPPING = "report/conflictingAppointmentsReportByInsrtuctor.htm";
	private static final String GENERATE_CONFLICTING_APPOINTMENTS_REPORT_BY_ROOM_MAPPING = "report/conflictingAppointmentsReportByRoom.htm";
	private static final String CANCEL_CONFLICTING_APPOINTMENT_MAPPING	= "report/cancelConflictAppointment.htm";
	
	//For GSSP-161
		private static final String GENERATE_INSTRUCTOR_OUTSIDE_APPOINTMENTS_REPORT_MAPPING = "report/instructorOutsideAppointmentsReport.htm";
	

	//Added for GSSP-185	
	private static final String GET_LESSON_TYPES_MAPPING = "report/loadActivityList.htm";
		
	//For GSSP-170
	private static final String MESSAGE_KEY = "message";
	private static final String STATUS_KEY = "status";
	//For GSSP-98
	private static final String	CANCEL_SINGLE_APPOINTMENT = "single";
	
	
	private static final String GENERATE_CSV_LESSON = "generateLessonCSV.htm";

	// forward constants
	private static final String REPORT_PAGE_FORWARD = "includes/report";
	
	// CSV header constants
    private static final String[] CSV_DISPLAY_HEADER_FIELDS = {
        "Customer first name", "Customer last name", "Customer GC ID",
        "Stat code", "Date", "Day of appointment", "Activity Type",
        "Instructor Name", "Appointment cancelled", "Recurring" };
    private static final String[] CSV_DISPLAY_FIELDS = {
        "customerFirstName", "customerLastName", "customerGCID", "statCode",
        "date", "dayOfAppointment", "activityType", "instructorName",
        "appointmentCancelled", "recurring" };
    

	@Autowired
	private ReportService reportService;
	
	@Autowired
	private AppointmentService appointmentService;
	@Autowired
	private ServiceService serviceService;
	//GSSP-278 Code changes
	@Autowired
	private RoomService roomService;
	
	@Autowired
	private InstructorService instructorService;
	//GSSP-278 Code changes
	@Autowired
	private ActivityService activityService;
	//For GSSP-170
	@Autowired
	private AppointmentEmailService appointmentEmailService;

	@RequestMapping(REPORT_MANAGE_MAPPING)
	public String forwardReportPage() {
		return REPORT_PAGE_FORWARD;
	}

	
	
	@RequestMapping(GENERATE_INSTRUCTOR_APPOINTMENT_STATUS_SCHEDULE_REPORT_MAPPING)

	@ResponseBody
	public List<InstructorReportPagingDTO> generateInstructorAppointmentStatusScheduleReport(
			@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,
			String startDate, String endDate) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("query enabled at generateInstructorAppointmentStatusScheduleReport list by locationId:{}",
					location.getLocationId());
		}

		Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
		Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

		return reportService.generateInstructorAppointmentStatusReport(
				location.getLocationId(), queryStartDate, queryEndDate);
	}

	@RequestMapping(GENERATE_INSTRUCTOR_SCHEDULE_REPORT_MAPPING)
	@ResponseBody
	// public List<Map<String, Object>> generateInstructorScheduleReport(
	public List<InstructorReportPagingDTO> generateInstructorScheduleReport(
			@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,
			String startDate, String endDate) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("query enabled instructor list by locationId:{}",
					location.getLocationId());
		}

		Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
		Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

		// return
		// reportService.generateInstructorScheduleReport(location.getLocationId(),
		// queryStartDate, queryEndDate);
		return reportService.generateInstructorReportPDF(
				location.getLocationId(), queryStartDate, queryEndDate, true);
	}


	@RequestMapping(GENERATE_REHEARSAL_BOOKING_REPORT_MAPPING)
	@ResponseBody
	@SuppressWarnings("unchecked")
	public List<Map<String, Object>> generateRehearsalBookingReport(
			@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long profileId,
			String startDate, String endDate) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("query enabled instructor list by profileId:{}",
					profileId);
		}

		Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
		Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

		List<Map<String, Object>> listMap = new ArrayList<Map<String, Object>>();

		List<Map<String, Object>> dtos = reportService
				.generateRehearsalBookingReport(profileId, queryStartDate,
						queryEndDate);

		List<RehearsalBookingReportDTO> all = new LinkedList<RehearsalBookingReportDTO>();
		for (Map<String, Object> dto : dtos) {
			List<RehearsalBookingReportDTO> list = (List<RehearsalBookingReportDTO>) dto
					.get(ReportService.REPORT_DTO_LIST_KEY);
			all.addAll(list);
		}

		Map<String, Object> map = new HashMap<String, Object>();
		map.put(ReportService.REPORT_DTO_LIST_KEY, all);
		listMap.add(map);

		if (LOG.isDebugEnabled()) {
			LOG.debug("Got rehearsal appointment list {}", dtos);
		}
		return listMap;
	}

	@RequestMapping(GENERATE_REHEARSAL_SCHEDULE_REPORT_MAPPING)
	@ResponseBody
	public List<Map<String, Object>> generateRehearsalScheduleReport(
			@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long profileId,
			String startDate, String endDate) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("query enabled instructor list by profileId:{}",
					profileId);
		}

		Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
		Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

		List<Map<String, Object>> listMap = new ArrayList<Map<String, Object>>();

		List<Map<String, Object>> dtos = reportService
				.generateRehearsalScheduleReport(profileId, queryStartDate,
						queryEndDate);

		for (Map<String, Object> dto : dtos) {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put(ReportService.REPORT_DTO_LIST_KEY,
					dto.get(ReportService.REPORT_DTO_LIST_KEY));
			listMap.add(map);
		}

		if (LOG.isDebugEnabled()) {
			LOG.debug("Got rehearsal appointment list {}", dtos);
		}
		return listMap;
	}

	@RequestMapping(GENERATE_MASTER_SCHEDULE_REPORT_MAPPING)
	@ResponseBody
	public List<Map<String, Object>> generateMasterScheduleReport(
			@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long profileId,
			String startDate, String endDate) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("query enabled instructor list by profileId:{}",
					profileId);
		}

		Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
		Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

		return reportService.generateMasterScheduleReport(profileId,
				queryStartDate, queryEndDate);
	}

	/**
	 * Query the cancelled appointment list report
	 * 
	 * @param pProfileId
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	@RequestMapping(CANCELLED_APPOINTMENT_MAPPING)
	@ResponseBody
	public List<Map<String, Object>> generateCancelledAppointmentReport(
			@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long pProfileId,
			String startDate, String endDate) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("query cancelled appointment list by profileId {}",
					pProfileId);
		}

		Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
		Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

		return reportService.findCancelledAppointmentReport(pProfileId,
				queryStartDate, queryEndDate, false);
	}
	
	/**
	 * Added for NewInsAptReport _ June 2015 Enhancement
	 * 
	 * @param location
	 * @param startDate
	 * @param endDate
	 * @param inputExternalId
	 * @return
	 */	
	//GSSP-190, added
	@RequestMapping(GENERATE_INSTRUCTOR_OPEN_APPOINTMENTS_REPORT_MAPPING)
	@ResponseBody
	// public List<Map<String, Object>> generateInstructorOpenAppointmentsReport(
	public List<InstructorReportPagingDTO> generateInstructorOpenAppointmentsReport(
			@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,
			String startDate, String endDate, String inputExternalId, String instructorName) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("query enabled instructor list by locationId:{}",
					location.getLocationId());
		}

		Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
		Date queryEndDate = DateTimeUtil.handleEndDate(endDate);
		String queryinputExternalId = SystemUtil.nameRegular(inputExternalId);
		
		return reportService.generateInstructorOpenAppointmentsReportPDF(
				location.getLocationId(), queryStartDate, queryEndDate, queryinputExternalId, true, instructorName);
	}
	
	/**
	 * For GSSP-170, Conflicting Appointments by Instructor
	 * 
	 * @param location
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	@RequestMapping(GENERATE_CONFLICTING_APPOINTMENTS_REPORT_BY_INSTRUCTOR_MAPPING)
	@ResponseBody
	public List<InstructorReportPagingDTO> generateConflictAppointmentsReportByInstructor(
			@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long pProfileId,
			String startDate, String endDate) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("query enabled conflicting appointments list by instructor based on profileId:{}",
					pProfileId);
		}

		Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
		Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

		return reportService.generateConflictAppointmentsReportByInstructor(
				pProfileId, queryStartDate, queryEndDate);
	}
	
	/**
	 * For GSSP-170, Conflicting Appointments by Room
	 * 
	 * @param location
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	@RequestMapping(GENERATE_CONFLICTING_APPOINTMENTS_REPORT_BY_ROOM_MAPPING)
	@ResponseBody
	public List<InstructorReportPagingDTO> generateConflictAppointmentsReportByRoom(
			@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long pProfileId,
			String startDate, String endDate) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("uery enabled conflicting appointments list by room based on profileId:{}",
					pProfileId);
		}
		
		Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
		Date queryEndDate = DateTimeUtil.handleEndDate(endDate);

		return reportService.generateConflictAppointmentsReportByRoom(
				pProfileId, queryStartDate, queryEndDate);
	}
	
	/**
	 * For GSSP-170, Cancel conflicting Appointments
	 * 
	 * @param appointmentId
	 * @param cancelType
	 * @param enableMail
	 * @return
	 */
	@RequestMapping(CANCEL_CONFLICTING_APPOINTMENT_MAPPING)
	@ResponseBody
	public Map<String, Object> cancelAppointment(String appointmentId, String cancelType, boolean enableMail) {		
		
		Person person = (Person) RequestContextHolder.currentRequestAttributes().getAttribute(PERSON_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		
		Map<String, Object> map = new HashMap<String, Object>();
		map.put(STATUS_KEY, true);
		map.put(MESSAGE_KEY, "Cancel appointment successfully!");
		
		try {
//GSSP-250 changes for cancellation
			long cancelReason=3L;			
			Appointment activityinfo=appointmentService.getAppointment(Long.parseLong(appointmentId));
			String activityId=activityinfo.getActivity().getActivityId().toString();
			long serviceId=activityinfo.getActivity().getService().getServiceId();			
			boolean isCancelled = appointmentService.cancelAppointment(Long.parseLong(appointmentId), cancelType, cancelReason, person, activityId,serviceId);
			
			if(!isCancelled) {
				map.put(STATUS_KEY, false);
				map.put(MESSAGE_KEY, "Cancel appointment failed!");
			}
			if(isCancelled && enableMail){
				
				Appointment appInfo = appointmentService.getAppointment(Long.parseLong(appointmentId));
				
				long profileId = (Long)RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY,
						RequestAttributes.SCOPE_SESSION);
				LocationProfile locationProfile = new LocationProfile();
				locationProfile.setProfileId(profileId);
				
				appInfo.setLocationProfile(locationProfile);
				
				//For GSSP-98, email for Cancel single appointment in a series
				String recStatus;
				if(CANCEL_SINGLE_APPOINTMENT.equals(cancelType.toString().trim().toLowerCase()) && 
							IsRecurring.Y.equals(appInfo.getAppointmentSeries().getIsRecurring())) {
					recStatus = "3";
				} else if(CANCEL_SINGLE_APPOINTMENT.equals(cancelType.toString().trim().toLowerCase())) {
					recStatus = "1";
				} else {
					recStatus = "2";
				}
				String instructorName = "Not Applicable";
				//GSSP-278 added cancel reason code, Activity type and the Roomname.		
				
				 if (null != appInfo.getInstructor().getInstructorId())
				 {	 
					
					Instructor instructor = instructorService.getInstructor(appInfo.getInstructor().getInstructorId());	
				

					 instructorName =( org.apache.commons.lang.StringUtils.isBlank(instructor.getPerson().getFirstName())?
							"":instructor.getPerson().getFirstName()) + " " 									
							+ (org.apache.commons.lang.StringUtils.isBlank(instructor.getPerson().getLastName())?"":
								instructor.getPerson().getLastName());
									
				 }
				 String cancelReasonCode=null;
					Activity  activityName  = activityService.getActivityByActivityId(appInfo.getActivity().getActivityId());
					//Cancel Reason message will not dispaly for Rehearsal,Floor,Jumpstart in online lesson services
				/*	if(appInfo.getActivity().getActivityId()== 100 ||appInfo.getActivity().getActivityId()== 140 ){
						cancelReasonCode= null;
					}
					else{
						List<AppointmentCancelReason> CancelCodeList= appointmentService.getCancelReasonCode(cancelReason);
						 
							 cancelReasonCode=CancelCodeList.get(0).getCancelReason();
						
					}
						*/
				//Changes for GSSP-243
				appointmentEmailService.sendEmailForCancelAppt(appInfo, recStatus, appInfo.getDuration(), appInfo.getCustomers(),instructorName,activityName,profileId);
			}
		} catch (Exception e) {
			LOG.error("Caught an exception {} in CalendarController.cancelAppointment", e);
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Cancel appointment failed!");
		}
		return map;
	}
	
	/**
	 * For GSSP-161, Added for Instructor outside availability report
	 * 
	 * @param location
	 * @param startTime
	 * @param endTime
	 * @param inputExternalId
	 * * @param dayType
	 * @return
	 */	
	@RequestMapping(GENERATE_INSTRUCTOR_OUTSIDE_APPOINTMENTS_REPORT_MAPPING)
	@ResponseBody
	public List<InstructorReportPagingDTO> generateInstructorOutsideAppointmentsReport(
			@ModelAttribute(AppConstants.LOCATION_PROFILE_ID_SESSION_KEY) Long pProfileId,
			String tFrom, String tTo, String inputExternalId, String dayType) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("query enabled instructor list by ProfileId:{}",
					pProfileId);
		}

		String queryStartTime = SystemUtil.nameRegular(tFrom);
		String queryEndTime = SystemUtil.nameRegular(tTo);
		String queryinputExternalId = SystemUtil.nameRegular(inputExternalId);
		String querydayType = SystemUtil.nameRegular(dayType);
		
		return reportService.generateInstructorOutsideAppointmentsReportPDF(
				pProfileId, queryStartTime, queryEndTime, queryinputExternalId, querydayType);
	}

	/**
	 * Export lesson appointment to csv file
	 * For GCSS-644
	 * 
	 * @param request HttpServletRequest
	 * @param response HttpServletResponse
	 * @param location Location
	 * @param startDate 
	 * @param endDate
	 * @throws IOException
	 */
	@RequestMapping(GENERATE_CSV_LESSON)
	public void generateCSVFile(
			HttpServletRequest request,
			HttpServletResponse response,
			@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,
			String startDate, String endDate) throws IOException {
	
		StringBuilder fileName = new StringBuilder("ExportLessons_");
		if (startDate.trim().equals(endDate.trim())) {
			fileName.append(startDate.replace("/", ""));
		} else {
			fileName.append(startDate.replace("/", ""))
			    .append("-")
			    .append(endDate.replace("/", ""));
		}
		fileName.append(".csv");

		response.setContentType("text/csv;charset=utf-8");
		response.setHeader("Content-Disposition", "attachment;filename=" + fileName.toString());
		
		Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
		Date queryEndDate = DateTimeUtil.handleEndDate(endDate);
		
		//Utilize ICsvBeanWriter to generate csv
		ICsvBeanWriter writer = null;
		writer = new CsvBeanWriter(response.getWriter(), CsvPreference.STANDARD_PREFERENCE);
		writer.writeHeader(CSV_DISPLAY_HEADER_FIELDS);
		
		List<ExportAppointmentDTO> list = appointmentService.getExportLessionCSVData(location.getLocationId(), queryStartDate, queryEndDate);
		for(ExportAppointmentDTO dto : list) {
			writer.write(dto, CSV_DISPLAY_FIELDS);
		}
		if(null != writer) {
			writer.close();
		}
	}
	//Added for GSSP-185
		@RequestMapping(GENERATE_ACTIVE_STUDENTS_REPORT_MAPPING)
		@ResponseBody
		
		public List<InstructorReportPagingDTO> generateActiveStudentsReport(
				@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,
				String startDate, String endDate, String activityType, String instructorName) {
			if (LOG.isDebugEnabled()) {
				LOG.debug("query enabled Student List by locationId:{}",
						location.getLocationId());
			}

			Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
			Date queryEndDate = DateTimeUtil.handleEndDate(endDate);
			String queryactivityType = SystemUtil.nameRegular(activityType);
			
			return reportService.generateActiveStudentsReport(
					location.getLocationId(), queryStartDate, queryEndDate,queryactivityType,  true, instructorName);
		}
		//Added for GSSP-205
				@RequestMapping(GENERATE_INACTIVE_STUDENTS_REPORT_MAPPING)
				@ResponseBody
				
				public List<InstructorReportPagingDTO> generateInActiveStudentReport(
						@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,@ModelAttribute(AppConstants.STORE_STRING)
						String storeNumber) {
					if (LOG.isDebugEnabled()) {
						LOG.debug("query enabled Student List by externalId:{}",
								location.getExternalId());
					}
					
					
					
					
					//return reportService.generateInActiveStudentReport(location.getExternalId().toString(),location.getLocationId().toString(), true);
					
					return reportService.generateInActiveStudentReport(storeNumber,location.getLocationId().toString(), true);
				}
				
		
		
		@RequestMapping(GET_LESSON_TYPES_MAPPING)
		@ResponseBody
		
		public List<ActivityDTO> generateActiveStudentsReport(
				@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location) 
		{
			
			List<ActivityDTO> lessonList = null;
			
			if (LOG.isDebugEnabled()) {
				LOG.debug("query enabled Student List by locationId:{}",
						location.getLocationId());
				LOG.debug("Fetching all Lesson Types");
			}
			
			
			lessonList = reportService.findLessonTypes(location.getLocationId());
			
			lessonList.add(0, new ActivityDTO(new Long(0), "All Activities"));
						
		
			return lessonList;
		}
		//Added for GSSP-203
				@RequestMapping(GENERATE_STUDENTS_CHECK_IN_MAPPING)
				@ResponseBody
				
				public List<InstructorReportPagingDTO> generateStudentCheckInReport(
						@ModelAttribute(AppConstants.LOCATION_ID_SESSION_KEY) Location location,
						String startDate, String endDate) {
					if (LOG.isDebugEnabled()) {
						LOG.debug("query enabled Student List by locationId:{}",
								location.getLocationId());
					}

					Date queryStartDate = DateTimeUtil.handleStartDate(startDate);
					Date queryEndDate = DateTimeUtil.handleEndDate(endDate);
					
					
					return reportService.generateStudentCheckInReport(
							location.getLocationId(), queryStartDate, queryEndDate,  true);
				}
				
	
}
