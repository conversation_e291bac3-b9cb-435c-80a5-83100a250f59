package com.guitarcenter.scheduler.controller;

import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.SessionAttributes;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import com.guitarcenter.scheduler.common.util.ActivityAndServiceUtil;
import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.dto.ProfileActivityCreateDTO;
import com.guitarcenter.scheduler.dto.ServiceDTO;
import com.guitarcenter.scheduler.dto.UpdateMessageDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileActivity;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.RequiresInstructor;
import com.guitarcenter.scheduler.service.ActivityService;
import com.guitarcenter.scheduler.service.LocationProfileService;
import com.guitarcenter.scheduler.service.ServiceService;
import com.guitarcenter.scheduler.service.impl.ServiceAndActivityValidate;

@SessionAttributes({AppConstants.LOCATION_PROFILE_ID_SESSION_KEY,AppConstants.PERSON_SESSION_KEY,AppConstants.SITE_ID_SESSION_KEY})
@Controller
public class ProfileActivityController implements AppConstants{

	private static final Logger		LOGGER		= LoggerFactory.getLogger(ProfileActivityController.class);
	private static final String		STATUS_KEY	= "status";
	private static final String		MESSAGE_KEY	= "message";
	private static final String VALIDATE_FLAG = "flag";
	private static final String VALIDATE_MESSAGE = "message";
	private static final String VALIDATE_OBJECT = "object";
	
	
	@Autowired
	private ActivityService			activityService;
	
	@Autowired
	private ServiceService			serviceService;

	@Autowired
	private LocationProfileService	locationProfileService;
	
	@Autowired
	private ServiceAndActivityValidate	serviceAndActivityValidate;

	/**
	 * @return List<ActivityDTO>
	 */
	@RequestMapping("profileActivity/loadProfileActivityList.htm")
	@ResponseBody
	public List<ActivityDTO> loadProfileActivityList() {
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(
				AppConstants.LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		List<ActivityDTO> list = new LinkedList<ActivityDTO>();
		try {
//			List<ProfileActivity> alist = activityService.loadProfileActivityByProfileId(profileId);
			/**
			 * For gcss-578,query the activity list that assosiated service is enabled
			 */
			List<ProfileActivity> alist = activityService.findActivityOfEnabledServiceAndProfile(profileId);
			for (ProfileActivity activity : alist) {
				list.add(ActivityAndServiceUtil.initActivityDTO(activity));
			}
			Collections.sort(list);
		} catch (Exception e) {
			LOGGER.error(e + "Get profileActivityDTO list Failed!");
		}
		return list;
	}

	@RequestMapping("profileActivity/addProfileActivity.htm")
	@ResponseBody
	public Map<String, Object> addProfileActivity(@ModelAttribute(LOCATION_PROFILE_ID_SESSION_KEY)
	long profileId, @RequestBody
	ProfileActivityCreateDTO pacd, @ModelAttribute(PERSON_SESSION_KEY)
	Person person , @ModelAttribute(SITE_ID_SESSION_KEY) Long siteId) {
		Site site = new Site();
		site.setSiteId(siteId);
		Map<String, Object> map = checkActivitySave(profileId,pacd.getServiceType(),pacd.getEnable());
		if(!(Boolean)map.get(STATUS_KEY)){
			return map;
		}
		try {
			map = locationProfileService.saveProfileServiceAndProfileActivity(pacd, profileId, site, person, map);
			map.put(STATUS_KEY, true);
			map.put(MESSAGE_KEY, "Save activity Sucessfully!!");
			//map.put("activityDTO", ActivityAndServiceUtil.initActivityDTO((Activity) map.get("activity")));
		} catch (Exception e) {
			LOGGER.info(e.getMessage());
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Save activity failed!");
			e.printStackTrace();
		}

		return map;
	}
	
	//Changes made for 263
		private Map<String, Object> checkActivitySave(Long profileId,
				Long serviceType, String enable) {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put(STATUS_KEY, true);
			
			if(null != profileId && null != serviceType &&  null!= enable && "on".equals(enable) && (Boolean)map.get(STATUS_KEY)){
				Boolean result = serviceAndActivityValidate.profileActivityCanEnable(profileId, serviceType);
				if(!result){
					map.put(STATUS_KEY, false);
					map.put(MESSAGE_KEY, "Service is disabled,activity cannot be enabled!");
				}
			}else{
				map.put(STATUS_KEY, false);
				map.put(MESSAGE_KEY, "Invalid Input Data!");
			}
			return map;
		}

	@RequestMapping("profileActivity/searchActivityByName.htm")
	@ResponseBody
	public List<ActivityDTO> searchActivityByName(String activityName, @ModelAttribute(LOCATION_PROFILE_ID_SESSION_KEY)
	long profileId) {

		List<Activity> qlist = activityService.queryActivityByDimName(activityName);

		List<ProfileActivity> plist = activityService.loadProfileActivityByProfileId(profileId);

		List<ActivityDTO> alist = new LinkedList<ActivityDTO>();
		boolean tempFlag = true;

		for (Activity activityCen : qlist) {
			for (ProfileActivity activityPro : plist) {
				if (activityCen.getActivityId().equals(activityPro.getActivity().getActivityId())) {
					tempFlag = false;
				}
			}
			if (tempFlag) {
				ActivityDTO dto = new ActivityDTO();
				dto.setActivityId(activityCen.getActivityId());
				dto.setActivityName(activityCen.getActivityName());
				alist.add(dto);
			}
			tempFlag = true;
		}

		return alist;
	}
	
	@RequestMapping("profileActivity/initProfileActivity.htm")
	@ResponseBody
	public Map<String, Object> initialCreateActivity(Long activityId) {
		Activity activity = activityService.getActivityByActivityId(activityId);
		Map<String, Object> map = new HashMap<String, Object>();
		Map<String, Object> tempMap = new HashMap<String, Object>();

		Long duration = activity.getMinimumDuration();
		String minimumDuration = String.valueOf(duration);
		map.put("activityId", activityId);
		tempMap.put("id", activity.getService().getServiceId());
		tempMap.put("serviceName", activity.getService().getServiceName());
		map.put("minimumDuration", minimumDuration);
		map.put("minimumDurationText", ActivityAndServiceUtil.getDuration(duration));
		map.put("maxmumDuration", String.valueOf(activity.getMaximumDuration()));
		map.put("maxmumDurationText", ActivityAndServiceUtil.getDuration(activity.getMaximumDuration()) );
		map.put("minAttender", (activity.getMinimumAttendees() == -1 || activity.getMinimumAttendees() == null)?"":activity.getMinimumAttendees());
		map.put("maxAttender", activity.getMaximumAttendees());
		map.put("instructor", activity.getRequiresInstructor());
		map.put("service", tempMap);
		map.put("enable", Enabled.N.equals(activity.getEnabled()) ? false : true);
		return map;
	}
	
	//GCSS-657
	@RequestMapping("profileActivity/deleteProfileActivity.htm")
	@ResponseBody
	public UpdateMessageDTO deleteProfileActivity(Long activityId, @ModelAttribute(PERSON_SESSION_KEY)
	Person person) {
		UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO("Delete success!", true);

		Map<String, Object> map = serviceAndActivityValidate.profileActivityCanDelete(activityId);
		Boolean result = (Boolean) map.get(VALIDATE_FLAG);
		if (!result) {
			updateMessageDTO.setMessage((String) map.get(VALIDATE_MESSAGE));
			updateMessageDTO.setStatus(false);
			return updateMessageDTO;
		}
		
		ProfileActivity profileActivity = (ProfileActivity) map.get(VALIDATE_OBJECT);
		try {
			locationProfileService.deleteProfileActivity(profileActivity.getLocationProfile().getProfileId(), profileActivity.getActivity().getActivityId());
		} catch (Exception e) {
			LOGGER.info(e.getMessage());
			updateMessageDTO.setMessage(e.getMessage());
			updateMessageDTO.setStatus(false);
		}
		return updateMessageDTO;
	}


	@RequestMapping("service/loadActivityDetail.htm")
	@ResponseBody
	public ActivityDTO loadActivityDetail(Long id){
		ActivityDTO activityDetailDto = new ActivityDTO();
		ProfileActivity profileactivity = activityService.getProfileActivity(id);
		activityDetailDto.setActivityId(profileactivity.getProfileActivityId());
		activityDetailDto.setActivityName(profileactivity.getActivity().getActivityName());
		activityDetailDto.setEnable(Enabled.Y.equals(profileactivity.getEnabled())?true:false);
		activityDetailDto.setMaximunAttendees(String.valueOf(profileactivity.getActivity().getMaximumAttendees()));
		Activity activity = activityService.getActivityByActivityId(profileactivity.getActivity().getActivityId());
		activityDetailDto.setServiceName(activity.getService().getServiceName());
		
		Long miniAttendees = activity.getMinimumAttendees();
		String attendee = null;
		if(miniAttendees == null || miniAttendees == -1){
			attendee = String.valueOf((char)32);
		}else{
			attendee = String.valueOf(miniAttendees);
		}
		activityDetailDto.setMinimunAttendees(attendee);
		activityDetailDto.setMinimumDuration(String.valueOf(activity.getMinimumDuration()));
		activityDetailDto.setMaxmumDuration(String.valueOf(activity.getMaximumDuration()));
		activityDetailDto.setVersion(profileactivity.getVersion());
		activityDetailDto.setMaxmumDurationText(ActivityAndServiceUtil.getDuration(activity.getMaximumDuration()));
		activityDetailDto.setMinimumDurationText(ActivityAndServiceUtil.getDuration(activity.getMinimumDuration()));
		String requiresInstructor = "";
		if(RequiresInstructor.N.equals(activity.getRequiresInstructor())){
			requiresInstructor = "N";
		}else if(RequiresInstructor.O.equals(activity.getRequiresInstructor())){
			requiresInstructor = "O";
		}else if(RequiresInstructor.R.equals(activity.getRequiresInstructor())){
			requiresInstructor = "R";
		}
		activityDetailDto.setRequiresInstructor(requiresInstructor);
		ServiceDTO serviceDTO = new ServiceDTO(activity.getService().getServiceId(),activity.getService().getServiceName());
		activityDetailDto.setServiceDTO(serviceDTO);
		return activityDetailDto;
	}
	
	@RequestMapping("service/updateActivity.htm")
	@ResponseBody
	public UpdateMessageDTO updateActivity(@RequestBody ActivityDTO activityDetailDTO,@ModelAttribute(PERSON_SESSION_KEY) Person person,@ModelAttribute("location")Location location){
		ProfileActivity profileActivity = activityService.getProfileActivity(activityDetailDTO.getActivityId());
		Enabled enable = profileActivity.getEnabled();
		profileActivity.setEnabled(activityDetailDTO.getEnable()?Enabled.Y:Enabled.N);
		UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO(UPDATE_SUCCESS, true);
		updateMessageDTO = checkActivityUpdate(profileActivity,enable,updateMessageDTO);
		if(!updateMessageDTO.getStatus()){
			return updateMessageDTO;
		}
		try{
			activityService.updateProfileActivity(profileActivity,person.getPersonId());
		}catch (Exception e) {
			updateMessageDTO = new UpdateMessageDTO(e.getMessage(), false);
			LOGGER.info(e.getMessage());
			return updateMessageDTO;
		}
		activityDetailDTO.setVersion(profileActivity.getVersion());
		activityDetailDTO.setAttenders(profileActivity.getActivity().getMinimumAttendees()+(StringUtils.isNotBlank(String.valueOf(profileActivity.getActivity().getMaximumAttendees()))?" to "+profileActivity.getActivity().getMaximumAttendees() : ""));
		updateMessageDTO.setObject(activityDetailDTO);
		return updateMessageDTO;
	}
	
	
	private UpdateMessageDTO checkActivityUpdate(ProfileActivity profileActivity,Enabled enabled,UpdateMessageDTO updateMessageDTO){
		Activity activity = profileActivity.getActivity();
		Long activityId = activity.getActivityId();
		Long serviceId = profileActivity.getActivity().getService().getServiceId();
		Long profileId = profileActivity.getLocationProfile().getProfileId();
		//from Y to N
		if(Enabled.Y.equals(enabled) && Enabled.N.equals(profileActivity.getEnabled())){
			
			Boolean result = serviceAndActivityValidate.profileActivityCanDisable(profileId, activityId);
			if(!result){
				updateMessageDTO.setMessage("profileActivity has been assigned to appointment,it cannot be disabled!");
				updateMessageDTO.setStatus(false);
				return updateMessageDTO;
			}
			
		}else if(Enabled.N.equals(enabled) && Enabled.Y.equals(profileActivity.getEnabled())){
			
			
			Boolean result1 = serviceAndActivityValidate.profileActivityCanEnable(profileId, serviceId);
			if(!result1){
				updateMessageDTO.setMessage("profileService is disabled,it cannot enable!");
				updateMessageDTO.setStatus(false);	
				return updateMessageDTO;
			}
					
		}
		return updateMessageDTO;
	}
}


