package com.guitarcenter.scheduler.controller;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.SessionAttributes;

import com.guitarcenter.scheduler.common.util.ActivityAndServiceUtil;
import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dto.ActivityCreateDTO;
import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.dto.ServiceDTO;
import com.guitarcenter.scheduler.dto.UpdateMessageDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.RequiresInstructor;
import com.guitarcenter.scheduler.service.ActivityService;
import com.guitarcenter.scheduler.service.ServiceService;
import com.guitarcenter.scheduler.service.impl.ServiceAndActivityValidate;

/**
 * Centralize Activity method.
 * <AUTHOR>
 *
 */
@SessionAttributes({ AppConstants.PERSON_STRING, AppConstants.SITE_ID_SESSION_KEY })
@Controller
public class CentrallizedActivityController implements AppConstants{

	private static final Logger		LOGGER		= LoggerFactory.getLogger(CentrallizedActivityController.class);
	private static final String		MESSAGE_KEY	= "message";
	private static final String		STATUS_KEY	= "status";
	private static final String VALIDATE_FLAG = "flag";
	private static final String VALIDATE_MESSAGE = "message";
	private static final String VALIDATE_OBJECT = "object";
	
	
	@Autowired
	private  ActivityService activityService;
	
	@Autowired
	@Qualifier("serviceService")
	private ServiceService serviceService;

	@Autowired
	@Qualifier("serviceAndActivityValidate")
	private ServiceAndActivityValidate serviceAndActivityValidate;
	
	@RequestMapping("centralized/loadActivityList.htm")
	@ResponseBody
	public List<ActivityDTO> loadActivityList(@ModelAttribute(SITE_ID_SESSION_KEY)final Long siteId) {

		final List<ActivityDTO> dtoList = new LinkedList<ActivityDTO>();
		final List<Activity> activityList = activityService.findBySite(siteId);
		
		for (Activity activity : activityList) {
			dtoList.add(ActivityAndServiceUtil.initActivityDTO(activity));
		}
		return dtoList;
	}
	
	@RequestMapping("centralized/getDefaultInstrctor.htm")
	@ResponseBody
	public String getDefaultInstrctor(final Long id) {
		final Service service = serviceService.getCentralizeService(id);
		String instructor;
		if(service == null){
			instructor = RequiresInstructor.O.toString();
		}else{
			instructor = StringUtils.isBlank(service.getRequiresInstructor().toString())?RequiresInstructor.O.toString():service.getRequiresInstructor().toString().trim();
		}
			
		return instructor;
	}

	
	@RequestMapping("centralized/saveActivity.htm")
	@ResponseBody
	public Map<String, Object> saveActivity(
			@RequestBody final ActivityCreateDTO actDto,
			@ModelAttribute(PERSON_STRING)final Person person,
			@ModelAttribute(SITE_ID_SESSION_KEY)final Long siteId) {
		Activity act = new Activity();
		UpdateMessageDTO updateMessageDTO = checkActivitySave(actDto,act);
		Map<String, Object> map = new HashMap<String, Object>();
		if(!updateMessageDTO.getStatus()){
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY,updateMessageDTO.getMessage());
			return map;
		}
		act = (Activity) updateMessageDTO.getObject();
		
		final Service service = new Service();
		service.setServiceId(actDto.getServiceId());

		final Site site = new Site();
		site.setSiteId(siteId);
		act.setSite(site);

		act.setActivityName(SystemUtil.createName(actDto.getActivityName().trim()));
		act.setService(service);
		
		act.setEnabled("on".equalsIgnoreCase(actDto.getEnable()) ? Enabled.Y
				: Enabled.N);
		if (StringUtils.isNotBlank(actDto.getInstructor())) {

			if (RequiresInstructor.N.toString().equals(actDto.getInstructor())) {
				act.setRequiresInstructor(RequiresInstructor.N);
			} else if (RequiresInstructor.O.toString().equals(
					actDto.getInstructor())) {
				act.setRequiresInstructor(RequiresInstructor.O);
			} else {
				act.setRequiresInstructor(RequiresInstructor.R);
			}
		}

		try {
			activityService.craeteActivity(act, person);
			map.put("activityDTO", ActivityAndServiceUtil.initActivityDTO(act));
			map.put(STATUS_KEY, true);
			map.put(MESSAGE_KEY, "Create activity successfully!");
		} catch (Exception e) {
			map.put(STATUS_KEY, false);
		}

		return map;
	}

	private UpdateMessageDTO checkActivitySave(ActivityCreateDTO actDto,Activity activity) {
		UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO();
		if (StringUtils.isBlank(actDto.getActivityName())) {
			updateMessageDTO.setStatus(false);
			updateMessageDTO.setMessage("Activity name is required!");
			return updateMessageDTO;
		}

		final Boolean resultBoolean  = activityService.hasSameName(SystemUtil.createName(actDto.getActivityName()));
		if(resultBoolean){
			updateMessageDTO.setStatus(false);
			updateMessageDTO.setMessage( "Activity name has been used!");
			return updateMessageDTO;
		}
		
		if (actDto.getServiceId() == null) {
			updateMessageDTO.setStatus(false);
			updateMessageDTO.setMessage("Service is required!");
			return updateMessageDTO;
		}
		
		Service service = serviceService.getCentralizeService(actDto.getServiceId());
		if(service == null){
			updateMessageDTO.setStatus(false);
			updateMessageDTO.setMessage("Service does not exit,or has been deleted!");
			return updateMessageDTO;
		}
		
		//maxAttenders >= 1 minAttenders >=0 or be null 
		//maxAttenders and minAttenders must be number
		//maxAttenders >= minAttenders
		String maxAttenderString = actDto.getMaxAttenders();
		String minAttenderString = actDto.getMinAttenders();
		//maxAttenders >= 1
		if (StringUtils.isBlank(maxAttenderString)) {
			updateMessageDTO.setStatus(false);
			//20131213 bug GCSS-437
			updateMessageDTO.setMessage("This activity must have at least one attendee. Please try again.");
			return updateMessageDTO;
		}else if(!ActivityAndServiceUtil.positiveNumber(maxAttenderString.trim())){
			updateMessageDTO.setStatus(false);
			//20131213 bug GCSS-437
			updateMessageDTO.setMessage("This activity must have at least one attendee. Please try again.");
			return updateMessageDTO;
		}
		//minAttenders >= 0 or blank then set -1 to database
		if (StringUtils.isBlank(minAttenderString)) {
			minAttenderString = "-1";
		}else{
			try{
				Long.valueOf(minAttenderString);
			}catch(Exception e){
				updateMessageDTO.setStatus(false);
				updateMessageDTO.setMessage("MinAttendees is unavailable. Please try again.");
				return updateMessageDTO;
			}
			if(!ActivityAndServiceUtil.positiveAndZero(minAttenderString.trim())){
				updateMessageDTO.setStatus(false);
				updateMessageDTO.setMessage("MinAttendees is unavailable. Please try again.");
				return updateMessageDTO;
			}
		}
		Long maxAttenders = Long.valueOf(maxAttenderString.trim());
		Long minAttenders = Long.valueOf(minAttenderString.trim());
		if(maxAttenders < minAttenders){
			updateMessageDTO.setStatus(false);
			//GCSS-437  Number of attendees - User enters a smaller number second (to) field
			updateMessageDTO.setMessage("Minimum attendees cannot be larger than maximum number of attendees. Please adjust and try again.");
			return updateMessageDTO;
		}
		activity.setMinimumAttendees(minAttenders);
		activity.setMaximumAttendees(maxAttenders);
		
		//MAXmumDuration and minmumDuration cannot be null
		//maxmunDuration and minmumDration must be number
		//maxmumDuration >= minmumDuration
		String maxmumDurationString = actDto.getMaxmumDuration();
		String minmumDurationString = actDto.getMinimumDuration();
		if (StringUtils.isBlank(maxmumDurationString)) {
			updateMessageDTO.setStatus(false);
			updateMessageDTO.setMessage("MaximumDuration is required!");
			return updateMessageDTO;
		}else if(!ActivityAndServiceUtil.positiveNumber(maxmumDurationString.trim())){
			updateMessageDTO.setStatus(false);
			updateMessageDTO.setMessage("MaximumDuration is unavailable!");
			return updateMessageDTO;
		}
		if (StringUtils.isBlank(minmumDurationString)) {
			updateMessageDTO.setStatus(false);
			//GCSS-437 User does not select a minimum duration
			updateMessageDTO.setMessage("Minimum duration has not been selected, Please select a minimum duration and try again.");
			return updateMessageDTO;
		}else if(!ActivityAndServiceUtil.positiveNumber(minmumDurationString.trim())){
			updateMessageDTO.setStatus(false);
			updateMessageDTO.setMessage("MinimumDuration is unavailable!");
			return updateMessageDTO;
		}
		Long minimumDuration = Long.valueOf(minmumDurationString.trim());
		Long maxmumDuration = Long.valueOf(maxmumDurationString.trim());
		if (maxmumDuration < minimumDuration) {
			updateMessageDTO.setStatus(false);
			updateMessageDTO.setMessage("MaximumDuration cannot be less than minimumDuration!");
			return updateMessageDTO;
		}
		activity.setMaximumDuration(maxmumDuration);
		activity.setMinimumDuration(minimumDuration);
		
		updateMessageDTO.setStatus(true);
		updateMessageDTO.setObject(activity);
		return updateMessageDTO;
	}

	@RequestMapping("centralized/loadActivity.htm")
	@ResponseBody
	public Object loadCentralizedActivity(Long id){
		Activity activity = activityService.getActivityByActivityId(id);
		UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO(null, true);
		if(activity == null){
			updateMessageDTO.setMessage("Activity does not exist or has been deleted!");
			updateMessageDTO.setStatus(false);
			return updateMessageDTO;
		}
		ActivityDTO activityDTO = new ActivityDTO();
		activityDTO.setActivityId(id);
		activityDTO.setActivityName(activity.getActivityName().trim());
		ServiceDTO serviceDTO = new ServiceDTO();
		serviceDTO.setServiceId(activity.getService().getServiceId());
		serviceDTO.setServiceName(activity.getService().getServiceName());
		activityDTO.setServiceDTO(serviceDTO);
		activityDTO.setServiceName(activity.getService().getServiceName());
		activityDTO.setServiceId(activity.getService().getServiceId());
		activityDTO.setMinimunAttendees(activity.getMinimumAttendees() == -1L ? "" : String.valueOf(activity.getMinimumAttendees()));
		activityDTO.setMaximunAttendees(String.valueOf(activity.getMaximumAttendees()));
		activityDTO.setAttenders(activity.getMaximumAttendees() == null ? String.valueOf(activity.getMinimumAttendees()) : activity.getMinimumAttendees() + " to "+activity.getMaximumAttendees());
		activityDTO.setVersion(activity.getVersion());
		String duration = String.valueOf(activity.getMinimumDuration()==null?"":activity.getMinimumDuration());
		String maxduration = String.valueOf(activity.getMaximumDuration()==null?"":activity.getMaximumDuration());
		activityDTO.setMinimumDuration(duration);
		activityDTO.setMaxmumDuration(maxduration);
		activityDTO.setRequiresInstructor(activity.getRequiresInstructor().toString());
		activityDTO.setEnable(Enabled.Y.equals(activity.getEnabled())?true:false);
		return activityDTO;
	}
	

	@RequestMapping("centralized/deleteActivity.htm")
	@ResponseBody
	public UpdateMessageDTO deleteCentralizedActivity(Long id){
		UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO(DELETE_SUCCESS, true);
		
		Map<String, Object> map = serviceAndActivityValidate.activityCanDelete(id);
		
		if(!(Boolean) map.get(VALIDATE_FLAG)){
			updateMessageDTO.setMessage((String)map.get(VALIDATE_MESSAGE));
			updateMessageDTO.setStatus(false);
			return updateMessageDTO;
		}
		
		try{
			//GCSS-656
			serviceAndActivityValidate.deleteActivity(id);
			Activity activity = (Activity) map.get(VALIDATE_OBJECT);
			activityService.deleteCentralizeActivity(activity);
		}catch(Exception e){
			LOGGER.info(e.getMessage());
			updateMessageDTO.setMessage(e.getMessage());
			updateMessageDTO.setStatus(false);
		}
		return updateMessageDTO;
	}
	
	

	
	@RequestMapping("centralized/updateActivity.htm")
	@ResponseBody
	public UpdateMessageDTO updateCentralizedActivity(@RequestBody ActivityDTO activityDetailDTO,@ModelAttribute(PERSON_STRING) Person person,@ModelAttribute(SITE_ID_SESSION_KEY)Long siteId){
		UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO(UPDATE_SUCCESS, true);
		Activity activity = activityService.getActivityByActivityId(activityDetailDTO.getActivityId());
		
		updateMessageDTO = checkActivityUpdate(activityDetailDTO, updateMessageDTO, activity,siteId);
		
		if(!updateMessageDTO.getStatus()){
			return updateMessageDTO;
		}
		
		activity = (Activity) updateMessageDTO.getObject();
		String requireInstructor = activityDetailDTO.getRequiresInstructor().trim();
		activity.setRequiresInstructor(RequiresInstructor.N.toString().equals(requireInstructor)?RequiresInstructor.N:(RequiresInstructor.O.toString().equals(requireInstructor)?RequiresInstructor.O:RequiresInstructor.R));
		activity.setEnabled(activityDetailDTO.getEnable()?Enabled.Y:Enabled.N);
		Service service = serviceService.getServiceById(activityDetailDTO.getServiceDTO().getServiceId());
		activity.setService(service);
		
		try{
			activityService.updateActivity(siteId,activity, person,activityDetailDTO.getGlobalChange());
			activityDetailDTO.setRequiresInstructor(SystemUtil.getRequiresInscructor(activity.getRequiresInstructor()));
			activityDetailDTO.setVersion(activity.getVersion());
			activityDetailDTO.setMinimumDuration(ActivityAndServiceUtil.getDuration(activity.getMinimumDuration()));
			activityDetailDTO.setMaxmumDuration(ActivityAndServiceUtil.getDuration(activity.getMaximumDuration()));
			activityDetailDTO.setAttenders((StringUtils.isBlank(activityDetailDTO.getMinimunAttendees())? " &nbsp;&nbsp; " : activityDetailDTO.getMinimunAttendees()) + (StringUtils.isNotBlank(activityDetailDTO.getMaximunAttendees())  ? " to "+activityDetailDTO.getMaximunAttendees() : ""));
			activityDetailDTO.setEnable(Enabled.Y.equals(activity.getEnabled())?true:false);
			activityDetailDTO.setServiceName(activity.getService().getServiceName());
			updateMessageDTO.setObject(activityDetailDTO);
		}catch(Exception e){
			LOGGER.info(e.getMessage());
			updateMessageDTO.setMessage(e.getMessage());
			updateMessageDTO.setStatus(false);
		}
		return updateMessageDTO;
	}

	private UpdateMessageDTO checkActivityUpdate(ActivityDTO activityDetailDTO,
			UpdateMessageDTO updateMessage, Activity activity,Long siteId) {
		
		UpdateMessageDTO updateMessageDTO = updateMessage;
		
		if(activity == null){
			updateMessageDTO.setMessage("Activity does not exit,or has been deleted!");
			updateMessageDTO.setStatus(false);
			return updateMessageDTO;
		}
		
		if(activity.getVersion() != activityDetailDTO.getVersion()){
			updateMessageDTO.setMessage("Activity has been updated!");
			updateMessageDTO.setStatus(false);
			return updateMessageDTO;
		}
		//checkActivitySave(activityDetailDTO, activity);
		/**save check  name has been used not same
		 * 
		 * check name required,min-max attendees,min-max duration
		 * 
		 * set name,attendees,duration to activity
		 * */
		if (StringUtils.isBlank(activityDetailDTO.getActivityName())) {
			updateMessageDTO.setStatus(false);
			updateMessageDTO.setMessage("Activity name is required!");
			return updateMessageDTO;
		}
		
		if (activityDetailDTO.getServiceId() == null) {
			updateMessageDTO.setStatus(false);
			updateMessageDTO.setMessage("Service is required!");
			return updateMessageDTO;
		}
		
		//maxAttenders >= 1 minAttenders >=0 or be null 
		//maxAttenders and minAttenders must be number
		//maxAttenders >= minAttenders
		String maxAttenderString = activityDetailDTO.getMaximunAttendees();
		String minAttenderString = activityDetailDTO.getMinimunAttendees();
		//maxAttenders >= 1
		if (StringUtils.isBlank(maxAttenderString)) {
			updateMessageDTO.setStatus(false);
			//20131213 bug GCSS-437
			updateMessageDTO.setMessage("This activity must have at least one attendee. Please try again.");
			return updateMessageDTO;
		}else if(!ActivityAndServiceUtil.positiveNumber(maxAttenderString.trim())){
			updateMessageDTO.setStatus(false);
			//20131213 bug GCSS-437
			updateMessageDTO.setMessage("This activity must have at least one attendee. Please try again.");
			return updateMessageDTO;
		}
		//minAttenders >= 0 or blank then set -1 to database
		if (StringUtils.isBlank(minAttenderString)) {
			minAttenderString = "-1";
		}else{
			try {
				Long.valueOf(minAttenderString.trim());
			} catch (Exception e) {
				updateMessageDTO.setStatus(false);
				//GCSS-437  User does not enter a value in Number of Attendees 
				updateMessageDTO.setMessage("MinAttendees is unavailable. Please try again.");
				return updateMessageDTO;
			}
			
			if(!ActivityAndServiceUtil.positiveAndZero(minAttenderString.trim())){
				updateMessageDTO.setStatus(false);
				updateMessageDTO.setMessage("MinAttendees is unavailable. Please try again.");
				return updateMessageDTO;
			}
		}
		Long maxAttenders = Long.valueOf(maxAttenderString.trim());
		Long minAttenders = Long.valueOf(minAttenderString.trim());
		if(maxAttenders < minAttenders){
			updateMessageDTO.setStatus(false);
			//GCSS-437  Number of attendees - User enters a smaller number second (to) field
			updateMessageDTO.setMessage("Minimum attendees cannot be larger than maximum number of attendees. Please adjust and try again.");
			return updateMessageDTO;
		}
		activity.setMinimumAttendees(minAttenders);
		activity.setMaximumAttendees(maxAttenders);
		
		//MAXmumDuration and minmumDuration cannot be null
		//maxmunDuration and minmumDration must be number
		//maxmumDuration >= minmumDuration
		String maxmumDurationString = activityDetailDTO.getMaxmumDuration();
		String minmumDurationString = activityDetailDTO.getMinimumDuration();
		if (StringUtils.isBlank(maxmumDurationString)) {
			updateMessageDTO.setStatus(false);
			updateMessageDTO.setMessage("MaximumDuration is required!");
			return updateMessageDTO;
		}else if(!ActivityAndServiceUtil.positiveNumber(maxmumDurationString.trim())){
			updateMessageDTO.setStatus(false);
			updateMessageDTO.setMessage("MaximumDuration is unavailable!");
			return updateMessageDTO;
		}
		if (StringUtils.isBlank(minmumDurationString)) {
			updateMessageDTO.setStatus(false);
			updateMessageDTO.setMessage("MinimumDuration is required!");
			return updateMessageDTO;
		}else if(!ActivityAndServiceUtil.positiveNumber(minmumDurationString.trim())){
			updateMessageDTO.setStatus(false);
			updateMessageDTO.setMessage("MinimumDuration is unavailable!");
			return updateMessageDTO;
		}
		Long minimumDuration = Long.valueOf(minmumDurationString.trim());
		Long maxmumDuration = Long.valueOf(maxmumDurationString.trim());
		if (maxmumDuration < minimumDuration) {
			updateMessageDTO.setStatus(false);
			updateMessageDTO.setMessage("MaximumDuration cannot be less than minimumDuration!");
			return updateMessageDTO;
		}
		activity.setMaximumDuration(maxmumDuration);
		activity.setMinimumDuration(minimumDuration);
		/**save check*/
		
		
		if(!SystemUtil.createName(activityDetailDTO.getActivityName()).equals(activity.getActivityName())){
			Boolean resultBoolean2  = activityService.hasSameName(SystemUtil.createName(activityDetailDTO.getActivityName()));
			if(resultBoolean2){
				updateMessageDTO.setMessage("Activity name has been used!");
				updateMessageDTO.setStatus(false);
				return updateMessageDTO;
			}
		}
		activity.setActivityName(SystemUtil.createName(activityDetailDTO.getActivityName().trim()));
		//if update-activity is disabled || requiresInstructor changed check if activity can disable
		if((!activityDetailDTO.getEnable()) || !activityDetailDTO.getRequiresInstructor().trim().equals(activity.getRequiresInstructor().toString().trim())){
			updateMessageDTO = checkActivityCanDisable(activityDetailDTO,activity, siteId);
			if(!updateMessageDTO.getStatus()){
				return updateMessageDTO;
			}
		}
		if(activityDetailDTO.getEnable()){
			updateMessageDTO = checkActivityCanEnable(activityDetailDTO,activity,siteId);
			if(!updateMessageDTO.getStatus()){
				return updateMessageDTO;
			}
		}
		
		updateMessageDTO.setStatus(true);
		updateMessageDTO.setObject(activity);
		return updateMessageDTO;
	}

	private UpdateMessageDTO checkActivityCanDisable(ActivityDTO activityDetailDTO,Activity activity,Long siteId){
		String message = "";
		Boolean result = false;
		UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO(null, true);
		//requireInstructor changed,check appointment
		//global disable activity( to disable),check appointment
		if(!activityDetailDTO.getRequiresInstructor().trim().equals(activity.getRequiresInstructor().toString().trim()) || activityDetailDTO.getGlobalChange()){
			result = serviceAndActivityValidate.activityAssignToAppoint(siteId, activity.getActivityId());
		}
		//can not update have two messages
		if(result){
			if(!activityDetailDTO.getRequiresInstructor().trim().equals(activity.getRequiresInstructor().toString().trim())){
				message = ",default require instructor cannot be change!";
			}else{
				message = ",activity cannot be disabled or deleted!";
			}
			updateMessageDTO.setMessage("Activity has been assigned to appointment"+message);
			updateMessageDTO.setStatus(false);
		}
		return updateMessageDTO;
	}
	
	private UpdateMessageDTO checkActivityCanEnable(ActivityDTO activityDetailDTO,Activity activity,Long siteId){
		UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO(null, true);
		//activit check service is enable
		//if service is disable,activity cannot enable
		if(Enabled.N.equals(activity.getService().getEnabled())){
			updateMessageDTO.setStatus(false);
			updateMessageDTO.setMessage("Activity belongs to service is disabled,it cannot be enable!");
			return updateMessageDTO;
		}
		//if global change && activity from disable to enable
		//check profileservice is enable
		if(activityDetailDTO.getGlobalChange()){
			Boolean result = serviceAndActivityValidate.activityHasDisabledProService(activity.getActivityId(), siteId);
			if(result){
				updateMessageDTO.setStatus(false);
				updateMessageDTO.setMessage("ProfileActivity belongs ProfileService is disabled,it cannot be enable!");
				return updateMessageDTO;
			}
		}
		return updateMessageDTO;
	}
}
