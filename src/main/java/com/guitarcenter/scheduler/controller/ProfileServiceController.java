package com.guitarcenter.scheduler.controller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.SessionAttributes;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.MessageConstants;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.dto.ProfileServiceCreateDTO;
import com.guitarcenter.scheduler.dto.ProfileServiceDTO;
import com.guitarcenter.scheduler.dto.ServiceDTO;
import com.guitarcenter.scheduler.dto.UpdateMessageDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileActivity;
import com.guitarcenter.scheduler.model.ProfileService;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.RequiresInstructor;
import com.guitarcenter.scheduler.service.ActivityService;
import com.guitarcenter.scheduler.service.LocationProfileService;
import com.guitarcenter.scheduler.service.ServiceService;
import com.guitarcenter.scheduler.service.SiteService;
import com.guitarcenter.scheduler.service.impl.ServiceAndActivityValidate;

@SessionAttributes({ AppConstants.PERSON_SESSION_KEY, AppConstants.LOCATION_PROFILE_ID_SESSION_KEY, AppConstants.SITE_ID_SESSION_KEY,AppConstants.LOCATION_STRING })
@Controller
public class ProfileServiceController implements AppConstants{

	private static final Logger		LOGGER		= LoggerFactory.getLogger(ProfileServiceController.class);
	private static final String     COMMA_SPACE  = ", ";
	private static final String		STATUS_KEY	= "status";
	private static final String		MESSAGE_KEY	= "message";
	private static final String VALIDATE_FLAG = "flag";
	private static final String VALIDATE_MESSAGE = "message";
	private static final String VALIDATE_OBJECT = "object";
	
	@Autowired
	private LocationProfileService	locationProfileService;

	@Autowired
	@Qualifier("serviceService")
	private ServiceService			serviceService;
	
	@Autowired
	private ActivityService			activityService;
	
	@Autowired
	@Qualifier("siteService")
	private SiteService			siteService;

	@Autowired
	@Qualifier("serviceAndActivityValidate")
	private ServiceAndActivityValidate serviceAndActivityValidate;

	// profile service redirction
	@RequestMapping("proServicePage.htm")
	public String proServiceActivity() {
		return "service_activity";
	}

	@RequestMapping("profileService/loadProfileServiceList.htm")
	@ResponseBody
	public List<ProfileServiceDTO> loadProfileServiceList(@ModelAttribute(LOCATION_PROFILE_ID_SESSION_KEY)Long profileId) {
		List<ProfileServiceDTO> list = new ArrayList<ProfileServiceDTO>();
		try {
			Set<ProfileService> serviceSet = new HashSet<ProfileService>(locationProfileService.getRealProfileServiceList(profileId));

			Iterator<ProfileService> it = serviceSet.iterator();

			while (it.hasNext()) {
				ProfileService service = it.next();
				ProfileServiceDTO dto = new ProfileServiceDTO();
				dto.setServiceId(service.getProfileServiceId());
				dto.setServiceName(service.getService().getServiceName());
				String avaibleAct = getActivityName(service.getService().getServiceId(), activityService.loadProfileActivityByProfileId(profileId));
				dto.setAvaibleAct(avaibleAct);
				dto.setEnable(Enabled.N.equals(service.getEnabled()) ? false : true);
				list.add(dto);
			}
			Collections.sort(list);
		} catch (Exception e) {
			LOGGER.error(e + "Get profileServiceDTO list Failed!");
		}
		return list;
	}

	@RequestMapping("profileService/addProfileService/loadAddedServiceList.htm")
	@ResponseBody
	public Set<ServiceDTO> loadAddedServiceList(@ModelAttribute(LOCATION_PROFILE_ID_SESSION_KEY)
	Long profileId) {
		long siteId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY,
				RequestAttributes.SCOPE_SESSION);
		Set<ServiceDTO> dtoList = new TreeSet<ServiceDTO>();

		List<Service> sitelist = serviceService.findServiceBySite(siteId);

		Set<Service> profileSet = new HashSet<Service>(locationProfileService.getProfileServiceList(profileId));
		boolean tempFlag = false;

		for (Service service : sitelist) {

			if (profileSet != null && !profileSet.isEmpty()) {

				for (Service proService : profileSet) {
					if (service.getServiceId().equals(proService.getServiceId())) {
						tempFlag = true;
					}
				}
			}
			if (!tempFlag && Enabled.Y.equals(service.getEnabled())) {
				ServiceDTO dto = new ServiceDTO();
				dto.setServiceId(service.getServiceId());
				dto.setServiceName(service.getServiceName());
				dto.setEnable(Enabled.Y.equals(service.getEnabled()) ? true : false);
				dto.setInstructor(SystemUtil.getRequiresInscructor(service.getRequiresInstructor()));
				dtoList.add(dto);
			}

			tempFlag = false;
		}

		return dtoList;
	}

	
	@RequestMapping("profileService/addProfileService/loadActivitiesInServiceList.htm")
	@ResponseBody
	public List<ActivityDTO> loadActivitiesInServiceList(Long serviceId) {
		List<Activity> list = activityService.loadActivitiesByService(serviceId);

		List<ActivityDTO> alist = new LinkedList<ActivityDTO>();

		for (Activity activity : list) {
			if(!Enabled.Y.equals(activity.getEnabled())){
				continue;
			}
			ActivityDTO act = new ActivityDTO();
			act.setActivityId(activity.getActivityId());
			act.setActivityName(activity.getActivityName());
			alist.add(act);
		}
		return alist;
	}
	
	//when create service chose activity by serviceId
	@RequestMapping("profileService/addProfileService/addSelectedActivity.htm")
	@ResponseBody
	public Map<String, Object> addSelectedActivity(String activityString, Long serviceId) {
		Map<String, Object> map = new HashMap<String, Object>();
		if (serviceId != null) {
			List<ActivityDTO> sList = new ArrayList<ActivityDTO>();
			List<Activity> activityList = activityService.loadActivitiesByService(serviceId);
				List<Long> activityId = SystemUtil.getIdList(activityString);
				for (Activity s : activityList) {
					if(!Enabled.Y.equals(s.getEnabled())){
						continue;
					}
					if (!activityId.contains(s.getActivityId())) {
						ActivityDTO dto = new ActivityDTO();
						dto.setActivityId(s.getActivityId());
						dto.setActivityName(s.getActivityName());
						sList.add(dto);
						map.put("activityList", sList);
						map.put(STATUS_KEY, true);
						map.put(MESSAGE_KEY, "Load activityList successfully!!");
					}
				}
		} else {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Please select service first!!");
			map.put("activityList", null);
		}

		return map;
	}


	@RequestMapping("profileService/addProfileService/saveProService.htm")
	@ResponseBody
	public Map<String, Object> createProServiceActivity(@RequestBody
	ProfileServiceCreateDTO pscDto,@ModelAttribute(PERSON_SESSION_KEY)Person person,@ModelAttribute(LOCATION_PROFILE_ID_SESSION_KEY)Long profileId ,@ModelAttribute(SITE_ID_SESSION_KEY)Long siteId) {

		Site site = new Site();
		site.setSiteId(siteId);
		Map<String, Object> map = new HashMap<String, Object>();
		
		try {
			map = checkServiceActivity(pscDto);
			map = locationProfileService.saveServiceAndActivity(pscDto, profileId, site, person, map);
		} catch (Exception e) {
			LOGGER.info(e.getMessage());
			map.put("status", false);
			map.put(MESSAGE_KEY, "Save Profile Info failed!");
		}
		return map;
	}
	
	//GCSS-657
	@RequestMapping("profileService/deleteProfileService.htm")
	@ResponseBody
	public UpdateMessageDTO deleteProfileService(Long serviceId,  @ModelAttribute(PERSON_SESSION_KEY) Person person){
		UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO("Delete success!", true);

		Map<String, Object> map = serviceAndActivityValidate.profileServiceCanDelete(serviceId);
		Boolean result = (Boolean) map.get(VALIDATE_FLAG);
		if (!result) {
			updateMessageDTO.setMessage((String) map.get(VALIDATE_MESSAGE));
			updateMessageDTO.setStatus(false);
			return updateMessageDTO;
		}

		ProfileService profileService = (ProfileService) map.get(VALIDATE_OBJECT);
		try {
			serviceAndActivityValidate.deleteProfileService(profileService.getLocationProfile().getProfileId(), profileService.getService().getServiceId());
		} catch (Exception e) {
			LOGGER.info(e.getMessage());
			updateMessageDTO.setMessage(e.getMessage());
			updateMessageDTO.setStatus(false);
		}
		return updateMessageDTO;
	}

	

	@RequestMapping("profileService/loadProfileServiceDetail.htm")
	@ResponseBody
	public ServiceDTO loadProfileServiceDetail(Long id, @ModelAttribute(SITE_ID_SESSION_KEY)
	Long siteId, @ModelAttribute(LOCATION_PROFILE_ID_SESSION_KEY)
	Long profileId) {
		ServiceDTO serviceDTO = new ServiceDTO();
		ProfileService profileService = serviceService.getProfileService(id);

		List<ActivityDTO> activityDTOs = new ArrayList<ActivityDTO>();
		List<ActivityDTO> notSelectedDTOs = new ArrayList<ActivityDTO>();

		List<Activity> notSelectedActivities = activityService.loadActivitiesByService(profileService.getService().getServiceId());
		List<Activity> activitiesInProfile = activityService.loadActivityByProfileId(profileId).get("result");
		final List<Activity> non_activitiesInProfile = activityService.loadActivityByProfileId(profileId).get("non_result");
		
		
		for (Activity activity : notSelectedActivities) {

			ActivityDTO activityDTO = new ActivityDTO(activity.getActivityId(), activity.getActivityName());
			if (activitiesInProfile.contains(activity)) {
				activityDTOs.add(activityDTO);
				continue;
			}
			
			if(!activity.getEnabled().equals(Enabled.Y)){
				continue;
			}
			if(non_activitiesInProfile.contains(activity)){
				continue;
			}
			notSelectedDTOs.add(activityDTO);
		}

		serviceDTO.setActivityDTOs(activityDTOs);
		serviceDTO.setNotSelecteDtos(notSelectedDTOs);
		serviceDTO.setEnable(Enabled.Y.equals(profileService.getEnabled()) ? true : false);
		serviceDTO.setServiceId(id);
		serviceDTO.setServiceName(profileService.getService().getServiceName());
		serviceDTO.setVersion(profileService.getVersion());
		serviceDTO.setInstructor(profileService.getService().getRequiresInstructor().toString());
		if (RequiresInstructor.R.equals(profileService.getService().getRequiresInstructor())) {
			serviceDTO.setInstructor("Y");
		}
		return serviceDTO;
	}


	@RequestMapping("profileService/updatepProfileService.htm")
	@ResponseBody
	public UpdateMessageDTO updatepProfileService(@RequestBody ServiceDTO serviceDTO,@ModelAttribute(PERSON_SESSION_KEY) Person person,@ModelAttribute(LOCATION_PROFILE_ID_SESSION_KEY)Long profileId ,@ModelAttribute(SITE_ID_SESSION_KEY) Long siteId,@ModelAttribute(LOCATION_STRING) Location location){
	
		UpdateMessageDTO updateMessageDTO = new UpdateMessageDTO(UPDATE_SUCCESS, true);
		Site site = siteService.getSiteById(siteId);
		Long id = serviceDTO.getServiceId();
		ProfileService profileService = serviceService.getProfileService(id);
		Enabled enable = profileService.getEnabled();
		
		updateMessageDTO = checkUpdateProfileServie(serviceDTO, profileId, updateMessageDTO,
				profileService, enable);
		if(!updateMessageDTO.getStatus()){
			return updateMessageDTO;
		}
		
		profileService.setVersion(serviceDTO.getVersion());
		profileService.setEnabled(serviceDTO.getEnable() == true ? Enabled.Y : Enabled.N);
		
		List<ActivityDTO> activityDTOs = serviceDTO.getActivityDTOs();
		List<Long> activityIds = new ArrayList<Long>();
		
		for (ActivityDTO activityDTO : activityDTOs) {
			activityIds.add(activityDTO.getActivityId());
		}
		
		
		try{
			serviceService.updateService(profileService,person.getPersonId(),activityIds,profileId,site,location,enable);
		}catch (Exception e) {
			updateMessageDTO.setMessage(e.getMessage());
			updateMessageDTO.setStatus(false);
			LOGGER.info(e.getMessage());
			return updateMessageDTO;
		}
		
		StringBuilder sbBuilder = new StringBuilder();
		List<Activity> notSelectedActivities = activityService.loadActivitiesByService(id);
		for (Activity activity : notSelectedActivities) {

			List<Activity> activitiesInProfile = activityService.loadActivityByProfileId(profileId).get("result");
			if (activitiesInProfile.contains(activity) && activity.getEnabled().equals(Enabled.Y)) {
				sbBuilder.append(activity.getActivityName()).append(COMMA_SPACE);
			}
		}
		
		serviceDTO.setVersion(profileService.getVersion());
		serviceDTO.setAvaibleAct(sbBuilder.length()>0?sbBuilder.substring(0, sbBuilder.length()-COMMA_SPACE.length()):"");
		updateMessageDTO.setObject(serviceDTO);
		return updateMessageDTO;
	}

	//from Y to N check appointment
	private UpdateMessageDTO checkUpdateProfileServie(ServiceDTO serviceDTO,
			Long profileId, UpdateMessageDTO updateMessageDTO,
			ProfileService profileService, Enabled enable) {
		if(!serviceDTO.getEnable() && Enabled.Y.equals(enable)){
			Boolean result = serviceAndActivityValidate.profileServiceCanDisable(profileService.getService().getServiceId(), profileId);
			if(!result){
				updateMessageDTO.setMessage("profileActivity has been assigned to appoint,this profileService cannot be disabled!");
				updateMessageDTO.setStatus(false);
				return updateMessageDTO;
			}
		}
		updateMessageDTO.setStatus(true);
		return updateMessageDTO;
	}

	
	private Map<String, Object> checkServiceActivity(ProfileServiceCreateDTO pscDto) {
		Map<String, Object> map = new HashMap<String, Object>();
		boolean status = true;
		if (StringUtils.isBlank(pscDto.getServiceType().toString())) {
			status = false;
			map.put(MESSAGE_KEY, MessageConstants.VALIDATION_SERVICE_ACTIVITY);
		}
		map.put("status", status);
		return map;
	}
	
	public String getActivityName(long serviceId,List<ProfileActivity> activitySet) {
		List<Activity> list = activityService.loadActivitiesByService(serviceId);
		StringBuffer sb = new StringBuffer();
		String temp = "";
		for (ProfileActivity setActivity : activitySet) {
			for (Activity activity : list) {
				if (activity.getActivityId().toString().equals(setActivity.getActivity().getActivityId().toString()) 
						&& Enabled.Y.toString().equals(setActivity.getEnabled().toString())) {
					sb.append(activity.getActivityName()).append(COMMA_SPACE);
				}
			}
		}

		if (sb.length() != 0) {
			temp = sb.substring(0, sb.length() - COMMA_SPACE.length());
		}
		return temp;
	}
}
