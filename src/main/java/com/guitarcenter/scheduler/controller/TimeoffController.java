/**
 * @Title: TimeoffController.java
 * @Package com.guitarcenter.scheduler.controller
 * @Description: 
 * Copyright: Copyright (c) 2014 
 * Company:
 * 
 * <AUTHOR>
 * @date Mar 14, 2014 4:53:10 PM
 * @version V1.0
 */
package com.guitarcenter.scheduler.controller;

import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_DATE_TIME_ILLEGAL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_END_DATE_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_END_TIME_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_START_DATE_ILLEGAL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_START_DATE_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_START_TIME_NULL;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_TIMEOFF;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_TIMEOFF_ONETIME;
import static com.guitarcenter.scheduler.common.util.MessageConstants.VALIDATION_VALID_TIME_FORMAT;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import com.guitarcenter.scheduler.common.util.CalendarUtil;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.TimeoffUtil;
import com.guitarcenter.scheduler.dto.ProfileTimeOffDTO;
import com.guitarcenter.scheduler.dto.TimeOffDTO;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileTimeoff;
import com.guitarcenter.scheduler.model.Timeoff;
import com.guitarcenter.scheduler.service.AvailabilityService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.ProfileTimeOffService;
import com.guitarcenter.scheduler.service.TimeoffService;
import com.guitarcenter.scheduler.service.ValidationService;

/**
 * @ClassName: TimeoffController
 * @Description:
 * <AUTHOR>
 * @date Mar 14, 2014 4:53:10 PM
 * 
 */
@Controller
public class TimeoffController {

	private static final Logger LOG = LoggerFactory
			.getLogger(TimeoffController.class);

	private static final String MESSAGE_KEY = "message";
	private static final String STATUS_KEY = "status";
	private static final String TIMEOFF_KEY = "timeoffs";
	private static final String RETURN_DTO = "dto";
	private static final String PROFILE_TIMEOFF_KEY = "profileTimeoffs";
	//GSSP-334 Changes.
	public static final String LOCATION_PROFILE_ID_SESSION_KEY	= "locationProfileId";
	public static final String SITE_ID_SESSION_KEY = "siteId";
	

	@Autowired
	private TimeoffService timeoffService;

	@Autowired
	private ValidationService validationService;

	@Autowired
	private InstructorService instructorService;
	
	@Autowired
	ProfileTimeOffService profileTimeOffService;
	
	@Resource
	private AvailabilityService availabilityService;

	/**
	 * Update timeoff object
	 * 
	 * @param dto
	 * @return
	 * @throws RuntimeException
	 */
	@RequestMapping("/timeoff/updateTimeoff.htm")
	@ResponseBody
	public Map<String, Object> updateTimeoff(@RequestBody TimeOffDTO dto)
			throws RuntimeException {
		if (LOG.isDebugEnabled()) {
			LOG.debug("TimeoffController.updateTimeoff: start");
		}
		Map<String, Object> map = new HashMap<String, Object>();

		Person person = (Person) RequestContextHolder
				.currentRequestAttributes().getAttribute("person",
						RequestAttributes.SCOPE_SESSION);
		try {
			map = this.checkTimeoff(dto);
			if ((Boolean) map.get(STATUS_KEY)) {
				Timeoff timeoff = getTimeoff(dto, person);
				timeoffService.saveTimeoff(timeoff);
				List<Timeoff> list = timeoffService
						.getDisplayTimeoffByInstructorId(dto.getInstructorId());
				List<TimeOffDTO> timeoffDtos = new ArrayList<TimeOffDTO>();
				for (Timeoff t : list) {
					TimeOffDTO timeoffDto = TimeoffUtil.timeOffDateToString(t);
					timeoffDtos.add(timeoffDto);
				}
				map.put(RETURN_DTO, timeoffDtos);
				map.put(STATUS_KEY, true);
				map.put(MESSAGE_KEY, "Create Time Off Success!");
			}
		} catch (Exception e) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Create Time Off Failed");
			LOG.error(
					"Caught an exception from TimeoffController.createTimeoff: {}",
					e);
			return map;
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("TimeoffController.updateTimeoff: end");
		}
		return map;
	}

	/**
	 * Helper method to build timeoff object from TimeoffDTO
	 * @param dto
	 * @param person
	 * @return
	 * @throws Exception
	 */
	private Timeoff getTimeoff(TimeOffDTO dto, Person person) throws Exception {
		Timeoff timeoff = new Timeoff();
		Long instrctorId = dto.getInstructorId();
		Instructor instructor = instructorService.getInstructor(instrctorId);
		timeoff.setInstructor(instructor);

		String startDate = dto.getFromDate();
		String endDate = dto.getToDate();
		String startTime = dto.getFromTime();
		String endTime = dto.getToTime();
		Date start = null;
		Date end = null;
		try {
			start = new SimpleDateFormat(DateTimeUtil.DATETIME_PATTERN_SLASH)
					.parse(startDate + " " + startTime);
			end = new SimpleDateFormat(DateTimeUtil.DATETIME_PATTERN_SLASH)
					.parse(endDate + " " + endTime);
		} catch (ParseException e) {
			LOG.error("TimeoffController.getTimeoff: parse date error");
			throw new Exception("Date parse error");
		}
		timeoff.setStartTime(start);
		timeoff.setEndTime(end);
		timeoff.setUpdated(new Date());
		timeoff.setUpdatedBy(person);
		return timeoff;
	}

	/**
	 * checkTimeoff
	 * 
	 * 
	 * @Title: checkTimeoff
	 * @Description:
	 * @param @param dto
	 * @param @return
	 * @return Map<String,Object>
	 * @throws
	 */
	private Map<String, Object> checkTimeoff(TimeOffDTO dto) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put(STATUS_KEY, true);
		String startDate = dto.getFromDate();
		String startTime = dto.getFromTime();
		String endDate = dto.getToDate();
		String endTime = dto.getToTime();
		if (startDate == null || "".equals(startDate.trim())) {
			map.put(MESSAGE_KEY, VALIDATION_START_DATE_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		if (startTime == null || "".equals(startTime.trim())) {
			map.put(MESSAGE_KEY, VALIDATION_START_TIME_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		if (endDate == null || "".equals(endDate.trim())) {
			map.put(MESSAGE_KEY, VALIDATION_END_DATE_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		if (endTime == null || "".equals(endTime.trim())) {
			map.put(MESSAGE_KEY, VALIDATION_END_TIME_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		if (!CalendarUtil.checkTimeFormat(startDate, startTime)
				|| !CalendarUtil.checkTimeFormat(endDate, endTime)) {
			map.put(MESSAGE_KEY, VALIDATION_VALID_TIME_FORMAT);
			map.put(STATUS_KEY, false);
			return map;
		}
		if (!CalendarUtil.checkDate(startDate, startTime, endDate, endTime)) {
			map.put(MESSAGE_KEY, VALIDATION_DATE_TIME_ILLEGAL);
			map.put(STATUS_KEY, false);
			return map;
		}
		if (!validationService.checkStartTime(startDate, startTime)) {
			map.put(MESSAGE_KEY, VALIDATION_START_DATE_ILLEGAL);
			map.put(STATUS_KEY, false);
			return map;
		}
		if (!validationService.checkInstructorByAppointmentTime(dto.getInstructorId(), startDate, endDate, CalendarUtil.plusFormatTime(startTime, 1), CalendarUtil.plusFormatTime(endTime, -1))) {
			map.put(MESSAGE_KEY, VALIDATION_TIMEOFF);
			map.put(STATUS_KEY, false);
			return map;
		}
		//GCSS-650
		if(!validationService.checkTimeoffOnetime(startDate, CalendarUtil.plusFormatTime(startTime, 1),endDate, CalendarUtil.plusFormatTime(endTime, -1), dto.getInstructorId())){
			map.put(MESSAGE_KEY, VALIDATION_TIMEOFF_ONETIME);
			map.put(STATUS_KEY, false);
			return map;
		}
		return map;
	}

	//**Raj
	/**
	 * Load timeoff list by instructor
	 * 
	 * @param instructorId
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/timeoff/loadtimeoffs.htm")
	public Map<String, Object> getTimeOffDtos(long instructorId) {
		Map<String, Object> map = new HashMap<String, Object>();
		List<Timeoff> timeoffs = timeoffService.getDisplayTimeoffByInstructorId(instructorId);
		List<TimeOffDTO> timeoffDtos = new ArrayList<TimeOffDTO>();
		for (Timeoff dto : timeoffs) {
			TimeOffDTO timeoffDto = TimeoffUtil.timeOffDateToString(dto);
			timeoffDtos.add(timeoffDto);
		}
 
		map.put(TIMEOFF_KEY, timeoffDtos);
		return map;
	}

	/**
	 * Delete timeoff of a instructor
	 * 
	 * @param timeoffId
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/timeoff/deletetimeoff.htm")
	public Map<String, Object> deleteTimeOffById(long timeoffId) {
		
		Map<String, Object> map = new HashMap<String, Object>();
		long instructorId;
		try {
			Timeoff timeoff = timeoffService.getTimeoffByTimeoffId(timeoffId);
			instructorId = timeoff.getInstructor().getInstructorId();
			this.timeoffService.deleteTimeoff(timeoff);
			map.put(STATUS_KEY, true);
		} catch (Exception e) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Delete Time Off Failed");
			LOG.error("Caught an exception from TimeoffController.DelteTimeoff: {}", e);
			return map;
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("TimeoffController.updateTimeoff: end");
		}
		/**
		 * XXX,use putAll to avoid covering the origin map
		 */
		map.putAll(getTimeOffDtos(instructorId));
		return map;
	}
	
	//--------------------------GSSP-334----------------------------------------------------------------------------------
	
	/**
	 * Load timeoff list by instructor
	 * 
	 * @param instructorId
	 * @return
	 */
	
	@ResponseBody
	@RequestMapping(value = "/profileTimeoff/loadProfileTimeoffs.htm")
	public Map<String, Object> getProfileTimeOffDtos(long siteID) {

		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
		Map<String, Object> map = new HashMap<String, Object>();
		List<ProfileTimeoff> profileTimeoffs = profileTimeOffService.getDisplayProfileTimeoffByProfileId(profileId);
		List<ProfileTimeOffDTO> profileTimeOffDTO = new ArrayList<ProfileTimeOffDTO>();
		profileTimeoffs.forEach(item->{
			ProfileTimeOffDTO timeoffDto = TimeoffUtil.profileTimeOffDateToString(item);
			profileTimeOffDTO.add(timeoffDto);
		});

		map.put(PROFILE_TIMEOFF_KEY, profileTimeOffDTO);
		return map;
	}
	
	

	/**
	 * Delete timeoff of a instructor
	 * 
	 * @param timeoffId
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/profileTimeoff/deleteProfileTimeoff.htm")
	public Map<String, Object> deleteProfileTimeOffById(long profileTimeoffId) {
		Map<String, Object> map = new HashMap<String, Object>();
		long profileId = 0L;
		try {
			ProfileTimeoff profileTimeoff = profileTimeOffService.getProfileTimeoffByProfileTimeoffId(profileTimeoffId);
			profileId = profileTimeoff.getProfileId();
			this.profileTimeOffService.deleteProfileTimeoff(profileTimeoff);
			map.put(STATUS_KEY, true);
		} catch (Exception e) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Delete Time Off Failed");
			LOG.error("Caught an exception from Profile TimeoffController.DeleteTimeoff: {}", e);
			return map;
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("Profile TimeoffController.updateTimeoff: end");
		}
		 map.putAll(getProfileTimeOffDtos(profileId)); 
		 return map;
	}
	
	/**
	 * Update timeoff object
	 * 
	 * @param dto
	 * @return
	 * @throws RuntimeException
	 */
	@RequestMapping("/profileTimeoff/updateProfileTimeoff.htm")
	@ResponseBody
	public Map<String, Object> updateProfileTimeoff(@RequestBody ProfileTimeOffDTO dto)throws RuntimeException {
		if (LOG.isDebugEnabled()) {
			LOG.debug("TimeoffController.updateTimeoff: start");
		}
		Map<String, Object> map = new HashMap<String, Object>();
		map = this.checkProfileTimeoff(dto);
		if (!(Boolean) map.get(STATUS_KEY)) {
			return map;
		}
		try {
		long profileId = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(LOCATION_PROFILE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
 
			if(profileTimeOffService.getAppointmentTimeForProfile(dto,profileId))
			{
				map.put(MESSAGE_KEY, "Profile time off can't be set due to existing appointments");
				map.put(STATUS_KEY, false);
			
		    return map;	
			}

		dto.setProfileId(profileId);
		dto.setVersion(1L);
 
			if ((Boolean) map.get(STATUS_KEY)) {
				ProfileTimeoff profileTimeoff = getProfileTime(dto);
				String fromDate = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_DATE).format(profileTimeoff.getStartTime());

				ProfileTimeOffDTO timeoffDtoBefore = profileTimeOffService.getProfileTimeOffIdbyDate(dto.getProfileId(),fromDate);
				profileTimeoff.setProfiletimeoffId(timeoffDtoBefore.getProfiletimeoffId());
				profileTimeOffService.saveOrUpdateProfileTimeOff(profileTimeoff);

				List<ProfileTimeoff> list = profileTimeOffService.getDisplayProfileTimeoffByProfileId(dto.getProfileId());
				List<ProfileTimeOffDTO> timeoffDtos = new ArrayList<ProfileTimeOffDTO>();
				
				 list.stream().forEach(t -> {
					 timeoffDtos.add(TimeoffUtil.profileTimeOffDateToString(t));
				 });
 
				map.put(RETURN_DTO, timeoffDtos);
				map.put(STATUS_KEY, true);
				map.put(MESSAGE_KEY, "Create Profile Time Off Success!");
			}
		} catch (Exception e) {
			map.put(STATUS_KEY, false);
			map.put(MESSAGE_KEY, "Create Profilee Time Off Failed");
			LOG.debug("ProfileTimeoffController Exception catch end");
			return map;
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("ProfileTimeoffController.updateTimeoff: end");
		}
		return map;
	}
 
		
 
	/**
	 * Helper method to build profile timeoff object from TimeoffDTO
	 * @param dto
	 * @param person
	 * @return
	 * @throws Exception
	 */
	private ProfileTimeoff getProfileTime(ProfileTimeOffDTO dto) throws Exception {
		//TODO:: Can we use @autowire instead of new ProfileTimeoff
		//Raj
		ProfileTimeoff profileTimeoff = new ProfileTimeoff();
		Long profileId = dto.getProfileId();
		profileTimeoff.setProfileId(profileId);
		String startDate = dto.getFromDate();
		String startTime = dto.getFromTime();
		String endTime = dto.getToTime();
		Date start = null;
		Date end = null;
		try {
			start = new SimpleDateFormat(DateTimeUtil.DATETIME_PATTERN_SLASH)
					.parse(startDate + " " + startTime);
			//GSSP-334 changed the end time to start time for single date
			end = new SimpleDateFormat(DateTimeUtil.DATETIME_PATTERN_SLASH)
					.parse(startDate + " " + endTime);
		} catch (ParseException e) {
			LOG.error("TimeoffController.getTimeoff: parse date error");
			throw new Exception("Date parse error");
		}
		profileTimeoff.setStartTime(start);
		profileTimeoff.setEndTime(end);
		profileTimeoff.setUpdated(new Date());
		profileTimeoff.setUpdatedBy(111L);
		profileTimeoff.setVersion(dto.getVersion());
		
		return profileTimeoff;
	}
	
	//author :RajKumar for 334 update validation 
	/**
	 * checkProfileTimeoff
	 * 
	 * 
	 * @Title: checkProfileTimeoff
	 * @Description:
	 * @param @param dto
	 * @param @return
	 * @return Map<String,Object>
	 * @throws
	 */
	private Map<String, Object> checkProfileTimeoff(ProfileTimeOffDTO dto) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put(STATUS_KEY, true);
		String startDate = dto.getFromDate();
		String startTime = dto.getFromTime();
		String endTime = dto.getToTime();
		if (startDate == null || "".equals(startDate.trim())) {
			map.put(MESSAGE_KEY, VALIDATION_START_DATE_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		if (startTime == null || "".equals(startTime.trim())) {
			map.put(MESSAGE_KEY, VALIDATION_START_TIME_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}

		if (endTime == null || "".equals(endTime.trim())) {
			map.put(MESSAGE_KEY, VALIDATION_START_TIME_NULL);
			map.put(STATUS_KEY, false);
			return map;
		}
		 if (!CalendarUtil.checkTimeFormat(startDate, startTime)
				|| !CalendarUtil.checkTimeFormat(startDate, endTime)) {
			map.put(MESSAGE_KEY, VALIDATION_VALID_TIME_FORMAT);
			map.put(STATUS_KEY, false);
			return map;
		}
		if (!CalendarUtil.checkDate(startDate, startTime, startDate, endTime)) {
			map.put(MESSAGE_KEY, VALIDATION_DATE_TIME_ILLEGAL);
			map.put(STATUS_KEY, false);
			return map;
		}
		if (!validationService.checkStartTime(startDate, startTime)) {
			map.put(MESSAGE_KEY, VALIDATION_START_DATE_ILLEGAL);
			map.put(STATUS_KEY, false);
			return map;
		}
		return map;
	}
	//------------------------------------------------------------------------334 END------------------------------
}
