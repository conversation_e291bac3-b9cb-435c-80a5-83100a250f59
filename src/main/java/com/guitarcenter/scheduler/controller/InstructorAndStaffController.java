package com.guitarcenter.scheduler.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.SessionAttributes;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.AvailabilityUtil;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.JsonUtil;
import com.guitarcenter.scheduler.common.util.TimeoffUtil;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorAppointmentStatusDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorLessonLinkDTO;
import com.guitarcenter.scheduler.dto.InstructorDetailDTO;
import com.guitarcenter.scheduler.dto.InstructorScheduleDetailDTO;
import com.guitarcenter.scheduler.dto.ListInstructorsDTO;
import com.guitarcenter.scheduler.dto.ListInstructorsSchedulerDTO;
import com.guitarcenter.scheduler.dto.ListStaffsDTO;
import com.guitarcenter.scheduler.dto.LocationDTO;
import com.guitarcenter.scheduler.dto.OnetimeDTO;
import com.guitarcenter.scheduler.dto.StaffDTO;
import com.guitarcenter.scheduler.dto.StaffDetailDTO;
import com.guitarcenter.scheduler.dto.TimeOffDTO;
import com.guitarcenter.scheduler.dto.UpdaeAtttandanceStatusRequestDTO;
import com.guitarcenter.scheduler.dto.UpdateInstructorScheduleMessageDTO;
import com.guitarcenter.scheduler.dto.UpdateMessageDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.Employee;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.InstructorAppointmentStatus;
import com.guitarcenter.scheduler.model.InstructorLessonListQueryModel;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.Onetime;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.PersonRole;
import com.guitarcenter.scheduler.model.Role;
import com.guitarcenter.scheduler.model.ServiceMode;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.Timeoff;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.service.AppointmentService;
import com.guitarcenter.scheduler.service.EmployeeService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.LocationManagerService;
import com.guitarcenter.scheduler.service.LocationProfileService;
import com.guitarcenter.scheduler.service.OnetimeService;
import com.guitarcenter.scheduler.service.PersonRoleService;
import com.guitarcenter.scheduler.service.RoleService;
import com.guitarcenter.scheduler.service.SiteService;
import com.guitarcenter.scheduler.service.TimeoffService;
import com.guitarcenter.scheduler.service.impl.ServiceAndActivityValidate;

@SessionAttributes({ AppConstants.PERSON_SESSION_KEY,AppConstants.LOCATION_PROFILE_ID_SESSION_KEY , AppConstants.LOCATION_STRING, AppConstants.SITE_ID_SESSION_KEY, AppConstants.STORE_STRING ,AppConstants.ROLE_STRING, AppConstants.LOCATION_ID_SESSION_KEY })
@Controller
public class InstructorAndStaffController implements AppConstants {

	private static final Logger		LOGGER		= LoggerFactory.getLogger(InstructorAndStaffController.class);
	private static final String		TO_STRING		= " - ";
	private static final String		COMMONT_STRING	= ", ";
	private static final String		BLANK_STRING	= " ";	
	
	//----GSSP-363--
	private static final String STATUS_KEY			  	 	= "status";
	private static final String MESSAGE_KEY            		= "message";
	private static final String MESSAGE_VALUE_APP		    = "Appointment Id is not valid!";
	private static final String MESSAGE_VALUE_CMNT		    = "Internal Remarks should not exceeds 2048 characters";
	private static final String MESSAGE_VALUE_STS		    = "Show status should not exceeds 256 characters";
	private static final String MESSAGE_VALUE_STU_CMNT		    = "Student Notes should not exceeds 2048 characters";
	

	@Resource
	private LocationProfileService	locationProfileService;

	@Resource
	private InstructorService		instructorService;

	@Resource
	private EmployeeService			employeeService;

	@Resource
	private SiteService				siteService;

	@Resource
	private PersonRoleService		personRoleService;

	@Resource
	private LocationManagerService	locationManagerService;
	
	@Resource
	private AppointmentService	appointmentService;

	@Resource
	private RoleService				roleService;
	
	@Resource
	private ServiceAndActivityValidate	 serviceAndActivityValidate;

	@Resource
	private TimeoffService timeoffService;

	@Resource
	private OnetimeService onetimeService;

	public TimeoffService getTimeoffService() {
		return timeoffService;
	}

	public void setTimeoffService(TimeoffService timeoffService) {
		this.timeoffService = timeoffService;
	}

	@RequestMapping("/mainPage.htm")
	public String mainPage() {
		return "/instructor_staff";
	}

	@ResponseBody
	@RequestMapping("instructor/loadInstructorList.htm")
	public ListInstructorsDTO loadInstructorList(@ModelAttribute(LOCATION_STRING)
	Location location) {
		List<Instructor> instructors = instructorService.queryInstructors(location.getLocationId());
		// InstructorListDto instructorListDto = new InstructorListDto();
		List<InstructorDetailDTO> instructorDtos = new ArrayList<InstructorDetailDTO>();
		//---GSSP Instructor Mode update changes
		List<Long> collect = instructors.stream().map(x -> x.getInstructorId()).collect(Collectors.toList());
		Map<Long, ServiceMode> getInstructorModeList = instructorService.getInstructorModeList(collect); 
		for (Instructor instructor : instructors) {
			InstructorDetailDTO instructorDto = new InstructorDetailDTO(instructor.getInstructorId(),
					instructor.getVersion());
			instructorDto.setEmail(instructor.getPerson().getEmail());
			List<Activity> activities = new ArrayList<Activity>(instructor.getActivities());
			StringBuilder activityTypes = new StringBuilder();
			for (Activity activity : activities) {
				activityTypes.append(activity.getActivityName()).append(COMMONT_STRING);
			}
			instructorDto.setActivityTypes(activityTypes.substring(0,
					activityTypes.length() == 0 ? 0 : activityTypes.length() - COMMONT_STRING.length()));
			instructorDto.setActive(Enabled.Y.equals(instructor.getEnabled()));
 
			//---GSSP Instructor Mode update changes
				if(getInstructorModeList.containsKey(instructor.getInstructorId())){
					ServiceMode sm = getInstructorModeList.get(instructor.getInstructorId());
					instructorDto.setInstructorMode(sm.getServiceModeName());
				}else{
					instructorDto.setInstructorMode("Both");
					instructorDto.setServiceModeId("0");
				}
		 
			instructorDto.setServiceModeName(instructorDto.getInstructorMode());

			// availability string
			Availability availability = instructor.getAvailability();
			instructorDto.setAvailability(AvailabilityUtil.getAvailabilityString(availability));
			instructorDto.setInstructorName(StringUtils.isBlank(instructor.getPerson().getFirstName())?"":instructor.getPerson().getFirstName() + BLANK_STRING
					+ (StringUtils.isBlank(instructor.getPerson().getLastName()) ? "":instructor.getPerson().getLastName()));
			//For GSSP-179
			if(!("T".equalsIgnoreCase(instructor.getStatus()) && !instructorDto.getActive()))
			{	
				instructorDtos.add(instructorDto);
			}	
		}
		Collections.sort(instructorDtos);
		ListInstructorsDTO listInstructorsDto = new ListInstructorsDTO();
		listInstructorsDto.setListInstructorDetailDTOs(instructorDtos);
		return listInstructorsDto;
	}

	//GSSP-363 :: Changes. 
	@ResponseBody
	@RequestMapping("instructor/loadInstructorScheduleList.htm")
	public ListInstructorsSchedulerDTO getInstructorLessonList(
			@ModelAttribute(LOCATION_STRING) Location location,
			@ModelAttribute("person") Person person,
			@RequestParam("params")String params) {
		InstructorLessonListQueryModel queryModel = JsonUtil.stringToObject(params,InstructorLessonListQueryModel.class);
		Date queryDate = new Date();
		try {
			queryDate = new SimpleDateFormat("yyyy-MM-dd").parse(queryModel.getQueryDate());
		} catch (Exception e) {
			LOGGER.info("Query Date Parser exception :"+e.getMessage());

		}
		return instructorService.getInstructorsSchedulerViewList(location,person, queryDate);
	}

	@ResponseBody
	@RequestMapping(value = "instructor/sendReminder.htm",method = RequestMethod.POST)
	public boolean sendReminderToCustomer(@RequestParam(value = "params",required = true) String params){
		InstructorLessonLinkDTO dto = JsonUtil.stringToObject(params, InstructorLessonLinkDTO.class);
		return instructorService.sendEmailReminderToCustomer(dto);
	}

	@ResponseBody
	@RequestMapping("/updateInstructorSchedule.htm")
	public UpdateInstructorScheduleMessageDTO updateInstructorSchedule(@RequestBody
			InstructorLessonLinkDTO instructorLessonLinkDTO, @ModelAttribute(PERSON_SESSION_KEY)
	Person person,@ModelAttribute(SITE_ID_SESSION_KEY)Long siteId) {
		Map<String, Object> map = new HashMap<String, Object>();
		UpdateInstructorScheduleMessageDTO updateMessage = new UpdateInstructorScheduleMessageDTO(UPDATE_SUCCESS, true);
		long site_Id = (Long) RequestContextHolder.currentRequestAttributes().getAttribute(SITE_ID_SESSION_KEY, RequestAttributes.SCOPE_SESSION);
 
		//Building InstructorScheduleDetailDTO from the input::InstructorLessonLinkDTO.
		InstructorScheduleDetailDTO  instructorScheduleDetailDTO = this.buildInstructorLessonLinkDTO(instructorLessonLinkDTO);
	
		map = this.checkInstructorScheduleAppt(instructorScheduleDetailDTO,person.getPersonId());
		if((Boolean)map.get(STATUS_KEY)){
				long appId  = Long.parseLong(instructorScheduleDetailDTO.getAppointmentId());
				InstructorAppointmentStatus instructorAppSts = new InstructorAppointmentStatus();
  
				instructorAppSts.setAppointmentId(appId);
				instructorAppSts.setComments(instructorScheduleDetailDTO.getComments());
				instructorAppSts.setStatus(instructorScheduleDetailDTO.getShowStatus());
				instructorAppSts.setStudentNote((instructorScheduleDetailDTO.getStudentNote()));
				instructorAppSts.setSiteId(site_Id);
				instructorAppSts.setVersion(1L);
				instructorAppSts.setUpdated(new Date());
				instructorAppSts.setUpdatedBy(person.getPersonId());
				//instructorAppSts.setLessonStatus((instructorScheduleDetailDTO.getLessonStatus()));
				//instructorAppSts.setNextLessonStatus((instructorScheduleDetailDTO.getNextLessonStatus()));
				instructorAppSts.setAssignment((instructorScheduleDetailDTO.getAssignment()));
				instructorAppSts.setPracticeNotes((instructorScheduleDetailDTO.getPracticeNotes()));
				instructorAppSts.setRemarks((instructorScheduleDetailDTO.getRemarks()));
				//instructorAppSts.setRate((instructorScheduleDetailDTO.getRate()));
				 
				try{
					instructorService.updateInstructorSchedule(instructorAppSts, person.getPersonId());
				}catch(Exception e){
					LOGGER.info("update failed :"+e.getMessage());
					updateMessage.setMessage("update failed!");
					updateMessage.setStatus(false);
					return updateMessage;
				}
		 
				//sendEmailFromInstructorToCustomer(InstructorLessonLinkDTO dto)
				if("submit_val".equals(instructorLessonLinkDTO.getSubmit())){
				instructorService.sendEmailFromInstructorToCustomer(instructorLessonLinkDTO); 
				}
		}else{
			updateMessage.setMessage((String)map.get(MESSAGE_KEY));
			updateMessage.setStatus(false);
			return updateMessage;
		}
		return updateMessage;
	}
	private InstructorScheduleDetailDTO buildInstructorLessonLinkDTO(InstructorLessonLinkDTO instructorLessonLinkDTO) {
		
		    InstructorScheduleDetailDTO instructorScheduleDetailDTO = null;
		    
		    if(null != instructorLessonLinkDTO){
		    	 instructorScheduleDetailDTO = new InstructorScheduleDetailDTO();
		    	 instructorScheduleDetailDTO.setAppointmentId(instructorLessonLinkDTO.getAppointmentId());
		    	 instructorScheduleDetailDTO.setComments((instructorLessonLinkDTO.getComments()));
		    	 instructorScheduleDetailDTO.setShowStatus(instructorLessonLinkDTO.getShowStatus());
		    	 instructorScheduleDetailDTO.setStudentNote((instructorLessonLinkDTO.getStudentNote()));
		    	 //instructorScheduleDetailDTO.setLessonStatus(instructorLessonLinkDTO.getLessonStatus());
		    	 //instructorScheduleDetailDTO.setNextLessonStatus(instructorLessonLinkDTO.getNextLessonStatus());
		    	 instructorScheduleDetailDTO.setAssignment(instructorLessonLinkDTO.getAssignment());
		    	 instructorScheduleDetailDTO.setPracticeNotes(instructorLessonLinkDTO.getPracticeNotes());
		    	 instructorScheduleDetailDTO.setRemarks(instructorLessonLinkDTO.getRemarks());
		    	 //instructorScheduleDetailDTO.setRate(instructorLessonLinkDTO.getRate());
			
		    }
		return instructorScheduleDetailDTO;
	}

	//----------------------GSSP-363  ---- Ends
	//----------------------GSSP-363  ---- Ends
		@ResponseBody
		@RequestMapping("instructor/loadInstructorDetail.htm")
		public InstructorDetailDTO loadInstructorDetail(Long id, @ModelAttribute(LOCATION_PROFILE_ID_SESSION_KEY)
		Long profileId, @ModelAttribute(SITE_ID_SESSION_KEY) 
		Long siteId) {
			InstructorDetailDTO instructorDetailDto = new InstructorDetailDTO();
			Set<Activity> allActivities = new HashSet<Activity>(locationProfileService.getProfileActivityList(profileId));
			Set<Activity> notSelectedActivities = new HashSet<Activity>();
			notSelectedActivities.addAll(allActivities);
			Instructor instructor = instructorService.getInstructor(id);
			Set<Activity> activities = new HashSet<Activity>(instructor.getActivities());
			StringBuilder activityType = new StringBuilder();
			for (Activity activity : activities) {
				activityType.append(activity.getActivityName()).append(COMMONT_STRING);
				if (notSelectedActivities.contains(activity)) {
					notSelectedActivities.remove(activity);
				}
			}
			List<Long> collect = new ArrayList();
			collect.add(instructor.getInstructorId());
			 Map<Long, ServiceMode> getInstructorModeList = instructorService.getInstructorModeList(collect);
			 
			Set<ServiceMode> allServiceModes=null;
			try {
				allServiceModes = new LinkedHashSet<ServiceMode>(instructorService.getServiceModeList());
			} catch (Exception e) {
				LOGGER.info("update failed :"+e);
			}
			Set<ServiceMode> notSelectedServiceModes = new LinkedHashSet<ServiceMode>();
			notSelectedServiceModes.addAll(allServiceModes);
			instructorDetailDto.setNotSelectedServiceMode(notSelectedServiceModes);
			
			Availability availability = instructor.getAvailability();
			String availabilityString = AvailabilityUtil.getAvailabilityString(availability);
			instructorDetailDto.setNotSelectedActivities(notSelectedActivities);
			instructorDetailDto.setActivitys(activities);
			instructorDetailDto.setEditHourShowList(AvailabilityUtil.getAvailabilityEditHourShowList(id,availability, siteId,
					true));
			//for story GCSS-590,get the timeoff
			List<Timeoff> timeoffs = timeoffService.getDisplayTimeoffByInstructorId(id);
			List<TimeOffDTO> timeoffDtos = new ArrayList<TimeOffDTO>();
			for(Timeoff dto : timeoffs){
				TimeOffDTO timeoffDto = TimeoffUtil.timeOffDateToString(dto);
				timeoffDtos.add(timeoffDto);
			}
			instructorDetailDto.setTimeOffs(timeoffDtos);
			
			//GCSS-650
			List<Onetime> onetimes = onetimeService.getDisplayOnetimeByInstructorId(id);
			List<OnetimeDTO> onetimeDtos = new ArrayList<OnetimeDTO>();
			for (Onetime dto : onetimes) {
				OnetimeDTO onetimeDto = onetimeDateToString(dto);
				onetimeDtos.add(onetimeDto);
			}
			instructorDetailDto.setOnetimes(onetimeDtos);
			instructorDetailDto.setVersion(instructor.getVersion());
			instructorDetailDto.setId(id);
			instructorDetailDto.setAvailability(availabilityString);
			instructorDetailDto.setVersion(instructor.getVersion());
			instructorDetailDto.setInstructorName((StringUtils.isBlank(instructor.getPerson().getFirstName())?"":instructor.getPerson().getFirstName()) + BLANK_STRING
					+ (StringUtils.isBlank(instructor.getPerson().getLastName())?"":instructor.getPerson().getLastName()));
			instructorDetailDto.setEmail(instructor.getPerson().getEmail());
			instructorDetailDto.setActivityTypes(activityType.length() > 0 ? activityType.substring(0,
					activityType.length() - COMMONT_STRING.length()) : "");
			instructorDetailDto.setActive(Enabled.Y.equals(instructor.getEnabled()));

			if(getInstructorModeList.containsKey(instructor.getInstructorId())){
			
				ServiceMode sm = getInstructorModeList.get(instructor.getInstructorId());
				instructorDetailDto.setServiceModeName(sm.getServiceModeName());
				 instructorDetailDto.setInstructorMode(Long.toString(sm.getServiceModeId()));
 
	 
			}else{
				 instructorDetailDto.setInstructorMode("0");
				instructorDetailDto.setServiceModeName("Both");
			}
			
			return instructorDetailDto;
		}
	
	private OnetimeDTO onetimeDateToString(Onetime onetime){
		String fromDate = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_DATE).format(onetime.getStartTime());
		String fromTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_TIME).format(onetime.getStartTime());
		String toTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_TIME).format(onetime.getEndTime());
		OnetimeDTO onetimeDto = new OnetimeDTO();
		onetimeDto.setStartDate(fromDate);
		onetimeDto.setFromTime(fromTime);
		onetimeDto.setToTime(toTime);
		onetimeDto.setInstructorId(onetime.getInstructor().getInstructorId());
		onetimeDto.setOnetimeId(onetime.getOnetimeId());
		onetimeDto.setOnetimeStartToEnd(DateTimeUtil.getWeekdayByDate(fromDate) +": "+fromDate + ", " +fromTime+" - "+toTime);
		return onetimeDto;
	}

	//changes made for GSSP-146	
	@ResponseBody
	@RequestMapping("instructor/loadStaffList.htm")
	public ListStaffsDTO loadStaffList(@ModelAttribute(SITE_ID_SESSION_KEY)
	Long siteId, @ModelAttribute(STORE_STRING)
	String storeNumber, Boolean checkStatus ) {
		ListStaffsDTO listStaffsDto = new ListStaffsDTO();
		//changes made for GSSP-288 
		//checking condition for find employees based on location or all employees
		if(checkStatus){
			Site site = siteService.getSiteById(siteId);
			Set<StaffDTO> employeeSet = employeeService.findByStoreNumber(site,storeNumber);
			List<StaffDTO> listStaff = new ArrayList<StaffDTO>(employeeSet);
			Collections.sort(listStaff);	
			listStaffsDto.setlistStaffDtos(listStaff);
			return listStaffsDto;
		}
		else
		{
			return employeeService.findAllStaff(siteId);
		}
	}
	
	@ResponseBody
	@RequestMapping("instructor/loadStaffDetail.htm")
	public StaffDetailDTO loadStaffDetail(Long id, @ModelAttribute(SITE_ID_SESSION_KEY)
	Long siteId) {
		Employee employee = employeeService.getEmployee(id);
		StaffDetailDTO staffDetailDto = new StaffDetailDTO();
		List<LocationDTO> locations = new ArrayList<LocationDTO>();
		Long personId = employee.getPerson().getPersonId();
		Set<PersonRole> personRoles = personRoleService.findByPerson(siteId, personId);
		List<String> selectedLocationIds = new ArrayList<String>();
		Site site = siteService.getSiteById(siteId);
		List<Location> allLocations = locationManagerService.locationsInSite(site);
		List<String> allLocationIds = new ArrayList<String>();
		// ===================== transfer allLocations
		// ==============================
		List<LocationDTO> notSelectedLocations = new LinkedList<LocationDTO>();
		for (Location location : allLocations) {
			allLocationIds.add(location.getExternalId());
		}
		StringBuilder roleLocationName = new StringBuilder(); 
		for (PersonRole personRole : personRoles) {
			//Changes made to GSSP-194
			if(null  !=  personRole.getLocation())
			{	
				String externalId = personRole.getLocation().getExternalId();
				allLocationIds.remove(externalId);
				String roleName = personRole.getRole().getRoleName();
				LocationDTO locationDto = new LocationDTO(externalId, externalId);
				locationDto.setRoleToLocation(externalId + TO_STRING + roleName);
				roleLocationName.append(externalId + TO_STRING + roleName).append(BLANK_STRING);
				locations.add(locationDto);
				selectedLocationIds.add(externalId);
			}	
		}
		for (String externalId : allLocationIds) {
			LocationDTO locationDto = new LocationDTO();
			locationDto.setId(externalId);
			locationDto.setText(externalId);
			notSelectedLocations.add(locationDto);
		}
		staffDetailDto.setSelectedLocationsList(locations);
		// ===================== transfer notSelectedLocation
		// ==============================
		staffDetailDto.setNotSelectedLocation(notSelectedLocations);
		// ===================== transfer allRoles
		// ==============================
		LinkedList<Role> unfilterroles = new LinkedList<Role>(roleService.findBySite(site));
		LinkedList<Role> roles = new LinkedList<Role>();
		for(Role role: unfilterroles)
		{
			//GSSP-209 - observation
			if(role.getRoleId() <= 3 && role.getRoleId() != 0)
				roles.add(role);
		}
		staffDetailDto.setRoles(roles);
		staffDetailDto.setId(id);
		staffDetailDto.setRoleLocationName(roleLocationName.toString());
		if (employee.getStatus() != null &&
            STAFF_STATUS.equals(employee.getStatus().trim())) {
			staffDetailDto.setActive(true);
		} else {
			staffDetailDto.setActive(false);
		}
		return staffDetailDto;
	}

	@ResponseBody
	@RequestMapping("/updateInstructor.htm")
	public UpdateMessageDTO updateInstructor(@RequestBody
			InstructorDetailDTO instructorUpdateDto, @ModelAttribute(PERSON_SESSION_KEY)
	Person person,@ModelAttribute(SITE_ID_SESSION_KEY)Long siteId) {

		UpdateMessageDTO updateMessage = new UpdateMessageDTO(UPDATE_SUCCESS, true);
		Instructor instructor = instructorService.getInstructor(instructorUpdateDto.getId());
		instructor.setVersion(instructorUpdateDto.getVersion());
		final Enabled now_status = instructor.getEnabled();

		Boolean status = instructorUpdateDto.getActive();
		if (status) {
			instructor.setEnabled(Enabled.Y);
		} else {
			instructor.setEnabled(Enabled.N);
		}
		// instructor update means activity will change only
		Set<Activity> activities = instructorUpdateDto.getActivitys();
		StringBuilder activityTypes = new StringBuilder();
		for (Activity activity : activities) {
			activityTypes.append(activity.getActivityName()).append(COMMONT_STRING);
		}
		instructor.setActivities(new HashSet<Activity>(activities));
		// update availability is a single part
		// first update availability then update instructor
		final Boolean result = checkInstructorUpdate(instructor,now_status);
		if(!result){
			//fix bug GCSS-654
			updateMessage.setMessage("Instructor has been used, it cannot be disabled!");
			updateMessage.setStatus(false);
			return updateMessage;
		}
		
		ServiceMode sm=null;
		try {
			sm = instructorService.getServiceMode(Long.valueOf(instructorUpdateDto.getInstructorMode()));
		} catch (NumberFormatException e1) {
			LOGGER.info("update failed :"+e1);
		} catch (Exception e1) {
			LOGGER.info("update failed :"+e1);
		}
		if(instructorUpdateDto.getInstructorMode()!= null ){
			sm.setServiceModeId(Long.valueOf(instructorUpdateDto.getInstructorMode()));
			Set<ServiceMode> serviceMd 	=new HashSet<ServiceMode>();
			serviceMd.add(sm);
			 instructor.setServiceMode(serviceMd);
			 
		}
		try{
			instructorService.updateInstructor(instructor, person.getPersonId(),now_status);
		}catch(Exception e){
			LOGGER.info("update failed :"+e.getMessage());
			updateMessage.setMessage("update failed!");
			updateMessage.setStatus(false);
			return updateMessage;
		}
		InstructorDetailDTO instructorDto = new InstructorDetailDTO();
		instructorDto.setActive(instructorUpdateDto.getActive());
		instructorDto.setActivityTypes(activityTypes.length() > 0 ? activityTypes.substring(0,
				activityTypes.length() - COMMONT_STRING.length()) : BLANK_STRING.trim());
		instructorDto.setAvailability(AvailabilityUtil.getAvailabilityEditHourShowList(instructorUpdateDto.getId(),instructor.getAvailability(), siteId,true).getAvailabilityString());
		instructorDto.setId(instructorUpdateDto.getId());
		instructorDto.setInstructorName(instructorUpdateDto.getInstructorName());
		instructorDto.setEmail(instructorUpdateDto.getEmail());
		updateMessage.setObject(instructorDto);
		instructorDto.setInstructorMode(sm.getServiceModeName());
		return updateMessage;
	}

	private Boolean checkInstructorUpdate(Instructor instructor,Enabled enabled) {
		Boolean result;
		if(Enabled.N.equals(instructor.getEnabled()) && Enabled.Y.equals(enabled)){
			result =  serviceAndActivityValidate.instructorCanDisable(instructor.getInstructorId());
		}else {
			result = true;
		}
		return result;
	}
	
	@ResponseBody
	@RequestMapping("instructor/updateStaff.htm")
	public UpdateMessageDTO updateStaff(@RequestBody
			StaffDetailDTO staffDetailDto, @ModelAttribute(SITE_ID_SESSION_KEY)
	Long siteId, @ModelAttribute(PERSON_SESSION_KEY)
	Person person,@ModelAttribute(LOCATION_ID_SESSION_KEY) Location location) {
		List<String> roleLocation = new ArrayList<String>();
		StringBuilder roleLocationName = new StringBuilder(); 
		List<LocationDTO> dtoList = staffDetailDto.getSelectedLocationsList();
		Role role = (Role) RequestContextHolder.currentRequestAttributes().getAttribute(ROLE_STRING, RequestAttributes.SCOPE_SESSION);
		UpdateMessageDTO updateMessage = new UpdateMessageDTO("updated success", true);
		//GSSP-163 changes
		Employee employee = employeeService.getEmployee(staffDetailDto.getId());
		Long personId = employee.getPerson().getPersonId();
		Set<PersonRole> personRoles = personRoleService.findByPerson(siteId,personId);
		if(role.getRoleId() == 1 )
		{
			if(dtoList.size() == 0 && personRoles.size() > 0)
			{
				for (PersonRole personRole : personRoles) {

					if(! personRole.getLocation().getExternalId().toString().equalsIgnoreCase(location.getExternalId()))
					{
						/*
						 * updateMessage.setMessage("Cannot delete role outside of home studio");
						 * updateMessage.setStatus(false); return updateMessage;
						 */					
						}
				}
			}
		}
		for (LocationDTO locationDto : dtoList) {
			final String  role_location = locationDto.getRoleToLocation();
			if(role.getRoleId() == 1 )
			{
				/*if (!locationDto.getId().equalsIgnoreCase(location.getExternalId()))
				{
					
					  updateMessage.setMessage("Cannot provide access outside of home studio");
					  updateMessage.setStatus(false); return updateMessage; } else {
					 */
				
					roleLocation.add(role_location);
					roleLocationName.append(role_location).append(BLANK_STRING);
				//}
			}
			else
			{	
				roleLocation.add(role_location);
				roleLocationName.append(role_location).append(BLANK_STRING);
			}
		}
		// delete person's authentication
		if (staffDetailDto.getActive()) {
			employee.setStatus(STAFF_STATUS);
		} else {
			employee.setStatus(STAFF_NO_STATUS);
		}
		personRoleService.updatePersonRole(siteId, personId, roleLocation, person, employee);
		StaffDTO staffDto = new StaffDTO();
		staffDto.setRoleLocation(roleLocation);
		staffDto.setRoleLocationName(roleLocationName.toString());
		staffDto.setActive(STAFF_STATUS.equals(employee.getStatus()));
		staffDto.setEmail(employee.getPerson().getEmail());
		staffDto.setId(staffDetailDto.getId());
		staffDto.setInstructorName(employee.getPerson().getFirstName() + BLANK_STRING
				+ employee.getPerson().getLastName());

		updateMessage.setObject(staffDto);
		return updateMessage;
	}
	
	//GSSP - 363 -----------------------Starts--------------------
	public Map<String, Object> checkInstructorScheduleAppt(InstructorScheduleDetailDTO dto,long personId) throws RuntimeException {
		  
		Map<String, Object> map = new HashMap<String, Object>();
		if(dto.getAppointmentId() == null){
			map.put(MESSAGE_KEY, MESSAGE_VALUE_APP);
			map.put(STATUS_KEY, false);
			return map; 
		}
		if(dto.getAppointmentId() != null && checkValidNumber(dto.getAppointmentId())){
			
		}else{
			map.put(MESSAGE_KEY, MESSAGE_VALUE_APP);
			map.put(STATUS_KEY, false);
			return map; 
		}
		if(dto.getComments() == null){
			map.put(MESSAGE_KEY, MESSAGE_VALUE_CMNT);
			map.put(STATUS_KEY, false); 
			return map; 
		}
		if(dto.getComments() != null && dto.getComments().length()> 2048){
			map.put(MESSAGE_KEY, MESSAGE_VALUE_CMNT);
			map.put(STATUS_KEY, false);
			return map; 
		}
		if(dto.getStudentNote() != null && dto.getStudentNote().length()> 2048){
			map.put(MESSAGE_KEY, MESSAGE_VALUE_STU_CMNT);
			map.put(STATUS_KEY, false);
			return map; 
		}
		if(dto.getShowStatus() == null){
			map.put(MESSAGE_KEY, MESSAGE_VALUE_STS);
			map.put(STATUS_KEY, false);
			return map; 
		}
		if(dto.getShowStatus() != null && dto.getShowStatus().length()> 256){
			map.put(MESSAGE_KEY, MESSAGE_VALUE_STS);
			map.put(STATUS_KEY, false);
			return map; 
		}
		map.put(STATUS_KEY, true);
		return map;
	}
	
	public static boolean checkValidNumber(Object obj) throws RuntimeException {
 
		boolean flag=false;
		try {
			// checking Long float using parseInt() method
			Long.parseLong((String) obj);
			flag = true;
		} catch (NumberFormatException e) {
			LOGGER.info("Number validation failed :"+e);
		}
		
		
		return flag;
	}
	
	@ResponseBody
	@RequestMapping(value = "/instructorportal/attendancestatus",method = RequestMethod.POST, headers = { "content-type=application/json" })
	public Map<String, Object> atttandancestatus(@RequestBody UpdaeAtttandanceStatusRequestDTO dto){
		
		//System.out.println(dto.toString());
		Map<String, Object> map = new LinkedHashMap<String, Object>();
		
		try {
		List<InstructorAppointmentStatusDTO>  instructorAppointmentStatusDTO = new ArrayList<InstructorAppointmentStatusDTO>();
		instructorAppointmentStatusDTO = instructorService.findInstructorAppointmentStatusByExternalId(dto.getEmployeeId(), dto.getAppointmentId());
		//System.out.println(instructorAppointmentStatusDTO);
		InstructorAppointmentStatus idto = new InstructorAppointmentStatus();
		for (InstructorAppointmentStatusDTO iasdto : instructorAppointmentStatusDTO) {
			idto.setAppointmentId(Long.parseLong(dto.getAppointmentId()));
			idto.setAssignment("");
			idto.setComments("");
			idto.setPracticeNotes("");
			idto.setRemarks("");
			idto.setSiteId(Long.parseLong(iasdto.getSite()));
			idto.setStatus((dto.getAttended() == "true") ? "Show":"No Show");
			idto.setStudentNote("");
			idto.setUpdated(new Date());
			idto.setVersion(0L);
			//idto.setVersion(Long.parseLong(iasdto.getVersion()));
			idto.setUpdatedBy(1L);
			//idto.setUpdatedBy(Long.parseLong(iasdto.getUpdatedBy()));
			
		}				
		
			instructorService.updateInstructorSchedule(idto);
		map.put("Status", true);
		map.put("message", "updated successfully");
		
		}
		catch(Exception e) {
			map.put("Status", false);
			map.put("message", "Update Failed!!!");
			
		}
		
		return map;
	}	
 //------------GSSP - 363 -----------------------Starts--------------------
}
