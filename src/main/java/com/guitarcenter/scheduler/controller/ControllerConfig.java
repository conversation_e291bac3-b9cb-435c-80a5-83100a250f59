package com.guitarcenter.scheduler.controller;

import com.guitarcenter.scheduler.common.exceptions.BizRuntimeException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import javax.servlet.http.HttpServletRequest;

/**
 * @Date 4/24/2020 4:41 PM
 * <AUTHOR>
 **/
@ControllerAdvice
public class ControllerConfig {
    private final static Logger logger = LoggerFactory.getLogger(ControllerConfig.class);


    /**
     * @param request
     * @param e
     * @return
     * @throws Exception
     */
    @ExceptionHandler(value = Exception.class)
    public void nodataErrorHandler(HttpServletRequest request, Exception e) {
        logger.error("caught controller error:",e);
        throw new BizRuntimeException(e.getMessage());
    }
}
