package com.guitarcenter.scheduler.security;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.service.LocationManagerService;
import com.guitarcenter.scheduler.service.SiteService;

/**
 * Override UsernamePasswordAuthenticationFilter to inject a GCSS token during
 * authentication.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public class GCSSAuthenticationFilter extends
        UsernamePasswordAuthenticationFilter implements AppConstants
{
    Logger log = LoggerFactory.getLogger(GCSSAuthenticationFilter.class);
    
    @Autowired
    private LocationManagerService locationManagerService;
    
    @Autowired
    private SiteService siteService;
    
    /**
     * Called when an authentication request is being processed. This
     * implementation simply rejects
     */
    @Override
    public Authentication attemptAuthentication(HttpServletRequest request,
                                                HttpServletResponse response)
        throws AuthenticationException
    {
        if (log.isDebugEnabled()) {
            log.debug("attempting to authenticate {} in domain {}",
                      request.getParameter(SPRING_SECURITY_FORM_USERNAME_KEY),
                      request.getParameter(DOMAIN_STRING));
        }
        String domain = request.getParameter(DOMAIN_STRING);
        if (StringUtils.isBlank(domain)) {
            throw new BadCredentialsException("A domain is required for GCSS authentication");
        }
        GCSSUsernamePasswordAuthenticationToken authToken =
            new GCSSUsernamePasswordAuthenticationToken(request.getParameter(SPRING_SECURITY_FORM_USERNAME_KEY),
                                                        request.getParameter(SPRING_SECURITY_FORM_PASSWORD_KEY));
        authToken.setDomain(domain);
        authToken.setSite(getSite(request));
        authToken.setLocation(getLocation(request, authToken.getSite()));
        setDetails(request, authToken);
        Authentication authentication =
            super.getAuthenticationManager().authenticate(authToken);
        if (log.isDebugEnabled()) {
            log.debug("returning authentication instance {}", authentication);
        }
        return authentication;
    }
    
    /**
     * Examine the provided HTTP request and determine the correct site
     * instance.
     * 
     * @param request HttpServletRequest instance to examine
     * @return an instance of Site
     */
    private Site getSite(HttpServletRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("identifying site in {}", request);
        }
        /* XXX: MEmes: hard-coded for now, but this needs to change
         */
        String siteIdentifier = "GCS";
        List<Site> sites = siteService.findSitesByExternalId(siteIdentifier);
        if (sites.size() > 1) {
            log.error("authentication filter could not get a single matching " +
                      " site for {}", siteIdentifier);
            throw new BadCredentialsException("A single site is required for authentication");
        }
        if (sites.isEmpty()) {
            if (log.isInfoEnabled()) {
                log.info("authentication filter did not find a site for {}",
                         siteIdentifier);
            }
            throw new BadCredentialsException("A site could not be found for authentication");
        }
        Site site = sites.get(0);
        if (log.isDebugEnabled()) {
            log.debug("returning site {}", site);
        }
        return site;
    }
    
    /**
     * Examine the provided HTTP request and determine the correct location
     * instance based on site.
     * 
     * @param request HttpServletRequest instance to examine
     * @param site the Site instance to match
     * @return an instance of Location
     */
    private Location getLocation(HttpServletRequest request, Site site) {
        if (log.isDebugEnabled()) {
            log.debug("identifying location in {} using {}", site, request);
        }
        Location location = null;
        String store = request.getParameter(STORE_STRING);
        if (StringUtils.isNotBlank(store)) {
            List<Location> locations =
               locationManagerService.findByExternalId(site.getSiteId(), store);
            if (locations.size() == 1) {
                location = locations.get(0);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("returning location {}", location);
        }
        return location;
    }
}
