package com.guitarcenter.scheduler.security;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.guitarcenter.scheduler.common.util.AppConstants;

public class ErrorStringControl implements AppConstants {
	private static final Logger	LOGGER	= LoggerFactory.getLogger(ErrorStringControl.class);



	public String getErrorString(final String errorInfo) {
		final InputStream inputStream = Thread.currentThread().getContextClassLoader()
				.getResourceAsStream(PROPERTIES_STRING);
		final Properties properties = new Properties();
		try {
			properties.load(inputStream);
			inputStream.close();
		} catch (IOException e) {
			try {
				inputStream.close();
			} catch (IOException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			LOGGER.error(e.getMessage());
		}
		
		return (String) properties.get(errorInfo);
	}
}
