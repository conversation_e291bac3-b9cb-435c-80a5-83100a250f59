package com.guitarcenter.scheduler.security;

import java.io.IOException;
import java.util.Collection;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.dto.UserLogDTO;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.service.UserLogService;

public class LoginAuthenticationSuccessHandler implements AuthenticationSuccessHandler, AppConstants {
    Logger log = LoggerFactory.getLogger(LoginAuthenticationSuccessHandler.class);
    
	private static final ErrorStringControl	ERRSTRCON					= new ErrorStringControl();
	final static String						ROLE_STRING					= "role";
	final static String						ADMIN_URL_STRING			= "/adminPage.htm";
	final static String						SUCCESS_URL_STRING			= "/beforeGoCalendar.htm";
	final static String						LOCATION_PROFILEID_STRING	= "locationProfileId";

	@Autowired
    private UserLogService userLogService;
	
	@Override
	public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
			Authentication authentication) throws IOException, ServletException {

	    if (log.isDebugEnabled()) {
            log.debug("handling login success: storeNumber is {} and domain is {}",
                      request.getParameter(STORE_STRING),
                      request.getParameter(DOMAIN_STRING));
        }

        String url = LOGIN_URL_STRING;
        UserLogDTO userLogDTO = new UserLogDTO();
        
		String storeNumber = request.getParameter(STORE_STRING);
		String domainNumber = request.getParameter(DOMAIN_STRING);
		HttpSession session = request.getSession();

		session.setAttribute(STORE_STRING, storeNumber);
		session.setAttribute(DOMAIN_STRING, domainNumber);

		Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
		for (GrantedAuthority authority: authorities) {
		    if (authority instanceof GCSSGrantedAuthority) {
		        if (log.isDebugEnabled()) {
		            log.debug("found a GCSSGrantedAuthority {}", authority);
		        }
		        GCSSGrantedAuthority gcssAuth = (GCSSGrantedAuthority) authority;
		        
		        session.setAttribute(ROLE_STRING, gcssAuth.getPersonRole().getRole());
		        session.setAttribute(PERSON_STRING, gcssAuth.getPersonRole().getPerson());
		        session.setAttribute(SITE_ID_SESSION_KEY, gcssAuth.getPersonRole().getSite().getSiteId());
		        
		        //Changes made for GSSP-209
		        if ((SITE_ADMIN_STRING.equals(gcssAuth.getPersonRole().getRole().getRoleName())  ||
		        		DISTRICT_MANAGER.equals(gcssAuth.getPersonRole().getRole().getRoleName()) || 
                	 REGIONAL_VICE_PRESIDENT.equals(gcssAuth.getPersonRole().getRole().getRoleName()) )&& 
		        	StringUtils.isBlank(storeNumber)) {
		        	
		        	//--GSSP-319 changes for Saving User log information to UserLog Entity --Starts--
					try {
						if (storeNumber != null && !StringUtils.isBlank(storeNumber)) {
							userLogDTO.setLocationExternalId(storeNumber);
						}
						userLogDTO.setPersonID(gcssAuth.getPersonRole().getPerson().getPersonId());
						userLogDTO.setRoleId(gcssAuth.getPersonRole().getRole().getRoleId().intValue());
						userLogService.saveUserLogDetails(userLogDTO);
					} catch (Exception e) {
						log.error("Caught {} from LoginAuthenticationSuccessHandler Admin User Log ", e);
					}
					//--GSSP-319 changes for Saving User log information to UserLog Entity --End--
		        	url = ADMIN_URL_STRING;
		        } else {
		        	
		        	//if(!INSTRUCTOR_STRING.equals(gcssAuth.getPersonRole().getRole().getRoleName())){

		            Location location = gcssAuth.getLocation();
		            session.setAttribute(LOCATION_STRING, location);
		            if (location != null &&
		                location.getLocationProfile() != null) {
		                session.setAttribute(LOCATION_PROFILEID_STRING,
		                                     location.getLocationProfile().getProfileId());
		                
		              //--GSSP-319 changes for Saving User log information to UserLog Entity --Starts--
						try {
							if (storeNumber != null && !StringUtils.isBlank(storeNumber)) {
								userLogDTO.setLocationExternalId(storeNumber);
							}
							userLogDTO.setPersonID(gcssAuth.getPersonRole().getPerson().getPersonId());
							userLogDTO.setRoleId(gcssAuth.getPersonRole().getRole().getRoleId().intValue());
							userLogService.saveUserLogDetails(userLogDTO);
						} catch (Exception e) {
							log.error("Caught {} from LoginAuthenticationSuccessHandler Staff User Log ", e);
						}
						//--GSSP-319 changes for Saving User log information to UserLog Entity --End--
		                url = SUCCESS_URL_STRING;
		            }
		        	//}
		        }
		    }
		}
		
		// setstatus let it post submit only store error come here
		if (LOGIN_URL_STRING.equalsIgnoreCase(url.trim())) {
			// Error message: User has no authority with $store
			session.setAttribute(MESSAGE_ERROR, ERRSTRCON.getErrorString(ERROR_STRING));
			session.setAttribute(J_USER_NAME, request.getParameter(J_USER_NAME));
			session.setAttribute(STORE_STRING, storeNumber);
		}

		if (log.isDebugEnabled()) {
		    log.debug("about to redirect user to {}",
		              request.getContextPath() + url);
		}
		response.setStatus(307);
		response.sendRedirect(request.getContextPath() + url);
	}

}