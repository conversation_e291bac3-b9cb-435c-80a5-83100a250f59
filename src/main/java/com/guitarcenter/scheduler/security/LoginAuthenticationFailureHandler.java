package com.guitarcenter.scheduler.security;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;

import com.guitarcenter.scheduler.common.util.AppConstants;

/**
 * 
 * this class can check most of the login error and return exception
 * 
 * <AUTHOR>
 * 
 */
public class LoginAuthenticationFailureHandler implements AuthenticationFailureHandler, AppConstants {

	private static final ErrorStringControl	ERRSTRCON	= new ErrorStringControl();



	// can't check store error
	@Override
	public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
			AuthenticationException exception) throws IOException, ServletException {

		String j_username = request.getParameter(J_USER_NAME);
		String store = request.getParameter(STORE_STRING);

		HttpSession session = request.getSession();
		
		//whend login failure persist username and store
		session.setAttribute(J_USER_NAME, j_username);
		session.setAttribute(STORE_STRING, store);
		session.setAttribute(DOMAIN_STRING, request.getParameter(DOMAIN_STRING));
		
		//when login failure clear password
		session.setAttribute(J_PASSWORD, "");

		session.setAttribute(MESSAGE_ERROR, ERRSTRCON.getErrorString(ERROR_STRING));

		response.setStatus(307);
		response.sendRedirect(request.getContextPath() + LOGIN_URL_STRING);
	}

}
