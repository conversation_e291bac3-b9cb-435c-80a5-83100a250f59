package com.guitarcenter.scheduler.security;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.ProviderNotFoundException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.model.Employee;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.PersonRole;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.service.EmployeeService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.PersonRoleService;

/**
 * Wraps an authentication provider to support run-time selection by domain.
 *
 * <AUTHOR> Emes <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public class GCSSAuthenticationProviderDecorator
    implements AuthenticationProvider, AppConstants
{
    Logger log = LoggerFactory.getLogger(GCSSAuthenticationProviderDecorator.class);
    
    /**
     * A reference to a person role service that can lookup Scheduler roles for
     * the provided authentication token.
     */
    @Autowired
    private PersonRoleService personRoleService;
    
    /**
     * A reference to a employee service that can lookup Employee by person id.
     */
    @Autowired
    private EmployeeService employeeService;
    
    
    /**
     * A reference to a Instructor service that can lookup Instructor by person id.
     */
   @Autowired
   private InstructorService instructorService;
    
    /**
     * The domain value that this provider is allowed to authenticate
     */
    private String domain;
    
    /**
     * The real authentication provider to use for authentication
     */
    private AuthenticationProvider authenticationProvider;
    
    /**
     * Sets the domain to use for this authentication provider
     * 
     * @param domain String containing the domain for authentication
     */
    public void setDomain(String domain) {
        this.domain = domain;
    }
    
    /**
     * Sets the reference to a real authentication provider
     * 
     * @param authenticationProvider an AuthenticationProvider instance
     */
    public void setAuthenticationProvider(AuthenticationProvider authenticationProvider) {
        this.authenticationProvider = authenticationProvider;
    }

    /**
     * Authenticates using the wrapped provider, but first tests to see if the
     * domain matches.
     * 
     * @param authentication an instance of Authentication to use
     * @return an instance of Authentication
     */
    @Override
    public Authentication authenticate(Authentication authentication)
        throws AuthenticationException
    {
        if (log.isDebugEnabled()) {
            log.debug("authenticating via delegate {} using {}",
                      authenticationProvider, authentication);
        }
        if (authenticationProvider == null) {
            throw new ProviderNotFoundException("An AuthenticationProvider for " + 
                                                domain + " was not found");
        }
        if (StringUtils.isBlank(domain)) {
            throw new BadCredentialsException("A domain is required for GCSS authentication");
        }
        Authentication auth = null;
        if (authentication instanceof GCSSUsernamePasswordAuthenticationToken) {
            GCSSUsernamePasswordAuthenticationToken gcssAuth =
                (GCSSUsernamePasswordAuthenticationToken) authentication;
            if (domain.equals(gcssAuth.getDomain())) {
                /* Break-out and log LDAP and GCSS authentication steps
                 */
                long start = System.currentTimeMillis();
                Authentication provider = authenticationProvider.authenticate(authentication);
                long end = System.currentTimeMillis();
                if (log.isInfoEnabled()) {
                    log.error("provider authentication for {} {} [{}] took {}ms",
                             gcssAuth.getDomain(), gcssAuth.getPrincipal(),
                             (provider != null ? "success" : "failure"),
                             end - start);
                }
                start = System.currentTimeMillis();
                auth = createAuthentication(provider, gcssAuth);
                end = System.currentTimeMillis();
                if (log.isInfoEnabled()) {
                    log.error("gcss authentication for {} {} [{} role(s)] took {}ms",
                             gcssAuth.getPrincipal(), gcssAuth.getDomain(),
                             (auth != null && auth.getAuthorities() != null ? auth.getAuthorities().size() : 0),
                             end - start);
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("returning {}", auth);
        }
        return auth;
    }

    /**
     * 
     * @param providerAuth
     * @param gcssAuth
     * @return an instance of Authentication fully populated or null
     */
    private Authentication createAuthentication(Authentication providerAuth,
                                                GCSSUsernamePasswordAuthenticationToken gcssAuth) {
        if (log.isDebugEnabled()) {
            log.debug("creating an authentication for {} and {}", providerAuth,
                      gcssAuth);
        }
        Authentication authentication = null;
        if (providerAuth != null) {
            /* Wrapped provider was successful so retrieve roles from the database
             * and add to a new Authentication instance.
             * 
             * Note that the roles from the wrapped provider are kept too.
             */
            List<GrantedAuthority> authorities =
                new ArrayList<GrantedAuthority>(providerAuth.getAuthorities());
            List<PersonRole> roles =
                personRoleService.findByAuthId(gcssAuth.getSite().getSiteId(),
                                               gcssAuth.getName());
            if (roles != null) {
                for (PersonRole role: roles) {
                    GrantedAuthority authority = null;
                    if (gcssAuth.getSite().getSiteId().equals(role.getSite().getSiteId())) {
                        /* If the role is "Site Admin" then it can be used if
                         * location was not specified or location profile has
                         * been created.
                         */
                        if (SITE_ADMIN_STRING.equals(role.getRole().getRoleName()) ||
                        		DISTRICT_MANAGER.equals(role.getRole().getRoleName()) || 
                        		REGIONAL_VICE_PRESIDENT.equals(role.getRole().getRoleName()) ) {
                            authority = getAuthority(role, gcssAuth.getLocation());
                            if (authority != null) {
                                authorities.add(authority);
                            }
                            break;
                        } else if (gcssAuth.getLocation() != null &&
                                   role.getLocation() != null &&
                                   gcssAuth.getLocation().getLocationId().equals(role.getLocation().getLocationId()) &&
                                   gcssAuth.getLocation().getLocationProfile() != null &&
                                   Enabled.Y.equals(gcssAuth.getLocation().getLocationProfile().getEnabled()) &&
                                   role.getPerson() != null) {
                          
                        	//Changes made for GSSP-199
                        	if(INSTRUCTOR_STRING.equals(role.getRole().getRoleName()))
                        	{
                        		
                        		/* Other roles MUST be for an active location profile
	                             * and enabled employee
	                             */
	                            List<Instructor> instructors =
	                            		instructorService.findByPersonId(role.getPerson().getPersonId());
	                            for (Instructor instructor: instructors) {
	                                if (instructor.getLocation().getLocationId().equals(gcssAuth.getLocation().getLocationId()) &&
	                                		INSTRUCTOR_ENTERPRISE_STATUS.equals(instructor.getStatus()) 
	                                		&& Enabled.Y.equals(instructor.getEnabled())) {
	                                    authority = getAuthority(role, gcssAuth.getLocation());
	                                    break;
	                                }
	                            }
                        	}   
                        	
                        	
                        	else
                        	{
                       
	                       
	                        	/* Other roles MUST be for an active location profile
	                             * and enabled employee
	                             */
	                            List<Employee> employees =
	                                employeeService.findByPersonId(role.getPerson().getPersonId());
	                            for (Employee employee: employees) {
	                                if (STAFF_STATUS.equals(employee.getStatus())) {
	                                    authority = getAuthority(role, gcssAuth.getLocation());
	                                    break;
	                                }
	                            }
                        	}   
	                            
                        }      
                            if (authority != null) {
                                authorities.add(authority);
                            }
                        }
                    }
                }
              authentication =
                new GCSSUsernamePasswordAuthenticationToken(providerAuth.getName(),
                                                            providerAuth.getCredentials(),
                                                            authorities);
        }
        if (log.isDebugEnabled()) {
            log.debug("returning authentication {}", authentication);
        }
        return authentication;
    }

    /**
     * Returns a GrantedAuthority for the Scheduler role provided with effect
     * in the location specified.
     * 
     * @param role instance of PersonRole
     * @param location instance of Location
     * @return instance of GrantedAuthority
     */
    private GrantedAuthority getAuthority(PersonRole role, Location location) {
        if (log.isDebugEnabled()) {
            log.debug("creating an authority for {} at {}", role, location);
        }
        GrantedAuthority authority = null;
        if (role != null && role.getRole() != null &&
            StringUtils.isNotBlank(role.getRole().getRoleName())) {
            authority = new GCSSGrantedAuthority(role, location);
        }
        if (log.isDebugEnabled()) {
            log.debug("returning authority {}", authority);
        }
        return authority;
    }

    /**
     * Determines if this instance supports the provided authentication object.
     * 
     * Simply delegates to contained AuthenticationProvider.
     * 
     * @param authentication an instance of Authentication to check
     * @return true if the AuthenticationProvider supports authentication of the
     *         type provided
     */
    @Override
    public boolean supports(Class<?> authentication) {
        if (log.isDebugEnabled()) {
            log.debug("examining {} to determine if supported", authentication);
        }
        if (authenticationProvider == null) {
            throw new ProviderNotFoundException("An AuthenticationProvider for " + 
                                                domain + " was not found");
        }
        boolean supports = authenticationProvider.supports(authentication);
        if (log.isDebugEnabled()) {
            log.debug("returning {}", supports);
        }
        return supports;
    }
}
