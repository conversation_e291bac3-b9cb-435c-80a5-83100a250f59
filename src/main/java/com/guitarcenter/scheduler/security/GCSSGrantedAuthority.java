package com.guitarcenter.scheduler.security;

import org.springframework.security.core.GrantedAuthority;

import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.PersonRole;

/**
 * Implements a GrantedAuthority for Scheduler role objects
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public class GCSSGrantedAuthority implements GrantedAuthority {
    private static final long serialVersionUID = 8898952006683761764L;
    public static final String ROLE_PREFIX = "ROLE_";

    private PersonRole personRole;
    private Location location;
    private String authorityString;
    
    public GCSSGrantedAuthority(PersonRole personRole, Location location) {
        this.personRole = personRole;
        this.location = location;
        this.authorityString = ROLE_PREFIX + personRole.getRole().getRoleName();
    }
    
    /**
     * Returns the PersonRole associated with this authority.
     * 
     * @return instance of PersonRole
     */
    public PersonRole getPersonRole() {
        return personRole;
    }
    
    /**
     * Returns the Location associated with this authority.
     * 
     * @return instance of Location
     */
    public Location getLocation() {
        return location;
    }
    
    /**
     * Returns a string that can uniquely identify the authority granted.
     */
    @Override
    public String getAuthority() {
        return authorityString;
    }

}
