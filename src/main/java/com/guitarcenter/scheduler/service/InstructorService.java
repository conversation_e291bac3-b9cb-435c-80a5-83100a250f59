package com.guitarcenter.scheduler.service;

import com.guitarcenter.scheduler.dao.criterion.dto.InstructorAppointmentStatusDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorLessonLinkDTO;
import com.guitarcenter.scheduler.dto.FinalFreeSlotsAndMaxMinDates;
import com.guitarcenter.scheduler.dto.InstructorInfoDTO;
import com.guitarcenter.scheduler.dto.ListInstructorsSchedulerDTO;
import com.guitarcenter.scheduler.integration.dto.EmployeeDTO;
import com.guitarcenter.scheduler.integration.dto.InstructorPersonalDetailsDTO;
import com.guitarcenter.scheduler.model.*;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.webservice.dto.InstructorACTServiceDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceResponseDTO;
import com.guitarcenter.scheduler.webservice.dto.LocationProfileInfoDTO;
import com.guitarcenter.scheduler.webservice.dto.SimpleInstructorAvailabilityDTO;
import com.guitarcenter.scheduler.webservice.dto.SimpleLocationProfileDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceResponseDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorActivitiesAPIDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAvailableHoursDTO;
import com.guitarcenter.scheduler.webservice.dto.LocationProfileInfoDTO;

import org.apache.solr.client.solrj.SolrServerException;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface InstructorService {
	
	public List<Instructor> loadInstructorList(long locationId, Enabled enabled);
	public boolean checkInstructorByTime(long instructorId, String startDate, String endDate, String startTime, String endTime);
	public boolean checkInstructorByProfileActivityId(long instructorId, long activityId, long profileId);
	public List<InstructorInfoDTO> loadByActivityId(long activityId);
	public boolean checkInstructorByAppointmentTime(boolean recurring, long instructorId, String startDate, String endDate, String startTime, String endTime);
	public List<Instructor> queryInstructors(long locationId);
	public boolean checkInstructorByAppointmentTime(long instructorId, String startDate, String endDate, String startTime, String endTime);
	public Instructor getInstructor(Long instrctorId);
	public List<Instructor> getInstructorListByIds(Long... ids);
	public List<Instructor> queryInstructorsById(long instructorId);
	
	public List<InstructorInfoDTO> loadByActivityAndLocation(long activityId, long locationId);
	public List<InstructorInfoDTO> findByInsAVLById(long instructorId);
    
    /**
     * Return a collection of Instructor records for the site id provided that
     * have a matching external id value.
     * 
     * Note: this is returning a collection simply because there is not a
     * database constraint on the external identifier; it is possible that
     * multiple instructors *could* be updated from a single external record in
     * the future.
     * 
     * @param siteId identifier for the site that results must be restricted to
     * @param externalId String containing the external id to lookup
     * @return List of Instructor instances that match the external id in the site
     *         provided
     */
    public List<Instructor> findInstructorsByExternalId(long siteId, String externalId);
    //GSSP-275
    
    public List<Instructor> findInstructor( String externalId);
    /**
     * Create or updates an Scheduler Instructor record with values taken from the
     * supplied EmployeeDTO record. Any exceptions are passed up to caller for
     * decision and handling.
     * 
     * The method will retrieve an existing instructor record from persistent
     * storage that matches the external id of the update record.
     * 
     * @param update an EmployeeDTO instance containing values to update
     * @throws IOException 
     * @throws SolrServerException 
     */
    public void updateFromExternal(EmployeeDTO update) throws SolrServerException, IOException;
	
	public List<InstructorInfoDTO> loadInstructorDTOs(long locationId, Enabled enabled);
	
	public List<InstructorInfoDTO> findByLocationIdAndActivityIds(long locationId, Long... activityIds);
	//public List<InstructorInfoDTO> findByLocationIdAndActivityIdsInsAVL(long locationId, Long... activityIds);
	public void updateInstructor(Instructor instructor, long personId,final Enabled now_status)
			throws Exception;
	public boolean checkUpdateInstructorByAppointmentTime(boolean recurring, long instructorId, String startDate, String endDate, String startTime, String endTime, String excludeAppointmentIdParam);
	
	/**
	 * Fetch the instructor list by profile and activity and startTime and endTime
	 * 
	 * @param profileId
	 * @param serviceId
	 * @param activityId
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public List<InstructorInfoDTO> loadInstructorListByProfileIdAndActivityIdAndDateTime(long profileId, long serviceId, long activityId, Long instructorId, Date startTime, Date endTime, Long appId);
	/**
	 * checkInstructorAvailabilityByTime
	 * 
	 *
	 * @Title: checkInstructorAvailabilityByTime
	 * @Description: 
	 * @param @param instructorId
	 * @param @param startDate
	 * @param @param endDate
	 * @param @param startTime
	 * @param @param endTime
	 * @param @return
	 * @return boolean
	 * @throws
	 */
	boolean checkInstructorAvailabilityByTime(long instructorId,
			String startDate, String endDate, String startTime, String endTime);
	/**
	 * check whether the instructorId's availability and onetime is free or not with the time parameters
	 * checkAvailabilityAndOntime
	 *
	 * @Title: checkAvailabilityAndOntime
	 * @Description: 
	 * @param @param instructorId
	 * @param @param pStartDate
	 * @param @param pEndDate
	 * @param @return
	 * @return boolean
	 * @throws
	 */
	public boolean checkAvailabilityAndOntime(long instructorId,Date pStartDate,Date pEndDate);
	
	//Changes maded for GSSP-199
	public List<Instructor> findByPersonId(long personId);
	public void isInstructorExist(InstructorPersonalDetailsDTO externalEmployee) throws SolrServerException, IOException;
	
	 //Changes maded for GSSP-363
	 public List<InstructorLessonLinkDTO> getInstructorLessonViewList(List<String> personList,Date startDate,Date endDate);
	 
	 public void updateInstructorSchedule(InstructorAppointmentStatus instructorAppointmentStatus, long personId)
				throws Exception;
	 
	 public void updateInstructorSchedule(InstructorAppointmentStatus instructorAppointmentStatus)
				throws Exception;

	 public ListInstructorsSchedulerDTO getInstructorsSchedulerViewList(Location location, Person person,Date queryDate);

	public boolean sendEmailReminderToCustomer (InstructorLessonLinkDTO dto);
	
	public boolean sendEmailFromInstructorToCustomer(InstructorLessonLinkDTO dto);
	
	//---GSSP Instructor Mode update changes
	public List<ServiceMode> getServiceModeList() throws Exception;
	
	public ServiceMode getServiceMode(Long ServiceModeID) throws Exception;
	
	 public Map<Long,ServiceMode> getInstructorModeList(List<Long> instructorIdList);	
	 public InstructorAVLServiceResponseDTO getInstructorAvailability(InstructorAVLServiceDTO  instructorServiceDTO);
	 
	 public InstructorActivitiesAPIDTO getInstructorActivity(InstructorACTServiceDTO  instructorServiceDTO);
     public List<Instructor> getInstructorActivitiesByInstructorsId(long instructorId);
     public List<Instructor> getInstructorsAffectRecently(String  update);
     public List<Instructor> getInstructorsAffectRecent(String update);
     public List<Instructor> getFullInstructorsAffectRecent(String update);
     public List<Instructor> getDisabledInstructorsAffectRecent(String update);
     public List<Instructor> getDisabledFullInstructorsAffectRecent(String update);

     public List<Instructor> getFullInstructorsAffectRecent();
     public Map<String, InstructorAVLServiceResponseDTO> getLocationDistRegion();
     
     public List<InstructorAppointmentStatusDTO> findInstructorAppointmentStatusByExternalId(String instructorExternalId, String appointmentId);


     
     public  FinalFreeSlotsAndMaxMinDates getInstructorFreeSLots(LocationProfileInfoDTO locationProfileDetails,InstructorAVLServiceDTO  instructorServiceDTO)  throws Exception;

     /**
      * Set instructor availability slots for self-service functionality
      *
      * @param result - Response DTO to populate with availability data
      * @param locationProfileDetails - Location and profile information
      * @param instructorServiceDTO - Request DTO containing instructor details
      * @throws Exception
      */
     public void setInstructorsFreeSlotsforSelfService(InstructorAVLServiceResponseDTO result,
                                                      LocationProfileInfoDTO locationProfileDetails,
                                                      InstructorAVLServiceDTO instructorServiceDTO) throws Exception;

     /**
      * Set instructor availability slots for self-service functionality (simplified version)
      * Uses simplified DTOs with only required fields for better performance and clarity
      *
      * @param result - Response DTO to populate with availability data
      * @param simpleLocationProfile - Simplified location profile with only required fields
      * @param simpleInstructorRequest - Simplified instructor request with only required fields
      * @throws Exception
      */
     public void setInstructorsFreeSlotsforSelfService(InstructorAVLServiceResponseDTO result,
                                                      SimpleLocationProfileDTO simpleLocationProfile,
                                                      SimpleInstructorAvailabilityDTO simpleInstructorRequest) throws Exception;


}
