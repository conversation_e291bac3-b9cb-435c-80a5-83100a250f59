package com.guitarcenter.scheduler.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.guitarcenter.scheduler.dao.criterion.dto.InstructorLessonLinkDTO;
import com.guitarcenter.scheduler.dto.InstructorWeeklySuccessDTO;
import com.guitarcenter.scheduler.dto.RegistedCustomerEmailDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Room;

public interface AppointmentEmailService {

	String EMAIL_TEMPLATE_FOR_SINGLE_CUSTOMER_REMINDER="apptEmailSingleCustomerReminder.ftl";

	String EMAIL_TEMPL_FOR_SINGLE_CUSTOMER_REM_INSTORE="apptEmailSingleCustomerRemInStore.ftl";

	String EMAIL_TEMPLATE_FOR_APPT = "apptEmailTemplate.ftl";
	
	//Changes made for LES_119
	String EMAIL_TEMPLATE_FOR_SINGLE_APPT = "apptEmailSingleTemplate.ftl";
	
	String SERVICE_TYPE = "serviceType";
	
	//For GSSP-158
	String EMAIL_TEMPLATE_FOR_INSTRUCTOR_SCCHEDULE = "insScheduleEmailTemplate.ftl";
	
	//Changes made for GSSP-230
	String EMAIL_TEMPLATE_FOR_TERMINATED_EMPLOYEE_NOTIFICATION = "terminatedNotificationEmailTemplate.ftl";
	
	String REG_CUST_EMAIL_TEMPLATE = "regCustomerEmailTemplate.ftl";

	//Changes for GSSP-243 :: ProfileID added
	//GSSP-278 changes
	void sendEmailForCreateAppt(Appointment appointment, String isRecurring, int duration,String instructorName,Room room,Activity activityname,long ProfileID);

	void sendEmailForCreateApptCRMFlow(Appointment appointment, String isRecurring, int duration,String instructorName,Room room,Activity activityname,long ProfileID, String orderId);

	//Changes for GSSP-243 :: ProfileID added
	//GSSP-278 changes
	void sendEmailForModifyAppt(Appointment appointment, String modifyType, int duration,String instructorName,Room room ,Activity activityName,long ProfileID);
	//GCSS-577
	//Changes for GSSP-243 :: ProfileID added
	//GSSP-278 changes
	void sendEmailForCancelAppt(Appointment appointment, String cancelType, int duration, Set<Customer> customerSet,String instructorName,Activity activityname, long ProfileID);

	public void sendReminderEmailForAppt();
	//For GSSP-158
	//GSSP-267 Job Notification
		public List<InstructorWeeklySuccessDTO> sendInstuctorScheduleEmail(String timeZone);

	
	//For Phase-2 Update Service Lesson LES-7
	//Changes for GSSP-243 :: ProfileID added
		//GSSP-278 changes
		//void sendEmailForModifySingleAppt(String date,String startTime,String endTime,List<Map<String,Object>> dataMap, Long serviceId,String cancelType,String instructorName,String lessonType,String curCustName,long ProfileID,String activityName, String roomName,String cancelReason);
		void sendEmailForModifySingleAppt(String date,String startTime,String endTime,List<Map<String,Object>> dataMap, Long serviceId,String cancelType,String instructorName,String lessonType,String curCustName,long ProfileID,String activityName, String roomName);

	//Added for LES_119 
	//Changes for GSSP-243 :: ProfileID added
		//GSSP-278 changes
	public void sendEmailForModifySingleAppt(Appointment appointment, String modifyType, int duration,String instructorName,Room room, Activity activityName,long ProfileID);
	
	
	//Added for LES_120
	//Changes for GSSP-243 :: ProfileID added
	//GSSP-278 changes
	public void sendEmailForCancelSingleAppt(Appointment appointment, String cancelType, int duration, Set<Customer> customerSet,
			String instructorName,Activity activityname,long ProfileID);
	
	//For GSSP-230
	public void sendTerminatedEmployeeEmail();

	public void sendEmailForCustomerReminder(Long appointmentId,Long customerId,String meetingJoinUrl) throws Exception;


    public void sendEmailForCustomerReminderInStore(InstructorLessonLinkDTO dto) throws Exception;
    
    public void sendEmailFromInstructorToCustomer(InstructorLessonLinkDTO dto) throws Exception;
    
    public void sendEmailToRegistedCustomer(RegistedCustomerEmailDTO dto) throws Exception;

	public void triggerAPIOnTrailLessoncomplection();
    
    
}