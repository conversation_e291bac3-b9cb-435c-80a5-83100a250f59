package com.guitarcenter.scheduler.service;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceResponseDTO;

@Component
public class TriggerInstructorAvaiablityForElastic {
	
	private static final Logger		LOG  = LoggerFactory.getLogger("schedulerlog");
	
	@Resource
	InstructorService instructorService;
	
	
	 public InstructorAVLServiceResponseDTO getInstructorAvailability(InstructorAVLServiceDTO  instructorServiceDTO) {					  
		  	
		  InstructorAVLServiceResponseDTO result = new InstructorAVLServiceResponseDTO();
		  try
		  {
			  result = instructorService.getInstructorAvailability(instructorServiceDTO);

		  }
		  catch(Exception e)
		  {
			  e.printStackTrace();
			  LOG.error("Error during fetching of data in new instructor Availability api Web Service", e);
		  }						 
		
		  return result;
		  			  
	  }

}
