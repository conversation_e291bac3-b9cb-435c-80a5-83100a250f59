package com.guitarcenter.scheduler.service.impl;

import com.google.common.base.Function;
import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.google.common.collect.Sets;
import com.guitarcenter.scheduler.common.util.*;
import com.guitarcenter.scheduler.dao.*;
import com.guitarcenter.scheduler.dao.criterion.*;
import com.guitarcenter.scheduler.dao.criterion.dto.AppointmentBookDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.CustomerAppointmentDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.ExportAppointmentDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorLessonLinkDTO;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dto.*;
import com.guitarcenter.scheduler.model.*;
import com.guitarcenter.scheduler.model.enums.Canceled;
import com.guitarcenter.scheduler.model.enums.IsRecurring;
import com.guitarcenter.scheduler.service.AppointmentService;
import com.guitarcenter.scheduler.service.CustomerService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.ProfileTimeOffService;
import com.guitarcenter.scheduler.webservice.dto.CustomerAppointmentDetailsResultDTO;

import com.guitarcenter.scheduler.webservice.dto.PauseServiceUpdateDTO;
import com.guitarcenter.scheduler.webservice.dto.PauseServiceUpdateResponseDTO;
import org.joda.time.DateTime;
import org.joda.time.Weeks;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.DateFormat;
import java.text.Format;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;

//import com.guitarcenter.scheduler.dao.criterion.dto.InstructorDateListDTO;
//import com.guitarcenter.scheduler.dao.criterion.dto.InstructorScheduleDTO;

@Service
public class AppointmentServiceImpl implements AppointmentService, AppConstants {

	private static final Logger		LOG					= LoggerFactory.getLogger(AppointmentServiceImpl.class);

	private static final String		CREATE_APP_LOG		= "AppointmentService.createAppointment: ";
	//GSSP 250
	private static final String		CREATE_CANCEL_APP_LOG		= "AppointmentService.createCancelAppointment: ";
	private static final String		LOAD_APPLIST_LOG	= "AppointmentService.loadAppointmentList: ";
	private static final String		GET_APP_LOG			= "AppointmentService.getAppointment: ";
	private static final String		LOAD_BY_WEEK_LOG	= "AppointmentService.getAppointment: ";

	private static final String		DOT					= ".";

	/**
	 * The index of each day of a week, sunday means 1,monday means 2 etc
	 */
	private static final int		SUN_INDEX			= 1;
	private static final int		MON_INDEX			= 2;
	private static final int		TUE_INDEX			= 3;
	private static final int		WED_INDEX			= 4;
	private static final int		THU_INDEX			= 5;
	private static final int		FRI_INDEX			= 6;

	// if isRecurring is selected the recycle number is 7
	private static final int		RECURRING_CECYCLE	= 7;

	// if the date in month view belong to previous month, it will be added a
	// prefix "p"
	private static final String		PREFIX_P			= "p";
	// if the date in month view belong to next month, it will be added a prefix
	// "n"
	private static final String		PREFIX_N			= "n";
	// identifier to indicate the date of current month
	private static final String		DATE_KEY			= "date";
	// map key of first date of month view
	private static final String		FIRST_DATE_KEY		= "firstDate";
	// map key of last date of month view
	private static final String		LAST_DATE_KEY		= "lastDate";

	private static final String 	CANCEL_SINGLE_APPOINTMENT = "single";

	
	//In calendar view,from 7:00 AM - 12:00 AM,there are 17 hours mean 18*6=1080 minutes duration
	private static final int DISABLED_DAY_DURATION = 1140;

	@Autowired
	private AppointmentDAO			appointmentDAO;
	
	@Autowired
	private AppointmentTransactionsDAO appointmentTransactionsDAO;

	@Autowired
	private AppointmentCancelDAO appointmentCancelDAO;

	@Autowired
	private AppointmentLogDAO appointmentLogDAO;

	@Autowired
	private AppointmentSeriesDAO appointmentSeriesDAO;

	@Autowired
	private LocationProfileDAO locationProfileDAO;

	@Autowired
	private AvailabilityDAO availabilityDAO;
	//GSSP-278 changes
	@Autowired
	private InstructorService instructorService;
	@Autowired
	private TimeoffDAO timeoffDAO;

	@Autowired
	private OnetimeDAO onetimeDAO;

	@Autowired
	@Qualifier("profileTimeoffDAO")
	private ProfileTimeoffDAO profileTimeoffDAO;

	@Autowired
	private CustomerService customerService;

	@Autowired
	ProfileTimeOffService profileTimeOffService;

	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	@Override
	public void createAppointment(Appointment appointment,CreateAppointmentDTO dto) {
		if (LOG.isDebugEnabled()) {
			LOG.debug(CREATE_APP_LOG + "start");
		}
		try {
			AppointmentSeries series = appointment.getAppointmentSeries();
			Person person = appointment.getUpdatedBy();

			if (IsRecurring.Y.equals(series.getIsRecurring())) {// if recurring
				// is checked
				Date startDate = series.getSeriesStartTime();
				Date endDate = series.getSeriesEndTime();// if endDate is null, the recurring duration from now to end of this year else use the current value

				Date startDateTime = appointment.getStartTime();

				//For GCSS-468,
				//This is used to handle the satuation that endTime goes to 12:00 AM, if that make it to 11:59:59 PM,
				//which will make the duration not match with the value from page,so the following if statement is used to make the duration right.
				int duration = new DateTime(appointment.getEndTime()).getMinuteOfDay() - new DateTime(startDateTime).getMinuteOfDay();
				if(duration < 0) {//Bug GCSS-468, if duration is Negative number, means the appointment end time is 00:00:00,need to add one day(1440 minutes) as the final duration
					duration += 1440;
				}
				//End

				if (null == endDate) {
					//264
					endDate = new DateTime(startDate).plusDays(51*7).toDate();//System created 3 years of appointments (52 x 3 = 156 weeks total) , now it's 1 year
				}
				series.setSeriesEndTime(endDate);//Set the endDate of AppointmentSeries

				int ms = 1000;
				int minute = ms * 60;
				int hour = minute * 60;
				float day = hour * 24;
				//GSSP-268 DST Issue Fix
				int genDays = Math.round((float)(endDate.getTime() - startDate.getTime()) / day);


				//---GSSP-334	Req 3 Recurring Appointment with profile Timeoff
				Format f = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH);
				String strDateApp = f.format(new Date());
				List<ProfileTimeOffDTO> profileTimeList = profileTimeOffService.getAppointmentTimeListForProfileAndDate(appointment.getLocationProfile().getProfileId(),strDateApp);
				//------------------------------------------------------

				int count = genDays / RECURRING_CECYCLE;
				for (int i = 0; i <= count; i++) {// iterate the date to generate appointment
					//TODO:: 344 Goal is to Skip the recurring appointment creation if falls under profile timeoff.
					DateTime newDate = new DateTime(startDateTime).plusDays(RECURRING_CECYCLE * i);
					Appointment a = new Appointment();
					a.setCreateTime(new Date());
					if(null!= dto.getAppStatus() && "H".equals(dto.getAppStatus())){
						a.setCanceled(Canceled.H);	
					}else  { 
						a.setCanceled(Canceled.N);
					}
					a.setActivity(appointment.getActivity());
					a.setAppointmentSeries(series);
					a.setBandName(appointment.getBandName());
					/* XXX: MEmes: - fix collection issues
					 * Do not re-use a collection for all appointments; a change
					 * of customer in one appointment should not have an effect
					 * on others. Make a deep copy of the customers from the
					 * source appointment.
					 */
					if(appointment.getCustomers()!=null){
						a.getCustomers().addAll(appointment.getCustomers());
					}

					//For GCSS-468,extends the appoitnment endTime to midnight
					Date endTimeOfApp = newDate.plusMinutes(duration).toDate();
					endTimeOfApp = SystemUtil.getEndTimeIfOnMidnight(endTimeOfApp);
					a.setEndTime(endTimeOfApp);

					a.setInstructor(appointment.getInstructor());
					a.setLocationProfile(appointment.getLocationProfile());
					a.setNote(appointment.getNote());
					a.setRoom(appointment.getRoom());
					a.setSite(appointment.getSite());
					a.setStartTime(newDate.toDate());

					//---GSSP-334	Req 3 Recurring Appointment with profile Timeoff
					//TODO:: Can we use ProfileTimeOffServiceImpl.java--> checkProfileTimeIsUnderGivenTime instead of checkProfileTimeOffMatchesonRecurring
					if(checkProfileTimeOffMatchesonRecurring(profileTimeList,newDate.toDate(),endTimeOfApp)){
						Long appId = appointmentDAO.save(a, person);
						//GSSP-210 changes
						if(dto != null && dto.getOrderId() != null){
						AppointmentTransactions appTns = new AppointmentTransactions();
						appTns.setAppointmentId(appId);
						appTns.setTransactionId(dto.getOrderId());
						appTns.setEmail(dto.getEmail());
						appTns.setPhone(dto.getPhone());
						appointmentTransactionsDAO.saveEntity(appTns, person);
						}
						updateAppointmentLog(a, person.getPersonId());
					}
					//-----------------------------------------------
					//GSSP-210 changes

				}
			} else {

				//For GCSS-468,extends the appoitnment endTime to midnight
				Date ed = appointment.getEndTime();
				ed = SystemUtil.getEndTimeIfOnMidnight(ed);

				appointment.setEndTime(ed);
				if(null!= dto.getAppStatus() && "H".equals(dto.getAppStatus())){
					appointment.setCanceled(Canceled.H);	
				}else  { 
					appointment.setCanceled(Canceled.N);
				} 
				appointment.setCreateTime(new Date());
				long appId = appointmentDAO.save(appointment, person);

				//GSSP-210 changes
				if(dto != null && dto.getOrderId() != null){
					AppointmentTransactions appTns = new AppointmentTransactions();
					appTns.setAppointmentId(appId);
					appTns.setTransactionId(dto.getOrderId());
					appTns.setEmail(dto.getEmail());
					appTns.setPhone(dto.getPhone());
					appointmentTransactionsDAO.saveEntity(appTns, person);
				}
				updateAppointmentLog(appointment, person.getPersonId());

			}
		} catch (RuntimeException e) {
			LOG.error("Caught an exception from " + CREATE_APP_LOG + " {}", e);
			throw new RuntimeException();
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug(CREATE_APP_LOG + "end");
		}
	}
	//GSSP-250 changes for save functionality
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	private void createcancelAppointment(Long appointmentId,long cancelReason,String activityID,long serviceId) {
		if (LOG.isDebugEnabled()) {
			LOG.debug(CREATE_CANCEL_APP_LOG + "start");
		}
		try {
			//GSSP-210 changes
			AppointmentCancel appointmentCancel = new AppointmentCancel();

			AppointmentCancelReason appointmentCancelReason = new AppointmentCancelReason();

			appointmentCancelReason.setAppointmentcancelreasonID(cancelReason);

			appointmentCancel.setAppointmentId(appointmentId);

			appointmentCancel.setAppointmentCancelReason(appointmentCancelReason);
			CreateAppointmentDTO dto = null;
			//279 GSSP-   adding jumpstart to avoid cancel reason
			//GSSP-322 adding Guitar center university to avoid cancel reason
			if(!activityID.equals("100")  && !activityID .equals("120") && !activityID .equals("20") && !activityID .equals("140") && !activityID .equals("400")  && !activityID .equals("320") && !activityID .equals("200")&& serviceId == 1){
				//GSSP-332 changes for DAO save
				appointmentCancelDAO.save(appointmentCancel, new Person());
			}


			//GSSP-210 changes

		} catch (RuntimeException e) {
			LOG.error("Caught an exception from " + CREATE_APP_LOG + " {}", e);
			throw new RuntimeException();
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug(CREATE_APP_LOG + "end");
		}
	}



	//Changes for GSSP-250
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public List<AppointmentCancelReason> getcancelReason(String isRecurring)
	{
		return appointmentDAO.getCancelReason(isRecurring);
	}



	@Override
	public List<Appointment> loadAppointmentList(long siteId, long profileId, Date startTime, Date endTime,
												 String services, String instructors, String rooms, String activitys) {
		if (LOG.isDebugEnabled()) {
			LOG.debug(LOAD_APPLIST_LOG + "start");
		}
		List<Appointment> list = new LinkedList<Appointment>();
		Long[] serviceIds = SystemUtil.getIdArray(services);
		Long[] instructorIds = SystemUtil.getIdArray(instructors);
		Long[] roomIds = SystemUtil.getIdArray(rooms);
		Long[] activityIds = SystemUtil.getIdArray(activitys);
		if ((null == services || 0 == services.length())) {
			serviceIds = null;
		}
		if (null == instructorIds || 0 == instructorIds.length) {
			instructorIds = null;
		}
		if (null == rooms || 0 == rooms.length()) {
			roomIds = null;
		}
		if (null == activitys || 0 == activitys.length()) {
			activityIds = null;
		}
		if (null != services && 0 != services.length()) {
			try {
				Criterion<Appointment, Appointment> criterion = AppointmentCriterion.find(siteId, profileId, startTime,
						endTime, serviceIds, instructorIds, roomIds, activityIds);
				list = appointmentDAO.search(criterion, DAOHelper.FETCH_INSTRUCTOR | DAOHelper.FETCH_ACTIVITY
						| DAOHelper.FETCH_ROOM | DAOHelper.FETCH_SERVICE | DAOHelper.FETCH_MORE_CUSTOMERS |DAOHelper.FETCH_PERSON);
			} catch (RuntimeException e) {
				LOG.error("Caught an exception from " + LOAD_APPLIST_LOG + " {}", e);
			}
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug(LOAD_APPLIST_LOG + "end");
		}
		return list;
	}



	@Override
	public Appointment getAppointment(long appointmentId) {
		if (LOG.isDebugEnabled()) {
			LOG.debug(GET_APP_LOG + "start");
		}
		Appointment appointment = null;
		try {
			Criterion<Appointment, Appointment> criterion = AppointmentCriterion.getByAppointmentId(appointmentId);
			appointment = appointmentDAO.get(criterion);
		} catch (RuntimeException e) {
			LOG.error("Caught an exception from " + GET_APP_LOG + " {}", e);
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug(GET_APP_LOG + "end");
		}
		return appointment;
	}

	@Override
	public Appointment getAppointmentWithCustomer(long appointmentId) {
		if (LOG.isDebugEnabled()) {
			LOG.debug(GET_APP_LOG + "start");
		}
		Appointment appointment = null;
		try {
			Criterion<Appointment, Appointment> criterion = AppointmentCriterion.getByAppointmentId(appointmentId);
			appointment = appointmentDAO.get(criterion, DAOHelper.FETCH_APPOINTMENT_SERIES | DAOHelper.FETCH_MORE_CUSTOMERS);
		} catch (RuntimeException e) {
			LOG.error("Caught an exception from " + GET_APP_LOG + " {}", e);
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug(GET_APP_LOG + "end");
		}
		return appointment;
	}

	private List<Date> dateToWeek(Date mdate) {

		//Changes for GSSP-187

		DateTime mdates = new DateTime(mdate);

		int b = mdates.getDayOfWeek();
		Date fdate;
		List<Date> list = new ArrayList<Date>();

		DateTime weekStartDate = null;

		if(b < 7)
			weekStartDate = mdates.minusDays(b);
		else
			weekStartDate = mdates;

		for (int a = 0; a <= 6; a++) {
			fdate = new Date();
			fdate =weekStartDate.plusDays(a).toDate();
			list.add(a, fdate);
		}
		return list;
	}



	@Override
	public Map<String, Object> loadByWeek(Long customerId, long siteId, long profileId, Date startTime, Date endTime,
										  String services, String instructors, String rooms, String activitys, String date) {
		if (LOG.isDebugEnabled()) {
			LOG.debug(LOAD_BY_WEEK_LOG + "start");
		}
		Map<String, Object> weekMap = new HashMap<String, Object>();
		// By using date parameter to get the date belong to current week
		Calendar c = Calendar.getInstance();
		String validDate = date.split(SPLITOR_DASH)[0];
		String[] dtArr = validDate.split(SPLITOR_SLASH);
		Date  currDate = new DateTime(Integer.parseInt(dtArr[2]), Integer.parseInt(dtArr[0]),
				Integer.parseInt(dtArr[1]), 0, 0, 0).toDate();
		List<Date> dateList = this.dateToWeek(currDate);
		startTime = dateList.get(0);
		endTime = dateList.get(dateList.size() - 1);
		c.setTime(endTime);
		endTime = new DateTime(c.get(Calendar.YEAR), (c.get(Calendar.MONTH) + 1), c.get(Calendar.DATE), 23, 59, 59)
				.toDate();
		List<Appointment> list;
		if (null == customerId) {
			list = this.loadAppointmentList(siteId, profileId, startTime, endTime, services, instructors, rooms,
					activitys);
		} else {
			list = this.loadByCustomer(customerId, startTime, endTime, profileId);
		}
		List<Appointment> listSun = new LinkedList<Appointment>();
		List<Appointment> listTue = new LinkedList<Appointment>();
		List<Appointment> listMon = new LinkedList<Appointment>();
		List<Appointment> listWed = new LinkedList<Appointment>();
		List<Appointment> listThu = new LinkedList<Appointment>();
		List<Appointment> listFri = new LinkedList<Appointment>();
		List<Appointment> listSat = new LinkedList<Appointment>();

		if (null != list && !list.isEmpty()) {
			for (Appointment at : list) {
				Date st = at.getStartTime(); // get the startTime of appointment
				c.setTime(st);
				int dayOfWeek = c.get(Calendar.DAY_OF_WEEK);// the index of the
				// day of the week

				if (SUN_INDEX == dayOfWeek) {
					listSun.add(at);
				} else if (MON_INDEX == dayOfWeek) {
					listMon.add(at);
				} else if (TUE_INDEX == dayOfWeek) {
					listTue.add(at);
				} else if (WED_INDEX == dayOfWeek) {
					listWed.add(at);
				} else if (THU_INDEX == dayOfWeek) {
					listThu.add(at);
				} else if (FRI_INDEX == dayOfWeek) {
					listFri.add(at);
				} else {
					listSat.add(at);
				}
			}
		}
		List<Integer> timeLineList = DateTimeUtil.getTimeLineList();
		if (listSun.isEmpty()) {
			weekMap.put(String.valueOf(0), listSun);
		} else {
			Map<String, Object> timeMap = new LinkedHashMap<String, Object>();
			for (int time : timeLineList) {
				List<Appointment> timeList = new LinkedList<Appointment>();
				for (Appointment a : listSun) {
					c.setTime(a.getStartTime());
					int hour = c.get(Calendar.HOUR_OF_DAY);
					if (hour == time) {
						timeList.add(a);
						timeMap.put(String.valueOf(hour), timeList);
					}
				}
			}
			weekMap.put(String.valueOf(0), timeMap);
		}
		if (listMon.isEmpty()) {
			weekMap.put(String.valueOf(1), listMon);
		} else {
			Map<String, Object> timeMap = new LinkedHashMap<String, Object>();
			for (int time : DateTimeUtil.getTimeLineList()) {
				List<Appointment> timeList = new LinkedList<Appointment>();
				for (Appointment a : listMon) {
					c.setTime(a.getStartTime());
					int hour = c.get(Calendar.HOUR_OF_DAY);
					if (hour == time) {
						timeList.add(a);
						timeMap.put(String.valueOf(hour), timeList);
					}
				}
			}
			weekMap.put(String.valueOf(1), timeMap);
		}
		if (listTue.isEmpty()) {
			weekMap.put(String.valueOf(2), listTue);
		} else {
			Map<String, Object> timeMap = new LinkedHashMap<String, Object>();
			for (int time : DateTimeUtil.getTimeLineList()) {
				List<Appointment> timeList = new LinkedList<Appointment>();
				for (Appointment a : listTue) {
					c.setTime(a.getStartTime());
					int hour = c.get(Calendar.HOUR_OF_DAY);
					if (hour == time) {
						timeList.add(a);
						timeMap.put(String.valueOf(hour), timeList);
					}
				}
			}
			weekMap.put(String.valueOf(2), timeMap);
		}
		if (listWed.isEmpty()) {
			weekMap.put(String.valueOf(3), listWed);
		} else {
			Map<String, Object> timeMap = new LinkedHashMap<String, Object>();
			for (int time : DateTimeUtil.getTimeLineList()) {
				List<Appointment> timeList = new LinkedList<Appointment>();
				for (Appointment a : listWed) {
					c.setTime(a.getStartTime());
					int hour = c.get(Calendar.HOUR_OF_DAY);
					if (hour == time) {
						timeList.add(a);
						timeMap.put(String.valueOf(hour), timeList);
					}
				}
			}
			weekMap.put(String.valueOf(3), timeMap);
		}
		if (listThu.isEmpty()) {
			weekMap.put(String.valueOf(4), listThu);
		} else {
			Map<String, Object> timeMap = new LinkedHashMap<String, Object>();
			for (int time : DateTimeUtil.getTimeLineList()) {
				List<Appointment> timeList = new LinkedList<Appointment>();
				for (Appointment a : listThu) {
					c.setTime(a.getStartTime());
					int hour = c.get(Calendar.HOUR_OF_DAY);
					if (hour == time) {
						timeList.add(a);
						timeMap.put(String.valueOf(hour), timeList);
					}
				}
			}
			weekMap.put(String.valueOf(4), timeMap);
		}
		if (listFri.isEmpty()) {
			weekMap.put(String.valueOf(5), listFri);
		} else {
			Map<String, Object> timeMap = new LinkedHashMap<String, Object>();
			for (int time : DateTimeUtil.getTimeLineList()) {
				List<Appointment> timeList = new LinkedList<Appointment>();
				for (Appointment a : listFri) {
					c.setTime(a.getStartTime());
					int hour = c.get(Calendar.HOUR_OF_DAY);
					if (hour == time) {
						timeList.add(a);
						timeMap.put(String.valueOf(hour), timeList);
					}
				}
			}
			weekMap.put(String.valueOf(5), timeMap);
		}
		if (listSat.isEmpty()) {
			weekMap.put(String.valueOf(6), listSat);
		} else {
			Map<String, Object> timeMap = new LinkedHashMap<String, Object>();
			for (int time : DateTimeUtil.getTimeLineList()) {
				List<Appointment> timeList = new LinkedList<Appointment>();
				for (Appointment a : listSat) {
					c.setTime(a.getStartTime());
					int hour = c.get(Calendar.HOUR_OF_DAY);
					if (hour == time) {
						timeList.add(a);
						timeMap.put(String.valueOf(hour), timeList);
					}
				}
			}
			weekMap.put(String.valueOf(6), timeMap);
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug(LOAD_BY_WEEK_LOG + "end");
		}
		return weekMap;
	}



	/**
	 * Get the each day of current month view
	 */
	@Override
	public Map<String, Object> getDeliveryMonthDateParam(String date) {
		Map<String, Object> map = new LinkedHashMap<String, Object>();
		String[] datePerid = date.split(SPLITOR_DASH);
		String[] dates = datePerid[0].split(SPLITOR_SLASH);
		int year = Integer.parseInt(dates[2]);
		int month = Integer.parseInt(dates[0]);
		// String dateVal = dates[1];
		String yearMonth = year + DOT + month;

		List<String> calendarList = this.getCalendarList(yearMonth);
		for (int i = 0; i < calendarList.size(); i++) {
			String dt = calendarList.get(i);
			if (0 == i) {
				String _dateVal = dt;
				int _year = year;
				int _month = month;
				if (dt.contains(PREFIX_P)) {// the first date belong to previous
					// month
					_dateVal = dt.substring(1);
					_month = month - 1;
					if (1 == month) {
						_month = 12;
						_year = year - 1;
					}
				} else {
					_dateVal = dt;
				}
				String value = (_month < 10 ? "0" + _month : _month) + SPLITOR_SLASH
						+ (_dateVal.length() < 2 ? "0" + _dateVal : _dateVal) + SPLITOR_SLASH + _year;
				map.put(FIRST_DATE_KEY, value);
			} else if (i == calendarList.size() - 1) {
				int _year = year;
				int _month = month;
				String _dateVal = dt;
				if (dt.contains(PREFIX_N)) {
					_dateVal = dt.substring(1);
					_month = month + 1;
					if (12 == month) {// if the current month is 12,the next
						// month is 1
						_year = year + 1;
						_month = 1;
					}
				} else {
					_dateVal = dt;
				}
				String value = (_month < 10 ? "0" + _month : _month) + SPLITOR_SLASH
						+ (_dateVal.length() < 2 ? "0" + _dateVal : _dateVal) + SPLITOR_SLASH + _year;
				map.put(LAST_DATE_KEY, value);
			} else {
				if (dt.contains(PREFIX_P)) {
					String _dateVal = dt;
					int _year = year;
					int _month = month;
					_dateVal = dt.substring(1);
					_month = month - 1;
					if (1 == month) {
						_month = 12;
						_year = year - 1;
					}
					String value = (_month < 10 ? "0" + _month : _month) + SPLITOR_SLASH
							+ (_dateVal.length() < 2 ? "0" + _dateVal : _dateVal) + SPLITOR_SLASH + _year;
					map.put(DATE_KEY + i, value);
				} else if (dt.contains(PREFIX_N)) {
					String _dateVal = dt;
					int _year = year;
					int _month = month + 1;
					_dateVal = dt.substring(1);
					if (12 == month) {// if the current month is 12,the next
						// month is 1
						_year = year + 1;
						_month = 1;
					}
					String value = (_month < 10 ? "0" + _month : _month) + SPLITOR_SLASH
							+ (_dateVal.length() < 2 ? "0" + _dateVal : _dateVal) + SPLITOR_SLASH + _year;
					map.put(DATE_KEY + i, value);
				} else {
					String value = (month < 10 ? "0" + month : month) + SPLITOR_SLASH
							+ (dt.length() < 2 ? "0" + dt : dt) + SPLITOR_SLASH + year;
					map.put(DATE_KEY + i, value);
				}
			}
		}
		return map;
	}



	public List<String> getCalendarList(String yearMonth) {
		String trimedYearMonth = yearMonth.trim();
		return new CalendarUtil(trimedYearMonth).getCalendarList();
	}



	/**
	 * Divide the current month view to at most 6part of 7days each
	 *
	 * @param m
	 * @return
	 */
	public Map<String, Object> getMonthMap(Map<String, Object> m) {
		Map<String, Object> map = new LinkedHashMap<String, Object>();
		int weekCounter = 1;
		Map<String, Object> cMap = new LinkedHashMap<String, Object>();
		for (Map.Entry<String, Object> entry : m.entrySet()) {
			cMap.put(entry.getKey(), entry.getValue());
			if (0 == weekCounter % 7) {
				map.put("week" + weekCounter, cMap);
				cMap = new LinkedHashMap<String, Object>();
			}
			weekCounter++;
		}
		map.put("week" + weekCounter, cMap);
		return map;
	}



	@Override
	public Map<String, Object> loadByMonth(Long customerId, long siteId, long profileId, String date, Date startTime,
										   Date endTime, String services, String instructors, String rooms, String activitys) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("AppointmentService.loadByMonth: start");
		}
		Calendar c = Calendar.getInstance();
		Map<String, Object> map = new LinkedHashMap<String, Object>();
		List<Appointment> list;
		try {
			if (null == customerId) {
				list = this.loadAppointmentList(siteId, profileId, startTime, endTime, services, instructors, rooms,
						activitys);
			} else { // Query the appointment list by searched customer
				list = this.loadByCustomer(customerId, startTime, endTime, profileId);
			}
			Map<String, Object> monthDateMap = this.getDeliveryMonthDateParam(date);
			for (Map.Entry<String, Object> entry : monthDateMap.entrySet()) {
				List<Appointment> appList = new LinkedList<Appointment>();
				Date dateInMap = new SimpleDateFormat("MM/dd/yyyy").parse(entry.getValue().toString());
				if (null != list && !list.isEmpty()) {
					for (Appointment a : list) {
						Date dateInApp = a.getStartTime();
						c.setTime(dateInApp);
						c.set(Calendar.HOUR_OF_DAY, 0);
						c.set(Calendar.MINUTE, 0);
						c.set(Calendar.SECOND, 0);
						c.set(Calendar.MILLISECOND, 0);
						dateInApp = c.getTime();
						if (0 == (dateInApp.getTime() - dateInMap.getTime())) {
							appList.add(a);
						}
						map.put(entry.getValue().toString(), appList);
					}
				} else {// if appointment list is empty,just fill the calendar
					// month view with empty list
					map.put(entry.getValue().toString(), new ArrayList<Appointment>());
				}
			}
		} catch (Exception e) {
			LOG.error("Caught an exception from AppointmentService.loadByMonth: {}", e);
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("AppointmentService.loadByMonth: end");
		}
		Map<String, Object> mp = getMonthMap(map);
		return mp;
	}



	/**
	 * Get appointment list by customerId and time duration
	 */
	@Override
	public List<Appointment> loadByCustomer(long customerId, Date startTime, Date endTime, long profileId) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("AppointmentService.loadByCustomer: start");
		}
		List<Appointment> list = new LinkedList<Appointment>();
		try {
			Criterion<Appointment, Appointment> criterion = AppointmentCriterion.findByCustomerAndDateTime(customerId,
					startTime, endTime, profileId);
			list = appointmentDAO.search(criterion, DAOHelper.FETCH_INSTRUCTOR | DAOHelper.FETCH_ACTIVITY
					| DAOHelper.FETCH_ROOM);
		} catch (Exception e) {
			LOG.error("Caught an exception from AppointmentService.loadByCustomer: {}", e);
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("AppointmentService.loadByCustomer: end");
		}
		return list;
	}

	private void updateSingleAppointment(Appointment app, CreateAppointmentDTO dto, Person updatedBy, int days) {
		app.setCustomers(dto.getCustomers());
		app.setActivity(dto.getActivity());
		app.setInstructor((dto.getInstructor() == null) ? null : dto.getInstructor());
		app.setBandName(dto.getBandName());
		Date newStartTime = new DateTime(dto.getAppointment().getStartTime()).plusDays(days*7).toDate();
		app.setStartTime(newStartTime);

		/**
		 * For GCSS-606,update the endDate of recurring app
		 */
		if(IsRecurring.Y.toString().equals(app.getAppointmentSeries().getIsRecurring().toString())) {

			//Changes made for GSSP-234
			if(null != dto.getActivity())
				app.getAppointmentSeries().setActivity(dto.getActivity());

			if(null !=  dto.getBandName())
				app.getAppointmentSeries().setBandName(dto.getBandName());
			if(null != dto.getNote())
				app.getAppointmentSeries().setNote(dto.getNote());

			//End of Changes made for GSSP-234

			//Set endDate to blank means appointment changed to recurring appointment series with no endDate
			if(null == dto.getEndDate() || "".equals(dto.getEndDate()) || 0 == dto.getEndDate().length()) {
				app.getAppointmentSeries().setSeriesEndTime(null);


			} else {
				//Set to limited recurring appointment series
				try {
					app.getAppointmentSeries().setSeriesEndTime(new SimpleDateFormat("MM/dd/yyyy").parse(dto.getEndDate().toString()));
				} catch (ParseException e) {
					LOG.error("Parse endDate error {}", e);
					throw new RuntimeException("Update Appointment Error!");
				}
			}
		}

		//For GCSS-468,extends the appoitnment endTime to midnight
		Date newEndTime = new DateTime(newStartTime).plusMinutes(Integer.parseInt(dto.getDuration())).toDate();
		newEndTime = SystemUtil.getEndTimeIfOnMidnight(newEndTime);
		app.setEndTime(newEndTime);

		app.setNote(dto.getNote());
		app.setRoom(dto.getRoom());
		appointmentDAO.update(app, updatedBy.getPersonId());

		//GSSP-210 changes
		updateAppointmentLog(app, updatedBy.getPersonId());

	}

	@Override
	public Appointment getClosestScheduledAppByActivity(long activityId) {
		Criterion<Appointment, Appointment> criterion = AppointmentCriterion.getClosestScheduledAppByActivity(activityId);
		return appointmentDAO.get(criterion);
	}

	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	@Override
	public void updateAppointment(CreateAppointmentDTO dto, Appointment db, Person person, Appointment appObj) { //added appointment object prodIssue2
		if (LOG.isDebugEnabled()) {
			LOG.debug("Began to update Appoinment from dto {}", dto);
		}
		try {

			if (IsRecurring.Y.equals(dto.getAppointmentSeries().getIsRecurring())) {


				//---GSSP-334	Req 4 Recurring Appointment get profile date list from profile table
				Long profileId = db.getLocationProfile().getProfileId();
				Format f = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH);
				String strDateApp = f.format(new Date());
				List<ProfileTimeOffDTO> profileTimeList = profileTimeOffService.getAppointmentTimeListForProfileAndDate(profileId,strDateApp);
				//-------------------------------------------------------------------------

				//iterate the list to update appointment
				List<Appointment> toBeUpdatedAppointmentList = this.loadActiveAppointmentListBySeries(dto, db);
				
				if(null != toBeUpdatedAppointmentList && !toBeUpdatedAppointmentList.isEmpty()) {
					for(int i=0; i<toBeUpdatedAppointmentList.size(); i++) {
						Appointment app = toBeUpdatedAppointmentList.get(i);
						Date newStartTime = new DateTime(dto.getAppointment().getStartTime()).plusDays(i*7).toDate();
						Date newEndTime = new DateTime(newStartTime).plusMinutes(Integer.parseInt(dto.getDuration())).toDate();
						newEndTime = SystemUtil.getEndTimeIfOnMidnight(newEndTime);
						if(checkProfileTimeOffMatchesonRecurring(profileTimeList,newStartTime,newEndTime)){

							this.updateSingleAppointment(app, dto, person, i);

						}
					}
				}

				Date previousEndDate = db.getAppointmentSeries().getSeriesEndTime();
				Date newEndDate = dto.getAppointmentSeries().getSeriesEndTime();
				//Issue fix 1:If the end date is changed from finite to null start
				Appointment lastApp=toBeUpdatedAppointmentList.get(toBeUpdatedAppointmentList.size() - 1);
				DateTime lastAppDateTime=new DateTime(lastApp.getStartTime());


				if((newEndDate==null)&&(previousEndDate!=null))
				{

					//start date in the db
					Date startTime=db.getAppointmentSeries().getSeriesStartTime();
					//current end date in the db
					Date currentLastDate=db.getAppointmentSeries().getSeriesEndTime();
					DateTime startdateTime = new DateTime(startTime);
					DateTime currentLastdateTime = new DateTime(currentLastDate);

					//For GCSS-468 copy,
					//This is used to handle the satuation that endTime goes to 12:00 AM, if that make it to 11:59:59 PM,
					//which will make the duration not match with the value from page,so the following if statement is used to make the duration right.
					int duration = new DateTime(db.getEndTime()).getMinuteOfDay() - new DateTime(db.getStartTime()).getMinuteOfDay();
					if(duration < 0) {//Bug GCSS-468, if duration is Negative number, means the appointment end time is 00:00:00,need to add one day(1440 minutes) as the final duration
						duration += 1440;
					}
					//End copy
					//ProdIssue2
					LocalDate currentDate = LocalDate.now();
					Date d1 = Date.from(currentDate.minusYears(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
					int weeks;
					if(startTime.before(d1))
					{
						weeks = Weeks.weeksBetween(new DateTime(appObj.getStartTime()), currentLastdateTime).getWeeks();
					}
					else
					{
						weeks = Weeks.weeksBetween(startdateTime, currentLastdateTime).getWeeks();
					}//prodIssue2 end
					Date endDate = new DateTime(db.getAppointmentSeries().getSeriesEndTime()).plusDays((51-weeks)*7).toDate();//System created 3 years of appointments (52 x 3 = 156 weeks total) , now it's 1 year
					int ms = 1000;
					int minute = ms * 60;
					int hour = minute * 60;
					int day = hour * 24;
					int genDays = (int) ((endDate.getTime() - currentLastDate.getTime()) / day);

					int count = genDays / RECURRING_CECYCLE;
					for (int i = 1; i <= count; i++) {// iterate the date to generate appointment
						DateTime newDate = new DateTime(lastAppDateTime).plusDays(RECURRING_CECYCLE * i);
						Appointment a = new Appointment();
						a.setCreateTime(new Date());
						a.setCanceled(Canceled.N);
						a.setActivity(dto.getActivity());
						a.setAppointmentSeries(dto.getAppointmentSeries());
						a.setBandName(dto.getBandName());
						/* XXX: MEmes: - fix collection issues
						 * Do not re-use a collection for all appointments; a change
						 * of customer in one appointment should not have an effect
						 * on others. Make a deep copy of the customers from the
						 * source appointment.
						 */
						if(dto.getCustomers()!=null){
							a.getCustomers().addAll(dto.getCustomers());
						}

						//For GCSS-468,extends the appointment endTime to midnight
						Date endTimeOfApp = newDate.plusMinutes(duration).toDate();
						endTimeOfApp = SystemUtil.getEndTimeIfOnMidnight(endTimeOfApp);
						a.setEndTime(endTimeOfApp);

						a.setInstructor(dto.getInstructor());
						a.setLocationProfile(db.getLocationProfile());
						a.setNote(dto.getNote());
						a.setRoom(dto.getRoom());
						a.setSite(db.getSite());
						a.setStartTime(newDate.toDate());
						if(checkProfileTimeOffMatchesonRecurring(profileTimeList,newDate.toDate(),endTimeOfApp)){
							appointmentDAO.save(a, person);

							//GSSP-210 changes
							updateAppointmentLog(a, person.getPersonId());
						}
					}


				}


				//Issue fix 1:If the end date is changed from finite to null end




				/**
				 * For GCSS-606,delete the extra appointments if endDate changed before previous endDate
				 * 1.check if endDate changed before previous endDate in AppointmentSeres
				 * 2.delete appointments from newEndDate to previous endDate not cancel
				 */

				if(null != previousEndDate && null != newEndDate) {//avoid NPE
					if(newEndDate.before(previousEndDate)) {

						int hour = new DateTime(db.getStartTime()).getHourOfDay();
						int minutes = new DateTime(db.getStartTime()).getMinuteOfHour();

						Long appointmentSeriesId = db.getAppointmentSeries().getAppointmentSeriesId();
						Date startTime = new DateTime(newEndDate).withHourOfDay(hour).withMinuteOfHour(minutes+1).withSecondOfMinute(0).toDate();
						Date endTime = new DateTime(previousEndDate).withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).toDate();
						this.UpdateExtraAppointmentsToCancel(appointmentSeriesId, startTime, endTime,person);
					}
					//Issue fix 4 start
					else
					{

						//start date in the db
						//Date startTime=db.getAppointmentSeries().getSeriesStartTime();
						//current end date in the db
						Date currentLastDate=db.getAppointmentSeries().getSeriesEndTime();
						DateTime newEnddateTime = new DateTime(newEndDate);
						DateTime currentLastdateTime = new DateTime(currentLastDate);

						//For GCSS-468 copy,
						//This is used to handle the satuation that endTime goes to 12:00 AM, if that make it to 11:59:59 PM,
						//which will make the duration not match with the value from page,so the following if statement is used to make the duration right.
						//int duration = new DateTime(db.getEndTime()).getMinuteOfDay() - new DateTime(db.getStartTime()).getMinuteOfDay();
						int duration=Integer.parseInt(dto.getDuration());//777
						if(duration < 0) {//Bug GCSS-468, if duration is Negative number, means the appointment end time is 00:00:00,need to add one day(1440 minutes) as the final duration
							duration += 1440;
						}
						//End copy

						int weeks = Weeks.weeksBetween(currentLastdateTime,newEnddateTime).getWeeks();
						Date endDate = new DateTime(db.getAppointmentSeries().getSeriesEndTime()).plusDays((weeks)*7).toDate();//System created 3 years of appointments (52 x 3 = 156 weeks total) , now it's 1 year
						int ms = 1000;
						int minute = ms * 60;
						int hour = minute * 60;
						int day = hour * 24;
						int genDays = (int) ((endDate.getTime() - currentLastDate.getTime()) / day);

						int count = genDays / RECURRING_CECYCLE;
						for (int i = 1; i <= count; i++) {// iterate the date to generate appointment
							DateTime newDate = new DateTime(lastAppDateTime).plusDays(RECURRING_CECYCLE * i);
							Appointment a = new Appointment();
							a.setCreateTime(new Date());
							a.setCanceled(Canceled.N);
							a.setActivity(dto.getActivity());
							a.setAppointmentSeries(dto.getAppointmentSeries());
							a.setBandName(dto.getBandName());
							/* XXX: MEmes: - fix collection issues
							 * Do not re-use a collection for all appointments; a change
							 * of customer in one appointment should not have an effect
							 * on others. Make a deep copy of the customers from the
							 * source appointment.
							 */
							if(dto.getCustomers()!=null){
								a.getCustomers().addAll(dto.getCustomers());
							}

							//For GCSS-468,extends the appointment endTime to midnight
							Date endTimeOfApp = newDate.plusMinutes(duration).toDate();
							endTimeOfApp = SystemUtil.getEndTimeIfOnMidnight(endTimeOfApp);
							a.setEndTime(endTimeOfApp);

							a.setInstructor(dto.getInstructor());
							a.setLocationProfile(db.getLocationProfile());
							a.setNote(dto.getNote());
							a.setRoom(dto.getRoom());
							a.setSite(db.getSite());
							a.setStartTime(newDate.toDate());

							if(checkProfileTimeOffMatchesonRecurring(profileTimeList,newDate.toDate(),endTimeOfApp))
							{
								appointmentDAO.save(a, person);

								//GSSP-210 changes
								updateAppointmentLog(a, person.getPersonId());
							}
						}




					}//Issue fix 4 end
				}

				/**
				 * For GSSP-173, fix for Discontinuous Appointments
				 * 1.check if endDate changed from null to before finite endDate in AppointmentSeres
				 * 2.delete all future appointments from newEndDate
				 */
				if(null == previousEndDate && null != newEndDate) {
					List<Appointment> AllAppointmentList = this.loadAllAppointmentListBySeries(db);
					if(null != AllAppointmentList && !AllAppointmentList.isEmpty()) {
						//Get last appointment's start time
						Date LastAppDate = AllAppointmentList.get(AllAppointmentList.size()-1).getStartTime();
						if(newEndDate.before(LastAppDate)) {

							int hour = new DateTime(db.getStartTime()).getHourOfDay();
							int minutes = new DateTime(db.getStartTime()).getMinuteOfHour();

							Long appointmentSeriesId = db.getAppointmentSeries().getAppointmentSeriesId();
							Date startTime = new DateTime(newEndDate).withHourOfDay(hour).withMinuteOfHour(minutes+1).withSecondOfMinute(0).toDate();
							Date endTime = new DateTime(LastAppDate).withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).toDate();
							this.UpdateExtraAppointmentsToCancel(appointmentSeriesId, startTime, endTime,person);
						}
					}
				}




			} else {
				this.updateSingleAppointment(db, dto, person, 0);
			}
		} catch (Exception e) {
			LOG.error("Caught {} when updating appointment", e);
			e.printStackTrace();
			throw new RuntimeException();
		}
	}

	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	@Override
	public void pauseAppointment(CreateAppointmentDTO dto, Appointment db, Person person, Appointment appObj) { //added appointment object prodIssue2
		if (LOG.isDebugEnabled()) {
			LOG.debug("Began to update Appoinment from dto {}", dto);
		}
		try {

			if (IsRecurring.Y.equals(dto.getAppointmentSeries().getIsRecurring())) {


				//---GSSP-334	Req 4 Recurring Appointment get profile date list from profile table
				Long profileId = db.getLocationProfile().getProfileId();
				Format f = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH);
				String strDateApp = f.format(new Date());
				List<ProfileTimeOffDTO> profileTimeList = profileTimeOffService.getAppointmentTimeListForProfileAndDate(profileId,strDateApp);
				//-------------------------------------------------------------------------

				//iterate the list to update appointment
				List<Appointment> toBeUpdatedAppointmentList = this.loadActiveAppointmentListBySeries(dto, db);

				if(null != toBeUpdatedAppointmentList && !toBeUpdatedAppointmentList.isEmpty()) {
					for(int i=0; i<toBeUpdatedAppointmentList.size(); i++) {
						Appointment app = toBeUpdatedAppointmentList.get(i);
						Date newStartTime = new DateTime(dto.getAppointment().getStartTime()).plusDays(i*7).toDate();
						Date newEndTime = new DateTime(newStartTime).plusMinutes(Integer.parseInt(dto.getDuration())).toDate();
						newEndTime = SystemUtil.getEndTimeIfOnMidnight(newEndTime);
						if(checkProfileTimeOffMatchesonRecurring(profileTimeList,newStartTime,newEndTime)){

							this.updateSingleAppointment(app, dto, person, i);

						}
					}
				}

				Date previousEndDate = db.getAppointmentSeries().getSeriesEndTime();
				Date newEndDate = dto.getAppointmentSeries().getSeriesEndTime();
				//Issue fix 1:If the end date is changed from finite to null start
				Appointment lastApp=toBeUpdatedAppointmentList.get(toBeUpdatedAppointmentList.size() - 1);
				DateTime lastAppDateTime=new DateTime(lastApp.getStartTime());


				if((newEndDate==null)&&(previousEndDate!=null))
				{

					//start date in the db
					Date startTime=db.getAppointmentSeries().getSeriesStartTime();
					//current end date in the db
					Date currentLastDate=db.getAppointmentSeries().getSeriesEndTime();
					DateTime startdateTime = new DateTime(startTime);
					DateTime currentLastdateTime = new DateTime(currentLastDate);

					//For GCSS-468 copy,
					//This is used to handle the satuation that endTime goes to 12:00 AM, if that make it to 11:59:59 PM,
					//which will make the duration not match with the value from page,so the following if statement is used to make the duration right.
					int duration = new DateTime(db.getEndTime()).getMinuteOfDay() - new DateTime(db.getStartTime()).getMinuteOfDay();
					if(duration < 0) {//Bug GCSS-468, if duration is Negative number, means the appointment end time is 00:00:00,need to add one day(1440 minutes) as the final duration
						duration += 1440;
					}
					//End copy
					//ProdIssue2
					LocalDate currentDate = LocalDate.now();
					Date d1 = Date.from(currentDate.minusYears(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
					int weeks;
					if(startTime.before(d1))
					{
						weeks = Weeks.weeksBetween(new DateTime(appObj.getStartTime()), currentLastdateTime).getWeeks();
					}
					else
					{
						weeks = Weeks.weeksBetween(startdateTime, currentLastdateTime).getWeeks();
					}//prodIssue2 end
					Date endDate = new DateTime(db.getAppointmentSeries().getSeriesEndTime()).plusDays((51-weeks)*7).toDate();//System created 3 years of appointments (52 x 3 = 156 weeks total) , now it's 1 year
					int ms = 1000;
					int minute = ms * 60;
					int hour = minute * 60;
					int day = hour * 24;
					int genDays = (int) ((endDate.getTime() - currentLastDate.getTime()) / day);

					int count = genDays / RECURRING_CECYCLE;
					for (int i = 1; i <= count; i++) {// iterate the date to generate appointment
						DateTime newDate = new DateTime(lastAppDateTime).plusDays(RECURRING_CECYCLE * i);
						Appointment a = new Appointment();
						a.setCreateTime(new Date());
						a.setCanceled(Canceled.N);
						a.setActivity(dto.getActivity());
						a.setAppointmentSeries(dto.getAppointmentSeries());
						a.setBandName(dto.getBandName());
						/* XXX: MEmes: - fix collection issues
						 * Do not re-use a collection for all appointments; a change
						 * of customer in one appointment should not have an effect
						 * on others. Make a deep copy of the customers from the
						 * source appointment.
						 */
						if(dto.getCustomers()!=null){
							a.getCustomers().addAll(dto.getCustomers());
						}

						//For GCSS-468,extends the appointment endTime to midnight
						Date endTimeOfApp = newDate.plusMinutes(duration).toDate();
						endTimeOfApp = SystemUtil.getEndTimeIfOnMidnight(endTimeOfApp);
						a.setEndTime(endTimeOfApp);

						a.setInstructor(dto.getInstructor());
						a.setLocationProfile(db.getLocationProfile());
						a.setNote(dto.getNote());
						a.setRoom(dto.getRoom());
						a.setSite(db.getSite());
						a.setStartTime(newDate.toDate());
						if(checkProfileTimeOffMatchesonRecurring(profileTimeList,newDate.toDate(),endTimeOfApp)){
							appointmentDAO.save(a, person);

							//GSSP-210 changes
							updateAppointmentLog(a, person.getPersonId());
						}
					}


				}


				//Issue fix 1:If the end date is changed from finite to null end




				/**
				 * For GCSS-606,delete the extra appointments if endDate changed before previous endDate
				 * 1.check if endDate changed before previous endDate in AppointmentSeres
				 * 2.delete appointments from newEndDate to previous endDate not cancel
				 */

				if(null != previousEndDate && null != newEndDate) {//avoid NPE
					if(newEndDate.before(previousEndDate)) {

						int hour = new DateTime(db.getStartTime()).getHourOfDay();
						int minutes = new DateTime(db.getStartTime()).getMinuteOfHour();

						Long appointmentSeriesId = db.getAppointmentSeries().getAppointmentSeriesId();
						Date startTime = new DateTime(newEndDate).withHourOfDay(hour).withMinuteOfHour(minutes+1).withSecondOfMinute(0).toDate();
						Date endTime = new DateTime(previousEndDate).withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).toDate();
						this.UpdateExtraAppointmentsToCancel(appointmentSeriesId, startTime, endTime,person);
					}
					//Issue fix 4 start
					else
					{

						//start date in the db
						//Date startTime=db.getAppointmentSeries().getSeriesStartTime();
						//current end date in the db
						Date currentLastDate=db.getAppointmentSeries().getSeriesEndTime();
						DateTime newEnddateTime = new DateTime(newEndDate);
						DateTime currentLastdateTime = new DateTime(currentLastDate);

						//For GCSS-468 copy,
						//This is used to handle the satuation that endTime goes to 12:00 AM, if that make it to 11:59:59 PM,
						//which will make the duration not match with the value from page,so the following if statement is used to make the duration right.
						//int duration = new DateTime(db.getEndTime()).getMinuteOfDay() - new DateTime(db.getStartTime()).getMinuteOfDay();
						int duration=Integer.parseInt(dto.getDuration());//777
						if(duration < 0) {//Bug GCSS-468, if duration is Negative number, means the appointment end time is 00:00:00,need to add one day(1440 minutes) as the final duration
							duration += 1440;
						}
						//End copy

						int weeks = Weeks.weeksBetween(currentLastdateTime,newEnddateTime).getWeeks();
						Date endDate = new DateTime(db.getAppointmentSeries().getSeriesEndTime()).plusDays((weeks)*7).toDate();//System created 3 years of appointments (52 x 3 = 156 weeks total) , now it's 1 year
						int ms = 1000;
						int minute = ms * 60;
						int hour = minute * 60;
						int day = hour * 24;
						int genDays = (int) ((endDate.getTime() - currentLastDate.getTime()) / day);

						int count = genDays / RECURRING_CECYCLE;
						for (int i = 1; i <= count; i++) {// iterate the date to generate appointment
							DateTime newDate = new DateTime(lastAppDateTime).plusDays(RECURRING_CECYCLE * i);
							Appointment a = new Appointment();
							a.setCreateTime(new Date());
							a.setCanceled(Canceled.N);
							a.setActivity(dto.getActivity());
							a.setAppointmentSeries(dto.getAppointmentSeries());
							a.setBandName(dto.getBandName());
							/* XXX: MEmes: - fix collection issues
							 * Do not re-use a collection for all appointments; a change
							 * of customer in one appointment should not have an effect
							 * on others. Make a deep copy of the customers from the
							 * source appointment.
							 */
							if(dto.getCustomers()!=null){
								a.getCustomers().addAll(dto.getCustomers());
							}

							//For GCSS-468,extends the appointment endTime to midnight
							Date endTimeOfApp = newDate.plusMinutes(duration).toDate();
							endTimeOfApp = SystemUtil.getEndTimeIfOnMidnight(endTimeOfApp);
							a.setEndTime(endTimeOfApp);

							a.setInstructor(dto.getInstructor());
							a.setLocationProfile(db.getLocationProfile());
							a.setNote(dto.getNote());
							a.setRoom(dto.getRoom());
							a.setSite(db.getSite());
							a.setStartTime(newDate.toDate());

							if(checkProfileTimeOffMatchesonRecurring(profileTimeList,newDate.toDate(),endTimeOfApp))
							{
								appointmentDAO.save(a, person);

								//GSSP-210 changes
								updateAppointmentLog(a, person.getPersonId());
							}
						}




					}//Issue fix 4 end
				}

				/**
				 * For GSSP-173, fix for Discontinuous Appointments
				 * 1.check if endDate changed from null to before finite endDate in AppointmentSeres
				 * 2.delete all future appointments from newEndDate
				 */
				if(null == previousEndDate && null != newEndDate) {
					List<Appointment> AllAppointmentList = this.loadAllAppointmentListBySeries(db);
					if(null != AllAppointmentList && !AllAppointmentList.isEmpty()) {
						//Get last appointment's start time
						Date LastAppDate = AllAppointmentList.get(AllAppointmentList.size()-1).getStartTime();
						if(newEndDate.before(LastAppDate)) {

							int hour = new DateTime(db.getStartTime()).getHourOfDay();
							int minutes = new DateTime(db.getStartTime()).getMinuteOfHour();

							Long appointmentSeriesId = db.getAppointmentSeries().getAppointmentSeriesId();
							Date startTime = new DateTime(newEndDate).withHourOfDay(hour).withMinuteOfHour(minutes+1).withSecondOfMinute(0).toDate();
							Date endTime = new DateTime(LastAppDate).withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).toDate();
							this.UpdateExtraAppointmentsToCancel(appointmentSeriesId, startTime, endTime,person);
						}
					}
				}




			} else {
				this.updateSingleAppointment(db, dto, person, 0);
			}
		} catch (Exception e) {
			LOG.error("Caught {} when updating appointment", e);
			e.printStackTrace();
			throw new RuntimeException();
		}
	}

	/**
	 * Helper method to query appointment list that to be deleted
	 *
	 * @param pAppointmentSereisId
	 * @param pStartTime
	 * @param pEndDate
	 */
	private void deleteExtraAppointments(Long pAppointmentSereisId, Date pStartTime, Date pEndDate) {
		Criterion<Appointment, Appointment> criterion = AppointmentCriterion
				.findByAppointmentSeriesAndDateTime(pAppointmentSereisId, pStartTime, pEndDate);
		List<Appointment> delList = appointmentDAO.search(criterion);
		for(Appointment a : delList) {
			appointmentDAO.delete(a);
		}
	}
	
	private void UpdateExtraAppointmentsToCancel(Long pAppointmentSereisId, Date pStartTime, Date pEndDate,Person pt) {
		Criterion<Appointment, Appointment> criterion = AppointmentCriterion
				.findByAppointmentSeriesAndDateTime(pAppointmentSereisId, pStartTime, pEndDate);
		List<Appointment> delList = appointmentDAO.search(criterion);
		for(Appointment a : delList) {
			 a.setCanceled(Canceled.Y);
			 appointmentDAO.update(a, pt);
		}
	}

	private List<Appointment> loadAllVliadAppointment(CreateAppointmentDTO dto, Appointment db) {
		List<Appointment> allVliadAppointmentList = new LinkedList<Appointment>();
		try {
			Date date = new Date();
			Criterion<Appointment, Appointment> criterion = AppointmentCriterion.findByAppointmentSeriesAndStartTime(db.getAppointmentSeries().getAppointmentSeriesId(), date);
			allVliadAppointmentList = appointmentDAO.search(criterion, DAOHelper.FETCH_APPOINTMENT_SERIES);
		} catch (Exception e) {
			LOG.error("Caught exceprion {} when loading appointment by series", e);
		}
		return allVliadAppointmentList;
	}

	@Override
	@Transactional
	public List<Appointment> loadAppointmentListBySeries(CreateAppointmentDTO dto, Appointment db) {
		List<Appointment> toBeUpdatedAppointmentList = new LinkedList<Appointment>();
		try {
			Date endDate = dto.getAppointmentSeries().getSeriesEndTime();
			if(null != endDate) {
				Calendar c = Calendar.getInstance();
				c.setTime(endDate);
				c.set(Calendar.HOUR_OF_DAY, 23);
				c.set(Calendar.MINUTE, 59);
				c.set(Calendar.SECOND, 59);
				endDate = c.getTime();
			}
			//I72I351432:GSSP-268 bug fix Changes start
			String r = dto.getIsRecurring();
			boolean isRecurring = false;
			if (null != r && 0 != r.length()) {
				isRecurring = Boolean.parseBoolean(r);
			}

			if(!isRecurring && db.getStartTime().after(endDate)){
				endDate = null;
			}
			//I72I351432 Changes end
			Criterion<Appointment, Appointment> criterion = AppointmentCriterion
					.findByAppointmentSeriesAndDateTime(db.getAppointmentSeries().getAppointmentSeriesId(), db.getStartTime(), endDate);
			toBeUpdatedAppointmentList = appointmentDAO.search(criterion, DAOHelper.FETCH_APPOINTMENT_SERIES);

		} catch (Exception e) {
			LOG.error("Caught {} when loadAppointmentListBySeries", e);
		}
		return toBeUpdatedAppointmentList;
	}

	@Override
	@Transactional
	public List<Appointment> loadActiveAppointmentListBySeries(CreateAppointmentDTO dto, Appointment db) {
		List<Appointment> toBeUpdatedAppointmentList = new LinkedList<Appointment>();
		try {
			Date endDate = dto.getAppointmentSeries().getSeriesEndTime();
			if(null != endDate) {
				Calendar c = Calendar.getInstance();
				c.setTime(endDate);
				c.set(Calendar.HOUR_OF_DAY, 23);
				c.set(Calendar.MINUTE, 59);
				c.set(Calendar.SECOND, 59);
				endDate = c.getTime();
			}
			//I72I351432:GSSP-268 bug fix Changes start
			String r = dto.getIsRecurring();
			boolean isRecurring = false;
			if (null != r && 0 != r.length()) {
				isRecurring = Boolean.parseBoolean(r);
			}

			if(!isRecurring && db.getStartTime().after(endDate)){
				endDate = null;
			}
			
			Criterion<Appointment, Appointment> criterion = AppointmentCriterion
					.findByActiveAppointmentSeriesAndDateTime(db.getAppointmentSeries().getAppointmentSeriesId(), db.getStartTime(), endDate);
			toBeUpdatedAppointmentList = appointmentDAO.search(criterion, DAOHelper.FETCH_APPOINTMENT_SERIES);

		} catch (Exception e) {
			LOG.error("Caught {} when loadActiveAppointmentListBySeries", e);
		}
		return toBeUpdatedAppointmentList;
	}

	/**
	 * For GSSP-173, fix for Discontinuous Appointments
	 *
	 * @param db
	 */
	@Override
	@Transactional
	public List<Appointment> loadAllAppointmentListBySeries(Appointment db) {
		List<Appointment> AllAppointmentList = new LinkedList<Appointment>();
		try {
			Criterion<Appointment, Appointment> criterion = AppointmentCriterion
					.findByAppointmentSeriesAndDateTime(db.getAppointmentSeries().getAppointmentSeriesId(), db.getStartTime(), null);
			AllAppointmentList = appointmentDAO.search(criterion, DAOHelper.FETCH_APPOINTMENT_SERIES);
		} catch (Exception e) {
			LOG.error("Caught {} when loadAllAppointmentListBySeries", e);
		}
		return AllAppointmentList;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public boolean checkAppointmentByRoom(long roomId, long profileId) {
		return appointmentDAO.checkAppointmentByRoom(roomId, profileId);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public boolean checkAppointmentByRoomActivity(long roomId, long activity, long profileId) {
		return appointmentDAO.checkAppointmentByRoomActivity(roomId, activity, profileId);
	}

	/**
	 * <p>Title: checkAppointmentByProfile</p>
	 * <p>Description: </p>
	 * @param profileId
	 * @return
	 * @see com.guitarcenter.scheduler.service.AppointmentService#checkAppointmentByProfile(long)
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public boolean checkAppointmentByProfile(long profileId) {
		return appointmentDAO.checkAppointmentByProfile(profileId);
	}

	/**
	 * <p>Title: checkStartTime</p>
	 * <p>Description: </p>
	 * @param startDate
	 * @param startTime
	 * @return
	 * @see com.guitarcenter.scheduler.service.AppointmentService#checkStartTime(java.lang.String, java.lang.String)
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public boolean checkStartTime(String startDate, String startTime) {
		return appointmentDAO.checkStartTime(startDate, startTime);
	}

	/**
	 * <p>Title: checkAppointmentByRoom</p>
	 * <p>Description: </p>
	 * @param roomId
	 * @return
	 * @see com.guitarcenter.scheduler.service.AppointmentService#checkAppointmentByRoom(long)
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public boolean checkAppointmentByRoom(long roomId) {
		return appointmentDAO.checkAppointmentByRoom(roomId);
	}

	@Override
	public Boolean hasByProfileIdAndActivityId(Long profileId, long activityId) {
		Criterion<Appointment, Boolean> criterion = AppointmentCriterion.hasByProfileIdAndActivityId(profileId, activityId);
		Boolean result = appointmentDAO.get(criterion);
		return result;
	}

	/**
	 * <p>Title: checkAppointmentByRoomTemplateIdActivity</p>
	 * <p>Description: </p>
	 * @param roomTemplateId
	 * @param activityId
	 * @return
	 * @see com.guitarcenter.scheduler.service.AppointmentService#checkAppointmentByRoomTemplateIdActivity(long, long)
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public boolean checkAppointmentByRoomTemplateIdActivity(long roomTemplateId, long activityId) {
		return appointmentDAO.checkAppointmentByRoomTemplateIdActivity(roomTemplateId, activityId);
	}

	@Override
	public List<StartTimeDTO> generateStartTimeListByProfileAndDate(Long profileId, Long instructorId, Date startDate) {

		DateTime today = new DateTime(startDate);
		int dayOfWeek = today.getDayOfWeek();
		LocationProfile profile = locationProfileDAO.get(profileId, DAOHelper.FETCH_AVAILABILITY | DAOHelper.FETCH_SITE);
		Availability profileAvailability = profile.getAvailability();
		Date profileStartTimeByDayOfWeek = profileAvailability.getStartTimeByDayOfWeek(dayOfWeek);
		Date profileEndTimeByDayOfWeek = profileAvailability.getEndTimeByDayOfWeek(dayOfWeek);

		if (instructorId == null || profileStartTimeByDayOfWeek == null || profileEndTimeByDayOfWeek == null) {
			return generateListByTimeRange(profileStartTimeByDayOfWeek, profileEndTimeByDayOfWeek);
		}

		DateTime profileStartDateTime = new DateTime(profileStartTimeByDayOfWeek);
		DateTime profileEndDateTime = new DateTime(profileEndTimeByDayOfWeek);

		TimeLineDTO profileTimeLine = new TimeLineDTO(
				today.withTime(profileStartDateTime.getHourOfDay(), profileStartDateTime.getMinuteOfHour(), profileStartDateTime.getSecondOfMinute(), profileStartDateTime.getMillisOfSecond()),
				today.withTime(profileEndDateTime.getHourOfDay(), profileEndDateTime.getMinuteOfHour(), profileEndDateTime.getSecondOfMinute(), profileEndDateTime.getMillisOfSecond())
		);
		TimeLineDTO instructorTimeLine = null;

		Criterion<Availability, Availability> criterion = AvailabilityCriterion.getByProfileIdAndInstructorId(profileId, instructorId);
		Availability instructorAvailability = availabilityDAO.get(criterion);
		Date instructorStartTimeByDayOfWeek = instructorAvailability.getStartTimeByDayOfWeek(dayOfWeek);
		Date instructorEndTimeByDayOfWeek = instructorAvailability.getEndTimeByDayOfWeek(dayOfWeek);

		if (instructorStartTimeByDayOfWeek != null || instructorEndTimeByDayOfWeek != null) {
			DateTime instructorStartDateTime = new DateTime(instructorStartTimeByDayOfWeek);
			DateTime instructorEndDateTime = new DateTime(instructorEndTimeByDayOfWeek);
			instructorTimeLine = new TimeLineDTO(
					today.withTime(instructorStartDateTime.getHourOfDay(), instructorStartDateTime.getMinuteOfHour(), instructorStartDateTime.getSecondOfMinute(), instructorStartDateTime.getMillisOfSecond()),
					today.withTime(instructorEndDateTime.getHourOfDay(), instructorEndDateTime.getMinuteOfHour(), instructorEndDateTime.getSecondOfMinute(), instructorEndDateTime.getMillisOfSecond())
			);
		}

		Criterion<Onetime, TimeLineDTO> onetimeCriterion = OnetimeCriterion.findByInstructorAndDateTime(instructorId, startDate);
		List<TimeLineDTO> onetimes = onetimeDAO.search(onetimeCriterion);

		if (onetimes.isEmpty() && instructorTimeLine == null){
			return Collections.emptyList();
		}

		for (TimeLineDTO onetime : onetimes) {
			DateTime startTime = onetime.getStartDateTime();
			DateTime endTime = onetime.getEndDateTime();
			if (profileTimeLine.getStartDateTime().isAfter(startTime)) {
				onetime.setStartDateTime(profileTimeLine.getStartDateTime());
			}
			if (profileTimeLine.getEndDateTime().isBefore(endTime)) {
				onetime.setEndDateTime(profileTimeLine.getEndDateTime());
			}
		}

		List<TimeLineDTO> list = Lists.newLinkedList();

		if (instructorTimeLine != null) {
			final TimeLineDTO _instructorTimeLine = instructorTimeLine;
			Collection<TimeLineDTO> overlap = Collections2.filter(onetimes, new Predicate<TimeLineDTO>() {
				@Override
				public boolean apply(TimeLineDTO input) {
					boolean result = true;
					if (input.getEndDateTime().isBefore(_instructorTimeLine.getStartDateTime()) || input.getStartDateTime().isAfter(_instructorTimeLine.getEndDateTime())) {
						result = false;
					}
					return result;
				}
			});
			for (TimeLineDTO timeLineDTO : overlap) {
				if (instructorTimeLine.getStartDateTime().isAfter(timeLineDTO.getStartDateTime())) {
					instructorTimeLine.setStartDateTime(timeLineDTO.getStartDateTime());
				}
				if (instructorTimeLine.getEndDateTime().isBefore(timeLineDTO.getEndDateTime())) {
					instructorTimeLine.setEndDateTime(timeLineDTO.getEndDateTime());
				}
			}
			list.add(instructorTimeLine);
			onetimes.removeAll(overlap);
		}
		list.addAll(onetimes);

		Criterion<Timeoff, TimeLineDTO> timeOffCriterion = TimeoffCriterion.findByInstructorAndDateTime(instructorId, startDate);
		List<TimeLineDTO> timeOffs = timeoffDAO.search(timeOffCriterion);

		for (TimeLineDTO timeOff : timeOffs) {
			DateTime startTime = timeOff.getStartDateTime();
			DateTime endTime = timeOff.getEndDateTime();
			Set<TimeLineDTO> removed = Sets.newHashSet();
			Set<TimeLineDTO> added = Sets.newHashSet();
			for (TimeLineDTO timeLineDTO : list) {
				DateTime timeOffStartTime = timeLineDTO.getStartDateTime();
				DateTime timeOffEndTime = timeLineDTO.getEndDateTime();
				if (startTime.isBefore(timeOffStartTime) && endTime.isAfter(timeOffEndTime)){
					removed.add(timeLineDTO);
					continue;
				}
				if (startTime.isAfter(timeOffStartTime) && endTime.isBefore(timeOffEndTime)){
					removed.add(timeLineDTO);
					added.add(new TimeLineDTO(timeOffStartTime, startTime));
					added.add(new TimeLineDTO(endTime, timeOffEndTime));
					continue;
				}
				if (startTime.isAfter(timeOffStartTime) && startTime.isBefore(timeOffEndTime)){
					timeLineDTO.setEndDateTime(startTime);
				}
				if (endTime.isAfter(timeOffStartTime) && endTime.isBefore(timeOffEndTime)){
					timeLineDTO.setStartDateTime(endTime);
				}
			}
			list.removeAll(removed);
			list.addAll(added);
		}

		List<StartTimeDTO> startTimeDTOs = Lists.newLinkedList();

		list = Ordering.natural().onResultOf(new Function<TimeLineDTO, Date>() {
			@Override
			public Date apply(TimeLineDTO input) {
				return input.getStartDateTime().toDate();
			}
		}).sortedCopy(list);
		for (TimeLineDTO timeLineDTO : list) {
			startTimeDTOs.addAll(generateListByTimeRange(timeLineDTO.getStartDateTime().toDate(), timeLineDTO.getEndDateTime().toDate()));
		}

		return startTimeDTOs;
	}

	/**
	 * Internal method invoked by generateStartTimeListByProfileAndDate to generate list by time range
	 *
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public List<StartTimeDTO> generateListByTimeRange(Date startTime, Date endTime) {

		if (startTime == null || endTime == null) {
			return Collections.emptyList();
		}

		List<StartTimeDTO> startTimeDTOs = Lists.newLinkedList();
		int minutes = new DateTime(endTime).getMinuteOfDay() - new DateTime(startTime).getMinuteOfDay();
		minutes = (int) Math.round(minutes / 15d);
		Date genDate = new DateTime(startTime).toDate();
		for (int i = 0; i <= minutes; i++) {
			if (i > 0) {
				genDate = new DateTime(genDate).plusMinutes(15).toDate();
			}
			String genTime = new DateTime(genDate).toString(DateTimeUtil.TIME_FORMAT_HH_MM);
			int genHour = Integer.parseInt(genTime.split(":")[0]);
			if (i == minutes && 0 == genHour) {
				startTimeDTOs.add(new StartTimeDTO("23:59", "12:00 AM"));
			} else if (genHour < 12) {
				String genTimeText = genTime + " AM";
				startTimeDTOs.add(new StartTimeDTO(genTime, genTimeText));
			} else if (12 == genHour) {
				String genTimeText = genTime + " PM";
				startTimeDTOs.add(new StartTimeDTO(genTime, genTimeText));
			} else {
				int hour = genHour - 12;
				int min = Integer.parseInt(genTime.split(":")[1]);
				String time = (hour < 10 ? ("0" + hour) : String.valueOf(hour)) + ":" + ((min == 0 ? ("00") : min));
				String genTimeText = time + " PM";
				startTimeDTOs.add(new StartTimeDTO(genTime, genTimeText));
			}
		}
		return startTimeDTOs;
	}

	@Override
	public List<Appointment> loadAppointmentListBySeriesAndStartTime(
			CreateAppointmentDTO dto, Appointment db) {
		List<Appointment> toBeUpdatedAppointmentList = new LinkedList<Appointment>();
		try {
			Criterion<Appointment, Appointment> criterion = AppointmentCriterion
					.findByAppointmentSeriesAndStartTime(db.getAppointmentSeries().getAppointmentSeriesId(), db.getStartTime());
			toBeUpdatedAppointmentList = appointmentDAO.search(criterion, DAOHelper.FETCH_APPOINTMENT_SERIES);
		} catch (Exception e) {
			LOG.error("Caught {} when loadAppointmentListBySeries", e);
		}
		return toBeUpdatedAppointmentList;
	}

	private List<Appointment> getLeftAppointmentList(List<Appointment> appointmentInSeriesList, List<Appointment> allAppointmentInSeries){
		List<Appointment> leftApptList = new ArrayList<Appointment>();
		leftApptList.addAll(allAppointmentInSeries);
		Iterator<Appointment> itr = appointmentInSeriesList.iterator();
		itr = appointmentInSeriesList.iterator();
		while(itr.hasNext()){
			leftApptList.remove((Appointment)itr.next());
		}
		return leftApptList;
	}

	private boolean isDeleteLastAppt(List<Appointment> appointmentInSeriesList, List<Appointment> allAppointmentInSeries){
		boolean isDeleteLastAppt = false;
		int size = allAppointmentInSeries.size();
		int lastIndex = size - 1;

		// Code changes made for GSSP-204
		if(lastIndex >= 0 )
		{
			Appointment lastAppointment = (Appointment)allAppointmentInSeries.get(lastIndex);
			Long lastApptId = lastAppointment==null?null:lastAppointment.getAppointmentId();
			Long curApptId;
			Appointment curAppointment;
			Iterator<Appointment> itr = appointmentInSeriesList.iterator();


			while(itr.hasNext()){
				curAppointment = (Appointment)itr.next();
				curApptId = curAppointment.getAppointmentId();
				if(curApptId == lastApptId && (Canceled.N.equals(curAppointment.getCanceled()) || Canceled.H.equals(curAppointment.getCanceled()) )){
					isDeleteLastAppt = true;
					break;
				}
			}

		}

		return isDeleteLastAppt;
	}

	//Including Cancel Reson for GSSP-250
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	@Override
	public boolean cancelAppointment(Long appointmentId, String cancelType,long cancelReason, Person updatedBy,String activityID,long serviceId) {
		Appointment db = appointmentDAO.get(appointmentId,DAOHelper.FETCH_APPOINTMENT_SERIES);
		AppointmentSeries series = db.getAppointmentSeries();

		//Get the appointment list by series id and current appointment date
		List<Appointment> appointmentInSeriesList =new ArrayList<Appointment>();
		if(CANCEL_SINGLE_APPOINTMENT.equals(cancelType.toString().trim().toLowerCase())){
			appointmentInSeriesList.add(db);
		}else{
			appointmentInSeriesList = this.findByStartDateAndAppointmentSeries(db.getStartTime(), series.getAppointmentSeriesId());
		}
		List<Appointment> allAppointmentInSeries = this.loadAllVliadAppointment(null, db);
		List<Appointment> notCanceledAppointmentList = getLeftAppointmentList(appointmentInSeriesList, allAppointmentInSeries);
		boolean isDeleteLastAppt = isDeleteLastAppt(appointmentInSeriesList,allAppointmentInSeries);
		try {
			if(CANCEL_SINGLE_APPOINTMENT.equals(cancelType.toString().trim().toLowerCase())) {
				for(Appointment a : allAppointmentInSeries) {
					if((Canceled.N.equals(a.getCanceled()) || Canceled.H.equals(a.getCanceled())) && a.getAppointmentId() != db.getAppointmentId()) {
						notCanceledAppointmentList.add(a);
					}
				}
				if(LOG.isDebugEnabled()) {
					LOG.debug("Getting to cancel one appointment by id", db.getAppointmentId());
				}
				//If this is a recurring appointment
				if(IsRecurring.Y.equals(series.getIsRecurring())) {
					//If the AppointmentSeries has a endDate
					if(null != series.getSeriesEndTime() && isDeleteLastAppt) {
						if(!notCanceledAppointmentList.isEmpty()) {
							if(!notCanceledAppointmentList.isEmpty()) {
								Date newEndDate = notCanceledAppointmentList.get(notCanceledAppointmentList.size() - 1).getStartTime();
								if(newEndDate.after(series.getSeriesStartTime())) {
									series.setSeriesEndTime(newEndDate);
									appointmentSeriesDAO.update(series, updatedBy.getPersonId());
								}
							} else {
								series.setSeriesEndTime(db.getStartTime());
								appointmentSeriesDAO.update(series, updatedBy.getPersonId());
							}
						}
						if(LOG.isDebugEnabled()) {
							LOG.debug("Canceling the last appontment id {} of seriesId {} with endDate, so the endDate of that series will modified to the previous appointment startDate", db.getAppointmentId(), series.getAppointmentSeriesId());
						}
					}
					//If cancel the last appointment of a series appointment list with no end date then set the appointment time to
					//the appointmentseries seriesEndTime
					if(null == series.getSeriesEndTime()) {
						boolean isAllCancelled = true;
						for(Appointment a : allAppointmentInSeries) {
							if((Canceled.N.equals(a.getCanceled()) || Canceled.H.equals(a.getCanceled())) && a.getAppointmentId() != db.getAppointmentId()) {
								isAllCancelled = false;
								break;
							}
						}
						//Set the endDate of the AppointmentSeries
						if(isAllCancelled) {
							series.setSeriesEndTime(db.getStartTime());
							appointmentSeriesDAO.update(series, updatedBy.getPersonId());
						}
					}

				}
				this.cancelSingleAppointment(db,cancelReason, updatedBy,activityID,serviceId);
			} else {
				//Load the appointment list by currrent appontnment time and appointmentseries
				for(Appointment a : appointmentInSeriesList) {
					this.cancelSingleAppointment(a,cancelReason, updatedBy,activityID,serviceId);
				}

				//If this is a recurring appointment
				if(IsRecurring.Y.equals(series.getIsRecurring())) {
					//If the AppointmentSeries has a endDate
					if(null != series.getSeriesEndTime()) {
						if(isDeleteLastAppt) {
							if(!notCanceledAppointmentList.isEmpty()) {
								Date newEndDate = notCanceledAppointmentList.get(notCanceledAppointmentList.size() - 1).getStartTime();
								if(newEndDate.after(series.getSeriesStartTime())) {
									series.setSeriesEndTime(newEndDate);
								}
							} else {
								series.setSeriesEndTime(db.getStartTime());
							}
							if(LOG.isDebugEnabled()) {
								LOG.debug("Canceling the last appontment id {} of seriesId {} with endDate, so the endDate of that series will modified to the previous appointment startDate", db.getAppointmentId(), series.getAppointmentSeriesId());
							}
						}
					} else {
						if(!notCanceledAppointmentList.isEmpty()) {
							series.setSeriesEndTime(notCanceledAppointmentList.get(notCanceledAppointmentList.size() - 1).getStartTime());
						} else {
							series.setSeriesEndTime(db.getStartTime());
						}
					}
				}
				appointmentSeriesDAO.update(series, updatedBy.getPersonId());
			}
		} catch (Exception e) {
			LOG.error("Caught exception {} when canceling appintment in AppointmentService.cancelAppointment", e);
			return false;
		}
		return true;
	}


	//Changes made for GSSP-250
	private void cancelSingleAppointment(Appointment app,long cancelReason, Person updateBy,String activityID,long serviceId) {
		app.setCanceled(Canceled.Y);
		appointmentDAO.update(app, updateBy.getPersonId());

		//GSSP-210 changes
		updateAppointmentLog(app, updateBy.getPersonId());

		//Changes made for GSSP-250
		createcancelAppointment(app.getAppointmentId(),cancelReason,activityID,serviceId);
	}

	private List<Appointment> findByStartDateAndAppointmentSeries(Date startTime, Long appointmentSeriesId) {
		List<Appointment> toBeUpdatedAppointmentList = new LinkedList<Appointment>();
		try {
			Criterion<Appointment, Appointment> criterion = AppointmentCriterion
					.findByAppointmentSeriesAndStartTime(appointmentSeriesId, startTime);
			toBeUpdatedAppointmentList = appointmentDAO.search(criterion, DAOHelper.FETCH_APPOINTMENT_SERIES);
		} catch (Exception e) {
			LOG.error("Caught {} when loadAppointmentListBySeries", e);
		}
		return toBeUpdatedAppointmentList;
	}

	@Transactional
	@Override
	public List<CalendarViewUnavailableHourDTO> getRoomViewUnavailableHourList(
			Long profileId, Date date) {
		List<CalendarViewUnavailableHourDTO> list = new LinkedList<CalendarViewUnavailableHourDTO>();
		try {

			LocationProfile profile = locationProfileDAO.get(profileId, DAOHelper.FETCH_AVAILABILITY);
			Availability a = profile.getAvailability();

			int dayOfWeek = new DateTime(date).getDayOfWeek();
			switch (dayOfWeek) {
				case 1://Monday
					list = getDivideEditHourListFromProfileOrInstructor(a.getMondayStartTime(), a.getMondayEndTime());
					break;
				case 2:
					list = getDivideEditHourListFromProfileOrInstructor(a.getTuesdayStartTime(), a.getTuesdayEndTime());
					break;
				case 3:
					list = getDivideEditHourListFromProfileOrInstructor(a.getWednesdayStartTime(), a.getWednesdayEndTime());
					break;
				case 4:
					list = getDivideEditHourListFromProfileOrInstructor(a.getThursdayStartTime(), a.getThursdayEndTime());
					break;
				case 5:
					list = getDivideEditHourListFromProfileOrInstructor(a.getFridayStartTime(), a.getFridayEndTime());
					break;
				case 6:
					list = getDivideEditHourListFromProfileOrInstructor(a.getSaturdayStartTime(), a.getSaturdayEndTime());
					break;
				default://Sunday
					list = getDivideEditHourListFromProfileOrInstructor(a.getSundayStartTime(), a.getSundayEndTime());
					break;
			}
		} catch (Exception e) {
			LOG.error("Caught exception {} when Generating RoomViewUnavailableHourDTO list from Availability error from profileId {}", e, profileId);
		}
		return list;
	}

	/**
	 * Genarate the unavailabe time that could not create appointment
	 *
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	private List<CalendarViewUnavailableHourDTO> getDivideEditHourListFromProfileOrInstructor(Date startTime, Date endTime) {
		List<CalendarViewUnavailableHourDTO> list = new LinkedList<CalendarViewUnavailableHourDTO>();

		if(null != startTime && null != endTime && !startTime.after(endTime)) {
			//Get the hour and minute of startTime
			DateTime startTimeJodatime = new DateTime(startTime);
			int startHour = startTimeJodatime.getHourOfDay();
			int startMinutes = startTimeJodatime.getMinuteOfHour();
			int durationOne = (startHour - 5) * 60 + startMinutes; //decides the height of grayed-out area in room view
			list.add(new CalendarViewUnavailableHourDTO("5:00", String.valueOf(durationOne)));

			//Get the hour and minute of endTime
			DateTime endTimeJodaTime = new DateTime(endTime);
			String endTimeStr = endTimeJodaTime.toString(DateTimeUtil.TIME_FORMAT_HH_MM);
			if(!"23:59".equals(endTimeStr.trim())) {
				int endHour = endTimeJodaTime.getHourOfDay();
				int endMinutes = endTimeJodaTime.getMinuteOfHour();
				int durationTwo = (24 - endHour) * 60 - endMinutes;
				list.add(new CalendarViewUnavailableHourDTO(endTimeStr, String.valueOf(durationTwo)));
			}
		}
		if(list.isEmpty()) {
			list.add(new CalendarViewUnavailableHourDTO("5:00", String.valueOf(DISABLED_DAY_DURATION)));
		}
		return list;
	}

	@Override
	public List<InstructorViewUnavailableHourDTOWrapper> getInstructorViewUnavailableHourList(
			Long profileId, Long[] instructorIds, Date date) {

		List<InstructorViewUnavailableHourDTOWrapper> resultList = new LinkedList<InstructorViewUnavailableHourDTOWrapper>();

		if(null != instructorIds && 0 != instructorIds.length) {
			for(long instructorId : instructorIds) {

				List<CalendarViewUnavailableHourDTO> list = new LinkedList<CalendarViewUnavailableHourDTO>();

				Criterion<Availability, Availability> criterion = AvailabilityCriterion.getByProfileIdAndInstructorId(profileId, instructorId);
				Availability a= availabilityDAO.get(criterion);
				int dayOfWeek = new DateTime(date).getDayOfWeek();

				switch (dayOfWeek) {
					case 1:
						list = getDivideEditHourListFromProfileOrInstructor(a.getMondayStartTime(), a.getMondayEndTime());
						resultList.add(new InstructorViewUnavailableHourDTOWrapper(String.valueOf(instructorId), list));
						break;
					case 2:
						list = getDivideEditHourListFromProfileOrInstructor(a.getTuesdayStartTime(), a.getTuesdayEndTime());
						resultList.add(new InstructorViewUnavailableHourDTOWrapper(String.valueOf(instructorId), list));
						break;
					case 3:
						list = getDivideEditHourListFromProfileOrInstructor(a.getWednesdayStartTime(), a.getWednesdayEndTime());
						resultList.add(new InstructorViewUnavailableHourDTOWrapper(String.valueOf(instructorId), list));
						break;
					case 4:
						list = getDivideEditHourListFromProfileOrInstructor(a.getThursdayStartTime(), a.getThursdayEndTime());
						resultList.add(new InstructorViewUnavailableHourDTOWrapper(String.valueOf(instructorId), list));
						break;
					case 5:
						list = getDivideEditHourListFromProfileOrInstructor(a.getFridayStartTime(), a.getFridayEndTime());
						resultList.add(new InstructorViewUnavailableHourDTOWrapper(String.valueOf(instructorId), list));
						break;
					case 6:
						list = getDivideEditHourListFromProfileOrInstructor(a.getSaturdayStartTime(), a.getSaturdayEndTime());
						resultList.add(new InstructorViewUnavailableHourDTOWrapper(String.valueOf(instructorId), list));
						break;
					default:
						list = getDivideEditHourListFromProfileOrInstructor(a.getSundayStartTime(), a.getSundayEndTime());
						resultList.add(new InstructorViewUnavailableHourDTOWrapper(String.valueOf(instructorId), list));
						break;
				}
			}
		}
		return resultList;
	}

	@Transactional
	@Override
	public List<InstructorViewUnavailableHourDTOWrapper> getInstructorViewUnavailableHourAndTimeoffList(
			Long pLocationId, Long pProfileId, Long[] pInstructorIds, Date pDate) {

		//All timeoff list from database
		TimeoffCriterion<TimeoffDateDTO> criterion = TimeoffCriterion.findAllTimeoffListByDate(pLocationId, pDate);
		List<TimeoffDateDTO> allTimeoffList = timeoffDAO.search(criterion);
		//TODO:: For 344 :: Goal to Block the Calender Day view for the Profile TimeOff.
		//1.Get the Profile Time Off based on ProfileID and Date  :: Write Similar method like getDisplayProfileTimeoffById but Restrictions should be equal.
		//If Not Full - meaning Studio Timeoff Existings for this particlar time to the store
		//Write a Util Method  Adjust/Add Studio Time Off to all the possible Instructors.
		//If Null Ignore and processed with existing flow.
		allTimeoffList = this.convertDateCrossingInTimeoff(allTimeoffList, pDate);

		DateTimeFormatter formatter = DateTimeFormat.forPattern(DateTimeUtil.DATE_PATTERN_SLASH);
		String queryDate = new DateTime(pDate).toString(formatter);

		//construct availability and add to timeoffdatedto list
		List<TimeoffDateDTO> allInstructorAvailabilityTimeoffDtoList = timeoffDAO.getTimeoffDateDTOByAvailabilityTime(queryDate, pLocationId);

		// GSSP-334 Grey out Code
		Format fr =  new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH);
		String pStrDate = fr.format(pDate);
		ProfileTimeOffDTO profileTimeOffDTO = profileTimeoffDAO.verifySingleAppointmentWithProfileTimeOff(pProfileId,pStrDate);
		long diffInMinutes =0L;
		String appStart =null;
		if(profileTimeOffDTO != null){
			appStart = profileTimeOffDTO.getFromTime();
			String appEnd = profileTimeOffDTO.getToTime();
			SimpleDateFormat sdf = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN);

			if(appStart != null && appEnd != null )
			{

				try {
					Date appStartDt = sdf.parse(appStart);
					Date appEndDt = sdf.parse(appEnd);

					long diff = appEndDt.getTime() - appStartDt.getTime();
					diffInMinutes = TimeUnit.MILLISECONDS.toMinutes(diff);
				} catch (ParseException e) {
					LOG.error("Error during  of DateFormat ", e);

				}
			}

		}



		//GCSS-729
		//get profile availability and add to all InstructorAvailability
		DateTime today = new DateTime(pDate);
		int dayOfWeek = today.getDayOfWeek();
		LocationProfile profile = locationProfileDAO.get(pProfileId, DAOHelper.FETCH_AVAILABILITY | DAOHelper.FETCH_SITE);
		Availability profileAvailability = profile.getAvailability();
		Date profileStartTimeByDayOfWeek = profileAvailability.getStartTimeByDayOfWeek(dayOfWeek);
		Date profileEndTimeByDayOfWeek = profileAvailability.getEndTimeByDayOfWeek(dayOfWeek);

		if (null != allInstructorAvailabilityTimeoffDtoList&&0!=allInstructorAvailabilityTimeoffDtoList.size()) {
			if (null == profileStartTimeByDayOfWeek|| null == profileEndTimeByDayOfWeek) {
				for (TimeoffDateDTO timeoffDateDTO : allInstructorAvailabilityTimeoffDtoList) {
					timeoffDateDTO.setStartTime(profileStartTimeByDayOfWeek);
					timeoffDateDTO.setEndTime(profileEndTimeByDayOfWeek);
				}
			} else {
				DateTime pStartTime = new DateTime(profileStartTimeByDayOfWeek);
				DateTime pEndTime = new DateTime(profileEndTimeByDayOfWeek);
				for (TimeoffDateDTO timeoffDateDTO : allInstructorAvailabilityTimeoffDtoList) {
					if(null==timeoffDateDTO.getStartTime()||null==timeoffDateDTO.getEndTime()){
						continue;
					}
					DateTime timeOffStartTime =  new DateTime(timeoffDateDTO.getStartTime());
					DateTime timeOffEndTime =  new DateTime(timeoffDateDTO.getEndTime());
					pStartTime=timeOffStartTime.withMillisOfSecond(0).withTime(pStartTime.getHourOfDay(),pStartTime.getMinuteOfHour(),pStartTime.getSecondOfMinute(),pStartTime.getMillisOfSecond());
					pEndTime=timeOffEndTime.withMillisOfSecond(0).withTime(pEndTime.getHourOfDay(),pEndTime.getMinuteOfHour(),pEndTime.getSecondOfMinute(),pEndTime.getMillisOfSecond());
					if(pStartTime.isAfter(timeOffStartTime)){
						timeoffDateDTO.setStartTime(pStartTime.toDate());
					}
					if(pEndTime.isBefore(timeOffEndTime)){
						timeoffDateDTO.setEndTime(pEndTime.toDate());
					}
				}
			}
		}
		List<InstructorViewUnavailableHourDTOWrapper> result = new ArrayList<InstructorViewUnavailableHourDTOWrapper>();

		//load one-time data
		Criterion<Onetime, Onetime> onetimeCriterion = OnetimeCriterion.findByInstructorsAndDate(queryDate, pInstructorIds);
		List<Onetime> onetimeList = onetimeDAO.search(onetimeCriterion);

		if(null != pInstructorIds && 0 != pInstructorIds.length) {

			for(Long instructorId : pInstructorIds) {

				//Iterate all instructor time off list to filter timeoff list for each instructor
				final Long id = instructorId;
				Collection<TimeoffDateDTO> coll1 = Collections2.filter(allTimeoffList, new Predicate<TimeoffDateDTO>(){
					@Override
					public boolean apply(TimeoffDateDTO input) {
						return id.toString().equals(input.getInstructorId().toString());
					}}
				);
				List<TimeoffDateDTO> newList = new ArrayList<TimeoffDateDTO>(coll1);

				//Iterate all instructor availability list to filter timeoff list for each instructor
				Collection<TimeoffDateDTO> coll2 = Collections2.filter(allInstructorAvailabilityTimeoffDtoList, new Predicate<TimeoffDateDTO>() {
					@Override
					public boolean apply(TimeoffDateDTO input) {
						return id.toString().equals(input.getInstructorId().toString());
					}
				});
				List<TimeoffDateDTO> newList2 = new ArrayList<TimeoffDateDTO>(coll2);
				//Handle the availability list
				List<TimeoffDateDTO> handledNewList2 = this.buildAvailabilityTimeoffDateDTOs(pDate, newList2);

				newList.addAll(handledNewList2);

				//Sort the list by startTime
				Collections.sort(newList, new TimeoffDateComparator());

				//Combines date if timeoff cover each other
				List<TimeoffDateDTO> handledDtoList = this.combineDate(newList);

				//Deal with one-time logic
				Collection<Onetime> currentInstructorOnetimes = Collections2.filter(onetimeList, new Predicate<Onetime>() {
							@Override
							public boolean apply(Onetime input) {
								return id.toString().equals(input.getInstructor().getInstructorId().toString());
							}
						}
				);

				if (null != profileStartTimeByDayOfWeek&& null != profileEndTimeByDayOfWeek) {
					DateTime profileStartTime = new DateTime(profileStartTimeByDayOfWeek);
					DateTime profileEndTime = new DateTime(profileEndTimeByDayOfWeek);
					for (Onetime onetime : currentInstructorOnetimes) {
						DateTime startTime = new DateTime(onetime.getStartTime());
						DateTime endTime = new DateTime(onetime.getEndTime());
						profileStartTime=startTime.withMillisOfSecond(0).withTime(profileStartTime.getHourOfDay(),profileStartTime.getMinuteOfHour(),profileStartTime.getSecondOfMinute(),profileStartTime.getMillisOfSecond());
						profileEndTime=endTime.withMillisOfSecond(0).withTime(profileEndTime.getHourOfDay(),profileEndTime.getMinuteOfHour(),profileEndTime.getSecondOfMinute(),profileEndTime.getMillisOfSecond());
						if(profileStartTime.isAfter(startTime)){
							startTime=profileStartTime;
						}
						if(profileEndTime.isBefore(endTime)){
							endTime=profileEndTime;
						}
						Set<TimeoffDateDTO> removed = Sets.newHashSet();
						Set<TimeoffDateDTO> added = Sets.newHashSet();
						for (TimeoffDateDTO timeoffDateDTO : handledDtoList) {
							DateTime timeOffStartTime =  new DateTime(timeoffDateDTO.getStartTime());
							DateTime timeOffEndTime =  new DateTime(timeoffDateDTO.getEndTime());
							if ((startTime.isBefore(timeOffStartTime) || startTime.isEqual(timeOffStartTime)) && (endTime.isAfter(timeOffEndTime) || endTime.isEqual(timeOffEndTime))){
								removed.add(timeoffDateDTO);
								continue;
							}
							if (startTime.isAfter(timeOffStartTime) && endTime.isBefore(timeOffEndTime)){
								removed.add(timeoffDateDTO);
								added.add(new TimeoffDateDTO(timeoffDateDTO.getInstructorId(),timeOffStartTime.toDate(), startTime.toDate()));
								added.add(new TimeoffDateDTO(timeoffDateDTO.getInstructorId(),endTime.toDate(), timeOffEndTime.toDate()));
								continue;
							}
							if (startTime.isEqual(timeOffStartTime) && endTime.isBefore(timeOffEndTime)){
								timeoffDateDTO.setStartTime(endTime.toDate());
								continue;
							}
							if (startTime.isAfter(timeOffStartTime) && startTime.isBefore(timeOffEndTime)){
								timeoffDateDTO.setEndTime(startTime.toDate());
							}
							if (endTime.isAfter(timeOffStartTime) && endTime.isBefore(timeOffEndTime)){
								timeoffDateDTO.setStartTime(endTime.toDate());
							}
						}
						handledDtoList.removeAll(removed);
						handledDtoList.addAll(added);
					}
				}

				//-- GSSP-334  Req 4	---------------
				//Build CalendarUnavailableHourDTO which consists of startTime and endTime
				if(diffInMinutes !=0L  &&  appStart != null){
					String duration = diffInMinutes+"";
					List<CalendarViewUnavailableHourDTO> calendarViewUnavailableHourDtos = this.buildCalendarViewUnavailableHourDtoList(handledDtoList,appStart,duration);
					result.add(new InstructorViewUnavailableHourDTOWrapper(String.valueOf(instructorId), calendarViewUnavailableHourDtos));
				}else{
					List<CalendarViewUnavailableHourDTO> calendarViewUnavailableHourDtos = this.buildCalendarViewUnavailableHourDtoList(handledDtoList);

					//Add CalendarUnavailableHourDTO to InstructorViewUnavailableHourDTOWrapper which consists of instructorId and CalendarViewUnavailableHourDTO list
					result.add(new InstructorViewUnavailableHourDTOWrapper(String.valueOf(instructorId), calendarViewUnavailableHourDtos));
				}
			}
		}
		return result;
	}

	/**
	 * Helper method to build availibility dto with current year month date
	 *
	 * @param pDate
	 * @param source
	 * @return
	 */
	private List<TimeoffDateDTO> buildAvailabilityTimeoffDateDTOs(Date pDate, List<TimeoffDateDTO> source) {

		List<TimeoffDateDTO> list = new ArrayList<TimeoffDateDTO>();

		//Enter initial start Time of the profiles eg. here it is 7 changed for LES-637
		Date initStartTime = new DateTime(pDate).withHourOfDay(5).withMinuteOfHour(0).withSecondOfMinute(0).toDate();
		Date initEndTime = new DateTime(pDate).withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(0).toDate();

		if(!source.isEmpty()) {
			for(TimeoffDateDTO dto : source) {
				Date st = dto.getStartTime();
				Date et = dto.getEndTime();
				if(null != st && null != et) {

					DateTime tmpSt = new DateTime(st);
					DateTime tmpEt = new DateTime(et);
					Date startTime = new DateTime(pDate).withHourOfDay(tmpSt.getHourOfDay()).withMinuteOfHour(tmpSt.getMinuteOfHour()).withSecondOfMinute(0).toDate();
					Date endTime = new DateTime(pDate).withHourOfDay(tmpEt.getHourOfDay()).withMinuteOfHour(tmpEt.getMinuteOfHour()).withSecondOfMinute(0).toDate();

					//Build the startTime dto
					list.add(new TimeoffDateDTO(initStartTime, startTime));
					//Build the endTime dto
					list.add(new TimeoffDateDTO(endTime, initEndTime));
				} else if(null == st && null == et) {
					list.add(new TimeoffDateDTO(initStartTime, initEndTime));
				}
			}
		} else {
			list.add(new TimeoffDateDTO(initStartTime, initEndTime));
		}
		return list;
	}

	/**
	 * Helper method to handle day crossing timeoff
	 *
	 * @param allTimeoffList
	 * @param pDate
	 * @return
	 */
	private List<TimeoffDateDTO> convertDateCrossingInTimeoff(List<TimeoffDateDTO> allTimeoffList, Date pDate) {
		
		//Enter initial start Time of the profiles eg. here it is 7 changed for LES-637
		Date currentStartDate = new DateTime(pDate).withHourOfDay(5).withMinuteOfHour(0).withSecondOfMinute(0).toDate();
		Date currentEndDate = new DateTime(pDate).withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).toDate();

		if(!allTimeoffList.isEmpty()) {
			for(TimeoffDateDTO dto : allTimeoffList) {

				if(dto.getStartTime().before(currentStartDate)) {
					//Enter initial start Time of the profiles eg. here it is 7 changed for LES-637
					Date startTime = new DateTime(pDate).withHourOfDay(5).withMinuteOfHour(0).withSecondOfMinute(0).toDate();
					dto.setStartTime(startTime);
				}
				if(dto.getEndTime().after(currentEndDate)) {
					Date endTime = new DateTime(pDate).withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).toDate();
					dto.setEndTime(endTime);
				}
			}
		}
		return allTimeoffList;
	}

	/**
	 * Build CalendarViewUnavailableHourDTO list which consists of fields of startTime and duration
	 *
	 * @param pList
	 * @return
	 */
	private List<CalendarViewUnavailableHourDTO> buildCalendarViewUnavailableHourDtoList(List<TimeoffDateDTO> pList,String startProfTime,String durationProf) {
		List<CalendarViewUnavailableHourDTO> list = new ArrayList<CalendarViewUnavailableHourDTO>();

		String MIDDLE_NIGHT_TIME = "23:59";
		DateTimeFormatter formatter = DateTimeFormat.forPattern("HH:mm");
		String startProfTimes = startProfTime;
		String durationProfs = durationProf;
		for(TimeoffDateDTO d : pList) {
			Date st = d.getStartTime();
			Date et = d.getEndTime();
			String startTime = new DateTime(st).toString(formatter);
			String endTime = new DateTime(et).toString(formatter);
			int duration = new DateTime(et).getMinuteOfDay() - new DateTime(st).getMinuteOfDay();
			if(MIDDLE_NIGHT_TIME.equals(endTime) && !MIDDLE_NIGHT_TIME.equals(startTime)) {
				duration += 1;
			} else if(MIDDLE_NIGHT_TIME.equals(endTime) && MIDDLE_NIGHT_TIME.equals(startTime)) {
				duration = 0;
			}
			String durationStr = String.valueOf(duration);
			if(0 != duration) {
				list.add(new CalendarViewUnavailableHourDTO(startTime, durationStr));
				list.add(new CalendarViewUnavailableHourDTO(startProfTimes, durationProfs));
			}
		}
		return list;
	}

	private List<CalendarViewUnavailableHourDTO> buildCalendarViewUnavailableHourDtoList(List<TimeoffDateDTO> pList) {
		List<CalendarViewUnavailableHourDTO> list = new ArrayList<CalendarViewUnavailableHourDTO>();
		String MIDDLE_NIGHT_TIME = "23:59";
		DateTimeFormatter formatter = DateTimeFormat.forPattern("HH:mm");
		for(TimeoffDateDTO d : pList) {
			Date st = d.getStartTime();
			Date et = d.getEndTime();
			String startTime = new DateTime(st).toString(formatter);
			String endTime = new DateTime(et).toString(formatter);
			int duration = new DateTime(et).getMinuteOfDay() - new DateTime(st).getMinuteOfDay();
			if(MIDDLE_NIGHT_TIME.equals(endTime) && !MIDDLE_NIGHT_TIME.equals(startTime)) {
				duration += 1;
			} else if(MIDDLE_NIGHT_TIME.equals(endTime) && MIDDLE_NIGHT_TIME.equals(startTime)) {
				duration = 0;
			}
			String durationStr = String.valueOf(duration);
			if(0 != duration) {
				list.add(new CalendarViewUnavailableHourDTO(startTime, durationStr));
			}
		}
		return list;
	}

	/**
	 * Combine date if dates in timeoff overlap each other
	 *
	 * @param list
	 * @return
	 */
	private List<TimeoffDateDTO> combineDate(List<TimeoffDateDTO> list) {

		Stack<Long> s = new Stack<Long>();
		Stack<Long> e = new Stack<Long>();

		s.push(0L);
		e.push(0L);

		for (TimeoffDateDTO time : list) {
			if (time.getStartTime().getTime() > e.peek()) { // no overlap
				s.push(time.getStartTime().getTime());
				e.push(time.getEndTime().getTime());
			} else if (time.getEndTime().getTime() >= e.peek()) { // partial overlap
				e.pop();
				e.push(time.getEndTime().getTime());
			} else if (time.getStartTime().getTime() <= s.peek()) {
				s.pop();
				s.push(time.getStartTime().getTime());
			}
		}
		List<TimeoffDateDTO> dtos = new ArrayList<TimeoffDateDTO>();
		while (!s.isEmpty()) {
			if (0 != s.peek() && 0 != e.peek() && (0 != (int)(e.peek() - s.peek()))) {
				dtos.add(new TimeoffDateDTO(new Date(s.peek()), new Date(e.peek())));
			}
			s.pop();
			e.pop();
		}
		return dtos;
	}


	@Transactional
	@Override
	public List<WeekViewUnavailableHourDTOWrapper> getWeekViewUnavailableHourDTOMap(
			Long profileId, String date) {

		List<WeekViewUnavailableHourDTOWrapper> wrappers = new LinkedList<WeekViewUnavailableHourDTOWrapper>();

		try {
			LocationProfile profile = locationProfileDAO.get(profileId, DAOHelper.FETCH_AVAILABILITY);
			Availability a = profile.getAvailability();

			//Get the first day and last day of current week
			String validDate = date.split(SPLITOR_DASH)[0];
			String[] dtArr = validDate.split(SPLITOR_SLASH);
			Date currDate = new DateTime(Integer.parseInt(dtArr[2]), Integer.parseInt(dtArr[0]), Integer.parseInt(dtArr[1]), 0, 0, 0).toDate();
			List<Date> dateList = this.dateToWeek(currDate);
			Calendar c = Calendar.getInstance();

			//Get the WeekViewUnavailableHourDTOWrapper which consists of week=true and a CalendarViewUnavailableHourDTO list for each day of current week
			for(Date d : dateList) {

				List<CalendarViewUnavailableHourDTO> list = new LinkedList<CalendarViewUnavailableHourDTO>();

				c.setTime(d);
				int dayOfWeek = c.get(Calendar.DAY_OF_WEEK);

				switch (dayOfWeek) {
					case 1:
						list = getDivideEditHourListFromProfileOrInstructor(a.getSundayStartTime(), a.getSundayEndTime());
						wrappers.add(new WeekViewUnavailableHourDTOWrapper(list));
						break;
					case 2:
						list = getDivideEditHourListFromProfileOrInstructor(a.getMondayStartTime(), a.getMondayEndTime());
						wrappers.add(new WeekViewUnavailableHourDTOWrapper(list));
						break;
					case 3:
						list = getDivideEditHourListFromProfileOrInstructor(a.getTuesdayStartTime(), a.getTuesdayEndTime());
						wrappers.add(new WeekViewUnavailableHourDTOWrapper(list));
						break;
					case 4:
						list = getDivideEditHourListFromProfileOrInstructor(a.getWednesdayStartTime(), a.getWednesdayEndTime());
						wrappers.add(new WeekViewUnavailableHourDTOWrapper(list));
						break;
					case 5:
						list = getDivideEditHourListFromProfileOrInstructor(a.getThursdayStartTime(), a.getThursdayEndTime());
						wrappers.add(new WeekViewUnavailableHourDTOWrapper(list));
						break;
					case 6:
						list = getDivideEditHourListFromProfileOrInstructor(a.getFridayStartTime(), a.getFridayEndTime());
						wrappers.add(new WeekViewUnavailableHourDTOWrapper(list));
						break;
					default:
						list = getDivideEditHourListFromProfileOrInstructor(a.getSaturdayStartTime(), a.getSaturdayEndTime());
						wrappers.add(new WeekViewUnavailableHourDTOWrapper(list));
						break;
				}
			}
		} catch (NumberFormatException e) {
			LOG.error("Caught exception {} when getting the unavailable edit hour in week view", e);
		}
		return wrappers;
	}

	@Transactional
	@Override
	public List<ExportAppointmentDTO> getExportLessionCSVData(long pLocationId,
															  Date pStartTime, Date pEndTime) {
		Criterion<Appointment, ExportAppointmentDTO> criterion = AppointmentCriterion.searchAppointmentByLocationIdAndDateTime(pLocationId, pStartTime, pEndTime);
		return appointmentDAO.search(criterion);
	}

	//249 customer updated appointments
	//Changes for GSSP-	278- cancel reason message fetching method starts here
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
		/*public List<AppointmentCancelReason> getCancelReasonCode(long cancleReasonId) {


				return appointmentDAO.getCancelReasonCode(cancleReasonId);


		}*/

	public String findInstructorName(CreateAppointmentDTO dto) {
		String instructorName = "Not Applicable";
		if (null != dto.getInstructorId())
		{

			Instructor instructor = instructorService.getInstructor(dto.getInstructorId());

			instructorName =( org.apache.commons.lang.StringUtils.isBlank(instructor.getPerson().getFirstName())?
					"":instructor.getPerson().getFirstName()) + " "
					+ (org.apache.commons.lang.StringUtils.isBlank(instructor.getPerson().getLastName())?"":
					instructor.getPerson().getLastName());


		}
		return instructorName;
	}
	//Changes for GSSP-	278 ends
	@Transactional
	@Override
	public List<WeekViewUnavailableHourDTOWrapper> getWeekViewUnavailableHourDTOMapByOneInstructor(
			Long pLocationId, Long profileId, Long[] instructorIds, String date) {
		List<WeekViewUnavailableHourDTOWrapper> wrappers = new LinkedList<WeekViewUnavailableHourDTOWrapper>();
		//Get the first day and last day of current week
		String validDate = date.split(SPLITOR_DASH)[0];
		String[] dtArr = validDate.split(SPLITOR_SLASH);
		Date currDate = new DateTime(Integer.parseInt(dtArr[2]), Integer.parseInt(dtArr[0]), Integer.parseInt(dtArr[1]), 0, 0, 0).toDate();
		List<Date> dateList = this.dateToWeek(currDate);
		for(Date d : dateList) {
			List<InstructorViewUnavailableHourDTOWrapper> instructorViewUnavailableHourDTOWrappers = getInstructorViewUnavailableHourAndTimeoffList(pLocationId, profileId,instructorIds, d);
			List<CalendarViewUnavailableHourDTO> list = new LinkedList<CalendarViewUnavailableHourDTO>();
			list=instructorViewUnavailableHourDTOWrappers.get(0).getList();
			wrappers.add(new WeekViewUnavailableHourDTOWrapper(list));
		}
		return wrappers;
	}


	/**
	 * Method to update the single appointment-Phase2-UPDATE LESSON SERVICE
	 *
	 *
	 */


	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	@Override
	public void updateLessonServiceAppointment(Appointment app, Long personID) {

		appointmentDAO.update(app, personID);
	}


	//Code added for GSSP-188
	/**
	 * Load Start Time for by instructor and  date
	 *
	 * @param instructorId
	 * @return
	 */


	@Override
	public  List<StartTimeDTO> getStartTimeByInstructor(Long profileId, Long instructorId, Date startDate) {

		DateTime today = new DateTime(startDate);
		int dayOfWeek = today.getDayOfWeek();

		TimeLineDTO instructorTimeLine = null;

		Criterion<Availability, Availability> criterion = AvailabilityCriterion.getByProfileIdAndInstructorId(profileId, instructorId);
		Availability instructorAvailability = availabilityDAO.get(criterion);
		Date instructorStartTimeByDayOfWeek = instructorAvailability.getStartTimeByDayOfWeek(dayOfWeek);
		Date instructorEndTimeByDayOfWeek = instructorAvailability.getEndTimeByDayOfWeek(dayOfWeek);

		if (instructorStartTimeByDayOfWeek != null || instructorEndTimeByDayOfWeek != null) {
			DateTime instructorStartDateTime = new DateTime(instructorStartTimeByDayOfWeek);
			DateTime instructorEndDateTime = new DateTime(instructorEndTimeByDayOfWeek);
			instructorTimeLine = new TimeLineDTO(
					today.withTime(instructorStartDateTime.getHourOfDay(), instructorStartDateTime.getMinuteOfHour(),
							instructorStartDateTime.getSecondOfMinute(), instructorStartDateTime.getMillisOfSecond()),
					today.withTime(instructorEndDateTime.getHourOfDay(), instructorEndDateTime.getMinuteOfHour(),
							instructorEndDateTime.getSecondOfMinute(), instructorEndDateTime.getMillisOfSecond())
			);
		}

		List<TimeLineDTO> list = Lists.newLinkedList();

		if (instructorTimeLine != null) {
			list.add(instructorTimeLine);
		}


		List<StartTimeDTO> startTimeDTOs = Lists.newLinkedList();

		list = Ordering.natural().onResultOf(new Function<TimeLineDTO, Date>() {
			@Override
			public Date apply(TimeLineDTO input) {
				return input.getStartDateTime().toDate();
			}
		}).sortedCopy(list);
		for (TimeLineDTO timeLineDTO : list) {
			startTimeDTOs.addAll(generateListByTimeRangeForTimeoff(timeLineDTO.getStartDateTime().toDate(), timeLineDTO.getEndDateTime().toDate()));
		}

		return startTimeDTOs;
	}



	//Code added for GSSP-188
	public List<StartTimeDTO> generateListByTimeRangeForTimeoff(Date startTime, Date endTime) {

		if (startTime == null || endTime == null) {
			return Collections.emptyList();
		}

		List<StartTimeDTO> startTimeDTOs = Lists.newLinkedList();
		int minutes = new DateTime(endTime).getMinuteOfDay() - new DateTime(startTime).getMinuteOfDay();
		minutes = (int) Math.round(minutes / 30d);
		Date genDate = new DateTime(startTime).toDate();
		for (int i = 0; i <= minutes; i++) {
			if (i > 0) {
				genDate = new DateTime(genDate).plusMinutes(30).toDate();
			}
			String genTime = new DateTime(genDate).toString(DateTimeUtil.TIME_FORMAT_HH_MM);
			int genHour = Integer.parseInt(genTime.split(":")[0]);
			if (i == minutes && 0 == genHour) {
				startTimeDTOs.add(new StartTimeDTO("23:59", "12:00 AM"));
			} else if (genHour < 12) {
				String genTimeText = genTime + " AM";
				startTimeDTOs.add(new StartTimeDTO(genTime, genTimeText));
			} else if (12 == genHour) {
				String genTimeText = genTime + " PM";
				startTimeDTOs.add(new StartTimeDTO(genTime, genTimeText));
			} else {
				int hour = genHour - 12;
				int min = Integer.parseInt(genTime.split(":")[1]);
				String time = (hour < 10 ? ("0" + hour) : String.valueOf(hour)) + ":" + ((min == 0 ? ("00") : min));
				String genTimeText = time + " PM";
				startTimeDTOs.add(new StartTimeDTO(genTime, genTimeText));
			}
		}
		return startTimeDTOs;
	}

	@Override
	public  void updateAppointmentLog(Appointment app,Long personId)
	{

		//GSSP-210 changes
		AppointmentLog appointmentLog = new AppointmentLog();

		appointmentLog = new AppointmentLog();

		appointmentLog.setAppointmentId(app.getAppointmentId());

		appointmentLog.setSiteID(app.getSite().getSiteId());
		appointmentLog.setActivityID(app.getActivity().getActivityId());
		appointmentLog.setCanceled(app.getCanceled());
		appointmentLog.setDuration(app.getDuration());
		appointmentLog.setEndTime(app.getEndTime());

		if(null !=  app.getInstructor())
			appointmentLog.setInstructorID(app.getInstructor().getInstructorId());

		appointmentLog.setNote(app.getNote());
		appointmentLog.setRoomID(app.getRoom().getRoomId());
		appointmentLog.setSiteID(app.getSite().getSiteId());
		appointmentLog.setStartTime(app.getStartTime());

		StringBuffer sb = new StringBuffer();


		if(null != app.getCustomers() && app.getCustomers().size() > 0 )
		{
			Set<Customer> customerSet = app.getCustomers();
			for(Customer customer : customerSet)
			{

				sb.append(customer.getCustomerId());
				sb.append(";");
			}
		}

		appointmentLog.setCustomerId(sb.toString());

		appointmentLog.setUpdatedBy(personId);

		// GSSP- 332 Duplicate cancel reasom code.
		appointmentLogDAO.saveEntity(appointmentLog, new Person());

	}

	public static boolean checkProfileTimeOffMatchesonRecurring(List<ProfileTimeOffDTO> profileTimeOffList,Date appointmentStart,Date appointmentEnd){
		boolean flag = true;
		try {

			DateFormat sdf = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN);
			Format fr =  new SimpleDateFormat("MM/dd/yyyy");
			Format f =  new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN);

			String strFromTime = f.format(appointmentStart);
			strFromTime =DateTimeUtil.addMinuteOnString(strFromTime,1);
			String strToTime = f.format(appointmentEnd);
			strToTime =DateTimeUtil.addMinuteOnString(strToTime,-1);
			String strDate = fr.format(appointmentStart);

			Date appStart = sdf.parse(strFromTime);
			Date appEnd = sdf.parse(strToTime);
			Date profileStart = null;
			Date profileEnd = null;


			for(ProfileTimeOffDTO pto:profileTimeOffList){

				if(strDate.equals(pto.getFromDate())){
					profileStart =  sdf.parse(pto.getFromTime());
					profileEnd = sdf.parse(pto.getToTime());

					if(profileStart.before(profileEnd) && appStart.before(appEnd)){
						if(!appStart.after(profileEnd))
							flag = false;
						else if (!appEnd.before(profileStart)){
							flag = false;
						}
						//TODO:: Again as described on ProfileDesc.
						if(appStart.after(profileEnd) && appEnd.after(profileEnd)){
							flag = true;
						}
						if(appStart.before(profileStart) && appEnd.before(profileStart)){
							flag = true;
						}
					}

				}

			}

		} catch (Exception e) {
			LOG.error("Caught an exception from checkProfileTimeOffMatchesonRecurring with profile timeOff" + CREATE_APP_LOG + " {}", e);
			flag = true;
		}



		return flag;
	}

	//GSSP-363
	@Override
	public List<InstructorLessonLinkDTO> getInstructorLessonViewList(List<String> personList,Date startDate,Date endDate){
		if (LOG.isDebugEnabled()) {
			LOG.debug("Query getInstructorLessonViewList list by locationId {} and time range in {} and {}", personList, startDate);
		}

		List<InstructorLessonLinkDTO> ListinstructorLessonLinkDTO = null;
		Criterion<Appointment, InstructorLessonLinkDTO> criterion = AppointmentCriterion.findInstructorScheduleByLocationIdAndInstructorId(personList,startDate, endDate);
		ListinstructorLessonLinkDTO = appointmentDAO.search(criterion);

		if (ListinstructorLessonLinkDTO == null) {
			return Collections.emptyList();
		}

		return ListinstructorLessonLinkDTO;
	}

	//GSSP-368
	@Override
	public List<AppointmentBookDTO> getAppointmentBookByLocationAndDate(Long pLocationId, Date startTime, Date endTime){
		if (LOG.isDebugEnabled()) {
			LOG.debug("Query getAppointmentBookByDate list by locationId {} and time range in {} and {}", pLocationId, startTime, endTime);
		}

		List<AppointmentBookDTO> listAppointmentBookDTO = null;
		Criterion<Appointment, AppointmentBookDTO> criterion = AppointmentCriterion.findAppointmentBookByDate(pLocationId, startTime, endTime);
		listAppointmentBookDTO = appointmentDAO.search(criterion);

		if (listAppointmentBookDTO == null) {
			return Collections.emptyList();
		}

		return listAppointmentBookDTO;
	}

	//LES-624
	@Override
	public List<CustomerAppointmentDTO> getAppointmentBookByCustomerIdAndDate(Long customerId, Date startTime, Date endTime) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("Query getAppointmentBookByDate list by customerId {} and time range in {} and {}", customerId ,  startTime, endTime);
		}

		List<CustomerAppointmentDTO> listCustomerAppointment = null;
		Criterion<Appointment, CustomerAppointmentDTO> criterion = AppointmentCriterion.findCustomerAppointmentsByCustomerIdAndDate(customerId, startTime, endTime);
		listCustomerAppointment = appointmentDAO.search(criterion);

		if (listCustomerAppointment == null) {
			return Collections.emptyList();
		}

		return listCustomerAppointment;
	}
	
	
	@Override
	public List<CustomerAppointmentDetailsResultDTO> loadByCustomerIdAndDate(String customerId, Date startTime, Date endTime) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("AppointmentService.loadByCustomer: start");
		}
		List<CustomerAppointmentDetailsResultDTO> list = new LinkedList<CustomerAppointmentDetailsResultDTO>();
		try {
			Criterion<Appointment, CustomerAppointmentDetailsResultDTO> criterion = AppointmentCriterion.findByCustomerAndDate(customerId,
					startTime, endTime);
			list = appointmentDAO.search(criterion);
		} catch (Exception e) {
			LOG.error("Caught an exception from AppointmentService.loadByCustomer: {}", e);
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("AppointmentService.loadByCustomer: end");
		}
		return list;
	}

	@Override
	public PauseServiceUpdateResponseDTO pauseCustomer(PauseServiceUpdateDTO pauseServiceUpdateDTO){
		if (LOG.isDebugEnabled()) {
			LOG.debug("AppointmentService.pauseCustomer: start");
		}


		List<CustomerAppointmentDetailsResultDTO> list = new LinkedList<CustomerAppointmentDetailsResultDTO>();
		try {
			// Here you would implement the actual business logic
			// For now, we're just returning a success response
			// TODO: Implement the following steps in future:
			// 2. Get all the customer Future appt series after enddate
			//AppointmentCritera.findCustomerAppointmentsAndSeriesByCustomerIdAndDate
			//findCustomerAppointmentsAndSeriesByCustomerIdAndDate

			// 3. If single appts cancel it
			// 4. If recurring appt update the series end date to the inputEnd
			//public void pauseupdateAppointment(CreateAppointmentDTO dto, Appointment db, Person person, Appointment appObj) {

			//pauseAppointment(dto, appFromDb, person,appObj);

		}

		catch (Exception e) {
			LOG.error("Caught an exception from AppointmentService.pauseCustomer: {}", e);
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("AppointmentService.pauseCustomer: end");
		}
		//return list;
		return null;
	}

	@Override
	public String getTransactionIdByAppointmentId(Long appointmentId) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("AppointmentService.getTransactionIdByAppointmentId: start, appointmentId: {}", appointmentId);
		}
		String transactionId = null;
		try {
			if (appointmentId != null) {
				Criterion<Appointment, String> criterion = AppointmentCriterion.findTransactionIdByAppointmentId(appointmentId);
				transactionId = appointmentDAO.get(criterion);
			}
		} catch (Exception e) {
			LOG.error("Caught an exception from AppointmentService.getTransactionIdByAppointmentId: {}", e);
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("AppointmentService.getTransactionIdByAppointmentId: end, returning transactionId: {}", transactionId);
		}
		return transactionId;
	}

}
