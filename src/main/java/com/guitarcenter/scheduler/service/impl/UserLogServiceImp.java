package com.guitarcenter.scheduler.service.impl;

import java.util.Date;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.UserLogDAO;
import com.guitarcenter.scheduler.dto.UserLogDTO;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.UserLog;
import com.guitarcenter.scheduler.service.UserLogService;

@Service("userLogService")
public class UserLogServiceImp implements UserLogService{
    Logger log = LoggerFactory.getLogger(UserLogServiceImp.class);
    
	@Resource
	private UserLogDAO userLogDAO;
 
	 /**
	 * Use for to save the user log details to User Log table.
	 * UserLogDTO contains login information 
	 * values of one user
	 */
    @Override
    @Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
    public void saveUserLogDetails(UserLogDTO userLogDTO){
    	userLogDTO.setVersion(0);
        userLogDTO.setLoginTimeStamp(new Date());
		userLogDTO.setUpdated(new Date());
    	UserLog userLog = new UserLog(userLogDTO.getPersonID(),userLogDTO.getVersion(),userLogDTO.getUpdated(),
    			userLogDTO.getLoginTimeStamp(),userLogDTO.getLogOutTimeStamp(),userLogDTO.getRoleId(),userLogDTO.getLocationExternalId());
    	userLogDAO.save(userLog,new  Person());
    }
 
}
