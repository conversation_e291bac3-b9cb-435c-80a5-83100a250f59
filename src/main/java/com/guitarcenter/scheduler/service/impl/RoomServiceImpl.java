package com.guitarcenter.scheduler.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.RoomDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.RoomCriterion;
import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.dto.ConflictingAppointmentListDTO;
import com.guitarcenter.scheduler.dto.RoomDTO;
import com.guitarcenter.scheduler.dto.ServiceDTO;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.SplitRoom;
import com.guitarcenter.scheduler.service.ActivityService;
import com.guitarcenter.scheduler.service.AppointmentService;
import com.guitarcenter.scheduler.service.RoomService;
import com.guitarcenter.scheduler.service.ServiceService;

@Service("roomService")
public class RoomServiceImpl implements RoomService {
	
	private static final Logger LOG = LoggerFactory.getLogger(RoomServiceImpl.class);
	
	@Autowired
	private RoomDAO roomDAO;
	
	@Resource
	private ActivityService activityService;
	@Resource
	private ServiceService serviceService;
	
	@Autowired
	private AppointmentService appointmentService;
	
	@Override
	public List<Room> loadRoomList(long profileId) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("RoomService.loadRoomList: start");
		}
		List<Room> roomList = new LinkedList<Room>();
		try {
			Criterion<Room, Room> roomCriterion = RoomCriterion.findByLocationProfileId(profileId);
			roomList = roomDAO.search(roomCriterion);
		} catch (RuntimeException e) {
			LOG.error("RoomService.loadRoomList: failure");
		}
		if(LOG.isDebugEnabled()) {
			LOG.debug("RoomService.loadRoomList: end");
		}
		return roomList;
	}

	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public boolean checkRoomByAppointmentTime(boolean recurring, long roomId, String startDate, String endDate, String startTime, String endTime, long profileId) {
		List<Room> splitRoomList = roomDAO.getSplitRoomsByParentId(roomId);
		Room room = this.getRoom(roomId);
		if(null == room.getParentRoom()){
			boolean check1 = checkAppointRoomByAppointmentTime(recurring, roomId, startDate, endDate, startTime, endTime, profileId);
			if(splitRoomList.size()==0){
				return check1;
			}else{
				boolean check2 = checkSplitRoom(recurring, splitRoomList, startDate, endDate, startTime, endTime, profileId);
				return check1 && check2;
			}
		}else{
			boolean check1 = checkAppointRoomByAppointmentTime(recurring, roomId, startDate, endDate, startTime, endTime, profileId);
			boolean check2 = checkAppointRoomByAppointmentTime(recurring, room.getParentRoom().getRoomId(), startDate, endDate, startTime, endTime, profileId);
			return check1 && check2;
		}
	}
	
	private boolean checkAppointRoomByAppointmentTime(boolean recurring, long roomId, String startDate, String endDate, String startTime, String endTime, long profileId){
		if(recurring && !startDate.equals(endDate)){
			return roomDAO.getRoomByAppointmentRecurringTime(roomId, startDate, endDate, startTime, endTime, profileId)==null?false:true;
		}else{
			return roomDAO.getRoomByAppointmentTime(roomId, startDate, startTime, endTime, profileId)==null?false:true;
		}
	}
	
	private boolean checkSplitRoom(boolean recurring, List<Room> roomList, String startDate, String endDate, String startTime, String endTime, long profileId){
		for(Room room : roomList){
			long roomId = room.getRoomId();
			if(!checkAppointRoomByAppointmentTime(recurring, roomId, startDate, endDate, startTime, endTime, profileId))
				return false;
		}
		return true;
	}

	//GSSP-241 Room
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	
	public ConflictingAppointmentListDTO checkRoomByConflictingAppointmentRecurringTime(long roomId, String startDate, String endDate, String startTime, String endTime, long profileId){
		
		Criterion<Room, ConflictingAppointmentListDTO> roomCriterion = RoomCriterion.findConflictAppointmentsByRoom(
				profileId,startDate,endDate,startTime,endTime,"N",roomId) ;

		return roomDAO.get(roomCriterion);
	
	}
	//GSSP-241 Instructor
		
		@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
		
		public ConflictingAppointmentListDTO checkInstructorByConflictingAppointmentRecurringTime(long profileId, String startDate, String endDate, String startTime, String endTime,Long InstructorId){
			
			Criterion<Room, ConflictingAppointmentListDTO> roomCriterion = RoomCriterion.findConflictAppointmentsByInstructor(profileId,startDate,endDate,startTime,endTime,InstructorId) ;

			return roomDAO.get(roomCriterion);
		
		}
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public boolean checkRoomByActivity(long roomId, long activityId) {
		Room r = roomDAO.get(roomId);
		List<ActivityDTO> activityList = activityService.getActivityDTOByProfileRoom(r.getLocationProfile().getProfileId(), roomId);
		for(ActivityDTO a : activityList){
			if(activityId == a.getActivityId()){
				return true;
			}
		}
		return false;
	}
	
	@Override
	public List<Room> getRoomListByIds(Long... ids) {
		Criterion<Room, Room> criterion = RoomCriterion.findByRoomIds(ids);
		return roomDAO.search(criterion);
	}
    
    @Override
    @Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
    public long createRoom(Room room){
        return roomDAO.save(room, room.getUpdatedBy());
    }
    
    private RoomDTO getRoomDTO(Room room){
		StringBuilder aString = new StringBuilder();
		List<ActivityDTO> activityList = activityService.getActivityDTOByProfileRoom(room.getLocationProfile().getProfileId(), room.getRoomId());
		for (ActivityDTO a : activityList) {
            aString.append(a.getActivityName());
            aString.append(SystemUtil.SPLIT_COMMA);
		}
        if (aString.length() > 1) {
            aString.deleteCharAt(aString.length() - 1);
        }
		StringBuilder sString = new StringBuilder();
		List<ServiceDTO> serviceList = serviceService.getServiceDTOByProfileRoom(room.getLocationProfile().getProfileId(), room.getRoomId());
		for (ServiceDTO s : serviceList) {
            sString.append(s.getServiceName());
            sString.append(SystemUtil.SPLIT_COMMA);
		}
        if (sString.length() > 1) {
            sString.deleteCharAt(sString.length() - 1);
        }
		RoomDTO dto = new RoomDTO(room.getRoomId(), room.getRoomSize()==null?SystemUtil.DIAPLAY_NULL:room.getRoomSize().getRoomSizeName(),
				room.getRoomNumber().getRoomNumber(), room.getRoomTemplate()==null?SystemUtil.DIAPLAY_NULL:room.getRoomTemplate().getRoomTemplateName(),
						SplitRoom.Y.equals(room.getIsSplitRoom())?SystemUtil.DIAPLAY_SELECT_ON:null, room.getProfileRoomName(), Enabled.Y.equals(room.getEnabled())?SystemUtil.DIAPLAY_SELECT_ON:null, aString.toString(), sString.toString());
    	return dto;
    }
    
    @Override
    @Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
    public RoomDTO getRoomDTO(long roomId){
    	Room room = roomDAO.get(roomId);
		return getRoomDTO(room);
    }
    
    private RoomDTO getEditRoomDTO(Room r){
    	if(r==null){
    		return null;
    	}
		List<ActivityDTO> activityList = activityService.getActivityDTOByProfileRoom(r.getLocationProfile().getProfileId(), r.getRoomId());
		StringBuilder aSb = new StringBuilder();
		for (ActivityDTO a : activityList) {
			aSb.append(a.getActivityId());
			aSb.append(SystemUtil.SPLIT_COMMA);
		}
        if (aSb.length() > 1) {
        	aSb.deleteCharAt(aSb.length() - 1);
        }
		List<ServiceDTO> serviceList = serviceService.getServiceDTOByProfileRoom(r.getLocationProfile().getProfileId(), r.getRoomId());
		StringBuffer sSb = new StringBuffer();
		for (ServiceDTO s : serviceList) {
			sSb.append(s.getServiceId());
			sSb.append(SystemUtil.SPLIT_COMMA);
		}
        if (sSb.length() > 1) {
        	sSb.deleteCharAt(sSb.length() - 1);
        }
        RoomDTO dto = new RoomDTO(r.getRoomId(), r.getRoomSize()==null?"":r.getRoomSize().getRoomSizeId().toString(), r.getRoomNumber().getRoomNumberId().toString(),
				r.getRoomTemplate()==null?"":r.getRoomTemplate().getRoomTemplateId().toString(), SplitRoom.Y.equals(r.getIsSplitRoom())?SystemUtil.DIAPLAY_SELECT_ON:null, r.getProfileRoomName(), Enabled.Y.equals(r.getEnabled())?SystemUtil.DIAPLAY_SELECT_ON:null, aSb.toString(), sSb.toString());
    	dto.setParentRoom(r.getParentRoom()==null?null:r.getParentRoom().getRoomId());
		dto.setActivityList(activityList);
    	dto.setServiceList(serviceList);
		return dto;
    }
    
	/**
	  * <p>Title: getRoomList</p>
	  * <p>Description: </p>
	  * @param profileId
	  * @return
	  * @see com.guitarcenter.scheduler.service.RoomService#getRoomList(long)
	  */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public List<RoomDTO> getRoomList(long profileId) {
		List<RoomDTO> l = new ArrayList<RoomDTO>();
		List<Room> list = this.loadRoomList(profileId);
		for (Room r : list) {
			RoomDTO dto = this.getRoomDTO(r);
			l.add(dto);
		}
		return l;
	}
	
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	@Override
	public List<RoomDTO> getRoomListByServiceType(long serviceId) {
		Criterion<Room, Room> criterion = RoomCriterion.findByServiceId(serviceId);
		List<Room> list = roomDAO.search(criterion);
		List<RoomDTO> dtos = new ArrayList<RoomDTO>();
		if(null != list && !list.isEmpty()) {
			for(Room r: list) {
				RoomDTO dto = new RoomDTO(r.getRoomId(), r.getProfileRoomName());
				dtos.add(dto);
			}
		}
		return dtos;
	}
	
	/**
	  * <p>Title: deleteRoom</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @see com.guitarcenter.scheduler.service.RoomService#deleteRoom(long)
	  */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public void deleteRoom(Room room) {
		roomDAO.delete(room);
	}

	/**
	  * <p>Title: updateRoom</p>
	  * <p>Description: </p>
	  * @param room
	  * @see com.guitarcenter.scheduler.service.RoomService#updateRoom(com.guitarcenter.scheduler.model.Room)
	  */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public RoomDTO updateRoom(Room room) {
		roomDAO.update(room, room.getUpdatedBy());
		return this.getRoomDTO(room.getRoomId());
	}
	/**
	 * 
	  * <p>Title: getRoomDTOByRoomId</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @return
	  * @see com.guitarcenter.scheduler.service.RoomService#getRoomDTOByRoomId(long)
	 */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public RoomDTO getRoomDTOByRoomId(long roomId) {
		Room room = roomDAO.get(roomId);
		return this.getEditRoomDTO(room);
	}

	/**
	  * <p>Title: deleteSplitRooms</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @see com.guitarcenter.scheduler.service.RoomService#deleteSplitRooms(long)
	  */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public void deleteSplitRooms(long roomId) {
		List<Room> splitRoom = getSplitRoomsByParentId(roomId);
		for(Room r : splitRoom){
			deleteRoom(r);
		}
	}

	/**
	  * <p>Title: getRoom</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @return
	  * @see com.guitarcenter.scheduler.service.RoomService#getRoom(long)
	  */
	@Override
	public Room getRoom(long roomId) {
		return roomDAO.get(roomId);
	}
	
	@Override
	public List<Room> loadEnabledRoomList(long profileId, Enabled enabled) {
		Criterion<Room, Room> criterion = RoomCriterion.findByProfileIdAndEnabled(profileId, enabled);
		return roomDAO.search(criterion);
	}
	
	@Override
	public List<RoomDTO> loadEnabledRoomListByActivity(long activityId, Enabled enabled) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("Load Room list by activityId:{} and Enabled:Y from RoomService.loadEnabledRoomListByActivity", activityId, Enabled.Y);
		}
		List<RoomDTO> dtos = new LinkedList<RoomDTO>();
		List<Room> list = new LinkedList<Room>();
		try {
			Criterion<Room, Room> criterion = RoomCriterion.findByActivityIdAndEnabled(activityId, enabled);
			list = roomDAO.search(criterion);
		} catch (Exception e) {
			if(LOG.isDebugEnabled()) {
				LOG.debug("Caught an exception {} from RoomService.loadEnabledRoomListByActivity", e);
			}
		}
		if(LOG.isDebugEnabled()) {
			LOG.debug("Got Room list:{} from RoomService.loadEnabledRoomListByActivity", list);
		}
		if(null != list && !list.isEmpty()) {
			for(Room r : list) {
				dtos.add(new RoomDTO(r.getRoomId(), r.getProfileRoomName()));
			}
		}
		return dtos;
	}

	/**
	  * <p>Title: checkSplitByRoomId</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @return
	  * @see com.guitarcenter.scheduler.service.RoomService#checkSplitByRoomId(long)
	  */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public boolean checkSplitByRoomId(long roomId) {
		Room room = getRoom(roomId);
		List<Room> splitRoomList = getSplitRoomsByParentId(roomId);
		if(room.getParentRoom()==null && splitRoomList.size()==0){
			return true;
		}
		return false;
	}

	/**
	  * <p>Title: getSplitRoomsByParentId</p>
	  * <p>Description: </p>
	  * @param roomId
	  * @return
	  * @see com.guitarcenter.scheduler.service.RoomService#getSplitRoomsByParentId(long)
	  */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public List<Room> getSplitRoomsByParentId(long roomId){
		return roomDAO.getSplitRoomsByParentId(roomId);
	}
	
	@Transactional
	@Override
	public List<RoomDTO> findByProfileIdAndActivityIds(long profileId,
			Long... activityIds) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("Find Room list in profile {} & activityIds {} in LocationProfileService.findByProfileIdAndActivityIds", profileId, activityIds);
		}
		List<RoomDTO> dtos = new LinkedList<RoomDTO>();
		List<Room> list;
		try {
			Criterion<Room, Room> criterion = RoomCriterion.findByProfileIdAndActivityIds(profileId, activityIds);
			list = roomDAO.search(criterion);
			if (LOG.isDebugEnabled()) {
				LOG.debug("Find Room list {} in RoomService.findByProfileIdAndActivityIds", list);
			}
			if(null != list && !list.isEmpty()) {
				for(Room r : list) {
					dtos.add(new RoomDTO(r.getRoomId(), r.getProfileRoomName()));
				}
			}
		} catch (Exception e) {
			LOG.error("Caught an exception {} when loading room list by profileId {} & activityIds {}", e, profileId, activityIds);
		}
		return dtos;
	}

	@Override
	public List<Room> findByTemplateId(Long templateId) {
		Criterion<Room, Room>  criterion = RoomCriterion.findByRoomTemplateId(templateId);
		List<Room> rooms = roomDAO.search(criterion);
		return rooms;
	}

	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public boolean checkUpdateRoomByAppointmentTime(boolean recurring,
			long roomId, String startDate, String endDate, String startTime,
			String endTime, long profileId,
			String excludeAppointmentIdParam) {
		if(recurring && !startDate.equals(endDate)){
			return roomDAO.getUpdateRoomByAppointmentRecurringTime(roomId, startDate, endDate, startTime, endTime, profileId, excludeAppointmentIdParam);
		}else{
			return roomDAO.getUpdateRoomByAppointmentTime(roomId, startDate, startTime, endTime, profileId, excludeAppointmentIdParam);
		}
	}
	
	@Transactional
	@Override
	public List<RoomDTO> loadRoomListByProfileIdAndActivityIdAndDateTime(
			Long profileId, Long serviceId, Long activityId, Long roomId,
			Date startTime, Date endTime, Long appId) {
		List<Room> list = new LinkedList<Room>();
		List<RoomDTO> dtos = new LinkedList<RoomDTO>();
		try {
			Criterion<Room, Room> criterion = RoomCriterion.findByProfileIdAndActivityIdAndDateTime(profileId, activityId, startTime, endTime, appId);
			list = roomDAO.search(criterion);
		} catch (Exception e) {
			LOG.error("Caught exception {} when loadRoomListByProfileIdAndActivityIdAndDateTime", e);
		}
		for(Room r : list) {
			dtos.add(new RoomDTO(r.getRoomId(), r.getProfileRoomName()));
		}
		//For GCSS-499,keep the origion room as the head element of the room list if it's valid
		if(null != appId) {
			Appointment appt = appointmentService.getAppointment(appId);
			if(!dtos.isEmpty()) {
				for(int i=0; i<dtos.size(); i++) {
					RoomDTO dto = dtos.get(i);
					if(null != dto.getRoomId() && null != appt.getRoom()) {
						if(dto.getRoomId().equals(appt.getRoom().getRoomId())) {
							Collections.swap(dtos, 0, i);
							break;
						}
					}
				}
			}
		}
		return dtos;
	}
}
