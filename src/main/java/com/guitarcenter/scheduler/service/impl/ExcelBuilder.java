package com.guitarcenter.scheduler.service.impl;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.web.servlet.view.document.AbstractExcelView;

import com.guitarcenter.scheduler.dao.criterion.dto.AppointmentHistoryDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorAppointmentStatusDTO;
import com.guitarcenter.scheduler.dto.CancelledAppointmentReportDTO;
import com.guitarcenter.scheduler.dto.CancelledAppointmentReportPagingDTO;
import com.guitarcenter.scheduler.dto.InstructorReportPagingDTO;
import com.guitarcenter.scheduler.dto.InstructorScheduleReportDTO;
import com.guitarcenter.scheduler.dto.RehearsalBookingReportDTO;
import com.guitarcenter.scheduler.dto.RehearsalBookingReportParentDTO;
import com.guitarcenter.scheduler.dto.RehearsalScheduleReportDTO;
import com.guitarcenter.scheduler.dto.RehearsalScheduleReportParentDTO;

public class ExcelBuilder extends AbstractExcelView {

	@Override
	protected void buildExcelDocument(Map<String, Object> model,
			HSSFWorkbook workbook, HttpServletRequest request,
			HttpServletResponse response) throws Exception {

		// String reportType=(String) model.get("reportType");

		switch ((String) model.get("reportType")) {

		case "instructorScheduleReport":
			instructorExcelReport(model, workbook);
			break;
		case "cancelledAppointments":
			cancellAppointmentsExcelReport(model, workbook);
			break;
		case "instructorOpenAppointmentsReport":
			instructorOpenAppointmentsExcelReport(model, workbook);
			break;
		case "conflictAppointmentsReportByInsrtuctor":
			conflictAppointmentsReportByInsrtuctorExcelReport(model, workbook);
			break;
		case "conflictAppointmentsReportByRoom":
			conflictAppointmentsReportByRoomExcelReport(model, workbook);
			break;
		case "instructorOutsideAppointmentsReport":
			instructorOutsideAppointmentsReportExcelReport(model, workbook);
			break;
		case "rehearsalBookingReport":
			rehearsalBookingReportExcelReport(model, workbook);
			break;
		case "rehearsalScheduleReport":
			rehearsalScheduleReportExcelReport(model, workbook);
			break;
		case "activeStudentsReport":
			activeStudentsReportExcelReport(model, workbook);
			break;
		case "studentCheckInReport":
			studentCheckInReportExcelReport(model, workbook);
			break;
		case "inActveStudentsReport":
			inActveStudentsReportExcelReport(model,workbook);
			break;
		case "profileExcelReport":
			profileExcelReport(model,workbook);
			break;
		case "appointmentHistoryReport":
			appointmentHistoryReport(model,workbook);
			break;
		case "instructorAppointmentStatusScheduleReport":
			instructorStatusExcelReport(model,workbook);
			break;
		}

	}
	
	public void instructorStatusExcelReport(Map<String, Object> model,
			HSSFWorkbook workbook) {

		@SuppressWarnings("unchecked")
		List<InstructorReportPagingDTO> parent = (List<InstructorReportPagingDTO>) model
				.get("instructorAppointmentStatusScheduleReport");

		Row row;
		// workbook = new HSSFWorkbook();
		HSSFSheet sheet = workbook.createSheet("Instructor_Schedule_Status_Report");
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setFontHeightInPoints((short) 10);
		font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
		style.setFont(font);
		
		CellStyle autoWrapText = workbook.createCellStyle(); //Create new style
		//autoWrapText.setFont(font);
		autoWrapText.setWrapText(true); //Set wordwrap
		
		
		//GSSP 252 Setting Instructor ExternalID
		sheet.setColumnWidth(0, 5000);
		//GSSP-189 setting date column width
		sheet.setColumnWidth(1, 5000);
		sheet.setColumnWidth(2, 6000);
		sheet.setColumnWidth(3, 6000);
		sheet.setColumnWidth(4, 6000);
		sheet.setColumnWidth(5, 5000);
		sheet.setColumnWidth(6, 7000);		
		sheet.setColumnWidth(7, 7000);
		sheet.setColumnWidth(8, 7000);
		
       
		
		int rownum = 0;
		row = sheet.createRow(rownum++);
		//GSSP 252 Setting Instructor ExternalID
		row.createCell(0).setCellValue("Instructor #");
		row.getCell(0).setCellStyle(style);
		
		row.createCell(1).setCellValue("Instructor Name");
		row.getCell(1).setCellStyle(style);
		
		row.createCell(2).setCellValue("Date");
		row.getCell(2).setCellStyle(style);
		
		row.createCell(3).setCellValue("Time Frame");
		row.getCell(3).setCellStyle(style);
		
		row.createCell(4).setCellValue("Activity Name");
		row.getCell(4).setCellStyle(style);
		
		row.createCell(5).setCellValue("Customer Name");
		row.getCell(5).setCellStyle(style);
		
		row.createCell(6).setCellValue("Completed?");
		row.getCell(6).setCellStyle(style);
		
		row.createCell(7).setCellValue("Internal Remarks");
		row.getCell(7).setCellStyle(style);
		
		row.createCell(8).setCellValue("Notes to Student");
		row.getCell(8).setCellStyle(style);
		
		
		
		for (InstructorReportPagingDTO parents : parent) {

			List<InstructorAppointmentStatusDTO> instructorStatusReports = parents
					.getInstructorAppointmentStatusReports();
			for (InstructorAppointmentStatusDTO instrprts : instructorStatusReports) {
				row = sheet.createRow(rownum++);
				/*String timeFrame = instrprts.getStartTime() + "-"
						+ instrprts.getEndTime();*/
				// StringBuilder tf=new
				// StringBuilder(instrprts.getStartTime()).append("-").append(instrprts.getEndTime());
				// String timeFrame=tf.toString();	
				//GSSP 252- excel Report for Instructor 
				row.createCell(0).setCellValue(instrprts.getInstructorExternalId());
				row.createCell(1).setCellValue(instrprts.getInstructorName());
				//GSSP-189 getting date for instructor schedule
				row.createCell(2).setCellValue(instrprts.getDateOfAppointment());
				row.createCell(3).setCellValue(instrprts.getTimeFrame());
				row.createCell(4).setCellValue(instrprts.getActivityName());
				row.createCell(5).setCellValue(instrprts.getCustomerName());
				row.createCell(6).setCellValue(instrprts.getShowStatus());
				row.createCell(7).setCellValue(instrprts.getComments());
				row.getCell(7).setCellStyle(autoWrapText);
				row.createCell(8).setCellValue(instrprts.getStudentNote());
				row.getCell(8).setCellStyle(autoWrapText);
				
			}
			// rownum=rownum+2;
		}
	}

	public void instructorExcelReport(Map<String, Object> model,
			HSSFWorkbook workbook) {

		@SuppressWarnings("unchecked")
		List<InstructorReportPagingDTO> parent = (List<InstructorReportPagingDTO>) model
				.get("instructorScheduleReport");

		Row row;
		// workbook = new HSSFWorkbook();
		HSSFSheet sheet = workbook.createSheet("Instructor_Schedule_Report");
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setFontHeightInPoints((short) 10);
		font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
		style.setFont(font);
		//GSSP 252 Setting Instructor ExternalID
		sheet.setColumnWidth(0, 5000);
		//GSSP-189 setting date column width
		sheet.setColumnWidth(1, 2600);
		sheet.setColumnWidth(2, 5000);
		sheet.setColumnWidth(3, 2600);
		sheet.setColumnWidth(4, 4000);
		sheet.setColumnWidth(5, 5000);
		sheet.setColumnWidth(6, 5500);		
		sheet.setColumnWidth(7, 6000);
		sheet.setColumnWidth(8, 6500);
		int rownum = 0;
		row = sheet.createRow(rownum++);
		//GSSP 252 Setting Instructor ExternalID
		row.createCell(0).setCellValue("Instructor #");
		row.getCell(0).setCellStyle(style);
		row.createCell(1).setCellValue("Instructor Name");
		row.getCell(1).setCellStyle(style);
		//GSSP-189 setting date column 
		row.createCell(2).setCellValue("Date");
		row.getCell(2).setCellStyle(style);
		row.createCell(3).setCellValue("Time Frame");
		row.getCell(3).setCellStyle(style);
		row.createCell(4).setCellValue("Duration");
		row.getCell(4).setCellStyle(style);
		row.createCell(5).setCellValue("Activity Type");
		row.getCell(5).setCellStyle(style);
		//GSSP-311 setting room name column 
		row.createCell(6).setCellValue("Room Name");
		row.getCell(6).setCellStyle(style);
		row.createCell(7).setCellValue("Customer Name");
		row.getCell(7).setCellStyle(style);
		row.createCell(8).setCellValue("Show/No Show/Cancel");
		row.getCell(8).setCellStyle(style);
		
		for (InstructorReportPagingDTO parents : parent) {

			List<InstructorScheduleReportDTO> instructorReports = parents
					.getInstructorReports();
			for (InstructorScheduleReportDTO instrprts : instructorReports) {
				row = sheet.createRow(rownum++);
				String timeFrame = instrprts.getStartTime() + "-"
						+ instrprts.getEndTime();
				// StringBuilder tf=new
				// StringBuilder(instrprts.getStartTime()).append("-").append(instrprts.getEndTime());
				// String timeFrame=tf.toString();	
				//GSSP 252- excel Report for Instructor 
				row.createCell(0).setCellValue(instrprts.getExternal_id());
				row.createCell(1).setCellValue(instrprts.getInstructorName());
				//GSSP-189 getting date for instructor schedule
				row.createCell(2).setCellValue(instrprts.getStartDate());
				row.createCell(3).setCellValue(timeFrame);
				row.createCell(4).setCellValue(instrprts.getDuration());
				row.createCell(5).setCellValue(instrprts.getActivityType());
				row.createCell(6).setCellValue(instrprts.getRoomName());
				row.createCell(7).setCellValue(instrprts.getCustomerName());
				row.createCell(8).setCellValue(instrprts.getSignitureBlock());
				
			}
			// rownum=rownum+2;
		}
	}

	public void cancellAppointmentsExcelReport(Map<String, Object> model,
			HSSFWorkbook workbook) {

		List<CancelledAppointmentReportPagingDTO> parent = (List<CancelledAppointmentReportPagingDTO>) model
				.get("cancelledAppointmentReport");
		Row row;
		// workbook = new HSSFWorkbook();
		HSSFSheet sheet = workbook.createSheet("Cancelled_Appointments_Report");
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setFontHeightInPoints((short) 10);
		font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
		style.setFont(font);
		//GSSP-189 setting date column width
		sheet.setColumnWidth(0, 3000);
		sheet.setColumnWidth(1, 4800);
		//sheet.setColumnWidth(2, 3000);
		sheet.setColumnWidth(2, 5000);
		sheet.setColumnWidth(3, 5000);
		sheet.setColumnWidth(4, 5000);
		//GSSP-210 changes
		sheet.setColumnWidth(5, 4000);
		sheet.setColumnWidth(6, 5100);
		sheet.setColumnWidth(7, 4000);
		sheet.setColumnWidth(8, 5100);
		int rownum = 0;
		row = sheet.createRow(rownum++);
		//GSSP-189 setting date column 
		row.createCell(0).setCellValue("Date");
		row.getCell(0).setCellStyle(style);
		row.createCell(1).setCellValue("Time");
		row.getCell(1).setCellStyle(style);
		row.createCell(2).setCellValue("Activity Type");
		row.getCell(2).setCellStyle(style);
		row.createCell(3).setCellValue("Instructor Name");
		row.getCell(3).setCellStyle(style);
		row.createCell(4).setCellValue("Customer Name");
		row.getCell(4).setCellStyle(style);
		//GSSP-214 changes
		row.createCell(5).setCellValue("Cancelled User");
		row.getCell(5).setCellStyle(style);
		row.createCell(6).setCellValue("Cancelled Timestamp");
		row.getCell(6).setCellStyle(style);
		row.createCell(7).setCellValue("Notes");
		row.getCell(7).setCellStyle(style);
		//GSSP-269 changes
		row.createCell(8).setCellValue("Cancelled Reason");
		row.getCell(8).setCellStyle(style);
		for (CancelledAppointmentReportPagingDTO parents : parent) {
			List<CancelledAppointmentReportDTO> cancelledapptReports = parents
					.getList();
			for (CancelledAppointmentReportDTO cnclrprts : cancelledapptReports) {
				row = sheet.createRow(rownum++);
				String timeFrame = cnclrprts.getStartTime() + "-"
						+ cnclrprts.getEndTime();
				//GSSP-189 Getting date for Cancelled_Appointments_Report 
				row.createCell(0).setCellValue(cnclrprts.getStartDateStr());
				row.createCell(1).setCellValue(timeFrame);
				row.createCell(2).setCellValue(cnclrprts.getActivityType());
				row.createCell(3).setCellValue(cnclrprts.getInstructorName());
				row.createCell(4).setCellValue(cnclrprts.getCustomerName());
				//GSSP-214 changes
				row.createCell(5).setCellValue(cnclrprts.getCancelledUser());
				row.createCell(6).setCellValue(cnclrprts.getCancelledTime());
				//GSSP-191 Getting Notes data for Cancelled_Appointments_Report
				row.createCell(7).setCellValue(cnclrprts.getNotes());
				row.createCell(8).setCellValue(cnclrprts.getCancelledReason());
			}

		}

	}

	public void instructorOpenAppointmentsExcelReport(
			Map<String, Object> model, HSSFWorkbook workbook) {

		List<InstructorReportPagingDTO> parent = (List<InstructorReportPagingDTO>) model
				.get("instructorOpenAppointmentsReport");

		Row row;
		// workbook = new HSSFWorkbook();
		HSSFSheet sheet = workbook
				.createSheet("instructor_OpenAppointments_Report");
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setFontHeightInPoints((short) 10);
		font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
		style.setFont(font);
		sheet.setColumnWidth(0, 4500);
		sheet.setColumnWidth(1, 3000);
		sheet.setColumnWidth(2, 5000);
		sheet.setColumnWidth(3, 3000);
		sheet.setColumnWidth(4, 5000);
		sheet.setColumnWidth(5, 5000);
		sheet.setColumnWidth(6, 5000);
		int rownum = 0;
		row = sheet.createRow(rownum++);
		row.createCell(0).setCellValue("Instructor Name");
		row.getCell(0).setCellStyle(style);
		row.createCell(1).setCellValue("Date");
		row.getCell(1).setCellStyle(style);
		row.createCell(2).setCellValue("Time Frame");
		row.getCell(2).setCellStyle(style);
		row.createCell(3).setCellValue("Duration");
		row.getCell(3).setCellStyle(style);
		row.createCell(4).setCellValue("Activity Type");
		row.getCell(4).setCellStyle(style);
		row.createCell(5).setCellValue("Customer Name");
		row.getCell(5).setCellStyle(style);
		row.createCell(6).setCellValue("Room Name");
		row.getCell(6).setCellStyle(style);
		for (InstructorReportPagingDTO parents : parent) {

			List<InstructorScheduleReportDTO> instructorReports = parents
					.getInstructorReports();
			for (InstructorScheduleReportDTO instrprts : instructorReports) {
				row = sheet.createRow(rownum++);
				String timeFrame = instrprts.getStartTime() + "-"
						+ instrprts.getEndTime();
				// StringBuilder tf=new
				// StringBuilder(instrprts.getStartTime()).append("-").append(instrprts.getEndTime());
				// String timeFrame=tf.toString();
				row.createCell(0).setCellValue(instrprts.getInstructorName());
				row.createCell(1).setCellValue(instrprts.getStartDate());
				row.createCell(2).setCellValue(timeFrame);
				row.createCell(3).setCellValue(instrprts.getDuration());
				row.createCell(4).setCellValue(instrprts.getActivityType());
				row.createCell(5).setCellValue(instrprts.getCustomerName());
				row.createCell(6).setCellValue(instrprts.getRoomName());
			}
			// rownum=rownum+2;
		}
	}

	public void conflictAppointmentsReportByInsrtuctorExcelReport(
			Map<String, Object> model, HSSFWorkbook workbook) {

		List<InstructorReportPagingDTO> parent = (List<InstructorReportPagingDTO>) model
				.get("conflictingAppointmentsReportByInsrtuctor");

		Row row;
		// workbook = new HSSFWorkbook();
		HSSFSheet sheet = workbook
				.createSheet("conflicting_AppointmentsReportBy_Insrtuctor");
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setFontHeightInPoints((short) 10);
		font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
		style.setFont(font);
		sheet.setColumnWidth(0, 4500);
		sheet.setColumnWidth(1, 3000);
		sheet.setColumnWidth(2, 5000);
		sheet.setColumnWidth(3, 3000);
		sheet.setColumnWidth(4, 5000);
		sheet.setColumnWidth(5, 5000);
		sheet.setColumnWidth(6, 5000);
		int rownum = 0;
		row = sheet.createRow(rownum++);
		row.createCell(0).setCellValue("Instructor Name");
		row.getCell(0).setCellStyle(style);
		row.createCell(1).setCellValue("Date");
		row.getCell(1).setCellStyle(style);
		row.createCell(2).setCellValue("Time Frame");
		row.getCell(2).setCellStyle(style);
		row.createCell(3).setCellValue("Duration");
		row.getCell(3).setCellStyle(style);
		row.createCell(4).setCellValue("Activity Type");
		row.getCell(4).setCellStyle(style);
		row.createCell(5).setCellValue("Customer Name");
		row.getCell(5).setCellStyle(style);
		for (InstructorReportPagingDTO parents : parent) {

			List<InstructorScheduleReportDTO> instructorReports = parents
					.getInstructorReports();
			for (InstructorScheduleReportDTO instrprts : instructorReports) {
				row = sheet.createRow(rownum++);
				String timeFrame = instrprts.getStartTime() + "-"
						+ instrprts.getEndTime();
				// StringBuilder tf=new
				// StringBuilder(instrprts.getStartTime()).append("-").append(instrprts.getEndTime());
				// String timeFrame=tf.toString();
				row.createCell(0).setCellValue(instrprts.getInstructorName());
				row.createCell(1).setCellValue(instrprts.getStartDate());
				row.createCell(2).setCellValue(timeFrame);
				row.createCell(3).setCellValue(instrprts.getDuration());
				row.createCell(4).setCellValue(instrprts.getActivityType());
				row.createCell(5).setCellValue(instrprts.getCustomerName());

			}
			// rownum=rownum+2;
		}
	}

	public void conflictAppointmentsReportByRoomExcelReport(
			Map<String, Object> model, HSSFWorkbook workbook) {

		List<InstructorReportPagingDTO> parent = (List<InstructorReportPagingDTO>) model
				.get("conflictingAppointmentsReportByRoom");

		Row row;
		// workbook = new HSSFWorkbook();
		HSSFSheet sheet = workbook
				.createSheet("conflicting_AppointmentsReport_ByRoom");
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setFontHeightInPoints((short) 10);
		font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
		style.setFont(font);
		sheet.setColumnWidth(0, 4500);
		sheet.setColumnWidth(1, 3000);
		sheet.setColumnWidth(2, 5000);
		sheet.setColumnWidth(3, 3000);
		sheet.setColumnWidth(4, 5000);
		sheet.setColumnWidth(5, 5000);
		sheet.setColumnWidth(6, 5000);
		int rownum = 0;
		row = sheet.createRow(rownum++);
		row.createCell(0).setCellValue("Room Name");
		row.getCell(0).setCellStyle(style);
		row.createCell(1).setCellValue("Date");
		row.getCell(1).setCellStyle(style);
		row.createCell(2).setCellValue("Time Frame");
		row.getCell(2).setCellStyle(style);
		row.createCell(3).setCellValue("Duration");
		row.getCell(3).setCellStyle(style);
		row.createCell(4).setCellValue("Activity Type");
		row.getCell(4).setCellStyle(style);
		row.createCell(5).setCellValue("Customer Name");
		row.getCell(5).setCellStyle(style);
		for (InstructorReportPagingDTO parents : parent) {

			List<InstructorScheduleReportDTO> instructorReports = parents
					.getInstructorReports();
			for (InstructorScheduleReportDTO instrprts : instructorReports) {
				row = sheet.createRow(rownum++);
				String timeFrame = instrprts.getStartTime() + "-"
						+ instrprts.getEndTime();
				// StringBuilder tf=new
				// StringBuilder(instrprts.getStartTime()).append("-").append(instrprts.getEndTime());
				// String timeFrame=tf.toString();
				row.createCell(0).setCellValue(instrprts.getRoomName());
				row.createCell(1).setCellValue(instrprts.getStartDate());
				row.createCell(2).setCellValue(timeFrame);
				row.createCell(3).setCellValue(instrprts.getDuration());
				row.createCell(4).setCellValue(instrprts.getActivityType());
				row.createCell(5).setCellValue(instrprts.getCustomerName());

			}
			// rownum=rownum+2;
		}
	}

	public void instructorOutsideAppointmentsReportExcelReport(
			Map<String, Object> model, HSSFWorkbook workbook) {

		List<InstructorReportPagingDTO> parent = (List<InstructorReportPagingDTO>) model
				.get("instructorOutsideAppointmentsReport");

		Row row;
		// workbook = new HSSFWorkbook();
		HSSFSheet sheet = workbook
				.createSheet("instructor_OutsideAppointments_Report");
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setFontHeightInPoints((short) 10);
		font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
		style.setFont(font);
		sheet.setColumnWidth(0, 4500);
		sheet.setColumnWidth(1, 3000);
		sheet.setColumnWidth(2, 5000);
		sheet.setColumnWidth(3, 3000);
		sheet.setColumnWidth(4, 5000);
		sheet.setColumnWidth(5, 5000);
		sheet.setColumnWidth(6, 5000);
		int rownum = 0;
		row = sheet.createRow(rownum++);
		row.createCell(0).setCellValue("Instructor Name");
		row.getCell(0).setCellStyle(style);
		row.createCell(1).setCellValue("Date");
		row.getCell(1).setCellStyle(style);
		row.createCell(2).setCellValue("Time Frame");
		row.getCell(2).setCellStyle(style);
		row.createCell(3).setCellValue("Duration");
		row.getCell(3).setCellStyle(style);
		row.createCell(4).setCellValue("Activity Type");
		row.getCell(4).setCellStyle(style);
		row.createCell(5).setCellValue("Customer Name");
		row.getCell(5).setCellStyle(style);
		row.createCell(6).setCellValue("Room Name");
		row.getCell(6).setCellStyle(style);
		for (InstructorReportPagingDTO parents : parent) {

			List<InstructorScheduleReportDTO> instructorReports = parents
					.getInstructorReports();
			for (InstructorScheduleReportDTO instrprts : instructorReports) {
				row = sheet.createRow(rownum++);
				String timeFrame = instrprts.getStartTime() + "-"
						+ instrprts.getEndTime();
				// StringBuilder tf=new
				// StringBuilder(instrprts.getStartTime()).append("-").append(instrprts.getEndTime());
				// String timeFrame=tf.toString();
				row.createCell(0).setCellValue(instrprts.getInstructorName());
				row.createCell(1).setCellValue(instrprts.getStartDate());
				row.createCell(2).setCellValue(timeFrame);
				row.createCell(3).setCellValue(instrprts.getDuration());
				row.createCell(4).setCellValue(instrprts.getActivityType());
				row.createCell(5).setCellValue(instrprts.getCustomerName());
				row.createCell(6).setCellValue(instrprts.getRoomName());
			}
			// rownum=rownum+2;
		}
	}

	public void rehearsalBookingReportExcelReport(Map<String, Object> model,
			HSSFWorkbook workbook) {

		List<RehearsalBookingReportParentDTO> parent = (List<RehearsalBookingReportParentDTO>) model
				.get("rehearsalBookingReport");

		Row row;
		// workbook = new HSSFWorkbook();
		HSSFSheet sheet = workbook.createSheet("rehearsa_lBooking_Report");
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setFontHeightInPoints((short) 10);
		font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
		style.setFont(font);
		sheet.setColumnWidth(0, 3000);
		sheet.setColumnWidth(1, 5000);
		sheet.setColumnWidth(2, 5000);
		sheet.setColumnWidth(3, 5000);
		sheet.setColumnWidth(4, 5000);
		sheet.setColumnWidth(5, 5000);
		int rownum = 0;
		row = sheet.createRow(rownum++);
		row.createCell(0).setCellValue("Time");
		row.getCell(0).setCellStyle(style);
		row.createCell(1).setCellValue("Duration");
		row.getCell(1).setCellStyle(style);
		row.createCell(2).setCellValue("Activity Type");
		row.getCell(2).setCellStyle(style);
		row.createCell(3).setCellValue("Room");
		row.getCell(3).setCellStyle(style);
		row.createCell(4).setCellValue("Customer Name");
		row.getCell(4).setCellStyle(style);
		row.createCell(5).setCellValue("Band Name");
		row.getCell(5).setCellStyle(style);
		for (RehearsalBookingReportParentDTO parents : parent) {

			List<RehearsalBookingReportDTO> rehearsalBookingReports = parents
					.getList();
			for (RehearsalBookingReportDTO reheslbkngrpts : rehearsalBookingReports) {
				row = sheet.createRow(rownum++);
				// String
				// timeFrame=reheslbkngrpts.getStartTime()+"-"+reheslbkngrpts.getEndTime();
				// StringBuilder tf=new
				// StringBuilder(instrprts.getStartTime()).append("-").append(instrprts.getEndTime());
				// String timeFrame=tf.toString();
				row.createCell(0).setCellValue(reheslbkngrpts.getStartTime());
				row.createCell(1).setCellValue(reheslbkngrpts.getDuration());
				row.createCell(2)
						.setCellValue(reheslbkngrpts.getActivityType());
				row.createCell(3).setCellValue(reheslbkngrpts.getRoomName());
				row.createCell(4)
						.setCellValue(reheslbkngrpts.getCustomerName());
				row.createCell(5).setCellValue(reheslbkngrpts.getBandName());

			}
			// rownum=rownum+2;
		}
	}

	public void rehearsalScheduleReportExcelReport(Map<String, Object> model,
			HSSFWorkbook workbook) {

		List<RehearsalScheduleReportParentDTO> parent = (List<RehearsalScheduleReportParentDTO>) model
				.get("rehearsalScheduleReport");

		Row row;
		// workbook = new HSSFWorkbook();
		HSSFSheet sheet = workbook.createSheet("rehearsal_Schedule_Report");
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setFontHeightInPoints((short) 10);
		font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
		style.setFont(font);
		sheet.setColumnWidth(0, 3000);
		sheet.setColumnWidth(1, 5000);
		sheet.setColumnWidth(2, 5000);
		sheet.setColumnWidth(3, 5000);
		sheet.setColumnWidth(4, 5000);
		sheet.setColumnWidth(5, 5000);
		int rownum = 0;
		row = sheet.createRow(rownum++);
		row.createCell(0).setCellValue("Time");
		row.getCell(0).setCellStyle(style);
		row.createCell(1).setCellValue("Duration");
		row.getCell(1).setCellStyle(style);
		row.createCell(2).setCellValue("Activity Type");
		row.getCell(2).setCellStyle(style);
		row.createCell(3).setCellValue("Room");
		row.getCell(3).setCellStyle(style);
		row.createCell(4).setCellValue("Customer Name");
		row.getCell(4).setCellStyle(style);
		row.createCell(5).setCellValue("Band Name");
		row.getCell(5).setCellStyle(style);
		for (RehearsalScheduleReportParentDTO parents : parent) {

			List<RehearsalScheduleReportDTO> rehearsalScheduleReports = parents
					.getList();
			for (RehearsalScheduleReportDTO reheslschlrpts : rehearsalScheduleReports) {
				row = sheet.createRow(rownum++);
				// String
				// timeFrame=reheslbkngrpts.getStartTime()+"-"+reheslbkngrpts.getEndTime();
				// StringBuilder tf=new
				// StringBuilder(instrprts.getStartTime()).append("-").append(instrprts.getEndTime());
				// String timeFrame=tf.toString();
				row.createCell(0).setCellValue(reheslschlrpts.getStartTime());
				row.createCell(1).setCellValue(reheslschlrpts.getDuration());
				row.createCell(2)
						.setCellValue(reheslschlrpts.getActivityType());
				row.createCell(3).setCellValue(reheslschlrpts.getRoomName());
				row.createCell(4)
						.setCellValue(reheslschlrpts.getCustomerName());
				row.createCell(5).setCellValue(reheslschlrpts.getBandName());

			}
			// rownum=rownum+2;
		}
	}
	//Added new active students report  GSSP-185
	public void activeStudentsReportExcelReport(
			Map<String, Object> model, HSSFWorkbook workbook) {

		List<InstructorReportPagingDTO> parent = (List<InstructorReportPagingDTO>) model
				.get("activeStudentsReport");

		Row row;
		// workbook = new HSSFWorkbook();
		HSSFSheet sheet = workbook
				.createSheet("active_Students_Report");
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setFontHeightInPoints((short) 10);
		font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
		style.setFont(font);
		sheet.setColumnWidth(0, 4500);
		sheet.setColumnWidth(1, 4500);
		sheet.setColumnWidth(2, 3000);
		sheet.setColumnWidth(3, 5000);
		sheet.setColumnWidth(4, 3000);
		sheet.setColumnWidth(5, 5000);
		sheet.setColumnWidth(6, 5000);
		int rownum = 0;
		row = sheet.createRow(rownum++);
		row.createCell(0).setCellValue("Student Name");
		row.getCell(0).setCellStyle(style);
		row.createCell(1).setCellValue("Instructor Name");
		row.getCell(1).setCellStyle(style);
		row.createCell(2).setCellValue("Date");
		row.getCell(2).setCellStyle(style);
		row.createCell(3).setCellValue("Time Frame");
		row.getCell(3).setCellStyle(style);
		row.createCell(4).setCellValue("Duration");
		row.getCell(4).setCellStyle(style);
		row.createCell(5).setCellValue("Activity Type");
		row.getCell(5).setCellStyle(style);
		row.createCell(6).setCellValue("Student Email");
		row.getCell(6).setCellStyle(style);
		for (InstructorReportPagingDTO parents : parent) {

			List<InstructorScheduleReportDTO> instructorReports = parents
					.getInstructorReports();
			for (InstructorScheduleReportDTO instrprts : instructorReports) {
				row = sheet.createRow(rownum++);
				String timeFrame = instrprts.getStartTime() + "-"
						+ instrprts.getEndTime();
				
				row.createCell(0).setCellValue(instrprts.getCustomerName());
				row.createCell(1).setCellValue(instrprts.getInstructorName());
				row.createCell(2).setCellValue(instrprts.getStartDate());
				row.createCell(3).setCellValue(timeFrame);
				row.createCell(4).setCellValue(instrprts.getDuration());
				row.createCell(5).setCellValue(instrprts.getActivityType());
				row.createCell(6).setCellValue(instrprts.getCustomerEmail());
			}
			
		}
	}

		//Added new student report  GSSP-203
				public void studentCheckInReportExcelReport(
						Map<String, Object> model, HSSFWorkbook workbook) {

					List<InstructorReportPagingDTO> parent = (List<InstructorReportPagingDTO>) model
							.get("studentCheckInReport");

					Row row;
					// workbook = new HSSFWorkbook();
					HSSFSheet sheet = workbook
							.createSheet("student_CheckIn_Report");
					HSSFCellStyle style = workbook.createCellStyle();
					HSSFFont font = workbook.createFont();
					font.setFontHeightInPoints((short) 10);
					font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
					style.setFont(font);
					sheet.setColumnWidth(0, 4500);
					sheet.setColumnWidth(1, 4500);
					sheet.setColumnWidth(2, 3000);
					sheet.setColumnWidth(3, 5000);
					sheet.setColumnWidth(4, 3000);
					sheet.setColumnWidth(5, 5000);
					sheet.setColumnWidth(6, 3000);
					sheet.setColumnWidth(7, 3000);
					int rownum = 0;
					row = sheet.createRow(rownum++);
					row.createCell(0).setCellValue("Student Name");
					row.getCell(0).setCellStyle(style);
					row.createCell(1).setCellValue("Instructor Name");
					row.getCell(1).setCellStyle(style);
					row.createCell(2).setCellValue("Date");
					row.getCell(2).setCellStyle(style);
					row.createCell(3).setCellValue("Lesson Time");
					row.getCell(3).setCellStyle(style);
					row.createCell(4).setCellValue("Duration");
					row.getCell(4).setCellStyle(style);
					row.createCell(5).setCellValue("Lesson Type");
					row.getCell(5).setCellStyle(style);
					row.createCell(6).setCellValue("Check In");
					row.getCell(6).setCellStyle(style);
					row.createCell(7).setCellValue("Check Out");
					row.getCell(7).setCellStyle(style);
					for (InstructorReportPagingDTO parents : parent) {

						List<InstructorScheduleReportDTO> instructorReports = parents
								.getInstructorReports();
						for (InstructorScheduleReportDTO instrprts : instructorReports) {
							row = sheet.createRow(rownum++);
							String timeFrame = instrprts.getStartTime() + "-"
									+ instrprts.getEndTime();
							
							row.createCell(0).setCellValue(instrprts.getCustomerName());
							row.createCell(1).setCellValue(instrprts.getInstructorName());
							row.createCell(2).setCellValue(instrprts.getStartDate());
							row.createCell(3).setCellValue(timeFrame);
							row.createCell(4).setCellValue(instrprts.getDuration());
							row.createCell(5).setCellValue(instrprts.getActivityType());
							
						}
						
					}
				}


				//Added new student report  GSSP-205
					public void inActveStudentsReportExcelReport(
							Map<String, Object> model, HSSFWorkbook workbook) {

						@SuppressWarnings("unchecked")
						List<InstructorReportPagingDTO> parent = (List<InstructorReportPagingDTO>) model
								.get("inActveStudentsReport");

						Row row;
						// workbook = new HSSFWorkbook();
						HSSFSheet sheet = workbook
								.createSheet("inActve_Students_Report");
						HSSFCellStyle style = workbook.createCellStyle();
						HSSFFont font = workbook.createFont();
						font.setFontHeightInPoints((short) 10);
						font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
						style.setFont(font);
						sheet.setColumnWidth(0, 4500);
						sheet.setColumnWidth(1, 4500);
						int rownum = 0;
						row = sheet.createRow(rownum++);
						row.createCell(0).setCellValue("Student Name");
						row.getCell(0).setCellStyle(style);
						row.createCell(1).setCellValue("Student Email");
						row.getCell(1).setCellStyle(style);
						row.createCell(2).setCellValue("Student Phone");
						row.getCell(2).setCellStyle(style);
						for (InstructorReportPagingDTO parents : parent) {

							List<InstructorScheduleReportDTO> instructorReports = parents.getInstructorReports();
									for (InstructorScheduleReportDTO instrprts : instructorReports) {
								row = sheet.createRow(rownum++);
								
								row.createCell(0).setCellValue(instrprts.getCustomerName());
								row.createCell(1).setCellValue(instrprts.getCustomerEmail());
								row.createCell(2).setCellValue(instrprts.getCustomerPhone());
								
							}
							
						}
					}
					//Added new student report  GSSP-213
					public void profileExcelReport(
							Map<String, Object> model, HSSFWorkbook workbook) {

						@SuppressWarnings("unchecked")
						List<InstructorReportPagingDTO> parent = (List<InstructorReportPagingDTO>) model
								.get("profileExcelReport");

						Row row;
						// workbook = new HSSFWorkbook();
						HSSFSheet sheet = workbook
								.createSheet("profile_Excel_Report");
						HSSFCellStyle style = workbook.createCellStyle();
						HSSFFont font = workbook.createFont();
						font.setFontHeightInPoints((short) 10);
						font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
						style.setFont(font);
						sheet.setColumnWidth(0, 3000);
						sheet.setColumnWidth(1, 3500);
						sheet.setColumnWidth(2, 3500);
						sheet.setColumnWidth(3, 4000);
						sheet.setColumnWidth(4, 4000);
						sheet.setColumnWidth(5, 3000);
						sheet.setColumnWidth(6, 3000);
						sheet.setColumnWidth(7, 3000);
						sheet.setColumnWidth(8, 3000);
						sheet.setColumnWidth(9, 3000);
						sheet.setColumnWidth(10, 3000);
						int rownum = 0;
						row = sheet.createRow(rownum++);
					
						
						row.createCell(0).setCellValue("Store Location");
						row.getCell(0).setCellStyle(style);
						row.createCell(1).setCellValue("Store Number");
						row.getCell(1).setCellStyle(style);
						row.createCell(2).setCellValue("Today Studio Hours");
						row.getCell(2).setCellStyle(style);
						
						row.createCell(3).setCellValue("Total Appointments");
						row.getCell(3).setCellStyle(style);
						
						row.createCell(4).setCellValue("Total Instuctors Available today ");
						row.getCell(4).setCellStyle(style);
						
						row.createCell(5).setCellValue("Total Cancelled Appointment today");
						row.getCell(5).setCellStyle(style);
						
						row.createCell(6).setCellValue("Total Conflits Appointment today");
						row.getCell(6).setCellStyle(style);
						
						row.createCell(7).setCellValue("Total Active students today");
						row.getCell(7).setCellStyle(style);
						row.createCell(8).setCellValue("Total Services");
						row.getCell(8).setCellStyle(style);
						row.createCell(9).setCellValue("Total Activities");
						row.getCell(9).setCellStyle(style);
						row.createCell(10).setCellValue("Total Rooms");
						row.getCell(10).setCellStyle(style);
						
						
						
						for (InstructorReportPagingDTO parents : parent) {
								row = sheet.createRow(rownum++);
							
								row.createCell(0).setCellValue(parents.getLocation());
								row.createCell(1).setCellValue(parents.getStoreNumber());
								row.createCell(2).setCellValue(parents.getStudioHours());								
								row.createCell(3).setCellValue(parents.getActiveAppointments());
								row.createCell(4).setCellValue(parents.getTotalInstructors());
								row.createCell(5).setCellValue(parents.getCanceledAppointmentsCount());
								row.createCell(6).setCellValue(parents.getTotalConflictingAppointments());
								row.createCell(7).setCellValue(parents.getActiveStudents());			
								
								row.createCell(8).setCellValue(parents.getServiceCount());
								row.createCell(10).setCellValue(parents.getRoomCount());
								row.createCell(9).setCellValue(parents.getActivityCount());
								
							
							
						}
					}
					//Added new student report  GSSP-210
					public void appointmentHistoryReport(
							Map<String, Object> model, HSSFWorkbook workbook) {

						@SuppressWarnings("unchecked")
						List<AppointmentHistoryDTO> parent = (List<AppointmentHistoryDTO>) model
								.get("appointmentHistoryReport");

						Row row;
						// workbook = new HSSFWorkbook();
						HSSFSheet sheet = workbook
								.createSheet("appointment_History_Report");
						HSSFCellStyle style = workbook.createCellStyle();
						HSSFFont font = workbook.createFont();
						font.setFontHeightInPoints((short) 10);
						font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
						style.setFont(font);
						sheet.setColumnWidth(0, 4000);
						sheet.setColumnWidth(1, 3500);
						sheet.setColumnWidth(2, 6000);
						sheet.setColumnWidth(3, 4000);
						sheet.setColumnWidth(4, 4000);
						sheet.setColumnWidth(5, 4500);
						sheet.setColumnWidth(6, 3000);
						sheet.setColumnWidth(7, 3000);
						sheet.setColumnWidth(8, 3000);
						sheet.setColumnWidth(9, 3000);
						sheet.setColumnWidth(10, 4800);
						sheet.setColumnWidth(11, 6500);
						sheet.setColumnWidth(12, 6500);
						sheet.setColumnWidth(13, 5000);
						sheet.setColumnWidth(14, 5000);
						int rownum = 0;
						row = sheet.createRow(rownum++);
					
						
						row.createCell(0).setCellValue("Appointment ID");
						row.getCell(0).setCellStyle(style);
						row.createCell(1).setCellValue("Updated By");
						row.getCell(1).setCellStyle(style);
						row.createCell(2).setCellValue("Updated Timestamp");
						row.getCell(2).setCellStyle(style);
						
						row.createCell(3).setCellValue("Customer");
						row.getCell(3).setCellStyle(style);
						
						row.createCell(4).setCellValue("Service Type");
						row.getCell(4).setCellStyle(style);
						
						row.createCell(5).setCellValue("Lesson Type");
						row.getCell(5).setCellStyle(style);
						
						row.createCell(6).setCellValue("Start Date");
						row.getCell(6).setCellStyle(style);
						
						row.createCell(7).setCellValue("End Date");
						row.getCell(7).setCellStyle(style);
						row.createCell(8).setCellValue("Is Recurring appointment");
						row.getCell(8).setCellStyle(style);
						row.createCell(9).setCellValue("Time");
						row.getCell(9).setCellStyle(style);
						row.createCell(10).setCellValue("Duration in Minutes");
						row.getCell(10).setCellStyle(style);
						row.createCell(11).setCellValue("Instructor");
						row.getCell(11).setCellStyle(style);
						row.createCell(12).setCellValue("Room");
						row.getCell(12).setCellStyle(style);
						row.createCell(13).setCellValue("Note");
						row.getCell(13).setCellStyle(style);
						row.createCell(14).setCellValue("Is Cancelled");
						row.getCell(14).setCellStyle(style);
						for (AppointmentHistoryDTO parents : parent) {
								row = sheet.createRow(rownum++);
							
								row.createCell(0).setCellValue(parents.getAppointmentId());
								row.createCell(1).setCellValue(parents.getUpdatedBy());
								row.createCell(2).setCellValue(parents.getUpdatedTimestamp());								
								row.createCell(3).setCellValue(parents.getCustomerName());
								row.createCell(4).setCellValue(parents.getServiceType());
								row.createCell(5).setCellValue(parents.getLessonType());
								row.createCell(6).setCellValue(parents.getStartDate());
								row.createCell(7).setCellValue(parents.getEndDate());								
								row.createCell(8).setCellValue(parents.getIsRecurring());								
								row.createCell(9).setCellValue(parents.getTime());
								row.createCell(10).setCellValue(parents.getDuration());
								row.createCell(11).setCellValue(parents.getInstructorName());
								row.createCell(12).setCellValue(parents.getRoom());
								row.createCell(13).setCellValue(parents.getNote());
								row.createCell(14).setCellValue(parents.getIsCancelled());
							
							
						}
					}

}
