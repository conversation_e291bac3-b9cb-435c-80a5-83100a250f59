package com.guitarcenter.scheduler.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.dto.ActiveStudentsDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.AppointmentHistoryDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.ExportDetailsDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InActiveStudentsDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorAppointmentStatusDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorAppointmentStatusListDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorDateListDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorOpenAppointmentsDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorScheduleDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.MasterDailyDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.MasterDailyDateListDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.RehearsalBookingDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.RehearsalBookingDateListDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.RehearsalScheduleDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.RehearsalScheduleDateListDTO;
import com.guitarcenter.scheduler.dto.ActivityDTO;
import com.guitarcenter.scheduler.dto.CancelledAppointmentDateListDTO;
import com.guitarcenter.scheduler.dto.CancelledAppointmentReportDTO;
import com.guitarcenter.scheduler.dto.CancelledApptDailyDTO;
import com.guitarcenter.scheduler.dto.InstructorInfoDTO;
import com.guitarcenter.scheduler.dto.InstructorReportPagingDTO;
import com.guitarcenter.scheduler.dto.InstructorScheduleReportDTO;
import com.guitarcenter.scheduler.dto.MasterScheduleReportDTO;
import com.guitarcenter.scheduler.dto.RehearsalBookingReportDTO;
import com.guitarcenter.scheduler.dto.RehearsalScheduleReportDTO;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.dto.InstructorAppointmentBusinessHours;
import com.guitarcenter.scheduler.service.AvailabilityService;
import com.guitarcenter.scheduler.service.MailSenderService;
import com.guitarcenter.scheduler.service.ReportService;

@Service("reportService")
@Transactional
public class ReportServiceImpl implements ReportService {

    private static final Logger LOG = LoggerFactory.getLogger(ReportServiceImpl.class);

    @Autowired
    private AppointmentDAO appointmentDAO;
    
    
    @Autowired
    private AvailabilityService availabilityService;
    
    @Autowired
  	private MailSenderService mailSenderService;
    
  //Changes made for GSSP-238
  	String EMAIL_TEMPLATE_FOR_CONFLICTING_APPOINTMENT = "conflictingAppointmentsTemplate.ftl";


    @Override
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> generateInstructorScheduleReport(long locationId, Date startDate, Date endDate) {
      
    	if (LOG.isDebugEnabled()) {
            LOG.debug("Query appointment list by locationId {} and time range in {} and {}", locationId, startDate, endDate);
        }

        Criterion<Appointment, InstructorDateListDTO> criterion = AppointmentCriterion.findInstructorScheduleByLocationIdAndDateTime(locationId, startDate, endDate);
        List<InstructorDateListDTO> search = null;
        try {
            search = appointmentDAO.search(criterion);
        } catch (Exception e) {
            LOG.error("Caught an exception {} when querying appointment list by locationId {} and startTime {} and endTime {}" +
                    " in ReportService.generateInstructorScheduleReport", e, locationId, startDate, endDate);
        }
        if (search == null) {
            return Collections.emptyList();
        }
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        Map<Long, Map<String, Object>> cache = new HashMap<Long, Map<String, Object>>();
        for (InstructorDateListDTO instructorDateListDTO : search) {
            Map<String, Object> map = cache.get(instructorDateListDTO.getInstructor().getInstructorId());
            if (map == null){
                map = new HashMap<String, Object>();
                cache.put(instructorDateListDTO.getInstructor().getInstructorId(), map);
                result.add(map);
            }

            InstructorInfoDTO instructorInfoDTO = (InstructorInfoDTO) map.get(INSTRUCTOR_INFO_KEY);
            if (instructorInfoDTO == null){
                instructorInfoDTO = new InstructorInfoDTO();
                map.put(INSTRUCTOR_INFO_KEY, instructorInfoDTO);
                instructorInfoDTO.setInstructorId(instructorDateListDTO.getInstructor().getInstructorId().toString());
                //GCSS-499,added the Validation of null value on firstName or lastName
                Person instructor_person = instructorDateListDTO.getInstructor().getPerson();
                String insFirstName = instructor_person.getFirstName();
                String insLastName = instructor_person.getLastName();
                instructorInfoDTO.setInstructorName((StringUtils.isEmpty(insFirstName) ? "" : insFirstName) + " " + (StringUtils.isEmpty(insLastName) ? "" : insLastName));
                //changes made for 252
                String ExternalId=instructorDateListDTO.getExternal_id();
                Date startHours_date = instructorDateListDTO.getStartHours();
                Date endHours_date = instructorDateListDTO.getEndHours();
                String startHours = null;
                String endHours = null;
                if (startHours_date != null) {
                    startHours = buildTimeStr(startHours_date);
                }
                if (endHours_date != null) {
                    endHours = buildTimeStr(endHours_date);
                }
                instructorInfoDTO.setInstructorAvailibilityTime(startHours + " - " + endHours);
                instructorInfoDTO.setInstructorNameWithAvaiTime(instructorInfoDTO.getInstructorName() + " - " + instructorInfoDTO.getInstructorAvailibilityTime());
            }

            List<InstructorScheduleReportDTO> list = (List<InstructorScheduleReportDTO>) map.get(REPORT_DTO_LIST_KEY);
            if (list == null){
                list = new ArrayList<InstructorScheduleReportDTO>();
                map.put(REPORT_DTO_LIST_KEY, list);
            }
            for (InstructorScheduleDTO instructorScheduleDTO : instructorDateListDTO.getList()) {
                InstructorScheduleReportDTO dto = new InstructorScheduleReportDTO();
                
                dto.setActivityType(instructorScheduleDTO.getActivityName());
                
                dto.setCustomerName(this.buildCustomerName(instructorScheduleDTO.getCustomers()));
                dto.setInstructorId(instructorScheduleDTO.getInstructor().getInstructorId());
                
                /**
                 * GCSS-499,added the Validation of null value on firstName or lastName
                 */
                String ifn = instructorScheduleDTO.getInstructor().getPerson().getFirstName();
                String iln = instructorScheduleDTO.getInstructor().getPerson().getLastName();
                dto.setInstructorName((StringUtils.isEmpty(ifn) ? "" : ifn) + " " + (StringUtils.isEmpty(iln) ? "" : iln));
                
                dto.setDuration(this.buildDuration(instructorScheduleDTO.getDuration()));
                dto.setCancelled(instructorScheduleDTO.getCanceled().toString());
                dto.setStartTime(buildTimeStr(instructorScheduleDTO.getStartTime()));
                dto.setEndTime(buildTimeStr(instructorScheduleDTO.getEndTime()));
                // added for GSSP-200
                dto.setStartDate(buildDateStr1(instructorScheduleDTO.getStartTime()));

                dto.setQueryStartDate(buildDateStr(startDate));
                dto.setQueryEndDate(buildDateStr(endDate));
                dto.setTimeFrame(dto.getStartTime() + " - " + dto.getEndTime());
              
                list.add(dto);
            }
        }
        return result;
    }
    
    @Override
	public List<InstructorReportPagingDTO> generateInstructorAppointmentStatusReport(long locationId, Date startDate,
			Date endDate) {
    	
    	 List<InstructorReportPagingDTO> parent = new ArrayList<InstructorReportPagingDTO>();
    	 InstructorReportPagingDTO inReportPaging = new InstructorReportPagingDTO();
    	
    	Criterion<Appointment, InstructorAppointmentStatusDTO> criterion = AppointmentCriterion.findInstructorAppointmentStatusByLocationIdAndDateTime(locationId, startDate, endDate);
    	 List<InstructorAppointmentStatusDTO> search = null;
    	try {
             search = appointmentDAO.search(criterion);
             
             inReportPaging.setInstructorAppointmentStatusReports(search);
             parent.add(inReportPaging);
             
             
         } catch (Exception e) {
             LOG.error("Caught an exception {} when querying appointment list by locationId {} and startTime {} and endTime {}" +
                     " in ReportService.generateInstructorAppointmentStatusReport", e, locationId, startDate, endDate);
         }
    	
		return parent;
	}
    
    /**
     * For GCSS-594,add new page for each instructor each day
     */
    @Override
    public List<InstructorReportPagingDTO> generateInstructorReportPDF(long locationId, Date startDate, Date endDate, boolean isFromPage) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Query appointment list by locationId {} and time range in {} and {}", locationId, startDate, endDate);
        }

        Criterion<Appointment, InstructorDateListDTO> criterion = AppointmentCriterion.findInstructorScheduleByLocationIdAndDateTime(locationId, startDate, endDate);
        List<InstructorDateListDTO> search = null;
        try {
            search = appointmentDAO.search(criterion);
        } catch (Exception e) {
            LOG.error("Caught an exception {} when querying appointment list by locationId {} and startTime {} and endTime {}" +
                    " in ReportService.generateInstructorReportPDF", e, locationId, startDate, endDate);
        }
        if (search == null) {
            return Collections.emptyList();
        }

        List<InstructorReportPagingDTO> parent = new ArrayList<InstructorReportPagingDTO>();
        
        Map<String, InstructorInfoDTO> cache = new HashMap<String, InstructorInfoDTO>();
        
        for (InstructorDateListDTO instructorDateListDTO : search) {
            
            InstructorInfoDTO instructorInfoDTO = cache.get(instructorDateListDTO.getInstructor().getInstructorId() + "_" + instructorDateListDTO.getDate1());
            
            if (instructorInfoDTO == null){
                instructorInfoDTO = new InstructorInfoDTO();
                cache.put(instructorDateListDTO.getInstructor().getInstructorId() + "_" + instructorDateListDTO.getDate1(), instructorInfoDTO);
                instructorInfoDTO.setInstructorId(instructorDateListDTO.getInstructor().getInstructorId().toString());
                /**
                 * GCSS-499,added the Validation of null value on firstName or lastName
                 */
                Person instructor_person = instructorDateListDTO.getInstructor().getPerson();
                String insFirstName = instructor_person.getFirstName();
                String insLastName = instructor_person.getLastName();
                instructorInfoDTO.setInstructorName((StringUtils.isEmpty(insFirstName) ? "" : insFirstName) + " " + (StringUtils.isEmpty(insLastName) ? "" : insLastName));

                Date startHours_date = instructorDateListDTO.getStartHours();
                Date endHours_date = instructorDateListDTO.getEndHours();
                String startHours = null;
                String endHours = null;
                if (startHours_date != null) {
                    startHours = buildTimeStr(startHours_date);
                }
                if (endHours_date != null) {
                    endHours = buildTimeStr(endHours_date);
                }
                instructorInfoDTO.setInstructorAvailibilityTime(startHours + " - " + endHours);
                instructorInfoDTO.setInstructorNameWithAvaiTime(instructorInfoDTO.getInstructorName() + " - " + instructorInfoDTO.getInstructorAvailibilityTime());
            }
            
            List<InstructorScheduleReportDTO> list = new ArrayList<InstructorScheduleReportDTO>();

            for (int i=0; i<instructorDateListDTO.getList().size(); i++) {
            	InstructorScheduleDTO instructorScheduleDTO = instructorDateListDTO.getList().get(i);
            	InstructorScheduleReportDTO dto = new InstructorScheduleReportDTO();
            	dto.setActivityType(instructorScheduleDTO.getActivityName());
            	dto.setCustomerName(this.buildCustomerName(instructorScheduleDTO.getCustomers()));
            	dto.setInstructorId(instructorScheduleDTO.getInstructor().getInstructorId());
            	//GSSP-311 Changes for set the room name 
            	dto.setRoomName(instructorScheduleDTO.getRoomName());
            	//Changes made for GSSP-252            	
            	dto.setExternal_id(instructorScheduleDTO.getInstructor().getExternalId());

            	/**
            	 * GCSS-499,added the Validation of null value on firstName or lastName
            	 */
            	String ifn = instructorScheduleDTO.getInstructor().getPerson().getFirstName();
            	String iln = instructorScheduleDTO.getInstructor().getPerson().getLastName();
            	dto.setInstructorName((StringUtils.isEmpty(ifn) ? "" : ifn) + " " + (StringUtils.isEmpty(iln) ? "" : iln));
            	dto.setDuration(this.buildDuration(instructorScheduleDTO.getDuration()));
            	dto.setCancelled(instructorScheduleDTO.getCanceled().toString());
            	dto.setStartTime(buildTimeStr(instructorScheduleDTO.getStartTime()));
            	dto.setEndTime(buildTimeStr(instructorScheduleDTO.getEndTime()));
            	 // added for GSSP-200
            	dto.setStartDate(buildDateStr1(instructorScheduleDTO.getStartTime()));
            	dto.setQueryStartDate(buildDateStr(startDate));
            	dto.setQueryEndDate(buildDateStr(endDate));
            	dto.setTimeFrame(dto.getStartTime() + " - " + dto.getEndTime());
            	list.add(dto);
            }
            if(!isFromPage) {
            	List<List<InstructorScheduleReportDTO>> partition = Lists.partition(list, 24);
            	for (List<InstructorScheduleReportDTO> list2 : partition) {
            		InstructorReportPagingDTO irp = new InstructorReportPagingDTO();
        			
        			List<InstructorInfoDTO> instructorInfoDTOs = new ArrayList<InstructorInfoDTO>();
        			instructorInfoDTOs.add(instructorInfoDTO);
        			irp.setInstructorInfo(instructorInfoDTOs);
        			irp.setCurrentDate(instructorDateListDTO.getDate1());
        			irp.setInstructorInfoDTO(instructorInfoDTO);
        			irp.setInstructorReports(list2);
        			
        			parent.add(irp);
				}
            } else {
            	InstructorReportPagingDTO pagingDTO = new InstructorReportPagingDTO();
            	pagingDTO.setInstructorInfoDTO(instructorInfoDTO);
            	pagingDTO.setInstructorReports(list);
            	parent.add(pagingDTO);
            }
        }
    	return parent;
    }

    @Override
    public List<Map<String, Object>> generateRehearsalBookingReport(long profileId, Date startDate, Date endDate) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Query rehearsal appointment list by profileId {} and time range in {} and {}", profileId, startDate, endDate);
        }

        Criterion<Appointment, RehearsalBookingDateListDTO> criterion = AppointmentCriterion.findRehearsalBookingByProfileIdAndCreateTime(profileId, startDate, endDate);
        List<RehearsalBookingDateListDTO> search = null;

        try {
            search = appointmentDAO.search(criterion);
        } catch (Exception e) {
            LOG.error("Caught an exception {} when querying appointment list by profileId {} and startTime {} and endTime {}" +
                    " in ReportService.generateRehearsalScheduleReport", e, profileId, startDate, endDate);
        }
        if (search == null) {
            return Collections.emptyList();
        }
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        for (RehearsalBookingDateListDTO rehearsalBookingDateListDTO : search) {
            Map<String, Object> map = new HashMap<String, Object>();
            result.add(map);
            map.put(REPORT_DATE_KEY, rehearsalBookingDateListDTO.getDate1());
            List<RehearsalBookingReportDTO> list = new ArrayList<RehearsalBookingReportDTO>();
            map.put(REPORT_DTO_LIST_KEY, list);
            for (RehearsalBookingDTO rehearsalBookingDTO : rehearsalBookingDateListDTO.getList()) {
                RehearsalBookingReportDTO dto = new RehearsalBookingReportDTO();
                
                dto.setActivityType(rehearsalBookingDTO.getActivityName());
                
                dto.setBandName(rehearsalBookingDTO.getBandName());
                dto.setCustomerName(buildCustomerName(rehearsalBookingDTO.getCustomers()));
                dto.setRoomName(rehearsalBookingDTO.getRoomName());
                dto.setStartTime(buildTimeStr(rehearsalBookingDTO.getStartTime()));
                dto.setDuration(buildDuration(rehearsalBookingDTO.getDuration()));
                dto.setStartDateStr(buildDateStr(rehearsalBookingDTO.getStartTime()));

                dto.setQueryStartDate(buildDateStr(startDate));
                dto.setQueryEndDate(buildDateStr(endDate));

                list.add(dto);
            }
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> generateRehearsalScheduleReport(long profileId, Date startDate, Date endDate) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Query rehearsal appointment list by profileId {} and time range in {} and {}", profileId, startDate, endDate);
        }

        Criterion<Appointment, RehearsalScheduleDateListDTO> criterion = AppointmentCriterion.findRehearsalScheduleByProfileIdAndDateTime(profileId, startDate, endDate);
        List<RehearsalScheduleDateListDTO> search = null;

        try {
            search = appointmentDAO.search(criterion);
        } catch (Exception e) {
            LOG.error("Caught an exception {} when querying appointment list by profileId {} and startTime {} and endTime {}" +
                    " in ReportService.generateRehearsalScheduleReport", e, profileId, startDate, endDate);
        }
        if (search == null) {
            return Collections.emptyList();
        }
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
        for (RehearsalScheduleDateListDTO rehearsalScheduleDateListDTO : search) {
            Map<String, Object> map = new HashMap<String, Object>();
            result.add(map);
            map.put(REPORT_DATE_KEY, rehearsalScheduleDateListDTO.getDate1());
            List<RehearsalScheduleReportDTO> list = new ArrayList<RehearsalScheduleReportDTO>();
            map.put(REPORT_DTO_LIST_KEY, list);
            for (RehearsalScheduleDTO rehearsalScheduleDTO : rehearsalScheduleDateListDTO.getList()) {
                RehearsalScheduleReportDTO dto = new RehearsalScheduleReportDTO();
                
                dto.setActivityType(rehearsalScheduleDTO.getActivityName());
                
                dto.setBandName(rehearsalScheduleDTO.getBandName());
                dto.setCustomerName(buildCustomerName(rehearsalScheduleDTO.getCustomers()));
                dto.setRoomName(rehearsalScheduleDTO.getRoomName());
                dto.setStartTime(buildTimeStr(rehearsalScheduleDTO.getStartTime()));
                dto.setDuration(buildDuration(rehearsalScheduleDTO.getDuration()));
                dto.setStartDateStr(buildDateStr(rehearsalScheduleDTO.getStartTime()));

                dto.setQueryStartDate(buildDateStr(startDate));
                dto.setQueryEndDate(buildDateStr(endDate));

                list.add(dto);
            }
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> generateMasterScheduleReport(long profileId, Date startDate, Date endDate) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Query master appointment list by profileId {} and time range in {} and {}", profileId, startDate, endDate);
        }

        Criterion<Appointment, MasterDailyDateListDTO> criterionAppointment = AppointmentCriterion.findDailyMasterByProfileIdAndDateTime(profileId, startDate, endDate);
        Criterion<Appointment, Map<String, List<InstructorAppointmentBusinessHours>>> criterionInstructor = AppointmentCriterion.findInstructorAppointmentBusinessHoursByProfileIdAndDateTime(profileId, startDate, endDate);
        List<MasterDailyDateListDTO> searchAppointment = null;
        Map<String, List<InstructorAppointmentBusinessHours>> searchInstructor = null;

        try {
            searchAppointment = appointmentDAO.search(criterionAppointment);
            searchInstructor = appointmentDAO.get(criterionInstructor);
        } catch (Exception e) {
            LOG.error("Caught an exception {} when querying appointment list by profileId {} and startTime {} and endTime {}" +
                    " in ReportService.generateRehearsalScheduleReport", e, profileId, startDate, endDate);
        }
        if (searchAppointment == null) {
            return Collections.emptyList();
        }
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();

        for (MasterDailyDateListDTO masterDailyDateListDTO : searchAppointment) {
            String date1 = masterDailyDateListDTO.getDate1();
            Map<String, Object> map = new HashMap<String, Object>();
            result.add(map);
            map.put(REPORT_DATE_KEY, date1);
            List<MasterScheduleReportDTO> list = new ArrayList<MasterScheduleReportDTO>();
            map.put(REPORT_DTO_LIST_KEY, list);
            for (MasterDailyDTO masterDailyDTO : masterDailyDateListDTO.getList()) {
                MasterScheduleReportDTO dto = new MasterScheduleReportDTO();
                
                dto.setActivityType(masterDailyDTO.getActivityName());
                
                dto.setCustomerName(buildCustomerName(masterDailyDTO.getCustomers()));
                dto.setDuration(buildDuration(masterDailyDTO.getDuration()));
                dto.setEndTime(buildTimeStr(masterDailyDTO.getEndTime()));
                dto.setInstructorName(buildInstructorName(masterDailyDTO.getInstructor()));
                //GSSP-311 Changes for added the room name
                dto.setProfileRoomName((masterDailyDTO.getRoomName()));
                dto.setStartTime(buildTimeStr(masterDailyDTO.getStartTime()));
                dto.setStartDateStr(buildDateStr(masterDailyDTO.getStartTime()));

                /**
                 * Add notes to report,for gcss-576
                 */
                dto.setNotes(StringUtils.isBlank(masterDailyDTO.getNotes()) ? "" : masterDailyDTO.getNotes());
                
                dto.setQueryStartDate(buildDateStr(startDate));
                dto.setQueryEndDate(buildDateStr(endDate));

                list.add(dto);
            }
            if (searchInstructor != null) {
                List<InstructorAppointmentBusinessHours> list1 = searchInstructor.get(date1);
                if (list1 != null){
                    for (InstructorAppointmentBusinessHours obj : list1) {
                        obj.setStartDateStr(buildDateStr(obj.getCurrentDate()));
                        obj.setStartTime(buildTimeStr(obj.getMinimumTime()));
                        obj.setEndTime(buildTimeStr(obj.getMaximumTime()));

                        obj.setScheduleTime(obj.getStartTime() + "-" + obj.getEndTime());
                        
                        //GCSS-499,added the Validation of null value on firstName or lastName
                        obj.setFullName((StringUtils.isEmpty(obj.getFirstName()) ? "" : obj.getFirstName()) + " " + (StringUtils.isEmpty(obj.getLastName()) ? "" : obj.getLastName()));
                    }
                    map.put(EMPLOYEE_LIST_KEY, list1);
                }
            }

        }
        return result;
    }
    
    @Override
    public List<Map<String, Object>> findCancelledAppointmentReport(Long pProfileId, Date pStartTime, Date pEndTime, boolean justLessonActivities) {
    	List<CancelledAppointmentDateListDTO> searchAppointment = null;
		try {
			Criterion<Appointment, CancelledAppointmentDateListDTO> criterion = AppointmentCriterion.findCancelledApptReportByProfileIdAndDateTime(pProfileId, pStartTime, pEndTime, justLessonActivities);
			searchAppointment = appointmentDAO.search(criterion);
		} catch (Exception e) {
			LOG.error("Caught exception {} while query cancelled appointment list", e);
		}
        if (searchAppointment == null) {
            return Collections.emptyList();
        }
        List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();

        for (CancelledAppointmentDateListDTO cancelledDailyDateListDTO : searchAppointment) {
            String date1 = cancelledDailyDateListDTO.getDate1();
            Map<String, Object> map = new HashMap<String, Object>();
            result.add(map);
            map.put(REPORT_DATE_KEY, date1);
            List<CancelledAppointmentReportDTO> list = new ArrayList<CancelledAppointmentReportDTO>();
            map.put(REPORT_DTO_LIST_KEY, list);
            for (CancelledApptDailyDTO cancelledDailyDTO : cancelledDailyDateListDTO.getList()) {
                CancelledAppointmentReportDTO dto = new CancelledAppointmentReportDTO();
                
                dto.setActivityType(cancelledDailyDTO.getActivityName());
                
                dto.setTime(buildTimeStr(cancelledDailyDTO.getStartTime()) + " - " + buildTimeStr(cancelledDailyDTO.getEndTime()));
                dto.setCustomerName(buildCustomerName(cancelledDailyDTO.getCustomers()));
                dto.setEndTime(buildTimeStr(cancelledDailyDTO.getEndTime()));
                dto.setInstructorName(buildInstructorName(cancelledDailyDTO.getInstructor()));
                dto.setStartTime(buildTimeStr(cancelledDailyDTO.getStartTime()));
                // added for GSSP-200
                dto.setStartDateStr(buildDateStr1(cancelledDailyDTO.getStartTime()));
                dto.setNotes(cancelledDailyDTO.getNotes());                        
                dto.setQueryStartDate(buildDateStr(pStartTime));
                dto.setQueryEndDate(buildDateStr(pEndTime));
              //GSSP-214 changes
                dto.setCancelledUser(buildCancelledUser(cancelledDailyDTO.getCancelledUser()));
                dto.setCancelledTime(buildTimeStr1(cancelledDailyDTO.getCancelledTime()));
                //GSSP-269 changes
                Map<String, String> cancelledReasonMap = new HashMap<String, String>();
                cancelledReasonMap.put(cancelledDailyDTO.getActivityName(), cancelledDailyDTO.getCancelledReason());                    
                buildCancelledReason(cancelledReasonMap);
                for (Map.Entry<String, String> entry : cancelledReasonMap.entrySet()){
                dto.setCancelledReason(entry.getValue());}
                cancelledReasonMap.clear();
                list.add(dto);
            }
        }
		return result;
	}
    
    /**
     * Added for NewInsAptReport _ June 2015 Enhancement
     */ 
    //GSSP-190,added
    @Override
    public List<InstructorReportPagingDTO> generateInstructorOpenAppointmentsReportPDF(long locationId, Date startDate, Date endDate, String inputExternalId, boolean isFromPage, String instructorName) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Query appointment list by locationId {} and time range in {} and {} and inputExternalId {}", locationId, startDate, endDate, inputExternalId);
        }

        Criterion<Appointment, InstructorOpenAppointmentsDTO> criterion = AppointmentCriterion.findInstructorOpenAppointmentsByLocationIdAndDateTime(locationId, startDate, endDate, inputExternalId,instructorName);
        List<InstructorOpenAppointmentsDTO> search = null;
        try {
            search = appointmentDAO.search(criterion);
        } catch (Exception e) {
            LOG.error("Caught an exception {} when querying appointment list by locationId {} and startTime {} and endTime {} inputExternalId {}" +
                    " in ReportService.generateInstructorOpenAppointmentsReportPDF", e, locationId, startDate, endDate, inputExternalId);
        }
        if (search == null) {
            return Collections.emptyList();
        }

        List<InstructorReportPagingDTO> parent = new ArrayList<InstructorReportPagingDTO>();
        
        Map<String, InstructorInfoDTO> cache = new HashMap<String, InstructorInfoDTO>();
        
        List<InstructorScheduleReportDTO> list = new ArrayList<InstructorScheduleReportDTO>();
        
        List<InstructorInfoDTO> insInfoDTO = new ArrayList<InstructorInfoDTO>();
        
        for (InstructorOpenAppointmentsDTO instructorOpenAppointmentsDTO : search) {
            
            InstructorInfoDTO instructorInfoDTO = cache.get(instructorOpenAppointmentsDTO.getInstructor().getInstructorId() + "_" + instructorOpenAppointmentsDTO.getDate1());
            
            if (instructorInfoDTO == null) {
                instructorInfoDTO = new InstructorInfoDTO();
                cache.put(instructorOpenAppointmentsDTO.getInstructor().getInstructorId() + "_" + instructorOpenAppointmentsDTO.getDate1(), instructorInfoDTO);
                instructorInfoDTO.setInstructorId(instructorOpenAppointmentsDTO.getInstructor().getInstructorId().toString());
                Person instructor_person = instructorOpenAppointmentsDTO.getInstructor().getPerson();
                String insFirstName = instructor_person.getFirstName();
                String insLastName = instructor_person.getLastName();
                instructorInfoDTO.setInstructorName((StringUtils.isEmpty(insFirstName) ? "" : insFirstName) + " " + (StringUtils.isEmpty(insLastName) ? "" : insLastName));
                insInfoDTO.add(instructorInfoDTO);
           } 
            
            	InstructorScheduleReportDTO dto = new InstructorScheduleReportDTO();
            	dto.setActivityType(instructorOpenAppointmentsDTO.getActivityName());
            	dto.setCustomerName(this.buildCustomerName(instructorOpenAppointmentsDTO.getCustomers()));
            	dto.setInstructorId(instructorOpenAppointmentsDTO.getInstructor().getInstructorId());
            	/**
            	 * GCSS-499,added the Validation of null value on firstName or lastName
            	 */
            	String ifn = instructorOpenAppointmentsDTO.getInstructor().getPerson().getFirstName();
            	String iln = instructorOpenAppointmentsDTO.getInstructor().getPerson().getLastName();
            	dto.setInstructorName((StringUtils.isEmpty(ifn) ? "" : ifn) + " " + (StringUtils.isEmpty(iln) ? "" : iln));
            	dto.setDuration(this.buildDuration(instructorOpenAppointmentsDTO.getDuration()));
            	dto.setCancelled(instructorOpenAppointmentsDTO.getCanceled().toString());
            	dto.setStartTime(buildTimeStr(instructorOpenAppointmentsDTO.getStartTime()));
            	dto.setEndTime(buildTimeStr(instructorOpenAppointmentsDTO.getEndTime()));
            	dto.setStartDate(instructorOpenAppointmentsDTO.getDate1());
            	dto.setQueryStartDate(buildDateStr(startDate));
            	dto.setQueryEndDate(buildDateStr(endDate));
            	dto.setTimeFrame(dto.getStartTime() + " - " + dto.getEndTime());
            	dto.setRoomName(instructorOpenAppointmentsDTO.getRoomName());
            	list.add(dto);
           }
 
            if(!isFromPage) {
            	List<List<InstructorScheduleReportDTO>> partition = Lists.partition(list, 24);
            	for (List<InstructorScheduleReportDTO> list2 : partition) {
            		InstructorReportPagingDTO irp = new InstructorReportPagingDTO();
        			irp.setInstructorInfo(insInfoDTO);
        			irp.setInstructorReports(list2);
        			
        			parent.add(irp);
				}
            } else {
            	InstructorReportPagingDTO pagingDTO = new InstructorReportPagingDTO();
            	pagingDTO.setInstructorInfo(insInfoDTO);
            	pagingDTO.setInstructorReports(list);
            	parent.add(pagingDTO);
            }

    	return parent;
    }
    
    /**
     * For GSSP-170, Conflicting Appointments by Instructor
     */    
    @Override
    public List<InstructorReportPagingDTO> generateConflictAppointmentsReportByInstructor(Long pProfileId, Date startDate, Date endDate) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Query conflicting appointments list by instructor based on profileId {} and time range in {} and {}", pProfileId, startDate, endDate);
        }

        Criterion<Appointment, InstructorOpenAppointmentsDTO> criterion = AppointmentCriterion.findConflictAppointmentsByInstructorProfileIdAndDateTime(pProfileId, startDate, endDate);
        List<InstructorOpenAppointmentsDTO> search = null;
        try {
            search = appointmentDAO.search(criterion);
        } catch (Exception e) {
            LOG.error("Caught an exception {} when querying conflicting appointments list by instructor based on profileId {} and time range in {} and {}" +
                    " in ReportService.generateConflictAppointmentsReportByInstructor", e, pProfileId, startDate, endDate);
        }
        if (search == null) {
            return Collections.emptyList();
        }
        
        Date currentDateTime  = new Date();

        List<InstructorReportPagingDTO> parent = new ArrayList<InstructorReportPagingDTO>();
        
        List<InstructorScheduleReportDTO> list = new ArrayList<InstructorScheduleReportDTO>();
        
        for (InstructorOpenAppointmentsDTO instructorOpenAppointmentsDTO : search) {
            
            InstructorScheduleReportDTO dto = new InstructorScheduleReportDTO();
        	dto.setActivityType(instructorOpenAppointmentsDTO.getActivityName());
        	dto.setCustomerName(this.buildCustomerName(instructorOpenAppointmentsDTO.getCustomers()));
        	dto.setAppointmentId(instructorOpenAppointmentsDTO.getAppointmentId());
        	String rStatus = instructorOpenAppointmentsDTO.getRecurringStatus();
        	dto.setRecurringStatus((StringUtils.isEmpty(rStatus) ? "N" : rStatus));
        	
        	//Enable Cancel Button only for future appointments
        	if(currentDateTime.before(instructorOpenAppointmentsDTO.getStartTime()))
        	{
        		dto.setShowCancelButton(true);
        	}     
        	else
        	{
        		dto.setShowCancelButton(false);
        	}
        	/**
        	 * GCSS-499,added the Validation of null value on firstName or lastName
        	 */
        	String ifn = instructorOpenAppointmentsDTO.getInstructor().getPerson().getFirstName();
        	String iln = instructorOpenAppointmentsDTO.getInstructor().getPerson().getLastName();
        	dto.setInstructorName((StringUtils.isEmpty(ifn) ? "" : ifn) + " " + (StringUtils.isEmpty(iln) ? "" : iln));
        	dto.setDuration(this.buildDuration(instructorOpenAppointmentsDTO.getDuration()));
        	dto.setStartTime(buildTimeStr(instructorOpenAppointmentsDTO.getStartTime()));
        	dto.setEndTime(buildTimeStr(instructorOpenAppointmentsDTO.getEndTime()));
        	dto.setStartDate(instructorOpenAppointmentsDTO.getDate1());
        	dto.setQueryStartDate(buildDateStr(startDate));
        	dto.setQueryEndDate(buildDateStr(endDate));
        	dto.setTimeFrame(dto.getStartTime() + " - " + dto.getEndTime());
        	list.add(dto);
           }
 
        	InstructorReportPagingDTO pagingDTO = new InstructorReportPagingDTO();
        	pagingDTO.setInstructorReports(list);
        	parent.add(pagingDTO);

    	return parent;
    }
    
    /**
     * For GSSP-170, Conflicting Appointments by Room
     */    
    @Override
    public List<InstructorReportPagingDTO> generateConflictAppointmentsReportByRoom(Long pProfileId, Date startDate, Date endDate) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Query conflicting appointments list by room based on profileId {} and time range in {} and {}", pProfileId, startDate, endDate);
        }

        Criterion<Appointment, InstructorOpenAppointmentsDTO> criterion = AppointmentCriterion.findConflictAppointmentsByRoomProfileIdAndDateTime(pProfileId, startDate, endDate);
        List<InstructorOpenAppointmentsDTO> search = null;
        try {
            search = appointmentDAO.search(criterion);
        } catch (Exception e) {
            LOG.error("Caught an exception {} when querying conflicting appointments list by room based on profileId {} and time range in {} and {}" +
                    " in ReportService.generateConflictAppointmentsReportByRoom", e, pProfileId, startDate, endDate);
        }
        if (search == null) {
            return Collections.emptyList();
        }
        
        Date currentDateTime  = new Date();

        List<InstructorReportPagingDTO> parent = new ArrayList<InstructorReportPagingDTO>();
        
        List<InstructorScheduleReportDTO> list = new ArrayList<InstructorScheduleReportDTO>();
        
        for (InstructorOpenAppointmentsDTO instructorOpenAppointmentsDTO : search) {
            
            InstructorScheduleReportDTO dto = new InstructorScheduleReportDTO();
        	dto.setActivityType(instructorOpenAppointmentsDTO.getActivityName());
        	dto.setCustomerName(this.buildCustomerName(instructorOpenAppointmentsDTO.getCustomers()));
        	dto.setAppointmentId(instructorOpenAppointmentsDTO.getAppointmentId());
        	String rStatus = instructorOpenAppointmentsDTO.getRecurringStatus();
        	dto.setRecurringStatus((StringUtils.isEmpty(rStatus) ? "N" : rStatus));
        	
        	//Enable Cancel Button only for future appointments
        	if(currentDateTime.before(instructorOpenAppointmentsDTO.getStartTime()))
        	{
        		dto.setShowCancelButton(true);
        	}     
        	else
        	{
        		dto.setShowCancelButton(false);
        	}
        	
        	dto.setDuration(this.buildDuration(instructorOpenAppointmentsDTO.getDuration()));
        	dto.setStartTime(buildTimeStr(instructorOpenAppointmentsDTO.getStartTime()));
        	dto.setEndTime(buildTimeStr(instructorOpenAppointmentsDTO.getEndTime()));
        	dto.setStartDate(instructorOpenAppointmentsDTO.getDate1());
        	dto.setQueryStartDate(buildDateStr(startDate));
        	dto.setQueryEndDate(buildDateStr(endDate));
        	dto.setTimeFrame(dto.getStartTime() + " - " + dto.getEndTime());
        	String rName = instructorOpenAppointmentsDTO.getRoomName();
        	dto.setRoomName((StringUtils.isEmpty(rName) ? "" : rName));
        	list.add(dto);
        }
 
        	InstructorReportPagingDTO pagingDTO = new InstructorReportPagingDTO();
        	pagingDTO.setInstructorReports(list);
        	parent.add(pagingDTO);

    	return parent;
    }
    
    /**
     * For GSSP-161, Instructor outside availability appointments report
     */    
    @Override
    public List<InstructorReportPagingDTO> generateInstructorOutsideAppointmentsReportPDF(Long pProfileId, String startTime, String endTime, String inputExternalId, String dayType) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Query appointment list by ProfileId {} and time range in {} and {} and inputExternalId {} and dayType {}", pProfileId, startTime, endTime, inputExternalId, dayType);
        }

        Criterion<Appointment, InstructorOpenAppointmentsDTO> criterion = AppointmentCriterion.findInstructorOutsideAppointmentsByLocationIdAndDateTime(pProfileId, startTime, endTime, inputExternalId, dayType);
        List<InstructorOpenAppointmentsDTO> search = null;
        try {
            search = appointmentDAO.search(criterion);
        } catch (Exception e) {
            LOG.error("Caught an exception {} when querying appointment list by ProfileId {} and startTime {} and endTime {} inputExternalId {} and dayType {}" +
                    " in ReportService.generateInstructorOutsideAppointmentsReportPDF", e, pProfileId, startTime, endTime, inputExternalId, dayType);
        }
        if (search == null) {
            return Collections.emptyList();
        }

        List<InstructorReportPagingDTO> parent = new ArrayList<InstructorReportPagingDTO>();
        
        List<InstructorScheduleReportDTO> list = new ArrayList<InstructorScheduleReportDTO>();
        
        for (InstructorOpenAppointmentsDTO instructorOpenAppointmentsDTO : search) {
            
            InstructorScheduleReportDTO dto = new InstructorScheduleReportDTO();
        	dto.setActivityType(instructorOpenAppointmentsDTO.getActivityName());
        	dto.setCustomerName(this.buildCustomerName(instructorOpenAppointmentsDTO.getCustomers()));
        	dto.setInstructorId(instructorOpenAppointmentsDTO.getInstructor().getInstructorId());
        	/**
        	 * Added the Validation of null value on firstName or lastName
        	 */
        	String ifn = instructorOpenAppointmentsDTO.getInstructor().getPerson().getFirstName();
        	String iln = instructorOpenAppointmentsDTO.getInstructor().getPerson().getLastName();
        	dto.setInstructorName((StringUtils.isEmpty(ifn) ? "" : ifn) + " " + (StringUtils.isEmpty(iln) ? "" : iln));
        	dto.setDuration(this.buildDuration(instructorOpenAppointmentsDTO.getDuration()));
        	dto.setStartTime(buildTimeStr(instructorOpenAppointmentsDTO.getStartTime()));
        	dto.setEndTime(buildTimeStr(instructorOpenAppointmentsDTO.getEndTime()));
        	dto.setStartDate(instructorOpenAppointmentsDTO.getDate1());
        	/*dto.setQueryStartDate(buildDateStr(startDate));
        	dto.setQueryEndDate(buildDateStr(endDate));*/
        	dto.setTimeFrame(dto.getStartTime() + " - " + dto.getEndTime());
        	String rName = instructorOpenAppointmentsDTO.getRoomName();
        	dto.setRoomName((StringUtils.isEmpty(rName) ? "" : rName));
        	list.add(dto);
           }
 
	        InstructorReportPagingDTO pagingDTO = new InstructorReportPagingDTO();
	    	pagingDTO.setInstructorReports(list);
	    	parent.add(pagingDTO);

    	return parent;
    }
    
    /**
     * Helper method to build duration literal string for UI displaying
     *
     * @param minutes
     * @return
     */
    private String buildDuration(float minutes) {//For GCSS-520,to support the half hour in creating app,change the paramater minutes to float
        String duration = "";
        if (minutes < 60) {
            duration = minutes + " minutes";
        } else if (60 == minutes) {
            duration = "1 Hour";
        } else {
            duration = (minutes / 60) + " Hrs";
        }
        duration = duration.replace(".0", "");
        return duration;
    }

    /**
     * Helper method to build customer name from a customer list
     *
     * @param customerList
     * @return
     */
    private String buildCustomerName(Collection<Customer> customerList) {
        StringBuilder name = new StringBuilder();
        if (null != customerList && !customerList.isEmpty()) {
            for (Customer c : customerList) {
                String firstName = c.getPerson().getFirstName();
                String lastName = c.getPerson().getLastName();
                if (StringUtils.isNotBlank(firstName)) {
                    name.append(firstName);
                    name.append(" ");
                }
                if (StringUtils.isNotBlank(lastName)) {
                    name.append(lastName);
                }
                name.append(", ");
            }
            name.deleteCharAt(name.length() - 2);
        }
        return name.toString();
    }

    private String buildDateStr(Date date) {
        return new DateTime(date).toString(DateTimeFormat.forPattern(DateTimeUtil.DATE_PATTERN_SLASH));
    }
//added for GSSP-200
    private String buildDateStr1(Date date) {
        return new DateTime(date).toString(DateTimeFormat.forPattern(DateTimeUtil.DATE_PATTERN_HIPHEN));
    }
    /**
     * Helper method to build time string from a Date
     *
     * @param date
     * @return
     */
    private String buildTimeStr(Date date) {
        String result = new DateTime(date).toString(DateTimeFormat.forPattern("hh:mm a"));
        if ("11:59 PM".equals(result)) {
            result = "12:00 AM";
        }
        return result;
    }
  //GSSP-214 changes
    private String buildTimeStr1(String string) {
//        String result = new DateTime(string).toString(DateTimeFormat.forPattern(""));
//        if ("11:59 PM".equals(result)) {
//            result = "12:00 AM";
//        }
        return string;
    }
    /**
     * Helper method to build instructor name from a instructor object
     *
     * @param i
     * @return
     */
    private String buildInstructorName(Instructor i) {
        String instructorName = "";
        if (null != i) {
            String firstName = i.getPerson().getFirstName();
            String lastName = i.getPerson().getLastName();
            if (StringUtils.isNotBlank(firstName)) {
                instructorName += firstName + " ";
            }
            if (StringUtils.isNotBlank(lastName)) {
                instructorName += lastName;
            }
        }
        return instructorName;
    }
    //for GSSP-214
    private String buildCancelledUser(Instructor i) {
        String cancelledUser = "";
        if (null != i) {
            String firstName = i.getPerson().getFirstName();
            String lastName = i.getPerson().getLastName();
            if (StringUtils.isNotBlank(firstName)) {
            	cancelledUser += firstName + " ";
            }
            if (StringUtils.isNotBlank(lastName)) {
            	cancelledUser += lastName;
            }
        }
        return cancelledUser;
    }
    
    //GSSP-269 
    //LES-631 Activity changed from Jump Start to Trial Lesson 
    private Map<String, String> buildCancelledReason(Map<String, String> cancelledReasonMap){
    	   	 
    	if(cancelledReasonMap.containsValue(""))
    	{
    		if((cancelledReasonMap.containsKey("In-Store Floor")) || (cancelledReasonMap.containsKey("In-Store Trial Lesson")))
    		{
    			
    			cancelledReasonMap.put(CANCELLED_REASON, "NA"); 
    		}
    		
       	}
    	    return cancelledReasonMap  ;
    	
    }
//added for GSSP-185
    @Override
    public List<InstructorReportPagingDTO> generateActiveStudentsReport(long locationId, Date startDate, Date endDate, String activityType, boolean isFromPage, String instructorName) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Query appointment list by locationId {} and time range in {} and {} and activityType{}", locationId, startDate, endDate, activityType);
        }

        Criterion<Appointment, ActiveStudentsDTO> criterion = AppointmentCriterion.findActiveStudentsByActivityAndDateTime(locationId, startDate, endDate, activityType, instructorName);
        List<ActiveStudentsDTO> search = null;
        try {
            search = appointmentDAO.search(criterion);
        } catch (Exception e) {
            LOG.error("Caught an exception {} when querying appointment list by locationId {} and startTime {} and endTime {} activityType {}" +
                    " in ReportService.generateActiveStudentsReport", e, locationId, startDate, endDate, activityType,instructorName);
        }
        if (search == null) {
            return Collections.emptyList();
        }

        List<InstructorReportPagingDTO> parent = new ArrayList<InstructorReportPagingDTO>();
        
        Map<String, InstructorInfoDTO> cache = new HashMap<String, InstructorInfoDTO>();
        
        List<InstructorScheduleReportDTO> list = new ArrayList<InstructorScheduleReportDTO>();
        
        List<InstructorInfoDTO> insInfoDTO = new ArrayList<InstructorInfoDTO>();
        
        for (ActiveStudentsDTO activeStudentsDTO : search) {
            
            InstructorInfoDTO instructorInfoDTO = cache.get(activeStudentsDTO.getInstructor().getInstructorId() + "_" + activeStudentsDTO.getDate1());
            
            if (instructorInfoDTO == null) {
                instructorInfoDTO = new InstructorInfoDTO();
                cache.put(activeStudentsDTO.getInstructor().getInstructorId() + "_" + activeStudentsDTO.getDate1(), instructorInfoDTO);
                instructorInfoDTO.setInstructorId(activeStudentsDTO.getInstructor().getInstructorId().toString());
                Person instructor_person = activeStudentsDTO.getInstructor().getPerson();
                String insFirstName = instructor_person.getFirstName();
                String insLastName = instructor_person.getLastName();
                instructorInfoDTO.setInstructorName((StringUtils.isEmpty(insFirstName) ? "" : insFirstName) + " " + (StringUtils.isEmpty(insLastName) ? "" : insLastName));
                insInfoDTO.add(instructorInfoDTO);
           } 
            
            	InstructorScheduleReportDTO dto = new InstructorScheduleReportDTO();
            	dto.setActivityType(activeStudentsDTO.getActivityName());
            	dto.setCustomerName(this.buildCustomerName(activeStudentsDTO.getCustomers()));
            	dto.setInstructorId(activeStudentsDTO.getInstructor().getInstructorId());
            	/**
            	 * added the Validation of null value on firstName or lastName
            	 */
            	String ifn = activeStudentsDTO.getInstructor().getPerson().getFirstName();
            	String iln = activeStudentsDTO.getInstructor().getPerson().getLastName();
               	dto.setInstructorName((StringUtils.isEmpty(ifn) ? "" : ifn) + " " + (StringUtils.isEmpty(iln) ? "" : iln));
            	dto.setDuration(this.buildDuration(activeStudentsDTO.getDuration()));
            	dto.setCancelled(activeStudentsDTO.getCanceled().toString());
            	dto.setStartTime(buildTimeStr(activeStudentsDTO.getStartTime()));
            	dto.setEndTime(buildTimeStr(activeStudentsDTO.getEndTime()));
            	dto.setStartDate(activeStudentsDTO.getDate1());
            	dto.setQueryStartDate(buildDateStr(startDate));
            	dto.setQueryEndDate(buildDateStr(endDate));
            	dto.setTimeFrame(dto.getStartTime() + " - " + dto.getEndTime());
            	dto.setRoomName(activeStudentsDTO.getRoomName());
            	dto.setCustomerEmail(activeStudentsDTO.getStudentEmail());
            	list.add(dto);
           }
 
            if(!isFromPage) {
            	List<List<InstructorScheduleReportDTO>> partition = Lists.partition(list, 24);
            	for (List<InstructorScheduleReportDTO> list2 : partition) {
            		InstructorReportPagingDTO irp = new InstructorReportPagingDTO();
        			irp.setInstructorInfo(insInfoDTO);
        			irp.setInstructorReports(list2);
        			
        			parent.add(irp);
				}
            } else {
            	InstructorReportPagingDTO pagingDTO = new InstructorReportPagingDTO();
            	pagingDTO.setInstructorInfo(insInfoDTO);
            	pagingDTO.setInstructorReports(list);
            	parent.add(pagingDTO);
            }

    	return parent;
    }
    
    
    
    //Added for GSSP-185
    @Override
    public List<ActivityDTO> findLessonTypes(Long locationId) {
      
        Criterion<Appointment,ActivityDTO> criterion = AppointmentCriterion.findLessonTypes(locationId);
        List<ActivityDTO> search = null;
        try {
            search = appointmentDAO.search(criterion);
        } catch (Exception e) {
         
        }
        
            return search;
        }      

  //added for GSSP-203
    @Override
    public List<InstructorReportPagingDTO> generateStudentCheckInReport(long locationId, Date startDate, Date endDate,  boolean isFromPage) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Query appointment list by locationId {} and time range in {}", locationId, startDate, endDate);
        }

        Criterion<Appointment, ActiveStudentsDTO> criterion = AppointmentCriterion.findStudentsCheckInByDateandTime(locationId, startDate, endDate);
        List<ActiveStudentsDTO> search = null;
        try {
            search = appointmentDAO.search(criterion);
        } catch (Exception e) {
            LOG.error("Caught an exception {} when querying appointment list by locationId {} and startTime {} and endTime {}" +
                    " in ReportService.generateStudentsCheckInReport", e, locationId, startDate, endDate);
        }
        if (search == null) {
            return Collections.emptyList();
        }

        List<InstructorReportPagingDTO> parent = new ArrayList<InstructorReportPagingDTO>();
        
        Map<String, InstructorInfoDTO> cache = new HashMap<String, InstructorInfoDTO>();
        
        List<InstructorScheduleReportDTO> list = new ArrayList<InstructorScheduleReportDTO>();
        
        List<InstructorInfoDTO> insInfoDTO = new ArrayList<InstructorInfoDTO>();
        
        for (ActiveStudentsDTO activeStudentsDTO : search) {
            
            InstructorInfoDTO instructorInfoDTO = cache.get(activeStudentsDTO.getInstructor().getInstructorId() + "_" + activeStudentsDTO.getDate1());
            
            if (instructorInfoDTO == null) {
                instructorInfoDTO = new InstructorInfoDTO();
                cache.put(activeStudentsDTO.getInstructor().getInstructorId() + "_" + activeStudentsDTO.getDate1(), instructorInfoDTO);
                instructorInfoDTO.setInstructorId(activeStudentsDTO.getInstructor().getInstructorId().toString());
                Person instructor_person = activeStudentsDTO.getInstructor().getPerson();
                String insFirstName = instructor_person.getFirstName();
                String insLastName = instructor_person.getLastName();
                instructorInfoDTO.setInstructorName((StringUtils.isEmpty(insFirstName) ? "" : insFirstName) + " " + (StringUtils.isEmpty(insLastName) ? "" : insLastName));
                insInfoDTO.add(instructorInfoDTO);
           } 
            
            	InstructorScheduleReportDTO dto = new InstructorScheduleReportDTO();
            	dto.setActivityType(activeStudentsDTO.getActivityName());
            	dto.setCustomerName(this.buildCustomerName(activeStudentsDTO.getCustomers()));
            	dto.setInstructorId(activeStudentsDTO.getInstructor().getInstructorId());
            	/**
            	 * added the Validation of null value on firstName or lastName
            	 */
            	String ifn = activeStudentsDTO.getInstructor().getPerson().getFirstName();
            	String iln = activeStudentsDTO.getInstructor().getPerson().getLastName();
               	dto.setInstructorName((StringUtils.isEmpty(ifn) ? "" : ifn) + " " + (StringUtils.isEmpty(iln) ? "" : iln));
            	dto.setDuration(this.buildDuration(activeStudentsDTO.getDuration()));
            	dto.setCancelled(activeStudentsDTO.getCanceled().toString());
            	dto.setStartTime(buildTimeStr(activeStudentsDTO.getStartTime()));
            	dto.setEndTime(buildTimeStr(activeStudentsDTO.getEndTime()));
            	dto.setStartDate(activeStudentsDTO.getDate1());
            	dto.setQueryStartDate(buildDateStr(startDate));
            	dto.setQueryEndDate(buildDateStr(endDate));
            	dto.setTimeFrame(dto.getStartTime() + " - " + dto.getEndTime());
            	dto.setRoomName(activeStudentsDTO.getRoomName());
            	dto.setCustomerEmail(activeStudentsDTO.getStudentEmail());
            	list.add(dto);
           }
 
            if(!isFromPage) {
            	List<List<InstructorScheduleReportDTO>> partition = Lists.partition(list, 24);
            	for (List<InstructorScheduleReportDTO> list2 : partition) {
            		InstructorReportPagingDTO irp = new InstructorReportPagingDTO();
        			irp.setInstructorInfo(insInfoDTO);
        			irp.setInstructorReports(list2);
        			
        			parent.add(irp);
				}
            } else {
            	InstructorReportPagingDTO pagingDTO = new InstructorReportPagingDTO();
            	pagingDTO.setInstructorInfo(insInfoDTO);
            	pagingDTO.setInstructorReports(list);
            	parent.add(pagingDTO);
            }

    	return parent;
    }

    //added for GSSP-205
      @Override
      public List<InstructorReportPagingDTO> generateInActiveStudentReport(String locationId,String externalId,boolean isFromPage) {
          if (LOG.isDebugEnabled()) {
              LOG.debug("Query appointment list by externalId {} ", locationId,externalId);
          }

          Criterion<Appointment, InActiveStudentsDTO> criterion = AppointmentCriterion.findInActiveStudentsByDate(locationId,externalId);
          List<InActiveStudentsDTO> search = null;
          try {
              search = appointmentDAO.search(criterion);
          } catch (Exception e) {
              LOG.error("Caught an exception {} when querying appointment list by locationId {} and startTime {} and endTime {}" +
                      " in ReportService.generateInActiveStudentReport", e, locationId,externalId);
          }
          if (search == null) {
              return Collections.emptyList();

          }

          

          
          List<InstructorScheduleReportDTO> list = new ArrayList<InstructorScheduleReportDTO>();
          List<InstructorReportPagingDTO> parent = new ArrayList<InstructorReportPagingDTO>();
          
          
          for (InActiveStudentsDTO InActiveStudentsDTO : search) {
              
              	InstructorScheduleReportDTO dto = new InstructorScheduleReportDTO();

              	dto.setCustomerEmail(InActiveStudentsDTO.getcustomerEmail());
              	
              	dto.setCustomerName(InActiveStudentsDTO.getCustomerFirstName() + " " + InActiveStudentsDTO.getCustomerLastName());
              	
              	dto.setCustomerPhone(InActiveStudentsDTO.getCustomerPhone());
              	list.add(dto);
             }
   
          InstructorReportPagingDTO pagingDTO = new InstructorReportPagingDTO();
          pagingDTO.setInstructorReports(list);
      	parent.add(pagingDTO);


      	return parent;
      }
      //Added for GSSP-210
      @Override
      public List<AppointmentHistoryDTO> generateAppointmentHistoryReport(Date startDate, Date endDate,String externalId) {
          if (LOG.isDebugEnabled()) {
              LOG.debug("Query appointment list by externalId, StartDate and EndDate {} ", externalId,startDate,endDate);
          }
			
          Criterion<Appointment, AppointmentHistoryDTO> criterion = AppointmentCriterion.findAppointmentHistory(startDate, endDate, externalId);
          List<AppointmentHistoryDTO> historySearch = null;
          try {
        	  historySearch = appointmentDAO.search(criterion);
          } catch (Exception e) {
              LOG.error("Caught an exception {} when querying appointment list by locationId {} and startTime {} and endTime {}" +
                      " in ReportService.generateAppointmentHistoryReport", e,externalId, startDate,endDate);
          }
          if (historySearch == null) {
              return Collections.emptyList();

          }
          
         
      	return historySearch;
      }
      
      private static Map<String,Boolean > findDaysofWeek(int startDayOfWeek,int endDayOfWeek)
      {
    	  
    	  Map<String,Boolean> availableDaysofWeek = new HashMap<String,Boolean>();
    	  
    	  int j = 0;
    	  
    	  if(startDayOfWeek > endDayOfWeek)
    	  {
    		  endDayOfWeek  = endDayOfWeek + 7;
    		  
    	  }
    	  
    	  for(int i = startDayOfWeek; i <= endDayOfWeek ; i++)
    	  {
    		  
    		  j = i % 7;
    		  availableDaysofWeek.put(AppConstants.WEEKEND_CHOSE[j], Boolean.TRUE);
    	  }
    		  
    	  
    	  return availableDaysofWeek;
    	  
      }
      @Override
      public List<InstructorReportPagingDTO> generateProfileDetailsReport(Date startDate, Date endDate) {
          if (LOG.isDebugEnabled()) {
              LOG.debug("Query appointment list by StartDate and EndDate {} ", startDate,endDate);
          }

          
          
         
          DateTime startDay = new DateTime(startDate);
		  int startDayOfWeek = startDay.getDayOfWeek();
		  
		  
		  
		  DateTime endDay = new DateTime(endDate);
		  int endDayOfWeek = endDay.getDayOfWeek();
		  
		  Map<String,Boolean> daysofWeek  = findDaysofWeek(startDayOfWeek,endDayOfWeek);
		    
			
			
          Criterion<Appointment, ExportDetailsDTO> criterion = AppointmentCriterion.findProfileByDate(daysofWeek,startDate, endDate);
          List<ExportDetailsDTO> search = null;
          try {
              search = appointmentDAO.search(criterion);
          } catch (Exception e) {
              LOG.error("Caught an exception {} when querying appointment list by locationId {} and startTime {} and endTime {}" +
                      " in ReportService.generateProfileDetailsReport", e, startDate,endDate);
          }
          if (search == null) {
              return Collections.emptyList();

          }
          List<InstructorReportPagingDTO> list = new ArrayList<InstructorReportPagingDTO>();
          InstructorReportPagingDTO dto = null;
          
          
          for (ExportDetailsDTO exportDetailsDTO : search) {
        	  dto = new InstructorReportPagingDTO();
        	  
        	  dto.setActiveAppointments(exportDetailsDTO.getActiveAppointments());
        	  dto.setActiveStudents(exportDetailsDTO.getActiveStudents());
        	  dto.setCanceledAppointmentsCount(exportDetailsDTO.getCanceledAppointmentsCount());
        	  dto.setLocation(exportDetailsDTO.getLocation());
        	  dto.setStoreNumber(exportDetailsDTO.getStoreNumber());
        	  dto.setServiceCount(exportDetailsDTO.getServiceCount());
        	  dto.setRoomCount(exportDetailsDTO.getRoomCount());
        	  dto.setActivityCount(exportDetailsDTO.getActivityCount());
        	  dto.setStudioHours(exportDetailsDTO.getStudioTiming());
        	  dto.setTotalConflictingAppointments(exportDetailsDTO.getTotalConflicts());
        	  dto.setTotalInstructors(exportDetailsDTO.getInstructorCount());
        	  
        	  list.add(dto);
          }
         

      	return list;
      }
      
      //Changes made for GSSP-238
      //Method which invokes the criterion and excel builder logic
      @Override
      public void generateConflictReportBatch() throws Exception {
          if (LOG.isDebugEnabled()) {
              LOG.debug("Query conflicting appointments list by room and instructors for all future appointments including all studios");
          }

          
          
        
          List<InstructorOpenAppointmentsDTO> consolidateRoomsearch = new ArrayList<InstructorOpenAppointmentsDTO>();
          List<InstructorOpenAppointmentsDTO> consolidateInstructorSearch = new ArrayList<InstructorOpenAppointmentsDTO>();
          
          Criterion<Appointment, InstructorOpenAppointmentsDTO> roomcriterion = null;
          Criterion<Appointment, InstructorOpenAppointmentsDTO> instructorCriterion = null;
          
          for(int years = 0; years <= AppConstants.NUMBER_OF_YEARS; years ++ ){
        	  DateTime startDates = new DateTime();
        	  startDates = startDates.plusYears(years);
            //  DateTime endDates  = startDates.plusDays(365);
        	  List<InstructorOpenAppointmentsDTO> search = null;
        	  LOG.error("in Side ReportServiceImpl.generateConflictReportBatch() in for loop years:"+years);
        	  instructorCriterion = AppointmentCriterion.findConflictAppointmentsByInstructorProfileIdAndDateTime(null, startDates.toDate(), startDates.plusYears(1).minusDays(1).toDate());
        	  LOG.error("in Side ReportServiceImpl.generateConflictReportBatch() in for loop after instructorCriterion"+years);
        	  try {
              	search = appointmentDAO.search(instructorCriterion);
              	consolidateInstructorSearch.addAll(search);
              	LOG.info("in Side ReportServiceImpl.generateConflictReportBatch() in for loop after instructorCriterion added to the consolidateInstructorSearch"+years);
              } catch (Exception e) {
                  LOG.error("Caught an exception {} when querying conflicting appointments list by instructor" +
                          " in ReportService.generateConflictReportBatch", e);
              }
        	  LOG.error("in Side ReportServiceImpl.generateConflictReportBatch() in for loop before instructorCriterion"+years);
        	  roomcriterion = AppointmentCriterion.findConflictAppointmentsByRoomProfileIdAndDateTime(null, startDates.toDate(), startDates.plusYears(1).minusDays(1).toDate());
              LOG.error("in Side ReportServiceImpl.generateConflictReportBatch() in for loop after instructorCriterion"+years);
              try {
                  search = appointmentDAO.search(roomcriterion);
                  consolidateRoomsearch.addAll(search);
              	LOG.error("in Side ReportServiceImpl.generateConflictReportBatch() in for loop after consolidateRoomsearch added to the consolidateRoomsearchSearch"+years);
              } catch (Exception e) {
                  LOG.error("Caught an exception {} when querying conflicting appointments list by room " +
                          " in ReportService.generateConflictReportBatch", e);                  
              }
            LOG.error("in Side ReportServiceImpl.generateConflictReportBatch()for loop end: " + startDates + "endDates"  + startDates.plusYears(1).minusDays(1));
          	/*startDates = startDates.plusYears(years);
          	endDates = endDates.plusDays(years);*/
           // LOG.error("in Side ReportServiceImpl.generateConflictReportBatch() in for loop After modify startDate: " + startDates + "endDates"  + startDates.plusYears(1).minusDays(1));
          }
          
          HSSFWorkbook workbook = new HSSFWorkbook();
          Map<String, Object> model = new HashMap<String, Object>();
          model.put("conflictingAppointmentsReportByRoom",consolidateRoomsearch);
          model.put("conflictingAppointmentsReportByInstructor",consolidateInstructorSearch);
          
          conflictAppointmentsInstructorReport(model,workbook);
      	  conflictAppointmentsRoomReport(model,workbook);
      
      	  Map<String,Object> dataMap =  renderDataForConflictingAppointmentMail();
         
          mailSenderService.sendMailWithAttachment(dataMap, EMAIL_TEMPLATE_FOR_CONFLICTING_APPOINTMENT,workbook,
        		  AppConstants.FILE_PATH, AppConstants.ALL_CONFLICT_APPOINTMENT_REPORT_FILE_NAME);
          
      	  return ;
      }
      
      
      //Code changes added for GSSP-238
      private  void conflictAppointmentsRoomReport(
				Map<String, Object> model, HSSFWorkbook workbook) {

			@SuppressWarnings("unchecked")
			List<InstructorOpenAppointmentsDTO> parents = (List<InstructorOpenAppointmentsDTO>) model
					.get("conflictingAppointmentsReportByRoom");

			HSSFRow row;		
			HSSFSheet sheet = workbook
					.createSheet("Confict_Appointment_ByRoom");
			HSSFCellStyle style = workbook.createCellStyle();
			
			
			HSSFFont font = workbook.createFont();
			font.setFontHeightInPoints((short) 10);
			font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
			style.setFont(font);
			
			sheet.setColumnWidth(0, 5500);
			sheet.setColumnWidth(1, 5000);
			sheet.setColumnWidth(2, 3000);
			sheet.setColumnWidth(3, 5000);
			sheet.setColumnWidth(4, 2500);
			sheet.setColumnWidth(5, 5000);
			sheet.setColumnWidth(6, 5000);
			int rownum = 0;
			row = sheet.createRow(rownum++);
			row.createCell(0).setCellValue("Store");
			row.getCell(0).setCellStyle(style);
			row.createCell(1).setCellValue("Room Name");
			row.getCell(1).setCellStyle(style);
			row.createCell(2).setCellValue("Date");			
			row.getCell(2).setCellStyle(style);
			row.createCell(3).setCellValue("Time Frame");
			row.getCell(3).setCellStyle(style);
			row.createCell(4).setCellValue("Duration");
			row.getCell(4).setCellStyle(style);
			row.createCell(5).setCellValue("Activity Type");
			row.getCell(5).setCellStyle(style);
			row.createCell(6).setCellValue("Customer Name");
			row.getCell(6).setCellStyle(style);
			for ( InstructorOpenAppointmentsDTO parent : parents) {
			
					row = sheet.createRow(rownum++);
					String timeFrame = buildTimeStr(parent.getStartTime()) + "-"
							+ buildTimeStr(parent.getEndTime());							
					row.createCell(0).setCellValue(parent.getStoreName());
					row.createCell(1).setCellValue(parent.getRoomName());
					row.createCell(2).setCellValue(buildDateStr(parent.getStartTime()));
					row.createCell(3).setCellValue(timeFrame);
					row.createCell(4).setCellValue(parent.getDuration());
					row.createCell(5).setCellValue(parent.getActivityName());
					row.createCell(6).setCellValue(this.buildCustomerName(parent.getCustomers()));

				}
			}			
			
    
    //Code changes added for GSSP-238
    private  void conflictAppointmentsInstructorReport(
				Map<String, Object> model, HSSFWorkbook workbook) {

			@SuppressWarnings("unchecked")
			List<InstructorOpenAppointmentsDTO> parents = (List<InstructorOpenAppointmentsDTO>) model
					.get("conflictingAppointmentsReportByInstructor");

			HSSFRow row;
		
			HSSFSheet sheet = workbook
					.createSheet("Confict_Appointment_ByInstuctor");
			HSSFCellStyle style = workbook.createCellStyle();
			HSSFFont font = workbook.createFont();
			
			
			
			font.setFontHeightInPoints((short) 10);
			font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
			style.setFont(font);
			
			sheet.setColumnWidth(0, 5500);
			sheet.setColumnWidth(1, 5000);
			sheet.setColumnWidth(2, 3000);
			sheet.setColumnWidth(3, 5000);
			sheet.setColumnWidth(4, 2500);
			sheet.setColumnWidth(5, 5000);
			sheet.setColumnWidth(6, 5000);
			int rownum = 0;
			row = sheet.createRow(rownum++);
			row.createCell(0).setCellValue("Store");
			row.getCell(0).setCellStyle(style);
			row.createCell(1).setCellValue("Instructor Name");
			row.getCell(1).setCellStyle(style);
			row.createCell(2).setCellValue("Date");
			row.getCell(2).setCellStyle(style);
			row.createCell(3).setCellValue("Time Frame");
			row.getCell(3).setCellStyle(style);
			row.createCell(4).setCellValue("Duration");
			row.getCell(4).setCellStyle(style);
			row.createCell(5).setCellValue("Activity Type");
			row.getCell(5).setCellStyle(style);
			row.createCell(6).setCellValue("Customer Name");
			row.getCell(6).setCellStyle(style);
			for ( InstructorOpenAppointmentsDTO parent : parents) {
			
					row = sheet.createRow(rownum++);						        	
					String timeFrame = buildTimeStr(parent.getStartTime()) + "-"
							+ buildTimeStr(parent.getEndTime());							
					row.createCell(0).setCellValue(parent.getStoreName());
					
					String ifn = parent.getInstructor().getPerson().getFirstName();
		        	String iln = parent.getInstructor().getPerson().getLastName();
		        			        	
					row.createCell(1).setCellValue((StringUtils.isEmpty(ifn) ? "" : ifn) + " " + (StringUtils.isEmpty(iln) ? "" : iln));
					row.createCell(2).setCellValue(buildDateStr(parent.getStartTime()));
					row.createCell(3).setCellValue(timeFrame);
					row.createCell(4).setCellValue(parent.getDuration());
					row.createCell(5).setCellValue(parent.getActivityName());
					row.createCell(6).setCellValue(this.buildCustomerName(parent.getCustomers()));

				}
			
			}	
	
    //Changes made for GSSP-238

    private Map<String, Object> renderDataForConflictingAppointmentMail() {
		Map<String, Object> dataMap = new HashMap<String, Object>();
					
		String sub = "Conflicting Appointments Report ";		
					
		dataMap.put(AppConstants.SUBJECT, sub);	
		
		dataMap.put(AppConstants.EMAIL_TYPE_TO, AppConstants.JOB_NOTIFICATION_EMAIL_SEND_LIST);
		
		
		
		return dataMap;
	}

	
      

}
