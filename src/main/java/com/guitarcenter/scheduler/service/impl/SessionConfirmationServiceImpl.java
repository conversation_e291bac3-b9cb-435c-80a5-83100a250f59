package com.guitarcenter.scheduler.service.impl;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.guitarcenter.scheduler.dto.SessionConfirmationResponse;
import com.guitarcenter.scheduler.service.SessionConfirmationService;
 
@Service
public class SessionConfirmationServiceImpl implements SessionConfirmationService {

	@Value("${adp.sessionConfirmationNumberUrl}")
    private String sessionConfirmationNumberUrl;
 
	@Override
    public String getSessionConfirmationNumber(RestTemplate restTemplate) {
    	//TODO:: Move this to properity file
       // String apiUrl = "https://tl1-www.guitarcenter.com/rest/model/atg/rest/SessionConfirmationActor/getSessionConfirmationNumber";
		 //sessionConfirmationNumberUrl = "https://tl1-rst.guitarcenter.com/rest/model/atg/rest/SessionConfirmationActor/getSessionConfirmationNumber";

		/*
		 * HttpHeaders headers = new HttpHeaders(); headers.set("Accept",
		 * MediaType.APPLICATION_JSON_VALUE);
		 */
 
        SessionConfirmationResponse response = restTemplate.getForObject(sessionConfirmationNumberUrl, SessionConfirmationResponse.class);
        return response != null ? response.getSessionConfirmationNumber() : null;
    }

}

