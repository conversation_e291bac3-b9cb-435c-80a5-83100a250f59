package com.guitarcenter.scheduler.service.impl;

import com.guitarcenter.scheduler.common.exceptions.BizRuntimeException;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.JsonUtil;
import com.guitarcenter.scheduler.dao.criterion.dto.AppointmentBookDTO;
import com.guitarcenter.scheduler.dto.BookedAppointmentQueryDTO;
import com.guitarcenter.scheduler.service.AppointmentBookService;
import com.guitarcenter.scheduler.service.AppointmentService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service("appointmentBookService")
public class AppointmentBookServiceImpl implements AppointmentBookService {
    private final static Logger logger = LoggerFactory.getLogger(AppointmentBookServiceImpl.class);

    @Autowired
    private AppointmentService appointmentService;

    @Override
    public List<AppointmentBookDTO> getBookedAppointmentsList(BookedAppointmentQueryDTO queryModel) {
        if(queryModel == null){
            throw new BizRuntimeException("query params is null");
        }
        if(StringUtils.isEmpty(queryModel.getStartDatetime())||StringUtils.isEmpty(queryModel.getEndDatetime())){
            throw new BizRuntimeException("query date range is null");
        }
        Date targetDateTimeStartSec = DateTimeUtil.handleStartDateTime(DateTimeUtil.stringToDate(queryModel.getStartDatetime(),DateTimeUtil.DATE_PATTERN_SLASH));
        Date targetDateTimeEndSec = DateTimeUtil.handleEndDateTime(DateTimeUtil.stringToDate(queryModel.getEndDatetime(),DateTimeUtil.DATE_PATTERN_SLASH));

        return appointmentService.getAppointmentBookByLocationAndDate(queryModel.getLocationId(), targetDateTimeStartSec, targetDateTimeEndSec);
    }

    @Override
    public AppointmentBookDTO reSubmitAppointmentBook(AppointmentBookDTO dto) {
        //add code here...
        logger.info("got dto:{}", JsonUtil.objectToString(dto));



        return dto;
    }
}
