package com.guitarcenter.scheduler.service.impl;



import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.Format;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.naming.Context;
import javax.naming.InitialContext;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.guitarcenter.scheduler.common.exceptions.BizRuntimeException;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorLessonLinkDTO;
import com.guitarcenter.scheduler.service.*;
import com.guitarcenter.scheduler.webservice.dto.LessonScheduledDTO;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.hibernate.LazyInitializationException;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.RestTemplateSingleton;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.CustomerDAO;
import com.guitarcenter.scheduler.dao.InstructorAppointmentStatusDAO;
import com.guitarcenter.scheduler.dao.LocationDAO;
import com.guitarcenter.scheduler.dao.LocationProfileDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.LocationCriterion;
import com.guitarcenter.scheduler.dao.criterion.LocationProfileCriterion;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorDateListDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorScheduleDTO;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dto.CustomerDetailDTO;
import com.guitarcenter.scheduler.dto.InstructorInfoDTO;
import com.guitarcenter.scheduler.dto.InstructorReportPageDatingDTO;
import com.guitarcenter.scheduler.dto.InstructorReportPagingDTO;
import com.guitarcenter.scheduler.dto.InstructorScheduleReportDTO;
import com.guitarcenter.scheduler.dto.InstructorWeeklySuccessDTO;
import com.guitarcenter.scheduler.dto.LessonCompletionDTO;
import com.guitarcenter.scheduler.dto.RegistedCustomerEmailDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.InstructorAppointmentStatus;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.LocationProfile;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.service.thread.EmailThread;
import com.guitarcenter.scheduler.webservice.dto.CustomerAppointmentDetailsResultDTO;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.Font.FontFamily;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.Phrase;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;

@Service("appointmentEmailService")
public class AppointmentEmailServiceImpl implements AppointmentEmailService {

	@Autowired
	private ExternalApiService externalApiService;

	@Autowired
	private MailSenderService mailSenderService;

	@Autowired
	private AppointmentDAO	appointmentDAO;


	@Autowired
	private LocationProfileDAO	locationProfileDAO;

	@Autowired
	private AppointmentService appointmentService;

	@Autowired
	private LocationDAO locationDAO;

	@Autowired
	private CustomerDAO customerDAO;

	@Autowired
	private LocationManagerService locationService;
	//267 Changes - Lakshmi
	private String environment="";

	@Value("${adp.contactcenterLessonCompletedUrl}")
    private String contactcenterLessonCompletedUrl;

	@Value("${adp.contactcenterLessonCompletedAPIKey}")
    private String contactcenterLessonCompletedAPIKey;

	@Value("${adp.dateInNumberic}")
    private String dateInNumberic;

	@Value("${adp.crmApiKey}")
	private String crmApiKey;

	@Value("${adp.crmUrl}")
	private String crmUrl;

	@Value("${adp.cancelledcrmUrl}")
	private String cancelledcrmUrl;

	@Value("${adp.rescheduledcrmUrl}")
	private String rescheduledcrmUrl;

	@Value("${adp.crmContentType}")
	private String crmContentType;

	@Autowired
	private InstructorAppointmentStatusDAO instructorAppointmentStatusDAO;

	private static final Logger LOG = LoggerFactory.getLogger(AppointmentEmailServiceImpl.class);

	public void sendReminderEmailForAppt() {
		List<Appointment> apptList = getEligibleAppts();

		//Changes maded for Reminder Email fix - GSSP-243
		Person updatedPerson;
		for (Appointment appt : apptList) {
			if(appt.getCustomers()!=null){
				Iterator<Customer> customerIter = appt.getCustomers().iterator();
				//Changes maded for Reminder Email fix - GSSP-243
				updatedPerson  = new Person();
				updatedPerson.setFirstName("");
				updatedPerson.setLastName("");
				appt.setUpdatedBy(updatedPerson);

				while(customerIter.hasNext()){
					Customer customer = customerIter.next();
					Map<String, Object> dataMap = renderDataForReminderEmail(appt, customer);
					mailSenderService.sendMail(dataMap, EMAIL_TEMPLATE_FOR_APPT);
				}
			}
		}
	}

	//GSSP-158  - Code for sending instructor scheduler to studio associates
	public List<InstructorWeeklySuccessDTO> sendInstuctorScheduleEmail(String timeZoneID) {

		DateTime curentDateTime = new DateTime().plusDays(1);
		//GSSP-255 Code Changes for 3 days
		DateTime endTime = new DateTime().plusDays(15);
		DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(DateTimeUtil.DATE_PATTERN_SLASH);
		DateTimeFormatter dateTimeFormatter2 = DateTimeFormat.forPattern(DateTimeUtil.DATE_PATTERN_HIPHEN);

		String  currentDay = dateTimeFormatter.print(curentDateTime);
		String previousDay = dateTimeFormatter2.print(curentDateTime);
		//GSSP-255 Code Changes for 3 days
		String  endday = dateTimeFormatter.print(endTime);
		Map<Long,List<String>> instructorLocationMap = null;

		Map<String, List<String>> instructorEmailListDatamap = new HashMap<String, List<String>>() ;
		Criterion<Appointment, Map<Long,List<String>>> empCriterion = AppointmentCriterion.findByZoneID(timeZoneID);
		List<Map<Long,List<String>>> employeeLocationList = appointmentDAO.search(empCriterion);

		//Changes made for GSSP-267
		List<String> instructorMailingList ;
		List<InstructorWeeklySuccessDTO> instructorList = new ArrayList<InstructorWeeklySuccessDTO>();
		//Changes made for GSSP-255
				Criterion<Appointment, Map<Long, List<String>>> emailCriterion = AppointmentCriterion.findByInstructorPersonalEmail(timeZoneID);
				List<Map<Long, List<String>>> instructorEmailList = appointmentDAO.search(emailCriterion);

				if(null != employeeLocationList && employeeLocationList.size() > 0)
					{
						Map<Long,List<String>> employeeLocationMap = employeeLocationList.get(0);

						//Changes made for GSSP-255
						if(null != instructorEmailList && instructorEmailList.size() > 0)
						{
							instructorLocationMap = instructorEmailList.get(0);
						}
					Date queryStartDate = DateTimeUtil.handleStartDate(currentDay);
					Date queryEndDate = DateTimeUtil.handleEndDate(endday);

					for(Long locationId : employeeLocationMap.keySet())
					{
						//Fix for 267 Email Bug
						instructorMailingList =  new ArrayList<String>();


						Criterion<Appointment, InstructorDateListDTO> criterion = AppointmentCriterion.
								findInstructorScheduleByLocationIdAndDateTime(locationId,queryStartDate, queryEndDate);
					    List<InstructorDateListDTO> search = null;
				        try {
				            search = appointmentDAO.search(criterion);
				        } catch (Exception e) {
				            LOG.error("Caught an exception {} when querying appointment list by locationId {} and startTime {} and endTime {}" +
				                    " in Appointmentemailservice sendInstuctorScheduleEmail method", e, locationId, queryStartDate, queryEndDate);
				        }


				        //Changes made for GSSP-255


				        List<InstructorReportPagingDTO> parent =  new ArrayList<InstructorReportPagingDTO>();


				        List<InstructorReportPageDatingDTO> instructorReportPageDatingDTOList = new ArrayList<InstructorReportPageDatingDTO>();

				        InstructorReportPageDatingDTO instructorReportPageDatingDTO =null;

				        //End of changes made for GSSP-255

				        Map<String, InstructorInfoDTO> cache = new HashMap<String, InstructorInfoDTO>();

				        for (InstructorDateListDTO instructorDateListDTO : search) {

				            InstructorInfoDTO instructorInfoDTO = cache.get(instructorDateListDTO.getInstructor().getInstructorId());


				            //Changes made for GSSP-255
				            if(! previousDay.equalsIgnoreCase(instructorDateListDTO.getDate1())){


				            	if(null != parent && parent.size() > 0){
			            	instructorReportPageDatingDTO = new InstructorReportPageDatingDTO(previousDay,parent);
			            	instructorReportPageDatingDTOList.add(instructorReportPageDatingDTO);
			            	}
				            	parent = new ArrayList<InstructorReportPagingDTO>();
				            	previousDay = instructorDateListDTO.getDate1();
				            }
				          //End of changes made for GSSP-255

				            if (instructorInfoDTO == null){
				                instructorInfoDTO = new InstructorInfoDTO();
				                cache.put(instructorDateListDTO.getInstructor().getInstructorId().toString() , instructorInfoDTO);
				                instructorInfoDTO.setInstructorId(instructorDateListDTO.getInstructor().getInstructorId().toString());

				                Person instructor_person = instructorDateListDTO.getInstructor().getPerson();
				                String insFirstName = instructor_person.getFirstName();
				                String insLastName = instructor_person.getLastName();
				                instructorInfoDTO.setInstructorName((StringUtils.isEmpty(insFirstName) ? "" : insFirstName) + " " + (StringUtils.isEmpty(insLastName) ? "" : insLastName));

				                Date startHours_date = instructorDateListDTO.getStartHours();
				                Date endHours_date = instructorDateListDTO.getEndHours();
				                String startHours = null;
				                String endHours = null;
				                if (startHours_date != null) {
				                    startHours = buildTimeStr(startHours_date);
				                }
				                if (endHours_date != null) {
				                    endHours = buildTimeStr(endHours_date);
				                }
				                instructorInfoDTO.setInstructorAvailibilityTime(startHours + " - " + endHours);
				                instructorInfoDTO.setInstructorNameWithAvaiTime(instructorInfoDTO.getInstructorName() + " - " +
				                			instructorInfoDTO.getInstructorAvailibilityTime());
				            }

				            List<InstructorScheduleReportDTO> list = new ArrayList<InstructorScheduleReportDTO>();

				            for (int i=0; i<instructorDateListDTO.getList().size(); i++) {
				            	InstructorScheduleDTO instructorScheduleDTO = instructorDateListDTO.getList().get(i);
				            	InstructorScheduleReportDTO dto = new InstructorScheduleReportDTO();
				            	dto.setActivityType(instructorScheduleDTO.getActivityName());
				            	dto.setCustomerName(this.buildCustomerName(instructorScheduleDTO.getCustomers()));
				            	dto.setInstructorId(instructorScheduleDTO.getInstructor().getInstructorId());
				            	String ifn = instructorScheduleDTO.getInstructor().getPerson().getFirstName();
				            	String iln = instructorScheduleDTO.getInstructor().getPerson().getLastName();
				            	dto.setInstructorName((StringUtils.isEmpty(ifn) ? "" : ifn) + " " + (StringUtils.isEmpty(iln) ? "" : iln));
				            	dto.setDuration(this.buildDuration(instructorScheduleDTO.getDuration()));
				            	dto.setCancelled(instructorScheduleDTO.getCanceled().toString());
				            	dto.setStartTime(buildTimeStr(instructorScheduleDTO.getStartTime()));
				            	dto.setEndTime(buildTimeStr(instructorScheduleDTO.getEndTime()));
				            	dto.setTimeFrame(dto.getStartTime() + " - " + dto.getEndTime());
				            	dto.setRoomName(instructorScheduleDTO.getRoomName());
				            	list.add(dto);
				            }

				            	InstructorReportPagingDTO pagingDTO = new InstructorReportPagingDTO();
				            	pagingDTO.setInstructorInfoDTO(instructorInfoDTO);
				            	pagingDTO.setInstructorReports(list);
				            	parent.add(pagingDTO);


				            }

				        //System.out.println("parent  .. "+parent);
				        	instructorReportPageDatingDTO = new InstructorReportPageDatingDTO(previousDay,parent);

		            	   	instructorReportPageDatingDTOList.add(instructorReportPageDatingDTO);
		            	   	//GSSP-255 sorting the date
		            	   	instructorReportPageDatingDTOList.sort((InstructorReportPageDatingDTO iRPDObj1, InstructorReportPageDatingDTO iRPDObj2)->{
								try {
									DateFormat DateFormat = new SimpleDateFormat("dd-MMM-yyyy");
									 Date date_previousDay1 = DateFormat.parse(iRPDObj1.getCurrentDate());
									 Date date_previousDay2 = DateFormat.parse(iRPDObj2.getCurrentDate());
									return date_previousDay1.compareTo(date_previousDay2);
								} catch (Exception e) {
									LOG.error("Caught an exception {} in AppointmentEmailServiceImpl.sendInstuctorScheduleEmail", e);
								}
								return 0;
							});



					     //changes made for GSSP-244, To add location Name into the subject.
				        	Location location =locationService.findById(new Long(locationId));
				        	String locationName= "";
							String externalId="";

				        	if(null != location && null != location.getLocationName() )
				        	{
				        		locationName=location.getLocationName();

				        	}
							if(null !=location&& null!= location.getExternalId())
							{
									externalId=location.getExternalId();
							}

				        	if(null != search && search.size() > 0)
				        	{
				        		//GSSP -265-PDF and ExcelAttachment Method.

				        		Document document=new Document();
				        		 ByteArrayOutputStream out = new ByteArrayOutputStream();
				        		 Map<String, Object> modelpdf = new HashMap<String, Object>();
				        		 modelpdf.put("InstructorWeeklyReport",instructorReportPageDatingDTOList);
				        		 InstructorWeeklyReportPDF(modelpdf, document, out, locationName, externalId, location);
				              	HSSFWorkbook workbook = new HSSFWorkbook();
			        	          Map<String, Object> model = new HashMap<String, Object>();
			        	          model.put("InstructorWeeklyReport",instructorReportPageDatingDTOList);
			        	          InstructorWeeklyReport(model,workbook, locationName, externalId);

			        	          instructorMailingList = instructorLocationMap.get(locationId);

				        		Map<String, Object> dataMap = renderDataForInstructorScheduleEmail(instructorReportPageDatingDTOList, employeeLocationMap.get(locationId),instructorMailingList,locationName,externalId);

				        	    mailSenderService.sendMailWithpdfAttachment(dataMap, EMAIL_TEMPLATE_FOR_INSTRUCTOR_SCCHEDULE,workbook,new ByteArrayInputStream(out.toByteArray()), document,
				        	    		new ByteArrayInputStream(out.toByteArray()));
				   			        	}

				        	//GSSp-267 Job notification
				        	 String locationNameList= externalId+"-"+locationName;


				     		if(null !=  instructorMailingList && instructorMailingList.size()>0)
				     		{



				     			instructorList.add(new InstructorWeeklySuccessDTO(locationNameList,instructorMailingList));
				     		}


				        }
				}
					return instructorList;


		}
		 public PdfPCell createCell(String content, float borderWidth, int colspan, int alignment, Font bold ) {
	        PdfPCell cell = new PdfPCell(new Phrase(content,bold));


	        cell.setBorderWidth(borderWidth);
	        cell.setColspan(colspan);
	        cell.setHorizontalAlignment(alignment);
	        return cell;
	    }


		//GSSP-265 Code Changes for generating excel sheet.
		 private  void InstructorWeeklyReportPDF(
					Map<String, Object> modelpdf,Document document, ByteArrayOutputStream out,String locationName, String externalId, Location location) {

		        @SuppressWarnings("unchecked")
				List<InstructorReportPageDatingDTO> instructorReportPageDatingDTOList = (List<InstructorReportPageDatingDTO>)modelpdf
						.get("InstructorWeeklyReport");;
						DateTime curentDateTime = new DateTime().plusDays(1);
						//GSSP-265 Code Changes for 3 days
						DateTime endTime = new DateTime().plusDays(15);
						DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(DateTimeUtil.DATE_PATTERN_SLASH);
						String  endday = dateTimeFormatter.print(endTime);
						String  currentday = dateTimeFormatter.print(curentDateTime);
			 try {

	        	 PdfWriter.getInstance(document,out);
	        	 InstructorReportPageDatingDTO  dayDetails = null;
	        	 InstructorReportPagingDTO  instructorDetails = null;
	        	 InstructorScheduleReportDTO appointmentDetails = null;
	        	 document.open();
	        	 Font regular = new Font(FontFamily.HELVETICA, 12);
	        	 Font bold  = new Font(FontFamily.HELVETICA, 12, Font.BOLD);
	    	     document.add(new Paragraph("Instructor Schedule Report",bold));
	    	   document.add(new Paragraph("From " +currentday + " To " +endday, bold));
	    	   document.add(new Phrase(" "));
	    	   for (int i=0;i < instructorReportPageDatingDTOList.size();i++) {
	    		 document.add(new Paragraph( "The instructor(s) schedule at store "+location.getExternalId()+"-"+location.getLocationName()+ " on "+instructorReportPageDatingDTOList.get(i).getCurrentDate(),bold));
	    		   dayDetails  = instructorReportPageDatingDTOList.get(i);
	    		   for (int k=0;k < dayDetails.getInstructorReportPagingDTO().size();k++){

	                	 PdfPTable table = new PdfPTable(7);
		                 table.setWidths(new int[]{1,1,1,1,1,1,2});
		                 table.setWidthPercentage(100);
		                 table.addCell(createCell("Instructor Name ", 1,1, Element.ALIGN_LEFT,bold));
		                 table.addCell(createCell("Timeframe", 1, 1, Element.ALIGN_LEFT,bold));
		                 table.addCell(createCell("Duration", 1, 1, Element.ALIGN_LEFT,bold));
		                 table.addCell(createCell("Activity Type", 1, 1, Element.ALIGN_LEFT,bold));
		                 table.addCell(createCell("Customer Name", 1, 1, Element.ALIGN_LEFT,bold));
		                 table.addCell(createCell("Room Name", 1, 1, Element.ALIGN_LEFT,bold));
		                 table.addCell(createCell("show/No show/Cancel", 1,2, Element.ALIGN_LEFT,bold));
	                	 instructorDetails  =  dayDetails.getInstructorReportPagingDTO().get(k);
	                	 document.add(new Phrase(" "));
	                	 document.add(new Paragraph(instructorDetails.getInstructorInfoDTO().getInstructorNameWithAvaiTime()));
	                	 for (int j=0;j < instructorDetails.getInstructorReports().size();j++){
	                		 document.add(new Phrase(" "));
	                		 appointmentDetails  = instructorDetails.getInstructorReports().get(j);
	                	 table.addCell(createCell((instructorDetails.getInstructorInfoDTO().getInstructorName()), 1,1, Element.ALIGN_LEFT,regular));
	                     table.addCell(createCell((appointmentDetails.getTimeFrame()), 1, 1, Element.ALIGN_LEFT,regular));
	                     table.addCell(createCell(( appointmentDetails.getDuration()), 1, 1, Element.ALIGN_LEFT,regular));
	                     table.addCell(createCell(( appointmentDetails.getActivityType()), 1, 1, Element.ALIGN_LEFT,regular));
	                     table.addCell(createCell(( appointmentDetails.getCustomerName()), 1, 1, Element.ALIGN_LEFT,regular));
	                     table.addCell(createCell(( appointmentDetails.getRoomName()), 1, 1, Element.ALIGN_LEFT,regular));
	                     table.addCell(createCell(( appointmentDetails.getSignitureBlock()), 1, 2, Element.ALIGN_LEFT,regular));
	            	 }

	                	 document.add(table);


	                 }

	                 }

	             document.close();

	         } catch (DocumentException e) {
	        	 LOG.error("Caught an exception {} in AppointmentEmailServiceImpl.createCell", e);
	         }
		 }

	     @SuppressWarnings("null")
		private  void InstructorWeeklyReport(
					Map<String, Object> model, HSSFWorkbook workbook,String locationName, String externalId) {


		        @SuppressWarnings("unchecked")
				List<InstructorReportPageDatingDTO> instructorReportPageDatingDTOList = (List<InstructorReportPageDatingDTO>)model
						.get("InstructorWeeklyReport");;
						DateTime curentDateTime = new DateTime().plusDays(1);
						//GSSP-265 Code Changes for 3 days
						DateTime endTime = new DateTime().plusDays(15);
						DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(DateTimeUtil.DATE_PATTERN_SLASH);
						String  endday = dateTimeFormatter.print(endTime);
						String  currentday = dateTimeFormatter.print(curentDateTime);
				HSSFRow row;
				HSSFSheet sheet = workbook
						.createSheet("InstcuctorScheduleReport");

				HSSFCellStyle cellStyle = workbook.createCellStyle();
				cellStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
				cellStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
				cellStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
				cellStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
				HSSFCellStyle style = workbook.createCellStyle();
		    	HSSFFont font = workbook.createFont();
				font.setFontHeightInPoints((short) 10);
				font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
				style.setFont(font);
				HSSFCellStyle Headerstyle = workbook.createCellStyle();
				Headerstyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
				Headerstyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
				Headerstyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
				Headerstyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
				font.setFontHeightInPoints((short) 10);
				font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
				Headerstyle.setFont(font);


				sheet.setColumnWidth(0, 5500);
				sheet.setColumnWidth(1, 5000);
				sheet.setColumnWidth(2, 3000);
				sheet.setColumnWidth(3, 5000);
				sheet.setColumnWidth(4, 2500);
				sheet.setColumnWidth(5, 5000);
				sheet.setColumnWidth(6, 5000);
				sheet.setColumnWidth(7, 5000);
				int rownum1 = 0;
				row = sheet.createRow(rownum1);
				row.createCell(0).setCellValue(new HSSFRichTextString("Instructor Schedule Report"));
				row.getCell(0).setCellStyle(style);
				 sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));

				int rownum2 = 1;
				row = sheet.createRow(rownum2);
				row.createCell(0).setCellValue(new HSSFRichTextString("From:"+currentday+" To:"+endday ));
				row.getCell(0).setCellStyle(style);
				 sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 8));
				 int rownum3 = 2;
					row = sheet.createRow(rownum3);
					row.createCell(0).setCellValue(new HSSFRichTextString("Below is the instructor(s) schedule at store" +externalId+" - "+locationName+" from "+currentday+" to "+endday));
					row.getCell(0).setCellStyle(style);
					 sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 8));

	       	int rownum = 4;
	       	row = sheet.createRow(rownum++);
	       	row.createCell(0).setCellValue("Date");
			row.getCell(0).setCellStyle(Headerstyle);
			row.createCell(1).setCellValue("Instructor Name");
			row.getCell(1).setCellStyle(Headerstyle);
			row.createCell(2).setCellValue("Time Frame");
			row.getCell(2).setCellStyle(Headerstyle);
			row.createCell(3).setCellValue("Duration");
			row.getCell(3).setCellStyle(Headerstyle);
			row.createCell(4).setCellValue("Activity Name");
			row.getCell(4).setCellStyle(Headerstyle);
			row.createCell(5).setCellValue("Customer Name");
			row.getCell(5).setCellStyle(Headerstyle);
			row.createCell(6).setCellValue("Room Name");
			row.getCell(6).setCellStyle(Headerstyle);
			row.createCell(7).setCellValue("show/no show/cancel");
			row.getCell(7).setCellStyle(Headerstyle);
			InstructorReportPageDatingDTO  dayDetails = null;
	      	 InstructorReportPagingDTO  instructorDetails = null;
	      	 InstructorScheduleReportDTO appointmentDetails = null;
	      	 ////Rajgan
				for (int i=0;i < instructorReportPageDatingDTOList.size();i++) {
					 dayDetails  = instructorReportPageDatingDTOList.get(i);

					 for (int k=0;k < dayDetails.getInstructorReportPagingDTO().size();k++){
						 instructorDetails  =  dayDetails.getInstructorReportPagingDTO().get(k);


				 for (int j=0;j < instructorDetails.getInstructorReports().size();j++){
					 appointmentDetails  = instructorDetails.getInstructorReports().get(j);

						row = sheet.createRow(rownum++);
						row.createCell(0).setCellValue(dayDetails.getCurrentDate());
						row.getCell(0).setCellStyle(cellStyle);
						row.createCell(1).setCellValue(instructorDetails.getInstructorInfoDTO().getInstructorName());
						row.getCell(1).setCellStyle(cellStyle);
						row.createCell(2).setCellValue(appointmentDetails.getTimeFrame());
						row.getCell(2).setCellStyle(cellStyle);
						row.createCell(3).setCellValue(appointmentDetails.getDuration());
						row.getCell(3).setCellStyle(cellStyle);
						row.createCell(4).setCellValue(appointmentDetails.getActivityType());
						row.getCell(4).setCellStyle(cellStyle);
						row.createCell(5).setCellValue(appointmentDetails.getCustomerName());
						row.getCell(5).setCellStyle(cellStyle);
						row.createCell(6).setCellValue(appointmentDetails.getRoomName());
						row.getCell(6).setCellStyle(cellStyle);
						row.createCell(7).setCellValue(appointmentDetails.getSignitureBlock());
						row.getCell(7).setCellStyle(cellStyle);

					}
					 }
				}
				}

	   //Code changes Ended for GSSP-265



		private Map<String, Object> renderDataForReminderEmail(Appointment appt, Customer customer) {
			Map<String, Object> dataMap = renderBasicData(appt, customer, appt.getDuration());
			dataMap.put(AppConstants.SUBJECT, "Guitar Center Studios rehearsal reminder");
			dataMap.put(SERVICE_TYPE, "reminder");
			return dataMap;
		}

	//separate method for Instructor view on demand emails
		private Map<String, Object> renderDataInstForReminderEmail(Appointment appt, Customer customer) {
			Map<String, Object> dataMap = renderInstBasicData(appt, customer, appt.getDuration());
			dataMap.put(AppConstants.SUBJECT, "Guitar Center Studios - Lesson Reminder");
			dataMap.put(SERVICE_TYPE, "reminder");
			return dataMap;
		}

		private Map<String, Object> renderDataInstForReminderEmailForNotes(Appointment appt, Customer customer) {
			Map<String, Object> dataMap = renderInstBasicData(appt, customer, appt.getDuration());
			dataMap.put(AppConstants.SUBJECT, "Guitar Center Studios - Notes From Instructor");
			dataMap.put(SERVICE_TYPE, "reminder");
			return dataMap;
		}

		private  String getEnvironment()
		{

			List<String> environemntList = null;
			Criterion<Appointment, String> criterion = AppointmentCriterion.findEnvironment();
			environemntList =  appointmentDAO.search(criterion);

			if(null != environemntList && environemntList.size() > 0)
				return environemntList.get(0);

			else
				return "";
		}

		//For GSSP-158
		//for Gssp 255 adding Instructorpersonal mail
		private Map<String, Object> renderDataForInstructorScheduleEmail(List<InstructorReportPageDatingDTO> list, List<String> mailingList,List<String> instructorEmailList, String locationName, String externalId) {
			Map<String, Object> dataMap = new HashMap<String, Object>();

			String sub = "Instructor(s) Schedule at store ";

			DateTime curentDateTime = new DateTime().plusDays(1);
			DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(DateTimeUtil.DATE_PATTERN_SLASH);
			String  today = dateTimeFormatter.print(curentDateTime);
			//changes made for GSSP-244
			// System.out.println("curentDateTime         "+today);
			 DateTime endTime = new DateTime().plusDays(15);

				String  endday = dateTimeFormatter.print(endTime);

			sub=sub+externalId+"-"+locationName;

			dataMap.put("date", today);
			dataMap.put(AppConstants.SUBJECT, sub);
			dataMap.put("locationName", locationName);
			dataMap.put("externalId", externalId);
			dataMap.put("parent",list);
			dataMap.put("period",today+" to "+endday);

			if(null !=  mailingList && mailingList.size()>0)
			{
				String[] mailArray = mailingList.toArray(new String[mailingList.size()]);
				//String[] mailArray   = {"<EMAIL>"} ;


				dataMap.put(AppConstants.EMAIL_TYPE_TO, mailArray);
			}


			//267 Changes - Lakshmi
			if(environment.equals("")) environment = getEnvironment();

			//Changes made for GSSP-255
					if(null !=  instructorEmailList && instructorEmailList.size()>0)
					{
						String[] instructorMailArray = instructorEmailList.toArray(new String[instructorEmailList.size()]);
						//String[] instructorMailArray   = {"<EMAIL>"} ;
					dataMap.put(AppConstants.EMAIL_TYPE_BCC, instructorMailArray);
					}


			return dataMap;
		}

	private List<Appointment> getEligibleAppts() {
		Criterion<Appointment, Appointment> apptCriterion = AppointmentCriterion.findApptsForMailReminder();
		List<Appointment> apptList = appointmentDAO.search(apptCriterion);
		return apptList;
	}


	private Location getLocation(Appointment appt) {
		Criterion<Location, Location> locationCriterion = LocationCriterion.getByProfileId(appt.getLocationProfile().getProfileId());
		return locationDAO.get(locationCriterion);
	}

	private LocationProfile getLocationProfile(Appointment appt) {
		Criterion<LocationProfile, LocationProfile> locationProfCriterion = LocationProfileCriterion.getByLocationProfileId(appt.getLocationProfile().getProfileId());
		return locationProfileDAO.get(locationProfCriterion);
	}

	private LocationProfile getLocationProfilewithProfileId(long ProfileID) {
		Criterion<LocationProfile, LocationProfile> locationProfCriterion = LocationProfileCriterion.getByLocationProfileId(ProfileID);
		return locationProfileDAO.get(locationProfCriterion);
	}

	//For GCSS-520,Change the duration parameter type to float to support half hour in appointment
	private Map<String, Object> renderBasicData(Appointment appointment, Customer customer, float duration) {
		SimpleDateFormat format = new SimpleDateFormat("EEEE, MMM d, yyyy-hh:mm aaa");
		Map<String, Object> dataMap = new HashMap<String, Object>();
		if(customer.getPerson()==null){
			customer = customerDAO.get(customer.getCustomerId(), DAOHelper.FETCH_PERSON);
		}
		Location location = getLocation(appointment);

		LocationProfile locationProfileObj = getLocationProfilewithProfileId(appointment.getLocationProfile().getProfileId());

		String tz = null;

		if(null != locationProfileObj && null!= locationProfileObj.getTimeZone()) {
			tz = locationProfileObj.getTimeZone();
		}

		//For GCSS-499,handle the customerName to avoid null value in firstName or lastName
		String customerFirstName = customer.getPerson().getFirstName();
		String customerLastName = customer.getPerson().getLastName();
		String customerName = (StringUtils.isBlank(customerFirstName) ? "" : (customerFirstName.trim() + " ") + (StringUtils.isBlank(customerLastName) ? "" : customerLastName));

		dataMap.put("customerName", customerName);
		dataMap.put("location", location.getLocationName());
		dataMap.put("date", format.format(appointment.getStartTime())+ " " + tz );
		try{
		String updateByFirstName = appointment.getUpdatedBy().getFirstName();
		String updateByLastName = appointment.getUpdatedBy().getLastName();
		String updatedBy = (StringUtils.isBlank(updateByFirstName) ? "" : (updateByFirstName.trim() + " ") + (StringUtils.isBlank(updateByLastName) ? "" : updateByLastName));
		dataMap.put("updatedBy", updatedBy);
		}catch (LazyInitializationException expected) {
		    LOG.error(
		            "could not initialize proxy - no Session",
		            expected
		        );
		}




		//dataMap.put("activityName", appointment.getActivity().getActivityName());
		//For GCSS- 278,more detail on confirmation email
		String durationStr = "";
		if(duration >= 60) {
			if(60 == duration) {
				durationStr = duration / 60 + " hr";
			} else {
				durationStr = duration / 60 + " hrs";
			}
		} else {
			durationStr = duration + " Min";
		}
		durationStr = durationStr.replace(".0", "");

		dataMap.put("duration", durationStr);
		dataMap.put("phone", location.getPhone());
		if(null != customer.getCustomerId()) {
			CustomerDetailDTO custDTO = customerDAO.getCustomerDetailsById(customer.getCustomerId());

		//GSSP-212_Prod Exception Changes :: email null check
		// Sending emails to secondary email IDs

			//OLL-3811
			if (String.valueOf(appointment.getActivity().getActivityId()).equals("140") ||
					String.valueOf(appointment.getActivity().getActivityId()).equals("320")) {
				customer.getPerson().setEmail("<EMAIL>");
				custDTO.setSecondaryEmail(null);
			}

		if(custDTO.getSecondaryEmail()== null || "".equals(custDTO.getSecondaryEmail())) {
			if(null != customer.getPerson().getEmail())
			{
				dataMap.put(AppConstants.EMAIL_TYPE_TO, new String[]{customer.getPerson().getEmail()});

			}
		}else if(null != customer.getPerson().getEmail())
			{
				dataMap.put(AppConstants.EMAIL_TYPE_TO, new String[]{customer.getPerson().getEmail(),custDTO.getSecondaryEmail()});

			}
		else if(customer.getPerson().getEmail() == null || "".equals(customer.getPerson().getEmail())){

			dataMap.put(AppConstants.EMAIL_TYPE_TO, new String[]{custDTO.getSecondaryEmail()});
		}
		}

		return dataMap;
	}

	private Map<String, Object> renderBasicDataWithoutTz(Appointment appointment, Customer customer, float duration) {
		SimpleDateFormat format = new SimpleDateFormat("EEEE, MMM d, yyyy-hh:mm aaa");
		Map<String, Object> dataMap = new HashMap<String, Object>();
		if(customer.getPerson()==null){
			customer = customerDAO.get(customer.getCustomerId(), DAOHelper.FETCH_PERSON);
		}
		Location location = getLocation(appointment);





		//For GCSS-499,handle the customerName to avoid null value in firstName or lastName
		String customerFirstName = customer.getPerson().getFirstName();
		String customerLastName = customer.getPerson().getLastName();
		String customerName = (StringUtils.isBlank(customerFirstName) ? "" : (customerFirstName.trim() + " ") + (StringUtils.isBlank(customerLastName) ? "" : customerLastName));

		dataMap.put("customerName", customerName);
		dataMap.put("location", location.getLocationName());
		dataMap.put("date", format.format(appointment.getStartTime()));
		try{
		String updateByFirstName = appointment.getUpdatedBy().getFirstName();
		String updateByLastName = appointment.getUpdatedBy().getLastName();
		String updatedBy = (StringUtils.isBlank(updateByFirstName) ? "" : (updateByFirstName.trim() + " ") + (StringUtils.isBlank(updateByLastName) ? "" : updateByLastName));
		dataMap.put("updatedBy", updatedBy);
		}catch (LazyInitializationException expected) {
		    LOG.error(
		            "could not initialize proxy - no Session",
		            expected
		        );
		}




		//dataMap.put("activityName", appointment.getActivity().getActivityName());
		//For GCSS- 278,more detail on confirmation email
		String durationStr = "";
		if(duration >= 60) {
			if(60 == duration) {
				durationStr = duration / 60 + " hr";
			} else {
				durationStr = duration / 60 + " hrs";
			}
		} else {
			durationStr = duration + " Min";
		}
		durationStr = durationStr.replace(".0", "");

		dataMap.put("duration", durationStr);
		dataMap.put("phone", location.getPhone());

		if(null != customer.getCustomerId()) {
			CustomerDetailDTO custDTO = customerDAO.getCustomerDetailsById(customer.getCustomerId());


		//GSSP-212_Prod Exception Changes :: email null check
		// Sending emails to secondary email IDs

		if(custDTO.getSecondaryEmail()== null || "".equals(custDTO.getSecondaryEmail())) {
			if(null != customer.getPerson().getEmail())
			{
				dataMap.put(AppConstants.EMAIL_TYPE_TO, new String[]{customer.getPerson().getEmail()});

			}
		}else if(null != customer.getPerson().getEmail())
			{
				dataMap.put(AppConstants.EMAIL_TYPE_TO, new String[]{customer.getPerson().getEmail(),custDTO.getSecondaryEmail()});

			}

		}
		return dataMap;
	}

	//separate method for Instructor view on demand emails
	private Map<String, Object> renderInstBasicData(Appointment appointment, Customer customer, float duration) {
		SimpleDateFormat format = new SimpleDateFormat("EEEE, MMM d, yyyy-hh:mm aaa");
		Map<String, Object> dataMap = new HashMap<String, Object>();
		if(customer.getPerson()==null){
			customer = customerDAO.get(customer.getCustomerId(), DAOHelper.FETCH_PERSON);
		}
		Location location = getLocation(appointment);

		//For GCSS-499,handle the customerName to avoid null value in firstName or lastName
		String customerFirstName = customer.getPerson().getFirstName();
		String customerLastName = customer.getPerson().getLastName();
		String customerName = (StringUtils.isBlank(customerFirstName) ? "" : (customerFirstName.trim() + " ") + (StringUtils.isBlank(customerLastName) ? "" : customerLastName));

		dataMap.put("customerName", customerName);
		dataMap.put("location", location.getLocationName());
		dataMap.put("date", format.format(appointment.getStartTime()));
		//dataMap.put("activityName", appointment.getActivity().getActivityName());
		//For GCSS- 278,more detail on confirmation email
		String durationStr = "";
		if(duration >= 60) {
			if(60 == duration) {
				durationStr = duration / 60 + " hr";
			} else {
				durationStr = duration / 60 + " hrs";
			}
		} else {
			durationStr = duration + " Min";
		}
		durationStr = durationStr.replace(".0", "");

		dataMap.put("duration", durationStr);
		dataMap.put("phone", location.getPhone());

		if(null != customer.getCustomerId()) {
			CustomerDetailDTO custDTO = customerDAO.getCustomerDetailsById(customer.getCustomerId());


		//GSSP-212_Prod Exception Changes :: email null check
		// Sending emails to secondary email IDs

		if(custDTO.getSecondaryEmail()== null || "".equals(custDTO.getSecondaryEmail())) {
			if(null != customer.getPerson().getEmail())
			{
				dataMap.put(AppConstants.EMAIL_TYPE_TO, new String[]{customer.getPerson().getEmail()});

			}
		}else if(null != customer.getPerson().getEmail())
			{
				dataMap.put(AppConstants.EMAIL_TYPE_TO, new String[]{customer.getPerson().getEmail(),custDTO.getSecondaryEmail()});

			}
		else if(customer.getPerson().getEmail() == null || "".equals(customer.getPerson().getEmail())){

			dataMap.put(AppConstants.EMAIL_TYPE_TO, new String[]{custDTO.getSecondaryEmail()});
		}

		}


		return dataMap;
	}









	/************** Update Lesson Service-Phase-2***********/
	//JIRA # “LES-7”

	@Override
	public void sendEmailForModifySingleAppt(String date,String startTime,String endTime,List<Map<String,Object>> emailBodies, Long serviceId,String cancelType,String instructorName,String serviceType,String curCustName,long ProfileID,String activityName,String roomName){

		String duration;
		DateTime d1 = null;
		DateTimeFormatter format1  = DateTimeFormat.forPattern("yyyy-MM-dd");
		DateTimeFormatter format2  = DateTimeFormat.forPattern("EEEE, MMM d, yyyy");
		//GSSP 253 data mapping changes
		Criterion<Location, Location> locationCriterion = LocationCriterion.getByProfileId(ProfileID);

    	Location location = locationDAO.get(locationCriterion);


    	String locationName = location.getLocationName();

    	String Phone = location.getPhone();
		d1= format1.parseDateTime(date);
		//GSSP-243 Changes
		String[] emailList = getEmployeeEmailIdsbyProfileID(ProfileID);
		DateTimeFormatter formatter = DateTimeFormat.forPattern(DateTimeUtil.TIME_OFF_DATE_TIME2);
		  DateTime dt1 = formatter.parseDateTime(startTime);
		  DateTime dt2 = formatter.parseDateTime(endTime);
		  Duration diff = new Duration(dt1, dt2);


		  if(diff.getStandardMinutes()==60){

			 duration= Long.toString(diff.getStandardHours())+" hr";
		  }
		  else if(diff.getStandardMinutes()>60){

			  duration= Long.toString(diff.getStandardHours())+" hrs";

		  }
		  else{

			  duration=Long.toString(diff.getStandardMinutes())+" min";

		  }
		  for(Map<String,Object> dataMap:emailBodies){
			  dataMap.put(AppConstants.EMAIL_TYPE_BCC,emailList );
			  dataMap.put("date", d1.toString(format2));
			  dataMap.put("time", startTime);
			  dataMap.put("duration",duration);
			  dataMap.put("instructor",instructorName);
			  dataMap.put("activityName",activityName);
			  dataMap.put("roomName",roomName);
			//  dataMap.put("cancelReason",cancelReason);
			  //GSSP 253 data mapping changes
			  dataMap.put("phone",Phone);
			  dataMap.put("location",locationName);

			  dataMap.put("updatedBy",curCustName);


			  if(cancelType.trim().equalsIgnoreCase("1")){

			  if (serviceId == 0) {
				  if(serviceType.trim().equalsIgnoreCase("UPDATE")){
					//changes for LES-151
						dataMap.put(AppConstants.SUBJECT, "Guitar Center rehearsal modified");
						dataMap.put(SERVICE_TYPE, "modifyRehearsalNotAll");
				  }
				  else{
					//changes for LES-151
					  dataMap.put(AppConstants.SUBJECT, "Guitar Center rehearsal cancelled");
					  dataMap.put(SERVICE_TYPE, "cancelRehearsalNotAll");
				  }
			  } else {
				  if(serviceType.trim().equalsIgnoreCase("UPDATE")){
					  //Changes for LES-151
						dataMap.put(AppConstants.SUBJECT, "Guitar Center lesson modified");
						dataMap.put(SERVICE_TYPE, "modifyLessonNotAll");
				  }
				  else{
					//Changes for LES-151
					  dataMap.put(AppConstants.SUBJECT, "Guitar Center lesson cancelled");
					  dataMap.put(SERVICE_TYPE, "cancelLessonNotAll");
				  }
				}
			  }
			  else{
				  if(serviceId==0){
					  if(serviceType.trim().equalsIgnoreCase("UPDATE")){
						//changes for LES-151
						  dataMap.put(AppConstants.SUBJECT, "Guitar Center rehearsal modified");
						  dataMap.put(SERVICE_TYPE, "modifyRehearsal");
					  }
					  else{
						//changes for LES-151
						  dataMap.put(AppConstants.SUBJECT, "Guitar Center rehearsal cancelled");
						  dataMap.put(SERVICE_TYPE, "cancelRehearsal");

					  }
					} else {
						if(serviceType.trim().equalsIgnoreCase("UPDATE")){
							//Changes for LES-151
							dataMap.put(AppConstants.SUBJECT, "Guitar Center lesson modified");
							dataMap.put(SERVICE_TYPE, "modifyLesson");
						}
						else{
							//Changes for LES-151
							dataMap.put(AppConstants.SUBJECT, "Guitar Center lesson cancelled");
							dataMap.put(SERVICE_TYPE, "cancelLesson");
						}
					}
			  }
			  new Thread(new EmailThread(dataMap, EMAIL_TEMPLATE_FOR_SINGLE_APPT, mailSenderService)).start();

		  }

	}

	@Override//GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-starts
	public void sendEmailForCreateAppt(Appointment appointment, String isRecurring, int duration,String instructorName,Room room ,Activity activityName,long ProfileID) {

		//TODO:: OLL-3811 If lesson type is Trail send email using CRM template to the customer and store users notification as normal
		//1.check the lesson type 2.If Trail then  form the object and body using CRM template 3. send email using CRM template
		//And also make sure customer email is not set if Trail Lesson type
		//For other lesson types send email using existing template
	/*	String crmApiKey = "L3xgnKvvzo6YvuG6gIijT9GkV8qPqD8O1VBTRdoc"; // Replace with actual API key
		String crmUrl = "https://gcapi.guitarcenter.com/dev/gcss/v1/lessons/trial/scheduled/brand/gc";
		String crmContentType = "application/json";*/


		if (String.valueOf(appointment.getActivity().getActivityId()).equals("140") ||
				String.valueOf(appointment.getActivity().getActivityId()).equals("320")) {
			LessonScheduledDTO lessonDTO = createLessonScheduledDTO(appointment,"0",duration,instructorName,activityName,ProfileID,null);
			System.out.println("Appointment Type is Trail Lesson, so sending email using CRM template");


			try {
				ResponseEntity<String> response = externalApiService.postJsonToUrl(crmUrl, crmApiKey, crmContentType, lessonDTO);
			} catch (Exception e) {
				LOG.error("Error posting to CRM URL", e);
			}


		}

		//For GSSP-243 send email with cc Associates/Managers/Leads


		String[] emailList = getEmployeeEmailIdsbyProfileID(ProfileID);

		if(appointment.getCustomers()!=null){
			Iterator<Customer> customerIter = appointment.getCustomers().iterator();
			while(customerIter.hasNext()){
				Customer customer = customerIter.next();
				Map<String, Object> dataMap = renderDataForCreateEmail(appointment,  isRecurring,  duration, instructorName,room,activityName,customer,ProfileID);


				//For GSSP-243 send email with cc Associates/Managers/Leads
				dataMap.put(AppConstants.EMAIL_TYPE_BCC, emailList);

				new Thread(new EmailThread(dataMap, EMAIL_TEMPLATE_FOR_APPT, mailSenderService)).start();
			}
		}
	}

	@Override
	public void sendEmailForCreateApptCRMFlow(Appointment appointment, String isRecurring, int duration, String instructorName, Room room, Activity activityname, long ProfileID, String orderId) {

		//TODO:: OLL-3811 If lesson type is Trail send email using CRM template to the customer and store users notification as normal
		//1.check the lesson type 2.If Trail then  form the object and body using CRM template 3. send email using CRM template
		//And also make sure customer email is not set if Trail Lesson type
		//For other lesson types send email using existing template



		if (String.valueOf(appointment.getActivity().getActivityId()).equals("140") ||
				String.valueOf(appointment.getActivity().getActivityId()).equals("320")) {
			LessonScheduledDTO lessonDTO = createLessonScheduledDTO(appointment,"0",duration,instructorName,activityname,ProfileID,orderId);
			System.out.println("Appointment Type is Trail Lesson, so sending email using CRM template");


			try {
				ResponseEntity<String> response = externalApiService.postJsonToUrl(crmUrl, crmApiKey, crmContentType, lessonDTO);
			} catch (Exception e) {
				LOG.error("Error posting to CRM URL", e);
			}


		}



		//For GSSP-243 send email with cc Associates/Managers/Leads


		String[] emailList = getEmployeeEmailIdsbyProfileID(ProfileID);

		if(appointment.getCustomers()!=null){
			Iterator<Customer> customerIter = appointment.getCustomers().iterator();
			while(customerIter.hasNext()){
				Customer customer = customerIter.next();
				Map<String, Object> dataMap = renderDataForCreateEmail(appointment,  isRecurring,  duration, instructorName,room,activityname,customer,ProfileID);


				//For GSSP-243 send email with cc Associates/Managers/Leads
				dataMap.put(AppConstants.EMAIL_TYPE_BCC, emailList);

				new Thread(new EmailThread(dataMap, EMAIL_TEMPLATE_FOR_APPT, mailSenderService)).start();
			}
		}
	}


	//GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-starts
	private Map<String, Object> renderDataForCreateEmail(
			Appointment appointment, String isRecurring, int duration, String instructorName,Room room ,Activity activityName,Customer customer,long ProfileID) {
		Map<String, Object> dataMap = renderBasicData(appointment, customer, duration);
		//GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-starts
		dataMap.put("instructorName", instructorName);
		dataMap.put("roomName", room.getProfileRoomName());
		dataMap.put("activityName",activityName.getActivityName());
		//GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-end
		if("false".equalsIgnoreCase(isRecurring)){
			if(appointment.getActivity().getService().getServiceId()==0){
				//changes for LES-151
				dataMap.put(AppConstants.SUBJECT, "Rehearsal scheduled at Guitar Center");
				dataMap.put(SERVICE_TYPE, "newRehearsal");
			}else{
				//changes for LES-151
				dataMap.put(AppConstants.SUBJECT, "Lesson scheduled at Guitar Center");
				dataMap.put(SERVICE_TYPE, "newLesson");
			}
		}else{
			//changes for LES-151
			if(appointment.getActivity().getService().getServiceId()==0){
				dataMap.put(AppConstants.SUBJECT, "Rehearsals scheduled at Guitar Center");
				dataMap.put(SERVICE_TYPE, "newRehearsalRecurring");
			}else{
				//changes for LES-151
				dataMap.put(AppConstants.SUBJECT, "Lessons scheduled at Guitar Center");
				dataMap.put(SERVICE_TYPE, "newLessonRecurring");
			}
		}
		return dataMap;
	}

	@Override//GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-starts
	public void sendEmailForModifyAppt(Appointment appointment, String modifyType, int duration,String instructorName,Room room ,Activity activityName,long ProfileID) {

		if(appointment.getCustomers()!=null){
			 //For GSSP-243 send email with cc Associates/Managers/Leads
			String[] emailList = getEmployeeEmailIdsbyProfileID(ProfileID);
			Iterator<Customer> customerIter = appointment.getCustomers().iterator();
			while(customerIter.hasNext()){
				Customer customer = customerIter.next();
				Map<String, Object> dataMap = renderDataForModifyEmail(appointment,
						modifyType, duration,instructorName,room,activityName, customer,ProfileID);
				//For GSSP-243 send email with cc Associates/Managers/Leads
				dataMap.put(AppConstants.EMAIL_TYPE_BCC,emailList );
				new Thread(new EmailThread(dataMap, EMAIL_TEMPLATE_FOR_APPT, mailSenderService)).start();


			}
		}
	}

	private LessonScheduledDTO createLessonScheduledDTO(Appointment appointment, String modifyType, int duration,String instructorName ,Activity activityName,long ProfileID,String orderId) {
 		Customer customer =   appointment.getCustomers().iterator().next();

		if(customer.getPerson()==null){
			customer = customerDAO.get(customer.getCustomerId(), DAOHelper.FETCH_PERSON);
		}
		Location location = getLocation(appointment);

		LocationProfile locationProfileObj = getLocationProfilewithProfileId(appointment.getLocationProfile().getProfileId());

		LessonScheduledDTO lessonDTO = new LessonScheduledDTO();
		lessonDTO.setLessonsType("Trial"); //GSSP-278 code changes
	switch (modifyType) {
	    case "0":
	        lessonDTO.setEvent("Scheduled");
	        break;
	    case "1":
	        lessonDTO.setEvent("ReScheduled");
	        break;
	    case "2":
	        lessonDTO.setEvent("Cancelled");
	        break;
	    default:
	        lessonDTO.setEvent("Scheduled");
	        break;
	}
		lessonDTO.setSfCustomerId1(SystemUtil.extractOrderId(orderId));//Need to set the Salesforce Customer ID if available
		lessonDTO.setLessonsRequestedDate(DateTimeUtil.formatDateForLesson(appointment.getStartTimeStr()));
		lessonDTO.setLessonsRequestedTime(DateTimeUtil.extractTimeForLesson(appointment.getStartTimeStr()));
		lessonDTO.setInstrument(activityName.getActivityName());
		lessonDTO.setStoreNumber(String.valueOf(location.getExternalId()));
		lessonDTO.setStoreStreet(String.valueOf(location.getAddress1()));
		lessonDTO.setStoreCity(String.valueOf(location.getCity()));
		lessonDTO.setStoreState(String.valueOf(location.getState()));
		lessonDTO.setStoreZip(String.valueOf(location.getZip()));
		lessonDTO.setLessonsDuration(String.valueOf(duration));
		lessonDTO.setLessonsFollowup("Y");
		Customer firstCustomer = appointment.getCustomers().iterator().next();
		lessonDTO.setLessonsFollowupName(customer.getPerson().getFirstName() + " " + customer.getPerson().getLastName());
		lessonDTO.setLessonsFollowupEmail(customer.getPerson().getEmail());
		lessonDTO.setLessonsFollowupPhone(DateTimeUtil.formatPhoneNumber(customer.getPerson().getPhone()));
		lessonDTO.setLessonsFollowupZipCode(String.valueOf(location.getZip()));
		lessonDTO.setComments(appointment.getNote());
		lessonDTO.setSource("GCSS");
		SimpleDateFormat sdf = new SimpleDateFormat("MM-dd-yyyy hh:mm a");
		String formattedDate = sdf.format(new Date());
		lessonDTO.setUpdated(formattedDate);
		lessonDTO.setAppointmentId(String.valueOf(appointment.getAppointmentId()));
		lessonDTO.setCustomerId(String.valueOf(customer.getExternalId()));
		lessonDTO.setAppointmentId(String.valueOf(appointment.getAppointmentId()));
		//lessonDTO.setInstrument(String.valueOf(appointment.getActivity().getActivityId()));
		return lessonDTO; // <-- Add this line


	}



	//For GSSP-243 send email with cc Associates/Managers/Leads
	private String[] getEmployeeEmailIdsbyProfileID( long profileID) {
		AppointmentCriterion<String> emailCriterion = AppointmentCriterion.findEmployeeEmailIdsbyProfileID(profileID);
		List<String> emailList = appointmentDAO.search(emailCriterion);
		String[] emailarray = null;

		if(null != emailList && emailList.size()>0)
		{
			emailarray = emailList.toArray(new String[emailList.size()]);
		}
	return 	emailarray;
	}

	//Added for LES-119
	@Override
	//GSSP-278 code changes
	public void sendEmailForModifySingleAppt(Appointment appointment, String modifyType, int duration, String instructorName,Room room,Activity  activityName, long ProfileID) {

		//TODO:: OLL-3811 If lesson type is Trail send email using CRM template to the customer and store users notification as normal
		//1.check the lesson type 2.If Trail then  form the object and body using CRM template 3. send email using CRM template
		//And also make sure customer email is not set if Trail Lesson type
		//For other lesson types send email using existing template
		/*String crmApiKey = "L3xgnKvvzo6YvuG6gIijT9GkV8qPqD8O1VBTRdoc"; // Replace with actual API key
		String crmUrl = "https://gcapi.guitarcenter.com/dev/gcss/v1/lessons/trial/scheduled/brand/gc";
		String crmContentType = "application/json";*/


		if (String.valueOf(appointment.getActivity().getActivityId()).equals("140") ||
		    String.valueOf(appointment.getActivity().getActivityId()).equals("320")) {
			LessonScheduledDTO lessonDTO = createLessonScheduledDTO(appointment,modifyType,duration,instructorName,activityName,ProfileID,null);
			System.out.println("Appointment Type is Trail Lesson, so sending email using CRM template");


			try {
				ResponseEntity<String> response = externalApiService.postJsonToUrl(rescheduledcrmUrl, crmApiKey, crmContentType, lessonDTO);
			} catch (Exception e) {
				LOG.error("Error posting to CRM URL", e);
			}


		}

		if(appointment.getCustomers()!=null){
			 //For GSSP-243 send email with cc Associates/Managers/Leads
			String[] emailList = getEmployeeEmailIdsbyProfileID(ProfileID);
			Iterator<Customer> customerIter = appointment.getCustomers().iterator();
			while(customerIter.hasNext()){
				Customer customer = customerIter.next();
//GSSP-278 code changes
				Map<String, Object> dataMap = renderDataForModifySingleEmail(appointment,
						modifyType, duration, customer,instructorName,room,activityName,ProfileID);
				 //For GSSP-243 send email with cc Associates/Managers/Leads
				 dataMap.put(AppConstants.EMAIL_TYPE_BCC,emailList );
				new Thread(new EmailThread(dataMap, EMAIL_TEMPLATE_FOR_SINGLE_APPT, mailSenderService)).start();


			}
		}
	}

	//Changes done for LES-119  //GSSP-278 Changes Made for Modify mail confirmation ,adding activity,instructor room.-starts
	private Map<String, Object> renderDataForModifyEmail(
			Appointment appointment, String modifyType, int duration,String instructorName,Room room ,Activity activityName, Customer customer, Long ProfileID) {
		Map<String, Object> dataMap = renderBasicData(appointment, customer, duration);

			if (appointment.getActivity().getService().getServiceId() == 0) {
				dataMap.put(AppConstants.SUBJECT, "Guitar Center rehearsals modified");
				dataMap.put(SERVICE_TYPE, "modifyRehearsalAll");
			} else {
				dataMap.put(AppConstants.SUBJECT, "Guitar Center lessons modified");
				dataMap.put(SERVICE_TYPE, "modifyLessonAll");
			}//GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-starts
			dataMap.put("instructorName", instructorName);
			dataMap.put("roomName", room.getProfileRoomName());
			dataMap.put("activityName",activityName.getActivityName());
			//GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-end
		return dataMap;
	}


	//Added for LES-119
	//GSSP-278 code changes
	private Map<String, Object> renderDataForModifySingleEmail(
			Appointment appointment, String modifyType, int duration, Customer customer,String instuctorName,Room room,Activity  activityName, Long ProfileID)
    {

		Map<String, Object> dataMap = renderBasicData(appointment, customer, duration);

		String dateTime = "";

		int dateTimeSplitIndex =0;



		if(null != dataMap && dataMap.containsKey("date"))
		{

			dateTime =  (String)dataMap.get("date");

			if(dateTime.indexOf("-")!= -1 )
			{
				dateTimeSplitIndex = dateTime.indexOf("-");

				if(dateTimeSplitIndex != 0)
				{
					dataMap.put("time", dateTime.substring(dateTimeSplitIndex+1));
					dataMap.put("date", dateTime.substring(0,dateTimeSplitIndex));
				}
			}
		}
		//GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-starts
			dataMap.put("instructor", instuctorName);
			dataMap.put("roomName", room.getProfileRoomName());
			dataMap.put("activityName",activityName.getActivityName());
			//GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-ends
		if ("1".equalsIgnoreCase(modifyType)) {
			if (appointment.getActivity().getService().getServiceId() == 0) {
				//changes for LES-151
				dataMap.put(AppConstants.SUBJECT, "Guitar Center rehearsal modified");
				dataMap.put(SERVICE_TYPE, "modifyRehearsal");
			} else {
				//changes for LES-151
				dataMap.put(AppConstants.SUBJECT, "Guitar Center lesson modified");
				dataMap.put(SERVICE_TYPE, "modifyLesson");
			}
		} else if ("3".equalsIgnoreCase(modifyType)) {
			if (appointment.getActivity().getService().getServiceId() == 0) {
				//changes for LES-151
				dataMap.put(AppConstants.SUBJECT, "Guitar Center rehearsal modified");
				dataMap.put(SERVICE_TYPE, "modifyRehearsalNotAll");
			} else {
				//changes for LES-151
				dataMap.put(AppConstants.SUBJECT, "Guitar Center lesson modified");
				dataMap.put(SERVICE_TYPE, "modifyLessonNotAll");
			}
		}
		return dataMap;
    }


	//Changes made for LES-120
	@Override//GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-starts
	public void sendEmailForCancelAppt(Appointment appointment, String cancelType, int duration, Set<Customer> customerSet,String instructorName,Activity activityname,long ProfileID) {
		if(customerSet!=null){
			 //For GSSP-243 send email with cc Associates/Managers/Leads
			String[] emailList = getEmployeeEmailIdsbyProfileID(ProfileID);
			Iterator<Customer> customerIter = customerSet.iterator();
			while(customerIter.hasNext()){
				Customer customer = customerIter.next();
				Map<String, Object> dataMap = renderDataForCancelEmail(appointment,
						cancelType, duration, customer,instructorName,activityname);
				//For GSSP-243 send email with cc Associates/Managers/Leads
				dataMap.put(AppConstants.EMAIL_TYPE_BCC,emailList);
				new Thread(new EmailThread(dataMap, EMAIL_TEMPLATE_FOR_APPT, mailSenderService)).start();
			}
		}
	}


	//Changes made for LES-120
	@Override
	//GSSP-278 code changes
	public void sendEmailForCancelSingleAppt(Appointment appointment, String cancelType, int duration, Set<Customer> customerSet,
			String instructorName,Activity activityname,long ProfileID) {

		//TODO:: OLL-3811 If lesson type is Trail send email using CRM template to the customer and store users notification as normal
		//1.check the lesson type 2.If Trail then  form the object and body using CRM template 3. send email using CRM template
		//And also make sure customer email is not set if Trail Lesson type
		//For other lesson types send email using existing template


		if (String.valueOf(appointment.getActivity().getActivityId()).equals("140") ||
				String.valueOf(appointment.getActivity().getActivityId()).equals("320")) {
			LessonScheduledDTO lessonDTO = createLessonScheduledDTO(appointment,"2",duration,instructorName,activityname,ProfileID,null);
			System.out.println("Appointment Type is Trail Lesson, so sending email using CRM template");


			try {
				ResponseEntity<String> response = externalApiService.postJsonToUrl(cancelledcrmUrl, crmApiKey, crmContentType, lessonDTO);
			} catch (Exception e) {
				LOG.error("Error posting to CRM URL", e);
			}


		}

		if(customerSet!=null){
			 //For GSSP-243 send email with cc Associates/Managers/Leads
			String[] emailList = getEmployeeEmailIdsbyProfileID(ProfileID);
			Iterator<Customer> customerIter = customerSet.iterator();
			while(customerIter.hasNext()){
				Customer customer = customerIter.next();
				Map<String, Object> dataMap = renderDataForSingleCancelEmail(appointment,
						cancelType, duration, customer,instructorName,activityname);
				//For GSSP-243 send email with cc Associates/Managers/Leads
				dataMap.put(AppConstants.EMAIL_TYPE_BCC,emailList);
				new Thread(new EmailThread(dataMap, EMAIL_TEMPLATE_FOR_SINGLE_APPT, mailSenderService)).start();
			}
		}
	}


	//Changes made for LES-120
	private Map<String, Object> renderDataForCancelEmail(
			Appointment appointment, String cancelType, int duration, Customer customer,String instructorName,Activity activityname) {
		Map<String, Object> dataMap = renderBasicData(appointment, customer, duration);

			if (appointment.getActivity().getService().getServiceId() == 0) {
				//changes for LES-151
				dataMap.put(AppConstants.SUBJECT, "Guitar Center rehearsals cancelled");
				dataMap.put(SERVICE_TYPE, "cancelRehearsalAll");
			} else {
				//changes for LES-151
				dataMap.put(AppConstants.SUBJECT, "Guitar Center lessons cancelled");
				dataMap.put(SERVICE_TYPE, "cancelLessonAll");
			}//GSSP-278 Changes Made for  mail confirmation ,adding activity,instructor room.-starts
			dataMap.put("instructorName", instructorName);
			dataMap.put("activityName", activityname.getActivityName());
			//dataMap.put("cancelReason", cancelreason);
			//GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-ends
		return dataMap;
	}


	//Added made for LES-120
	//GSSP-278 code changes
	private Map<String, Object> renderDataForSingleCancelEmail(
			Appointment appointment, String cancelType, int duration, Customer customer,String instructorName,Activity activityname) {
		Map<String, Object> dataMap = renderBasicData(appointment, customer, duration);


		String dateTime = "";

		int dateTimeSplitIndex =0;



		if(null != dataMap && dataMap.containsKey("date"))
		{

			dateTime =  (String)dataMap.get("date");

			if(dateTime.indexOf("-")!= -1 )
			{
				dateTimeSplitIndex = dateTime.indexOf("-");

				if(dateTimeSplitIndex != 0)
				{
					dataMap.put("time", dateTime.substring(dateTimeSplitIndex+1));
					dataMap.put("date", dateTime.substring(0,dateTimeSplitIndex));
				}
			}
		}//GSSP-278 Changes Made for  mail confirmation ,adding activity,instructor room.-starts

			dataMap.put("instructor", instructorName);
			dataMap.put("activityName", activityname.getActivityName());
		//	dataMap.put("cancelReason", cancelreason);
			//GSSP-278 Changes Made for  mail confirmation ,adding activity,instructor room.-ends
		if ("1".equalsIgnoreCase(cancelType)) {
			if (appointment.getActivity().getService().getServiceId() == 0) {
				//changes for LES-151
				dataMap.put(AppConstants.SUBJECT, "Guitar Center rehearsal cancelled");
				dataMap.put(SERVICE_TYPE, "cancelRehearsal");
			} else {
				//changes for LES-151
				dataMap.put(AppConstants.SUBJECT, "Guitar Center lesson cancelled");
				dataMap.put(SERVICE_TYPE, "cancelLesson");
			}
		} else if ("3".equalsIgnoreCase(cancelType)) {
			if (appointment.getActivity().getService().getServiceId() == 0) {
				//changes for LES-151
				dataMap.put(AppConstants.SUBJECT, "Guitar Center rehearsal cancelled");
				dataMap.put(SERVICE_TYPE, "cancelRehearsalNotAll");
			} else {
				//changes for LES-151
				dataMap.put(AppConstants.SUBJECT, "Guitar Center lesson cancelled");
				dataMap.put(SERVICE_TYPE, "cancelLessonNotAll");
			}
		}
		return dataMap;
	}




	/**
	 * Helper method to build duration literal string for UI displaying
	 *
	 * @param minutes
	 * @return
	 */
	private String buildDuration(float minutes) {
		String duration = "";
		if (minutes < 60) {
			duration = minutes + " minutes";
		} else if (60 == minutes) {
			duration = "1 Hour";
		} else {
			duration = (minutes / 60) + " Hrs";
		}
		duration = duration.replace(".0", "");
		return duration;
	}

	/**
	 * Helper method to build time string from a Date
	 *
	 * @param date
	 * @return
	 */
	private String buildTimeStr(Date date) {
		String result = new DateTime(date).toString(DateTimeFormat.forPattern("hh:mm a"));
		if ("11:59 PM".equals(result)) {
			result = "12:00 AM";
		}
		return result;
	}
	/**
	 * Helper method to build customer name from a customer list
	 *
	 * @param customerList
	 * @return
	 */
	private String buildCustomerName(Collection<Customer> customerList) {
		StringBuilder name = new StringBuilder();
		if (null != customerList && !customerList.isEmpty()) {
			for (Customer c : customerList) {
				String firstName = c.getPerson().getFirstName();
				String lastName = c.getPerson().getLastName();
				if (StringUtils.isNotBlank(firstName)) {
					name.append(firstName);
					name.append(" ");
				}
				if (StringUtils.isNotBlank(lastName)) {
					name.append(lastName);
				}
				name.append(", ");
			}
			name.deleteCharAt(name.length() - 2);
		}
		return name.toString();
	}

		/**
		 * for instructor to send appointment reminder
		 * @param appointmentId
		 */
		@Override
		public void sendEmailForCustomerReminder(Long appointmentId,
												 Long customerId,
												 String meetingJoinUrl)  throws Exception {

			if(appointmentId == null||customerId == null||StringUtils.isEmpty(meetingJoinUrl)) {
				throw new BizRuntimeException("email send failed appointment id is null or meetingJoinUrl is null");
			}
			Appointment appointment = appointmentService.getAppointment(appointmentId);
			Customer customer = customerDAO.get(customerId,DAOHelper.FETCH_PERSON | DAOHelper.FETCH_CUSTOMER_STATUS);

			//Adding for Timezone display on customer email

			Location locationObj = getLocation(appointment);

			LocationProfile locationProfileObj = getLocationProfile(appointment);


			String tz = null;
			if(null != locationProfileObj && null!= locationProfileObj.getTimeZone()) {
				tz = locationProfileObj.getTimeZone();
			}

			Map dataMap = renderDataInstForReminderEmail(appointment,customer);
			SimpleDateFormat startTimeFormat = new SimpleDateFormat("EEE, MMM d, yyyy-hh:mm aaa");
			Format endTimeFormat = new SimpleDateFormat("hh:mm aaa");
			dataMap.put("date",startTimeFormat.format(appointment.getStartTime()));
			dataMap.put("endTime",endTimeFormat.format(appointment.getEndTime()) + " " + tz );

			dataMap.put("meetingJoinUrl",meetingJoinUrl);
			String instructorFullName = appointment.getInstructor().getPerson().getFirstName()+" "+appointment.getInstructor().getPerson().getLastName();
			dataMap.put("instructorFullName",instructorFullName);

			//mailSenderService.sendMail(dataMap, EMAIL_TEMPLATE_FOR_SINGLE_CUSTOMER_REMINDER);
			new Thread(new EmailThread(dataMap, EMAIL_TEMPLATE_FOR_SINGLE_CUSTOMER_REMINDER, mailSenderService)).start();
		}

	/**
	 * for instructor to send appointment reminder
	 * @param dto InstructorLessonLinkDTO
	 */
	@Override
	public void sendEmailForCustomerReminderInStore(InstructorLessonLinkDTO dto) throws Exception {
		if (dto.getAppointmentId() == null || dto.getCustomerId() == null) {
			throw new BizRuntimeException("email send failed appointment id is null");
		}
		Appointment appointment = appointmentService.getAppointment(Long.parseLong(dto.getAppointmentId()));
		Customer customer = customerDAO.get(Long.parseLong(dto.getCustomerId()), DAOHelper.FETCH_PERSON | DAOHelper.FETCH_CUSTOMER_STATUS);
		Location locationObj = getLocation(appointment);

		LocationProfile locationProfileObj = getLocationProfile(appointment);


		String tz = null;
		if(null != locationProfileObj && null!= locationProfileObj.getTimeZone()) {
			tz = locationProfileObj.getTimeZone();
		}

		StringBuilder location = new StringBuilder();
		StringBuilder locationForMap = new StringBuilder();
		//location.append(locationObj.getExternalId());
		//location.append(" ");
		//location.append(dto.getLocationName());
		//location.append(", ");
		location.append(locationObj.getAddress1());
		if (locationObj.getAddress2()!= null && !locationObj.getAddress2().isEmpty()) {
			location.append(", ");
			location.append(locationObj.getAddress2());
		}
		location.append(", ");
		location.append(locationObj.getCity());
		location.append(", ");
		location.append(locationObj.getState());
		location.append(", ");
		location.append(locationObj.getZip());

		if (StringUtils.isNotBlank(locationObj.getPhone())) {
			location.append(" Phone: ");
			location.append(locationObj.getPhone());
		}

		//For MAP
		locationForMap.append(locationObj.getAddress1());
		if (locationObj.getAddress2()!= null && !locationObj.getAddress2().isEmpty()) {
			locationForMap.append(", ");
			locationForMap.append(locationObj.getAddress2());
		}
		locationForMap.append(", ");
		locationForMap.append(locationObj.getCity());
		locationForMap.append(", ");
		locationForMap.append(locationObj.getState());
		locationForMap.append(", ");
		locationForMap.append(locationObj.getZip());

		Map dataMap = renderDataInstForReminderEmail(appointment,customer);
		SimpleDateFormat startTimeFormat = new SimpleDateFormat("EEE, MMM d, yyyy-hh:mm aaa");
		Format endTimeFormat = new SimpleDateFormat("hh:mm aaa");
		dataMap.put("date",startTimeFormat.format(appointment.getStartTime()));
		dataMap.put("endTime",endTimeFormat.format(appointment.getEndTime()) + " " + tz);
		dataMap.put("location", location.toString());
		String locationMapUrl = "";
		try {
			locationMapUrl = "https://www.google.com/maps/place/" + URLEncoder.encode(locationForMap.toString(), "UTF-8");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		dataMap.put("locationMapUrl", locationMapUrl);
		String instructorFullName = appointment.getInstructor().getPerson().getFirstName()+" "+appointment.getInstructor().getPerson().getLastName();
		dataMap.put("instructorFullName",instructorFullName);

		new Thread(new EmailThread(dataMap, EMAIL_TEMPL_FOR_SINGLE_CUSTOMER_REM_INSTORE, mailSenderService)).start();

	}

	@Transactional
	@Override
	public void sendEmailFromInstructorToCustomer(InstructorLessonLinkDTO dto) throws Exception {
		if (dto.getAppointmentId() == null || dto.getCustomerId() == null) {
			throw new BizRuntimeException("email send failed appointment id is null");
		}
		//System.out.println("sendEmailFromInstructorToCustomer");
		//System.out.println("InstructorLessonLinkDTO     "+dto);
		Appointment appointment = appointmentService.getAppointment(Long.parseLong(dto.getAppointmentId()));
 		Customer customer = customerDAO.get(Long.parseLong(dto.getCustomerId()), DAOHelper.FETCH_PERSON | DAOHelper.FETCH_CUSTOMER_STATUS);
		Location locationObj = getLocation(appointment);

		LocationProfile locationProfileObj = getLocationProfile(appointment);

		Appointment nextAppointment = null;
		Appointment prevAppointment = null;
		List<InstructorAppointmentStatus> instructorAppointmentStatus =	 null;

		if(appointment.getAppointmentSeries().getIsRecurring().toString().equals("Y")) {
			//findByActiveAppointmentSeriesAndDateTime
			Criterion<Appointment, Appointment> criterion = AppointmentCriterion
			.findByActiveAppointmentbyAppSeries(appointment.getAppointmentSeries().getAppointmentSeriesId());
			List<Appointment> appList = appointmentDAO.search(criterion, DAOHelper.FETCH_APPOINTMENT_SERIES);
				//System.out.println(appList.size());

				Collections.sort(appList, new Comparator<Appointment>() {
					  public int compare(Appointment o1, Appointment o2) {
					      if (o1.getStartTime() == null || o2.getStartTime() == null)
					        return 0;
					      return o1.getStartTime().compareTo(o2.getStartTime());
					  }
					});

				/*
				 * Appointment nextAppointment = null; Appointment prevAppointment = null;
				 */
				List<InstructorAppointmentStatus> nextinstructorAppointmentStatus = null;
				Boolean isPrevious = true;
				Boolean isNextApp = false;
				for(Appointment apt :appList ) {
					if(isNextApp) {
						nextAppointment = apt;
						isNextApp = false;
					}

					if(apt.getAppointmentId().equals(appointment.getAppointmentId())) {
						isPrevious = false;
						isNextApp = true;
					}
					if(isPrevious) { prevAppointment = apt; }
				}

				if(null != prevAppointment && null != prevAppointment.getAppointmentId()) {
					 instructorAppointmentStatus =	instructorAppointmentStatusDAO.getInstructorAppointmentStatus(prevAppointment.getAppointmentId());
						/*
						 * System.out.println(instructorAppointmentStatus.get(0).getRemarks());
						 * System.out.println(instructorAppointmentStatus.get(0).getPracticeNotes());
						 */
				}



		}

		//TODO:: If Recurreing Appointment Get Next non-called appointment with in the series and previous appointmentId for the instru.
		//If not next appointment , don't display next app details on the email template.



		String tz = null;
		if(null != locationProfileObj && null!= locationProfileObj.getTimeZone()) {
			tz = locationProfileObj.getTimeZone();
		}

		StringBuilder location = new StringBuilder();
		StringBuilder locationForMap = new StringBuilder();
		//location.append(locationObj.getExternalId());
		//location.append(" ");
		//location.append(dto.getLocationName());
		//location.append(", ");
		location.append(locationObj.getAddress1());
		if (locationObj.getAddress2()!= null && !locationObj.getAddress2().isEmpty()) {
			location.append(", ");
			location.append(locationObj.getAddress2());
		}
		location.append(", ");
		location.append(locationObj.getCity());
		location.append(", ");
		location.append(locationObj.getState());
		location.append(", ");
		location.append(locationObj.getZip());

		if (StringUtils.isNotBlank(locationObj.getPhone())) {
			location.append(" Phone: ");
			location.append(locationObj.getPhone());
		}

		//For MAP
		locationForMap.append(locationObj.getAddress1());
		if (locationObj.getAddress2()!= null && !locationObj.getAddress2().isEmpty()) {
			locationForMap.append(", ");
			locationForMap.append(locationObj.getAddress2());
		}
		locationForMap.append(", ");
		locationForMap.append(locationObj.getCity());
		locationForMap.append(", ");
		locationForMap.append(locationObj.getState());
		locationForMap.append(", ");
		locationForMap.append(locationObj.getZip());

		Map dataMap = renderDataInstForReminderEmailForNotes(appointment,customer);
		SimpleDateFormat startTimeFormat = new SimpleDateFormat("EEE, MMM d, yyyy-hh:mm aaa");
		Format endTimeFormat = new SimpleDateFormat("hh:mm aaa");
		dataMap.put("date",startTimeFormat.format(appointment.getStartTime()));
		dataMap.put("endTime",endTimeFormat.format(appointment.getEndTime()) + " " + tz);
		dataMap.put("location", location.toString());
		String locationMapUrl = "";
		try {
			locationMapUrl = "https://www.google.com/maps/place/" + URLEncoder.encode(locationForMap.toString(), "UTF-8");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		dataMap.put("locationMapUrl", locationMapUrl);
		String instructorFullName = appointment.getInstructor().getPerson().getFirstName()+" "+appointment.getInstructor().getPerson().getLastName();
		dataMap.put("instructorFullName",instructorFullName);
		//dataMap.put("lessonStatus",dto.getLessonStatus());
		//dataMap.put("nextLessonStatus",dto.getNextLessonStatus());
		dataMap.put("assignment",dto.getAssignment());
		dataMap.put("practiceNotes", dto.getPracticeNotes());
		dataMap.put("remarks",dto.getRemarks());
		if(null != nextAppointment && null!= nextAppointment.getStartTime()) {
			   java.time.format.DateTimeFormatter dateFormatter
	        = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

	    // date format 2
	       java.time.format.DateTimeFormatter dateFormatterNew
	        = java.time.format.DateTimeFormatter.ofPattern("EEEE, MMM d, HH:mm a");

	       java.time.LocalDateTime ldateTime = java.time.LocalDateTime.parse(nextAppointment.getStartTimeStr(), dateFormatter);

			dataMap.put("nextAppointmentDate",dateFormatterNew.format(ldateTime));
			dataMap.put("nextLessonType",nextAppointment.getActivity().getActivityName());

		}else {
			dataMap.put("nextAppointmentDate","");
			dataMap.put("nextLessonType","");
		}

		if(null != instructorAppointmentStatus && !instructorAppointmentStatus.isEmpty() && null != instructorAppointmentStatus.get(0) && null != instructorAppointmentStatus.get(0).getRemarks()
				&& null != instructorAppointmentStatus.get(0).getPracticeNotes() ) {
		dataMap.put("previousRemarks",instructorAppointmentStatus.get(0).getRemarks());
		dataMap.put("previousPracticeNotes",instructorAppointmentStatus.get(0).getPracticeNotes());

		}else {
			dataMap.put("previousRemarks","");
			dataMap.put("previousPracticeNotes","");
		}
		/*
		 * dataMap.put("nextAppointmentDate",nextAppointment.getStartTime());
		 * dataMap.put("remarks",nextAppointment.getActivity().getActivityName());
		 */
		//dataMap.put("rating", dto.getRate());


		new Thread(new EmailThread(dataMap, "apptEmailFromInstructorToCustomer.ftl", mailSenderService)).start();

	}

		//Changes made for GSSP-230

		public void sendTerminatedEmployeeEmail() {

			Criterion<Appointment, Map<Long,List<String>>> empCriterion = AppointmentCriterion.findByZoneID(null);
			List<Map<Long,List<String>>> employeeLocationList = appointmentDAO.search(empCriterion);

			Criterion<Appointment, Map<Long,List<String>>> terminatedEmpCriterion = AppointmentCriterion.findTerminatedEnabledInstructor();
			List<Map<Long,List<String>>> terminatedEnabledInstructorListMap = appointmentDAO.search(terminatedEmpCriterion);

			Map<Long,List<String>> terminatedEnabledInstructorMap = new HashMap<Long,List<String>>();

			Map<Long,List<String>> employeeLocationMap = new HashMap<Long,List<String>>();

			List<String> terminateEmployeeList = new ArrayList<String>();




			if(null !=  terminatedEnabledInstructorListMap  &&  terminatedEnabledInstructorListMap.size() > 0)
			{
				terminatedEnabledInstructorMap =  terminatedEnabledInstructorListMap.get(0);

				if(null != employeeLocationList && employeeLocationList.size() > 0)
				{

					employeeLocationMap = employeeLocationList.get(0);
				}

				for(Long locationId : terminatedEnabledInstructorMap.keySet())
				{
					terminateEmployeeList = 	terminatedEnabledInstructorMap.get(locationId);
					Location location =locationService.findById(new Long(locationId));
					Map<String, Object> dataMap = renderDataForTerminatedEmail(terminateEmployeeList, employeeLocationMap.get(locationId),location);

					mailSenderService.sendMail(dataMap, EMAIL_TEMPLATE_FOR_TERMINATED_EMPLOYEE_NOTIFICATION);
				}

			}

		}


		//Changes made for GSSP-230
		private Map<String, Object> renderDataForTerminatedEmail(List<String> terminatedEmployeeList, List<String> mailingList,Location location) {
			Map<String, Object> dataMap = new HashMap<String, Object>();

			String sub = "Guitar Center Studios Terminated Instructors Notification for " + location.getExternalId()+"-"+location.getLocationName();


			dataMap.put(AppConstants.SUBJECT, sub);
			//dataMap.put(AppConstants.EMAIL_TYPE_CC, AppConstants.JOB_NOTIFICATION_EMAIL_SEND_LIST);
			dataMap.put("parent",terminatedEmployeeList);

			if(null !=  mailingList && mailingList.size()>0)
			{
				String[] mailArray = mailingList.toArray(new String[mailingList.size()]);
				dataMap.put(AppConstants.EMAIL_TYPE_TO, mailArray);
				dataMap.put(AppConstants.EMAIL_TYPE_CC, AppConstants.GCSS_SUPPORT);
			}


			return dataMap;
		}

		@Override
		public void sendEmailToRegistedCustomer(RegistedCustomerEmailDTO dto) throws Exception {//For GSSP-243 send email with cc Associates/Managers/Leads

			//String emailList = dto.getRegEmail();

			//emailList = "<EMAIL>";


				//while(customerIter.hasNext()){

					Map<String, Object> dataMap = new HashMap<>();

					dataMap.put(AppConstants.SUBJECT, "Customer Registation Completed success at Guitar Center");
					dataMap.put("RegEmail", dto.getRegEmail());
					//dataMap.put("RegEmail", "Email");
					dataMap.put("keyword", dto.getPassword());
					//For GSSP-243 send email with cc Associates/Managers/Leads
					//dataMap.put(AppConstants.EMAIL_TYPE_TO, emailList);

					 dataMap.put(AppConstants.FROM, "<EMAIL>");
					 Context env = (Context) new InitialContext().lookup("java:comp/env");
					 dataMap.put("customerName", dto.getFirstName()+" "+dto.getLastName());


					 String emailHeaderImgUrl = (String)env.lookup(AppConstants.EMAIL_HEADER_IMG_URL);
					 dataMap.put(AppConstants.EMAIL_HEADER_IMG_URL, emailHeaderImgUrl);

				     dataMap.put(AppConstants.EMAIL_TYPE_TO, new String[]{"<EMAIL>"});
					 dataMap.put(AppConstants.EMAIL_TYPE_BCC, new String[]{"<EMAIL>"});
					 dataMap.put("exception", "exception");
					 dataMap.put("successStatus", true);

					new Thread(new EmailThread(dataMap, REG_CUST_EMAIL_TEMPLATE, mailSenderService)).start();
				//}
			}

		@Override
		public void triggerAPIOnTrailLessoncomplection() {

			 // 1. Get a Calendar instance set to now
	        Calendar cal = Calendar.getInstance();

	        // 2. Subtract one day
	        //TODO:OLL-3738
	        int daysToAdd = -1;
	        if (dateInNumberic != null && !dateInNumberic.trim().isEmpty()) {
	            try {
	                daysToAdd = Integer.parseInt(dateInNumberic.trim());
	            } catch (NumberFormatException e) {
	                // Log or handle invalid number format if needed
	                daysToAdd = -1;
	            }
	        }

	        cal.add(Calendar.DAY_OF_MONTH, daysToAdd);

	      //  cal.add(Calendar.DAY_OF_MONTH, dateInNumberic);

	        // 3. Get the java.util.Date for yesterday
	        Date yesterday = cal.getTime();

	        // 4. Format it as "2025-04-22" style
	        SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
	        String formatted = fmt.format(yesterday);
	        Date yesterdayAtMidn = null;
			try {
				yesterdayAtMidn = fmt.parse(formatted);
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
				throw new RuntimeException("Failed to retrieve lesson completion data", e);
			}
	        LOG.error("CompletedLesson job running for date:"+yesterdayAtMidn);
	        Criterion<Appointment, LessonCompletionDTO> empCriterion = AppointmentCriterion.findTrailLessonForDateRange(yesterdayAtMidn,yesterdayAtMidn);
			List<LessonCompletionDTO> employeeLocationList = appointmentDAO.search(empCriterion);



			try {
			    RestTemplate restTemplate = RestTemplateSingleton.getRestTemplate();
			    //Comment this. //TODO:OLL-3738
			 //   String contactcenterLessonCompletedUrl = "https://gcapi.guitarcenter.com/dev/sam/v1/lessons/trial/completed/brand/gc";
			 //   String contactcenterLessonCompletedAPIKey = "X1UZDzwCqY3XCPGsTNK9wpic12KIXbm8zFMn2NW8";

			    HttpHeaders headers = new HttpHeaders();
			    headers.setContentType(MediaType.APPLICATION_JSON);
			    headers.set("X-API-KEY", contactcenterLessonCompletedAPIKey); // Replace with actual API key

			    int retryCount = 10;

			    for (LessonCompletionDTO dto : employeeLocationList) {
			        for (int attempt = 1; attempt <= retryCount; attempt++) {
			            try {
			                HttpEntity<LessonCompletionDTO> requestEntity = new HttpEntity<>(dto, headers);

			                LOG.info("Attempt {} for AppointmentID: {}", attempt, dto.getAppointmentId());

			                ResponseEntity<String> response = restTemplate.postForEntity(contactcenterLessonCompletedUrl, requestEntity, String.class);

			                LOG.error("Successfully posted for dto: {} with Status: {}",
			                		 new ObjectMapper().writeValueAsString(dto), response.getStatusCode());

			                break; // exit retry loop on success

			            } catch (Exception ex) {
			            	LOG.error("Attempt {} failed for AppointmentID: {}. Error: {}",
			                          attempt, dto.getAppointmentId(), ex.getMessage());


			                if (attempt == retryCount) {
			                	LOG.error("Final failure for AppointmentID: {} after {} attempts.",
			                              dto.getAppointmentId(), retryCount, ex);
			                	throw new RuntimeException("Failed to retrieve lesson completion data", ex);
			                } else {
			                    try {
			                        Thread.sleep(20000); // Basic backoff before retry
			                    } catch (InterruptedException ie) {
			                        Thread.currentThread().interrupt(); // Restore interrupt status
			                        LOG.error("Retry sleep interrupted for AppointmentID: {}", dto.getAppointmentId(), ie);
			                    }
			                }
			            }
			        }
			    }

			} catch (Exception e) {
			    LOG.error("Unexpected error during posting loop", e);
			    throw new RuntimeException("Failed to retrieve lesson completion data", e);
			}
		    }





			// TODO Auto-generated method stub
		//	System.out.print("triggerAPIOnTrailLessoncomplection");
	//	}




}