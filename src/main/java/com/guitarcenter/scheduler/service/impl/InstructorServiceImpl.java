package com.guitarcenter.scheduler.service.impl;

import java.io.IOException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.TreeMap;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.util.SystemOutLogger;
import org.apache.solr.client.solrj.SolrServerException;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.joda.time.LocalTime;
import org.joda.time.Minutes;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.CalendarUtil;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.common.util.TimeoffUtil;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.AvailabilityDAO;
import com.guitarcenter.scheduler.dao.InstructorAppointmentStatusDAO;
import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.PersonDAO;
import com.guitarcenter.scheduler.dao.PersonRoleDAO;
import com.guitarcenter.scheduler.dao.RoleDAO;
import com.guitarcenter.scheduler.dao.RoomDAO;
import com.guitarcenter.scheduler.dao.ServiceModeDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentCriterion;
import com.guitarcenter.scheduler.dao.criterion.AvailabilityCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.InstructorCriterion;
import com.guitarcenter.scheduler.dao.criterion.RoomCriterion;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorAppointmentStatusDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorLessonLinkDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.RoomDTO;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dto.FinalFreeSlotsAndMaxMinDates;
import com.guitarcenter.scheduler.dto.InstoreAvailableDTO;
import com.guitarcenter.scheduler.dto.InstructorAccessDTO;
import com.guitarcenter.scheduler.dto.InstructorInfoDTO;
import com.guitarcenter.scheduler.dto.ListInstructorsSchedulerDTO;
import com.guitarcenter.scheduler.dto.OnLineAvailableDTO;
import com.guitarcenter.scheduler.dto.OnLineInstoreAvailableDTO;
import com.guitarcenter.scheduler.dto.ProfileTimeOffDTO;
import com.guitarcenter.scheduler.integration.dto.EmployeeDTO;
import com.guitarcenter.scheduler.integration.dto.InstructorPersonalDetailsDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.InstoreAvailability;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.InstructorAppointmentStatus;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.OnlineAvailability;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.PersonPersonalDetails;
import com.guitarcenter.scheduler.model.PersonRole;
import com.guitarcenter.scheduler.model.Role;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.ServiceMode;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.Canceled;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.RequiresInstructor;
import com.guitarcenter.scheduler.service.AppointmentEmailService;
import com.guitarcenter.scheduler.service.AppointmentService;
import com.guitarcenter.scheduler.service.AvailabilityService;
import com.guitarcenter.scheduler.service.InstoreAvailabilityService;
import com.guitarcenter.scheduler.service.InstructorPersonalDetailsService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.LocationManagerService;
import com.guitarcenter.scheduler.service.OnlineAvailabilityService;
import com.guitarcenter.scheduler.service.PersonManagerService;
import com.guitarcenter.scheduler.service.SearchService;
import com.guitarcenter.scheduler.service.SiteService;
import com.guitarcenter.scheduler.service.TimeoffService;
import com.guitarcenter.scheduler.service.ValidationService;
import com.guitarcenter.scheduler.webservice.dto.ActivityApiDTO;
import com.guitarcenter.scheduler.webservice.dto.ActivityDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorACTServiceDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorACTServiceResponseDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceResponseDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorActivitiesAPIDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAvailableHoursDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorServiceResponseDTO;
import com.guitarcenter.scheduler.webservice.dto.Lessons;
import com.guitarcenter.scheduler.webservice.dto.LocationProfileInfoDTO;
import com.guitarcenter.scheduler.webservice.dto.Times;
import com.guitarcenter.scheduler.webservice.util.WebServiceUtil;


@Service
public class InstructorServiceImpl implements InstructorService, AppConstants{

	private static final Logger LOG = LoggerFactory.getLogger(InstructorServiceImpl.class);
	
	@Autowired
	private InstructorDAO instructorDAO;
	
	@Autowired
	private InstructorAppointmentStatusDAO instructorAppointmentStatusDAO;

    @Autowired
    private PersonDAO personDAO;	
    
    @Autowired
	private OnlineAvailabilityService onlineAvailabilityService;
	
	@Autowired
	private InstoreAvailabilityService instoreAvailabilityService;
    
    @Autowired
    private InstructorPersonalDetailsService instructorPersonalDetailsService;
    
    @Autowired
	private ValidationService validationService;
    
    @Autowired
    private AvailabilityDAO availabilityDAO;
    
    private Map<Long, Map<Long, RoomDTO>> profileRoomcache = new HashMap<Long, Map<Long, RoomDTO>>();
 
	@Autowired
	private RoomDAO roomDAO;
	
    @Autowired
    private SiteService siteService;
    
    //Changes made for GSSP-199
    @Autowired
    private PersonRoleDAO personRoleDAO;    
      
    @Autowired
    private RoleDAO roleDAO;
    
    @Autowired
    private ServiceModeDAO serviceModeDAO;

    @Autowired
    private AppointmentEmailService appointmentEmailService;
    
      
    @Autowired
    private PersonManagerService personManagerService;
    
    @Autowired
    private SearchService searchService;
    
    @Autowired
    private LocationManagerService locationManageService;
    
    @Autowired
    private AppointmentService appointmentService;
    
    @Autowired
	private AppointmentDAO			appointmentDAO;
	
	@Resource
	private AvailabilityService availabilityService;
	
	@Autowired
	private TimeoffService timeoffService;
 
 
	
	@Override
	public List<Instructor> loadInstructorList(long locationId, Enabled enabled) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("InstructorService.loadInstructorList: start");
		}
		List<Instructor> list = new LinkedList<Instructor>();
		try {
			Criterion<Instructor, Instructor> instructorCriterion = InstructorCriterion.findByLocation(locationId, enabled);
			list = instructorDAO.search(instructorCriterion, DAOHelper.FETCH_PERSON | DAOHelper.FETCH_AVAILABILITY);
		} catch (RuntimeException e) {
			LOG.error("InstructorService.loadInstructorList: failure");
		}
		if(LOG.isDebugEnabled()) {
			LOG.debug("InstructorService.loadInstructorList: end");
		}
		return list;
	}
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public boolean checkInstructorByTime(long instructorId, String startDate,
			String endDate, String startTime, String endTime) {
		//for the records condition stored, only check the start date is enough
        if(!Pattern.matches("\\d{2}/\\d{2}/\\d{4}",startDate)){
            LOG.error("StartDate is invalid. the source is [{}]", startDate);
            return false;
        }
        if(!Pattern.matches("\\d{2}:\\d{2}",startTime)){
            LOG.error("StartTime is invalid. the source is [{}]", startTime);
            return false;
        }
        if(!Pattern.matches("\\d{2}:\\d{2}",endTime)){
            LOG.error("EndTime is invalid. the source is [{}]", endTime);
            return false;
        }
        DateTimeFormatter formatter = DateTimeFormat.forPattern("MM/dd/yyyy HH:mm");
        DateTime startDateTime = DateTime.parse(startDate + " " + startTime, formatter);
        DateTime endDateTime = DateTime.parse(startDate + " " + endTime, formatter);
        Criterion<Instructor, Boolean> criterion = InstructorCriterion.checkTimeAvailability(instructorId, startDateTime.toDate(), endDateTime.toDate());
        return instructorDAO.get(criterion);
	}

	//GCSS-670
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public boolean checkInstructorAvailabilityByTime(long instructorId, String startDate,
			String endDate, String startTime, String endTime) {
		return instructorDAO.getInstructorByTime(instructorId, startDate, startTime, endTime)==null?false:true;
	}
	
	@Override
	public List<InstructorInfoDTO> loadByActivityId(long activityId) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("ActivityService.loadActivitiesByService start");
		}
		List<Instructor> list = new LinkedList<Instructor>();
		List<InstructorInfoDTO> dtos = new LinkedList<InstructorInfoDTO>();
		try {
			Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByActivityId(activityId);
			list = instructorDAO.search(criterion, DAOHelper.FETCH_PERSON);
		} catch (Exception e) {
			LOG.error("Caught an {} from InstructorService.loadByActivityId", e);
		}
		if(LOG.isDebugEnabled()) {
			LOG.debug("Get InstructorList: {} from InstructorService.loadByActivityId", list);
		}
		if(null != list && !list.isEmpty()) {
			for(Instructor i : list) {
				InstructorInfoDTO dto = new InstructorInfoDTO();
				dto.setInstructorId(String.valueOf(i.getInstructorId()));
				String firstName = i.getPerson().getFirstName();
				String lastName = i.getPerson().getLastName();
				dto.setInstructorName(firstName + " " + lastName);
				dtos.add(dto);
			}
		}
		return dtos;
	}
	
	@Transactional
	@Override
	public List<Instructor> queryInstructors(long locationId) {
		
		Criterion<Instructor, Instructor> instructorCriterion = InstructorCriterion.findByLocation(locationId);
		List<Instructor> list = instructorDAO.search(instructorCriterion, DAOHelper.FETCH_PERSON|DAOHelper.FETCH_AVAILABILITY|DAOHelper.FETCH_MORE_ACTIVITIES);
		
		return list;
	}
	
	@Transactional
	@Override
	public Instructor getInstructor(Long instrctorId){
		
		return instructorDAO.get(instrctorId,DAOHelper.FETCH_MORE_ACTIVITIES|DAOHelper.FETCH_AVAILABILITY|DAOHelper.FETCH_PERSON);
	}
	
	@Transactional
	@Override
	public void updateInstructor(Instructor instructor,long personId,Enabled status) throws Exception{
		Person pt =new Person();
		pt.setPersonId(personId);
		try {
			instructorDAO.saveOrUpdateInstructor(instructor,pt);
		} catch (Exception e) {
				LOG.error("Caught exception:{} from Instructor update fail", e);
		} 
	}
	
	/**
	  * <p>Title: checkInstructorByAppointmentTime</p>
	  * <p>Description: </p>
	  * @param recurring
	  * @param instructorId
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @return
	  * @see com.guitarcenter.scheduler.service.InstructorService#checkInstructorByAppointmentTime(boolean, long, java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	  */
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public boolean checkInstructorByAppointmentTime(boolean recurring,
			long instructorId, String startDate, String endDate,
			String startTime, String endTime) {
		if(recurring && !startDate.equals(endDate)){
			return instructorDAO.checkInstructorByAppointmentRecurringTime(instructorId, startDate, endDate, startTime, endTime)==null?false:true;
		}
		return instructorDAO.checkInstructorByAppointmentTime(instructorId, startDate, startTime, endTime)==null?false:true;
	}
	
	@Override
	public List<Instructor> getInstructorListByIds(Long... ids) {
		Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByInstructorIds(ids);
		List<Instructor> list = instructorDAO.search(criterion, DAOHelper.FETCH_PERSON);
		return list;
	}
	
	@Transactional
	@Override
	public List<Instructor> queryInstructorsById(long instructorId) {
		Criterion<Instructor, Instructor> instructorCriterion = InstructorCriterion.findByInstructorId(instructorId);
		List<Instructor> list = instructorDAO.search(instructorCriterion, DAOHelper.FETCH_PERSON|DAOHelper.FETCH_AVAILABILITY|DAOHelper.FETCH_MORE_ACTIVITIES|DAOHelper.FETCH_LOCATION|DAOHelper.FETCH_LOCATION_PROFILE|DAOHelper.FETCH_SERVICE|DAOHelper.FETCH_MORE_SERVICES);
		
		return list;
	}
	
	
	@Transactional
	@Override
	public List<Instructor> getInstructorActivitiesByInstructorsId(long instructorId) {
		Criterion<Instructor, Instructor> instructorCriterion = InstructorCriterion.findByInstructorId(instructorId);
		List<Instructor> list = instructorDAO.search(instructorCriterion, DAOHelper.FETCH_PERSON|DAOHelper.FETCH_MORE_ACTIVITIES|DAOHelper.FETCH_LOCATION|DAOHelper.FETCH_LOCATION_PROFILE|DAOHelper.FETCH_SERVICE|DAOHelper.FETCH_MORE_SERVICES|DAOHelper.FETCH_SITE);
		
		return list;
	}
	
	@Transactional
	@Override
	public List<InstructorInfoDTO> loadByActivityAndLocation(long activityId, long locationId) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("query instructor by acticity:{} and locationId:{}", activityId, locationId);
		}
		List<InstructorInfoDTO> dtos = new LinkedList<InstructorInfoDTO>();
		List<Instructor> list = new LinkedList<Instructor>();
		try {
			Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByLocationIdAndActivityId(locationId, activityId, Enabled.Y);
			list = instructorDAO.search(criterion, DAOHelper.FETCH_PERSON);
		} catch (Exception e) {
			LOG.error("Caught exception:{} from loadByActivityAndLocation", e);
		}
		if(LOG.isDebugEnabled()) {
			LOG.debug("got instrcuctor list:{} from loadByActivityAndLocation", list);
		}
		if(null != list && !list.isEmpty()) {
			for(Instructor i : list) {
				InstructorInfoDTO dto = new InstructorInfoDTO();
				dto.setInstructorId(String.valueOf(i.getInstructorId()));
				String firstName = i.getPerson().getFirstName();
				String lastName = i.getPerson().getLastName();
				dto.setInstructorName(firstName + " " + lastName);
				dtos.add(dto);
			}
		}
		return dtos;
	}
    
    /**
     * Return a collection of Instructor records for the site id provided that
     * have a matching external id value.
     * 
     * Note: this is returning a collection simply because there is not a
     * database constraint on the external identifiers; it is possible that
     * multiple instructors *could* be updated from a single external record in
     * the future.
     * 
     * @param siteId identifier for the site that results must be restricted to
     * @param externalId String containing the external id to match
     * @return List of Instructor instances that match the external id in the site
     *         provided
     */
    @Transactional
    @Override
    public List<Instructor> findInstructorsByExternalId(long siteId, String externalId) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Searching for instructor with external id {} and site id {}",
                      externalId, siteId);
        }
        Criterion<Instructor, Instructor> criterion =
                InstructorCriterion.findByExternalId(siteId, externalId);
        List<Instructor> instructors = instructorDAO.search(criterion, DAOHelper.FETCH_PERSON);
        if (LOG.isDebugEnabled()) {
            LOG.debug("Returning instructor list {}", instructors);
        }
        return instructors;
    }
    
    /**
     * Create or updates an Scheduler Instructor record with values taken from the
     * supplied EmployeeDTO record. Any exceptions are passed up to caller for
     * decision and handling.
     * 
     * The method will retrieve an existing instructor record from persistent
     * storage that matches the external id of the update record.
     * 
     * @param update an EmployeeDTO instance containing values to update
     * @throws IOException 
     * @throws SolrServerException 
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    @Override
    public void updateFromExternal(EmployeeDTO update)
        throws SolrServerException, IOException
    {
        if (LOG.isDebugEnabled()) {
            LOG.debug("about to attempt an update for instructor {}", update);
        }
        if (update == null) {
            LOG.warn("a null instructor instance was passed to updateFromExternal");
            return;
        }
        
        if (StringUtils.isBlank(update.getSite())) {
            if (LOG.isInfoEnabled()) {
                LOG.info("updated instructor {} does not have a site string");
            }
            return;
        }
        /* Find the site that matches the external id provided by update record.
         * 
         * Note: sites are a key element of data separation, so throw an
         * exception if an exact match of 1 site is not received.
         */
        List<Site> sites = siteService.findSitesByExternalId(update.getSite());
        if (sites.size() > 1) {
            LOG.warn("updated instructor site external id {} matches too " +
                     "many sites; ignoring record", update.getSite());
            return;
        }
        if (sites.isEmpty()) {
            if (LOG.isInfoEnabled()) {
                LOG.info("updated instructor site external id {} does not match " +
                         "an existing site; ignoring record", update.getSite());
            }
            return;
        }
        Site updateSite = sites.get(0);
        
        /* Now try to find an existing Instructor record to update
         */
        if (StringUtils.isBlank(update.getExternalId())) {
            LOG.warn("updated instructor {} does not have an external id; " +
                     "ignoring record", update);
            return;
        }
        Instructor instructor = null;
        List<Instructor> existing =
            findInstructorsByExternalId(updateSite.getSiteId(),
                                      update.getExternalId());
        //GSSP-211 CHANGES
        boolean isPrimaryLocation = true;
              
        switch(existing.size()) {
            case 1:
                /* Update an existing Employee from the DTO
                 */
                instructor = existing.get(0);
                if (LOG.isDebugEnabled()) {
                    LOG.debug("found a record; updating {}", instructor);
                }
                updateInstructorFromDTO(instructor, update);
                personDAO.update(instructor.getPerson(),
                                 personManagerService.getSystemUpdatePerson());
                instructorDAO.update(instructor,
                                   personManagerService.getSystemUpdatePerson());
                break;
                
            case 0:
                /* Need to create a new Instructor record and populate with DTO
                 * 
                 * Note that instructor requires a Location, so do not persist
                 * the instructor if there is not a matching location.
                 */
                instructor = createInstructorFromDTO(update, updateSite);
                if (instructor.getLocation() != null) {
                    personDAO.save(instructor.getPerson(),
                                   personManagerService.getSystemUpdatePerson());
                    availabilityDAO.save(instructor.getAvailability(),
                                         personManagerService.getSystemUpdatePerson());
                    instructorDAO.save(instructor,
                                       personManagerService.getSystemUpdatePerson());
                    
                    //Changes made for GSSP-199
                    Role role = new Role();                  
                    role.setRoleName(AppConstants.INSTRUCTOR_STRING);                     
                    if (StringUtils.isNotBlank(role.getRoleName())) {
                        role = roleDAO.get(role);
                    } else {
                        role = null;
                    }
                    
                    if (role != null) {
                        PersonRole personRole = new PersonRole();
                        personRole.setLocation(instructor.getLocation());
                        personRole.setPerson(instructor.getPerson());
                        personRole.setRole(role);
                        personRole.setSite(updateSite);
                        personRoleDAO.save(personRole,
                                           personManagerService.getSystemUpdatePerson());
                    }
                    //End of Changes made for GSSP-199
                    
                } else {
                    if (LOG.isInfoEnabled()) {
                        LOG.info("new instructor {} does not have a location; " +
                                 "skipping save", instructor);
                    }
                }
                break;
                
                //GSSP-211 CHANGES
                default:   
                	
                	if(existing.size() > 1)
                	{	
	                	for(Instructor locationInstructor : existing)
	                	{
	                		LOG.warn("Processing"  + locationInstructor.getInstructorId());
	                		if(locationInstructor.getExternalSource().equalsIgnoreCase("EDW"))
	                		{
	                			instructor = locationInstructor;
	                			isPrimaryLocation = true;
	                		}
	                		else if(locationInstructor.getExternalSource().equalsIgnoreCase("GCSS"))
	                		{
	                			isPrimaryLocation = false;
	                		}
	                		                		
	                		 updateInstructorFromDTO(locationInstructor, update);
	                		 if(isPrimaryLocation)
	                		 {
	                			 locationInstructor.setExternalSource("EDW");
	                		 }
	                		 else
	                		 {
	                			 locationInstructor.setExternalSource("GCSS");
	                			 
	                		 }
	                		 
	                		
	                         personDAO.update(locationInstructor.getPerson(),
	                                          personManagerService.getSystemUpdatePerson());
	                         
	                         
	                         instructorDAO.update(locationInstructor,
	                                            personManagerService.getSystemUpdatePerson());
	                	}    
                	}
                	break;
          /*  default:
                 Found too many Instructor records - DO NOT UPDATE!
                 
                LOG.warn("updated instructor external id {} matched too many " +
                         "existing records; ignoring update request",
                         update.getExternalId());
                return;*/
        }
        /* Update was successful so make sure search engine has the latest
         * data on record.
         */
       //EDW Job Issue FIX
        if (null != instructor && null != instructor.getInstructorId()) {
            searchService.updateRecord(instructor);
        }else{
        	 LOG.warn("a null instructor instance was passed to updateFromExternal");
        }
        if (LOG.isDebugEnabled()) {
            LOG.debug("update completed for instructor {}", update);
        }
    }

    /**
     * Creates a new Employee record ready for persistence.
     * 
     * @param update EmployeeDTO containing the data to update
     * @param updateSite a Site instance to populate
     * @return Employee instance ready for persistence
     */
    
    private Instructor createInstructorFromDTO(EmployeeDTO update, Site updateSite) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("about to create a new Instructor instance from {} and {}",
                      update, updateSite);
        }
        
        Instructor instructor = new Instructor();
        instructor.setSite(updateSite);
        instructor.setExternalId(update.getExternalId());
        //TODO::GSSP-211 changes 1.set the location in new 
        instructor.setLocation(getLocationFromDTO(update, updateSite));
        instructor.setPerson(new Person());
        //Changes made to GSSP-231
        //instructor.setEnabled(Enabled.N);
        instructor.setAvailability(new Availability());
        instructor.getAvailability().setSite(updateSite);
        updateInstructorFromDTO(instructor, update);
        if (LOG.isDebugEnabled()) {
            LOG.debug("returning new employee {}", instructor);
        }
        return instructor;
    }
    /**
     * Updates an Instructor with the values found in update.
     * 
     * @param instructor Instructor instance that will be persisted
     * @param update EmployeeDTO containing the updated values
     */
    private void updateInstructorFromDTO(Instructor instructor, EmployeeDTO update) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("about to update {} from {}", instructor, update);
        }
        if (StringUtils.isNotBlank(update.getFirstName())) {
            instructor.getPerson().setFirstName(update.getFirstName());
        }
        if (StringUtils.isNotBlank(update.getLastName())) {
            instructor.getPerson().setLastName(update.getLastName());
        }
        if (StringUtils.isNotBlank(update.getEmail())) {
            instructor.getPerson().setEmail(update.getEmail());
        }
        
        if (StringUtils.isNotBlank(update.getExternalSource())) {
            instructor.setExternalSource(update.getExternalSource());
        }
        if (StringUtils.isNotBlank(update.getStatus())) {
            instructor.setStatus(update.getStatus());
        }
        if (StringUtils.isNotBlank(update.getAuthId())) {
            instructor.getPerson().setAuthId(update.getAuthId());
        }
        if (LOG.isDebugEnabled()) {
            LOG.debug("updated instructor {}", instructor);
        }   
    }
   
   
    /**
     * Returns a matching Location based on studio identifier, if found.
     * 
     * @param update EmployeeDTO to examine
     * @param updateSite Site of the employee
     * @return an instance of Location or null
     */
    private Location getLocationFromDTO(EmployeeDTO update, Site updateSite) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("finding location for {}", update);
        }
        Location location = null;
        List<Location> locations =
            locationManageService.findByExternalId(updateSite.getSiteId(),
                                                   update.getStudio());
        switch (locations.size()) {
            case 1: 
                /* Found a single match; use it for the new Employee
                 */
                location = locations.get(0);
                break;
                
            case 0:
                /* No location found; generate a warning message
                 */
                if (LOG.isInfoEnabled()) {
                    LOG.info("did not find a location for " + update);
                }
                break;
                
            default:
                /* Too many locations; generate a warning message
                 */
                LOG.warn("too many locations found for " + update);
        }
        if (LOG.isDebugEnabled()) {
            LOG.debug("returning {}", location);
        }
        return location;
    }
	
	@Override
	public List<InstructorInfoDTO> loadInstructorDTOs(long locationId, Enabled enabled) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("query enabled instructor list by locationId:{}", locationId);
		}
		List<InstructorInfoDTO> dtos = new LinkedList<InstructorInfoDTO>();
		List<Instructor> list = this.loadInstructorList(locationId, enabled);
		if(LOG.isDebugEnabled()) {
			LOG.debug("get enabled instructor list {}", list);
		}
		if(null != list && !list.isEmpty()) {
			for(Instructor i : list) {
				InstructorInfoDTO dto = new InstructorInfoDTO();
				dto.setInstructorId(String.valueOf(i.getInstructorId()));
				String firstName = i.getPerson().getFirstName();
				String lastName = i.getPerson().getLastName();
				dto.setInstructorName(firstName + " " + lastName);
				dto.setAvailability(i.getAvailability());//instructor avialibility
				dtos.add(dto);
			}
		}
		return dtos;
	}
	
	@Transactional
	@Override
	public List<InstructorInfoDTO> findByLocationIdAndActivityIds(
			long locationId, Long... activityIds) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("Find Instructor List by locationId {} & activityIds {} from LocationManageService.findByLocationIdAndActivityIds", locationId, activityIds);
		}
		List<InstructorInfoDTO> dtos = new LinkedList<InstructorInfoDTO>();
		List<Instructor> list;
		try {
			Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByLocationIdAndActivityIds(locationId, activityIds);
			list = instructorDAO.search(criterion, DAOHelper.FETCH_PERSON);
			if(LOG.isDebugEnabled()) {
				LOG.debug("Find Instructor List {} from LocationManageService.findByLocationIdAndActivityIds", list);
			}
			if(null != list && !list.isEmpty()) {
				for(Instructor i : list) {
					dtos.add(new InstructorInfoDTO(String.valueOf(i.getInstructorId()), this.buildInstructorName(i)));
				}
			}
		} catch (Exception e) {
			LOG.error("Caught an exception when loading instructor list by locationId {} & activityIds {}", locationId, activityIds);
		}
		
		return dtos;
	}
	
/*
	@Transactional
	@Override
	public List<InstructorInfoDTO> findByLocationIdAndActivityIdsInsAVL(
			long locationId, Long... activityIds) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("Find Instructor List by locationId {} & activityIds {} from LocationManageService.findByLocationIdAndActivityIds", locationId, activityIds);
		}
		List<InstructorInfoDTO> dtos = new LinkedList<InstructorInfoDTO>();
		List<Instructor> list;
		try {
			Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByLocationIdAndActivityIdsInsAVL(locationId, activityIds);
			list = instructorDAO.search(criterion, DAOHelper.FETCH_PERSON);
			if(LOG.isDebugEnabled()) {
				LOG.debug("Find Instructor List {} from LocationManageService.findByLocationIdAndActivityIds", list);
			}
			if(null != list && !list.isEmpty()) {
				for(Instructor i : list) {
					dtos.add(new InstructorInfoDTO(String.valueOf(i.getInstructorId()), this.buildInstructorName(i)));
				}
			}
		} catch (Exception e) {
			LOG.error("Caught an exception when loading instructor list by locationId {} & activityIds {}", locationId, activityIds);
		}
		
		return dtos;
	}*/
	
	@Transactional
	@Override
	public List<InstructorInfoDTO> findByInsAVLById(long instructorId) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("Find Instructor List by locationId {} & activityIds {} from LocationManageService.findByLocationIdAndActivityIds", instructorId, instructorId);
		}
		List<InstructorInfoDTO> dtos = new LinkedList<InstructorInfoDTO>();
		List<Instructor> list;
		try {
			Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByLocationIdAndActivityIdsInsAVL(instructorId);
			list = instructorDAO.search(criterion, DAOHelper.FETCH_PERSON);
			if(LOG.isDebugEnabled()) {
				LOG.debug("Find Instructor List {} from LocationManageService.findByLocationIdAndActivityIds", list);
			}
			if(null != list && !list.isEmpty()) {
				for(Instructor i : list) {
					dtos.add(new InstructorInfoDTO(String.valueOf(i.getInstructorId()), this.buildInstructorName(i)));
				}
			}
		} catch (Exception e) {
			LOG.error("Caught an exception when loading instructor list by locationId {} & activityIds {}", instructorId, instructorId);
		}
		
		return dtos;
	}
	
	private String buildInstructorName(Instructor i) {
		String instructorName = "";
		if(null != i) {
			String firstName = i.getPerson().getFirstName();
			String lastName = i.getPerson().getLastName();
			if(StringUtils.isNotBlank(firstName)) {
				instructorName += firstName + " ";
			}
			if(StringUtils.isNotBlank(lastName)) {
				instructorName += lastName;
			}
		}
		return instructorName;
	}

	@Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
	public boolean checkUpdateInstructorByAppointmentTime(boolean recurring,
			long instructorId, String startDate, String endDate,
			String startTime, String endTime, String excludeAppointmentIdParam) {
		if(recurring && !startDate.equals(endDate)){
			return instructorDAO.checkUpdateInstructorByAppointmentRecurringTime(instructorId, startDate, endDate, startTime, endTime, excludeAppointmentIdParam);
		}
		return instructorDAO.checkUpdateInstructorByAppointmentTime(instructorId, startDate, startTime, endTime, excludeAppointmentIdParam);
	}

	/**
	  * <p>Title: checkInstructorByProfileActivityId</p>
	  * <p>Description: </p>
	  * @param instructorId
	  * @param activityId
	  * @param profileId
	  * @return
	  * @see com.guitarcenter.scheduler.service.InstructorService#checkInstructorByProfileActivityId(long, long, long)
	  */
	@Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
	public boolean checkInstructorByProfileActivityId(long instructorId,
			long activityId, long profileId) {
		return instructorDAO.checkInstructorByProfileActivityId(instructorId, activityId, profileId);
	}
	
	@Transactional
	@Override
	public List<InstructorInfoDTO> loadInstructorListByProfileIdAndActivityIdAndDateTime(
			long profileId, long serviceId, long activityId, Long instructorId, Date startTime,
			Date endTime, Long appId) {
		List<Instructor> list = null;
		List<InstructorInfoDTO> dtos = new LinkedList<InstructorInfoDTO>();
		try {
			Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByProfileIdAndActivityIdAndDateTime(profileId, activityId, startTime, endTime, appId);
			list = instructorDAO.search(criterion);
		} catch (Exception e) {
			LOG.error("Caught exception when loading instructor list ByProfileIdAndActivityIdAndDateTime", e);
		}

        if (list == null) {
            list = Collections.emptyList();
        }

        for (Instructor i : list) {
            dtos.add(new InstructorInfoDTO(String.valueOf(i.getInstructorId()), this.buildInstructorName(i)));
        }

		//For GCSS-499,keep the origion instructor as the head element of the instructor list if it's valid
		if(null != appId) {
			Appointment appt = appointmentService.getAppointment(appId);
			if(!dtos.isEmpty()) {
				for(int i=0; i<dtos.size(); i++) {
					InstructorInfoDTO dto = dtos.get(i);
					if(null != dto.getInstructorId() && 0 != dto.getInstructorId().length() && null != appt.getInstructor()) {
						if(appt.getInstructor().getInstructorId().equals(Long.parseLong(dto.getInstructorId()))) {
							Collections.swap(dtos, 0, i);
							break;
						}
					}
				}
			}
		}
		return dtos;
	}
	
	/**
	 * Helper method to validate if the availabity of an instructor is available for using in some day
	 * For GCSS-488
	 * 
	 * @param dayOfWeek
	 * @param a
	 * @return
	 */
	private boolean isInstructorAvailybityAvailable(int dayOfWeek, Availability a) {
		boolean isInstructorAvailabilityAvailable = true;
		switch (dayOfWeek) {
		case 1:
			if(isAvailabilityTimeNull(a.getMondayStartTime(), a.getMondayEndTime())) isInstructorAvailabilityAvailable = false;
			break;
		case 2:
			if(isAvailabilityTimeNull(a.getTuesdayStartTime(), a.getTuesdayEndTime())) isInstructorAvailabilityAvailable = false;
			break;
		case 3:
			if(isAvailabilityTimeNull(a.getWednesdayStartTime(), a.getWednesdayEndTime())) isInstructorAvailabilityAvailable = false;
			break;
		case 4:
			if(isAvailabilityTimeNull(a.getThursdayStartTime(), a.getThursdayEndTime())) isInstructorAvailabilityAvailable = false;
			break;
		case 5:
			if(isAvailabilityTimeNull(a.getFridayStartTime(), a.getFridayEndTime())) isInstructorAvailabilityAvailable = false;
			break;
		case 6:
			if(isAvailabilityTimeNull(a.getSaturdayStartTime(), a.getSaturdayEndTime())) isInstructorAvailabilityAvailable = false;
			break;
		default:
			if(isAvailabilityTimeNull(a.getSundayStartTime(), a.getSundayEndTime())) isInstructorAvailabilityAvailable = false;
			break;
		}
		return isInstructorAvailabilityAvailable;
	}
	
	/**
	 * Helper method to return true if any(startTime or endTime) of the availability is null otherwise resturn false
	 * For GCSS-488 
	 * 
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	private boolean isAvailabilityTimeNull(Date startTime, Date endTime) {
		if(null == startTime || null == endTime) {
			return true;
		}
		return false;
	}

	/**
	 * For GCSS-590
	  * <p>Title: checkInstructorByAppointmentTime</p>
	  * <p>Description: </p>
	  * @param instructorId
	  * @param startDate
	  * @param endDate
	  * @param startTime
	  * @param endTime
	  * @return
	  * @see com.guitarcenter.scheduler.service.InstructorService#checkInstructorByAppointmentTime(long, java.lang.String, java.lang.String, java.lang.String, java.lang.String)
	  */
	@Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
	public boolean checkInstructorByAppointmentTime(long instructorId,
			String startDate, String endDate, String startTime, String endTime) {
		return instructorDAO.checkInstructorByAppointmentTime(instructorId, startDate, endDate, startTime, endTime)==null?false:true;
	}
	/**
	 * For GCSS-715
	  * <p>Title: checkAvailabilityAndOntime</p>
	  * <p>Description: </p>
	  * @param instructorId
	  * @param pStartDate
	  * @param pEndDate
	  * @return
	  * @see com.guitarcenter.scheduler.service.InstructorService#checkAvailabilityAndOntime(long,java.util.Date, java.util.Date)
	  */
	@Override
	public boolean checkAvailabilityAndOntime(long instructorId,Date pStartDate,Date pEndDate) {
		Boolean result;
		Criterion<Instructor, Boolean> criterion = InstructorCriterion.checkAppointmentDateTimeByAvailabilityAndOntime(instructorId,pStartDate, pEndDate);
		result = instructorDAO.get(criterion);
		return result;
	}
	
	//Changes made for GSSP-199
	
	@Override
	/**
     * Return a list of Instructor instances that match the supplied person id.
     * 
     * @param personId a person identifier to use
     * @return a List of Instructor instances that match
     */
    public List<Instructor> findByPersonId(long personId) {
        if (LOG.isDebugEnabled()) {
        	LOG.debug("finding instructors for {}", personId);
        }
        Criterion<Instructor, Instructor> criterion = InstructorCriterion.findByPersonId(personId);
        List<Instructor> instructors = instructorDAO.search(criterion, DAOHelper.FETCH_PERSON);
        if (LOG.isDebugEnabled()) {
        	LOG.debug("Returning employee list {}", instructors);
        }
        return instructors;
    }

	//GSSP-275 Implemented method to add the person id in the personpersonal detail table
	public void isInstructorExist(InstructorPersonalDetailsDTO update) throws SolrServerException, IOException{

        if (LOG.isDebugEnabled()) {
            LOG.debug("about to attempt an update for instructor {}", update);
        }
        if (update == null) {
            LOG.warn("a null instructor instance was passed to updateFromExternal");
            return;
        }
        
        /* Now try to find an existing Instructor record to update
         */
        
        Instructor instructor = null;
        List<Instructor> existing =
        		findInstructor(update.getExternalId());
        if (existing.size()== 0) {
            LOG.warn("instructor {} does not have an external id; " +
                     "ignoring record", update);
            return;
        }
     
            /* Update an existing Employee from the DTO
             */
        else if(update.getEmail() != null && !update.getEmail().isEmpty()){
            instructor = existing.get(0);
            
         
            if(instructor.getPerson()!=null)
            {
                //Start of update person email in person detail table
            List<PersonPersonalDetails> pList=instructorPersonalDetailsService.getListByPersonId(instructor.getPerson().getPersonId());
            //object creation
            
            	if(pList.size()==0 && update.getEmail()!=null)
            		{            			
            		instructorPersonalDetailsService.createPersonPersonalDetails(update, instructor,personManagerService.getSystemUpdatePerson());
                      		}
            	else if(pList.size()==1 && update.getEmail()!=null)
            		{            			
            		instructorPersonalDetailsService.updatePersonPersonalDetails(update, instructor,personManagerService.getSystemUpdatePerson());
            		}
            }
        
    
            
            if (LOG.isDebugEnabled()) {
                LOG.debug("found a record; updating {}", instructor);
            }
                   
        if (LOG.isDebugEnabled()) {
            LOG.debug("update completed for personpersonaldetailtable{}", update);
        }
    
        }
	}
	// changes made for GSSP-275
	 @Transactional
	    @Override
	    public List<Instructor> findInstructor( String externalId) {
	        if (LOG.isDebugEnabled()) {
	            LOG.debug("Searching for instructor with external id {} and site id {}",
	                      externalId);
	        }
	        Criterion<Instructor, Instructor> criterion =
	                InstructorCriterion.findinstructorByExternal( externalId);
	        List<Instructor> instructors = instructorDAO.search(criterion, DAOHelper.FETCH_PERSON);
	        if (LOG.isDebugEnabled()) {
	            LOG.debug("Returning instructor list {}", instructors);
	        }
	        return instructors;
	    }
	//Changes  for GSSP-363 
	 @Override
	    public List<InstructorLessonLinkDTO> getInstructorLessonViewList(List<String> personList,Date startDate,Date endDate){
		 	List<InstructorLessonLinkDTO> instDTOList = new ArrayList<InstructorLessonLinkDTO>();
		 	
		 	List<InstructorLessonLinkDTO> instructorLessonLinkDTOList = appointmentService.getInstructorLessonViewList(personList, startDate, endDate);
		 	
		 	
			//GSSP-363 Get max of ZoomMeetingID groupby AppointmentId
		 	List<InstructorLessonLinkDTO> result = instructorLessonLinkDTOList.stream()
                    .collect(Collectors.groupingBy(InstructorLessonLinkDTO::getAppointmentId, 
                            Collectors.maxBy(Comparator.comparing(InstructorLessonLinkDTO::getZoomMeetingID))))
                    .values().stream()
                    .map(Optional::get)
                    .collect(Collectors.toList());

		 	//Sorted by Start Date
		 	List<InstructorLessonLinkDTO> sortedZoomMeetingID = result.stream()
		 			  .sorted(Comparator.comparing(InstructorLessonLinkDTO::getStartDate))
		 			  .collect(Collectors.toList());

		 	for(InstructorLessonLinkDTO iltDto :sortedZoomMeetingID){
                InstructorLessonLinkDTO  instDtoObj = new  InstructorLessonLinkDTO();
               instDtoObj.setZoomMeetingID(iltDto.getZoomMeetingID());
               instDtoObj.setUtcEndTime(iltDto.getUtcEndTime());
             
            	instDtoObj.setAppointmentId(iltDto.getAppointmentId());
            	instDtoObj.setCanceled(iltDto.getCanceled());
            	instDtoObj.setDuration(this.buildDuration(Float.valueOf(iltDto.getDuration().trim()).floatValue()) );
            	instDtoObj.setActivityName(iltDto.getActivityName());
            	instDtoObj.setInstructorId(iltDto.getInstructorId());
            	instDtoObj.setInstructorFirstName(iltDto.getInstructorFirstName());
            	instDtoObj.setInstructorLastName(iltDto.getInstructorLastName());
            	instDtoObj.setCustomerExternalId(iltDto.getCustomerExternalId());
            	instDtoObj.setCustomerId(iltDto.getCustomerId());
            	instDtoObj.setCustomerFirstName(iltDto.getCustomerFirstName());
            	instDtoObj.setCustomerLastName(iltDto.getCustomerLastName());
            	instDtoObj.setInstructorExternalId(iltDto.getInstructorExternalId());
            	instDtoObj.setLocationExternalId(iltDto.getLocationExternalId());
            	instDtoObj.setLocationName(iltDto.getLocationName());
            	instDtoObj.setZoomMeetingUrl(iltDto.getZoomMeetingUrl());
            	instDtoObj.setShowStatus(iltDto.getShowStatus());
            	instDtoObj.setStudentNote(iltDto.getStudentNote());
            	instDtoObj.setLocationTimezone((iltDto.getLocationTimezone()));
            	instDtoObj.setServiceId(iltDto.getServiceId());
            	instDtoObj.setRoomNumberId(iltDto.getRoomNumberId());
            	instDtoObj.setAppointmentRoomId(iltDto.getAppointmentRoomId());
            	instDtoObj.setRoomNumberName(iltDto.getRoomNumberName());
            	//instDtoObj.setLessonStatus(iltDto.getLessonStatus());
            	//instDtoObj.setNextLessonStatus(iltDto.getNextLessonStatus());
            	instDtoObj.setAssignment(iltDto.getAssignment());
            	instDtoObj.setPracticeNotes(iltDto.getPracticeNotes());
            	instDtoObj.setRemarks(iltDto.getRemarks());
            	//instDtoObj.setRate(iltDto.getRate());
              /*  if("".equals(iltDto.getStudentNote())){
                    instDtoObj.setEnabledStudentNote(false);   
                }else{
                    instDtoObj.setEnabledStudentNote(true);
                }
                if("".equals(iltDto.getLessonStatus())){
                    instDtoObj.setEnabledStudentNote(false);   
                }else{
                    instDtoObj.setEnabledStudentNote(true);
                }*/
            	if("".equals(iltDto.getAssignment())){
                    instDtoObj.setEnabledStudentNote(false);   
                }else{
                    instDtoObj.setEnabledStudentNote(true);
                }
            	if("".equals(iltDto.getShowStatus())){
            		instDtoObj.setEnabledStatus(false);	
            	}else{
            		instDtoObj.setEnabledStatus(true);
            	}
            	instDtoObj.setComments(iltDto.getComments());
            	if("".equals(iltDto.getComments())){
            		instDtoObj.setEnabledComments(false);	
            	}else{
            		instDtoObj.setEnabledComments(true);
            	}
            	instDtoObj.setStartDate(iltDto.getStartDate());
                instDtoObj.setStartTime(iltDto.getStartTime());
            	instDtoObj.setEndTime(iltDto.getEndTime());
            	instDtoObj.setCustomerEmail(iltDto.getCustomerEmail());
            	instDtoObj.setCustomerPhone(iltDto.getCustomerPhone());
            	instDtoObj.setJoinURL(iltDto.getJoinURL());
            	instDtoObj.setUtcStartTime(iltDto.getUtcStartTime());
            	if(null != iltDto.getCustomerPhone()){
            		instDtoObj.setFormattedcustomerPhone(SystemUtil.phoneFormate(iltDto.getCustomerPhone()));
            	}
            	
            	  //If no zoom_link LaunchStatus is NA and enabled Launch will be false.
                //Else if utc time is grater than current time will setLaunchStatus Finished and setEnabledLaunch false ,
                //else if utc time is less than current utc time  setLaunchStatus Launch and setEnabledLaunch is true
            	if(this.isStartDateBeforeCurrentDate(instDtoObj.getEndTime(), instDtoObj.getLocationTimezone())){
             	   instDtoObj.setEnabledLaunch(false);
                    instDtoObj.setLaunchStatus("finished");     	            	   
                }else if (this.isValidZoomLinkTimeStamp(instDtoObj.getEndTime(), instDtoObj.getLocationTimezone(),instDtoObj.getUtcEndTime())){
                	 instDtoObj.setEnabledLaunch(false);
                     instDtoObj.setLaunchStatus("online");  
                }
                else if( null == iltDto.getZoomMeetingID() || "".equals(iltDto.getZoomMeetingID() )){
                    if("20".equals(iltDto.getServiceId())){
                 	   instDtoObj.setEnabledLaunch(false);
                        instDtoObj.setLaunchStatus("online");  
                    }else{
                 	   instDtoObj.setEnabledLaunch(false);
                        instDtoObj.setLaunchStatus("instore");
                    }
                }else if(null != instDtoObj.getUtcEndTime() && null != instDtoObj.getUtcEndTime() && isFutureUTCTime(instDtoObj.getUtcEndTime(),instDtoObj.getUtcStartTime())){
                    instDtoObj.setEnabledLaunch(true);
                    instDtoObj.setLaunchStatus("online");
                }else if(null != instDtoObj.getUtcEndTime() && isFutureUTCEndTime(instDtoObj.getUtcEndTime())){
                    instDtoObj.setEnabledLaunch(false);
                    instDtoObj.setLaunchStatus("online");
                 }else{
                    instDtoObj.setEnabledLaunch(false);
                    instDtoObj.setLaunchStatus("finished");
                }
            	
            	//#####Send Remainder Email button#############
            	if(!instDtoObj.getServiceId().equals("20") 
            			&& this.isStartDateAfterCurrentDate(instDtoObj.getEndTime(), instDtoObj.getLocationTimezone())){
            		instDtoObj.setEnabledSendRemainder(true);
            	}else if (instDtoObj.getServiceId().equals("20") &&  
            			this.isStartDateAfterCurrentDate(instDtoObj.getEndTime(), instDtoObj.getLocationTimezone()) 
        				&& this.isValidZoomLinkTimeStamp1(instDtoObj.getEndTime(), instDtoObj.getLocationTimezone(),instDtoObj.getUtcEndTime())){  
            		instDtoObj.setEnabledSendRemainder(true);
            	}else {
            		instDtoObj.setEnabledSendRemainder(false);
            	}
            

            	instDTOList.add(instDtoObj);
			}
		 	 if (LOG.isDebugEnabled()) {
		            LOG.debug("Returning instructorLessonLinkDTOList list {}", instructorLessonLinkDTOList);
		        }

	    	return instDTOList;
	    }
	 
	 
	 private  boolean isValidZoomLinkTimeStamp(Date dt, String tz, Date utcStartTime) {
		 boolean flag=false;
		 String DATE_FORMAT = "dd-M-yyyy hh:mm:ss a";
		 
		 if(null != dt && null!= tz && null != utcStartTime){

			 	//Timezone ZonedDateTime
				      DateFormat formatter = new SimpleDateFormat(DATE_FORMAT);
				      String formattedDate = formatter.format(dt);
			          LocalDateTime ldt = LocalDateTime.parse(formattedDate, java.time.format.DateTimeFormatter.ofPattern(DATE_FORMAT));
			          ZoneId timeZoneZodeId = ZoneId.of(tz);
			          ZonedDateTime timeZonedDateTime = ldt.atZone(timeZoneZodeId);
			      
			    //UTC ZonedDateTime
			        String utcformattedDate = formatter.format(utcStartTime);
				    LocalDateTime utcldt = LocalDateTime.parse(utcformattedDate, java.time.format.DateTimeFormatter.ofPattern(DATE_FORMAT));
				    ZoneId utcZodeId = ZoneId.of("UTC");
				    ZonedDateTime utcDateTime = utcldt.atZone(utcZodeId);
				//TimeZone to UTC Conversation.    
				    ZonedDateTime  timeZonedDateTimetoUTC =  timeZonedDateTime.withZoneSameInstant(utcZodeId);
				    
				    if(!timeZonedDateTimetoUTC.isEqual(utcDateTime)){
				    	flag = true;
				    }
		 }						
     	return flag;
	 }
	 
	 
	 private  boolean isValidZoomLinkTimeStamp1(Date dt, String tz, Date utcStartTime) {
		 boolean flag=false;
		 String DATE_FORMAT = "dd-M-yyyy hh:mm:ss a";
		 
		 if(null != dt && null!= tz && null != utcStartTime){

			 	//Timezone ZonedDateTime
				      DateFormat formatter = new SimpleDateFormat(DATE_FORMAT);
				      String formattedDate = formatter.format(dt);
			          LocalDateTime ldt = LocalDateTime.parse(formattedDate, java.time.format.DateTimeFormatter.ofPattern(DATE_FORMAT));
			          ZoneId timeZoneZodeId = ZoneId.of(tz);
			          ZonedDateTime timeZonedDateTime = ldt.atZone(timeZoneZodeId);
			      
			    //UTC ZonedDateTime
			        String utcformattedDate = formatter.format(utcStartTime);
				    LocalDateTime utcldt = LocalDateTime.parse(utcformattedDate, java.time.format.DateTimeFormatter.ofPattern(DATE_FORMAT));
				    ZoneId utcZodeId = ZoneId.of("UTC");
				    ZonedDateTime utcDateTime = utcldt.atZone(utcZodeId);
				//TimeZone to UTC Conversation.    
				    ZonedDateTime  timeZonedDateTimetoUTC =  timeZonedDateTime.withZoneSameInstant(utcZodeId);
				    
				    if(timeZonedDateTimetoUTC.isEqual(utcDateTime)){
				    	flag = true;
				    }
		 }						
     	return flag;
	 }
	 

	private boolean isStartDateAfterCurrentDate(Date dt,String tz){
		boolean flag=false;
		 String DATE_FORMAT = "dd-M-yyyy hh:mm:ss a";
		 if(null != dt && null!= tz){

			 	//Timezone ZonedDateTime
				      DateFormat formatter = new SimpleDateFormat(DATE_FORMAT);
				      String formattedDate = formatter.format(dt);
			          LocalDateTime ldt = LocalDateTime.parse(formattedDate, java.time.format.DateTimeFormatter.ofPattern(DATE_FORMAT));
			          ZoneId timeZoneZodeId = ZoneId.of(tz);
			          ZonedDateTime timeZonedDateTime = ldt.atZone(timeZoneZodeId);
			          
			          java.time.Instant ins = java.time.Instant.now(); 
			          ZoneId z = ZoneId.of(tz);
			          ZonedDateTime currentUTCTime = ins.atZone( z );
				    
			          if(timeZonedDateTime.isAfter((currentUTCTime))){
							 flag= true;
					}
		 }						
		return flag;
	}
	 
	 private boolean isStartDateBeforeCurrentDate(Date dt,String tz){
		 boolean flag=false;
		 String DATE_FORMAT = "dd-M-yyyy hh:mm:ss a";
		 if(null != dt && null!= tz){

			 	//Timezone ZonedDateTime
				      DateFormat formatter = new SimpleDateFormat(DATE_FORMAT);
				      String formattedDate = formatter.format(dt);
			          LocalDateTime ldt = LocalDateTime.parse(formattedDate, java.time.format.DateTimeFormatter.ofPattern(DATE_FORMAT));
			          ZoneId timeZoneZodeId = ZoneId.of(tz);
			          ZonedDateTime timeZonedDateTime = ldt.atZone(timeZoneZodeId);
			          
			          java.time.Instant ins = java.time.Instant.now(); 
			          ZoneId z = ZoneId.of(tz);
			          ZonedDateTime currentUTCTime = ins.atZone( z );
				    
			          if(timeZonedDateTime.isBefore((currentUTCTime))){
							 flag= true;
					}
		 }						
		return flag;
	 }
	 	private boolean isFutureUTCTime(Date utcEndTime,Date utcStartTime) {
 
	 		  Calendar cal = Calendar.getInstance();
	 		  cal.setTime(utcEndTime);
	 		  cal.add(Calendar.MINUTE, 0);
	 		  utcEndTime = cal.getTime();

	 		  Calendar calsrt = Calendar.getInstance();
	 		  calsrt.setTime(utcStartTime);
	 		  calsrt.add(Calendar.MINUTE, 0);
	 		  utcStartTime = calsrt.getTime(); 
	 		  boolean flag= true;
	 		try {
	  
				 Date currentUTCTime = this.getCurrentUtcTime();
	 
				 Long diff = utcEndTime.getTime()-utcStartTime.getTime();
				 Long diffMinutes = diff / (60 * 1000);				 
				 diffMinutes= diffMinutes+15;
				 Long enddiff =  utcEndTime.getTime()-currentUTCTime.getTime();
				 Long endDiffMinutes = enddiff / (60 * 1000);
				 
				 if(endDiffMinutes<=diffMinutes && endDiffMinutes>0) {
					 return true;
				 }
				 if(endDiffMinutes<0) {
					 return false;
				 }
				 if(endDiffMinutes>diffMinutes) {
					 return false;
				 }
 
			} catch (ParseException e) {
				LOG.info("Error on isFutureUTCTime enabled Launch:"+e.getMessage());
			}
	 		 return flag;
	 	}
	 	private boolean isFutureUTCEndTime(Date utcEndTime) {
	 		  Calendar cal = Calendar.getInstance();
	 		  cal.setTime(utcEndTime);
	 		  cal.add(Calendar.MINUTE, 0);
	 		  utcEndTime = cal.getTime();
 
	 		try {
	  
				 Date currentUTCTime = this.getCurrentUtcTime();
				if(utcEndTime.after(currentUTCTime)){
					  return true;
				}
				 
			} catch (ParseException e) {
				LOG.info("Error on isFutureUTCTime disabled Launch:"+e.getMessage());
			}
	 		 return false;
	 	}
	 	//#########################################################
	 	
	 	private   Date getCurrentUtcTime() throws ParseException  {
	 	    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.S");
	 	    simpleDateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
	 	    SimpleDateFormat localDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.S");
	 	    return localDateFormat.parse( simpleDateFormat.format(new Date()) );
	 	}
	 	
		//---GSSP-363-----
	 	@Transactional
		@Override
		public void updateInstructorSchedule(InstructorAppointmentStatus instructorAppointmentStatus,long personId) throws Exception{
	 	 
				 instructorAppointmentStatusDAO.saveOrUpdateInstructorAppointmentStatus(instructorAppointmentStatus);
 		}
	 	
	 	@Transactional
		@Override
		public void updateInstructorSchedule(InstructorAppointmentStatus instructorAppointmentStatus) throws Exception{
	 	 
				 instructorAppointmentStatusDAO.saveOrUpdateInstructorAppointmentStatus(instructorAppointmentStatus);
 		}


    @Override
    public boolean sendEmailReminderToCustomer(InstructorLessonLinkDTO dto) {
        try {
            if (dto.getServiceId().equalsIgnoreCase("20")) {
                appointmentEmailService.sendEmailForCustomerReminder(
                        Long.parseLong(dto.getAppointmentId()),
                        Long.parseLong(dto.getCustomerId()),
                        dto.getJoinURL());
            } else {
                appointmentEmailService.sendEmailForCustomerReminderInStore(dto) ;
            }
        }
        catch (Exception e){
                LOG.error("email send failed",e);
                return false;
        }
        return true;

    }
    
    @Override
    public boolean sendEmailFromInstructorToCustomer(InstructorLessonLinkDTO dto) {
        try {
 
                appointmentEmailService.sendEmailFromInstructorToCustomer(dto) ;
             
        }
        catch (Exception e){
                LOG.error("email send failed",e);
                return false;
        }
        return true;

    }

    @Override
    public ListInstructorsSchedulerDTO getInstructorsSchedulerViewList(Location location, Person person,Date queryDate) {
        List<String> personList = new ArrayList<String>();
        personList.add(person.getPersonId()+"");
        String queryDateStr =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(queryDate);
        String queryDateStr1 =  new SimpleDateFormat("MM/dd/yyyy").format(queryDate);
        
        //Date queryEndDate = DateTimeUtil.getEndTimeOfTargetDate(queryDate);
        //Date queryStartDate = DateTimeUtil.getStartTimeOfTargetDate(queryDate);
        DateTime endDateTime = DateTimeUtil.handleendDateTime(queryDateStr);
        DateTime startDateTime = DateTimeUtil.handleStartDateTime(queryDateStr);
        Date queryEndDate = endDateTime.toDate();
        Date queryStartDate = startDateTime.toDate();

      	List<InstructorLessonLinkDTO> instructorLessonViewList = this.getInstructorLessonViewList(personList, queryStartDate, queryEndDate);
//        List<InstructorLessonLinkDTO> instructorLessonViewList = new ArrayList<>();
        ListInstructorsSchedulerDTO listInstructorsDto = null;
        if(null!= instructorLessonViewList) {
            listInstructorsDto = new ListInstructorsSchedulerDTO();
            listInstructorsDto.setInstructorFirstName(person.getFirstName());
            listInstructorsDto.setInstructorLastName(person.getLastName());
            listInstructorsDto.setInstructorAuthId(person.getAuthId());
            listInstructorsDto.setQueryDate(queryDateStr1);
            listInstructorsDto.setDayStartEndTime(getStartAndEndTimes(instructorLessonViewList));
            listInstructorsDto.setInstructorLessonLinkDTO(instructorLessonViewList);
            if(!instructorLessonViewList.isEmpty() && instructorLessonViewList.get(0) != null ){
            	 listInstructorsDto.setTimeZone(TimeoffUtil.getTimeZoneConverstion(instructorLessonViewList.get(0).getLocationTimezone()));
            }
           
            
        }
        return listInstructorsDto;
    }

    private String buildTimeStr(Date date) {
	         String result = new DateTime(date).toString(DateTimeFormat.forPattern("hh:mm a"));
	         if ("11:59 PM".equals(result)) {
	             result = "12:00 AM";
	         }
	         return result;
	     }
	 	 
	 	 private String buildDuration(float minutes) {//For GCSS-520,to support the half hour in creating app,change the paramater minutes to float
	         String duration = "";
	         if (minutes < 60) {
	             duration = minutes + " minutes";
	         } else if (60 == minutes) {
	             duration = "1 Hour";
	         } else {
	             duration = (minutes / 60) + " Hrs";
	         }
	         duration = duration.replace(".0", "");
	         return duration;
	     }
	 	 
	 	public static String getStartAndEndTimes(List<InstructorLessonLinkDTO> instructorLessonViewList) throws RuntimeException {
			 
			String result="";
			DateFormat sdf = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN);
			List<Date> inslistdes = new ArrayList<Date>();
			DateFormat inFormat3 = new SimpleDateFormat( "hh:mm a");
			try {
				
				 for(InstructorLessonLinkDTO ins:instructorLessonViewList){ 
					 Date  insStart = inFormat3.parse(ins.getStartTime());
					 String time = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_TIME2, Locale.getDefault()). format(ins.getEndTime());
					 Date insEnd =  inFormat3.parse(time);
					 inslistdes.add(insStart);
					 inslistdes.add(insEnd);
				 }

				   Date maxDate = inslistdes.stream().max(Date::compareTo).get();
				   Date minDate = inslistdes.stream().min(Date::compareTo).get();
				 String endTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_TIME2, Locale.getDefault()). format(maxDate);
				 String startTime = new SimpleDateFormat(DateTimeUtil.TIME_OFF_DATE_TIME2, Locale.getDefault()). format(minDate);
				 result = startTime+" - "+endTime;
			} catch (Exception e) {
				LOG.info("Error on Date Time:"+e.getMessage());
			}
			
			
			return result;
		}
	 //------------GSSP - 363 -----------------------Starts--------------------
	 	
	 	//---GSSP Instructor Mode update changes	
	 	@Transactional
		@Override
		public List<ServiceMode> getServiceModeList() throws Exception{
	 		return serviceModeDAO.getServiceModeList();
	 	}
	 	
	 	@Transactional
		@Override
		public ServiceMode getServiceMode(Long ServiceModeID) throws Exception{
	 		return serviceModeDAO.getServiceMode(ServiceModeID);
	 	}
	 	
	 	@Transactional
		@Override
		public Map<Long,ServiceMode> getInstructorModeList(List<Long> instructorIdList) {
			
			Criterion<Instructor,  Map<Long, ServiceMode>> criterion = InstructorCriterion.findInstructorServiveModeMap(instructorIdList);
			Map<Long,ServiceMode> mp = instructorDAO.get(criterion);	
			return mp;
		}
	 	
	  public InstructorAVLServiceResponseDTO getInstructorAvailability(InstructorAVLServiceDTO  instructorServiceDTO){
 
	   LocationProfileInfoDTO locationProfileDetails =  null;
	  
	   InstructorAVLServiceResponseDTO result = new InstructorAVLServiceResponseDTO();

	  try
	  
	  {
		  locationProfileDetails = new LocationProfileInfoDTO();

		  locationProfileDetails.setInstructorId(Long.valueOf(instructorServiceDTO.getInstructorId())); 
 
			 List<Instructor> instructors1 = queryInstructorsById(locationProfileDetails.getInstructorId());
			 
			//---------------------------Added Instructor Mode Changes Starts--------------------------------------------------
			 	List<Long> collect = new ArrayList<Long>();
				collect.add(locationProfileDetails.getInstructorId());
				 Map<Long, ServiceMode> getInstructorModeList =  getInstructorModeList(collect);
				 ServiceMode sm= null;
				 if(getInstructorModeList.containsKey(locationProfileDetails.getInstructorId())){
						 sm = getInstructorModeList.get(locationProfileDetails.getInstructorId());
				 }
				 Set<String> insModeSet = new LinkedHashSet<>(50);
				 if(sm != null){
	 
				 if("In-Store".equals(sm.getServiceModeName()))
				 {   insModeSet.add("\"In-Store\"");
				 result.setInstructorMode(insModeSet.toString());
				 }
				 if("Online".equals(sm.getServiceModeName())){
					 insModeSet.add("\"Online\"");
					 result.setInstructorMode(insModeSet.toString());
				 }
		 
				 if("Both".equals(sm.getServiceModeName())){
					 insModeSet.add("\"In-Store\"");
					 insModeSet.add("\"Online\""); 
					 insModeSet.add("\"Both\"");
					 result.setInstructorMode(insModeSet.toString());
				 }
				 }else{
					 insModeSet.add("\"In-Store\"");
					 insModeSet.add("\"Online\""); 
					 insModeSet.add("\"Both\"");
					 result.setInstructorMode(insModeSet.toString()); 
				 }
			
				
			//---------------------------Added Instructor Mode Changes Ends--------------------------------------------------	
				 Set<String> actSet = new HashSet<>(100);
		            Set<String> fullactSet = new HashSet<>(100);
		            if("Active".equals(instructorServiceDTO.getInstructorStatus())){

		 

		            ArrayList<ActivityApiDTO> al = new ArrayList<ActivityApiDTO>();
		            Long actvities[]  =   new Long[] {7L,8L,9L,10L,11L,12L,13L,17L,18L,81L,82L,80L,240L,241L,242L,243L,244L,245L,180L,181L,182L,183L,184L,185L,186L,187L,188L,189L,190L,191L,192L,193L,220L,221L,222L,223L,224L,225L,226L,227L,228L,229L,230L,246L,247L,248L,249L,250L,251L,252L,253L,280L};
		            
		            List<Long> activities = new ArrayList<Long>();
		            activities = Arrays.asList(actvities);
		            
		             
		            if(null!= instructors1 && !instructors1.isEmpty()) {
		            	boolean instoretrail = false;
		            for(Activity act : instructors1.get(0).getActivities()) {
		            	//OLL-3732::If instore-trail lesson add the instoretraillesson flag as true.
		            	if (Long.valueOf(140).equals(act.getActivityId())) {          	   
		            	    instoretrail = true;
		            	   // result.setInstoretrail(true);
		            	}

		            	
		                if(activities.contains(act.getActivityId())) {
		                        if(act.getMaximumAttendees()!=0){
		                        ActivityApiDTO actDto = new ActivityApiDTO();
		                        
		        
		                        String actname = act.getActivityName();
		                        String activityName = act.getActivityName();
		                        fullactSet.add("\""+activityName+"\"");
		                        String target1 = "In-Store ";
		                        String target2 = "Online ";
		                        String target3 = "Lesson";
		                        if(actname.contains(target1)){
		                            activityName = actname.replace(target1, "");    
		                        }
		                        if(actname.contains(target2)){
		                            activityName = activityName.replace(target2, "");    
		                        }
		                        if(actname.contains(target3)){
		                            activityName = activityName.replace(target3, "");    
		                        }
		                        activityName = activityName.trim();
		                        actDto.setActivityName(activityName.trim());
		         
		                        //actSet.add(activityName);
		                        actSet.add("\""+activityName+"\"");
		                        al.add(actDto);
		                        }
		            }
		            }
		            if(instoretrail) {
		            	result.setInstoretrail(true);
		            }else {
		            	result.setInstoretrail(false);
		            }
		            
		            }
		            }

		 

		            for(Instructor inst : instructors1) {
		                 
		                  locationProfileDetails.setProfileID(inst.getLocation().getLocationProfile().getProfileId());
		 
		                  locationProfileDetails.setLocationID(inst.getLocation().getLocationId());
		                  
		                    result.setName(inst.getPerson().getFirstName()+" "+inst.getPerson().getLastName());
		                    result.setStoreNumber(inst.getLocation().getExternalId()); 
		                    result.setFirstname(inst.getPerson().getFirstName());
		                    result.setLastname(inst.getPerson().getLastName()); 
		                    //Updated Instructor Auth_id to External_id
		                    result.setId(inst.getExternalId());
		                    result.setEmail(inst.getPerson().getEmail());
		                    result.setPhoneNumber(inst.getPerson().getPhone());
		                    //Added Current Timestamp.
		                    Date now = new Date();
		                    DateFormat df = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
		                    result.setLastupdateddate(df.format(now));
		         
		                    
		                    result.setStoreaddress1(inst.getLocation().getAddress1());
		                    result.setStoreaddress2(inst.getLocation().getAddress2());
		                    result.setStorestate(inst.getLocation().getState());
		                    result.setStorecity(inst.getLocation().getCity());
		                    result.setStorezip(inst.getLocation().getZip());
		                    
		            }
		         
		            //result.setInstruments(al.toString());
		            result.setInstruments(actSet.toString());
		            result.setActivityFull(fullactSet.toString());
		            result.setInstructorStatus(instructorServiceDTO.getInstructorStatus());
		            result.setInstructorId(instructorServiceDTO.getInstructorId());
		 
		            if("Active".equals(instructorServiceDTO.getInstructorStatus())){
		              setInstructorsFreeSlots(result, locationProfileDetails, instructorServiceDTO);
		            }
		              
		          
		      }
		      catch(Exception e)
		      {
		          e.printStackTrace();
		          LOG.error("Error during fetching of data in INew nstructor Availability Web Service", e);
		      }                         
		    
		     
		      return result;
		                    

		 

		         }
	 	
	 	@Transactional
		@Override
	 	public InstructorActivitiesAPIDTO getInstructorActivity(InstructorACTServiceDTO  instructorServiceDTO){
	
	 	  LocationProfileInfoDTO locationProfileDetails =  null;
	 	  List<Instructor> instructors1 = null;
	 	  InstructorActivitiesAPIDTO instructorActivitiesAPIDTO = null;
	 	 
	 	
	 	  try
	 	  {
	 		  locationProfileDetails = new LocationProfileInfoDTO();
	 		  locationProfileDetails.setInstructorId(Long.valueOf(instructorServiceDTO.getInstructorId())); 
	  		  instructors1 = getInstructorActivitiesByInstructorsId(locationProfileDetails.getInstructorId());
	  		  
	  		  //Prepare the result DTO
	  		 instructorActivitiesAPIDTO = prepareResultInstructorActivities(instructors1);
	  		  
	 				 				 		  
	 	  }
	 	  catch(Exception e)
	 	  {
	 		  e.printStackTrace();
	 		  LOG.error("Error during fetching of data in INew nstructor Availability Web Service", e);
	 	  }		
	 	  
	 	  return instructorActivitiesAPIDTO;
	 	 }
	 		
	 	
	 	
	 	private InstructorActivitiesAPIDTO prepareResultInstructorActivities(List<Instructor> instructors) {
	 		InstructorActivitiesAPIDTO result = null;
	 		
	 		for(Instructor inst : instructors) {
	 			result = new InstructorActivitiesAPIDTO();
	 			
	 			
				  
				    result.setName(inst.getPerson().getFirstName()+" "+inst.getPerson().getLastName());
				    result.setStoreNumber(inst.getLocation().getExternalId()); 
					result.setFirstname(inst.getPerson().getFirstName());
					result.setLastname(inst.getPerson().getLastName());
					result.setId(inst.getPerson().getAuthId());
					result.setEmail(inst.getPerson().getEmail());
					result.setPhoneNumber(inst.getPerson().getPhone());
					//Added Current Timestamp.
					Date now = new Date();
				    DateFormat df = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
					result.setLastupdateddate(df.format(now));
		 
					
					result.setStoreaddress1(inst.getLocation().getAddress1());
					result.setStoreaddress2(inst.getLocation().getAddress2());
					result.setStorestate(inst.getLocation().getState());
					result.setStorecity(inst.getLocation().getCity());
					result.setStorezip(inst.getLocation().getZip());
					Set<Activity> setAct = new HashSet<Activity>();
					setAct = inst.getActivities();
					Set<ActivityDTO> setActLst = new HashSet<ActivityDTO>();
				
					for(Activity act:setAct) {
						ActivityDTO actDto = new ActivityDTO();
						actDto.setActivityId(act.getActivityId());
						actDto.setSite_id(act.getSite().getSiteId()+"");
						actDto.setService_id(act.getService().getServiceId()+"");
						actDto.setActivityName(act.getActivityName());
						actDto.setMinimumAttendees(act.getMinimumAttendees());
						actDto.setMaximumAttendees(act.getMaximumAttendees());
						actDto.setMinimumDuration(act.getMinimumDuration());
						actDto.setMaximumDuration(act.getMaximumDuration());
						actDto.setRequiresInstructor(act.getRequiresInstructor());
						actDto.setEnabled(act.getEnabled());
						actDto.setExternalId(act.getExternalId());
						 
						setActLst.add(actDto);
					}
					result.setActivities(setActLst);
					//---------------------------Added Instructor Mode Changes Starts--------------------------------------------------
				 	List<Long> collect = new ArrayList<Long>();
					collect.add(inst.getInstructorId());
					 Map<Long, ServiceMode> getInstructorModeList =  getInstructorModeList(collect);
					 ServiceMode sm= null;
					 if(getInstructorModeList.containsKey(inst.getInstructorId())){
							 sm = getInstructorModeList.get(inst.getInstructorId());
					 }
					 Set<String> insModeSet = new LinkedHashSet<>(50);
					 if(sm != null){
		 
					 if("In-Store".equals(sm.getServiceModeName()))
					 {   insModeSet.add("\"In-Store\"");
					 result.setServiceMode(insModeSet.toString());
					 }
					 if("Online".equals(sm.getServiceModeName())){
						 insModeSet.add("\"Online\"");
						 result.setServiceMode(insModeSet.toString());
					 }
			 
					 if("Both".equals(sm.getServiceModeName())){
						 insModeSet.add("\"In-Store\"");
						 insModeSet.add("\"Online\""); 
						 insModeSet.add("\"Both\"");
						 result.setServiceMode(insModeSet.toString());
					 }
					 }else{
						 insModeSet.add("\"In-Store\"");
						 insModeSet.add("\"Online\""); 
						 insModeSet.add("\"Both\"");
						 result.setServiceMode(insModeSet.toString()); 
					 }
				
					
				//---------------------------Added Instructor Mode Changes Ends--------------------------------------------------
				//	result.setServiceMode((inst.getServiceMode()));
	 		}
			return result;

	 	}

	 public void  setInstructorsFreeSlotsforSelfService(InstructorAVLServiceResponseDTO result,LocationProfileInfoDTO locationProfileDetails,
										  InstructorAVLServiceDTO  instructorServiceDTO) throws Exception
	{

		LocalTime profileTimeoffStartTime= null;
		LocalTime profileTimeoffEndTime= null;
		int dayOfWeek =0;

		long profileId = locationProfileDetails.getProfileID();
		Availability instructorAvailability = null;

		Map<String, List<InstructorAvailableHoursDTO>>  instructorDayAvailabilityMap = null;

		List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList  = null ,timeOffHoursDTOList = null ;

		DateTime startTime = null ,endTime =null;

		List<InstructorAvailableHoursDTO> freeSlotsList = null;

		List<Long> instructorsList = new ArrayList<Long>();

		List<Times>  dividedSlotsList = null, appointmentDateList = null;;

		List<String> availabilityList = new ArrayList<String>();
		List<String> availabilityDayList = new ArrayList<String>();;

		Map<String,List<Times>> slotsByDateMap = new TreeMap<String,List<Times>>();

		Map<String,List<InstructorAvailableHoursDTO>> timeOffMap = new HashMap<String,List<InstructorAvailableHoursDTO>>();

		List<InstructorAvailableHoursDTO>  timeOffList = null;

		InstructorAvailableHoursDTO timeOffHoursDTO = null;

		LocalTime timeOffStartTime = null,  timeOffEndTime = null;
		Map<String,List<String>> nonAvailableSlots = null;

		boolean getAvailability = false;

		String instructorName ="";

		DateTime timeOffStartDate  = null, timeOffEndDate = null;

		Map<String,Boolean>  dateTimeRoomAvalabilityMap  = new HashMap<String,Boolean>();
		Lessons instructorLessonsByDay  =  null;

		List<Lessons> instructorLessonsByDayList = new ArrayList<Lessons>();

		DateFormat df1 = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH);

		Long duration = 30l;

		if(duration % 30  !=0)
			duration = duration + (30 - (duration % 30) );

		//-- GSSP-321-Prevent customers from scheduling a lesson online, when the stores are not offering lessons
		//----Changes for GSSP-321 for get the profileTimeOffDetails from DB --------
		Date startDateInput =null;
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		String dateInString = instructorServiceDTO.getStartDate();
		try
		{
			startDateInput = formatter.parse(dateInString);

		} catch (ParseException e) {
			LOG.error("Error during on Date format in Instructor Availability Web Service", e);
		}
		Map<String, ProfileTimeOffDTO> profileTimeOffMap = timeoffService.profileTimeOffDetailsInsAVL(profileId,startDateInput);

		//Getting the instructor list based on location type and activity type
		Long[] activityIds = new Long[]{ locationProfileDetails.getActivityID()};
		List<InstructorInfoDTO> iList = findByInsAVLById(new Long(instructorServiceDTO.getInstructorId()));

		for(InstructorInfoDTO instructorInfoDTO : iList)
		{

			instructorsList.add(new Long(instructorInfoDTO.getInstructorId()));
			if(null != instructorInfoDTO.getInstructorName())
				instructorName = instructorInfoDTO.getInstructorName();
		}
		// instructorsList.add(new Long(instructorServiceDTO.getInstructorId()));

		long[] instructorArray = instructorsList.stream().mapToLong(i->i).toArray();

		Long[] InstructorArray  =  ArrayUtils.toObject (instructorArray);

		DateTime availaibilityStartDay = new DateTime(instructorServiceDTO.getStartDate());

		nonAvailableSlots = new  HashMap<String,List<String>>();

		if(null != locationProfileDetails.getSeriesEndDate() ) {
		}

		Date availaibilityWeekDay =  null;

		int days = 30;

		getAvailability = true;
		availaibilityWeekDay =  availaibilityStartDay.plusDays(days+1).toDate();
		availaibilityWeekDay = DateTimeUtil.handleEndDate(df1.format(availaibilityWeekDay));


		if(InstructorArray.length > 0)
		{
			//Logic for getting the instructor appointment for the week
			Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> timeOffriterion =
					AppointmentCriterion.getTimeOffForInstructorsInsAVL(InstructorArray,availaibilityStartDay.toDate(),availaibilityWeekDay);

			timeOffMap  = appointmentDAO.get(timeOffriterion);


		}

		if(getAvailability)
		{

			//Block for iterating through all instructors and finding their free slots
			for(InstructorInfoDTO instructorInfoDTO : iList)

			{

				if(null != instructorInfoDTO.getInstructorName()) {
				}

				Long instructorID = new Long(instructorInfoDTO.getInstructorId());


				//Logic for getting instructor Availability
				Criterion<Availability, Availability> criterion = AvailabilityCriterion.getByInstructorIdInsAVL(instructorID);
				instructorAvailability= availabilityDAO.get(criterion);

				//Logic for getting the instructor appointment for the week

				Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> instructorCriterion =
						AppointmentCriterion.findInstructorAvailableHoursInsAVL(instructorID,  availaibilityStartDay.toDate(),availaibilityWeekDay);

				instructorDayAvailabilityMap  = appointmentDAO.get(instructorCriterion);

				String parsingDate = "";

				DateTime currentDay = null;

				//Block for get details of each day

				for(int i=0;i<=days;i++)
				{

					instructorAvailableHoursDTOList  = null;

					currentDay =  availaibilityStartDay.plusDays(i);

					parsingDate = currentDay.toString(DateTimeUtil.DATE_PATTERN);

					Boolean isTimeOffDay = false;

					if (timeOffMap.containsKey(instructorInfoDTO.getInstructorId())) {

						timeOffList = timeOffMap.get(instructorInfoDTO.getInstructorId());

						timeOffHoursDTOList = new ArrayList<InstructorAvailableHoursDTO>();


						for(InstructorAvailableHoursDTO timeOffAvailabilityDTO : timeOffList)
						{

							timeOffStartDate = timeOffAvailabilityDTO.getAppointmentStartDate();

							timeOffEndDate  = timeOffAvailabilityDTO.getAppointmentEndDate();

							if(currentDay.isEqual(timeOffStartDate )&& currentDay.isEqual(timeOffEndDate))
							{
								timeOffStartTime = timeOffAvailabilityDTO.getAppointmentStartTime().plusMinutes(1);
								timeOffEndTime   = timeOffAvailabilityDTO.getAppointmentEndTime();
							}

							else
							{
								if(currentDay.isEqual(timeOffStartDate ) && currentDay.isBefore(timeOffEndDate))
								{
									timeOffStartTime = timeOffAvailabilityDTO.getAppointmentStartTime().plusMinutes(1);
									timeOffEndTime   = new LocalTime ("23:59");

								}

								else
								{
									if (currentDay.isAfter(timeOffStartDate) && currentDay.isEqual(timeOffEndDate))
									{
										timeOffStartTime = new LocalTime ("00:01");
										timeOffEndTime  = timeOffAvailabilityDTO.getAppointmentEndTime();;
									}

									else
									{
										if (currentDay.isAfter(timeOffStartDate) && currentDay.isBefore(timeOffEndDate))
										{
											isTimeOffDay = true;
											break;
										}

									}
								}

							}

							if(null != timeOffStartTime &&  null != timeOffEndTime)
							{
								timeOffHoursDTO = new InstructorAvailableHoursDTO(timeOffStartTime,timeOffEndTime);
								timeOffHoursDTOList.add(timeOffHoursDTO);

							}

							timeOffStartTime = null;
							timeOffEndTime = null;


						}

					}


					else
					{
						timeOffHoursDTOList = null;
					}



					if(!isTimeOffDay)
					{

						if(instructorDayAvailabilityMap.containsKey(parsingDate))
						{
							instructorAvailableHoursDTOList = instructorDayAvailabilityMap.get(parsingDate);

						}

						if(null != instructorAvailableHoursDTOList  && instructorAvailableHoursDTOList.size() > 0 )
						{
							if(null != timeOffHoursDTOList &&  timeOffHoursDTOList.size() > 0)
								instructorAvailableHoursDTOList.addAll(timeOffHoursDTOList);

						}
						else
						{
							if(null != timeOffHoursDTOList &&  timeOffHoursDTOList.size() > 0)
								instructorAvailableHoursDTOList = timeOffHoursDTOList;


						}


						dayOfWeek = currentDay.getDayOfWeek();


						if(null !=  instructorAvailability.getStartTimeByDayOfWeek(dayOfWeek) && null !=    instructorAvailability.getEndTimeByDayOfWeek(dayOfWeek))
						{
							startTime = new DateTime(instructorAvailability.getStartTimeByDayOfWeek(dayOfWeek)).minuteOfDay().roundFloorCopy();;

							endTime   = new DateTime(instructorAvailability.getEndTimeByDayOfWeek(dayOfWeek)).minuteOfDay().roundFloorCopy();;

							if(null != startTime && null != endTime)
							{


								//----Changes for GSSP-321 for get profileTimeoffStartTime profileTimeoffEndTime value for free slots
								profileTimeoffStartTime=null;//for clear the object on iteration
								profileTimeoffEndTime=null;
								SimpleDateFormat getHourMin = new SimpleDateFormat("HH:mm");
								if(profileTimeOffMap.get(currentDay.toString("yyyy-MM-dd")) != null)
								{
									ProfileTimeOffDTO profileTimeOffDate =(ProfileTimeOffDTO)profileTimeOffMap.get(currentDay.toString("yyyy-MM-dd"));
									if(profileTimeOffDate !=null)
									{
										String startTimeHrs =  getHourMin.format(profileTimeOffDate.getStartTime());
										String endTimeHrs =  getHourMin.format(profileTimeOffDate.getEndTime());
										profileTimeoffStartTime = new LocalTime(startTimeHrs);
										profileTimeoffEndTime =   new LocalTime(endTimeHrs);
									}
								}

								//----Changes for GSSP-321 for call getAvailableSlotsOnProfileTimeOff only on leave days
								if(profileTimeoffStartTime != null && profileTimeoffEndTime!= null  ){
									freeSlotsList = WebServiceUtil.getAvailableSlotsOnProfileTimeOffInsAVL(startTime, endTime, instructorAvailableHoursDTOList,profileTimeoffStartTime,profileTimeoffEndTime);
								}else{
									freeSlotsList = WebServiceUtil.getAvailableSlotsInsAVL(startTime, endTime, instructorAvailableHoursDTOList);
								}

								Date dateVal = getDateFromString(parsingDate);
								String dayStr = dateVal.getDay()+"";

								List<OnLineAvailableDTO>  onlineAvlList = null;
								List<InstoreAvailableDTO>  instoreAvlList = null;
								List<OnLineInstoreAvailableDTO>  alllist = new ArrayList<OnLineInstoreAvailableDTO>();

								onlineAvlList = onlineAvailabilityService.getOnlineAvlFormatByInstructorId(instructorID);

								for(OnLineAvailableDTO onls: onlineAvlList){
									if(dayStr.equals(onls.getWeekDay())){
										OnLineInstoreAvailableDTO allDto= new OnLineInstoreAvailableDTO();
										allDto.setFromTime(onls.getFromTime());
										allDto.setToTime(onls.getToTime());
										allDto.setServiceMode("Online");
										alllist.add(allDto);
									}
								}

								instoreAvlList = instoreAvailabilityService.getInstoreAvlFormatByInstructorId(instructorID);
								for(InstoreAvailableDTO onls: instoreAvlList){
									if(dayStr.equals(onls.getWeekDay())){
										OnLineInstoreAvailableDTO allDto= new OnLineInstoreAvailableDTO();
										allDto.setFromTime(onls.getFromTime());
										allDto.setToTime(onls.getToTime());
										allDto.setServiceMode("Instore");
										alllist.add(allDto);
									}

								}
								Collections.sort(alllist);
								alllist.stream().map(s -> s.getFromTime());

								//##########################################
								boolean flag=false;
								dividedSlotsList = new ArrayList<Times>();
								List<OnLineAvailableDTO> ln = new ArrayList<OnLineAvailableDTO>();
								String availabilityslot ="";
								if(freeSlotsList != null){
									for(InstructorAvailableHoursDTO fl:freeSlotsList){

										String  startTime1 = fl.getAppointmentStartTime().toString(DateTimeUtil.HOURS_SEC_PATTERN);
										String  endTime1 = fl.getAppointmentEndTime().toString(DateTimeUtil.HOURS_SEC_PATTERN);

										String  startTime2 = fl.getAppointmentStartTime().toString(DateTimeUtil.HOURS_PATTERN);
										String  endTime2 = fl.getAppointmentEndTime().toString(DateTimeUtil.HOURS_PATTERN);

										OnLineAvailableDTO onl = new OnLineAvailableDTO();
										onl.setFromTime(startTime2);
										onl.setToTime(endTime2);
										ln.add(onl);

										int diffInMinutes = Minutes.minutesBetween(fl.getAppointmentStartTime(), fl.getAppointmentEndTime()).getMinutes();
										char quotes ='"';

										Times  freeSlot = new Times(WebServiceUtil.formatTimeTo12HrInsAVL(startTime1),WebServiceUtil.formatTimeTo12HrInsAVL(endTime1),result.getName(),profileId);


									}
									if(null != freeSlotsList && freeSlotsList.size() > 0)
									{

										dividedSlotsList = divideSlotsforSelfService(dateTimeRoomAvalabilityMap,currentDay,freeSlotsList,profileId,duration.intValue(),
												instructorID,activityIds[0],11l,instructorName,nonAvailableSlots);
									}
									//~~~~~~~~~~~~~~~~~~~~~~
									String sm ="";

									for(Times tm:dividedSlotsList){
										sm ="";
										for(OnLineInstoreAvailableDTO onls: alllist){

											flag= checkSlotUnderInstructorAvlty(tm.getStartTime(),tm.getEndTime(),onls.getFromTime(),onls.getToTime());

											if(flag){
												if("Instore".equals(onls.getServiceMode())){
													sm ="#INSTORE";
												}
												else if("Online".equals(onls.getServiceMode())){
													sm ="#ONLINE";
												}else  sm ="#BOTH";
											}
											if(flag)break;
										}
										char quotes ='"';
										if("".equals(sm))sm ="#BOTH";

										//changeFormateOfDate
										String startDate = changeFormateOfDate(parsingDate);
										String endDateStr = null;
										if( (endDateStr==null || "".equals(endDateStr.trim()))){
											endDateStr = CalendarUtil.getDefaultEndDate(startDate,3);
										}
										boolean checkInstructorByAppointmentTime = validationService.checkInstructorByAppointmentTime(true,instructorID, startDate, endDateStr, CalendarUtil.plusFormatTime(tm.getStartTime(), 1), CalendarUtil.plusFormatTime(tm.getEndTime(), -1));
										if(!checkInstructorByAppointmentTime){
											//recur = "#RN";
										}else {

											availabilityslot = quotes+parsingDate+"T"+tm.getStartTime()+";"+parsingDate+"T"+tm.getEndTime()+sm+quotes;
										}

										if(null != availabilityslot && !"".equals(availabilityslot)) {
											availabilityDayList.add(availabilityslot);
										}

									}
									//~~~~~~~~~~~~~~~~~~~~~~

									if(!slotsByDateMap.containsKey(parsingDate))
									{
										List<Times> list = new ArrayList<Times>();
										slotsByDateMap.put(parsingDate, list);
									}

									appointmentDateList = slotsByDateMap.get(parsingDate);
									appointmentDateList.addAll(dividedSlotsList);
								}

							}

						}

					}
					availabilityList.addAll(availabilityDayList);
					availabilityDayList.clear();

				}

			}

			try {
				for(String slotDate : slotsByDateMap.keySet())
				{

					appointmentDateList = slotsByDateMap.get(slotDate);
					//Added for LES-149
					appointmentDateList.sort(Times.startTimeasLTMComparator);//Java 8 comparator
					instructorLessonsByDay  = new Lessons(slotDate,appointmentDateList);
					instructorLessonsByDayList.add(instructorLessonsByDay);


				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			result.setAvailability(availabilityList.toString());
			if(null != instructorLessonsByDayList && instructorLessonsByDayList.size() > 0)
			{
				result.setLessons(instructorLessonsByDayList);
			}

		}


	}

		private void  setInstructorsFreeSlots(InstructorAVLServiceResponseDTO result,LocationProfileInfoDTO locationProfileDetails,
				  InstructorAVLServiceDTO  instructorServiceDTO) throws Exception
		  {			 
 
			  LocalTime profileTimeoffStartTime= null;
			  LocalTime profileTimeoffEndTime= null;
			  int dayOfWeek =0;			  		
			  
			  long profileId = locationProfileDetails.getProfileID();
			  Availability instructorAvailability = null;
			  
			  Map<String, List<InstructorAvailableHoursDTO>>  instructorDayAvailabilityMap = null;
			  
			  List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList  = null ,timeOffHoursDTOList = null ;
			  
			  DateTime startTime = null ,endTime =null;
			  
			  List<InstructorAvailableHoursDTO> freeSlotsList = null; 
			  
			  List<Long> instructorsList = new ArrayList<Long>();
			  
			  List<Times>  dividedSlotsList = null, appointmentDateList = null;;
			  
			  List<String> availabilityList = new ArrayList<String>();
			  List<String> availabilityDayList = new ArrayList<String>();;
			  
			  Map<String,List<Times>> slotsByDateMap = new TreeMap<String,List<Times>>();
			  
			  Map<String,List<InstructorAvailableHoursDTO>> timeOffMap = new HashMap<String,List<InstructorAvailableHoursDTO>>();
			  
			  List<InstructorAvailableHoursDTO>  timeOffList = null;
			  
			  InstructorAvailableHoursDTO timeOffHoursDTO = null;
			  
			  LocalTime timeOffStartTime = null,  timeOffEndTime = null;
			  Map<String,List<String>> nonAvailableSlots = null;
			  
			  boolean getAvailability = false;
			  
			  String instructorName ="";
			  
			  DateTime timeOffStartDate  = null, timeOffEndDate = null;
			  
			  Map<String,Boolean>  dateTimeRoomAvalabilityMap  = new HashMap<String,Boolean>();
			  Lessons instructorLessonsByDay  =  null;
			   
			  List<Lessons> instructorLessonsByDayList = new ArrayList<Lessons>();						  		
			  
			  DateFormat df1 = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH);			  	
			  
			   Long duration = 30l;
			   
			  if(duration % 30  !=0)
				  duration = duration + (30 - (duration % 30) ); 
	 
			  //-- GSSP-321-Prevent customers from scheduling a lesson online, when the stores are not offering lessons
			  //----Changes for GSSP-321 for get the profileTimeOffDetails from DB --------
			  	Date startDateInput =null;
			  	SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		        String dateInString = instructorServiceDTO.getStartDate();
		        try 
		        {
		        	startDateInput = formatter.parse(dateInString);

		        } catch (ParseException e) {
		        	 LOG.error("Error during on Date format in Instructor Availability Web Service", e);
		        }
		        Map<String, ProfileTimeOffDTO> profileTimeOffMap = timeoffService.profileTimeOffDetailsInsAVL(profileId,startDateInput);
	
			  //Getting the instructor list based on location type and activity type	 
			  Long[] activityIds = new Long[]{ locationProfileDetails.getActivityID()};
			  List<InstructorInfoDTO> iList = findByInsAVLById(new Long(instructorServiceDTO.getInstructorId()));	
 
			  for(InstructorInfoDTO instructorInfoDTO : iList)
			  {
				  
				  instructorsList.add(new Long(instructorInfoDTO.getInstructorId()));
				  if(null != instructorInfoDTO.getInstructorName())
					  instructorName = instructorInfoDTO.getInstructorName();
			  }		  
			 // instructorsList.add(new Long(instructorServiceDTO.getInstructorId()));
			  
			  long[] instructorArray = instructorsList.stream().mapToLong(i->i).toArray();
			  
			  Long[] InstructorArray  =  ArrayUtils.toObject (instructorArray);
		  
			  DateTime availaibilityStartDay = new DateTime(instructorServiceDTO.getStartDate());	
			  
			 nonAvailableSlots = new  HashMap<String,List<String>>();
			 
			  if(null != locationProfileDetails.getSeriesEndDate() ) {
			}
			  
			  Date availaibilityWeekDay =  null;
			  
			  int days = 30;
 
				  getAvailability = true;
				  availaibilityWeekDay =  availaibilityStartDay.plusDays(days+1).toDate();
				  availaibilityWeekDay = DateTimeUtil.handleEndDate(df1.format(availaibilityWeekDay));
 
		
			  if(InstructorArray.length > 0)
			  {	  
				//Logic for getting the instructor appointment for the week
				  Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> timeOffriterion = 
						  		AppointmentCriterion.getTimeOffForInstructorsInsAVL(InstructorArray,availaibilityStartDay.toDate(),availaibilityWeekDay);
				  
				  timeOffMap  = appointmentDAO.get(timeOffriterion);
				  
			  
			  }

			 if(getAvailability) 
			 {	 

				 //Block for iterating through all instructors and finding their free slots				  
					  for(InstructorInfoDTO instructorInfoDTO : iList)				  
						  
					  {	  
						   
						  if(null != instructorInfoDTO.getInstructorName()) {
						}
					
						  Long instructorID = new Long(instructorInfoDTO.getInstructorId());
					
						  
						  //Logic for getting instructor Availability 
						  Criterion<Availability, Availability> criterion = AvailabilityCriterion.getByInstructorIdInsAVL(instructorID);	
						  instructorAvailability= availabilityDAO.get(criterion);
						   		  					  
						  //Logic for getting the instructor appointment for the week
						   
						  Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> instructorCriterion = 
								  		AppointmentCriterion.findInstructorAvailableHoursInsAVL(instructorID,  availaibilityStartDay.toDate(),availaibilityWeekDay);
						   
						  instructorDayAvailabilityMap  = appointmentDAO.get(instructorCriterion);
						 			  
						  String parsingDate = "";
						  
						  DateTime currentDay = null;
						
						  //Block for get details of each day
						  
						  for(int i=0;i<=days;i++)
						  {

							  instructorAvailableHoursDTOList  = null;
							  
							  currentDay =  availaibilityStartDay.plusDays(i);
							  
							   parsingDate = currentDay.toString(DateTimeUtil.DATE_PATTERN);
						
							   Boolean isTimeOffDay = false;
								  
							   if (timeOffMap.containsKey(instructorInfoDTO.getInstructorId())) {
									  
									  timeOffList = timeOffMap.get(instructorInfoDTO.getInstructorId());
									  
									  timeOffHoursDTOList = new ArrayList<InstructorAvailableHoursDTO>();
										
																  
									  for(InstructorAvailableHoursDTO timeOffAvailabilityDTO : timeOffList)
									  {
 
										  timeOffStartDate = timeOffAvailabilityDTO.getAppointmentStartDate();
										  
										  timeOffEndDate  = timeOffAvailabilityDTO.getAppointmentEndDate();
										  
										  if(currentDay.isEqual(timeOffStartDate )&& currentDay.isEqual(timeOffEndDate))
										  {
											  timeOffStartTime = timeOffAvailabilityDTO.getAppointmentStartTime().plusMinutes(1);
											  timeOffEndTime   = timeOffAvailabilityDTO.getAppointmentEndTime();
										  }
										  
										  else
										  {
											  if(currentDay.isEqual(timeOffStartDate ) && currentDay.isBefore(timeOffEndDate))
											  {
												  timeOffStartTime = timeOffAvailabilityDTO.getAppointmentStartTime().plusMinutes(1);
												  timeOffEndTime   = new LocalTime ("23:59");		
												  
											  }	  
												  
											  else 
											  {	  
												 if (currentDay.isAfter(timeOffStartDate) && currentDay.isEqual(timeOffEndDate))
												 {
													 timeOffStartTime = new LocalTime ("00:01");
													  timeOffEndTime  = timeOffAvailabilityDTO.getAppointmentEndTime();;		
												 }
												 
												 else
												 {
													 if (currentDay.isAfter(timeOffStartDate) && currentDay.isBefore(timeOffEndDate))
													 {
														 isTimeOffDay = true;													 
														 break;
													 }
													 
												 }	 
											  }	 
																					  
										  }
										  
										  if(null != timeOffStartTime &&  null != timeOffEndTime)
										  {
											  timeOffHoursDTO = new InstructorAvailableHoursDTO(timeOffStartTime,timeOffEndTime);
											  timeOffHoursDTOList.add(timeOffHoursDTO);
											  
										  }
										  
										  timeOffStartTime = null;
										  timeOffEndTime = null;
											  
												  
									  }
							
							  }
							   
							   
							   else
							   {
								   timeOffHoursDTOList = null;
							   }
							   
							   
							 
							if(!isTimeOffDay)
							{	
							   
							  if(instructorDayAvailabilityMap.containsKey(parsingDate))
							   {	  
								    instructorAvailableHoursDTOList = instructorDayAvailabilityMap.get(parsingDate);
								  
							   }	   
							  
							   if(null != instructorAvailableHoursDTOList  && instructorAvailableHoursDTOList.size() > 0 )
							   {
								   if(null != timeOffHoursDTOList &&  timeOffHoursDTOList.size() > 0)
									   instructorAvailableHoursDTOList.addAll(timeOffHoursDTOList);							   
								   
							   }
							   else
							   {
								   if(null != timeOffHoursDTOList &&  timeOffHoursDTOList.size() > 0)
									   instructorAvailableHoursDTOList = timeOffHoursDTOList;
								   
								   
							   }
								   
							 	   	  	   
							   dayOfWeek = currentDay.getDayOfWeek();
							 
							   
							   if(null !=  instructorAvailability.getStartTimeByDayOfWeek(dayOfWeek) && null !=    instructorAvailability.getEndTimeByDayOfWeek(dayOfWeek))
							   {	   
									   startTime = new DateTime(instructorAvailability.getStartTimeByDayOfWeek(dayOfWeek)).minuteOfDay().roundFloorCopy();;					   
											 
									   endTime   = new DateTime(instructorAvailability.getEndTimeByDayOfWeek(dayOfWeek)).minuteOfDay().roundFloorCopy();;
							  
								   if(null != startTime && null != endTime)
								   {	  
									   

						   //----Changes for GSSP-321 for get profileTimeoffStartTime profileTimeoffEndTime value for free slots
									   profileTimeoffStartTime=null;//for clear the object on iteration
									   profileTimeoffEndTime=null;
									   SimpleDateFormat getHourMin = new SimpleDateFormat("HH:mm");
									   if(profileTimeOffMap.get(currentDay.toString("yyyy-MM-dd")) != null)
									   {
										   ProfileTimeOffDTO profileTimeOffDate =(ProfileTimeOffDTO)profileTimeOffMap.get(currentDay.toString("yyyy-MM-dd"));
										   if(profileTimeOffDate !=null)
										   {
													String startTimeHrs =  getHourMin.format(profileTimeOffDate.getStartTime());
												   	String endTimeHrs =  getHourMin.format(profileTimeOffDate.getEndTime());
												   	profileTimeoffStartTime = new LocalTime(startTimeHrs);
													profileTimeoffEndTime =   new LocalTime(endTimeHrs);
										    }
									   }
	
							 //----Changes for GSSP-321 for call getAvailableSlotsOnProfileTimeOff only on leave days
									   if(profileTimeoffStartTime != null && profileTimeoffEndTime!= null  ){
										    freeSlotsList = WebServiceUtil.getAvailableSlotsOnProfileTimeOffInsAVL(startTime, endTime, instructorAvailableHoursDTOList,profileTimeoffStartTime,profileTimeoffEndTime);
									   }else{
										    freeSlotsList = WebServiceUtil.getAvailableSlotsInsAVL(startTime, endTime, instructorAvailableHoursDTOList);
									   }
 
									  Date dateVal = getDateFromString(parsingDate);
									  String dayStr = dateVal.getDay()+"";
									  
									   List<OnLineAvailableDTO>  onlineAvlList = null;
										List<InstoreAvailableDTO>  instoreAvlList = null;
										List<OnLineInstoreAvailableDTO>  alllist = new ArrayList<OnLineInstoreAvailableDTO>();
										 
											 onlineAvlList = onlineAvailabilityService.getOnlineAvlFormatByInstructorId(instructorID);
										
										for(OnLineAvailableDTO onls: onlineAvlList){
											if(dayStr.equals(onls.getWeekDay())){
											OnLineInstoreAvailableDTO allDto= new OnLineInstoreAvailableDTO();
											 allDto.setFromTime(onls.getFromTime());
											 allDto.setToTime(onls.getToTime());
											 allDto.setServiceMode("Online");
											 alllist.add(allDto);
											}
										}
										 
										instoreAvlList = instoreAvailabilityService.getInstoreAvlFormatByInstructorId(instructorID);
										for(InstoreAvailableDTO onls: instoreAvlList){
											if(dayStr.equals(onls.getWeekDay())){
											OnLineInstoreAvailableDTO allDto= new OnLineInstoreAvailableDTO();
											 allDto.setFromTime(onls.getFromTime());
											 allDto.setToTime(onls.getToTime());
											 allDto.setServiceMode("Instore");
											 alllist.add(allDto);
											}
										 
										}
										Collections.sort(alllist);
										alllist.stream().map(s -> s.getFromTime());

									   //##########################################
									   boolean flag=false;
									   dividedSlotsList = new ArrayList<Times>();
									   List<OnLineAvailableDTO> ln = new ArrayList<OnLineAvailableDTO>();
									   String availabilityslot ="";
									   if(freeSlotsList != null){
										   for(InstructorAvailableHoursDTO fl:freeSlotsList){
				 
											String  startTime1 = fl.getAppointmentStartTime().toString(DateTimeUtil.HOURS_SEC_PATTERN);
											String  endTime1 = fl.getAppointmentEndTime().toString(DateTimeUtil.HOURS_SEC_PATTERN);
											
											String  startTime2 = fl.getAppointmentStartTime().toString(DateTimeUtil.HOURS_PATTERN);
											String  endTime2 = fl.getAppointmentEndTime().toString(DateTimeUtil.HOURS_PATTERN);
											
											OnLineAvailableDTO onl = new OnLineAvailableDTO();
											onl.setFromTime(startTime2);
											onl.setToTime(endTime2);
											ln.add(onl);
  
											int diffInMinutes = Minutes.minutesBetween(fl.getAppointmentStartTime(), fl.getAppointmentEndTime()).getMinutes();
											char quotes ='"';
 
										   Times  freeSlot = new Times(WebServiceUtil.formatTimeTo12HrInsAVL(startTime1),WebServiceUtil.formatTimeTo12HrInsAVL(endTime1),result.getName(),profileId);

										   
										}
										   if(null != freeSlotsList && freeSlotsList.size() > 0)
										   {
 
											   dividedSlotsList = divideSlots(dateTimeRoomAvalabilityMap,currentDay,freeSlotsList,profileId,duration.intValue(),
													   instructorID,activityIds[0],11l,instructorName,nonAvailableSlots);
										   }
										   //~~~~~~~~~~~~~~~~~~~~~~
										   String sm ="";
										   
										   for(Times tm:dividedSlotsList){
											   sm ="";
												 for(OnLineInstoreAvailableDTO onls: alllist){
													 
														flag= checkSlotUnderInstructorAvlty(tm.getStartTime(),tm.getEndTime(),onls.getFromTime(),onls.getToTime());
														
														if(flag){
															 if("Instore".equals(onls.getServiceMode())){
																 sm ="#INSTORE";
															 }
															 else if("Online".equals(onls.getServiceMode())){
																 sm ="#ONLINE";
															 }else  sm ="#BOTH";
														}
														if(flag)break;
													}
											   char quotes ='"';
											   if("".equals(sm))sm ="#BOTH";
				 
												//changeFormateOfDate
												String startDate = changeFormateOfDate(parsingDate);
												String endDateStr = null;
												if( (endDateStr==null || "".equals(endDateStr.trim()))){
													endDateStr = CalendarUtil.getDefaultEndDate(startDate,3);
												}
												boolean checkInstructorByAppointmentTime = validationService.checkInstructorByAppointmentTime(true,instructorID, startDate, endDateStr, CalendarUtil.plusFormatTime(tm.getStartTime(), 1), CalendarUtil.plusFormatTime(tm.getEndTime(), -1));
												if(!checkInstructorByAppointmentTime){
													//recur = "#RN";
												}else {
												 
											   availabilityslot = quotes+parsingDate+"T"+tm.getStartTime()+";"+parsingDate+"T"+tm.getEndTime()+sm+quotes;
												}
									 
												if(null != availabilityslot && !"".equals(availabilityslot)) {
		                                               availabilityDayList.add(availabilityslot);
		                                           }
												
										   }
										   //~~~~~~~~~~~~~~~~~~~~~~
										   
										   if(!slotsByDateMap.containsKey(parsingDate))
										   {
											   List<Times> list = new ArrayList<Times>();											   
											   slotsByDateMap.put(parsingDate, list);     
										   }

										   appointmentDateList = slotsByDateMap.get(parsingDate); 
										   appointmentDateList.addAll(dividedSlotsList);
									   }

							   }   
							   
						   }	   
					 
						}
							 availabilityList.addAll(availabilityDayList);
							 availabilityDayList.clear();
							 
					  }	
						 
				  }
			        					  
		  			try {
						for(String slotDate : slotsByDateMap.keySet())
						{
							
							 appointmentDateList = slotsByDateMap.get(slotDate);
							//Added for LES-149
							 appointmentDateList.sort(Times.startTimeasLTMComparator);//Java 8 comparator
							 instructorLessonsByDay  = new Lessons(slotDate,appointmentDateList);
							 instructorLessonsByDayList.add(instructorLessonsByDay);
							 
							 
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					result.setAvailability(availabilityList.toString());
					if(null != instructorLessonsByDayList && instructorLessonsByDayList.size() > 0)
					{
						result.setLessons(instructorLessonsByDayList);
					}
					
			 }	
			 

		}

		 public List<Instructor> getInstructorsAffectRecently(String updateDate){		  
			 return instructorDAO.getInstructorsAffectRecently(updateDate);
		 }
 
		 public List<Instructor> getInstructorsAffectRecent(String updateDate){
			 List<Instructor> list = new ArrayList<Instructor>();

				Criterion<Instructor, Instructor> instructorCriterion = InstructorCriterion.findinstructorByChanges(updateDate);
				 list = instructorDAO.search(instructorCriterion);
			 
			 return list;
		 } 
		 
		 public List<Instructor> getFullInstructorsAffectRecent(String updateDate){
			 List<Instructor> list = new ArrayList<Instructor>();

				Criterion<Instructor, Instructor> instructorCriterion = InstructorCriterion.findFullinstructorByChanges();
				 list = instructorDAO.search(instructorCriterion);
			 
			 return list;
		 } 
		 
		 public List<Instructor> getDisabledInstructorsAffectRecent(String updateDate){
			 List<Instructor> list = new ArrayList<Instructor>();

				Criterion<Instructor, Instructor> instructorCriterion = InstructorCriterion.findDisabledInstructorByChanges(updateDate);
				 list = instructorDAO.search(instructorCriterion);
			 
			 return list;
		 }
		 
		 public List<Instructor> getDisabledFullInstructorsAffectRecent(String updateDate){
			 List<Instructor> list = new ArrayList<Instructor>();

				Criterion<Instructor, Instructor> instructorCriterion = InstructorCriterion.findDisabledFullInstructorByChanges(updateDate);
				 list = instructorDAO.search(instructorCriterion);
			 
			 return list;
		 }
		 
			
			private static Date getDateFromString(String dateStr){
				SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		        //String dateInString = "7-Jun-2013";
		        Date date = new Date();

		        try {

		              date = formatter.parse(dateStr);
	 
		        } catch (ParseException e) {
		        	LOG.error("Caught {} when check getDateFromString Appointment", e);
		        }
		        return date;
			}
			
			private static String changeFormateOfDate(String dateStr){
				SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		        //String dateInString = "7-Jun-2013";
		        Date date = new Date();

		        try {

		              date = formatter.parse(dateStr);
	 
		        } catch (ParseException e) {
		        	LOG.error("Caught {} when check getDateFromString Appointment", e);
		        }
		        DateFormat dateFormat = new SimpleDateFormat("MM/dd/yyyy");  
                String strDate = dateFormat.format(date); 
		        return strDate;
			}
			
			public static boolean checkSlotUnderInstructorAvlty(String onlinTimeStart,String onlinTimeEnd,String instAvailStart,String instAvailEnd) throws Exception{
				
				boolean flag = false;
				
				try {
					DateFormat sdf = new SimpleDateFormat(DateTimeUtil.HOURS_PATTERN);
					Date insAvlStart = sdf.parse(instAvailStart);
					Date insAvlEnd = sdf.parse(instAvailEnd);
					Date onlineStart = sdf.parse(onlinTimeStart);
					Date onlineEnd = sdf.parse(onlinTimeEnd);
 
					if(onlineStart.before(onlineEnd) && insAvlStart.before(insAvlEnd)){
					 
						if(onlineStart.after(insAvlStart) && onlineEnd.before(insAvlEnd)){
							flag = true;
						}
						

						if(onlineStart.equals(insAvlStart) && onlineEnd.before(insAvlEnd)){
							flag = true;
						}
						if(onlineEnd.equals(insAvlEnd) && onlineStart.after(insAvlStart)){
							flag = true;
						}
						if( onlineStart.before(insAvlStart) &&  onlineEnd.before(insAvlEnd) &&  onlineEnd.after(insAvlStart)){
							flag = true;
						}
						if( onlineStart.before(insAvlEnd) &&  onlineEnd.after(insAvlEnd) &&  onlineStart.after(insAvlStart)){
							flag = true;
						}
						if(onlineStart.equals(insAvlStart) && onlineEnd.equals(insAvlEnd)){
							flag = true;
						}
					 
					}

				} catch (ParseException e) {
					throw  e;	 
				}
		  
		    return flag;
		} 
			
			private List<Times> divideSlots(Map<String,Boolean> dateTimeRoomAvalabilityMap , DateTime currentDay,List<InstructorAvailableHoursDTO> freeSlotsList, 
					  long profileId,int duration,long instructorId, long activityId, long appId,String instructorName, Map<String,List<String> >nonAvailableSlots) throws Exception
				{				  					
				
						List<Times> dividedFreeSlotList = new ArrayList<Times>();
					
						LocalTime newStartTime  = null,newEndTime=null,slotEndTime=null; 
						
						 Map<Long, RoomDTO> roomCache = new HashMap<Long, RoomDTO>();
						
						Times dividedFreeSlot = null;				
						
						String startTime = "", endTime="",currentDayTimeString = "";	
						
						String parsingDate = currentDay.toString(DateTimeUtil.DATE_PATTERN_SLASH);						
					
										
						DateTime currentDateStartime = null, currentDateEndTime = null;
						
					
						//Define divideSlotList
						 for (InstructorAvailableHoursDTO  freeSlots:freeSlotsList)
						 {
							 newStartTime = freeSlots.getAppointmentStartTime();
							 slotEndTime =  freeSlots.getAppointmentEndTime();
							 
							 if (newStartTime.getMinuteOfHour() == 15 || newStartTime.getMinuteOfHour() == 45)
									 newStartTime = newStartTime.plusMinutes(15);
							 
							 newEndTime = newStartTime.plusMinutes(duration-1);			
							
							 currentDateStartime = currentDay.plusHours(newStartTime.getHourOfDay());
							 
							 currentDateStartime = currentDateStartime.plusMinutes(newStartTime.getMinuteOfHour());
							 
							 currentDateEndTime  = currentDateStartime.plusMinutes(duration-1);
													
						     while (newEndTime.isBefore(slotEndTime) || newEndTime.equals(slotEndTime)) 
						     {	
						    	 						 						 
								 if(!currentDateStartime.dayOfMonth().equals(currentDay.dayOfMonth()) || 
										 !currentDateEndTime.dayOfMonth().equals(currentDay.dayOfMonth()))
									 break;
								
								 
								 newEndTime =  newEndTime.plusMinutes(1);
								 
								 currentDateEndTime  = currentDateStartime.plusMinutes(1);
						    	 
						    	 if(!profileRoomcache.containsKey(profileId))
						    	 {	 					    		 
							    	 //Get the Rooms for that profile
							    	 Criterion<Room, Map<Long, RoomDTO>> profileRoomcriterion = 
							    	 		RoomCriterion.findByProfileIdAndActivityId(profileId, activityId);
							    	 roomCache = roomDAO.get(profileRoomcriterion);
							    	 profileRoomcache.put(profileId, roomCache);
						    	 }	 
						    	 
						    	 roomCache = profileRoomcache.get(profileId);
						    	 
						    	 startTime = newStartTime.toString(DateTimeUtil.HOURS_PATTERN); 		    			 
						    			 
					    		 endTime= newEndTime.toString(DateTimeUtil.HOURS_PATTERN) ;  
					    		 
					    		 			    		 
					    		 currentDayTimeString = profileId + "_ " + parsingDate + "_" + startTime + "_" + endTime;
					    		 
					    		 boolean available = false ;
					    		 
						    	if(! dateTimeRoomAvalabilityMap.containsKey(currentDayTimeString))
						    		 {
						    		 
							    	 
							    		 /*if(nonAvailableSlots != null && nonAvailableSlots.containsKey(parsingDate))
							    		 {
							    			  
							    			 List<String> timeList = nonAvailableSlots.get(parsingDate);
							    			 
							    			 for (String curtimeSt : timeList){
							    				
							    				 int index = curtimeSt.indexOf("_");
							                	 String stTime =  curtimeSt.substring(0,index);
							                	 String edTime =  curtimeSt.substring(index+1);
							                	 
							                	 LocalTime lStTime = DateTimeFormat.forPattern("HH:mm").parseLocalTime(stTime);
							                	 LocalTime lEdTime = DateTimeFormat.forPattern("HH:mm").parseLocalTime(edTime);
							                	 
							                	 if((lStTime.isBefore(newStartTime) || lStTime.equals(newStartTime)) && (lEdTime.isAfter(newEndTime) || lEdTime.equals(newEndTime))) {
							                		 available = true;
							                		 break;
							                	 }

							    			 }
							    			 
							    			
							    		 }	*/					    	 
						    		 available = true;
						    		 								    	 
								    	 dateTimeRoomAvalabilityMap.put(currentDayTimeString, available);
								    	 
						    		 }
								    	 
					    		 				    		
							    	 
						    	 available = dateTimeRoomAvalabilityMap.get(currentDayTimeString);
						
						    	 if(available)			    		 			    	 
						    	 {		 
						    			
						    		 dividedFreeSlot = new Times( startTime, 
						    				  endTime,instructorName,instructorId);

						    		 dividedFreeSlotList.add(dividedFreeSlot);					    		 
						    	 }	
						  
						    	 newStartTime = newStartTime.plusMinutes(30);
						    	 
						    	 newEndTime =  newStartTime.plusMinutes(duration-1);  
						    	 
						    	 currentDateStartime= currentDateStartime.plusMinutes(30);
						    	 
						    	 currentDateEndTime = currentDateEndTime.plusMinutes(duration-1);
						    	 
						    	 
						    
						     }		
						 }
					 
						 return dividedFreeSlotList;
						
				}


	/**
	 * Divides instructor availability blocks into 30-minute time slots for self-service booking
	 * Simplified version with room logic removed as it's not currently used
	 *
	 * @param dateTimeRoomAvalabilityMap - Cache for availability decisions (kept for performance)
	 * @param currentDay - Current date being processed
	 * @param freeSlotsList - List of instructor availability blocks
	 * @param profileId - Location profile ID (used for caching key)
	 * @param duration - Slot duration in minutes (typically 30)
	 * @param instructorId - Instructor ID
	 * @param activityId - Activity ID (parameter kept for interface compatibility but not used)
	 * @param appId - Appointment ID (parameter kept for interface compatibility but not used)
	 * @param instructorName - Instructor name for slot creation
	 * @param nonAvailableSlots - Room unavailability data (parameter kept but not used)
	 * @return List of Times objects representing available 30-minute slots
	 * @throws Exception
	 */
	private List<Times> divideSlotsforSelfService(Map<String,Boolean> dateTimeRoomAvalabilityMap, DateTime currentDay,
			List<InstructorAvailableHoursDTO> freeSlotsList, long profileId, int duration, long instructorId,
			long activityId, long appId, String instructorName, Map<String,List<String>> nonAvailableSlots) throws Exception {

		List<Times> dividedFreeSlotList = new ArrayList<Times>();

		LocalTime newStartTime = null, newEndTime = null, slotEndTime = null;
		Times dividedFreeSlot = null;
		String startTime = "", endTime = "", currentDayTimeString = "";
		String parsingDate = currentDay.toString(DateTimeUtil.DATE_PATTERN_SLASH);
		DateTime currentDateStartime = null, currentDateEndTime = null;


		// Process each instructor availability block
		for (InstructorAvailableHoursDTO freeSlots : freeSlotsList) {
			newStartTime = freeSlots.getAppointmentStartTime();
			slotEndTime = freeSlots.getAppointmentEndTime();

			// Normalize start times to 30-minute boundaries (00 or 30 minutes)
			if (newStartTime.getMinuteOfHour() == 15 || newStartTime.getMinuteOfHour() == 45) {
				newStartTime = newStartTime.plusMinutes(15);
			}

			newEndTime = newStartTime.plusMinutes(duration - 1);
			currentDateStartime = currentDay.plusHours(newStartTime.getHourOfDay());
			currentDateStartime = currentDateStartime.plusMinutes(newStartTime.getMinuteOfHour());
			currentDateEndTime = currentDateStartime.plusMinutes(duration - 1);

			// Generate 30-minute slots within the availability block
			while (newEndTime.isBefore(slotEndTime) || newEndTime.equals(slotEndTime)) {

				// Ensure slot doesn't cross day boundary
				if (!currentDateStartime.dayOfMonth().equals(currentDay.dayOfMonth()) ||
						!currentDateEndTime.dayOfMonth().equals(currentDay.dayOfMonth())) {
					break;
				}

				newEndTime = newEndTime.plusMinutes(1);
				currentDateEndTime = currentDateStartime.plusMinutes(1);

				// Format time strings for slot creation
				startTime = newStartTime.toString(DateTimeUtil.HOURS_PATTERN);
				endTime = newEndTime.toString(DateTimeUtil.HOURS_PATTERN);

				// Create unique cache key for this time slot
				currentDayTimeString = profileId + "_ " + parsingDate + "_" + startTime + "_" + endTime;

				boolean available = false;

				// Check cache for availability decision (performance optimization)
				if (!dateTimeRoomAvalabilityMap.containsKey(currentDayTimeString)) {
					// Since room logic is removed, all slots are considered available
					// This could be enhanced in the future to check other availability constraints
					available = true;
					dateTimeRoomAvalabilityMap.put(currentDayTimeString, available);
				}

				available = dateTimeRoomAvalabilityMap.get(currentDayTimeString);

				// Create time slot if available
				if (available) {
					dividedFreeSlot = new Times(startTime, endTime, instructorName, instructorId);
					dividedFreeSlotList.add(dividedFreeSlot);
				}

				// Move to next 30-minute slot
				newStartTime = newStartTime.plusMinutes(30);
				newEndTime = newStartTime.plusMinutes(duration - 1);
				currentDateStartime = currentDateStartime.plusMinutes(30);
				currentDateEndTime = currentDateEndTime.plusMinutes(duration - 1);
			}
		}

		return dividedFreeSlotList;
	}
			

			 @Override
			 		 public List<Instructor> getFullInstructorsAffectRecent(){
			 			 List<Instructor> list = new ArrayList<Instructor>();
			 
			 				Criterion<Instructor, Instructor> instructorCriterion = InstructorCriterion.findFullinstructorByChanges();
			 				 list = instructorDAO.search(instructorCriterion);
			 			 
			 			 return list;
			 		 }
			 public Map<String, InstructorAVLServiceResponseDTO> getLocationDistRegion() {

					Map<String, InstructorAVLServiceResponseDTO> map = new HashMap<String, InstructorAVLServiceResponseDTO>();

					Criterion<Appointment, InstructorAVLServiceResponseDTO> criterion = AppointmentCriterion.findLocationDistAndRegin();
					List<InstructorAVLServiceResponseDTO> search = null;
					try {
						search = appointmentDAO.search(criterion);
					} catch (Exception e) {

					}
					for (InstructorAVLServiceResponseDTO iAVLDto : search) {
						map.put(iAVLDto.getStoreNumber(), iAVLDto);
					}

					return map;
									
				
				}
			//atttandancestatus
				@Override
				public List<InstructorAppointmentStatusDTO> findInstructorAppointmentStatusByExternalId(String instructorExternalId, String appointmentId) {
					
					InstructorCriterion<InstructorAppointmentStatusDTO> criterio = InstructorCriterion.getInstructorDetailsByExternalId(instructorExternalId, appointmentId);
					
					List<InstructorAppointmentStatusDTO> list = instructorDAO.search(criterio);
					
					return list;
					
					
				}
			 

			public  FinalFreeSlotsAndMaxMinDates getInstructorFreeSLots(LocationProfileInfoDTO locationProfileDetails,
					  InstructorAVLServiceDTO  instructorServiceDTO)  throws Exception{
				 
				FinalFreeSlotsAndMaxMinDates finalFreeSlotsAndMaxMinDates = null;
				  LocalTime profileTimeoffStartTime= null;
				  LocalTime profileTimeoffEndTime= null;
				  int dayOfWeek =0;			  		
				  
				  long profileId = locationProfileDetails.getProfileID();
				  Availability instructorAvailability = null;
				  
				  Map<String, List<InstructorAvailableHoursDTO>>  instructorDayAvailabilityMap = null;
				  List<InstructorServiceResponseDTO>  instructorDayAvailabilityMap2 = null;
				  
				  Map<String, List<InstructorAvailableHoursDTO>>  instructorDayAvailabilityMap3 = null;
				  
				  List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList  = null ,timeOffHoursDTOList = null ;
				  
				  List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList3  = null;
				  
				  DateTime startTime = null ,endTime =null;
				  
				  List<InstructorAvailableHoursDTO> freeSlotsList = null; 
				  
				  List<Long> instructorsList = new ArrayList<Long>();
				  
				
				  Map<String,List<InstructorAvailableHoursDTO>> timeOffMap = new HashMap<String,List<InstructorAvailableHoursDTO>>();
				  
				  List<InstructorAvailableHoursDTO>  timeOffList = null;
				  
				  InstructorAvailableHoursDTO timeOffHoursDTO = null;
				  
				  LocalTime timeOffStartTime = null,  timeOffEndTime = null;
				  boolean getAvailability = false;
				  
				  DateTime timeOffStartDate  = null, timeOffEndDate = null;
				  
				  DateFormat df1 = new SimpleDateFormat(DateTimeUtil.DATE_PATTERN_SLASH);			  	
				  
				   Long duration = 30l;
				   
				  if(duration % 30  !=0)
					  duration = duration + (30 - (duration % 30) ); 
		 
				  //-- GSSP-321-Prevent customers from scheduling a lesson online, when the stores are not offering lessons
				  //----Changes for GSSP-321 for get the profileTimeOffDetails from DB --------
				  	Date startDateInput =null;
				  	SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			        String dateInString = instructorServiceDTO.getStartDate();
			        try 
			        {
			        	startDateInput = formatter.parse(dateInString);

			        } catch (ParseException e) {
			        	 LOG.error("Error during on Date format in Instructor Availability Web Service", e);
			        }
			        Map<String, ProfileTimeOffDTO> profileTimeOffMap = timeoffService.profileTimeOffDetailsInsAVL(profileId,startDateInput);
		
				  List<InstructorInfoDTO> iList = findByInsAVLById(new Long(instructorServiceDTO.getInstructorId()));	

				  for(InstructorInfoDTO instructorInfoDTO : iList)
				  {
					  
					  instructorsList.add(new Long(instructorInfoDTO.getInstructorId()));
					  if(null != instructorInfoDTO.getInstructorName()) {
					}
				  }		  
				 // instructorsList.add(new Long(instructorServiceDTO.getInstructorId()));
				  
				  long[] instructorArray = instructorsList.stream().mapToLong(i->i).toArray();
				  
				  Long[] InstructorArray  =  ArrayUtils.toObject (instructorArray);
			  
				  DateTime availaibilityStartDay = new DateTime(instructorServiceDTO.getStartDate());	
				  
				 if(null != locationProfileDetails.getSeriesEndDate() ) {
				}
				  
				  Date availaibilityWeekDay =  null;
				  
				  int days = 1;

					  getAvailability = true;
					  availaibilityWeekDay =  availaibilityStartDay.plusDays(days+1).toDate();
					  availaibilityWeekDay = DateTimeUtil.handleEndDate(df1.format(availaibilityWeekDay));


			
				  if(InstructorArray.length > 0)
				  {	  
					//Logic for getting the instructor appointment for the week
					  Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> timeOffriterion = 
							  		AppointmentCriterion.getTimeOffForInstructorsInsAVL(InstructorArray,availaibilityStartDay.toDate(),availaibilityWeekDay);
					  
					  timeOffMap  = appointmentDAO.get(timeOffriterion);
					  
				  
				  }

				 if(getAvailability) 
				 {	 

					 //Block for iterating through all instructors and finding their free slots				  
						  for(InstructorInfoDTO instructorInfoDTO : iList)				  
							  
						  {	  
							   
							  if(null != instructorInfoDTO.getInstructorName()) {
							}
						
							  Long instructorID = new Long(instructorInfoDTO.getInstructorId());
						
							  
							  //Logic for getting instructor Availability 
							  Criterion<Availability, Availability> criterion = AvailabilityCriterion.getByInstructorIdInsAVL(instructorID);	
							  instructorAvailability= availabilityDAO.get(criterion);
							   		  					  
							  //Logic for getting the instructor appointment for the week
							   
							  /*Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> instructorCriterion = 
									  		AppointmentCriterion.findInstructorApptsExcludingBreaks(instructorID,  availaibilityStartDay.toDate(),availaibilityWeekDay,instructorServiceDTO.getStartDate());
							   
							  instructorDayAvailabilityMap  = appointmentDAO.get(instructorCriterion);*/
							  
							  //System.out.println("==> "+instructorServiceDTO.getStartDate());
							  
							  
							 // instructorServiceDTO.getStartDate()
							  
							  Criterion<Appointment, List<InstructorServiceResponseDTO>> instructorCriterion1 = 
						  		AppointmentCriterion.findInstructorMaxAndMinTime(instructorID,  availaibilityStartDay.toDate(),availaibilityWeekDay,instructorServiceDTO.getStartDate());

							  instructorDayAvailabilityMap2  = appointmentDAO.get(instructorCriterion1);
							  
							  //System.out.println("==> "+instructorDayAvailabilityMap2);
							  
							  String startTimeApp = "";
							  String endTimeApp = "";
							  for(InstructorServiceResponseDTO gt:instructorDayAvailabilityMap2){
								    startTimeApp = gt.getSeriesStartDate();
								    endTimeApp = gt.getSeriesEndDate();  
							  }
							  
							  
							  Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> instructorCriterion = 
								  		AppointmentCriterion.findInstructorApptsIncludingBreaks(instructorID,  availaibilityStartDay.toDate(),availaibilityWeekDay,instructorServiceDTO.getStartDate(),startTimeApp,endTimeApp);

							  instructorDayAvailabilityMap  = appointmentDAO.get(instructorCriterion);
							  
							  
							  //666666666666666666666666666666666666666666666666666666666666666666
							  
					  Criterion<Appointment, Map<String, List<InstructorAvailableHoursDTO>>> instructorCriterion3 = 
						  		AppointmentCriterion.findBreakMoreThan30(instructorID,  availaibilityStartDay.toDate(),availaibilityWeekDay,instructorServiceDTO.getStartDate(),startTimeApp,endTimeApp);

					  instructorDayAvailabilityMap3  = appointmentDAO.get(instructorCriterion3);
							  
							  //%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
							 			  
							  String parsingDate = "";
							  
							  DateTime currentDay = null;
							
							  //Block for get details of each day
							  
							  for(int i=1;i<=days;i++)
							  {
								//System.out.println("days     ===="+i); 		
								  instructorAvailableHoursDTOList  = null;
								  
								  currentDay =  availaibilityStartDay.plusDays(i);
								  
								   parsingDate = instructorServiceDTO.getStartDate(); 
								   //1393  
								   // if (i!=1)parsingDate = addOneDay(parsingDate);
							
								   Boolean isTimeOffDay = false;
									  
								   if (timeOffMap.containsKey(instructorInfoDTO.getInstructorId())) {
										  
										  timeOffList = timeOffMap.get(instructorInfoDTO.getInstructorId());
										  
										  timeOffHoursDTOList = new ArrayList<InstructorAvailableHoursDTO>();
											
																	  
										  for(InstructorAvailableHoursDTO timeOffAvailabilityDTO : timeOffList)
										  {

											  timeOffStartDate = timeOffAvailabilityDTO.getAppointmentStartDate();
											  
											  timeOffEndDate  = timeOffAvailabilityDTO.getAppointmentEndDate();
											  
											  if(currentDay.isEqual(timeOffStartDate )&& currentDay.isEqual(timeOffEndDate))
											  {
												  timeOffStartTime = timeOffAvailabilityDTO.getAppointmentStartTime().plusMinutes(1);
												  timeOffEndTime   = timeOffAvailabilityDTO.getAppointmentEndTime();
											  }
											  
											  else
											  {
												  if(currentDay.isEqual(timeOffStartDate ) && currentDay.isBefore(timeOffEndDate))
												  {
													  timeOffStartTime = timeOffAvailabilityDTO.getAppointmentStartTime().plusMinutes(1);
													  timeOffEndTime   = new LocalTime ("23:59");		
													  
												  }	  
													  
												  else 
												  {	  
													 if (currentDay.isAfter(timeOffStartDate) && currentDay.isEqual(timeOffEndDate))
													 {
														 timeOffStartTime = new LocalTime ("00:01");
														  timeOffEndTime  = timeOffAvailabilityDTO.getAppointmentEndTime();;		
													 }
													 
													 else
													 {
														 if (currentDay.isAfter(timeOffStartDate) && currentDay.isBefore(timeOffEndDate))
														 {
															 isTimeOffDay = true;													 
															 break;
														 }
														 
													 }	 
												  }	 
																						  
											  }
											  
											  if(null != timeOffStartTime &&  null != timeOffEndTime)
											  {
												  timeOffHoursDTO = new InstructorAvailableHoursDTO(timeOffStartTime,timeOffEndTime);
												  timeOffHoursDTOList.add(timeOffHoursDTO);
												  
											  }
											  
											  timeOffStartTime = null;
											  timeOffEndTime = null;
												  
													  
										  }
								
								  }
								   
								   
								   else
								   {
									   timeOffHoursDTOList = null;
								   }
								   
								   
								 
								if(!isTimeOffDay)
								{	
								   
								  if(instructorDayAvailabilityMap.containsKey(parsingDate))
								   {	  
									    instructorAvailableHoursDTOList = instructorDayAvailabilityMap.get(parsingDate);
									    //------------------------------
									    if(instructorDayAvailabilityMap3 != null)
									    instructorAvailableHoursDTOList3 = instructorDayAvailabilityMap3.get(parsingDate);
									    //-------------------------------
									  
								   }
								  
								  //System.out.println("parsingDate  "+parsingDate);
								  
								   if(null != instructorAvailableHoursDTOList  && instructorAvailableHoursDTOList.size() > 0 )
								   {
									   if(null != timeOffHoursDTOList &&  timeOffHoursDTOList.size() > 0)
										   instructorAvailableHoursDTOList.addAll(timeOffHoursDTOList);							   
									   
								   }
								   else
								   {
									   if(null != timeOffHoursDTOList &&  timeOffHoursDTOList.size() > 0)
										   instructorAvailableHoursDTOList = timeOffHoursDTOList;
									   
									   
								   }
								   
								   //1393
								  /*  Date startDateInput2 =null;
								   String dateInString2=null;
								  	SimpleDateFormat formatter2 = new SimpleDateFormat("yyyy-MM-dd");
								  	if (i!=0)dateInString2 = addOneDay(instructorServiceDTO.getStartDate()); 
							       // String dateInString2 = instructorServiceDTO.getStartDate();
							        try 
							        {
							        	startDateInput2 = formatter.parse(dateInString);

							        } catch (ParseException e) {
							        	 LOG.error("Error during on Date format in Instructor Availability Web Service", e);
							        } */
								     
								   dayOfWeek = getDayNumber7to6(startDateInput);   
								  // dayOfWeek = dayOfWeek-1;
								    //System.out.println(startDateInput+"   A dayOfWeek   -  "+dayOfWeek);
								   //dayOfWeek = currentDay.getDayOfWeek();
								 
								   //==//==System.out.println(instructorAvailability.getStartTimeByDayOfWeek(7));
								   if(null !=  instructorAvailability.getStartTimeByDayOfWeek(dayOfWeek) && null !=    instructorAvailability.getEndTimeByDayOfWeek(dayOfWeek))
								   {	   
										   startTime = new DateTime(instructorAvailability.getStartTimeByDayOfWeek(dayOfWeek)).minuteOfDay().roundFloorCopy();;					   
												 
										   endTime   = new DateTime(instructorAvailability.getEndTimeByDayOfWeek(dayOfWeek)).minuteOfDay().roundFloorCopy();;
								  
									   if(null != startTime && null != endTime)
									   {	  
										   

							   //----Changes for GSSP-321 for get profileTimeoffStartTime profileTimeoffEndTime value for free slots
										   profileTimeoffStartTime=null;//for clear the object on iteration
										   profileTimeoffEndTime=null;
										   SimpleDateFormat getHourMin = new SimpleDateFormat("HH:mm");
										   if(profileTimeOffMap.get(currentDay.toString("yyyy-MM-dd")) != null)
										   {
											   ProfileTimeOffDTO profileTimeOffDate =(ProfileTimeOffDTO)profileTimeOffMap.get(currentDay.toString("yyyy-MM-dd"));
											   if(profileTimeOffDate !=null)
											   {
														String startTimeHrs =  getHourMin.format(profileTimeOffDate.getStartTime());
													   	String endTimeHrs =  getHourMin.format(profileTimeOffDate.getEndTime());
													   	profileTimeoffStartTime = new LocalTime(startTimeHrs);
														profileTimeoffEndTime =   new LocalTime(endTimeHrs);
											    }
										   }
		
								 //----Changes for GSSP-321 for call getAvailableSlotsOnProfileTimeOff only on leave days
										   if(profileTimeoffStartTime != null && profileTimeoffEndTime!= null  ){
											    freeSlotsList = WebServiceUtil.getAvailableSlotsOnProfileTimeOffInsAVL(startTime, endTime, instructorAvailableHoursDTOList,profileTimeoffStartTime,profileTimeoffEndTime);
										   }else{
											    freeSlotsList = WebServiceUtil.getAvailableSlotsInsAVLDayForce(startTime, endTime, instructorAvailableHoursDTOList);
										   }
										   List<InstructorAvailableHoursDTO>  freeSlotsList2 = new ArrayList<InstructorAvailableHoursDTO>();
										   List<InstructorAvailableHoursDTO>  freeSlotsList3 = new ArrayList<InstructorAvailableHoursDTO>();
										   //^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
										   List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList3x = new ArrayList<InstructorAvailableHoursDTO>();
										    if(instructorAvailableHoursDTOList3 !=null){
										     instructorAvailableHoursDTOList3x = instructorAvailableHoursDTOList3.stream()
										 			  .sorted(Comparator.comparing(InstructorAvailableHoursDTO::getAppointmentStartTime))
										 			  .collect(Collectors.toList());
										    }
										   
										   for(InstructorAvailableHoursDTO ss:freeSlotsList){
											   LocalTime startTime1 = ss.getAppointmentStartTime();
											   LocalTime endTime1 = ss.getAppointmentEndTime();
											   String typeNull =null;
											   List<InstructorAvailableHoursDTO> instructorAvailableHoursDTOList4x = new ArrayList<InstructorAvailableHoursDTO>();
								    	         instructorAvailableHoursDTOList4x =instructorAvailableHoursDTOList3x;
								    	         int cnt =0;
								    	           LocalTime startGropBrl = null;
												   LocalTime endGropBrl = null;
											   for(InstructorAvailableHoursDTO bb:instructorAvailableHoursDTOList4x){
												  
												 
												   
												   LocalTime startTime2 = bb.getAppointmentStartTime();
												   LocalTime endTime2 = bb.getAppointmentEndTime();
									    		 boolean isOverlap = isOverlap(startTime1, endTime1, startTime2, endTime2);
								    	         
								    	         boolean isSame = isSame(startTime1, endTime1, startTime2, endTime2);
								    	         
								    	         
								    	         if (isOverlap) {
								    	        	 cnt=cnt+1;
								    	        	 if(cnt==1)startGropBrl =startTime2;
								    	        	 endGropBrl=endTime2;
								    	             System.out.println("Intervals overlap.");
								    	             
								    	             ss.setType("Both");
								    	             typeNull="Both";
								    	             //instructorAvailableHoursDTOList4x.remove(bb);
								    	             
								    	             if(isSame) {
									    	             System.out.println("Intervals isSame.");
									    	             //ss.setType("Break");
									    	            
									    	            ss.setType("Break");
									    	             typeNull="Break";
									    	            // instructorAvailableHoursDTOList4x.remove(bb);
									    	         }
								    	         } 
								    	           
											   }
											  //-----This logic for  multiple breaks equals gap
											   if(cnt>1){
												   boolean isSame = isSame(startTime1, endTime1, startGropBrl, endGropBrl);
												   if(isSame) {
													   ss.setType("Break");
												   }
									    	          
											   }
											 //-----This logic for  multiple breaks equals gap
											   
											   if(ss.getType()==null)ss.setType("Gaps");
											   freeSlotsList3.add(ss);
											   
											   }
										  
										   List<InstructorAvailableHoursDTO>  freeSlotsListResult = new ArrayList<InstructorAvailableHoursDTO>();
										   for(InstructorAvailableHoursDTO ss:freeSlotsList3){
											   LocalTime startTime1 = ss.getAppointmentStartTime();
											   LocalTime endTime1 = ss.getAppointmentEndTime();
											   String type = ss.getType();
											   System.out.println(" startTime1 "+startTime1);
											   System.out.println(" endTime1 "+endTime1);
											   System.out.println(" Type "+type);
											   
											   int currnTime1 = ss.getAppointmentStartTime().toDateTimeToday().getMinuteOfDay();
									    		int currnTime2 = ss.getAppointmentEndTime().toDateTimeToday().getMinuteOfDay();
									    		int diff = currnTime2 - currnTime1;
									    		if(diff>60 && "Gaps".equals(ss.getType())){
									    			freeSlotsListResult.add(ss);
									    		}
									    		if(diff>=30 && "Break".equals(ss.getType())){
									    			freeSlotsListResult.add(ss);
									    		}
									    		if(diff>=60 && "Both".equals(ss.getType())){
									    			freeSlotsListResult.add(ss);
									    		}
										   }
 
										  //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++
									         //--EEE--freeSlotsList2=filteredList;
									         freeSlotsList2=freeSlotsListResult;
									        //++++++++++++++++++++++++++++++++++++++++++++++++++++
										   //System.out.println("  AF    ==>"+freeSlotsList2.size());
										   List<InstructorAvailableHoursDTO> sortedZoomMeetingID = freeSlotsList2.stream()
										 			  .sorted(Comparator.comparing(InstructorAvailableHoursDTO::getAppointmentStartTime))
										 			  .collect(Collectors.toList());
										   
										   /*for(InstructorAvailableHoursDTO ss:sortedZoomMeetingID){
												  System.out.println("StartTime" +ss.getAppointmentStartTime()+"EndTime" +ss.getAppointmentEndTime()); 
											   }
										   //^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
										   for(InstructorAvailableHoursDTO ss:freeSlotsList2){
											  System.out.println("StartTime" +ss.getAppointmentStartTime()+"EndTime" +ss.getAppointmentEndTime()); 
										   }*/
										   InstructorAvailableHoursDTO instructorAvailableHoursminMaxDTO = DateTimeUtil.getMinAndMaxTimes(instructorAvailableHoursDTOList);
											
											 finalFreeSlotsAndMaxMinDates = new FinalFreeSlotsAndMaxMinDates();
											finalFreeSlotsAndMaxMinDates.setFreeSlotsList(sortedZoomMeetingID);
											finalFreeSlotsAndMaxMinDates.setInstructorAvailableHoursminMaxDTO(instructorAvailableHoursminMaxDTO);
										

										  
											   
											
								   }   
								   
							   }	   
						 
							}
								
								 
						  }	
							 
					  }
				        					  
			  		
						
				 }
				return finalFreeSlotsAndMaxMinDates;	
				 

			
			}
						
			public static int getDayNumber(Date date) {
			    Calendar cal = Calendar.getInstance();
			    cal.setTime(date);
			    return cal.get(Calendar.DAY_OF_WEEK);
			}
			
			public static int getDayNumber7to6(Date date) {
				Calendar c = Calendar.getInstance();
				c.setTime(date);
				int dayOfWeek = c.get(Calendar.DAY_OF_WEEK);

				dayOfWeek =dayOfWeek-1;
				if(dayOfWeek ==0)dayOfWeek=7;
			    return dayOfWeek;
			}
	
		
			
			
/*			
		    private   String getDateFormatAPI(String sourceDate){
		    	//2021-07-12 15:00:00
		    	String startTime = null;
		    	//String sourceDate = "2012-02-29";
				SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
				Date myDate = format.parse(sourceDate);
				myDate = DateUtil.addDays(myDate, 1);
		 	   DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		 			Date insAvlStart = null;
		 
		 			try{
		 				insAvlStart = sdf.parse(date);
		 				DateFormat dateFormat = new SimpleDateFormat("MM/dd/yyyy");  
			            startTime = dateFormat.format(insAvlStart);
		 	 
		 			} catch (ParseException e) {
		 				// TODO Auto-generated catch block
		 				e.printStackTrace();
		 			}
		 			
		          return  startTime;
		    }*/
		    
		   
		    	     private static String addOneDay(String date) {
		    	    return LocalDate.parse(date).plusDays(1).toString();
		    	  }
		    	  
		/*    	  public static void main(String args[]){
		    	      System.out.println(addOneDay("2023-04-19")); 
		 
		    	  }*/
 
		    	     //===============================================================
		    	  // Method to check if two intervals overlap and same
		    	     public static boolean isOverlap(LocalTime start1, LocalTime end1, LocalTime start2, LocalTime end2) {
		    	         return start1.isBefore(end2) && start2.isBefore(end1);
		    	     }
		    	   
		    	     // Method to check if two intervals same
		    	     public static boolean isSame(LocalTime start1, LocalTime end1, LocalTime start2, LocalTime end2) {
		    	         return start1.isEqual(start2) && end1.isEqual(end2);
		    	     }
		    	     //==============================================
}
