package com.guitarcenter.scheduler.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.guitarcenter.scheduler.dto.CustomerRegistrationRequest;
import com.guitarcenter.scheduler.dto.CustomerResetPasswordLink;
import com.guitarcenter.scheduler.dto.RegistrationResponse;
import com.guitarcenter.scheduler.service.CustomerRegistrationService;

@Service
public  class CustomerRegistrationServiceImpl implements CustomerRegistrationService {

	private static final Logger LOG = LoggerFactory.getLogger(CustomerRegistrationServiceImpl.class);
	
	@Value("${adp.registerNewCustomerUrl}")
    private String registerNewCustomerUrl;

	@Value("${adp.resetLinkEmailTriggerApi}")
    private String resetLinkEmailTriggerApi;
   
	@Override
    public RegistrationResponse registerCustomer(CustomerRegistrationRequest request,RestTemplate restTemplate) {
    	//TODO:: Move to propertiesregisterCustomer
		
        //String apiUrl = "https://tl1-www.guitarcenter.com/rest/model/atg/userprofiling/ProfileActor/create-SPA";
        // registerNewCustomerUrl = "https://tl1-rst.guitarcenter.com/rest/model/atg/userprofiling/ProfileActor/create-SPA";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<CustomerRegistrationRequest> entity = new HttpEntity<>(request, headers);
        ResponseEntity<RegistrationResponse> responseEntity = null;
        try {
			//restTemplate = WebServiceUtil.getRestTemplate();
        	
			 responseEntity = restTemplate.postForEntity(registerNewCustomerUrl, entity, RegistrationResponse.class);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			LOG.error("Info  registerCustomer  Exception "+request.getEmail()); 
			e.printStackTrace();
			e.getCause();
		}
       
            return responseEntity.getBody();
        
        
    }
	
	

	@Override
    public RegistrationResponse resetPasswordEmailTrigger(CustomerResetPasswordLink customerResetPasswordLink,RestTemplate restTemplate) {
    	//TODO:: Move to properties
        //String apiUrl = "https://tl1-www.guitarcenter.com/rest/model/atg/account/myaccountActor/emailPasswordResetLink";
		 // resetLinkEmailTriggerApi = "https://tl1-rst.guitarcenter.com/rest/model/atg/account/myaccountActor/emailPasswordResetLink";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<CustomerResetPasswordLink> entity = new HttpEntity<>(customerResetPasswordLink, headers);
        ResponseEntity<RegistrationResponse> responseEntity = null;
        try {
			//restTemplate = WebServiceUtil.getRestTemplate();
        	
			 responseEntity = restTemplate.postForEntity(resetLinkEmailTriggerApi, entity, RegistrationResponse.class);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			e.getCause();
		}
        

        if (responseEntity.getStatusCode() == HttpStatus.OK) {
            return responseEntity.getBody();
        } else {
            // Handle other status codes appropriately
            return null;
        }
    }
}


