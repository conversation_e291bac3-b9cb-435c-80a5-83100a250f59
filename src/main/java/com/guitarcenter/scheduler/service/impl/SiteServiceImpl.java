package com.guitarcenter.scheduler.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.guitarcenter.scheduler.dao.SiteDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.SiteCriterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.service.SiteService;

@Service("siteService")
public class SiteServiceImpl implements SiteService{
    Logger log = LoggerFactory.getLogger(SiteServiceImpl.class);
    
	@Resource
	private SiteDAO siteDAO;
	@Override
	public Site getSiteById(long id) {
		return siteDAO.get(id);
	}
	
    /**
     * Return a collection of Site records that have matching external id value.
     * 
     * Note: this is returning a collection simply because there is not a
     * database constraint on the external identifiers; it is possible that
     * multiple sites *could* be updated from a single external record in the
     * future. Callers will need to determine the best course of action if there
     * are multiple sites returned.
     * 
     * @param externalId String containing the external identifier to use for
     *                   lookup
     * @return a List of matching Site records
     */
    @Override
    public List<Site> findSitesByExternalId(String externalId) {
        if (log.isDebugEnabled()) {
            log.debug("finding sites with external id matching {}", externalId);
        }
        Criterion<Site, Site> criterion = SiteCriterion.findByExternalId(externalId);
        List<Site> sites = siteDAO.search(criterion, DAOHelper.FETCH_NONE);
        if (log.isDebugEnabled()) {
            log.debug("returning sites {}", sites);
        }
        return sites;
    }

}
