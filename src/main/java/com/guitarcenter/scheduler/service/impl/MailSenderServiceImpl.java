package com.guitarcenter.scheduler.service.impl;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.Map;

import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.internet.MimeMessage;
import javax.mail.util.ByteArrayDataSource;
import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.service.MailSenderService;
import com.itextpdf.text.Document;

import freemarker.template.Template;
import freemarker.template.TemplateException;


@Service("mailSenderService")
public class MailSenderServiceImpl implements MailSenderService {

	private static final Logger LOG = LoggerFactory.getLogger(MailSenderServiceImpl.class);

	@Autowired
	private JavaMailSender mailSender;

	@Autowired
	private FreeMarkerConfigurer freeMarkerConfigurer;

	@Override
	public void sendMail(Map<String, Object> dataMap, String templatePath) {
		try {
			MimeMessage mailMessage = initMessage(dataMap, templatePath);
			Session session = ((JavaMailSenderImpl)mailSender).getSession();
			((JavaMailSenderImpl)mailSender).setUsername(getSessionMailProperty(session, "user"));
			((JavaMailSenderImpl)mailSender).setPassword(getSessionMailProperty(session, "password"));
			((JavaMailSenderImpl)mailSender).setHost(getSessionMailProperty(session, "host"));
			mailSender.send(mailMessage);
						
		} catch (Exception e) {
			LOG.error("[Error message]: Error happened when sending email.", e);
		}
	}
	
	//GSSP-238 Changes
	/*@Override
	public void sendMailWithAttachment(Map<String, Object> dataMap, String templatePath, HSSFWorkbook workbook) throws MessagingException,Exception {
		try {
			MimeMessage mailMessage = initMessage(dataMap, templatePath,workbook);
//			MimeMessage attachmentMail = initMessage(dataMap, templatePath);
			
			
			
			Session session = ((JavaMailSenderImpl)mailSender).getSession();
			((JavaMailSenderImpl)mailSender).setUsername(getSessionMailProperty(session, "user"));
			((JavaMailSenderImpl)mailSender).setPassword(getSessionMailProperty(session, "password"));
			((JavaMailSenderImpl)mailSender).setHost(getSessionMailProperty(session, "host"));
			mailSender.send(mailMessage);
		} 
		catch (MessagingException e) {
			LOG.error("[Error message]: Error happened when initializing email message.", e);
			throw e;
		}
		catch (Exception e) {
			
		
			LOG.error("[Error message]: Error happened when sending email.", e);
			throw e;
		}
	}*/

	//GSSP-240 Changes
	@Override
	public void sendMailWithAttachment(Map<String, Object> dataMap, String templatePath, HSSFWorkbook workbook,	String filePath, String fileName) 
		throws Exception{
		try {
			MimeMessage mailMessage = initMessage(dataMap, templatePath,workbook, filePath, fileName );
			Session session = ((JavaMailSenderImpl)mailSender).getSession();
			((JavaMailSenderImpl)mailSender).setUsername(getSessionMailProperty(session, "user"));
			((JavaMailSenderImpl)mailSender).setPassword(getSessionMailProperty(session, "password"));
			((JavaMailSenderImpl)mailSender).setHost(getSessionMailProperty(session, "host"));
		 	mailSender.send(mailMessage);
		}catch(Exception e){
			LOG.error("[Error message]: Error happened when sending email.", e);
			throw e;
			}
	}
	
	private String getSessionMailProperty(Session session, String property) {
		String protocol = session.getProperty("mail.transport.protocol");
		if (!StringUtils.hasText(protocol)) {
			protocol = "smtp";
		}
		StringBuilder builder = new StringBuilder().append("mail.")
				.append(protocol).append(".").append(property);
		String value = session.getProperty(builder.toString());
		return value;
	}

	private MimeMessage initMessage(Map<String, Object> dataMap, String templatePath) throws IOException {
		MimeMessage mailMessage = mailSender.createMimeMessage();
		try {
			MimeMessageHelper messageHelper = new MimeMessageHelper(mailMessage, true, "utf-8");
			if (!StringUtils.hasText((String) dataMap.get(AppConstants.FROM))) {
				dataMap.put(AppConstants.FROM, "<EMAIL>");
			}
			if (!StringUtils.hasText((String) dataMap.get(AppConstants.SUBJECT))) {
				dataMap.put(AppConstants.SUBJECT, "Email from Guitar Center Studios");
			}
			messageHelper.setFrom((String) dataMap.get(AppConstants.FROM));
			messageHelper.setSubject((String) dataMap.get(AppConstants.SUBJECT));
			if ((dataMap.get(AppConstants.EMAIL_TYPE_TO) == null || ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_TO)).length == 0)
					&& (dataMap.get(AppConstants.EMAIL_TYPE_CC) == null || ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_CC)).length == 0)
					&& (dataMap.get(AppConstants.EMAIL_TYPE_BCC) == null || ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_BCC)).length == 0)) {
				LOG.error("[Error message]: There are no recipients.");
				return null;
			}
			if (dataMap.get(AppConstants.EMAIL_TYPE_TO) != null
					&& ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_TO)).length > 0) {
				messageHelper.setTo((String[]) dataMap.get(AppConstants.EMAIL_TYPE_TO));
			}
			if (dataMap.get(AppConstants.EMAIL_TYPE_CC) != null
					&& ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_CC)).length > 0) {
				messageHelper.setCc((String[]) dataMap.get(AppConstants.EMAIL_TYPE_CC));
			}
			if (dataMap.get(AppConstants.EMAIL_TYPE_BCC) != null
					&& ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_BCC)).length > 0) {
				messageHelper.setBcc((String[]) dataMap.get(AppConstants.EMAIL_TYPE_BCC));
			}
			Context env = (Context) new InitialContext().lookup("java:comp/env");
			String emailHeaderImgUrl = (String)env.lookup(AppConstants.EMAIL_HEADER_IMG_URL);
			dataMap.put(AppConstants.EMAIL_HEADER_IMG_URL, emailHeaderImgUrl);
			String msgText = getMailText(dataMap, templatePath);
			if (!StringUtils.hasText(msgText)) {
				LOG.error("[Error message]: The content of email is empty.");
				return null;
			}
			messageHelper.setText(msgText, true);	
			
			
			
		} catch (MessagingException e) {
			LOG.error("[Error message]: Error happened when initializing email message.", e);
			return null;
		} catch (NamingException e) {
			LOG.error("[Error message]: Error happened when search server URL with JNDI.", e);
		}
		return mailMessage;
	}
	
/*	private MimeMessage initMessage(Map<String, Object> dataMap, String templatePath,HSSFWorkbook workbook ) throws IOException, MessagingException {
		MimeMessage mailMessage = mailSender.createMimeMessage();
		try {
			MimeMessageHelper messageHelper = new MimeMessageHelper(mailMessage, true, "utf-8");
			if (!StringUtils.hasText((String) dataMap.get(AppConstants.FROM))) {
				dataMap.put(AppConstants.FROM, "<EMAIL>");
			}
			if (!StringUtils.hasText((String) dataMap.get(AppConstants.SUBJECT))) {
				dataMap.put(AppConstants.SUBJECT, "Email from Guitar Center Studios");
			}
			messageHelper.setFrom((String) dataMap.get(AppConstants.FROM));
			messageHelper.setSubject((String) dataMap.get(AppConstants.SUBJECT));
			if ((dataMap.get(AppConstants.EMAIL_TYPE_TO) == null || ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_TO)).length == 0)
					&& (dataMap.get(AppConstants.EMAIL_TYPE_CC) == null || ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_CC)).length == 0)
					&& (dataMap.get(AppConstants.EMAIL_TYPE_BCC) == null || ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_BCC)).length == 0)) {
				LOG.error("[Error message]: There are no recipients.");
				return null;
			}
			if (dataMap.get(AppConstants.EMAIL_TYPE_TO) != null
					&& ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_TO)).length > 0) {
				messageHelper.setTo((String[]) dataMap.get(AppConstants.EMAIL_TYPE_TO));
			}
			if (dataMap.get(AppConstants.EMAIL_TYPE_CC) != null
					&& ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_CC)).length > 0) {
				messageHelper.setCc((String[]) dataMap.get(AppConstants.EMAIL_TYPE_CC));
			}
			if (dataMap.get(AppConstants.EMAIL_TYPE_BCC) != null
					&& ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_BCC)).length > 0) {
				messageHelper.setBcc((String[]) dataMap.get(AppConstants.EMAIL_TYPE_BCC));
			}
			Context env = (Context) new InitialContext().lookup("java:comp/env");
			String emailHeaderImgUrl = (String)env.lookup(AppConstants.EMAIL_HEADER_IMG_URL);
			dataMap.put(AppConstants.EMAIL_HEADER_IMG_URL, emailHeaderImgUrl);
			String msgText = getMailText(dataMap, templatePath);
			if (!StringUtils.hasText(msgText)) {
				LOG.error("[Error message]: The content of email is empty.");
				return null;
			}
			messageHelper.setText(msgText, true);	
			
			//GSSP-238 Changes
			FileOutputStream fos = new FileOutputStream("/home/<USER>/AllStoreConflictingAppointmentsReport.xls");
			workbook.write(fos);
			fos.close();
			DataSource fds = new FileDataSource("/home/<USER>/AllStoreConflictingAppointmentsReport.xls");
			
			messageHelper.addAttachment("AllStoreConflictingAppointmentsReport.xls", fds);
			
		} catch (MessagingException e) {
			LOG.error("[Error message]: Error happened when initializing email message.", e);
			throw e;
		} catch (NamingException e) {
			LOG.error("[Error message]: Error happened when search server URL with JNDI.", e);
		}
		return mailMessage;
	}*/

		//GSSP-240 
	private MimeMessage initMessage(Map<String, Object> dataMap, String templatePath,
		HSSFWorkbook workbook, String filePath, String fileName) throws IOException {
		MimeMessage mailMessage = mailSender.createMimeMessage();
		try {
			MimeMessageHelper messageHelper = new MimeMessageHelper(mailMessage, true, "utf-8");
			if (!StringUtils.hasText((String) dataMap.get(AppConstants.FROM))) {
				dataMap.put(AppConstants.FROM, "<EMAIL>");
			}
			if (!StringUtils.hasText((String) dataMap.get(AppConstants.SUBJECT))) {
				dataMap.put(AppConstants.SUBJECT, "Email from Guitar Center Studios");
			}
			messageHelper.setFrom((String) dataMap.get(AppConstants.FROM));
			messageHelper.setSubject((String) dataMap.get(AppConstants.SUBJECT));
			if ((dataMap.get(AppConstants.EMAIL_TYPE_TO) == null || ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_TO)).length == 0)
					&& (dataMap.get(AppConstants.EMAIL_TYPE_CC) == null || ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_CC)).length == 0)
					&& (dataMap.get(AppConstants.EMAIL_TYPE_BCC) == null || ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_BCC)).length == 0)) {
				LOG.error("[Error message]: There are no recipients.");
				return null;
			}
			if (dataMap.get(AppConstants.EMAIL_TYPE_TO) != null
					&& ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_TO)).length > 0) {
				messageHelper.setTo((String[]) dataMap.get(AppConstants.EMAIL_TYPE_TO));
			}
			if (dataMap.get(AppConstants.EMAIL_TYPE_CC) != null
					&& ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_CC)).length > 0) {
				messageHelper.setCc((String[]) dataMap.get(AppConstants.EMAIL_TYPE_CC));
			}
			if (dataMap.get(AppConstants.EMAIL_TYPE_BCC) != null
					&& ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_BCC)).length > 0) {
				messageHelper.setBcc((String[]) dataMap.get(AppConstants.EMAIL_TYPE_BCC));
			}
			Context env = (Context) new InitialContext().lookup("java:comp/env");
			String emailHeaderImgUrl = (String)env.lookup(AppConstants.EMAIL_HEADER_IMG_URL);
			dataMap.put(AppConstants.EMAIL_HEADER_IMG_URL, emailHeaderImgUrl);
			String msgText = getMailText(dataMap, templatePath);
			if (!StringUtils.hasText(msgText)) {
				LOG.error("[Error message]: The content of email is empty.");
				return null;
			}
			messageHelper.setText(msgText, true);	
			
			messageHelper.addAttachment(fileName, getDataSource(filePath,fileName, workbook));
			
		} catch (MessagingException e) {
			LOG.error("[Error message]: Error happened when initializing email message.", e);
		} catch (NamingException e) {
			LOG.error("[Error message]: Error happened when search server URL with JNDI.", e);
		}
		return mailMessage;
	}

	//GSSP-240
	private DataSource getDataSource(String filePath, String fileName, HSSFWorkbook workbook) throws IOException{
			File file = new File(filePath,fileName);//fullFilePath
	        FileOutputStream fos = new FileOutputStream(file);
	        workbook.write(fos);
	        fos.close();
			//Data source needs full file path. Should not send only file name. 
			return new FileDataSource(file);
	}

	
	private String getMailText(Map<String, Object> dataMap, String templatePath) {
		try {
			Template template = freeMarkerConfigurer.getConfiguration().getTemplate(templatePath);
			return FreeMarkerTemplateUtils.processTemplateIntoString(template, dataMap);
		} catch (IOException e) {
			LOG.error("[Error message]: Error happened when searching email template file.", e);
		} catch (TemplateException e) {
			LOG.error("[Error message]: Error happened when initializing email template file.", e);
		} 
		return null;
	}

	//GSSP -265-PDF and ExcelAttachment.
	@Override
	public void sendMailWithpdfAttachment(Map<String, Object> dataMap, String templatePath,HSSFWorkbook workbook,ByteArrayInputStream boatXL,
			Document document,ByteArrayInputStream boat) {
		
			try {
				MimeMessage mailMessage = initMessage(dataMap, templatePath,workbook,boatXL,document,boat );
				Session session = ((JavaMailSenderImpl)mailSender).getSession();
				((JavaMailSenderImpl)mailSender).setUsername(getSessionMailProperty(session, "user"));
				((JavaMailSenderImpl)mailSender).setPassword(getSessionMailProperty(session, "password"));
				((JavaMailSenderImpl)mailSender).setHost(getSessionMailProperty(session, "host"));
			 	mailSender.send(mailMessage);
			 	//267 Changes
				String subject = (String)dataMap.get(AppConstants.SUBJECT);
				String[] bcc = (String[]) dataMap.get(AppConstants.EMAIL_TYPE_BCC);
				if((subject).contains(AppConstants.SUBJECT_LOG))
				{
					LOG.error("Instructor email successfully sent for Store" + subject.substring(31));
					LOG.error("Email has been sent to following instructors" + Arrays.toString(bcc));
				}
				
			}catch(Exception e){
				LOG.error("[Error message]: Error happened when sending email.", e);
				
				}
		}
		
		private MimeMessage initMessage(Map<String, Object> dataMap, String templatePath,HSSFWorkbook workbook, ByteArrayInputStream boatXL, Document document,
				ByteArrayInputStream boat) {
			
				MimeMessage mailMessage = mailSender.createMimeMessage();
				try {
					MimeMessageHelper messageHelper = new MimeMessageHelper(mailMessage, true, "utf-8");
					if (!StringUtils.hasText((String) dataMap.get(AppConstants.FROM))) {
						dataMap.put(AppConstants.FROM, "<EMAIL>");
					}
					if (!StringUtils.hasText((String) dataMap.get(AppConstants.SUBJECT))) {
						dataMap.put(AppConstants.SUBJECT, "Email from Guitar Center Studios");
					}
					messageHelper.setFrom((String) dataMap.get(AppConstants.FROM));
					messageHelper.setSubject((String) dataMap.get(AppConstants.SUBJECT));
					if ((dataMap.get(AppConstants.EMAIL_TYPE_TO) == null || ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_TO)).length == 0)
							&& (dataMap.get(AppConstants.EMAIL_TYPE_CC) == null || ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_CC)).length == 0)
							&& (dataMap.get(AppConstants.EMAIL_TYPE_BCC) == null || ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_BCC)).length == 0)) {
						LOG.error("[Error message]: There are no recipients.");
						return null;
					}
					if (dataMap.get(AppConstants.EMAIL_TYPE_TO) != null
							&& ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_TO)).length > 0) {
						messageHelper.setTo((String[]) dataMap.get(AppConstants.EMAIL_TYPE_TO));
					}
					if (dataMap.get(AppConstants.EMAIL_TYPE_CC) != null
							&& ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_CC)).length > 0) {
						messageHelper.setCc((String[]) dataMap.get(AppConstants.EMAIL_TYPE_CC));
					}
					if (dataMap.get(AppConstants.EMAIL_TYPE_BCC) != null
							&& ((String[]) dataMap.get(AppConstants.EMAIL_TYPE_BCC)).length > 0) {
						messageHelper.setBcc((String[]) dataMap.get(AppConstants.EMAIL_TYPE_BCC));
					}
					Context env = (Context) new InitialContext().lookup("java:comp/env");
					String emailHeaderImgUrl = (String)env.lookup(AppConstants.EMAIL_HEADER_IMG_URL);
					dataMap.put(AppConstants.EMAIL_HEADER_IMG_URL, emailHeaderImgUrl);
					String msgText = getMailText(dataMap, templatePath);
					if (!StringUtils.hasText(msgText)) {
						LOG.error("[Error message]: The content of email is empty.");
						return null;
					}
					messageHelper.setText(msgText, true);	
					
					 
					messageHelper.addAttachment("InstructorScheduleReport.xls", getDataSourceEXCEL(AppConstants.FILE_PATH,AppConstants.INSTRUCTOR_REPORT_FILE_NAME, workbook));
					messageHelper.addAttachment("InstructorScheduleReport.pdf", new ByteArrayDataSource(boat, "application/pdf"));
					
					
					
				} catch (MessagingException e) {
					LOG.error("[Error message]: Error happened when initializing email message.", e);
				} catch (NamingException e) {
					LOG.error("[Error message]: Error happened when search server URL with JNDI.", e);
				}
				catch (IOException e) {
					LOG.error("[Error message]: Error IO exception.", e);
				}
				return mailMessage;
			
	}
		
		private DataSource getDataSourceEXCEL(String filePath, String fileName, HSSFWorkbook workbook) throws IOException{
			File file = new File(filePath,fileName);//fullFilePath
	        FileOutputStream fos = new FileOutputStream(file);
	        workbook.write(fos);
	        fos.close();
			//Data source needs full file path. Should not send only file name. 
			return new FileDataSource(file);
	}
}
		
		
