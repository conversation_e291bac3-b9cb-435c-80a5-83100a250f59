/**
 * @Title: ServiceServiceImpl.java
 * @Package com.guitarcenter.scheduler.service
 * Copyright: Copyright (c) 2013 
 * Company:
 * 
 * <AUTHOR>
 * @date Sep 12, 2013 11:00:57 AM
 * @version V1.0
 */
package com.guitarcenter.scheduler.service.impl;

import java.util.LinkedList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.jfree.util.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.ActivityDAO;
import com.guitarcenter.scheduler.dao.AvailabilityDAO;
import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.LocationProfileDAO;
import com.guitarcenter.scheduler.dao.PersonRoleDAO;
import com.guitarcenter.scheduler.dao.ProfileActivityDAO;
import com.guitarcenter.scheduler.dao.ProfileServiceDAO;
import com.guitarcenter.scheduler.dao.RoleDAO;
import com.guitarcenter.scheduler.dao.ServiceDAO;
import com.guitarcenter.scheduler.dao.criterion.ActivityCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.ProfileActivityCriterion;
import com.guitarcenter.scheduler.dao.criterion.ProfileServiceCriterion;
import com.guitarcenter.scheduler.dao.criterion.ServiceCriterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dto.InstructorAccessDTO;
import com.guitarcenter.scheduler.dto.ServiceDTO;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.LocationProfile;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.PersonRole;
import com.guitarcenter.scheduler.model.ProfileActivity;
import com.guitarcenter.scheduler.model.ProfileService;
import com.guitarcenter.scheduler.model.Role;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.RequiresInstructor;
import com.guitarcenter.scheduler.service.LocationManagerService;
import com.guitarcenter.scheduler.service.PersonManagerService;
import com.guitarcenter.scheduler.service.ServiceService;
import com.guitarcenter.scheduler.service.SiteService;

/**
 * @ClassName: ServiceServiceImpl
 * <AUTHOR>
 * @date Sep 12, 2013 11:00:57 AM
 *
 */
@Service(value = "serviceService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class ServiceServiceImpl implements ServiceService {
	//GSSP-211 CHANGES
	private static final List<InstructorAccessDTO> String = null;

	@Autowired
	@Qualifier("serviceDAO")
	private ServiceDAO serviceDAO;

	@Autowired
	@Qualifier("locationProfileDAO")
	private LocationProfileDAO locationProfileDAO;
	
	@Autowired
	@Qualifier("profileActivityDAO")
	private ProfileActivityDAO profileActivityDAO;
	
	@Autowired
	@Qualifier("profileServiceDAO")
	private ProfileServiceDAO profileServiceDAO;
	
	@Autowired
	@Qualifier("activityDAO")
	private ActivityDAO activityDAO;
	//GSSP-211 CHANGES
	
	 @Autowired
	    private SiteService siteService;
	
	@Autowired
	private InstructorDAO instructorDAO;
	
	@Autowired
	 private PersonManagerService personManagerService;
	
	
	 @Autowired
	    private LocationManagerService locationManageService;
	 
	 @Autowired
	    private AvailabilityDAO availabilityDAO;
	 
	 
	  //Changes made for GSSP-211
	    @Autowired
	    private PersonRoleDAO personRoleDAO;    
	      
	    @Autowired
	    private RoleDAO roleDAO;
	 
	
	/**
	 * <p>Title: getServiceListBySiteId</p>
	 * <p>Description: </p>
	 * @param siteId
	 * @return
	 * @see com.guitarcenter.scheduler.service.ServiceService#getServiceListBySiteId(long)
	 */
	@Override
	public List<com.guitarcenter.scheduler.model.Service> getServiceListBySiteId(long siteId) {
		return serviceDAO.getServiceListBySiteId(siteId);
	}
	
	
    @Override
    public List<com.guitarcenter.scheduler.model.Service> findServiceBySite(
            final long siteID) {
        final Criterion<com.guitarcenter.scheduler.model.Service, com.guitarcenter.scheduler.model.Service> criterion = ServiceCriterion
                .findBySiteId(siteID);
        final List<com.guitarcenter.scheduler.model.Service> list = serviceDAO
                .search(criterion);

        return list;
    }

	@Transactional
	@Override
	public com.guitarcenter.scheduler.model.Service getServiceById(
			long serviceId){
		return serviceDAO.get(serviceId);
	}
	
	@Transactional
	@Override
	public com.guitarcenter.scheduler.model.Service getCentralizeService(final long serviceId){
		return serviceDAO.get(serviceId);
	}
	
	@Transactional
	@Override
	public ProfileService getProfileService(long id){
		return profileServiceDAO.get(id,DAOHelper.FETCH_SERVICE);
	}

	@Transactional
	@Override
	public void createService(com.guitarcenter.scheduler.model.Service service,
			long updatedBy) {
		serviceDAO.save(service, updatedBy);
	}
	
	@Override
	public List<ServiceDTO> getServiceDTOListBySite(long siteId) {
		List<com.guitarcenter.scheduler.model.Service> list = this.getServiceListBySiteId(siteId);
		List<ServiceDTO> dtos = new LinkedList<ServiceDTO>();
		if(null != list && !list.isEmpty()) {
			for(com.guitarcenter.scheduler.model.Service s : list) {
				dtos.add(new ServiceDTO(s.getServiceId(), s.getServiceName()));
			}
		}
		return dtos;
	}
	
	//update profile service
	@Transactional
	 @Override
	 public void updateService(ProfileService profileService,long updatedById,List<Long> activityIds,long profileId,Site site,Location location,Enabled enabled){
	  
		LocationProfile locationProfile = locationProfileDAO.get(profileId);
		profileServiceDAO.update(profileService, updatedById);
		
		//save ProfileActivity
		//update ProfileActivity
		for (Long activityId : activityIds) {
			Criterion<ProfileActivity, ProfileActivity>  criterion = ProfileActivityCriterion.getByProfileIdAndActivityId(profileId, activityId);
			ProfileActivity profileActivity = profileActivityDAO.get(criterion);
			if(profileActivity == null){
				//save
				profileActivity = new ProfileActivity();
				Activity activity = activityDAO.get(activityId);
				profileActivity.setActivity(activity);
				profileActivity.setEnabled(profileService.getEnabled());
				profileActivity.setSite(site);
				profileActivity.setLocationProfile(locationProfile);
				profileActivityDAO.save(profileActivity, updatedById);
				//if profileService from Y to N update profileActivity && !profileService.getEnable .equals (profileActivity.getEnable)---profileActivity.getEnable = Y
			}else if((Enabled.N.equals(profileService.getEnabled()) && Enabled.Y.equals(enabled)) && Enabled.Y.equals(profileActivity.getEnabled())){
				//update
				profileActivity.setEnabled(Enabled.N);
				profileActivityDAO.update(profileActivity, updatedById);
			}
		}
	 }


	//when delete centralized Service  how can do with profile service and activity and profile activity
	@Transactional
	@Override
	public void removeCentralizedService(com.guitarcenter.scheduler.model.Service service){
	
		 Criterion<Activity, Activity> criterion = ActivityCriterion.findByService(service.getServiceId());
		 List<Activity> activities = activityDAO.search(criterion);
		 for (Activity activity : activities) {
			activityDAO.delete(activity);
		}
		
		serviceDAO.delete(service);
	}
	
	//update centralize Service
	@Transactional
	@Override
	public void updateCentralizeService(ServiceDTO serviceDTO,Person updatedBy){
		
		com.guitarcenter.scheduler.model.Service service = serviceDAO.get(serviceDTO.getServiceId());
		//update to disable
		if(!serviceDTO.getEnable()){
			Criterion<Activity, Activity> criterion = ActivityCriterion.findByService(service.getServiceId());
			List<Activity> activities = activityDAO.search(criterion);
			for (Activity activity : activities) {
				if(Enabled.Y.equals(activity.getEnabled())){
					activity.setEnabled(Enabled.N);
					activityDAO.update(activity, updatedBy);
				}
			}
		}
		//global change && from enable to disable
		//update profileService to disable
		//update profileActivity to disable
		if(serviceDTO.getGlobalChange()){
			
			//global change  change profileService
			Criterion<ProfileService, ProfileService>  criterion = ProfileServiceCriterion.findByServiceIdAndEnabled(service.getServiceId(), serviceDTO.getEnable() ? Enabled.N : Enabled.Y);
			List<ProfileService> profileServices = profileServiceDAO.search(criterion);
			for (ProfileService profileService : profileServices) {
				profileService.setEnabled(serviceDTO.getEnable()?Enabled.Y:Enabled.N);
				profileServiceDAO.update(profileService, updatedBy);
				
				//if global change to disable,update profileActivity
				if(!serviceDTO.getEnable()){
					Criterion<ProfileActivity, ProfileActivity> profileCriterion = ProfileActivityCriterion.findByProfileIdAndServiceIds(profileService.getLocationProfile().getProfileId(), profileService.getService().getServiceId());
					List<ProfileActivity> profileActivities = profileActivityDAO.search(profileCriterion);
					
					for (ProfileActivity profileActivity : profileActivities) {
						if(Enabled.Y.equals(profileActivity.getEnabled())){
							profileActivity.setEnabled(Enabled.N);
							profileActivityDAO.update(profileActivity, updatedBy);
						}
					}
					
				}
			}
		}
		//update centralized service
		service.setServiceName(SystemUtil.createName(serviceDTO.getServiceName()));
		String requireInstructor = serviceDTO.getInstructor().trim();
		service.setRequiresInstructor(RequiresInstructor.N.toString().equals(requireInstructor)?RequiresInstructor.N:(RequiresInstructor.O.toString().equals(requireInstructor)?RequiresInstructor.O:RequiresInstructor.R));
		service.setEnabled(serviceDTO.getEnable()?Enabled.Y:Enabled.N);
		serviceDAO.update(service, updatedBy);
	}

	@Override
	public Boolean hasSameServiceName(String createName) {
		com.guitarcenter.scheduler.model.Service service = new com.guitarcenter.scheduler.model.Service();
		service.setServiceName(createName);
		List<com.guitarcenter.scheduler.model.Service> list = serviceDAO.search(service);
		return SystemUtil.listIsBlank(list) ? false : true;
	}


	/**
	  * <p>Title: getServiceDTOByProfileRoom</p>
	  * <p>Description: </p>
	  * @param profileId
	  * @param roomId
	  * @return
	  * @see com.guitarcenter.scheduler.service.ServiceService#getServiceDTOByProfileRoom(long, long)
	  */
	@Transactional
	@Override
	public List<ServiceDTO> getServiceDTOByProfileRoom(long profileId,
			long roomId) {
		return serviceDAO.getServiceDTOByProfileRoom(profileId, roomId);
	}
	
	@Override
	public List<ServiceDTO> loadByProfileAndInstructorAndEnabled(
			long profileId, long instructorId, Enabled enabled) {
		List<com.guitarcenter.scheduler.model.Service> list = new LinkedList<com.guitarcenter.scheduler.model.Service>();
		List<ServiceDTO> dtos = new LinkedList<ServiceDTO>();
		try {
			Criterion<com.guitarcenter.scheduler.model.Service, com.guitarcenter.scheduler.model.Service> criterion = ServiceCriterion.findByProfileIdAndInstructorIdAndEnabled(profileId, instructorId, enabled);
			list = serviceDAO.search(criterion);
		} catch (Exception e) {
			Log.error("Caught exception {} when load service list By ProfileAndInstructorAndEnabled", e);
		}
		for(com.guitarcenter.scheduler.model.Service s : list) {
			dtos.add(new ServiceDTO(s.getServiceId(), s.getServiceName()));
		}
		return dtos;
	}
	
	@Override
	public List<ServiceDTO> loadByProfileAndRoomAndEnabled(long profileId,
			long roomId, Enabled enabled) {
		List<com.guitarcenter.scheduler.model.Service> list = new LinkedList<com.guitarcenter.scheduler.model.Service>();
		List<ServiceDTO> dtos = new LinkedList<ServiceDTO>();
		try {
			Criterion<com.guitarcenter.scheduler.model.Service, com.guitarcenter.scheduler.model.Service> criterion = ServiceCriterion.findByProfileIdAndRoomIdAndEnabled(profileId, roomId, enabled);
			list = serviceDAO.search(criterion);
		} catch (Exception e) {
			Log.error("Caught exception {} when load service list By ProfileAndRoomAndEnabled", e);
		}
		
		for(com.guitarcenter.scheduler.model.Service s : list) {
			dtos.add(new ServiceDTO(s.getServiceId(), s.getServiceName()));
		}
		return dtos;
	}


	//GSSP-211 CHANGES
	
	@Override
	public List<Location> findLocation() {
		
		List<com.guitarcenter.scheduler.model.Location> list = new LinkedList<Location>();
	
		try {
			Criterion<com.guitarcenter.scheduler.model.Service, Location> criterion = ServiceCriterion.findLocation();
			list = serviceDAO.search(criterion);
		} catch (Exception e) {
			Log.error("Caught exception {} when load location ny find location", e);
		}		

		return list;
		
	}
	
	
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
	@Override
	public boolean createDualInstructor(String externalId, String locationId, Instructor instructor,Person person)  {
		
	try
	{
		
		
		  List<Site> sites = siteService.findSitesByExternalId("GCS");
	      Site updateSite = sites.get(0);
	        
	        
		Location location =
		            locationManageService.findById(new Long(locationId));
		 
		 
		Instructor dualInstructor = new Instructor();
		dualInstructor.setSite(updateSite);
		dualInstructor.setExternalId(externalId);		                
		dualInstructor.setLocation(location);
		dualInstructor.setPerson(instructor.getPerson());
		dualInstructor.setAvailability(new Availability());
		dualInstructor.getAvailability().setSite(updateSite);		               
		dualInstructor.setExternalSource("GCSS");        
		dualInstructor.setEnabled(Enabled.Y);
		dualInstructor.setUpdatedBy(person);
		dualInstructor.setStatus("A");
		
		 Role role = new Role();                  
         role.setRoleName(AppConstants.INSTRUCTOR_STRING);                     
         if (StringUtils.isNotBlank(role.getRoleName())) {
             role = roleDAO.get(role);
         } else {
             role = null;
         }
         
         if (role != null) {
             PersonRole personRole = new PersonRole();
             personRole.setLocation(location);
             personRole.setPerson(instructor.getPerson());
             personRole.setRole(role);
             personRole.setSite(updateSite);
             personRoleDAO.save(personRole,
                                personManagerService.getSystemUpdatePerson());
         }
   
                         
     availabilityDAO.save(dualInstructor.getAvailability(),
    		 person);
     instructorDAO.save(dualInstructor,
    		 			person);
     
	}
	catch(Exception e)
	{
		Log.error("Caught exception {} while updating dual instructor", e);
		return false;
	}
    	return true;
	}
	
	
	
	
}
