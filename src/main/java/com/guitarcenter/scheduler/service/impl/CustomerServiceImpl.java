package com.guitarcenter.scheduler.service.impl;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.net.ssl.SSLContext;

import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.apache.http.util.EntityUtils;


import org.apache.commons.lang.StringUtils;
import org.apache.lucene.queryparser.xml.builders.TermQueryBuilder;
import org.apache.solr.client.solrj.SolrServerException;
import org.elasticsearch.ElasticsearchGenerationException;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.guitarcenter.scheduler.common.exceptions.BizRuntimeException;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.ElasticConnecation;
import com.guitarcenter.scheduler.common.util.RestTemplateSingleton;
import com.guitarcenter.scheduler.common.util.SingletonElasticConnection;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.CustomerDAO;
import com.guitarcenter.scheduler.dao.CustomerHistoryDAO;
import com.guitarcenter.scheduler.dao.CustomerStatusDAO;
import com.guitarcenter.scheduler.dao.InstrumentDAO;
import com.guitarcenter.scheduler.dao.ParentDetailsDAO;
import com.guitarcenter.scheduler.dao.PersonDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.CustomerCriterion;
import com.guitarcenter.scheduler.dao.criterion.CustomerStatusCriterion;
import com.guitarcenter.scheduler.dao.criterion.InstrumentCriterion;
import com.guitarcenter.scheduler.dao.criterion.dto.CustomerAppointmentDTO;
import com.guitarcenter.scheduler.dao.criterion.dto.InstructorLessonLinkDTO;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dao.util.PGPUtils;
import com.guitarcenter.scheduler.dto.CustomerAppointmentsQueryDTO;
import com.guitarcenter.scheduler.dto.CustomerDTO;
import com.guitarcenter.scheduler.dto.CustomerDetailDTO;
import com.guitarcenter.scheduler.dto.CustomerRegistrationRequest;
import com.guitarcenter.scheduler.dto.CustomerResetPasswordLink;
import com.guitarcenter.scheduler.dto.InstrumentDTO;
import com.guitarcenter.scheduler.dto.LocationDistRegDTO;
import com.guitarcenter.scheduler.dto.RegistedCustomerEmailDTO;
import com.guitarcenter.scheduler.dto.RegistrationResponse;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.CustomerHistory;
import com.guitarcenter.scheduler.model.CustomerStatus;
import com.guitarcenter.scheduler.model.Instrument;
import com.guitarcenter.scheduler.model.ParentDetails;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.service.AppointmentEmailService;
import com.guitarcenter.scheduler.service.AppointmentSeriesService;
import com.guitarcenter.scheduler.service.AppointmentService;
import com.guitarcenter.scheduler.service.CustomerRegistrationService;
import com.guitarcenter.scheduler.service.CustomerService;
import com.guitarcenter.scheduler.service.GetInstructorAvailabiltyService;
import com.guitarcenter.scheduler.service.LocationManagerService;
import com.guitarcenter.scheduler.service.PasswordGeneratorService;
import com.guitarcenter.scheduler.service.PersonManagerService;
import com.guitarcenter.scheduler.service.SearchService;
import com.guitarcenter.scheduler.service.SessionConfirmationService;
import com.guitarcenter.scheduler.service.SiteService;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceResponseDTO;

@Service("customerService")
public class CustomerServiceImpl implements CustomerService {

	private static final Logger LOG = LoggerFactory.getLogger(CustomerServiceImpl.class);

	@Autowired
	private CustomerDAO customerDAO;

	@Autowired
	private AppointmentEmailService appointmentEmailService;

	@Autowired
	private PersonDAO personDAO;

	@Autowired
	private ParentDetailsDAO parentDetailsDAO;

	@Autowired
	private CustomerStatusDAO customerStatusDAO;

	@Autowired
	private CustomerHistoryDAO customerHistoryDAO;

	@Autowired
	private InstrumentDAO instrumentDAO;

	@Autowired
	private SearchService searchService;

	@Autowired
	private SiteService siteService;

	@Autowired
	private PersonManagerService personManagerService;

	@Autowired
	private AppointmentSeriesService appointmentSeriesService;

	@Autowired
	private AppointmentService appointmentService;

	@Autowired
	private CustomerRegistrationService customerRegistrationService;

	@Autowired
	private SessionConfirmationService sessionConfirmationService;

	@Autowired
	private PasswordGeneratorService passwordGeneratorService;

	@Autowired
	@Qualifier("getInstructorAvailabiltyService")
	private GetInstructorAvailabiltyService getInstructorAvailabiltyService;
	
	@Value("${adp.posPromptLessonsBaseUrl}")
    private String posPromptLessonsBaseUrl;
	
	@Value("${adp.posPromptLessonsApiKey}")
    private String posPromptLessonsApiKey;
	
	// String posLessonsBaseUrl = "https://pkq021w8oe.execute-api.us-west-2.amazonaws.com/GuitarCenter";
	   //     String posLessonsApiKey = "MUmNCFmdC26af2Q0Ja0XZ8rSJUN7vnj87xaRnLQj";

	

	private String generatedPassword;
	private String sessionConfirmationNumber;

	private RestHighLevelClient restHighLevelClient;
	// private ElasticConnecation ec = new ElasticConnecation();

	//private String elasticIndex = "lessons_associate_capture_qa";// gc_115605_737

	@Override
	public List<Customer> getAllCustomer() {
		if (LOG.isDebugEnabled()) {
			LOG.debug("CustomerService.getAllCustomer: start");
		}
		List<Customer> customers = new ArrayList<Customer>();
		try {
			customers = customerDAO.searchAll(DAOHelper.FETCH_PERSON);
		} catch (Exception e) {
			LOG.error("CustomerService.getAllCustomer: error");
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("CustomerService.getAllCustomer: start");
		}
		return customers;
	}

	@Override
	public List<Customer> searchCustomerByName(String name) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("CustomerService.searchCustomer: start");
		}
		List<Customer> customers = new LinkedList<Customer>();
		try {
			Criterion<Customer, Customer> criterion = CustomerCriterion.likeByName(name);
			customers = customerDAO.search(criterion, DAOHelper.FETCH_PERSON);
		} catch (Exception e) {
			LOG.error("CustomerService.searchCustomer: error");
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("CustomerService.searchCustomer: end");
		}
		return customers;
	}

	@Override
	public List<CustomerDTO> loadCustomerListBySite(long siteId) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("Begin to serach customer list by siteId:{} from CustomerService.getCustomerListBySite", siteId);
		}
		List<CustomerDTO> dtos = new LinkedList<CustomerDTO>();
		List<Customer> list;
		try {
			Criterion<Customer, Customer> criterion = CustomerCriterion.findAll(siteId);
			list = customerDAO.search(criterion, DAOHelper.FETCH_PERSON | DAOHelper.FETCH_CUSTOMER_STATUS);
			if (LOG.isDebugEnabled()) {
				LOG.debug("Got Customer list:{} from CustomerService.getCustomerListBySite", list);
			}
			if (null != list && !list.isEmpty()) {
				for (Customer c : list) {
					CustomerDTO customerDto = new CustomerDTO();
					customerDto.setRecordId(c.getCustomerId());
					customerDto.setEmail(c.getPerson().getEmail());
					customerDto.setFullName(c.getPerson().getFirstName(), c.getPerson().getLastName());
					customerDto.setPhone(c.getPerson().getPhone());
					customerDto.setExternalId(c.getExternalId());
					String statCode = c.getCustomerStatus().getExternalId();
					customerDto.setStatus(statCode);
					dtos.add(customerDto);
				}
			}
		} catch (Exception e) {
			LOG.error("Caught {} from CustomerService.getCustomerListBySite", e);
		}
		return dtos;
	}

	@Override
	public CustomerDTO getCustomerById(long customerId) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("Began to get customer list by customerId:{} from CustomerService.getCustomerById", customerId);
		}
		Customer customer = null;
		try {
			customer = customerDAO.get(customerId, DAOHelper.FETCH_PERSON | DAOHelper.FETCH_CUSTOMER_STATUS);
		} catch (Exception e) {
			LOG.error("Caught {} when getting a customer by {}", e, customerId);
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("Got the loaded customer {} from CustomerService.getCustomerById", customer);
		}
		CustomerDTO dto = new CustomerDTO();
		if (null != customer) {
			dto.setRecordId(customer.getCustomerId());
			dto.setCustomerStatus(customer.getCustomerStatus().getExternalId());
			dto.setEmail(customer.getPerson().getEmail());
			dto.setExternalId(customer.getExternalId());
			String firstName = customer.getPerson().getFirstName();
			String lastName = customer.getPerson().getLastName();
			dto.setFullName(firstName, lastName);
			dto.setPhone(customer.getPerson().getPhone());
		}
		return dto;
	}

	public CustomerDetailDTO getCustomerDetailById(Long customerId) {
		if (customerId == null) {
			throw new BizRuntimeException("generate customer detail DTO failed, customerId is null");
		}
		Customer customer = customerDAO.get(customerId, DAOHelper.FETCH_PERSON | DAOHelper.FETCH_CUSTOMER_STATUS);
		if (customer == null) {
			throw new BizRuntimeException("fetch customer by id failed");
		}
		CustomerDetailDTO detailDTO = new CustomerDetailDTO();
		detailDTO.setGcId(customer.getCustomerId());
		detailDTO.setCustomerExternalId(customer.getExternalId());
		detailDTO.setPhoneNumber(customer.getPerson().getPhone());
		detailDTO.setLastBooked(customer.getLastBooked());
//	    detailDTO.setBirthday(null);
//	    detailDTO.setHeadSculpturePath(null);
		String firstName = customer.getPerson().getFirstName();
		String lastName = customer.getPerson().getLastName();
		detailDTO.generateAndSetFullName(firstName, lastName);
		detailDTO.setEmail(customer.getPerson().getEmail());
//        detailDTO.setFutureAppointments(null);

		if (!StringUtils.isEmpty(customer.getLessonCounts())) {
			detailDTO.setLessonsCount(Integer.parseInt(customer.getLessonCounts()));
		} else {
			detailDTO.setLessonsCount(0);
		}
		return detailDTO;
	}

	@Override
	public List<CustomerAppointmentDTO> getCustomerAppointments(CustomerAppointmentsQueryDTO queryDTO) {

		List<CustomerAppointmentDTO> custUIDTOList = new ArrayList<CustomerAppointmentDTO>();
		Date startQueryDate = new Date();
		Date endQueryDate = new Date();
		try {
			startQueryDate = new SimpleDateFormat("MM/dd/yyyy").parse(queryDTO.getStartDatetime());
			endQueryDate = new SimpleDateFormat("MM/dd/yyyy").parse(queryDTO.getEndDatetime());
		} catch (Exception e) {
			LOG.info("Query Date Parser exception :" + e.getMessage());

		}
		String queryDateStrStart = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(startQueryDate);
		String queryDateStrEnd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(endQueryDate);

		DateTime startDateTime = DateTimeUtil.handleStartDateTime(queryDateStrStart);
		DateTime endDateTime = DateTimeUtil.handleendDateTime(queryDateStrEnd);
		Date queryEndDate = endDateTime.toDate();
		Date queryStartDate = startDateTime.toDate();
		List<CustomerAppointmentDTO> custAppointmentsDTOList = appointmentService
				.getAppointmentBookByCustomerIdAndDate(queryDTO.getCustomerId(), queryStartDate, queryEndDate);
		List<CustomerAppointmentDTO> result = custAppointmentsDTOList.stream()
				.collect(Collectors.groupingBy(CustomerAppointmentDTO::getAppointmentId,
						Collectors.maxBy(Comparator.comparing(CustomerAppointmentDTO::getZoomMeetingID))))
				.values().stream().map(Optional::get).collect(Collectors.toList());

		Comparator<CustomerAppointmentDTO> byDate = Comparator.comparing(CustomerAppointmentDTO::getStartTimeDate);
		// result.sort(byDate);

		for (CustomerAppointmentDTO custAppointForDBDTO : result) {
			CustomerAppointmentDTO custAppointForUIDTO = new CustomerAppointmentDTO();
			custAppointForUIDTO.setZoomMeetingID(custAppointForDBDTO.getZoomMeetingID());
			custAppointForUIDTO.setServiceId(custAppointForDBDTO.getServiceId());
			custAppointForUIDTO.setJoinURL(custAppointForDBDTO.getJoinURL());
			custAppointForUIDTO.setEnabledStatus(custAppointForDBDTO.isEnabledStatus());
			custAppointForUIDTO.setStatus(custAppointForDBDTO.getStatus());
			custAppointForUIDTO.setEnabledNotes(custAppointForDBDTO.isEnabledNotes());
			custAppointForUIDTO.setEnabledComments(custAppointForDBDTO.isEnabledComments());
			custAppointForUIDTO.setActivityName(custAppointForDBDTO.getActivityName());
			custAppointForUIDTO.setAppointmentId(custAppointForDBDTO.getAppointmentId());
			custAppointForUIDTO.setComments(custAppointForDBDTO.getComments());
			custAppointForUIDTO.setCustomerFirstName(custAppointForDBDTO.getCustomerFirstName());
			custAppointForUIDTO.setCustomerLastName(custAppointForDBDTO.getCustomerLastName());
			custAppointForUIDTO.setInstructorId(custAppointForDBDTO.getInstructorId());
			custAppointForUIDTO.setCustomerId(custAppointForDBDTO.getCustomerId());
			custAppointForUIDTO.setStudentNotes(custAppointForDBDTO.getStudentNotes());
			custAppointForUIDTO.setDuration(custAppointForDBDTO.getDuration());
			custAppointForUIDTO.setInstructorFirstName(custAppointForDBDTO.getInstructorFirstName());
			custAppointForUIDTO.setInstructorLastName(custAppointForDBDTO.getInstructorLastName());
			custAppointForUIDTO.setInstructorId(custAppointForDBDTO.getInstructorId());
			custAppointForUIDTO.setLocation(custAppointForDBDTO.getLocation());
			custAppointForUIDTO.setLocationId(custAppointForDBDTO.getLocationId());
			custAppointForUIDTO.setStartDate(custAppointForDBDTO.getStartDate());
			custAppointForUIDTO.setStartTime(custAppointForDBDTO.getStartTime());
			custAppointForUIDTO.setEndTime(custAppointForDBDTO.getEndTime());
			custAppointForUIDTO.setEndTimeDate(custAppointForDBDTO.getEndTimeDate());
			custAppointForUIDTO.setStartTimeDate(custAppointForDBDTO.getStartTimeDate());
			custAppointForUIDTO.setUtcStartTime(custAppointForDBDTO.getUtcStartTime());
			custAppointForUIDTO.setUtcEndTime(custAppointForDBDTO.getUtcEndTime());
			custAppointForUIDTO.setLocationTimezone(custAppointForDBDTO.getLocationTimezone());
			// #####Send Remainder Email button#############
			if (!custAppointForUIDTO.getServiceId().equals("20") && DateTimeUtil.isStartDateAfterCurrentDate(
					custAppointForUIDTO.getEndTimeDate(), custAppointForUIDTO.getLocationTimezone())) {
				custAppointForUIDTO.setEnabledSendRemainder(true);
			} else if (custAppointForUIDTO.getServiceId().equals("20")
					&& DateTimeUtil.isStartDateAfterCurrentDate(custAppointForUIDTO.getEndTimeDate(),
							custAppointForUIDTO.getLocationTimezone())
					&& DateTimeUtil.isValidZoomLinkTimeStamp1(custAppointForUIDTO.getEndTimeDate(),
							custAppointForUIDTO.getLocationTimezone(), custAppointForUIDTO.getUtcEndTime())) {
				custAppointForUIDTO.setEnabledSendRemainder(true);
			} else {
				custAppointForUIDTO.setEnabledSendRemainder(false);
			}

			custUIDTOList.add(custAppointForUIDTO);
			custUIDTOList.sort(byDate);
		}
		for (CustomerAppointmentDTO dates : custUIDTOList) {
			LOG.error(dates.getStartDate());
		}
		return custUIDTOList;
	}

	@Override
	public boolean sendEmailReminderToCustomer(CustomerAppointmentDTO dto) {

		InstructorLessonLinkDTO instDto = new InstructorLessonLinkDTO();
		instDto.setAppointmentId(dto.getAppointmentId());
		instDto.setCustomerId(dto.getCustomerId());
		instDto.setJoinURL(dto.getJoinURL());
		instDto.setServiceId(dto.getServiceId());

		try {
			if (dto.getServiceId().equalsIgnoreCase("20")) {
				appointmentEmailService.sendEmailForCustomerReminder(Long.parseLong(dto.getAppointmentId()),
						Long.parseLong(dto.getCustomerId()), dto.getJoinURL());
			} else {
				appointmentEmailService.sendEmailForCustomerReminderInStore(instDto);
			}
		} catch (Exception e) {
			LOG.error("email send failed", e);
			return false;
		}
		return true;

	}

	/**
	 * Return a collection of Customer records for the site id provided that have a
	 * matching external id value.
	 * 
	 * Note: this is returning a collection simply because there is not a database
	 * constraint on the external identifiers; it is possible that multiple
	 * customers *could* be updated from a single external record in the future.
	 * 
	 * @param siteId     identifier for the site that results must be restricted to
	 * @param externalId String containing the external id to match
	 * @return List of Customer instances that match the external id in the site
	 *         provided
	 */
	@Override
	public Customer getCustomerByExternalId(long siteId, String externalId) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("Searching for customer with external id {} and site id {}", externalId, siteId);
		}
		Criterion<Customer, Customer> criterion = CustomerCriterion.getByExternalId(siteId, externalId);
		Customer customer = customerDAO.get(criterion, DAOHelper.FETCH_PERSON);
		if (LOG.isDebugEnabled()) {
			LOG.debug("Returning customer {}", customer);
		}
		return customer;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
	public void saveCustomerHistory(CustomerDTO update) throws SolrServerException, IOException {

		try {
			CustomerHistory ch = new CustomerHistory();
			ch.setBadgeNumber(update.getBadgeNumber());
			ch.setUpdated(new Date());
			ch.setUpdatedBy(1l);
			ch.setSiteId(1l);
			ch.setCustomerStatus(update.getCustomerStatus());
			ch.setExternalId(update.getExternalId());
			ch.setLocation_external_id(update.getLocation_external_id());
			ch.setLessonCounts(update.getLessonCounts());
			if (update.getLastBookedDate() != null || !"".equals(update.getLastBookedDate())) {
				ch.setLastBooked(DateTimeUtil.stringToDate(update.getLastBookedDate(), "MM-dd-yyyy"));
			} else {
				ch.setLastBooked(null);
			}
			ch.setParentFullName(update.getFullName());
			ch.setFirstName(update.getFirstName());
			ch.setLastName(update.getLastName());
			ch.setEmail(update.getEmail());
			ch.setPhone(update.getPhone());
			// System.out.println(DateTimeUtil.stringToDate(update.getPosUpdated(),
			// "dd-MMM-yy HH:mm:ss"));
			// ch.setPosUpdated(DateTimeUtil.stringToDate(update.getPosUpdated(), "dd-MMM-yy
			// HH:mm:ss"));
			if (update.getPosUpdated() != null || !"".equals(update.getPosUpdated())) {
				ch.setPosUpdated(DateTimeUtil.stringToDate(update.getPosUpdated(), "dd-MMM-yy HH:mm:ss"));
			} else {
				ch.setPosUpdated(null);
			}
			ch.setContractDay(update.getContractDay());
			customerHistoryDAO.saveCustomerHistory(ch);
		} catch (Exception e) {
			LOG.error("Exception While saveing History table ==>  " + e);
		}
	}

	/**
	 * Create or updates an Scheduler Customer record with values taken from the
	 * supplied CustomerDTO record. Any exceptions are passed up to caller for
	 * decision and handling.
	 * 
	 * The method will retrieve an existing customer record from persistent storage
	 * that matches the external id of the update record.
	 * 
	 * @param update a CustomerDTO instance containing values to update
	 * @throws IOException
	 * @throws SolrServerException
	 */
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
	@Override
	public void updateFromExternal(CustomerDTO update) throws SolrServerException, IOException {
		if (StringUtils.isBlank(update.getPosUpdated()))
			update.setPosUpdated(null);
		if (StringUtils.isBlank(update.getLastBookedDate()))
			update.setLastBookedDate(null);
		LOG.error("Info Loading create CustomerDTO Tibco  dto ==>  " + update);
		try {
			saveCustomerHistory(update);
		} catch (Exception e) {
			LOG.error("Info ERROR  create Customer HISTORY FAILED DTO    dto ==>  " + e);
		}

		// System.out.println("Info Loading create CustomerDTO Tibco dto ==> "+ update);
		if (LOG.isDebugEnabled()) {
			LOG.debug("about to attempt an update for customer {}", update);
		}
		if (update == null) {
			LOG.warn("a null customer instance was passed to updateFromExternal");
			return;
		}

		if (StringUtils.isBlank(update.getSite())) {
			if (LOG.isInfoEnabled()) {
				LOG.info("updated customer {} does not have a site string");
			}
			return;
		}
		/*
		 * Find the site that matches the external id provided by update record.
		 */
		List<Site> sites = siteService.findSitesByExternalId(update.getSite());
		if (sites.size() > 1) {
			LOG.warn("updated customer site external id {} matches too " + "many sites; ignoring record",
					update.getSite());
			return;
		}

		if (sites.isEmpty()) {
			if (LOG.isInfoEnabled()) {
				LOG.info("updated customer site external id {} does not match " + "an existing site; ignoring record",
						update.getSite());
			}
			return;
		}
		Site updateSite = sites.get(0);

		/*
		 * Make sure there is a valid customer status in the update.
		 */
		// -GSSP -388 Customer Status empty to T status-Starts
		if ("".equals(update.getCustomerStatus())) {
			update.setCustomerStatus("T");
		}
		// -GSSP -388 Customer Status empty to T status-Ends
		CustomerStatus customerStatus = getCustomerStatusByExternalId(updateSite.getSiteId(),
				update.getCustomerStatus());
		if (customerStatus == null) {
			LOG.warn("invalid customer status '{}' in record {} from {}", update.getCustomerStatus(),
					update.getExternalId(), update.getExternalSource());
			return;
		}
		/*
		 * Now try to find an existing Customer record to update
		 */
		if (StringUtils.isBlank(update.getExternalId())) {
			LOG.warn("updated customer {} does not have an external id; " + "ignoring record", update);
			return;
		}
		Customer customer = getCustomerByExternalId(updateSite.getSiteId(), update.getExternalId());

		Long id = 0L;
		Long idSave = 0L;
		boolean flagUpdate = false;
		if (customer != null) {
			/*
			 * Update an existing Customer from the DTO
			 */

			if (customer.getPosUpdated() == null && update.getPosUpdated() != null) {
				customer.setPosUpdated(DateTimeUtil.stringToDate(update.getPosUpdated(), "dd-MMM-yy HH:mm:ss"));
				flagUpdate = true;
			}
			if (customer.getPosUpdated() != null && update.getPosUpdated() == null) {
				customer.setPosUpdated(null);
				flagUpdate = true;
			}
			if (customer.getPosUpdated() != null && update.getPosUpdated() != null) {
				if (customer.getPosUpdated()
						.compareTo(DateTimeUtil.stringToDate(update.getPosUpdated(), "dd-MMM-yy HH:mm:ss")) == -1) {
					customer.setPosUpdated(DateTimeUtil.stringToDate(update.getPosUpdated(), "dd-MMM-yy HH:mm:ss"));
					flagUpdate = true;
				}
			}
			if (flagUpdate) {

				Long parentId = null;
				ParentDetails prntId = customerDAO.getParentIdFromCustomerTable(customer.getCustomerId());
				if (LOG.isDebugEnabled()) {
					LOG.debug("found a record; updating {}", customer);
				}
				if (prntId != null)
					parentId = prntId.getParentId();
				// ---GSSP-381 Capture latest Auto billing period of Customer
				if ("A".equals(update.getCustomerStatus())
						&& !"A".equals(customer.getCustomerStatus().getExternalId())) {
					customer.setSubscriptionStartDate(new Date());
					customer.setSubscriptionEndDate(null);
					// set Start Date at Update,null at End Date
				}
				if (!"A".equals(update.getCustomerStatus()) && "A".equals(customer.getCustomerStatus().getExternalId())
						&& customer.getSubscriptionStartDate() != null) {
					customer.setSubscriptionEndDate(new Date());
					// set End Date
				}
				if (CustomerStatus.CUSTOMER_STATUS_CANCELLED.equals(customerStatus.getExternalId())
						&& "A".equals(customer.getCustomerStatus().getExternalId())
						&& customer.getSubscriptionStartDate() != null) {
					customer.setSubscriptionEndDate(new Date());
					// set Final End Date
				}
				updateCustomerFromDTO(customer, update);
				customer.setCustomerStatus(customerStatus);
				customer.setContractDay(update.getContractDay());
				// ---------------------GSSP-438----------
				LOG.error("customer.getPosUpdated() " + customer.getPosUpdated());
				// if(update.getPosUpdated() != null)System.out.println("tibco
				// update.getPosUpdated(). ()
				// "+DateTimeUtil.stringToDate(update.getPosUpdated(), "dd-MMM-yy HH:mm:ss"));

				// ---------------------
				personDAO.update(customer.getPerson(), personManagerService.getSystemUpdatePerson());

				CustomerDetailDTO custDTO = customerDAO.getCustomerDetailsById(customer.getCustomerId());

				if (parentId != null) {
					// updateCustomerParentFromDTO(customer, update);
					ParentDetails parentDetails = new ParentDetails();
					if (StringUtils.isNotBlank(update.getParentFullName())) {
						parentDetails.setFullName(update.getParentFullName());
					}

					if (StringUtils.isNotBlank(update.getExternalSource())) {
						parentDetails.setExternalSource(update.getExternalSource());
					}
					parentDetails.setParentId(parentId);
					parentDetails.setVersion(prntId.getVersion());
					if (custDTO.getSecondaryEmail() != null || !"".equals(custDTO.getSecondaryEmail())) {
						parentDetails.setSecondaryEmail(custDTO.getSecondaryEmail());
					}
					parentDetailsDAO.update(parentDetails, personManagerService.getSystemUpdatePerson());
				} else if (update.getParentFullName() != null) {
					ParentDetails pd = new ParentDetails();
					pd = createCustomerParentFromDTO(update);
					if (custDTO.getSecondaryEmail() != null || !"".equals(custDTO.getSecondaryEmail())) {
						pd.setSecondaryEmail(custDTO.getSecondaryEmail());
					}
					id = parentDetailsDAO.save(pd, personManagerService.getSystemUpdatePerson());
					pd.setParentId(id);
					customer.setParentId(pd);
				}

				customerDAO.update(customer, personManagerService.getSystemUpdatePerson());

				/*
				 * If this is an updated customer and the status is Cancelled, make sure all
				 * record clean-up is completed.
				 * 
				 * Note: not called for new customers since they have no existing records,
				 * cancelled or not.
				 */
				if (CustomerStatus.CUSTOMER_STATUS_CANCELLED.equals(customerStatus.getExternalId())) {
					handleCancelledCustomer(customer);
				}
				if (customer != null && customer.getCustomerId() != null) {
					searchService.updateRecord(customer);
				}

			}
		} else {
			/*
			 * Need to create a new Customer record and populate with DTO
			 */

			customer = createCustomerFromDTO(update, updateSite);
			customer.setCustomerStatus(customerStatus);
			idSave = personDAO.save(customer.getPerson(), personManagerService.getSystemUpdatePerson());
			if (update.getParentFullName() != null) {
				ParentDetails pd = new ParentDetails();
				pd = createCustomerParentFromDTO(update);
				id = parentDetailsDAO.save(pd, personManagerService.getSystemUpdatePerson());
				pd.setParentId(id);
				customer.setParentId(pd);

			}

			// ---GSSP-381 Capture latest Auto billing period of Customer
			if ("A".equals(update.getCustomerStatus())) {
				customer.setSubscriptionStartDate(new Date());
			}
			// ---GSSP-380 Customer Sign-In date capture
			customer.setCustomerCreated(new Date());
			if (update.getPosUpdated() != null)
				customer.setPosUpdated(DateTimeUtil.stringToDate(update.getPosUpdated(), "dd-MMM-yy HH:mm:ss"));
			customerDAO.save(customer, personManagerService.getSystemUpdatePerson());

			
			//========================moving inside save block---start
			// Create ATG account API call.

			if (customer != null && customer.getCustomerId() != null && customer.getPerson() != null
					&& customer.getPerson().getEmail() != null) {
				// LOG.error("Info Start registedCustomerandSendResetPassEmail -->"+
				// customer.getCustomerId());
				LOG.error(" registedCustomerandSendResetPassEmail  -->" + customer.getPerson().getEmail());
				try {
					registedCustomerandSendResetPassEmail(customer);
				} catch (Exception e) {
					LOG.error("Exception  registedCustomerandSendResetPassEmail  -->" + customer.getExternalId());
					e.printStackTrace();
				}
			}

			// TODO:: /OLL-3508 1.Check Activate Billled Customer or not , if trail customer
			// ignore it. 2.Get the ES Connection 3. Update the customer data to ES.

			// 1.
			// List of allowed customer statuses
			// customer.getPerson().setEmail("<EMAIL>");
			
			//posPromptLessonsBaseUrl = "https://samapi-qa.guitarcenter.com";
			//posPromptLessonsApiKey = "pF72NPKIT42So6Nau75Yf4jHSjDhYlme4hwUflCV";
			if (customer != null && customer.getCustomerId() != null && customer.getPerson() != null
					&& customer.getPerson().getEmail() != null
					&& (customer.getCustomerStatus().getCustomerStatusId() == 0l
					|| customer.getCustomerStatus().getCustomerStatusId() == 40l
					|| customer.getCustomerStatus().getCustomerStatusId() == 41l
					|| customer.getCustomerStatus().getCustomerStatusId() == 42l
					|| customer.getCustomerStatus().getCustomerStatusId() == 20l)) {
				
				 try {
			            // Build custom SSLContext that trusts all certificates
			            SSLContext sslContext = new SSLContextBuilder()
			                    .loadTrustMaterial(null, (TrustStrategy) (chain, authType) -> true)
			                    .build();

			            // Create HttpClient with custom SSLContext and NoopHostnameVerifier
			            try (CloseableHttpClient httpClient = HttpClients.custom()
			                    .setSSLContext(sslContext)
			                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
			                    .build()) {

			                // Construct JSON request body using customer object
			                Map<String, Object> requestBody = new HashMap<>();
			                requestBody.put("studentId", customer.getExternalId());
			                requestBody.put("studentEmail", customer.getPerson() != null ? customer.getPerson().getEmail() : null);
			                requestBody.put("studentFirstName", customer.getPerson() != null ? customer.getPerson().getFirstName() : null);
			                requestBody.put("studentLastName", customer.getPerson() != null ? customer.getPerson().getLastName() : null);
			                requestBody.put("parentName", customer.getParentId() != null ? customer.getParentId().getFullName() : null);
			                
							/*
							 * requestBody.put("studentId", "9883909974"); requestBody.put("studentEmail",
							 * "<EMAIL>"); requestBody.put("studentFirstName", "MARTY");
							 * requestBody.put("studentLastName", "OLIVAS"); requestBody.put("parentName",
							 * null);
							 */

			                // Convert Java Object to JSON
			                ObjectMapper objectMapper = new ObjectMapper();
			                String jsonRequestBody = objectMapper.writeValueAsString(requestBody);

			                
			                //QA url and key 
			                //posPromptLessonsBaseUrl="https://samapi-qa.guitarcenter.com";
			                // posPromptLessonsApiKey="pF72NPKIT42So6Nau75Yf4jHSjDhYlme4hwUflCV";
			                	
			                // Create HttpPost request
			                HttpPost httpPost = new HttpPost(posPromptLessonsBaseUrl + "/lessons-associate-capture?service=gcss");
			                httpPost.setHeader("Content-Type", "application/json");
			                httpPost.setHeader("x-api-key", posPromptLessonsApiKey);
			                httpPost.setEntity(new StringEntity(jsonRequestBody));
			                LOG.error("CustomerServiceImple HttpPost Request Body: " + jsonRequestBody);

			                // Execute request
			                try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
			                    String responseBody = EntityUtils.toString(response.getEntity());
			                    LOG.error("CustomerServiceImple HttpPost Response: " + responseBody);
			                }
			                catch (Exception e) {
					            LOG.error("CustomerServiceImple CloseableHttpResponse 3  Exception: " + e.toString());
					        }
			            }catch (Exception e) {
				            LOG.error("CustomerServiceImple SSLContext 2 Exception: " + e.toString());
				        }
			        } catch (Exception e) {
			            LOG.error("CustomerServiceImple HttpPost 1 Exception: " + e.toString());
			        }
				 
				
				//END
				

				

			}
			//-----------------------Moving inside save block---end
		}

		/*
		 * Update was successful so make sure search engine has the latest data on
		 * record.
		 */

		if (customer != null && customer.getCustomerId() != null) {
			 searchService.updateRecord(customer);
		}


		if (LOG.isDebugEnabled()) {
			LOG.debug("update completed for customer {}", update);
		}

	}

	
	



	private void registedCustomerandSendResetPassEmail(Customer customer) {
		// TODO Auto-generated method stub

		// TODO:: Once Customer is created Successfully in GCSS DB
		// 1.Create the account by Calling API>>
		// 2.Once created the account succuesllfy , Send email tot the customer email
		// with login details.
		// 3.If failure case , send email to create account manually , This step need to
		// confirm.
		LOG.error("Info  registedCustomerandSendResetPassEmail method  starts   ");
		//
		CustomerRegistrationRequest customerRegistrationRequest = new CustomerRegistrationRequest();
		RestTemplate restTemplate = null;
		if (null != customer.getPerson() && null != customer.getPerson().getEmail()
				&& (customer.getCustomerStatus().getCustomerStatusId() == 0l
						|| customer.getCustomerStatus().getCustomerStatusId() == 40l
						|| customer.getCustomerStatus().getCustomerStatusId() == 41l
						|| customer.getCustomerStatus().getCustomerStatusId() == 42l
						|| customer.getCustomerStatus().getCustomerStatusId() == 20l)) {// And Validate not a trail
																						// customer
			// Create Session

			try {
				restTemplate = RestTemplateSingleton.getRestTemplate();
			} catch (KeyManagementException e) {

				e.printStackTrace();
			} catch (KeyStoreException e) {

				e.printStackTrace();
			} catch (NoSuchAlgorithmException e) {

				e.printStackTrace();
			} catch (Exception e) {
				LOG.error("restTemplate Exception   ");
				e.printStackTrace();
			}

			// LOG.error("Info restTemplate -> "+restTemplate );
			sessionConfirmationNumber = sessionConfirmationService.getSessionConfirmationNumber(restTemplate);
			customerRegistrationRequest.set_dynSessConf(sessionConfirmationNumber);
			// LOG.error("Info sessionConfirmationNumber -> "+sessionConfirmationNumber );
			// Generate and set Password
			generatedPassword = passwordGeneratorService.generateRandomPassword(12);
			customerRegistrationRequest.setPassword(generatedPassword);
			customerRegistrationRequest.setConfirmPassword(generatedPassword);

			// LOG.error("Info sessionConfirmationNumber ->
			// kthutyteurh"+sessionConfirmationNumber+"d8d8d6jrjuuv" );
			// Setup Customer fields

			customerRegistrationRequest.setFirstName(customer.getPerson().getFirstName());
			customerRegistrationRequest.setLastName(customer.getPerson().getLastName());
			customerRegistrationRequest.setEmail(customer.getPerson().getEmail());
			customerRegistrationRequest.setRequireCaptcha(false);
			customerRegistrationRequest.setSPA(true);

		}
		if (null != restTemplate && customerRegistrationRequest.getEmail() != null) {
			LOG.error("Input data  registerCustomer     " + customerRegistrationRequest.toString());
			RegistrationResponse registrationResponse = customerRegistrationService
					.registerCustomer(customerRegistrationRequest, restTemplate);
			LOG.error("Output data  registerCustomer     " + registrationResponse.toString());
			// Send reset Email to Customer .

			if (null != registrationResponse && null != registrationResponse.getStatusCode()
					&& registrationResponse.getStatusCode().equalsIgnoreCase("Success")) {
				CustomerResetPasswordLink customerResetPasswordLink = new CustomerResetPasswordLink();
				customerResetPasswordLink.setLogin(customerRegistrationRequest.getEmail());
				customerResetPasswordLink.set_dynSessConf(customerRegistrationRequest.get_dynSessConf());

				LOG.error("Input data  resetPasswordEmailTrigger     " + customerResetPasswordLink.toString());
				RegistrationResponse passwordEmailTriggerResponse = customerRegistrationService
						.resetPasswordEmailTrigger(customerResetPasswordLink, restTemplate);
				LOG.error("Output data  resetPasswordEmailTrigger     " + passwordEmailTriggerResponse.toString());

			}
		}
		// LOG.error("Info registedCustomerandSendResetPassEmail method Ends " );

	}

	/**
	 * Creates a new Customer record ready for persistence.
	 * 
	 * @param update     CustomerDTO containing the data to update
	 * @param updateSite a Site instance to populate
	 * @return Customer instance ready for persistence
	 */
	private Customer createCustomerFromDTO(CustomerDTO update, Site updateSite) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("about to create a new Customer instance from {} and {}", update, updateSite);
		}
		Customer customer = new Customer();
		customer.setSite(updateSite);
		customer.setExternalId(update.getExternalId());
		customer.setContractDay(update.getContractDay());
		customer.setPerson(new Person());
		updateCustomerFromDTO(customer, update);
		if (StringUtils.isNotBlank(update.getLessonCounts())) {
			customer.setLessonCounts(update.getLessonCounts());
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("returning new customer {}", customer);
		}
		return customer;
	}

	// #############################################################

	private ParentDetails createCustomerParentFromDTO(CustomerDTO update) {

		ParentDetails parentDetails = new ParentDetails();
		if (StringUtils.isNotBlank(update.getParentFullName())) {
			parentDetails.setFullName(update.getParentFullName());
		}

		if (StringUtils.isNotBlank(update.getExternalSource())) {
			parentDetails.setExternalSource(update.getExternalSource());
		}

		return parentDetails;
	}

	private void updateCustomerParentFromDTO(Customer customer, CustomerDTO update) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("about to create a new Customer instance from {} and {}", update, "");
		}

		/*
		 * if (customer.getParentPersonId()!= null || customer.getParentPersonId() !=0L
		 * ) { customer.getPerson().setPersonId(customer.getParentPersonId()); } if
		 * (StringUtils.isNotBlank(update.getFirstName())) {
		 * customer.getPerson().setFirstName(update.getParentFirstName()); } if
		 * (StringUtils.isNotBlank(update.getLastName())) {
		 * customer.getPerson().setLastName(update.getParentLastName()); }
		 */

		if (StringUtils.isNotBlank(update.getExternalSource())) {
			customer.setExternalSource(update.getExternalSource());
		}

		// Changes made to LES_19
		if (StringUtils.isNotBlank(update.getBadgeNumber())) {
			customer.setBadgeNumber(update.getBadgeNumber());
		}
		if (StringUtils.isNotBlank(update.getExternalSource())) {
			customer.setExternalSource(update.getExternalSource());
		}
		// End of Changes for LES_19

		// Changes made to GSSP-220
		if (StringUtils.isNotBlank(update.getLocation_external_id())) {
			customer.setLocation_external_id(update.getLocation_external_id());
		}

		if (update.getInstruments() != null && !update.getInstruments().isEmpty()) {
			for (InstrumentDTO instrumentDTO : update.getInstruments()) {
				Instrument instrument = getInstrumentByExternalId(customer.getSite().getSiteId(),
						instrumentDTO.getExternalId());
				if (instrument != null) {
					customer.getInstruments().add(instrument);
				}
			}
		}

	}
	// #############################################################

	/**
	 * Updates a Customer with the values found in update.
	 * 
	 * @param customer Customer instance that will be persisted
	 * @param update   CustomerDTO containing the updated values
	 */
	private void updateCustomerFromDTO(Customer customer, CustomerDTO update) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("about to update {} from {}", customer, update);
		}

		if (StringUtils.isNotBlank(update.getFirstName())) {
			customer.getPerson().setFirstName(update.getFirstName());
		}
		if (StringUtils.isNotBlank(update.getLastName())) {
			customer.getPerson().setLastName(update.getLastName());
		}
		if (StringUtils.isNotBlank(update.getPhone())) {
			customer.getPerson().setPhone(update.getPhone());
		}
		if (StringUtils.isNotBlank(update.getEmail())) {
			customer.getPerson().setEmail(update.getEmail());
		}
		if (StringUtils.isNotBlank(update.getExternalSource())) {
			customer.setExternalSource(update.getExternalSource());
		}

		// Changes made to LES_19
		if (StringUtils.isNotBlank(update.getBadgeNumber())) {
			customer.setBadgeNumber(update.getBadgeNumber());
		}
		if (StringUtils.isNotBlank(update.getExternalSource())) {
			customer.setExternalSource(update.getExternalSource());
		}
		// End of Changes for LES_19

		// Changes made to GSSP-220
		if (StringUtils.isNotBlank(update.getLocation_external_id())) {
			customer.setLocation_external_id(update.getLocation_external_id());
		}

		if (update.getInstruments() != null && !update.getInstruments().isEmpty()) {
			for (InstrumentDTO instrumentDTO : update.getInstruments()) {
				Instrument instrument = getInstrumentByExternalId(customer.getSite().getSiteId(),
						instrumentDTO.getExternalId());
				if (instrument != null) {
					customer.getInstruments().add(instrument);
				}
			}
		}

		// Changes made to POS-808
		if (StringUtils.isNotBlank(update.getLessonCounts())) {
			customer.setLessonCounts(update.getLessonCounts());
		}

		if (StringUtils.isNotBlank(update.getLastBookedDate())) {
			customer.setLastBooked(DateTimeUtil.stringToDate(update.getLastBookedDate(), "MM-dd-yyyy"));
		}

		if (LOG.isDebugEnabled()) {
			LOG.debug("updated customer {}", customer);
		}

	}

	/**
	 * Private helper method to lookup and return a single CustomerStatus instance
	 * that matches the supplied external id.
	 * 
	 * @param externalId the external id to lookup
	 * @return instance of CustomerStatus or null
	 */
	private CustomerStatus getCustomerStatusByExternalId(Long siteId, String externalId) {

		if (LOG.isDebugEnabled()) {
			LOG.debug("looking for a CustomerStatus that matches site id {} " + " and external id {}", siteId,
					externalId);
		}
		CustomerStatus status = null;
		Criterion<CustomerStatus, CustomerStatus> criterion = CustomerStatusCriterion.findByExternalId(siteId,
				externalId);
		List<CustomerStatus> statuses = customerStatusDAO.search(criterion, DAOHelper.FETCH_NONE);
		if (statuses.size() == 1) {
			status = statuses.get(0);
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("returning {}", status);
		}
		return status;
	}

	/**
	 * Private helper method to lookup and return a single Instrument instance that
	 * matches the supplied external id.
	 * 
	 * @param externalId the external id to lookup
	 * @return instance of Instrument or null
	 */
	private Instrument getInstrumentByExternalId(Long siteId, String externalId) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("looking for a Instrument that matches site id {} " + " and external id {}", siteId, externalId);
		}
		Instrument instrument = null;
		Criterion<Instrument, Instrument> criterion = InstrumentCriterion.findByExternalId(siteId, externalId);
		List<Instrument> instruments = instrumentDAO.search(criterion, DAOHelper.FETCH_NONE);
		if (instruments.size() == 1) {
			instrument = instruments.get(0);
		}
		if (LOG.isDebugEnabled()) {
			LOG.debug("returning {}", instrument);
		}
		return instrument;
	}

	/**
	 * Build the instrument name of a customer which is split by comma
	 * 
	 * @param customerId The Id of a customer
	 */
	@Override
	public String getCustomerDetail(long customerId) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("Get customer info by id {}", customerId);
		}
		StringBuilder info = new StringBuilder();
		try {
			Customer c = customerDAO.get(customerId, DAOHelper.FETCH_MORE_INSTRUMENTS);
			if (LOG.isDebugEnabled()) {
				LOG.debug("Get returning customer object {}", c);
			}
			if (null != c) {
				Set<Instrument> instruments = c.getInstruments();
				if (null != instruments && !instruments.isEmpty()) {
					for (Instrument i : instruments) {
						info.append(i.getInstrumentName());
						info.append(",");
					}
					info.deleteCharAt(info.length() - 1);
				}
			}
		} catch (Exception e) {
			LOG.error("Loading customer instrument info error {} ", e);
		}
		return info.toString();
	}

	/**
	 * Called when an existing customer has a cancelled status.
	 * 
	 * Method should perform any necessary record clean-up and co-ordination.
	 * 
	 * @param customer Customer record that has been cancelled
	 */
	private void handleCancelledCustomer(Customer customer) {
		if (LOG.isDebugEnabled()) {
			LOG.debug("cancelling customer {}", customer);
		}
		/*
		 * Clean up any current/future appointments held by the customer
		 */
		appointmentSeriesService.deleteCancelledCustomerAppointments(customer.getCustomerId());
		if (LOG.isDebugEnabled()) {
			LOG.debug("cancelled customer handler completed");
		}
	}

	/**
	 * <p>
	 * Title: checkCustomerByAppointmentTime
	 * </p>
	 * <p>
	 * Description:
	 * </p>
	 * 
	 * @param recurring
	 * @param customerId
	 * @param startDate
	 * @param endDate
	 * @param startTime
	 * @param endTime
	 * @param profileId
	 * @return
	 * @see com.guitarcenter.scheduler.service.CustomerService#checkCustomerByAppointmentTime(boolean,
	 *      java.lang.String, java.lang.String, java.lang.String, java.lang.String,
	 *      java.lang.String, long)
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public boolean checkCustomerByAppointmentTime(boolean recurring, long customerId, String startDate, String endDate,
			String startTime, String endTime, long profileId) {
		if (recurring && !startDate.equals(endDate)) {
			return customerDAO.checkCustomerByAppointmentRecurringTime(customerId, startDate, endDate, startTime,
					endTime, profileId);
		} else {
			return customerDAO.checkCustomerByAppointmentTime(customerId, startDate, startTime, endTime, profileId);
		}
	}

	/**
	 * <p>
	 * Title: checkUpdateCustomerByAppointmentTime
	 * </p>
	 * <p>
	 * Description:
	 * </p>
	 * 
	 * @param recurring
	 * @param customerId
	 * @param startDate
	 * @param endDate
	 * @param startTime
	 * @param endTime
	 * @param profileId
	 * @param excludeAppointmentIdParam
	 * @return
	 * @see com.guitarcenter.scheduler.service.CustomerService#checkUpdateCustomerByAppointmentTime(boolean,
	 *      java.lang.String, java.lang.String, java.lang.String, java.lang.String,
	 *      java.lang.String, long, java.lang.String)
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public boolean checkUpdateCustomerByAppointmentTime(boolean recurring, long customerId, String startDate,
			String endDate, String startTime, String endTime, long profileId, String excludeAppointmentIdParam) {
		if (recurring && !startDate.equals(endDate)) {
			return customerDAO.checkUpdateCustomerByAppointmentRecurringTime(customerId, startDate, endDate, startTime,
					endTime, profileId, excludeAppointmentIdParam);
		} else {
			return customerDAO.checkUpdateCustomerByAppointmentTime(customerId, startDate, startTime, endTime,
					profileId, excludeAppointmentIdParam);
		}
	}

//249 customer updated appointments
	@Override
	public Boolean isUpdatedByCustomer(long personID) {
		Criterion<Customer, Boolean> criterion = CustomerCriterion.getUpdatedByCustomeFlag(personID);
		Boolean flag = customerDAO.get(criterion);
		return flag;
	}

	@Override
	public void changeToCustomerUpdated(List<Appointment> list) {
		for (Appointment app : list) {

			long personID = app.getUpdatedBy().getPersonId();
			Boolean flagForUpdate = isUpdatedByCustomer(personID);
			if (flagForUpdate) {
				app.setIsUpdatedByCustomer(flagForUpdate);
			}

		}

	} // 249 customer updated appointments end

	@Override
	public CustomerDetailDTO getCustomerEditDetails(Long customerId) {
		if (customerId == null) {
			throw new BizRuntimeException("generate customer detail DTO failed, customerId is null");
		}
		CustomerDetailDTO custDTO = customerDAO.getCustomerDetailsById(customerId);
		custDTO.setSecondaryEmail(PGPUtils.setNullToEmpty(custDTO.getSecondaryEmail()));
		custDTO.setPhoneNumber(PGPUtils.setNullToEmpty(custDTO.getPhoneNumber()));
		return custDTO;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
	public CustomerDetailDTO saveEditCustomerDetails(CustomerDetailDTO dto) {
		CustomerDetailDTO custDTO = new CustomerDetailDTO();
		long customerId = dto.getGcId();
		if (LOG.isDebugEnabled()) {
			LOG.debug("Began to get customer list by customerId:{} from CustomerService.saveEditCustomerDetails",
					customerId);
		}
		Customer customer = null;
		try {
			customer = customerDAO.getCustomerById(customerId);

		} catch (Exception e) {
			LOG.error("Caught {} when getting a customer by {}", e, customerId);

		}

		ParentDetails pd = new ParentDetails();
		Long id = 0L;
		Long parentId = null;
		if (customer.getParentId() == null) {

			pd.setSecondaryEmail(dto.getSecondaryEmail());
			pd.setExternalSource("GCSS");
			id = parentDetailsDAO.save(pd, personManagerService.getSystemUpdatePerson());
			pd.setParentId(id);
			customer.setParentId(pd);

		} else {

			pd = customer.getParentId();
			pd.setSecondaryEmail(dto.getSecondaryEmail());
			parentDetailsDAO.update(pd, personManagerService.getSystemUpdatePerson());
			customer.setParentId(pd);
		}

		customerDAO.update(customer, personManagerService.getSystemUpdatePerson());
		try {
			if (customer != null && customer.getCustomerId() != null) {
				searchService.updateRecordSec(customer);
			}
		} catch (Exception e) {
			LOG.error("Caught {} when getting a customer by {}", e, customerId);

		}
		return custDTO;
	}

}
