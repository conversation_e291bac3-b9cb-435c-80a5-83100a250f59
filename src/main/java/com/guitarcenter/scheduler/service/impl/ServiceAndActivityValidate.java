package com.guitarcenter.scheduler.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.ActivityDAO;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.LocationProfileDAO;
import com.guitarcenter.scheduler.dao.ProfileActivityDAO;
import com.guitarcenter.scheduler.dao.ProfileServiceDAO;
import com.guitarcenter.scheduler.dao.RoomDAO;
import com.guitarcenter.scheduler.dao.RoomTemplateDAO;
import com.guitarcenter.scheduler.dao.ServiceDAO;
import com.guitarcenter.scheduler.dao.criterion.AppointmentCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.InstructorCriterion;
import com.guitarcenter.scheduler.dao.criterion.LocationProfileCriterion;
import com.guitarcenter.scheduler.dao.criterion.ProfileActivityCriterion;
import com.guitarcenter.scheduler.dao.criterion.ProfileServiceCriterion;
import com.guitarcenter.scheduler.dao.criterion.RoomCriterion;
import com.guitarcenter.scheduler.dao.criterion.RoomTemplateCriterion;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.LocationProfile;
import com.guitarcenter.scheduler.model.ProfileActivity;
import com.guitarcenter.scheduler.model.ProfileService;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.RoomTemplate;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.service.AvailabilityService;
import com.guitarcenter.scheduler.service.LocationProfileService;

@Service("serviceAndActivityValidate")
public class ServiceAndActivityValidate{

	private static final String VALIDATE_FLAG = "flag";
	private static final String VALIDATE_MESSAGE = "message";
	private static final String VALIDATE_OBJECT = "object";
	
	@Autowired
	@Qualifier("profileActivityDAO")
	private ProfileActivityDAO profileActivityDAO;
	
	@Autowired
	private RoomTemplateDAO roomTemplateDAO;
	
	@Autowired
	private ProfileServiceDAO profileServiceDAO;
	
	@Autowired
	private ActivityDAO activityDAO;
	
	@Autowired
	private LocationProfileDAO locationProfileDAO;
	
	@Autowired
	private AppointmentDAO appointmentDAO;
	
	@Autowired
	private ServiceDAO serviceDAO;
	
	@Autowired
	private InstructorDAO instructorDAO;
	
	@Autowired
	private RoomDAO roomDAO;
	
	@Resource
	private AvailabilityService availabilityService;
	
	@Autowired
	private LocationProfileService	locationProfileService;
	
	
	public Map<String, Object> activityCanDelete(Long activityId){
		Map<String, Object> map = new HashMap<String, Object>();
		Activity activity = activityDAO.get(activityId);
		if(activity == null){
			map.put(VALIDATE_FLAG, false);
			map.put(VALIDATE_MESSAGE, "activity does not exist or it has been deleted !");
		}else{
			map.put(VALIDATE_OBJECT, activity);
			map.put(VALIDATE_FLAG, true);
		}
		//GCSS-656 check appointment
		Boolean checkApp = appointmentDAO.checkAppointmentByActivityId(activityId);
		if(checkApp){
			map.put(VALIDATE_FLAG, false);
			map.put(VALIDATE_MESSAGE, "Activity has been scheduled, it cannot be deleted!");
		}
//		Boolean result = activityAssignedToProfile(activityId);
//		if(result){
//			map.put(VALIDATE_FLAG, false);
//			map.put(VALIDATE_MESSAGE, "activity has been assigned to profile activity,it cannot be deleted!");
//		}
//		result = activityAssignedToRoomTemp(activityId);
//		if(result){
//			map.put(VALIDATE_FLAG, false);
//			map.put(VALIDATE_MESSAGE, "activity has been assigned to roomTemplate,it cannot be deleted!");
//		}
//		result = activityAssignedToInstructor(activityId);
//		if(result){
//			map.put(VALIDATE_FLAG, false);
//			map.put(VALIDATE_MESSAGE, "activity has been assigned to instructor,it cannot be deleted!");
//		}
		return map;
	}
	
	//GCSS-656
	@Transactional
	public void deleteActivity(Long activityId) throws Exception{
		activityDAO.deleteProfileAvtivityByActivityId(activityId);
		activityDAO.deleteRoomTemplateAvtivityByActivityId(activityId);
		activityDAO.deleteInstructorAvtivityByActivityId(activityId);
		activityDAO.deleteRoomAvtivityByActivityId(activityId);
	}
	
	//GCSS-656
	@Transactional
	public void deleteService(Long serviceId) throws Exception{
		activityDAO.deleteInstructorAvtivityByServiceId(serviceId);
		activityDAO.deleteProfileAvtivityByServiceId(serviceId);
		activityDAO.deleteRoomTemplateAvtivityByServiceId(serviceId);
		activityDAO.deleteRoomAvtivityByServiceId(serviceId);
		serviceDAO.deleteProfileServiceByServiceId(serviceId);
		serviceDAO.deleteRoomTemplateServiceByServiceId(serviceId);
		serviceDAO.deleteRoomServiceByServiceId(serviceId);
	}
	
	//GCSS-657
	@Transactional
	public void deleteProfileService(Long profileId, Long serviceId) throws Exception{
		profileActivityDAO.deleteProfileActivityByProfileIdServiceId(profileId, serviceId);
		locationProfileService.deleteProfileService(profileId, serviceId);
	}
	
	private Boolean activityAssignedToInstructor(Long activityId) {
		Criterion<Instructor, Instructor>  criterion = InstructorCriterion.findByActivityId(activityId);
		 List<Instructor> list = instructorDAO.search(criterion);
		return (list==null || list.isEmpty())?false:true;
	}

	public Map<String, Object> serviceCanDelete(Long serviceId) {
		
		Map<String, Object> map = new HashMap<String, Object>();
		com.guitarcenter.scheduler.model.Service service = serviceDAO.get(serviceId);
		if(service == null){
			map.put(VALIDATE_FLAG, false);
			map.put(VALIDATE_MESSAGE, "service does not exit or has been deleted!");
		}else{
			map.put(VALIDATE_FLAG, true);
			map.put(VALIDATE_OBJECT, service);
		}
		//GCSS-656 check appointment
		Boolean checkApp = appointmentDAO.checkAppointmentByServiceId(serviceId);
		if(checkApp){
			map.put(VALIDATE_FLAG, false);
			map.put(VALIDATE_MESSAGE, "Service has been scheduled, it cannot be deleted!");
		}
//		Boolean result = serviceAssignedToProfile(serviceId);
//		if(result){
//			map.put(VALIDATE_FLAG, false);
//			map.put(VALIDATE_MESSAGE, "service has been assigned to profile,it cannot be disabled or deleted!");
//		}
//		result = serviceAssignedToRoomTemplate(serviceId);
//		if(result){
//			map.put(VALIDATE_FLAG, false);
//			map.put(VALIDATE_MESSAGE, "service has been assigned to roomTemplate,it cannot be deleted!");
//		}
	
		return map;
	}

	//GCSS-657
	@Transactional
	public Map<String, Object> profileServiceCanDelete(Long profileServiceId) {
		
		Map<String, Object> map = new HashMap<String, Object>();
		ProfileService profileService= locationProfileService.getProfileService(profileServiceId);
		if(profileService == null){
			map.put(VALIDATE_FLAG, false);
			map.put(VALIDATE_MESSAGE, "Service does not exit or has been deleted!");
		}else{
			map.put(VALIDATE_FLAG, true);
			map.put(VALIDATE_OBJECT, profileService);
		}
		Boolean checkApp = appointmentDAO.checkAppointmentByProfileIdServiceId(profileService.getLocationProfile().getProfileId(), profileService.getService().getServiceId());
		if(checkApp){
			map.put(VALIDATE_FLAG, false);
			map.put(VALIDATE_MESSAGE, "Service has been scheduled, it cannot be deleted!");
		}
		return map;
	}
	
	//GCSS-657
	@Transactional
	public Map<String, Object> profileActivityCanDelete(Long profileActivityId) {
		
		Map<String, Object> map = new HashMap<String, Object>();
		ProfileActivity profileActivity= profileActivityDAO.get(profileActivityId);
		if(profileActivity == null){
			map.put(VALIDATE_FLAG, false);
			map.put(VALIDATE_MESSAGE, "Activity does not exit or has been deleted!");
		}else{
			map.put(VALIDATE_FLAG, true);
			map.put(VALIDATE_OBJECT, profileActivity);
		}
		Boolean checkApp = appointmentDAO.checkAppointmentByProfileIdActivityId(profileActivity.getLocationProfile().getProfileId(), profileActivity.getActivity().getActivityId());
		if(checkApp){
			map.put(VALIDATE_FLAG, false);
			map.put(VALIDATE_MESSAGE, "Activity has been scheduled, it cannot be deleted!");
		}
		return map;
	}

	public Boolean profileServiceCanDisable(Long serviceId,Long profileId) {
		Criterion<ProfileActivity, ProfileActivity>  profileActivityCriterion = ProfileActivityCriterion.findByProfileIdAndServiceIdAndEnabled(profileId, serviceId, Enabled.Y);
		List<ProfileActivity> profileActivities = profileActivityDAO.search(profileActivityCriterion);
		for (ProfileActivity profileActivity : profileActivities) {
			Criterion<Appointment, Boolean> criterion =  AppointmentCriterion.hasByProfileIdAndActivityId(profileId, profileActivity.getActivity().getActivityId());
			Boolean result = appointmentDAO.get(criterion);
			if(result){
				return false;
			}
		} 
		return true;
	}
	
	
	public Boolean activityAssignToAppoint(Long siteId,Long activityId){
		Criterion<Appointment, Boolean> criterion = AppointmentCriterion.hasBySiteAndActivityId(siteId, activityId);
		Boolean result = appointmentDAO.get(criterion);
		return result;
	}
	public Boolean profileActivityCanDisable(Long profileId,Long activityId){
		Criterion<Appointment, Boolean> criterion = AppointmentCriterion.hasByProfileIdAndActivityId(profileId,activityId);
		Boolean result = appointmentDAO.get(criterion);
		return !result;
	}
	
	public Boolean profileActivityCanEnable(Long profileId,Long serviceId){
		Criterion<ProfileService, Boolean> criterion = ProfileServiceCriterion.hasByProfileIdAndServiceIdAndEnabled(profileId, serviceId,Enabled.N);
		Boolean result = profileServiceDAO.get(criterion);
		return !result;
	}
	
	private Boolean activityAssignedToProfile(Long activityId) {
		Criterion<ProfileActivity, Boolean> criterion = ProfileActivityCriterion
			    .hasActivityByActivityIds(new Long[] { activityId });
		Boolean result = profileActivityDAO.get(criterion);
		return result;
	}
	
	private Boolean activityAssignedToRoomTemp(Long activityId){
		Criterion<RoomTemplate, Boolean> criterion = RoomTemplateCriterion.hasByActivityId(activityId);
		Boolean result = roomTemplateDAO.get(criterion);
		return result;
	}
	
	//==============================================
	private Boolean serviceAssignedToRoomTemplate(Long serviceId){
		Criterion<RoomTemplate, Boolean>  criterion = RoomTemplateCriterion.hasByServiced(serviceId);
		Boolean result = roomTemplateDAO.get(criterion);
		return result;
	}
	
	private Boolean serviceAssignedToProfile(Long serviceId){
		Criterion<ProfileService, Boolean> criterion = ProfileServiceCriterion.hasServiceByServiceIds(new Long[] { serviceId });
	    Boolean result = profileServiceDAO.get(criterion);
	    return result;
	}
	
	
	public Boolean serviceAssignedToAppoint(Long serviceId,Long siteId){
		Criterion<Appointment, Boolean>  criterion = AppointmentCriterion.hasBySiteIdAndServiceId(siteId, serviceId);
		Boolean result = appointmentDAO.get(criterion);
		return result;
		
	}
	
	//profile activity and profile servie's enable are both n
	public Boolean activityHasDisabledProService(Long activityId,Long siteId){
		Criterion<ProfileActivity, Boolean>  criterion = ProfileActivityCriterion.hasBySiteIdAndActivityId(siteId, activityId);
		Boolean result = profileActivityDAO.get(criterion);
		return result;
	}
	
	//roomtemplateCanDelte
	public Boolean roomtemplateCanDelete(Long roomtemplateId){
		Criterion<Room, Room> criterion = RoomCriterion.findByRoomTemplateId(roomtemplateId);
		List<Room> rooms = roomDAO.search(criterion);
		if(rooms != null && !rooms.isEmpty()){
			return false;
		}
		return true;
	}
	
	//Instructor has appointment
	public Boolean instructorCanDisable(final Long instructorId){
	    final Criterion<Appointment, Boolean> criterion = AppointmentCriterion.hasByInstructorId(instructorId);
        return !appointmentDAO.get(criterion);
	}

    public Boolean checkInstructorAppoint(Long instructorId, Availability availability,int i) {
        Boolean result;
        switch (i) {
        case 1:
            result = getInstrucotrAppresult(availability.getMondayStartTime(),availability.getMondayEndTime(),i,instructorId);
            break;
        case 2:
            result = getInstrucotrAppresult(availability.getTuesdayStartTime(),availability.getTuesdayEndTime(),i,instructorId);
            break;
        case 3:
            result = getInstrucotrAppresult(availability.getWednesdayStartTime(),availability.getWednesdayEndTime(),i,instructorId);
            break;
        case 4:
            result = getInstrucotrAppresult(availability.getThursdayStartTime(),availability.getThursdayEndTime(),i,instructorId);
            break;
        case 5:
            result = getInstrucotrAppresult(availability.getFridayStartTime(),availability.getFridayEndTime(),i,instructorId);
            break;
        case 6:
            result = getInstrucotrAppresult(availability.getSaturdayStartTime(),availability.getSaturdayEndTime(),i,instructorId);
            break;
        case 7:
            result = getInstrucotrAppresult(availability.getSundayStartTime(),availability.getSundayEndTime(),i,instructorId);
            break;
        default:
            result = true;
            break;
        }
        
        return result;
    }
    
    public Boolean checkProfileAppoint(Long appointmentId,Availability availability,int i){
    	 Boolean result;
         switch (i) {
         case 1:
             result = getAppointmengResult(availability.getMondayStartTime(),availability.getMondayEndTime(),i,appointmentId);
             break;
         case 2:
             result = getAppointmengResult(availability.getTuesdayStartTime(),availability.getTuesdayEndTime(),i,appointmentId);
             break;
         case 3:
             result = getAppointmengResult(availability.getWednesdayStartTime(),availability.getWednesdayEndTime(),i,appointmentId);
             break;
         case 4:
             result = getAppointmengResult(availability.getThursdayStartTime(),availability.getThursdayEndTime(),i,appointmentId);
             break;
         case 5:
             result = getAppointmengResult(availability.getFridayStartTime(),availability.getFridayEndTime(),i,appointmentId);
             break;
         case 6:
             result = getAppointmengResult(availability.getSaturdayStartTime(),availability.getSaturdayEndTime(),i,appointmentId);
             break;
         case 7:
             result = getAppointmengResult(availability.getSundayStartTime(),availability.getSundayEndTime(),i,appointmentId);
             break;
         default:
             result = true;
             break;
         }
         
         return result;
    }

    /**
     * if there's no appoint return true.
     * @param startDate
     * @param endDate
     * @param i
     * @param instructorId
     * @return
     */
    private Boolean getInstrucotrAppresult(Date startDate, Date endDate, int i,Long instructorId) {
        Boolean result;
        if(startDate == null || endDate == null){
            Criterion<Instructor, Boolean> criterion = InstructorCriterion.hasAppointmentByInstructorAndDayOfWeek(instructorId, i);
            result = !instructorDAO.get(criterion);
        }else{
            Criterion<Instructor, Boolean> criterion = InstructorCriterion.checkAppointmentDateTimeByInstructorAndDateTime(instructorId, startDate, endDate, i);
            result = instructorDAO.get(criterion);
        }
        
        return result;
    }
    
    private Boolean getAppointmengResult(Date startDate, Date endDate, int i,Long profileId){
    	
    	Criterion<LocationProfile, Boolean> criterion = LocationProfileCriterion.checkAppointmentDateTimeByProfileIdAndDateTime(profileId, startDate, endDate, i);
        Boolean result = locationProfileDAO.get(criterion);
        
        return result;
    }
    
    /**
     * @param profileId
     * @param startDate
     * @param endDate
     * @return boolean
     * Jun 16, 2014
     * 
     * Check the time duration user choose in UI when updating availability.
     * 
     * Comparing user enter time with studio available hour.
     * If the availability time is out of studio available time, show error message to user.
     */
    
    public boolean checkProfileStudioHour(Long profileId,Availability availability,int dayOfWeek){
    	DateTime baseOnNow = DateTime.now();

    	Date startDate = availability.getStartTimeByDayOfWeek(dayOfWeek);
    	Date endDate = availability.getEndTimeByDayOfWeek(dayOfWeek);
    	
    	if(startDate == null || endDate == null ){
    		return true;
    	}
    	
    	DateTime startDateTime = buildDateTime(baseOnNow, startDate).plusMillis(1);
    	DateTime startEndTime = buildDateTime(baseOnNow, endDate).minusMillis(1);
    	
    	Availability profileAvailability = availabilityService.findByProfileId(profileId);
    	
    	Date startTime = profileAvailability.getStartTimeByDayOfWeek(dayOfWeek);
    	Date endTime = profileAvailability.getEndTimeByDayOfWeek(dayOfWeek);
    	
    	if(startTime == null || endTime == null){
    		return false;
    	}
    	
    	DateTime profileStartDate = buildDateTime(baseOnNow, startTime);
    	DateTime profileEndDate = buildDateTime(baseOnNow, endTime);
    	
    	if(startDateTime.isAfter(profileStartDate) && startEndTime.isBefore(profileEndDate)){
    		return true;
    	}
   		return false;
    }

	private DateTime buildDateTime(DateTime baseOnNow, Date pInputDate) {
		DateTime dateTime = new DateTime(pInputDate);
    	return baseOnNow.withTime(dateTime.getHourOfDay(),dateTime.getMinuteOfHour() , dateTime.getSecondOfMinute(), 0);
	}
}
