package com.guitarcenter.scheduler.service.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROLE;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.apache.solr.client.solrj.SolrServerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.AvailabilityUtil;
import com.guitarcenter.scheduler.dao.EmployeeDAO;
import com.guitarcenter.scheduler.dao.PersonDAO;
import com.guitarcenter.scheduler.dao.PersonRoleDAO;
import com.guitarcenter.scheduler.dao.RoleDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.EmployeeCriterion;
import com.guitarcenter.scheduler.dao.criterion.PersonCriterion;
import com.guitarcenter.scheduler.dao.criterion.PersonRoleCriterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dto.ListStaffsDTO;
import com.guitarcenter.scheduler.dto.StaffDTO;
import com.guitarcenter.scheduler.integration.dto.EmployeeDTO;
import com.guitarcenter.scheduler.model.Employee;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.PersonRole;
import com.guitarcenter.scheduler.model.Role;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.service.EmployeeService;
import com.guitarcenter.scheduler.service.LocationManagerService;
import com.guitarcenter.scheduler.service.PersonManagerService;
import com.guitarcenter.scheduler.service.PersonRoleService;
import com.guitarcenter.scheduler.service.SearchService;
import com.guitarcenter.scheduler.service.SiteService;

@Service("employeeService")
public class EmployeeServiceImpl implements EmployeeService,AppConstants  {
    Logger log = LoggerFactory.getLogger(EmployeeServiceImpl.class);
    private static final String		TO_STRING		= " - ";
	private static final String		BLANK_STRING	= " ";	

	@Resource
	private EmployeeDAO employeeDAO;

    @Autowired
    private PersonDAO personDAO;
    
    @Autowired
    private PersonRoleDAO personRoleDAO;
    
    @Autowired
    private RoleDAO roleDAO;
	
	@Autowired
	private SiteService siteService;
	
	@Autowired
	private PersonManagerService personManagerService;
	
	@Autowired
	private SearchService searchService;
	
	@Autowired
	private LocationManagerService locationManageService;
	
	@Resource
	private PersonRoleService		personRoleService;
	
	//created for GSSP-288, find employees by store number
	@Transactional
	@Override
	public Set<StaffDTO> findByStoreNumber(Site site, String storeNumber) {
		return new HashSet<StaffDTO>(getAllByStoreNumber(site, storeNumber));
	}
	//created for GSSP-288
	@Transactional
	@Override
	public List<StaffDTO> getAllByStoreNumber(Site site, String storeNumber) {
		if (log.isDebugEnabled()) {
			log.debug("Searching for employee associated with store number {}", storeNumber);
		}
		String LocationId="";
		try
		{
			List<Location> existing = locationManageService.findByExternalId(site.getSiteId(), storeNumber);
			LocationId = existing.get(0).getLocationId().toString(); // getting location id by using externalId(storeNumber)
		}
		catch (NullPointerException e) {
			log.error("Caught a NullPointerException from locationManageService.findByExternalId : {}", e);
		} 
		Criterion<Employee, StaffDTO> criterion =
				EmployeeCriterion.findStaffBasedOnLocation(site.getSiteId(), LocationId);
		List<StaffDTO> employees = employeeDAO.search(criterion, DAOHelper.FETCH_PERSON);
		if (log.isDebugEnabled()) {
			log.debug("Returning employee list {}", employees);
		}
		return employees;
	}
	
	//created for GSSP-288
	public ListStaffsDTO findAllStaff(long siteId)
	{
		Site site = siteService.getSiteById(siteId);
		ListStaffsDTO listStaffsDto = new ListStaffsDTO();
		// ===================== transfer staffList
		// ==============================
		int partitionSize = 1000;
		List<PersonRole> personRoles = new ArrayList<PersonRole>();
		List<StaffDTO> staffDtos = new ArrayList<StaffDTO>();
		Set<Employee> employeeSet = findBySite(site);
		if(null != employeeSet)
			AvailabilityUtil.partitionListBySize(employeeSet.stream().map(employee -> employee.getPerson().getPersonId()).collect(Collectors.toList()),partitionSize).forEach((List<Long> personId) -> {
				personRoles.addAll( personRoleService.findRolesByPersondIds(personId)); 
			});
		StringBuilder buf = new StringBuilder();
		StringBuilder roleLocationName = new StringBuilder();
		for (Employee employee : employeeSet) {
			//Changes made for GSSP-180
			StaffDTO staffDto = new StaffDTO();
			List<String> roleLocation = new LinkedList<String>();
			roleLocationName.setLength(0);
			for (PersonRole personRole : personRoles ){
				if(employee.getPerson().getPersonId().equals(personRole.getPerson().getPersonId()))
				{
					if (personRole.getLocation() != null) {
						buf.setLength(0);
						buf.append(personRole.getLocation().getExternalId())
						.append(TO_STRING)
						.append(personRole.getRole().getRoleName());
						roleLocation.add(buf.toString());
						roleLocationName.append(buf.toString()).append(BLANK_STRING);
					}
				}
			}
			staffDto.setRoleLocation(roleLocation);
			staffDto.setId(employee.getEmployeeId());
			if (employee.getStatus() != null &&
					STAFF_STATUS.equals(employee.getStatus().trim())) {
				staffDto.setActive(true);
			} else {
				staffDto.setActive(false);
			}
			staffDto.setEmail(employee.getPerson().getEmail());
			staffDto.setInstructorName(employee.getPerson().getFirstName() + BLANK_STRING
					+ employee.getPerson().getLastName());
			staffDto.setRoleLocationName(roleLocationName.toString());
			staffDtos.add(staffDto);
		}
		Collections.sort(staffDtos);		
		listStaffsDto.setlistStaffDtos(staffDtos);
		return listStaffsDto;
	}
	
	//Find all employees by site id
	@Transactional
	@Override
	public Set<Employee> findBySite(Site site) {
		/* Modified by MEmes: old method returned all employees regardless of
		 * site.
		 */
		return new HashSet<Employee>(getAllBySite(site));
	}
	
	@Transactional
	@Override
	public List<Employee> getAllBySite(Site site) {
	    if (log.isDebugEnabled()) {
            log.debug("Searching for employee associated with site {}", site);
        }
        Criterion<Employee, Employee> criterion =
            EmployeeCriterion.findBySiteId(site.getSiteId());
       List<Employee> employees = employeeDAO.search(criterion, DAOHelper.FETCH_PERSON);
        if (log.isDebugEnabled()) {
            log.debug("Returning employee list {}", employees);
        }
        return employees;
	}
	

	/**
	 * Returns a List of Employee instances that are associated with the site.
	 * 
	 * @param site Site to match
	 * @return List of Employee instances
	 */
	
	@Transactional
	@Override
	public Employee getEmployee(long id) {
		
		return employeeDAO.get(id,DAOHelper.FETCH_PERSON);
	}	
	
	/**
     * Return a collection of Employee records for the site id provided that
     * have a matching external id value.
     * 
     * Note: this is returning a collection simply because there is not a
     * database constraint on the external identifiers; it is possible that
     * multiple employees *could* be updated from a single external record in
     * the future.
     * 
     * @param siteId identifier for the site that results must be restricted to
     * @param externalId String containing the external id to match
     * @return List of Employee instances that match the external id in the site
     *         provided
     */
	@Transactional
    @Override
    public List<Employee> findEmployeesByExternalId(long siteId, String externalId) {
        if (log.isDebugEnabled()) {
            log.debug("Searching for employee with external id {} and site id {}",
                      externalId, siteId);
        }
        Criterion<Employee, Employee> criterion =
            EmployeeCriterion.findByExternalId(siteId, externalId);
        List<Employee> employees = employeeDAO.search(criterion, DAOHelper.FETCH_PERSON);
        if (log.isDebugEnabled()) {
            log.debug("Returning employee list {}", employees);
        }
        return employees;
    }
    
    
    /**
     * Return a collection of Employee records for the site id provided that
     * have a matching external id value.
     * 
     * Note: this is returning a collection simply because there is not a
     * database constraint on the external identifiers; it is possible that
     * multiple employees *could* be updated from a single external record in
     * the future.
     * 
     * @param siteId identifier for the site that results must be restricted to
     * @param externalId String containing the external id to match
     * @return List of Employee instances that match the external id in the site
     *         provided
     */
	@Transactional
	@Override
	public List<Person> findPersonByAuthId(long siteId, String authId) {
		if (log.isDebugEnabled()) {
			log.debug("Searching for employee with external id {} and site id {}", authId, siteId);
		}
		Criterion<Person, Person> criterion = PersonCriterion.findByAuthId(siteId, authId);
		List<Person> persons = personDAO.search(criterion, DAOHelper.FETCH_PERSON);
		if (log.isDebugEnabled()) {
			log.debug("Returning employee list {}", persons);
		}
		return persons;
	}

	/**
	 * Create or updates an Scheduler Employee record with values taken from the
	 * supplied Employee record. Any exceptions are passed up to caller for
	 * decision and handling.
	 * 
	 * The method will retrieve an existing employee record from persistent
	 * storage that matches the external id of the update record.
	 * 
	 * @param update
	 *            an EmployeeDTO instance containing values to update
	 * @throws IOException
	 * @throws SolrServerException
	 */
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
	@Override
	public void updateFromExternal(EmployeeDTO update) throws SolrServerException, IOException {

		if (log.isDebugEnabled()) {
			log.debug("about to attempt an update for employee {}", update);
		}
		if (update == null) {
			log.warn("a null employee instance was passed to updateFromExternal");
			return;
		}

		if (StringUtils.isBlank(update.getSite())) {
			if (log.isInfoEnabled()) {
				log.info("updated employee {} does not have a site string");
			}
			return;
		}
		/*
		 * Find the site that matches the external id provided by update record.
		 * 
		 * Note: sites are a key element of data separation, so throw an
		 * exception if an exact match of 1 site is not received.
		 */
		List<Site> sites = siteService.findSitesByExternalId(update.getSite());
		if (sites.size() > 1) {
			log.warn("updated employee site external id {} matches too " + "many sites; ignoring record",
					update.getSite());
			return;
		}
		if (sites.isEmpty()) {

			log.error("Site is empty");
			if (log.isInfoEnabled()) {
				log.info("updated employee site external id {} does not match " + "an existing site; ignoring record",
						update.getSite());
			}
			return;
		}
		Site updateSite = sites.get(0);

		/*
		 * Now try to find an existing Employee record to update
		 */
		if (StringUtils.isBlank(update.getExternalId())) {
			log.warn("updated employee {} does not have an external id; " + "ignoring record", update);
			return;
		}
		Employee employee = null;
		List<Employee> existing = findEmployeesByExternalId(updateSite.getSiteId(), update.getExternalId());

		switch (existing.size()) {
		case 1:
			/*
			 * Update an existing Employee from the DTO
			 */

			employee = existing.get(0);
			if (log.isDebugEnabled()) {
				log.debug("found a record; updating {}", employee);
			}
			updateEmployeeFromDTO(employee, update);
			personDAO.update(employee.getPerson(), personManagerService.getSystemUpdatePerson());
			employeeDAO.update(employee, personManagerService.getSystemUpdatePerson());

			

			// Changes made for GSSP-209 if VP/DM terminated remove from person
			// role
			/*
			 * Set<PersonRole >updatedPersonRole =
			 * personRoleService.findByPerson(employee.getSite().getSiteId(),
			 * employee.getPerson().getPersonId());
			 * if(employee.getEnterpriseStatus().equalsIgnoreCase("T") && (
			 * updatedRole.getRoleName().equalsIgnoreCase(AppConstants.
			 * DISTRICT_MANAGER) ||
			 * updatedRole.getRoleName().equalsIgnoreCase(AppConstants.
			 * REGIONAL_VICE_PRESIDENT)) ) { log.error("Inside if loop"); if(
			 * null != updatedPersonRole && updatedPersonRole.size() > 0){ for
			 * (PersonRole personRole : updatedPersonRole) {
			 * log.error("Inside for loop"); personRoleDAO.delete(personRole); }
			 * } }
			 */

			break;

		case 0:
			/*
			 * Need to create a new Employee record and populate with DTO.
			 * 
			 * Note that employee requires a Location, so do not persist the
			 * employee if there is not a matching location.
			 */

			Role role = getRoleFromUpdate(update);

			/*
			 * if(role.getRoleName().equalsIgnoreCase(AppConstants.
			 * DISTRICT_MANAGER) ||
			 * role.getRoleName().equalsIgnoreCase(AppConstants.
			 * REGIONAL_VICE_PRESIDENT)) { log.error("Inside DM");
			 * update.setStudio("999"); }
			 */

			employee = createEmployeeFromDTO(update, updateSite);

			// Changes made for GSSP-209 update person and do not update
			// employee for RVP/DM
			if (employee.getLocation() != null) {
				personDAO.save(employee.getPerson(), personManagerService.getSystemUpdatePerson());

				employeeDAO.save(employee, personManagerService.getSystemUpdatePerson());

				/*
				 * New employees get a chance to have a login role setup
				 * automatically if there is a match between Scheduler role and
				 * job code. If a role is not found, the employee should still
				 * be saved.
				 */

				if (role != null) {
					PersonRole personRole = new PersonRole();

					personRole.setLocation(employee.getLocation());

					personRole.setPerson(employee.getPerson());
					personRole.setRole(role);
					personRole.setSite(updateSite);

					personRoleDAO.save(personRole, personManagerService.getSystemUpdatePerson());

				}
			} else {
				if (log.isInfoEnabled()) {
					log.info("employee {} has null location; skipping save", employee);
				}
			}
			break;

		default:
			/*
			 * Found too many employee records - DO NOT UPDATE!
			 */
			
			log.warn("updated employee external id {} matched too many " + "existing records; ignoring update request",
					update.getExternalId());
			return;
		}
		/*
		 * Update was successful so make sure search engine has the latest data
		 * on record.
		 */
		if (employee.getEmployeeId() != null) {
			searchService.updateRecord(employee);
		}
		if (log.isDebugEnabled()) {
			log.debug("update completed for employee {}", update);
		}
	}

	/**
	 * Creates a new Employee record ready for persistence.
	 * 
	 * @param update
	 *            EmployeeDTO containing the data to update
	 * @param updateSite
	 *            a Site instance to populate
	 * @return Employee instance ready for persistence
	 */
	private Employee createEmployeeFromDTO(EmployeeDTO update, Site updateSite) {
		if (log.isDebugEnabled()) {
			log.debug("about to create a new Employee instance from {} and {}", update, updateSite);
		}
		Employee employee = new Employee();
		employee.setSite(updateSite);
		employee.setExternalId(update.getExternalId());
		employee.setLocation(getLocationFromDTO(update, updateSite));
		employee.setPerson(new Person());
		updateEmployeeFromDTO(employee, update);
		if (log.isDebugEnabled()) {
			log.debug("returning new employee {}", employee);
		}
		return employee;
	}

	/**
	 * Creates a new Employee record ready for persistence.
	 * 
	 * @param update
	 *            EmployeeDTO containing the data to update
	 * @param updateSite
	 *            a Site instance to populate
	 * @return Employee instance ready for persistence
	 */
	private Person createPersonFromDTO(EmployeeDTO update, Site updateSite) {
		if (log.isDebugEnabled()) {
			log.debug("about to create a new Employee instance from {} and {}", update, updateSite);
		}
		if (log.isDebugEnabled()) {

		}
		Person person = new Person();
		if (StringUtils.isNotBlank(update.getFirstName())) {
			person.setFirstName(update.getFirstName());
		}
		if (StringUtils.isNotBlank(update.getLastName())) {
			person.setLastName(update.getLastName());
		}
		if (StringUtils.isNotBlank(update.getEmail())) {
			person.setEmail(update.getEmail());
		}

		if (StringUtils.isNotBlank(update.getAuthId())) {
			person.setAuthId(update.getAuthId());
		}

		if (log.isDebugEnabled()) {
			log.debug("updated person {}", person);
		}
		return person;
	}

	/**
	 * Updates an Employee with the values found in update.
	 * 
	 * @param employee
	 *            Employee instance that will be persisted
	 * @param update
	 *            EmployeeDTO containing the updated values
	 */
	private void updateEmployeeFromDTO(Employee employee, EmployeeDTO update) {
		if (log.isDebugEnabled()) {
			log.debug("about to update {} from {}", employee, update);
		}
		if (StringUtils.isNotBlank(update.getFirstName())) {
			employee.getPerson().setFirstName(update.getFirstName());
		}
		if (StringUtils.isNotBlank(update.getLastName())) {
			employee.getPerson().setLastName(update.getLastName());
		}
		if (StringUtils.isNotBlank(update.getEmail())) {
			employee.getPerson().setEmail(update.getEmail());
		}
		if (StringUtils.isNotBlank(update.getExternalSource())) {
			employee.setExternalSource(update.getExternalSource());
		}
		if (StringUtils.isNotBlank(update.getStatus())) {

			employee.setEnterpriseStatus(update.getStatus());

			// Changes made to GSSP-180
			if (employee.getEnterpriseStatus().equalsIgnoreCase("T"))
				employee.setStatus(AppConstants.STAFF_NO_STATUS);
		}
		if (StringUtils.isNotBlank(update.getAuthId())) {
			employee.getPerson().setAuthId(update.getAuthId());
		}
		if (log.isDebugEnabled()) {
			log.debug("updated employee {}", employee);
		}
	}

	/**
	 * Helper to match a job code to Scheduler role.
	 * 
	 * @param update
	 *            EmployeeDTO to examine for a job code
	 * @return Role instance or null
	 */
	private Role getRoleFromUpdate(EmployeeDTO update) {
		if (log.isDebugEnabled()) {
			log.debug("examining {} for matching role", update);
		}
		Role role = new Role();
		switch (update.getJobCode()) {
		case 1877:
		case 3014:
			role.setRoleName(AppConstants.STUDIO_ASSOCIATE_STRING);
			break;
		case 2005:
		case 3042:
			role.setRoleName(AppConstants.STUDIO_LEAD_STRING);
			break;
		case 2004:
		case 2164:
		case 3002:
		case 3013:
			role.setRoleName(AppConstants.STUDIO_MANAGER_STRING);
			break;

		// Changes made for GSSP-209
		/*
		 * case 1724: log.error("Processing DM");
		 * role.setRoleName(AppConstants.DISTRICT_MANAGER); break;
		 * 
		 * case 2021: log.error("Processing RVP");
		 * role.setRoleName(AppConstants.REGIONAL_VICE_PRESIDENT); break;
		 */

		default:
		}
		if (StringUtils.isNotBlank(role.getRoleName())) {
			role = roleDAO.get(role);
		} else {
			role = null;
		}
		if (log.isDebugEnabled()) {
			log.debug("returning {}", role);
		}
		return role;
	}
	
	
	/**
	 * Helper to match a job code to Scheduler role.
	 * 
	 * @param update
	 *            EmployeeDTO to examine for a job code
	 * @return Role instance or null
	 */
	private Role getRoleFromJobCode(Integer jobCode) {
		if (log.isDebugEnabled()) {
			log.debug("job Code", jobCode);
		}
		Role role = new Role();
		switch (jobCode) {
			// Changes made for GSSP-209
		
		  case 1724: log.error("Processing DM");
		  role.setRoleName(AppConstants.SITE_ADMIN_STRING); break;
		  
		  case 2021: log.error("Processing RVP");
		  role.setRoleName(AppConstants.SITE_ADMIN_STRING); break;
		  
		//--GSSP-379 -Admin Changes
		  case 2614: 
		  log.error("Processing ADM");
		  role.setRoleName(AppConstants.SITE_ADMIN_STRING); break;

		default:
		}
		if (StringUtils.isNotBlank(role.getRoleName())) {
			role = roleDAO.get(role);
		} else {
			role = null;
		}
		if (log.isDebugEnabled()) {
			log.debug("returning {}", role);
		}
		return role;
		
	
	}

	/**
	 * Returns a matching Location based on studio identifier, if found.
	 * 
	 * @param update
	 *            EmployeeDTO to examine
	 * @param updateSite
	 *            Site of the employee
	 * @return an instance of Location or null
	 */
	private Location getLocationFromDTO(EmployeeDTO update, Site updateSite) {
		if (log.isDebugEnabled()) {
			log.debug("finding location for {}", update);
		}
		Location location = null;
		List<Location> locations = locationManageService.findByExternalId(updateSite.getSiteId(), update.getStudio());
		switch (locations.size()) {
		case 1:
			/*
			 * Found a single match; use it for the new Employee
			 */
			location = locations.get(0);
			log.error("Location :" + location.getLocationName());
			break;

		case 0:
			/*
			 * No location found; generate a warning message
			 */
			if (log.isInfoEnabled()) {
				log.info("did not find a location for " + update);
			}
			break;

		default:
			/*
			 * Too many locations; generate a warning message
			 */
			log.warn("too many locations found for " + update);
		}
		if (log.isDebugEnabled()) {
			log.debug("returning {}", location);
		}
		return location;
	}

	/**
	 * Return a list of Employee instances that match the supplied person id.
	 * 
	 * @param personId
	 *            a person identifier to use
	 * @return a List of Employee instances that match
	 */
	public List<Employee> findByPersonId(long personId) {
		if (log.isDebugEnabled()) {
			log.debug("finding employees for {}", personId);
		}
		Criterion<Employee, Employee> criterion = EmployeeCriterion.findByPersonId(personId);
		List<Employee> employees = employeeDAO.search(criterion, DAOHelper.FETCH_PERSON);
		if (log.isDebugEnabled()) {
			log.debug("Returning employee list {}", employees);
		}
		return employees;
	}

	/**
	 * Create or updates an Scheduler RVP/DM record with values taken from the
	 * supplied RVP/DM record. Any exceptions are passed up to caller for
	 * decision and handling.
	 * 
	 * The method will retrieve an existing employee record from persistent
	 * storage that matches the external id of the update record.
	 * 
	 * @param update
	 *            an EmployeeDTO instance containing values to update
	 * @throws IOException
	 * @throws SolrServerException
	 */
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
	@Override
	public void updateFromAuthIdForRVPDM(EmployeeDTO update) throws SolrServerException, IOException {

		if (log.isDebugEnabled()) {
			log.debug("about to attempt an update for RVP/DM {}", update);
		}
		if (update == null) {
			log.warn("a null RVP/DM instance was passed to updateFromExternal");
			return;
		}

		if("SSI".equals(update.getSite()))update.setSite("GCS");
		if (StringUtils.isBlank(update.getSite())) {
			if (log.isInfoEnabled()) {
				log.info("updated RVP/DM {} does not have a site string");
			}
			return;
		}
		/*
		 * Find the site that matches the external id provided by update record.
		 * 
		 * Note: sites are a key element of data separation, so throw an
		 * exception if an exact match of 1 site is not received.
		 */
		List<Site> sites = siteService.findSitesByExternalId(update.getSite());
		if (sites.size() > 1) {
			log.warn("updated RVP/DM site  {} matches too " + "many sites; ignoring record",
					update.getSite());
			return;
		}
		if (sites.isEmpty()) {

			log.error("Site is empty");
			if (log.isInfoEnabled()) {
				log.info("updated RVP/DM site auth id {} does not match " + "an existing site; ignoring record",
						update.getAuthId());
			}
			return;
		}
		Site updateSite = sites.get(0);

		/*
		 * Now try to find an existing Employee record to update
		 */
		if (StringUtils.isBlank(update.getAuthId())) {
			log.warn("updated person {} does not have an auth id; " + "ignoring record", update);
			return;
		}
		Person person = null;
		List<Person> existing = findPersonByAuthId(updateSite.getSiteId(), update.getAuthId());

		switch (existing.size()) {
		case 1:
			
			personDAO.update(existing.get(0), personManagerService.getSystemUpdatePerson());
			log.error("After Saving person " +  update.getAuthId());
			
			List<PersonRole> updatedPersonRole = personRoleService.findByAuthId(updateSite.getSiteId(),
					update.getAuthId());
			if (update.getStatus().equalsIgnoreCase("T")) {
				
				if (null != updatedPersonRole && updatedPersonRole.size() > 0) {
					for (PersonRole personRole : updatedPersonRole) {
						
						Person dPerson = null;
						if(personRole.getPerson() != null){
							
						 dPerson = personRole.getPerson();
						}
						personRoleDAO.delete(personRole);
						if(dPerson != null){
							
						personDAO.delete(dPerson);
						
					}
					}
				}
			}	else{
				
				Role role = getRoleFromJobCode(update.getJobCode());
				for (PersonRole personRole : updatedPersonRole) {
					updatePersonFromDTO(personRole,update);
					personDAO.update(personRole.getPerson(), personManagerService.getSystemUpdatePerson());					
					personRole.setRole(role);
					personRoleDAO.update(personRole, personManagerService.getSystemUpdatePerson());
					
				}
			}
			break;
		case 0:
			
			
			Role role = getRoleFromJobCode(update.getJobCode());
			if (role != null) {
				person = createPersonFromDTO(update, updateSite);
				
			    personDAO.save(person,
                         personManagerService.getSystemUpdatePerson());
			    
				PersonRole personRole = new PersonRole();
				personRole.setPerson(person);				
				personRole.setRole(role);
				personRole.setSite(updateSite);
				personRoleDAO.save(personRole, personManagerService.getSystemUpdatePerson());

			}
			break;

		default:
			
			log.warn("updated Person auth id {} matched too many " + "existing records; ignoring update request",
					update.getAuthId());
			return;

		}
	}
	
	private void updatePersonFromDTO(PersonRole personRole, EmployeeDTO update) {
		if (log.isDebugEnabled()) {
			log.debug("about to update {} from {}", personRole, update);
		}
		if (StringUtils.isNotBlank(update.getFirstName())) {
			personRole.getPerson().setFirstName(update.getFirstName());
		}
		if (StringUtils.isNotBlank(update.getLastName())) {
			personRole.getPerson().setLastName(update.getLastName());
		}
		if (StringUtils.isNotBlank(update.getEmail())) {
			personRole.getPerson().setEmail(update.getEmail());
		}
		
		if (StringUtils.isNotBlank(update.getAuthId())) {
			personRole.getPerson().setAuthId(update.getAuthId());
		}
		if (log.isDebugEnabled()) {
			log.debug("updated employee {}", personRole);
		}
	}

	
}
