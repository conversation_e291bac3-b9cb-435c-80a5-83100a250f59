package com.guitarcenter.scheduler.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Marshaller;

import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.AvailabilityDAO;
import com.guitarcenter.scheduler.dao.criterion.AvailabilityCriterion;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dto.AvailabilityValueDTO2;
import com.guitarcenter.scheduler.dto.Break;
import com.guitarcenter.scheduler.dto.EmployeeScheduleDTO;
import com.guitarcenter.scheduler.dto.EmployeeScheduleDTO2;
import com.guitarcenter.scheduler.dto.EmployeeScheduleImport;
import com.guitarcenter.scheduler.dto.FinalFreeSlotsAndMaxMinDates;
import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.service.AvailabilityService;
import com.guitarcenter.scheduler.service.FileTransferScheduleReportService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.MailSenderService;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAvailableHoursDTO;
import com.guitarcenter.scheduler.webservice.dto.LocationProfileInfoDTO;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;


@Service("fileTransferScheduleReportService")
@Transactional
public class FileTransferScheduleReportServiceImpl implements FileTransferScheduleReportService {

    private static final Logger LOG = LoggerFactory.getLogger(FileTransferScheduleReportServiceImpl.class);

    @Autowired
    private AppointmentDAO appointmentDAO;
    
    @Value("${adp.dayForceStore}")
    private String dayForceStore;
    
    @Value("${adp.hostnameGcTrafic}")
    private String hostnameGcTrafic;

    @Value("${adp.usernameGcTrafic}")
    private String usernameGcTrafic;
    
    @Value("${adp.connectKeyGcTrafic}")
    private String connectKeyGcTrafic;
    
    @Value("${adp.fromPathGcTrafic}")
    private String fromPathGcTrafic;
    
    @Value("${adp.hostPathGcTraficDayForce}")
    private String hostPathGcTraficDayForce;
    
    @Value("${adp.usernameDayForce}")
    private String usernameDayForce;
    
    @Value("${adp.hostnameDayForce}")
    private String  hostnameDayForce;
    
    @Value("${adp.privateKeyPathDayForce}")
    private String  privateKeyPathDayForce;
    
    @Value("${adp.knownHostsPathDayForce}")
    private String  knownHostsPathDayForce;
    
    @Value("${adp.encryptFilePath}")
    private String encryptFilePath;
    
    @Value("${adp.encryptFilePathExtn}")
    private String encryptFilePathExtn;
    
    @Autowired
	@Qualifier("availabilityDAO")
	private AvailabilityDAO availabilityDAO;
    
    /*@Value("${crm.privateKeyPath}")
    private String privateKeyPath;

    @Value("${crm.knownHostsPath}")
    private String knownHostsPath;*/
    

    @Autowired
	private InstructorService instructorService;
    
    
    
    @Autowired
    private AvailabilityService availabilityService;
    
    @Autowired
  	private MailSenderService mailSenderService;
    
  //Changes made for GSSP-238
  	String EMAIL_TEMPLATE_FOR_CONFLICTING_APPOINTMENT = "conflictingAppointmentsTemplate.ftl";

     
    @Override
    public List<Map<String, Object>> generateEmployeeMasterScheduleReportJob(String startDate, String endDate) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Query master appointment list by profileId {} and time range in {} and {}",  startDate, endDate);
        }
        LOG.error("FileTransferScheduleReportService   GT");
        	List<EmployeeScheduleDTO> list = new ArrayList<EmployeeScheduleDTO>();
        	
        	//String startPeriod = getStartTime();
	        //String endPeriod = getEndTime();
	        String startPeriod = null;
	        String endPeriod = null;
	        startPeriod = startDate.replace(" ","T");
	        endPeriod = endDate.replace(" ","T");
        //Criterion<Appointment, EmployeeScheduleDTO> criterionAppointment = AppointmentCriterion.findMasterEmployeeDetails(startDate, endDate);
       // list = appointmentDAO.search(criterionAppointment);
        
	        //dayForceStore  = "101,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,151,152,153,154,155,156,157,181,199,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,232,233,235,236,237,238,241,242,243,244,245,246,251,261,310,311,312,320,321,322,323,324,330,331,332,333,334,335,336,337,338,339,341,342,343,344,345,350,351,352,360,361,362,363,364,366,367,392,393,394,410,421,422,423,424,426,431,432,433,440,441,442,444,445,446,447,448,449,450,451,452,453,454,455,456,460,461,462,463,464,465,466,467,468,469,470,471,480,481,489,490,492,493,494,495,496,497,498,510,511,512,519,521,522,523,524,525,531,540,550,551,552,553,554,555,556,611,612,613,614,615,616,617,618,619,620,621,622,623,625,626,631,632,633,710,711,712,713,720,721,722,723,724,726,727,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,750,751,752,753,759,760,761,762,763,764,765,766,767,769,770,771,772,773,774,775,776,777,779,780,781,782,783,784,785,786,787,789,790,791,792,809,810,811,812,814,815,816,817,818,819,821,822,824,825,826,827,828,829,830,831,832,833,838,839,841,842,843,845,846,847,851,852,853,854,855,856,857,858,860,861,862,863,910,987,999";
	        // dayForceStore = "817";
	        String[] dayForceStorel = dayForceStore.split(",");
	      	//List<String> storeList = Arrays.asList(elements); 
	      
        List<AvailabilityValueDTO2> instructorAvailability= new ArrayList<AvailabilityValueDTO2>();
        Criterion<Availability, List<AvailabilityValueDTO2>> criterion = AvailabilityCriterion.getByInstructorIdInsValAVL(dayForceStorel);	
		  instructorAvailability= availabilityDAO.get(criterion);
		  
		  LOG.error("instructorAvailabilit -----");
		  
		  
        List<Map<String, Object>> listObj = new ArrayList<Map<String, Object>>();
        encryptFilePath=System.getProperty("catalina.base");
        encryptFilePath = encryptFilePath+"/temp/";
        LOG.error("encryptFilePath  ......... "+encryptFilePath);
       
			// convertObjectToXML(instructorAvailability,startPeriod, endPeriod,encryptFilePath);
			 int batchSize = 250; 
			 // Number of records per file
			 int totalRecords = instructorAvailability.size();
			 int numFiles = (int) Math.ceil((double) totalRecords / batchSize);

      

             for (int fileIndex = 0; fileIndex < numFiles; fileIndex++) {
                 int startIndex = fileIndex * batchSize;
                 int endIndex = Math.min(startIndex + batchSize, totalRecords);

      

                 List<AvailabilityValueDTO2> batchList = instructorAvailability.subList(startIndex, endIndex);

                 convertObjectToXML(batchList,startPeriod, endPeriod,encryptFilePath);
            
         
        //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        
      
       Calendar cal = Calendar.getInstance();
       String pattern = "MMddyyHHmmss";
		cal.setTime(new Date());
		String currentDate = new SimpleDateFormat(pattern).format(cal.getTime());
	 		//encryptFilePathExtn =  "sample"+"_"+currentDate+"."+"csv";
	 		encryptFilePathExtn =  "EMPLOYEE_Schedule_"+currentDate+"."+"Ready";
	 		//encryptFilePathExtn =  "dayForceSchedule"+""+"."+"xml";
        fromPathGcTrafic=encryptFilePath+encryptFilePathExtn;;
       
        
      	//==System.out.println("Connected  ...start ");
       //==System.out.println("Connected  ...start ");
      	
      	//######################################################################################
        /*fromPathGcTrafic=encryptFilePath+encryptFilePathExtn;;
     	 hostnameGcTrafic= "ftstest01.dayforcehcm.com";
     	 usernameGcTrafic ="guitarcentertest";
     	 connectKeyGcTrafic="gbpnfl13";
     	 hostPathGcTraficDayForce="/Import/EmployeeScheduleImport/"; */
     	 
     	 
     	 
      	/*for adp.properties
      	 * 
      	 * 
      	 * 
      	adp.hostPathGcTraficDayForce=/Import/EmployeeScheduleImport
      	adp.usernameDayForce=gcetlusr2
      	adp.hostnameDayForce =fts01.dayforcehcm.com
      	adp.privateKeyPathDayForce =/var/lib/gcss/.ssh/id_rsa
      	adp.knownHostsPathDayForce = /var/lib/gcss/.ssh/known_hosts
      	*/
        //####################################################################################################	
        /*	 ChannelSftp sftpChannel =null;
           try {
     		  //String user ="musicandarts"; // username for remote host
     	 	   String password =connectKeyGcTrafic; // password of the remote host

     		 
     		JSch jsch = new JSch();
     		Properties config = new Properties();
     		// config.put("kex", "diffie-hellman-group-exchange-sha256,diffie-hellman-group14-sha1");
     		jsch.setConfig(config);
       
       
       
       
     		 Session session = jsch.getSession(usernameGcTrafic, hostnameGcTrafic, 22);
     		  // Properties config = new Properties();
     		    config.put("StrictHostKeyChecking", "no");
     		   session.setConfig(config);;
     		   session.setPassword(connectKeyGcTrafic);
     		   session.setConfig("PreferredAuthentications","publickey,keyboard-interactive,password");
     		   session.connect(); 
     		 
     		LOG.error("Connected  ...1 ");

     		  sftpChannel = (ChannelSftp) session.openChannel("sftp");
     		  sftpChannel.connect();
     		  sftpChannel.put(fromPathGcTrafic,hostPathGcTraficDayForce);
     		  sftpChannel.exit();
     		  sftpChannel.disconnect();

     		  session.disconnect();  
     		 LOG.error("Connected  ...End "); 
     		 }catch(Exception ex){
 
     			 LOG.error("expection .......... "+ex);
     		    
     		 } finally {
   			
   			 // Release any channels and sessions that may be in use
   			 
   			if (sftpChannel != null) {
   				sftpChannel.disconnect();
   				try {
   					sftpChannel.getSession().disconnect();
   					sftpChannel=null;
   				} catch (JSchException jse) {
   					LOG.error("Caught a JSchException setting up CRM transfer", jse);
   				}
   			}
   			 
   		} 
            */
        	
     	 //  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
        	  String sftpPGPdir = hostPathGcTraficDayForce;
        	 	  
        			 
         
       	ChannelSftp sftp = null;
          try {
              sftp = getChannel();
              try {

      			  sftp.cd(sftpPGPdir);;
                 // File f1 = new File(encryptFilePath+encryptFilePathExtn);
      			  File f1 = new File(fromPathGcTrafic); 
                  sftp.put(new FileInputStream(f1), f1.getName(), ChannelSftp.OVERWRITE);

              } catch (Exception sftpe) {
      
              	LOG.warn("Caught a JSchException setting up CRM transfer", sftpe);
              }
    		}catch ( Exception sftpe) {
    		    
          	LOG.warn("Caught a JSchException setting up CRM transfer", sftpe);
          }
          finally {
    			
    			 //* Release any channels and sessions that may be in use
    			 
    			if (sftp != null) {
    				sftp.disconnect();
    				try {
    					sftp.getSession().disconnect();
    				} catch (JSchException jse) {
    					LOG.warn("Caught a JSchException setting up CRM transfer", jse);
    				}
    			}
    			 
    		}  
             }
           //%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
           //####################################################################################################
          //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        //==System.out.println(" GENERATE_MASTER_SCHEDULE_REPORT_MAPPING End" );
          return listObj;
      }
     
      // usernameDayForce  = "gcetlusr2";
      //  hostnameDayForce ="fts01.dayforcehcm.com";
      //  privateKeyPathDayForce ="/var/lib/gcss/.ssh/id_rsa";
       //  knownHostsPathDayForce = "/var/lib/gcss/.ssh/known_hosts";
      private ChannelSftp getChannel()
              throws IntegrationServiceException
          {
      	
              if (LOG.isDebugEnabled()) {
                  LOG.debug("trying to setup channel for SFTP");
              }
              ChannelSftp channel = null;
              if (isValidEnvironment()) {
                  try {
                      JSch jsch = new JSch();
					    LOG.error("  privateKeyPath  "+privateKeyPathDayForce);
                      jsch.addIdentity(privateKeyPathDayForce);
                      //jsch.setKnownHosts(knownHostsPath);
                      Session session = jsch.getSession(usernameDayForce, hostnameDayForce);
                      session.connect();
                      channel = (ChannelSftp) session.openChannel("sftp");
					  LOG.error("before Connect");
                      channel.connect();
					  LOG.error("After Connect");
                  } catch (JSchException jse) {
                      LOG.error("Caught a JSchException setting up Dayforce transfer", jse);
                      throw new IntegrationServiceException("SSH setup issue", jse);
                  }
                  catch (Exception jse) {
                      LOG.error("Caught a JSchException setting up Dayforce transfer", jse);
                      throw new IntegrationServiceException("SSH setup issue", jse);
                  }
              } else {
				  LOG.error("Dayforce integration properties are invalid" );
                  throw new IntegrationServiceException("Dayforce integration properties are invalid");
              }
              if (LOG.isDebugEnabled()) {
                  LOG.debug("returning {}", channel);
              }
              return channel;
          }
      
      
      private boolean isValidEnvironment() {
          if (LOG.isDebugEnabled()) {
              LOG.debug("validating environment: hostname = {}, username = {}, " +
                        "privateKeyPath = {}, knownHostsPath = {}", hostnameDayForce,
                        usernameDayForce, privateKeyPathDayForce, knownHostsPathDayForce);
          }
          boolean isValid = StringUtils.isNotBlank(hostnameDayForce) &&
              StringUtils.isNotBlank(usernameDayForce) &&
              StringUtils.isNotBlank(privateKeyPathDayForce) &&
              //StringUtils.isNotBlank(knownHostsPath) &&
              new File(privateKeyPathDayForce).canRead();
			  //&&
              //new File(knownHostsPath).canRead();
          if (LOG.isDebugEnabled()) {
              LOG.debug("returning {}", isValid);
          }
          return isValid;
      }
      
    
    
    private  void convertObjectToXML( List<AvailabilityValueDTO2> instructorAvailability,String startPeriod,String endPeriod,String encryptFilePath) {
        // create JAXB context and instantiate marshaller  
    	 try{
    	//System.out.println("convertObjectToXML nw");
    	JAXBContext context = JAXBContext.newInstance(EmployeeScheduleDTO2.class);
        Marshaller m = context.createMarshaller();
        
        
        /* NamespacePrefixMapper mapper = new NamespacePrefixMapper() {
            public String getPreferredPrefix(String namespaceUri, String suggestion, boolean requirePrefix) {
                if ("http://www.w3.org/2001/XMLSchema-instance".equals(namespaceUri) && !requirePrefix)
                    return "";
                return "";
            }
        };
        m.setProperty("com.sun.xml.internal.bind.namespacePrefixMapper", mapper); */
        List<EmployeeScheduleDTO> finalListDto = new ArrayList<EmployeeScheduleDTO>();
        
       // XmlStreamWriter xmlStreamWriter =xmlStreamWriter.writeProcessingInstruction("xml", "version=\"1.0\" encoding=\"UTF-8\"");
         m.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);
          m.setProperty("com.sun.xml.bind.xmlDeclaration", true);
          
          String staDate = startPeriod.replace("T00:00:00","");
          
          String ensDate = endPeriod.replace("T23:59:50","");
          
          String strMonDate = getDateDayStrRVs(staDate);
          
          String strMonDateMain = getDateDayStrRVs(staDate)+"T00:00:00";
          String endMonDateMain = getDateDayStrRVs2(ensDate)+"T23:59:59";
          
          String enDate = endPeriod.replace("T00:00:00","");
          String startDt = getDateDayStrRVs(staDate);
          
          String endDt = getDateDayStrRVs(enDate);
        
          
  //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~        
          for(int i=0;!startDt.equals(endDt);i++ ){
           	  
           	  
           	  LOG.error("startDt     "+startDt);
           	
         
           	String startMonDt =startDt;
          //staDate = getDateDayStrRVs(staDate);
            staDate= getMMddToDDmm(startDt);
          Date startDateDy = getDateFromStrDDMM(staDate);
          int numb = getDayNumber(startDateDy);
          //List<Break> breakListA = new ArrayList<Break>();
          for(AvailabilityValueDTO2 alvInstr : instructorAvailability){
        	   try{
        		   
            LOG.error(" ....   "+alvInstr.getProfilelId());
        	 LOG.error(" ....   "+alvInstr.getProfilelId()); 
        	 LOG.error(" ....   "+alvInstr.getInstructor_id());
        	 LOG.error(" ....   "+alvInstr.getLocationId());
        	 LOG.error(" ....   "+alvInstr.getInsExternalId());
        	 
        	 List<Break> breakListA = new ArrayList<Break>();
        	 
        	 EmployeeScheduleDTO insAvalbility = new EmployeeScheduleDTO();
        	 if(numb ==1)  insAvalbility.setStartTime(alvInstr.getSundayStartTime());
     		if(numb ==1)  insAvalbility.setEndTime(alvInstr.getSundayEndTime());
     		if(numb ==2)  insAvalbility.setStartTime(alvInstr.getMondayStartTime());
     		if(numb ==2)  insAvalbility.setEndTime(alvInstr.getMondayEndTime());
     		if(numb ==3)  insAvalbility.setStartTime(alvInstr.getTuesdayStartTime());
     		if(numb ==3)  insAvalbility.setEndTime(alvInstr.getTuesdayEndTime());
     		if(numb ==4)  insAvalbility.setStartTime(alvInstr.getWednesdayStartTime());
     		if(numb ==4)  insAvalbility.setEndTime(alvInstr.getWednesdayEndTime());
     		if(numb ==5)  insAvalbility.setStartTime(alvInstr.getThursdayStartTime());
     		if(numb ==5)  insAvalbility.setEndTime(alvInstr.getThursdayEndTime());
     		if(numb ==6)  insAvalbility.setStartTime(alvInstr.getFridayStartTime());
     		if(numb  ==6)  insAvalbility.setEndTime(alvInstr.getFridayEndTime());
     		if(numb ==7)  insAvalbility.setStartTime(alvInstr.getSaturdayStartTime());
     		if(numb ==7)  insAvalbility.setEndTime(alvInstr.getSaturdayEndTime());
     		
     		
     		 //==System.out.println("  getStartTime  "+insAvalbility.getStartTime());
     		 //==System.out.println("  getEndDateTime  "+insAvalbility.getEndTime());
     		
     		 
     		if(insAvalbility.getStartTime() != null && !"null".equals(insAvalbility.getStartTime())){
     		AvailabilityValueDTO2 availValDTO2  = new AvailabilityValueDTO2();
     		
     		/*availValDTO2.setInstructor_id(instructor_id);
     		availValDTO2.setProfilelId(profilelId);
     		availValDTO2.setLocationId(locationId);*/
     		 
     		FinalFreeSlotsAndMaxMinDates finalFreeSlotsAndMaxMinDates =getInstructorAvailabilitySlots(alvInstr,staDate);
            List<InstructorAvailableHoursDTO> breakTime = null;
            
            try {
				breakTime = finalFreeSlotsAndMaxMinDates.getFreeSlotsList();
			} catch (Exception e) {
				LOG.error("Exception  ...1 "+e.getMessage());
				 
			}
            
            InstructorAvailableHoursDTO instructorAvailableHoursDTO = finalFreeSlotsAndMaxMinDates.getInstructorAvailableHoursminMaxDTO();
         
         for(InstructorAvailableHoursDTO hy:breakTime ){
        	 
        	//System.out.println(hy);
        	 
         }
         
         //**********************************####################################  alvInstr
          String insDD = "";
       		 String storeDD ="";
         
        //String staDate = startPeriod.replace("T00:00:00","");
    	//InstructorServiceResponseDTO result = instructorService.getInstructorDayForceAvailability(insDD,storeDD,staDate);

    	//List<Lessons> lsr = result.getLessons();
       		//List<Break> breakListA = new ArrayList<Break>();
    //	List<Times> tms= new ArrayList<Times>();
    
    	for(InstructorAvailableHoursDTO tmsVal :breakTime){
    		//==System.out.println(tmsVal);
    		
    		/* int currnTime1 = tmsVal.getAppointmentStartTime().toDateTimeToday().getMinuteOfDay();
    		int currnTime2 = tmsVal.getAppointmentEndTime().toDateTimeToday().getMinuteOfDay();
    		int diff = currnTime2 - currnTime1;
    		if(diff<=60){
    			
    		}else{ */
    			
    		
	    		Break brkDTO= new Break();
	    		String avlStartTime =startMonDt+"T"+tmsVal.getAppointmentStartTime();
	    		String avlEndTime =startMonDt+"T"+tmsVal.getAppointmentEndTime();
	    		
	    		  avlStartTime =avlStartTime.replace(":00.000", ":00");
	    		  avlEndTime =avlEndTime.replace(":00.000", ":00");
	    		brkDTO.setStartTime(avlStartTime);
				brkDTO.setEndTime(avlEndTime);
	    		 
	    			brkDTO.setBreakType("m");
	    		 
	    		breakListA.add(brkDTO);
    		 //}
    	}
    	  
//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~    	
    	//**********************************##################################################
        	
    	EmployeeScheduleDTO esd = new EmployeeScheduleDTO();
        esd.setBreakActvity(breakListA);
      


    	 
    
    esd.setInstructorId(alvInstr.getInsExternalId());
    esd.setStore(alvInstr.getLocationId());
    
     ///AVL Time
    String inStartTime =instructorAvailableHoursDTO.getAppointmentStartTime()+"";
	  String inEndTime =instructorAvailableHoursDTO.getAppointmentEndTime()+"";
	  
	  inStartTime = inStartTime.replace(":00.000", ":00");
	  inEndTime = inEndTime.replace(":00.000", ":00");
	  
    esd.setStartTime(startMonDt+"T"+inStartTime+"");
    esd.setEndTime(startMonDt+"T"+inEndTime+"");
    
    
    finalListDto.add(esd);
    
        	   }
     		
     		//~~~~~~~~~~~~~~~~~~~~~~
          }
          catch(Exception e){
        	  //e.printStackTrace();
        	  LOG.error("Exception  ...1 "+e.getMessage());
          }
          }
          startDt =addOneDay(startDt);
          }
        //List<EmployeeScheduleDTO> finalListDto = new ArrayList<EmployeeScheduleDTO>();
 
          	 
        //-----------------------------------------
        //List<EmployeeScheduleDTO> minInstructorStrTime = new ArrayList<EmployeeScheduleDTO>();
        
       // List<Break> breakActvity5 = new ArrayList<Break>();
        //System.out.println(breakListA);
        
          	//for (EmployeeScheduleDTO  url : minInstructorStrTime) {
          		
          	 
           	// EmployeeScheduleDTO esd = new EmployeeScheduleDTO();
               //esd.setBreakActvity(breakListA);
             


           	 
           
          // esd.setInstructorId(esd.getInstructorId());
          // esd.setStore(esd.getInstructorId());
           
           
          // finalListDto.add(esd);
           //}
          	 //-------------------------------------------
          	 ///EmployeeScheduleDTO employeeScheduleDTO2 = new EmployeeScheduleDTO();
          	EmployeeScheduleImport output = new  EmployeeScheduleImport();
          	
          	//List<EmployeeScheduleDTO>  employeeScheduleDTO = new ArrayList<EmployeeScheduleDTO>();
 
          	output.setEmployeeSchedule(finalListDto);
         
        
        output.setStartTime(strMonDateMain);
        output.setEndTime(endMonDateMain);
        output.setDeleteLevel("ALL");
        output.setValidationLevel("NONE");
       // empSch.setEmployeeScheduleDTO(empSch);
        // Write to System.out
        //System.out.println("convertObjectToXML 22 ");
       // m.marshal(empSch, System.out);
        String bas = System.getProperty("catalina.base");
        //System.out.println("Good >>==|>   "+bas);
        // Write to File
        
        EmployeeScheduleDTO2 output2 = new  EmployeeScheduleDTO2();
        
        output2.setEmployeeScheduleImport(output);
        Calendar cal = Calendar.getInstance();
        String pattern = "MMddyyHHmmss";
 		cal.setTime(new Date());
 		String currentDate = new SimpleDateFormat(pattern).format(cal.getTime());
      // encryptFilePathExtn =  "EMPLOYEE_Schedule_"+currentDate+"."+"xml";
        m.marshal(output2, new File(encryptFilePath+"EMPLOYEE_Schedule_"+currentDate+"."+"Ready"));
        //==System.out.println("Good 22 ");
    } catch(Exception e){
    	//e.printStackTrace();
    	LOG.error("Exception  .........3 "+e.getMessage());
    }
    }



    private String buildDateStr(Date date) {
        return new DateTime(date).toString(DateTimeFormat.forPattern(DateTimeUtil.DATE_PATTERN_SLASH));
    }
//added for GSSP-200
    private String buildDateStr1(Date date) {
        return new DateTime(date).toString(DateTimeFormat.forPattern(DateTimeUtil.DATE_PATTERN_HIPHEN));
    }
    /**
     * Helper method to build time string from a Date
     *
     * @param date
     * @return
     */
    private String buildTimeStr(Date date) {
        String result = new DateTime(date).toString(DateTimeFormat.forPattern("hh:mm a"));
        if ("11:59 PM".equals(result)) {
            result = "12:00 AM";
        }
        return result;
    }
  //GSSP-214 changes
    private String buildTimeStr1(String string) {
//        String result = new DateTime(string).toString(DateTimeFormat.forPattern(""));
//        if ("11:59 PM".equals(result)) {
//            result = "12:00 AM";
//        }
        return string;
    }
    /**
     * Helper method to build instructor name from a instructor object
     *
     * @param i
     * @return
     */
    private String buildInstructorName(Instructor i) {
        String instructorName = "";
        if (null != i) {
            String firstName = i.getPerson().getFirstName();
            String lastName = i.getPerson().getLastName();
            if (StringUtils.isNotBlank(firstName)) {
                instructorName += firstName + " ";
            }
            if (StringUtils.isNotBlank(lastName)) {
                instructorName += lastName;
            }
        }
        return instructorName;
    }
    //for GSSP-214
    private String buildCancelledUser(Instructor i) {
        String cancelledUser = "";
        if (null != i) {
            String firstName = i.getPerson().getFirstName();
            String lastName = i.getPerson().getLastName();
            if (StringUtils.isNotBlank(firstName)) {
            	cancelledUser += firstName + " ";
            }
            if (StringUtils.isNotBlank(lastName)) {
            	cancelledUser += lastName;
            }
        }
        return cancelledUser;
    }
    private String buildCustomerName(Collection<Customer> customerList) {
        StringBuilder name = new StringBuilder();
        if (null != customerList && !customerList.isEmpty()) {
            for (Customer c : customerList) {
                String firstName = c.getPerson().getFirstName();
                String lastName = c.getPerson().getLastName();
                if (StringUtils.isNotBlank(firstName)) {
                    name.append(firstName);
                    name.append(" ");
                }
                if (StringUtils.isNotBlank(lastName)) {
                    name.append(lastName);
                }
                name.append(", ");
            }
            name.deleteCharAt(name.length() - 2);
        }
        return name.toString();
    }     
    private String buildDuration(float minutes) {//For GCSS-520,to support the half hour in creating app,change the paramater minutes to float
        String duration = "";
        if (minutes < 60) {
            duration = minutes + " minutes";
        } else if (60 == minutes) {
            duration = "1 Hour";
        } else {
            duration = (minutes / 60) + " Hrs";
        }
        duration = duration.replace(".0", "");
        return duration;
    }
   // 2011-04-13T09:00:00  duration.replace(".0", "");
    private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-dd-MM HH:mm:ss");
    private static String getStartTime() {

      	 Calendar day = Calendar.getInstance();
      	
   		day.set(Calendar.MILLISECOND, 0);
   		day.set(Calendar.SECOND, 0);
   		day.set(Calendar.MINUTE, 0);
   		day.set(Calendar.HOUR_OF_DAY, 0);
   		// day.add(Calendar.DATE, -1);
      
          return simpleDateFormat.format(day.getTime());
      }

       private static String getEndTime() {

    	   Calendar day = Calendar.getInstance();
    	   	
    		day.set(Calendar.MILLISECOND, 0);
    		day.set(Calendar.SECOND, 0);
    		day.set(Calendar.MINUTE, 59);
    		day.set(Calendar.HOUR_OF_DAY, 23);
    		 //day.add(Calendar.DATE, -1);
    		day.add(Calendar.DATE, 15);
      
          return simpleDateFormat.format(day.getTime());
       }
       
   	 private Date getDateFromStrDDMM(String date){
	    	//2021-07-12 15:00:00
	    	String startTime = null;
	 
	 	   DateFormat sdf = new SimpleDateFormat("yyyy-dd-MM");
	 			Date insAvlStart = null;
	 
	 			try{
	 				insAvlStart = sdf.parse(date);
	 	 
	 			} catch (Exception e) {
	 				// TODO Auto-generated catch block
	 				e.printStackTrace();
	 			}
	 			 
	          return  insAvlStart;
	    }
	private String getDayStr(String num){
		Map<String,String> mb = new HashMap<String,String>();
		String output = "";
		mb.put("0", "Sun");
		mb.put("1", "Mon");
		mb.put("2", "Tue");
		mb.put("3", "Wed");
		mb.put("4", "Thu");
		mb.put("5", "Fri");
		mb.put("6", "Sat");
		output = mb.get(num);
		return output;
	 
	}
	
	public static int getDayNumber(Date date) {
	    Calendar cal = Calendar.getInstance();
	    cal.setTime(date);
	    return cal.get(Calendar.DAY_OF_WEEK);
	}
	
/*	public static void main(String args[]){
		
		AvailabilityValueDTO2 availabilityValueDTO2 = new AvailabilityValueDTO2();
		availabilityValueDTO2.setProfilelId("583");
		availabilityValueDTO2.setInstructor_id("21933");  
		
		 String staDateString ="2023-16-02";
		
		//List<InstructorAvailableHoursDTO> getInd = getInstructorAvailabilitySlots( availabilityValueDTO2, staDateString);
		
	}*/

	public   FinalFreeSlotsAndMaxMinDates getInstructorAvailabilitySlots(AvailabilityValueDTO2 availabilityValueDTO2,String staDate){ 
		
		
		FinalFreeSlotsAndMaxMinDates finalFreeSlotsAndMaxMinDates = null;
		
		List<InstructorAvailableHoursDTO> InstructorAVLServiceList = new ArrayList<InstructorAvailableHoursDTO>();

		  InstructorAVLServiceDTO instructorServiceDTO = new InstructorAVLServiceDTO();
		  
		  //yyyy-mm-dd
		  String dateSt = getDateDayStrRVs(staDate);
		 
		  instructorServiceDTO.setInstructorStatus("Active");
		 
			instructorServiceDTO.setInstructorId(availabilityValueDTO2.getInstructor_id());
			instructorServiceDTO.setStartDate(dateSt);
		 //==System.out.println("------------------------------"+instructorServiceDTO.getStartDate());
			LocationProfileInfoDTO locationProfileDetails = new LocationProfileInfoDTO();

			  locationProfileDetails.setInstructorId(Long.parseLong(availabilityValueDTO2.getInstructor_id())); 
			 
	                 
               locationProfileDetails.setProfileID(Long.parseLong(availabilityValueDTO2.getProfilelId()));

             //  locationProfileDetails.setLocationID(Long.parseLong(availabilityValueDTO2.getLocationId()));
			 
			
			//LocationProfileInfoDTO locationProfileDetails 
			
			try {
				//System.out.println(instructorService.getInstructorFreeSLots(locationProfileDetails,instructorServiceDTO));
				  finalFreeSlotsAndMaxMinDates =instructorService.getInstructorFreeSLots(locationProfileDetails,instructorServiceDTO);
					
				  //sysout
					List<InstructorAvailableHoursDTO> dd = finalFreeSlotsAndMaxMinDates.getFreeSlotsList();
					System.out.println(dd);
					InstructorAVLServiceList =dd;
					
					for(InstructorAvailableHoursDTO th:dd){
						LOG.error("Start-------> "+th.getAppointmentStartTime());
						LOG.error("End-------> "+th.getAppointmentEndTime());
						
					}
			} catch (Exception e) {
				// TODO Auto-generated catch block
				// e.printStackTrace();
				LOG.error("Exception  ...freeslot "+e.getMessage());
			}
			
			
		return finalFreeSlotsAndMaxMinDates;

	} 


/*public static void main(String args[]){
	String ss = "2023-16-02";
	System.out.println("getDateDayStrRVs  "+getDateDayStrRVs(ss));
}*/
private static String getDateDayStrRVs(String date){
	//2021-07-12 15:00:00
	String startTime = null;

	   DateFormat sdf = new SimpleDateFormat("yyyy-dd-MM");
			Date insAvlStart = null;

			try{
				insAvlStart = sdf.parse(date);
				Calendar c = Calendar.getInstance(); 
				c.setTime(insAvlStart); 
				//c.add(Calendar.DATE, -1);
				insAvlStart = c.getTime();
	 
			} catch (Exception e) {
				// TODO Auto-generated catch block
				//e.printStackTrace();
				LOG.error("Exception getDateDayStrRVs ...1 "+e.getMessage());
			}
			DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");  
     startTime = dateFormat.format(insAvlStart);
   return  startTime;
}	

private static String getDateDayStrRVs2(String date){
	//2021-07-12 15:00:00
	String startTime = null;

	   DateFormat sdf = new SimpleDateFormat("yyyy-dd-MM");
			Date insAvlStart = null;

			try{
				insAvlStart = sdf.parse(date);
				Calendar c = Calendar.getInstance(); 
				c.setTime(insAvlStart); 
				c.add(Calendar.DATE, -1);
				insAvlStart = c.getTime();
	 
			} catch (Exception e) {
				// TODO Auto-generated catch block
				//e.printStackTrace();
				LOG.error("Exception getDateDayStrRVs ...1 "+e.getMessage());
			}
			DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");  
     startTime = dateFormat.format(insAvlStart);
   return  startTime;
}	
     static   String addOneDay(String date) {
    return LocalDate.parse(date).plusDays(1).toString();
  }
   
     
     private static String getMMddToDDmm(String date){
    		//2021-07-12 15:00:00
    		String startTime = null;

    		   DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    				Date insAvlStart = null;

    				try{
    					insAvlStart = sdf.parse(date);
    					Calendar c = Calendar.getInstance(); 
    					c.setTime(insAvlStart); 
    					//c.add(Calendar.DATE, -1);
    					insAvlStart = c.getTime();
    		 
    				} catch (Exception e) {
    					// TODO Auto-generated catch block
    					//e.printStackTrace();
    					LOG.error("Exception getDateDayStrRVs ...1 "+e.getMessage());
    				}
    				DateFormat dateFormat = new SimpleDateFormat("yyyy-dd-MM");  
    	     startTime = dateFormat.format(insAvlStart);
    	   return  startTime;
    	}	
     
     
  /*   public static void main(String args[]){
    	 String startDt = "2023-04-07";
         String endDt = "2023-04-22";
         
         for(int i=0;!startDt.equals(endDt);i++ ){
       	  
       	  
       	  System.out.println("startDt     "+startDt);
       	startDt =addOneDay(startDt);
         }
     }*/
}
