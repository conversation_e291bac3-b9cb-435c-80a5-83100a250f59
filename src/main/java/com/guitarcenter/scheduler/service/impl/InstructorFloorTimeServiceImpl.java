package com.guitarcenter.scheduler.service.impl;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.InstructorFloorTimeCriterion;
import com.guitarcenter.scheduler.dto.InstructorFloorTimeDTO;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.service.InstructorFloorTimeService;
import com.guitarcenter.scheduler.service.JobNotificationEmailServiceImpl;
import com.guitarcenter.scheduler.service.MailSenderService;

//New class created for GSSP - 272

@Service("instructorFloorTimeService")
public class InstructorFloorTimeServiceImpl implements InstructorFloorTimeService{
	

private static final Logger		LOGGER	= LoggerFactory.getLogger(InstructorFloorTimeServiceImpl.class);    
    @Autowired
    @Qualifier("instructorDAO")    
    private InstructorDAO mInstructorDAO;
	
  	String EMAIL_TEMPLATE_INSTRUCTOR_FLOORTIME= "instructorFloorTimeTemplate.ftl";

    @Autowired
	@Qualifier("mailSenderService")
  	private MailSenderService mMailSenderService;
    //Miscellaneous task
    @Autowired
    private JobNotificationEmailServiceImpl jobNotificationEmailServiceImpl;

	public InstructorDAO getInstructorDAO() {
		return mInstructorDAO;
	}
	
	public void setInstructorDAO(InstructorDAO instrDAO) {
		this.mInstructorDAO = instrDAO;
	}

	public MailSenderService getMailSenderService() {
		return mMailSenderService;
	}
	
	public void setMailSenderService(MailSenderService mailService) {
		this.mMailSenderService = mailService;
	}
	
      @Override
      public void generateInstructorFloorTimeReport() throws Exception {
	
           HSSFWorkbook workbook = new HSSFWorkbook();
           Map<String, Object> model = new HashMap<String, Object>();
           model.put("instructorFloorTime",getInstructorFloorTime());
           createInstructorFloorTimeReport(model,workbook);
           Map<String,Object> dataMap =  renderDataForIntructorFloorTimeEmail();
           String fileName = AppConstants.INSTRUCTOR_FLOORTIME_FILE_NAME + new SimpleDateFormat("yyyyMMdd").format(new Date()) + AppConstants.FILE_EXTENTION;
           mMailSenderService.sendMailWithAttachment(dataMap, EMAIL_TEMPLATE_INSTRUCTOR_FLOORTIME, workbook, 
       			AppConstants.FILE_PATH, fileName);
           
}
      private List<InstructorFloorTimeDTO> getInstructorFloorTime()
      {   	  
    	  DateTime endTime = new DateTime().minusDays(1);
    	  DateTime startTime = new DateTime().minusDays(7);
    	  DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN);
    	  DateTimeFormatter dateTimeFormatter2 = DateTimeFormat.forPattern(DateTimeUtil.DATETIME_PATTERN);
    	  String  endDay = dateTimeFormatter.print(endTime);
    	  String startDay = dateTimeFormatter2.print(startTime);
    	  DateTime endDateTime = DateTimeUtil.handleendDateTime(endDay);
      	  DateTime startDateTime = DateTimeUtil.handleStartDateTime(startDay);
      	  Date queryEndDate = endDateTime.toDate();
      	  Date queryStartDate = startDateTime.toDate();
    	  Criterion<Instructor, InstructorFloorTimeDTO> criterion = InstructorFloorTimeCriterion.findInstructorFloorTime(queryStartDate,queryEndDate);
    	  List<InstructorFloorTimeDTO> instrList = mInstructorDAO.search(criterion);
    	  if(instrList != null){
        	  LOGGER.info(" getInstructorFloorTime(), instrList size= "+instrList.size());
              }else{
            	  LOGGER.info(" getInstructorFloorTime(), instrList size is null");
              }
              return instrList;
      }
      private void createInstructorFloorTimeReport(Map<String, Object> model, HSSFWorkbook workbook)
      {
    	  HSSFRow row;		
			HSSFSheet sheet = workbook
					.createSheet("Weekly Floor Report");
			HSSFCellStyle style = workbook.createCellStyle();
			
			HSSFFont font = workbook.createFont();
			font.setFontHeightInPoints((short) 10);
			font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
			style.setFont(font);
			
			sheet.setColumnWidth(0, 5500);
			sheet.setColumnWidth(1, 5000);
			sheet.setColumnWidth(2, 6000);
			sheet.setColumnWidth(3, 5500);
			int rownum = 0;
			row = sheet.createRow(rownum++);
			row.createCell(0).setCellValue("Store");
			row.getCell(0).setCellStyle(style);
			row.createCell(1).setCellValue("Instructor ID");
			row.getCell(1).setCellStyle(style);
			row.createCell(2).setCellValue("Instructor Name");			
			row.getCell(2).setCellStyle(style);
			row.createCell(3).setCellValue("Floor Time(In Minutes)");
			row.getCell(3).setCellStyle(style);
			
			for ( InstructorFloorTimeDTO instrFloorDto : getInstructorFloorTime()) {
					row = sheet.createRow(rownum++);
					
					String store = instrFloorDto.getLocationID() + " "+ instrFloorDto.getLocationName();
					row.createCell(0).setCellValue(store);
					
					row.createCell(1).setCellValue(instrFloorDto.getInstructorID());

					String instrFullName = instrFloorDto.getFirstName()+" "+instrFloorDto.getLastName(); 
					row.createCell(2).setCellValue(instrFullName);
					
					row.createCell(3).setCellValue(instrFloorDto.getDuration());
				}
      }
      
      private Map<String, Object> renderDataForIntructorFloorTimeEmail(){
    	  Map<String, Object> dataMap = new HashMap<String, Object>();
    	  dataMap.put(AppConstants.SUBJECT , AppConstants.INSTRUCTOR_FLOORTIME_EMAIL_SUBJECT);
    	  List<InstructorFloorTimeDTO> recipientList = getRecipientEmailIds(); 
    	  if(recipientList != null && recipientList.size()>0)
    	  {
    		  String[] recipientArray = new String[recipientList.size()];
    		  int count = 0;
    		  for(InstructorFloorTimeDTO instructorDto : recipientList )
    		  {
    			  recipientArray[count] = instructorDto.getRecipientId();
    			  count ++;
    		  }
    		  dataMap.put(AppConstants.EMAIL_TYPE_TO, recipientArray);
    	  }
    	  else{
 			 LOGGER.info(" getRecipientEmailIds(), recipientList null ");
 		}
   		
 		
 		String environment=jobNotificationEmailServiceImpl.getEnvironment();
				//Miscellaneous task
 				if(environment.equalsIgnoreCase("Production"))
 				{	
 					dataMap.put(AppConstants.EMAIL_TYPE_CC, AppConstants.JOB_NOTIFICATION_EMAIL_SEND_LIST);
 				}
 				else
 				{
 					dataMap.put(AppConstants.EMAIL_TYPE_CC, AppConstants.JOB_NOTIFICATION_EMAIL_SEND_LIST_QA);			
 				}
   		return dataMap;
    	  
      }
      
      private List<InstructorFloorTimeDTO> getRecipientEmailIds() {
          Criterion<Instructor, InstructorFloorTimeDTO> criterion = InstructorFloorTimeCriterion.getRecipientEmailIds();
          List<InstructorFloorTimeDTO> dtoList = mInstructorDAO.search(criterion);
          if(dtoList != null){
    	  LOGGER.info(" getRecipientEmailIds(), dtoList size= "+dtoList.size());
          }else{
        	  LOGGER.info(" getRecipientEmailIds(), dtoList size is null");
          }
		  
          return dtoList;
      }
		

    	  
}
