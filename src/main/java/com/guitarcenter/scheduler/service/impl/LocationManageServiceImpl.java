package com.guitarcenter.scheduler.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.apache.solr.client.solrj.SolrServerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.LocationDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.LocationCriterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dto.LocationDTO;
import com.guitarcenter.scheduler.dto.LocationDistRegDTO;
import com.guitarcenter.scheduler.dto.SearchDTO;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.LocationProfile;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.service.LocationManagerService;
import com.guitarcenter.scheduler.service.PersonManagerService;
import com.guitarcenter.scheduler.service.SearchService;
import com.guitarcenter.scheduler.service.SiteService;

@Service("locationManageService")
public class LocationManageServiceImpl implements LocationManagerService {
	
	private static final Logger LOG = LoggerFactory.getLogger(LocationManageServiceImpl.class);

	@Resource
	private LocationDAO locationDao;
	
	@Autowired
	private SiteService siteService;
	
	@Autowired
	private SearchService searchService;
	
	@Autowired
	private PersonManagerService personManagerService;

	@Transactional
	@Override
	@Deprecated
	public Location findByExternalId(String externalId) {
		Location example = new Location();
		example.setExternalId(externalId);
		List<Location> list = locationDao.search(example, DAOHelper.FETCH_SITE|DAOHelper.FETCH_LOCATION_PROFILE);
		if (list.isEmpty()) {
			return null;
		}
		return list.get(0);
	}
	
	/**
     * Return a collection of Location records for the site id provided that
     * have a matching external id value.
     * 
     * Note: this is returning a collection simply because there is not a
     * database constraint on the external identifiers; it is possible that
     * multiple locations *could* be updated from a single external record in
     * the future.
     * 
     * @param siteId identifier for the site that results must be restricted to
     * @param externalId String containing the external id to match
     * @return List of Location instances that match the external id in the site
     *         provided
     */
	@Transactional
    @Override
	public List<Location> findByExternalId(long siteId, String externalId) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Searching for location with external id {} and site id {}",
                      externalId, siteId);
        }
        Criterion<Location, Location> criterion =
            LocationCriterion.findByExternalId(siteId, externalId);
        List<Location> locations =
            locationDao.search(criterion, DAOHelper.FETCH_SITE | DAOHelper.FETCH_LOCATION_PROFILE);
        if (LOG.isDebugEnabled()) {
            LOG.debug("Returning location list {}", locations);
        }
        return locations;
    }
    
	@Transactional
	@Override
	public List<SearchDTO> findLocationsByCriteria(String criteria) {
		if(LOG.isDebugEnabled()) {
			LOG.debug("Begin to query Location by {} in LocationManageService.findLocationsByCriteria", criteria);
		}
		boolean isId = true;
		List<SearchDTO> dtos = new LinkedList<SearchDTO>();
		try {
			if(LOG.isDebugEnabled()) {
				LOG.debug("The criteria of querying Location is LocationId: {}", criteria);
			}
			Long.parseLong(criteria);
		} catch (NumberFormatException e) {
			if(LOG.isDebugEnabled()) {
				LOG.debug("The criteria of querying Location is LocationName: {}", criteria);
			}
			isId = false;
		}
		List<Location> list = new ArrayList<Location>();
		if(isId) {
			list.add(locationDao.get(Long.parseLong(criteria), DAOHelper.FETCH_LOCATION_PROFILE));
		} else {
			Criterion<Location, Location> criterion = LocationCriterion.findByLocationName(criteria);
			list = locationDao.search(criterion, DAOHelper.FETCH_LOCATION_PROFILE);
		}
		//Iterate the list to construct the DTO list
		if(null != list && !list.isEmpty()) {
			for(Location l : list) {
				SearchDTO dto = new SearchDTO();
				dto.setAddress1(l.getAddress1());
				dto.setAddress2(l.getAddress2());
				dto.setCity(l.getCity());
				dto.setCountry(l.getCountry());
				if(null != l.getLocationProfile()) {
					LocationProfile lp = l.getLocationProfile();
					Enabled enabled = lp.getEnabled();
					if(Enabled.Y.equals(enabled)) {
						dto.setEnabled("Yes");
					} else {
						dto.setEnabled("No");
					}
				} else {
					dto.setEnabled(null);
				}
				dto.setExternalId(null == l.getExternalId() ? "" : l.getExternalId());
				dto.setFax(l.getFax());
				dto.setLocationName(l.getLocationName());
				dto.setPhone(l.getPhone());
				dto.setRecordId(l.getLocationId());
				dto.setState(l.getState());
				dto.setZip(l.getZip());
				dtos.add(dto);
			}
		}
		if(LOG.isDebugEnabled()) {
			LOG.debug("Got the Location List {} from LocationManageService.findLocationsByCriteria", dtos);
		}
		return dtos;
	}

	@Transactional
	@Override
	public List<Location> locationsInSite(Site site) {
	    /* XXX: Fixed by MEmes: prior method was returning all locations, not
         * just those associated with the supplied site.
         */
        if (LOG.isDebugEnabled()) {
            LOG.debug("Searching for locations associated with site {}", site);
        }
        Criterion<Location, Location> criterion =
            LocationCriterion.findBySiteId(site.getSiteId());
        List<Location> locations = locationDao.search(criterion, DAOHelper.FETCH_LOCATION_PROFILE);
        if (LOG.isDebugEnabled()) {
            LOG.debug("Returning location list {}", locations);
        }
        return locations;
	}

	@Override
	public Location findById(long locationId) {
		return locationDao.get(locationId, DAOHelper.FETCH_LOCATION_PROFILE);
	}
    
    /**
     * Create or updates an Scheduler Location record with values taken from the
     * supplied LocationDTO record. Any exceptions are passed up to caller for
     * decision and handling.
     * 
     * The method will retrieve an existing location record from persistent
     * storage that matches the external id of the update record.
     * 
     * @param update a LocationDTO instance containing values to update
     * @throws IOException 
     * @throws SolrServerException 
     */
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    @Override
    public void updateFromExternal(LocationDTO update)
        throws SolrServerException, IOException
    {
        if (LOG.isDebugEnabled()) {
            LOG.debug("about to attempt an update for location {}", update);
        }

        if (update == null) {
            LOG.warn("a null location instance was passed to updateFromExternal");
            return;
        }
        
        if (StringUtils.isBlank(update.getSite())) {
            if (LOG.isInfoEnabled()) {
                LOG.info("updated location {} does not have a site string");
            }
            return;
        }
        /* Find the site that matches the external id provided by update record.
         * 
         * Note: sites are a key element of data separation, so throw an
         * exception if an exact match of 1 site is not received.
         */
        List<Site> sites = siteService.findSitesByExternalId(update.getSite());
        if (sites.size() > 1) {
            LOG.warn("updated location site external id {} matches too " +
                     "many sites; ignoring record", update.getSite());
            throw new IllegalStateException("Found too many sites for " +
                                            update.getSite());
        }
        if (sites.isEmpty()) {
            if (LOG.isInfoEnabled()) {
                LOG.info("updated location site external id {} does not match " +
                         "an existing site; ignoring record", update.getSite());
            }
            throw new IllegalStateException("Did not find existing site for " +
                                            update.getSite());
        }
        Site updateSite = sites.get(0);
        
        /* Now try to find an existing Location record to update
         */
        if (StringUtils.isBlank(update.getExternalId())) {
            LOG.warn("updated location {} does not have an external id; " +
                     "ignoring record", update);
            return;
        }
        Location location = null;
        List<Location> existing =
            findByExternalId(updateSite.getSiteId(), update.getExternalId());
        switch(existing.size()) {
            case 1:
                /* Update an existing Location from the DTO
                 */
                location = existing.get(0);
                if (LOG.isDebugEnabled()) {
                    LOG.debug("found a record; updating {}", location);
                }
                updateLocationFromDTO(location, update);
                locationDao.update(location,
                                   personManagerService.getSystemUpdatePerson());
                break;
                
            case 0:
                /* Need to create a new Location record and populate with DTO
                 */
                location = createLocationFromDTO(update, updateSite);
                locationDao.save(location,
                                 personManagerService.getSystemUpdatePerson());
                break;
                
            default:
                /* Found too many location records - DO NOT UPDATE!
                 */
                LOG.warn("updated location external id {} matched too many " +
                         "existing records; ignoring update request",
                         update.getExternalId());
                return;
        }
        /* Update was successful so make sure search engine has the latest
         * data on record.
         */
        if (location.getLocationId() != null) {
            searchService.updateRecord(location);
        }
        if (LOG.isDebugEnabled()) {
            LOG.debug("update completed for location {}", update);
        }
    }

    /**
     * Creates a new Location record ready for persistence.
     * 
     * @param update LocationDTO containing the data to update
     * @param updateSite a Site instance to populate
     * @return Location instance ready for persistence
     */
    private Location createLocationFromDTO(LocationDTO update, Site updateSite) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("about to create a new Location instance from {} and {}",
                      update, updateSite);
        }
        Location location = new Location();
        location.setSite(updateSite);
        location.setExternalId(update.getExternalId());
        updateLocationFromDTO(location, update);
        if (LOG.isDebugEnabled()) {
            LOG.debug("returning new location {}", location);
        }
        return location;
    }

    /**
     * Updates a Location with the values found in update.
     * 
     * @param location Location instance that will be persisted
     * @param update LocationDTO containing the updated values
     */
    private void updateLocationFromDTO(Location location, LocationDTO update) {
        if (LOG.isDebugEnabled()) {
            LOG.debug("about to update {} from {}", location, update);
        }
        if (StringUtils.isNotBlank(update.getName())) {
            location.setLocationName(update.getName());
        }
        if (StringUtils.isNotBlank(update.getAddress1())) {
            location.setAddress1(update.getAddress1());
        }
        if (StringUtils.isNotBlank(update.getAddress2())) {
            location.setAddress2(update.getAddress2());
        }
        if (StringUtils.isNotBlank(update.getCity())) {
            location.setCity(update.getCity());
        }
        if (StringUtils.isNotBlank(update.getState())) {
            location.setState(update.getState());
        }
        if (StringUtils.isNotBlank(update.getZip())) {
            location.setZip(update.getZip());
        }
        if (StringUtils.isNotBlank(update.getCountry())) {
            location.setCountry(update.getCountry());
        }
        if (StringUtils.isNotBlank(update.getPhone())) {
            location.setPhone(update.getPhone());
        }
        if (StringUtils.isNotBlank(update.getFax())) {
            location.setFax(update.getFax());
        }
        if (StringUtils.isNotBlank(update.getExternalSource())) {
            location.setExternalSource(update.getExternalSource());
        }
        if (LOG.isDebugEnabled()) {
            LOG.debug("updated location {}", location);
        }
        
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    @Override
    public LocationDistRegDTO getLocationDistrictReg(String externalId) {

		LocationDistRegDTO loc = null;
		loc = locationDao.getLocationDistrictReg(externalId);
		return loc;
	}

	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    @Override
    public LocationDistRegDTO getLocationDistrictReg(long locationId) {

		LocationDistRegDTO loc = null;
		loc = locationDao.getLocationDistrictReg(locationId);
		return loc;
	}
}
