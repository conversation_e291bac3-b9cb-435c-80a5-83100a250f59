package com.guitarcenter.scheduler.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.InstructorCriterion;
import com.guitarcenter.scheduler.dto.InstructorActivitiesDTO;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.service.InstructorActivitiesService;
import com.guitarcenter.scheduler.service.JobNotificationEmailServiceImpl;
import com.guitarcenter.scheduler.service.MailSenderService;

// Created for GSSP-240 - Instructor Activities report generation

@Service("instructorActivitiesService")
public class InstructorActivitiesServiceImpl implements InstructorActivitiesService {
	
	private static final Logger		LOGGER	= LoggerFactory.getLogger(InstructorActivitiesServiceImpl.class);

    
    @Autowired
    @Qualifier("instructorDAO")    
    private InstructorDAO mInstructorDAO;
	
  	String EMAIL_TEMPLATE_INSTRUCTOR_ACTIVITIES= "instructorActivitiesTemplate.ftl";

    @Autowired
	@Qualifier("mailSenderService")
  	private MailSenderService mMailSenderService;
    //Miscellaneous task
    @Autowired
    private JobNotificationEmailServiceImpl jobNotificationEmailServiceImpl;

	public InstructorDAO getInstructorDAO() {
		return mInstructorDAO;
	}
	
	public void setInstructorDAO(InstructorDAO instrDAO) {
		this.mInstructorDAO = instrDAO;
	}

	public MailSenderService getMailSenderService() {
		return mMailSenderService;
	}
	
	public void setMailSenderService(MailSenderService mailService) {
		this.mMailSenderService = mailService;
	}
	
      @Override
      public void generateInstructorActivitiesReport() throws Exception {

          HSSFWorkbook workbook = new HSSFWorkbook();
          Map<String, Object> model = new HashMap<String, Object>();
          model.put("instructorActivities",getInstructorActivities());
          
          createInstructorActivitiesReport(model,workbook);
      	  Map<String,Object> dataMap =  renderDataForIntructorActivitiesEmail();
          mMailSenderService.sendMailWithAttachment(dataMap, EMAIL_TEMPLATE_INSTRUCTOR_ACTIVITIES, workbook, 
			AppConstants.FILE_PATH, AppConstants.INSTR_ACTIVITIES_FILE_NAME);
          
      }
      
      
      private  void createInstructorActivitiesReport(
				Map<String, Object> model, HSSFWorkbook workbook) {
			HSSFRow row;		
			HSSFSheet sheet = workbook
					.createSheet("Instructor Activities");
			HSSFCellStyle style = workbook.createCellStyle();
			
			HSSFFont font = workbook.createFont();
			font.setFontHeightInPoints((short) 10);
			font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
			style.setFont(font);
			
			sheet.setColumnWidth(0, 5500);
			sheet.setColumnWidth(1, 6000);
			sheet.setColumnWidth(2, 4000);
			int rownum = 0;
			row = sheet.createRow(rownum++);
			row.createCell(0).setCellValue("Store");
			row.getCell(0).setCellStyle(style);
			row.createCell(1).setCellValue("Instructor Name");
			row.getCell(1).setCellStyle(style);
			row.createCell(2).setCellValue("Activity Type");			
			row.getCell(2).setCellStyle(style);
			
			for ( InstructorActivitiesDTO instrActDto : getInstructorActivities()) {
					row = sheet.createRow(rownum++);
					
					String store = instrActDto.getLocationId() + " "+ instrActDto.getLocationName();
					row.createCell(0).setCellValue(store);

					String instrFullName = instrActDto.getFirstName()+" "+instrActDto.getLastName(); 
					row.createCell(1).setCellValue(instrFullName);
					
					row.createCell(2).setCellValue(instrActDto.getActivityName());
				}
			
			}			

      private Map<String, Object> renderDataForIntructorActivitiesEmail() {
  		Map<String, Object> dataMap = new HashMap<String, Object>();
  		dataMap.put(AppConstants.SUBJECT, AppConstants.INSTRUCTOR_ACTIVITIES_EMAIL_SUBJECT);	
		
  		List<InstructorActivitiesDTO> recipientList = getRecipientEmailIds(); 
		if(recipientList !=null && recipientList.size() >0 )
		{
			String[] recipientArray = new String[recipientList.size()];
			int cnt=0;
			for ( InstructorActivitiesDTO instrActDto : recipientList) {
				recipientArray[cnt] = instrActDto.getRecipientId();
				cnt++;
			}
			dataMap.put(AppConstants.EMAIL_TYPE_TO, recipientArray);
			
		}
		else{
			 LOGGER.info(" getRecipientEmailIds(), recipientList null ");
		}
  		
		

 		String environment=jobNotificationEmailServiceImpl.getEnvironment();
				//Miscellaneous task
 				if(environment.equalsIgnoreCase("Production"))
 				{	
 					dataMap.put(AppConstants.EMAIL_TYPE_CC, AppConstants.JOB_NOTIFICATION_EMAIL_SEND_LIST);
 				}
 				else
 				{
 					dataMap.put(AppConstants.EMAIL_TYPE_CC, AppConstants.JOB_NOTIFICATION_EMAIL_SEND_LIST_QA);			
 				}
  		return dataMap;
  	}
      
    
      private List<InstructorActivitiesDTO> getInstructorActivities() {
          Criterion<Instructor, InstructorActivitiesDTO> criterion = InstructorCriterion.findInstructorActivities();
          List<InstructorActivitiesDTO> instrList = mInstructorDAO.search(criterion);
          if(instrList != null){
    	  LOGGER.info(" getInstructorActivities(), instrList size= "+instrList.size());
          }else{
        	  LOGGER.info(" getInstructorActivities(), instrList size is null");
          }
          return instrList;
      }
      
    
    
      private List<InstructorActivitiesDTO> getRecipientEmailIds() {
          Criterion<Instructor, InstructorActivitiesDTO> criterion = InstructorCriterion.getRecipientEmailIds();
          List<InstructorActivitiesDTO> dtoList = mInstructorDAO.search(criterion);
          if(dtoList != null){
    	  LOGGER.info(" getRecipientEmailIds(), dtoList size= "+dtoList.size());
          }else{
        	  LOGGER.info(" getRecipientEmailIds(), dtoList size is null");
          }
		  
          return dtoList;
      }
      
}
