package com.guitarcenter.scheduler.service.impl;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.dao.InstructorDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.InstructorCriterion;
import com.guitarcenter.scheduler.dto.InstructorActivitiesAndAvailabilityDTO;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.service.InstructorActivityAndAvailabitityService;
import com.guitarcenter.scheduler.service.JobNotificationEmailServiceImpl;
import com.guitarcenter.scheduler.service.MailSenderService;
//GSSP-298
@Service("instructorActivityAndAvailabitityService")
public class InstructorActivityAndAvailabilityServiceImpl implements InstructorActivityAndAvailabitityService{
	private static final Logger		LOGGER	= LoggerFactory.getLogger(InstructorActivityAndAvailabilityServiceImpl.class);

	@Autowired
    @Qualifier("instructorDAO")    
    private InstructorDAO mInstructorDAO;
	
  	String EMAIL_TEMPLATE_INSTRUCTOR_ACTIVITIES= "instructorActivitiesAndAvailabilityTemplate.ftl";

    @Autowired
	@Qualifier("mailSenderService")
  	private MailSenderService mMailSenderService;
    @Autowired
    private JobNotificationEmailServiceImpl jobNotificationEmailServiceImpl;

	public InstructorDAO getInstructorDAO() {
		return mInstructorDAO;
	}
	
	public void setInstructorDAO(InstructorDAO instrDAO) {
		this.mInstructorDAO = instrDAO;
	}

	public MailSenderService getMailSenderService() {
		return mMailSenderService;
	}
	
	public void setMailSenderService(MailSenderService mailService) {
		this.mMailSenderService = mailService;
	}
	
      @Override
      public void generateInstructorAcitivityAndAvailabilityReport() throws Exception {

    	  HSSFWorkbook workbook = new HSSFWorkbook();
          Map<String, Object> model = new HashMap<String, Object>();
          model.put("instructorActivities",getInstructorActivitiesAvailability());
          
          createInstructorActivitiesAvailabiltyReport(model,workbook);
      	  Map<String,Object> dataMap =  renderDataForIntructorActivitiesEmail();
          mMailSenderService.sendMailWithAttachment(dataMap, EMAIL_TEMPLATE_INSTRUCTOR_ACTIVITIES, workbook, 
			AppConstants.FILE_PATH, AppConstants.INSTR_ACTIVITIES_AVAILABLITY_FILE_NAME);
          
      }
      
      
      private  void createInstructorActivitiesAvailabiltyReport(
				Map<String, Object> model, HSSFWorkbook workbook) {
			HSSFRow row;		
			HSSFSheet sheet = workbook
					.createSheet("Instructors Availability and Activities monthly report");
			HSSFCellStyle style = workbook.createCellStyle();
			HSSFFont font = workbook.createFont();
			font.setFontHeightInPoints((short) 10);
			font.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);
			style.setFont(font);
			style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
			style.setBorderTop(HSSFCellStyle.BORDER_THIN);
			style.setBorderRight(HSSFCellStyle.BORDER_THIN);
			style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
			style.setAlignment(CellStyle.ALIGN_CENTER);
			
			CellStyle cellStyle = workbook.createCellStyle();// created separate style for headers
			cellStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.index);
			cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
			HSSFFont headerFont = workbook.createFont();
			headerFont.setFontHeightInPoints((short) 10);
			headerFont.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
			cellStyle.setFont(headerFont);
			cellStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
			cellStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
			cellStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
			cellStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
			CellStyle cellAllignment = workbook.createCellStyle();
			cellAllignment.setBorderBottom(HSSFCellStyle.BORDER_THIN);
			cellAllignment.setBorderTop(HSSFCellStyle.BORDER_THIN);
			cellAllignment.setBorderRight(HSSFCellStyle.BORDER_THIN);
			cellAllignment.setBorderLeft(HSSFCellStyle.BORDER_THIN);
			cellAllignment.setAlignment(CellStyle.ALIGN_LEFT);
			
			int rownum = 0;
			Set<String> activity = new HashSet<String>(); 
			for(InstructorActivitiesAndAvailabilityDTO activityName : getInstructorActivitiesAvailability())
			{
				String str[] = activityName.getActivityName().split(",");
				for (String s : str)
				{
				activity.add(s);
				}
			}
			row = sheet.createRow(rownum++);
			row.createCell(0).setCellValue("STORE");
			row.getCell(0).setCellStyle(style);
			row.getCell(0).setCellStyle(cellStyle);
			row.createCell(1).setCellValue("INSTRUCTOR NAME");
			row.getCell(1).setCellStyle(style);
			row.getCell(1).setCellStyle(cellStyle);
			int cellNum = 1;
			Iterator<String> itr=activity.iterator(); 
			while(itr.hasNext())
			{
				sheet.setColumnWidth(cellNum +1, 4500);
				row.createCell(cellNum +1).setCellValue(itr.next());
				row.getCell(cellNum +1).setCellStyle(style);
				row.getCell(cellNum +1).setCellStyle(cellStyle);
				cellNum++;
			}
			sheet.setColumnWidth(0, 5500);
			sheet.setColumnWidth(1, 5500);
			sheet.setColumnWidth(cellNum, 5500);
			sheet.setColumnWidth(cellNum +1, 5500);
			sheet.setColumnWidth(cellNum +2, 5500);
			sheet.setColumnWidth(cellNum +3, 5500);
			sheet.setColumnWidth(cellNum +4, 5500);
			sheet.setColumnWidth(cellNum +5, 5500);
			sheet.setColumnWidth(cellNum +6, 5500);
			sheet.setColumnWidth(cellNum +7, 5500);
			sheet.setColumnWidth(cellNum +8, 5500);
			sheet.setColumnWidth(cellNum +9, 5500);
			sheet.setColumnWidth(cellNum +10, 5500);
			sheet.setColumnWidth(cellNum +11, 5500);
			sheet.setColumnWidth(cellNum +12, 5500);
			sheet.setColumnWidth(cellNum +13, 5500);
			row.createCell(cellNum).setCellValue("MONDAY START TIME");			
			row.getCell(cellNum).setCellStyle(style);
			row.getCell(cellNum).setCellStyle(cellStyle);
			row.createCell(cellNum +1).setCellValue("MONDAY END TIME");			
			row.getCell(cellNum +1).setCellStyle(style);
			row.getCell(cellNum +1).setCellStyle(cellStyle);
			row.createCell(cellNum +2).setCellValue("TUESDAY START TIME");			
			row.getCell(cellNum +2).setCellStyle(style);
			row.getCell(cellNum +2).setCellStyle(cellStyle);
			row.createCell(cellNum +3).setCellValue("TUESDAY END TIME");			
			row.getCell(cellNum +3).setCellStyle(style);
			row.getCell(cellNum +3).setCellStyle(cellStyle);
			row.createCell(cellNum +4).setCellValue("WEDNESDAY START TIME");			
			row.getCell(cellNum +4).setCellStyle(style);
			row.getCell(cellNum +4).setCellStyle(cellStyle);
			row.createCell(cellNum +5).setCellValue("WEDNESDAY END TIME");			
			row.getCell(cellNum +5).setCellStyle(style);
			row.getCell(cellNum +5).setCellStyle(cellStyle);
			row.createCell(cellNum +6).setCellValue("THURSDAY START TIME");			
			row.getCell(cellNum +6).setCellStyle(style);
			row.getCell(cellNum +6).setCellStyle(cellStyle);
			row.createCell(cellNum +7).setCellValue("THURSDAY END TIME");			
			row.getCell(cellNum +7).setCellStyle(style);
			row.getCell(cellNum +7).setCellStyle(cellStyle);
			row.createCell(cellNum +8).setCellValue("FRIDAY START TIME");			
			row.getCell(cellNum +8).setCellStyle(style);
			row.getCell(cellNum +8).setCellStyle(cellStyle);
			row.createCell(cellNum +9).setCellValue("FRIDAY END TIME");			
			row.getCell(cellNum +9).setCellStyle(style);
			row.getCell(cellNum +9).setCellStyle(cellStyle);
			row.createCell(cellNum +10).setCellValue("SATURDAY START TIME");			
			row.getCell(cellNum +10).setCellStyle(style);
			row.getCell(cellNum +10).setCellStyle(cellStyle);
			row.createCell(cellNum +11).setCellValue("SATURDAY END TIME");			
			row.getCell(cellNum +11).setCellStyle(style);
			row.getCell(cellNum +11).setCellStyle(cellStyle);
			row.createCell(cellNum +12).setCellValue("SUNDAY START TIME");			
			row.getCell(cellNum +12).setCellStyle(style);
			row.getCell(cellNum +12).setCellStyle(cellStyle);
			row.createCell(cellNum +13).setCellValue("SUNDAY END TIME");			
			row.getCell(cellNum +13).setCellStyle(style);
			row.getCell(cellNum +13).setCellStyle(cellStyle);
			for ( InstructorActivitiesAndAvailabilityDTO instrActDto : getInstructorActivitiesAvailability()) {
					row = sheet.createRow(rownum++);
					String store = instrActDto.getStore();
					row.createCell(0).setCellValue(store);
					row.getCell(0).setCellStyle(style);
					row.getCell(0).setCellStyle(cellAllignment);
					String instrFullName = instrActDto.getInstructorName();
					row.createCell(1).setCellValue(instrFullName);
					row.getCell(1).setCellStyle(style);
					row.getCell(1).setCellStyle(cellAllignment);
					String ActivityArr[] = instrActDto.getActivityName().split(",");
					for(int j=2;j<=cellNum; j++)
					{
						row.createCell(j);
						row.getCell(j).setCellStyle(style);
						for(String ActivityStr : ActivityArr){
						if(sheet.getRow(0).getCell(j).toString().equalsIgnoreCase(ActivityStr))
						{
							row.getCell(j).setCellValue("X");
						}
						}
					}
					
					row.createCell(cellNum).setCellValue((instrActDto.getMondayStartTime() == null) ? "Not Available" : instrActDto.getMondayStartTime());
					row.getCell(cellNum).setCellStyle(style);
					row.getCell(cellNum).setCellStyle(cellAllignment);
					row.createCell(cellNum+1).setCellValue((instrActDto.getMondayEndTime() == null) ? "Not Available" : instrActDto.getMondayEndTime());
					row.getCell(cellNum+1).setCellStyle(style);
					row.getCell(cellNum+1).setCellStyle(cellAllignment);
					row.createCell(cellNum+2).setCellValue((instrActDto.getTuesdayStartTime() == null) ? "Not Available" : instrActDto.getTuesdayStartTime());
					row.getCell(cellNum+2).setCellStyle(style);
					row.getCell(cellNum+2).setCellStyle(cellAllignment);
					row.createCell(cellNum+3).setCellValue((instrActDto.getTuesdayendTime() == null) ? "Not Available" : instrActDto.getTuesdayendTime());
					row.getCell(cellNum+3).setCellStyle(style);
					row.getCell(cellNum+3).setCellStyle(cellAllignment);
					row.createCell(cellNum+4).setCellValue((instrActDto.getWednesdayStartTime() == null) ? "Not Available" : instrActDto.getWednesdayStartTime());
					row.getCell(cellNum+4).setCellStyle(style);
					row.getCell(cellNum+4).setCellStyle(cellAllignment);
					row.createCell(cellNum+5).setCellValue((instrActDto.getWednesdayEndTime() == null) ? "Not Available" : instrActDto.getWednesdayEndTime());
					row.getCell(cellNum+5).setCellStyle(style);
					row.getCell(cellNum+5).setCellStyle(cellAllignment);
					row.createCell(cellNum+6).setCellValue((instrActDto.getThursdayStartTime() == null) ? "Not Available" : instrActDto.getThursdayStartTime());
					row.getCell(cellNum+6).setCellStyle(style);
					row.getCell(cellNum+6).setCellStyle(cellAllignment);
					row.createCell(cellNum+7).setCellValue((instrActDto.getThursdayEndTime() == null) ? "Not Available" : instrActDto.getThursdayEndTime());
					row.getCell(cellNum+7).setCellStyle(style);
					row.getCell(cellNum+7).setCellStyle(cellAllignment);
					row.createCell(cellNum+8).setCellValue((instrActDto.getFridayStartTime() == null) ? "Not Available" : instrActDto.getFridayStartTime());
					row.getCell(cellNum+8).setCellStyle(style);
					row.getCell(cellNum+8).setCellStyle(cellAllignment);
					row.createCell(cellNum+9).setCellValue((instrActDto.getFridayEndTime() == null) ? "Not Available" : instrActDto.getFridayEndTime());
					row.getCell(cellNum+9).setCellStyle(style);
					row.getCell(cellNum+9).setCellStyle(cellAllignment);
					row.createCell(cellNum+10).setCellValue((instrActDto.getSaturdayStarTime() == null) ? "Not Available" : instrActDto.getSaturdayStarTime());
					row.getCell(cellNum+10).setCellStyle(style);
					row.getCell(cellNum+10).setCellStyle(cellAllignment);
					row.createCell(cellNum+11).setCellValue((instrActDto.getSaturdayEndTime() == null) ? "Not Available" : instrActDto.getSaturdayEndTime());
					row.getCell(cellNum+11).setCellStyle(style);
					row.getCell(cellNum+11).setCellStyle(cellAllignment);
					row.createCell(cellNum+12).setCellValue((instrActDto.getSundayStartTime() == null) ? "Not Available" : instrActDto.getSundayStartTime());
					row.getCell(cellNum+12).setCellStyle(style);
					row.getCell(cellNum+12).setCellStyle(cellAllignment);
					row.createCell(cellNum+13).setCellValue((instrActDto.getSundayEndTime() == null) ? "Not Available" : instrActDto.getSundayEndTime());
					row.getCell(cellNum+13).setCellStyle(style);
					row.getCell(cellNum+13).setCellStyle(cellAllignment);
				}
			}			

      private Map<String, Object> renderDataForIntructorActivitiesEmail() {
  		Map<String, Object> dataMap = new HashMap<String, Object>();
  		dataMap.put(AppConstants.SUBJECT, AppConstants.INSTRUCTOR_ACTIVITIES_AVAILABILITY_EMAIL_SUBJECT);	
		
  		List<InstructorActivitiesAndAvailabilityDTO> recipientList = getRecipientEmailIds(); 
		if(recipientList !=null && recipientList.size() >0 )
		{
			String[] recipientArray = new String[recipientList.size()];
			int cnt=0;
			for ( InstructorActivitiesAndAvailabilityDTO instrActDto : recipientList) {
				recipientArray[cnt] = instrActDto.getRecipientId();
				cnt++;
			}
			dataMap.put(AppConstants.EMAIL_TYPE_TO, recipientArray);
			
		}
		else{
			 LOGGER.info(" getRecipientEmailIds(), recipientList null ");
		}
  		
		

 		String environment=jobNotificationEmailServiceImpl.getEnvironment();
				//Miscellaneous task
 				if(environment.equalsIgnoreCase("Production"))
 				{	
 					dataMap.put(AppConstants.EMAIL_TYPE_CC, AppConstants.JOB_NOTIFICATION_EMAIL_SEND_LIST);
 				}
 				else
 				{
 					dataMap.put(AppConstants.EMAIL_TYPE_CC, AppConstants.JOB_NOTIFICATION_EMAIL_SEND_LIST_QA);			
 				}
  		return dataMap;
  	}
      
    
      private List<InstructorActivitiesAndAvailabilityDTO> getInstructorActivitiesAvailability() {
          Criterion<Instructor, InstructorActivitiesAndAvailabilityDTO> criterion = InstructorCriterion.findInstructorActivitiesAndAvailability();
          List<InstructorActivitiesAndAvailabilityDTO> instrList = mInstructorDAO.search(criterion);
          if(instrList != null){
    	  LOGGER.info(" getInstructorActivities(), instrList size= "+instrList.size());
          }else{
        	  LOGGER.info(" getInstructorActivities(), instrList size is null");
          }
          return instrList;
      }
      
    
    
      private List<InstructorActivitiesAndAvailabilityDTO> getRecipientEmailIds() {
          Criterion<Instructor, InstructorActivitiesAndAvailabilityDTO> criterion = InstructorCriterion.getInstructorActivitiesAndAvailabilityRecipientEmailIds();
          List<InstructorActivitiesAndAvailabilityDTO> dtoList = mInstructorDAO.search(criterion);
          if(dtoList != null){
    	  LOGGER.info(" getRecipientEmailIds(), dtoList size= "+dtoList.size());
          }else{
        	  LOGGER.info(" getRecipientEmailIds(), dtoList size is null");
          }
		  
          return dtoList;
      }
      
}