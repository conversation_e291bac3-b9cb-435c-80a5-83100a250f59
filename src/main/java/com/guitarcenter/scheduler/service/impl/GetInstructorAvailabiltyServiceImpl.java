package com.guitarcenter.scheduler.service.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.service.GetInstructorAvailabiltyService;
import com.guitarcenter.scheduler.service.InstructorService;
import com.guitarcenter.scheduler.service.JobNotificationEmailServiceImpl;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceDTO;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceResponseDTO;
import com.guitarcenter.scheduler.webservice.dto.LocationProfileInfoDTO;


@Service("getInstructorAvailabiltyService")
public class GetInstructorAvailabiltyServiceImpl implements GetInstructorAvailabiltyService{

	@Autowired
	private InstructorService instructorService;
	
	@Autowired
    private JobNotificationEmailServiceImpl jobNotificationEmailServiceImpl;
	
	@Value("${adp.customerRegisterElasticIndex}")
    private String customerRegisterElasticIndex;
	
	@Value("${adp.elasticIndex}")
    private String elasticIndex;
	
	@Value("${adp.accessKeyId}")
	private String accessKeyId;

	@Value("${adp.secretKey}")
	private String secretKey;
	
	@Value("${adp.serviceName}")
    private String serviceName;
	
	@Value("${adp.region}")
	private String region;

	@Value("${adp.aesEndpoint}")
	private String aesEndpoint;
 
 
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public List<InstructorAVLServiceResponseDTO> getInstructorAvailabilitySlots(){ 
		
		List<InstructorAVLServiceResponseDTO> InstructorAVLServiceList = new ArrayList<InstructorAVLServiceResponseDTO>();
		Calendar c = Calendar.getInstance();
		c.add(Calendar.MINUTE, -15);
		Date currentDatePlusOne = c.getTime();
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");  
        String strDate1 = dateFormat.format(currentDatePlusOne );
		List<Instructor> insList = instructorService.getInstructorsAffectRecent(strDate1);
		DateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");

		Map<String, InstructorAVLServiceResponseDTO>  locDstRgn = instructorService.getLocationDistRegion();
		
		  Calendar cal = Calendar.getInstance();
		  cal.add(Calendar.DATE, 2);
		  String dateStr = dateFormat2.format(cal.getTime());
		  //System.out.println(dateStr);
		  InstructorAVLServiceDTO instructorServiceDTO = new InstructorAVLServiceDTO();
		for(Instructor ins:insList){
			if(null != ins && ins.getExternalId() != null) {
			InstructorAVLServiceResponseDTO insAvl = new InstructorAVLServiceResponseDTO();
			//---Set Instructor active status - Start
			if("A".equals(ins.getStatus()) && "Y".equals(ins.getEnabled().toString())){
				instructorServiceDTO.setInstructorStatus("Active");
			}else{
				instructorServiceDTO.setInstructorStatus("InActive");
			}
			//---Set Instructor active status - End
			
			//String[] filteredStores = new String[]{"101","770","771","772","773","774","775","776","777","781","782","783","784","785","789","791"};

	        // Convert String Array to List
	     //   List<String> filteredStoreslist = Arrays.asList(filteredStores);
			
			instructorServiceDTO.setInstructorId(ins.getInstructorId()+"");
			instructorServiceDTO.setStartDate(dateStr);
			insAvl= instructorService.getInstructorAvailability(instructorServiceDTO);
			
			//TODO::
			LocationProfileInfoDTO locationProfileDetails = new LocationProfileInfoDTO();

			  locationProfileDetails.setInstructorId(Long.valueOf(instructorServiceDTO.getInstructorId())); 
			  
			  List<Instructor> instructors1 = instructorService.queryInstructorsById(locationProfileDetails.getInstructorId());
			  
			  for(Instructor inst : instructors1) {
	                 
                  locationProfileDetails.setProfileID(inst.getLocation().getLocationProfile().getProfileId());
 
                  locationProfileDetails.setLocationID(inst.getLocation().getLocationId());
			  }
			
			//LocationProfileInfoDTO locationProfileDetails 
			
			/*
			 * try { //System.out.println(instructorService.getInstructorFreeSLots(
			 * locationProfileDetails,instructorServiceDTO)); } catch (Exception e) { //
			 * TODO Auto-generated catch block e.printStackTrace(); }
			 */
			
			//END:
			//if(null != insAvl && insAvl.getStoreNumber() != null && filteredStoreslist.contains(insAvl.getStoreNumber())) {
			  
			  InstructorAVLServiceResponseDTO  lst = locDstRgn.get(insAvl.getStoreNumber());
			  if(lst != null){
					 insAvl.setStoreDistrict(lst.getStoreDistrict());
					 insAvl.setStoreRegion(lst.getStoreRegion());
					 insAvl.setStoreLocation(lst.getStoreLocation());
					}
				System.out.println("  ------------------------     "+insAvl);
				
                InstructorAVLServiceList.add(insAvl);
           // }
			}
		}
		
		return InstructorAVLServiceList;
 
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public List<InstructorAVLServiceResponseDTO> getDisabledInstructorsAvailabiltySlots(){ 
		
	
		List<InstructorAVLServiceResponseDTO> InstructorAVLServiceList = new ArrayList<InstructorAVLServiceResponseDTO>();
		Calendar c = Calendar.getInstance();
		c.add(Calendar.MINUTE, -60);
		Date currentDatePlusOne = c.getTime();
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");  
        String strDate1 = dateFormat.format(currentDatePlusOne );
		List<Instructor> insList = instructorService.getDisabledInstructorsAffectRecent(strDate1);
		DateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
		  Calendar cal = Calendar.getInstance();
		  cal.add(Calendar.DATE, 2);
		  String dateStr = dateFormat2.format(cal.getTime());
		  
		  Map<String, InstructorAVLServiceResponseDTO>  locDstRgn = instructorService.getLocationDistRegion();
		  
		   
		  InstructorAVLServiceDTO instructorServiceDTO = new InstructorAVLServiceDTO();
		  //System.out.println("date :"+c.get(Calendar.DATE));
		for(Instructor ins:insList){
			InstructorAVLServiceResponseDTO insAvl = new InstructorAVLServiceResponseDTO();
			//---Set Instructor active status - Start
			if("A".equals(ins.getStatus()) && "Y".equals(ins.getEnabled().toString())){
				instructorServiceDTO.setInstructorStatus("Active");
			}else{
				instructorServiceDTO.setInstructorStatus("InActive");
			}
		//	String[] filteredStores = new String[]{"101","770","771","772","773","774","775","776","777","781","782","783","784","785","789","791"};

	        // Convert String Array to List
	    //    List<String> filteredStoreslist = Arrays.asList(filteredStores);
			
			instructorServiceDTO.setInstructorId(ins.getInstructorId()+"");
			instructorServiceDTO.setStartDate(dateStr);
			insAvl= instructorService.getInstructorAvailability(instructorServiceDTO);
			
			InstructorAVLServiceResponseDTO  lst = locDstRgn.get(insAvl.getStoreNumber());
			if(lst != null){
				insAvl.setStoreDistrict(lst.getStoreDistrict());
				 insAvl.setStoreRegion(lst.getStoreRegion());
				 insAvl.setStoreLocation(lst.getStoreLocation());
			}
		//	if(null != insAvl && insAvl.getStoreNumber() != null && filteredStoreslist.contains(insAvl.getStoreNumber())) {
                InstructorAVLServiceList.add(insAvl);
         //   }
		}
		
		return InstructorAVLServiceList;
 
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public String getEnvironmentName(){
		String environment=jobNotificationEmailServiceImpl.getEnvironment();
		return environment;
	}
	
	 @Override
		public Map<String, String> getPropertiesDataForElasticLoader() {
			Map<String, String> m= new HashMap<String, String>();
			
			/* String serviceName = "es";
			 String region = "us-west-2";
			 String aesEndpoint = "https://search-gc-es-487-prod-rhanh3ea25k5getrxi3c3ltqba.us-west-2.es.amazonaws.com"; */
			m.put("customerRegisterElasticIndex", customerRegisterElasticIndex);
			m.put("elasticIndex", elasticIndex);
			m.put("accessKeyId", accessKeyId);
			m.put("secretKey", secretKey);
			m.put("serviceName", serviceName);
			m.put("region", region);
			m.put("aesEndpoint", aesEndpoint);
			
		//	local
			
		/*
		 * m.put("customerRegisterElasticIndex", "lessons_associate_capture_qa");
		 * m.put("accessKeyId", "********************"); m.put("secretKey",
		 * "qHd0Si2hoC91NdUhenwMfFPXhdZVvl8FMjd3mClK"); m.put("serviceName", "es");
		 * m.put("region", "us-west-2"); m.put("aesEndpoint",
		 * "https://search-gc-es-487-prod-rhanh3ea25k5getrxi3c3ltqba.us-west-2.es.amazonaws.com"
		 * );
		 */
			
			
			return m;
		 
		}
}
