package com.guitarcenter.scheduler.service.impl;

import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.guitarcenter.scheduler.common.util.RestTemplateSingleton;
import com.guitarcenter.scheduler.service.ExternalApiService;

/**
 * Service implementation for making external API calls using Spring 4 RestTemplate
 * Provides methods to POST JSON data to external URLs with custom headers
 */
@Service
public class ExternalApiServiceImpl implements ExternalApiService {
    
    private static final Logger LOG = LoggerFactory.getLogger(ExternalApiServiceImpl.class);
    
    /**
     * Posts JSON data to the specified URL with custom headers using Spring 4 RestTemplate
     * 
     * @param url The target URL to post data to
     * @param apiKey The API key for authentication (will be set as x-api-key header)
     * @param contentType The content type (typically "application/json")
     * @param dtoObject The DTO object to be serialized to JSON and sent in the request body
     * @return ResponseEntity<String> containing the response from the external API
     * @throws Exception if there are issues with the HTTP request or JSON serialization
     */
    @Override
    public ResponseEntity<String> postJsonToUrl(String url, String apiKey, String contentType, Object dtoObject) throws Exception {
        
        LOG.info("Starting POST request to URL: {}", url);
        
        // Validate input parameters
        if (url == null || url.trim().isEmpty()) {
            throw new IllegalArgumentException("URL cannot be null or empty");
        }
        if (apiKey == null || apiKey.trim().isEmpty()) {
            throw new IllegalArgumentException("API key cannot be null or empty");
        }
        if (dtoObject == null) {
            throw new IllegalArgumentException("DTO object cannot be null");
        }
        
        RestTemplate restTemplate = null;
        ResponseEntity<String> response = null;
        
        try {
            // Get RestTemplate instance using the existing singleton utility
            restTemplate = RestTemplateSingleton.getRestTemplate();
            
            // Create HTTP headers
            HttpHeaders headers = new HttpHeaders();
            
            // Set Content-Type header
            if (contentType != null && !contentType.trim().isEmpty()) {
                headers.setContentType(MediaType.parseMediaType(contentType));
            } else {
                headers.setContentType(MediaType.APPLICATION_JSON);
            }
            
            // Set x-api-key header for authentication
            headers.set("x-api-key", apiKey);
            
            // Log headers for debugging (without exposing the full API key)
            LOG.debug("Request headers - Content-Type: {}, x-api-key: {}***", 
                     headers.getContentType(), 
                     apiKey.length() > 4 ? apiKey.substring(0, 4) : "****");
            
            // Convert DTO object to JSON string for logging
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonRequestBody = objectMapper.writeValueAsString(dtoObject);
            LOG.debug("Request body JSON: {}", jsonRequestBody);
          //  String jsonRequestBody = objectMapper.writeValueAsString(dtoObject);
            LOG.error("Request body JSON: {}", jsonRequestBody);
            
            // Create HTTP entity with headers and body
            HttpEntity<Object> requestEntity = new HttpEntity<>(dtoObject, headers);
            
            // Execute POST request
            LOG.info("Executing POST request to: {}", url);
            response = restTemplate.postForEntity(url, requestEntity, String.class);
            
            // Log response details
            HttpStatus statusCode = response.getStatusCode();
            String responseBody = response.getBody();
            
            LOG.info("POST request completed successfully. Status: {}, Response length: {}", 
                    statusCode, responseBody != null ? responseBody.length() : 0);
            LOG.debug("Response body: {}", responseBody);
            LOG.error("Response body: {}", responseBody);
            
            return response;
            
        } catch (HttpClientErrorException e) {
            // Handle 4xx client errors
            LOG.error("Client error during POST request to {}: Status: {}, Response: {}", 
                     url, e.getStatusCode(), e.getResponseBodyAsString());
            throw new Exception("Client error during API call: " + e.getStatusCode() + " - " + e.getResponseBodyAsString(), e);
            
        } catch (HttpServerErrorException e) {
            // Handle 5xx server errors
            LOG.error("Server error during POST request to {}: Status: {}, Response: {}", 
                     url, e.getStatusCode(), e.getResponseBodyAsString());
            throw new Exception("Server error during API call: " + e.getStatusCode() + " - " + e.getResponseBodyAsString(), e);
            
        } catch (KeyStoreException | NoSuchAlgorithmException | KeyManagementException e) {
            // Handle SSL/TLS configuration errors
            LOG.error("SSL/TLS configuration error during POST request to {}: {}", url, e.getMessage());
            throw new Exception("SSL/TLS configuration error: " + e.getMessage(), e);
            
        } catch (Exception e) {
            // Handle any other unexpected errors
            LOG.error("Unexpected error during POST request to {}: {}", url, e.getMessage(), e);
            throw new Exception("Unexpected error during API call: " + e.getMessage(), e);
        }
    }
    
    /**
     * Convenience method specifically for posting lesson scheduled data to the Guitar Center API
     * Uses the predefined URL and handles the specific DTO type
     * 
     * @param apiKey The API key for authentication
     * @param lessonScheduledDTO The lesson scheduled DTO object
     * @return ResponseEntity<String> containing the response from the API
     * @throws Exception if there are issues with the HTTP request
     */
    @Override
    public ResponseEntity<String> postLessonScheduledData(String apiKey, Object lessonScheduledDTO) throws Exception {
        
        final String LESSON_SCHEDULED_URL = "https://gcapi.guitarcenter.com/dev/gcss/v1/lessons/trial/scheduled/brand/gc";
        final String CONTENT_TYPE = "application/json";
        
        LOG.info("Posting lesson scheduled data to Guitar Center API");
        
        return postJsonToUrl(LESSON_SCHEDULED_URL, apiKey, CONTENT_TYPE, lessonScheduledDTO);
    }
    
    /**
     * Method with retry logic for robust API communication
     * Attempts the POST request multiple times with exponential backoff
     * 
     * @param url The target URL
     * @param apiKey The API key
     * @param contentType The content type
     * @param dtoObject The DTO object
     * @param maxRetries Maximum number of retry attempts
     * @return ResponseEntity<String> containing the response
     * @throws Exception if all retry attempts fail
     */
    @Override
    public ResponseEntity<String> postJsonToUrlWithRetry(String url, String apiKey, String contentType, 
                                                        Object dtoObject, int maxRetries) throws Exception {
        
        LOG.info("Starting POST request with retry logic. URL: {}, Max retries: {}", url, maxRetries);
        
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                LOG.debug("Attempt {} of {}", attempt, maxRetries);
                
                ResponseEntity<String> response = postJsonToUrl(url, apiKey, contentType, dtoObject);
                
                LOG.info("POST request successful on attempt {}", attempt);
                return response;
                
            } catch (Exception e) {
                lastException = e;
                LOG.warn("Attempt {} failed: {}", attempt, e.getMessage());
                
                // Don't wait after the last attempt
                if (attempt < maxRetries) {
                    try {
                        // Exponential backoff: wait 2^attempt seconds
                        long waitTime = (long) Math.pow(2, attempt) * 1000;
                        LOG.debug("Waiting {} ms before next attempt", waitTime);
                        Thread.sleep(waitTime);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new Exception("Retry interrupted", ie);
                    }
                }
            }
        }
        
        LOG.error("All {} retry attempts failed for URL: {}", maxRetries, url);
        throw new Exception("All retry attempts failed. Last error: " + lastException.getMessage(), lastException);
    }
}
