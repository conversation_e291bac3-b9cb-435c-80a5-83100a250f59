package com.guitarcenter.scheduler.service.impl;

import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import com.guitarcenter.scheduler.service.AppointmentService;
import com.guitarcenter.scheduler.webservice.dto.UpdateSingleApptDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.guitarcenter.scheduler.dao.util.DAOHelper;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.common.util.DateTimeUtil;
import com.guitarcenter.scheduler.common.util.SystemUtil;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.CustomerDAO;
import com.guitarcenter.scheduler.dao.LocationDAO;
import com.guitarcenter.scheduler.dao.LocationProfileDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.LocationCriterion;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.LocationProfile;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.service.AsyncEmailService;
import com.guitarcenter.scheduler.service.ExternalApiService;
import com.guitarcenter.scheduler.service.MailSenderService;
import com.guitarcenter.scheduler.webservice.dto.LessonScheduledDTO;

import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;

/**
 * Implementation of AsyncEmailService that handles asynchronous email operations.
 * This service is designed to break circular dependencies by providing
 * a separate async layer for email operations.
 */
@Service("asyncEmailService")
public class AsyncEmailServiceImpl implements AsyncEmailService {

    private static final Logger LOG = LoggerFactory.getLogger(AsyncEmailServiceImpl.class);
    private static final String EMAIL_TEMPLATE_FOR_SINGLE_APPT = "apptEmailSingleTemplate.ftl";
    private static final String SERVICE_TYPE = "serviceType";

    @Autowired
    private MailSenderService mailSenderService;

    @Autowired
    private LocationDAO locationDAO;

    @Autowired
    private AppointmentDAO appointmentDAO;

    @Autowired
    private CustomerDAO customerDAO;

    @Autowired
    private ExternalApiService externalApiService;

    @Autowired
    private LocationProfileDAO locationProfileDAO;

    // Inject the service
    @Autowired
    private AppointmentService appointmentService;


    @Value("${adp.crmApiKey}")
    private String crmApiKey;

    @Value("${adp.crmUrl}")
    private String crmUrl;

    @Value("${adp.cancelledcrmUrl}")
    private String cancelledcrmUrl;


    @Value("${adp.rescheduledcrmUrl}")
    private String rescheduledcrmUrl;

    @Value("${adp.subscribedrescheduledcrmUrl}")
    private String subscribedrescheduledcrmUrl;


    // https://gcapi.guitarcenter.com/dev/gcss/v1/lessons/trial/cancelled/brand/gc

    @Value("${adp.crmContentType}")
    private String crmContentType;

    @Override
    @Async
    public void sendSMSForModifySingleApptAsync(String date, String startTime, String endTime,
                                                List<Map<String, Object>> emailBodies, Long serviceId,
                                                String cancelType, String instructorName, String serviceType,
                                                String curCustName, long profileID, String activityName,
                                                String roomName, UpdateSingleApptDTO updateSingleApptDTO) {

        LOG.debug("Starting async email processing for modify single appointment");

        Criterion<Location, Location> locationCriterion = LocationCriterion.getByProfileId(profileID);

        Location location = locationDAO.get(locationCriterion);

        // Get orderId from appointment if available
       // String orderId = null;
      /*  try {
            if (updateSingleApptDTO.getAptId() != null) {
                Long appointmentId = Long.parseLong(updateSingleApptDTO.getAptId());
                orderId = appointmentService.getTransactionIdByAppointmentId(appointmentId);
                LOG.debug("Retrieved orderId for appointment {}: {}", appointmentId, orderId);
            }
        } catch (Exception e) {
            LOG.warn("Could not retrieve orderId for appointment {}: {}", updateSingleApptDTO.getAptId(), e.getMessage());
        }*/

        LessonScheduledDTO lessonDTO = createLessonScheduledForSMSDTO(date, startTime, endTime, emailBodies, serviceId,
                cancelType, instructorName, serviceType, curCustName, profileID, activityName, roomName, location, updateSingleApptDTO);
        System.out.println("Appointment Type is Trail Lesson, so sending sms using CRM template");

        try {
            org.springframework.http.ResponseEntity<String> responsdfdsse = externalApiService.postJsonToUrl(subscribedrescheduledcrmUrl, crmApiKey, crmContentType, lessonDTO);
        } catch (Exception e) {
            LOG.error("Error posting to CRM URL", e);
        }

    }

    @Override
    @Async
    public void sendEmailForModifySingleApptAsync(Appointment appointment, String modifyType, int duration,
                                                  String instructorName, Room room, Activity activityName,
                                                  long profileID) {

        LOG.debug("Starting async email processing for modify single appointment (Appointment object version)");

        try {
            //TODO:: OLL-3811 If lesson type is Trail send email using CRM template to the customer and store users notification as normal
            //1.check the lesson type 2.If Trail then  form the object and body using CRM template 3. send email using CRM template
            //And also make sure customer email is not set if Trail Lesson type
            //For other lesson types send email using existing template
            String orderId = appointmentService.getTransactionIdByAppointmentId(appointment.getAppointmentId());
            if (String.valueOf(appointment.getActivity().getActivityId()).equals("140") ||
                    String.valueOf(appointment.getActivity().getActivityId()).equals("320")) {
              //  appointmentService.getTransactionIdByAppointmentId(appointment.getAppointmentId());
                LessonScheduledDTO lessonDTO = createLessonScheduledDTO(appointment, modifyType, duration, instructorName, activityName, profileID, orderId);
                System.out.println("Appointment Type is Trail Lesson, so sending email using CRM template");

                try {
                    org.springframework.http.ResponseEntity<String> response = externalApiService.postJsonToUrl(rescheduledcrmUrl, crmApiKey, crmContentType, lessonDTO);
                } catch (Exception e) {
                    LOG.error("Error posting to CRM URL", e);
                }
            }

            if (appointment.getCustomers() != null) {
                // Get employee email list
                //For GSSP-243 send email with cc Associates/Managers/Leads
                String[] emailList = getEmployeeEmailIdsbyProfileID(profileID);
                Iterator<Customer> customerIter = appointment.getCustomers().iterator();
                while (customerIter.hasNext()) {
                    Customer customer = customerIter.next();
                    //GSSP-278 code changes
                    Map<String, Object> dataMap = renderDataForModifySingleEmail(appointment,
                            modifyType, duration, customer, instructorName, room, activityName, profileID);
                    //For GSSP-243 send email with cc Associates/Managers/Leads
                    dataMap.put(AppConstants.EMAIL_TYPE_BCC, emailList);

                    // Send the email
                    mailSenderService.sendMail(dataMap, EMAIL_TEMPLATE_FOR_SINGLE_APPT);
                }
            }

            LOG.debug("Completed async email processing for modify single appointment (Appointment object version)");

        } catch (Exception e) {
            LOG.error("Error in async email processing for modify single appointment (Appointment object version)", e);
        }
    }

    @Override
    @Async
    public void sendEmailForCancelSingleApptAsync(Appointment appointment, String cancelType, int duration,
                                                  Set<Customer> customerSet, String instructorName,
                                                  Activity activityname, long profileID) {

        LOG.debug("Starting async email processing for cancel single appointment");

        try {
            //TODO:: OLL-3811 If lesson type is Trail send email using CRM template to the customer and store users notification as normal
            //1.check the lesson type 2.If Trail then  form the object and body using CRM template 3. send email using CRM template
            //And also make sure customer email is not set if Trail Lesson type
            //For other lesson types send email using existing template
            String orderId = appointmentService.getTransactionIdByAppointmentId(appointment.getAppointmentId());

            if (String.valueOf(appointment.getActivity().getActivityId()).equals("140") ||
                    String.valueOf(appointment.getActivity().getActivityId()).equals("320")) {
                LessonScheduledDTO lessonDTO = createLessonScheduledDTO(appointment, "2", duration, instructorName, activityname, profileID, orderId);
                System.out.println("Appointment Type is Trail Lesson, so sending email using CRM template");

                try {
                    org.springframework.http.ResponseEntity<String> response = externalApiService.postJsonToUrl(cancelledcrmUrl, crmApiKey, crmContentType, lessonDTO);
                } catch (Exception e) {
                    LOG.error("Error posting to CRM URL", e);
                }
            }

            if (customerSet != null) {
                // Get employee email list
                //For GSSP-243 send email with cc Associates/Managers/Leads
                String[] emailList = getEmployeeEmailIdsbyProfileID(profileID);
                Iterator<Customer> customerIter = customerSet.iterator();
                while (customerIter.hasNext()) {
                    Customer customer = customerIter.next();
                    Map<String, Object> dataMap = renderDataForSingleCancelEmail(appointment,
                            cancelType, duration, customer, instructorName, activityname);
                    //For GSSP-243 send email with cc Associates/Managers/Leads
                    dataMap.put(AppConstants.EMAIL_TYPE_BCC, emailList);

                    // Send the email
                    mailSenderService.sendMail(dataMap, EMAIL_TEMPLATE_FOR_SINGLE_APPT);
                }
            }

            LOG.debug("Completed async email processing for cancel single appointment");

        } catch (Exception e) {
            LOG.error("Error in async email processing for cancel single appointment", e);
        }
    }

    @Override
    @Async
    public void sendEmailForCreateApptAsync(Appointment appointment, String isRecurring, int duration,
                                            String instructorName, Room room, Activity activityName,
                                            long profileID) {

        LOG.debug("Starting async email processing for create appointment");

        try {
            //TODO:: OLL-3811 If lesson type is Trail send email using CRM template to the customer and store users notification as normal
            //1.check the lesson type 2.If Trail then  form the object and body using CRM template 3. send email using CRM template
            //And also make sure customer email is not set if Trail Lesson type
            //For other lesson types send email using existing template

            if (String.valueOf(appointment.getActivity().getActivityId()).equals("140") ||
                    String.valueOf(appointment.getActivity().getActivityId()).equals("320")) {
                LessonScheduledDTO lessonDTO = createLessonScheduledDTO(appointment, "0", duration, instructorName, activityName, profileID, null);
                System.out.println("Appointment Type is Trail Lesson, so sending email using CRM template");

                try {
                    org.springframework.http.ResponseEntity<String> response = externalApiService.postJsonToUrl(crmUrl, crmApiKey, crmContentType, lessonDTO);
                } catch (Exception e) {
                    LOG.error("Error posting to CRM URL", e);
                }
            }

            //For GSSP-243 send email with cc Associates/Managers/Leads
            String[] emailList = getEmployeeEmailIdsbyProfileID(profileID);

            if (appointment.getCustomers() != null) {
                Iterator<Customer> customerIter = appointment.getCustomers().iterator();
                while (customerIter.hasNext()) {
                    Customer customer = customerIter.next();
                    Map<String, Object> dataMap = renderDataForCreateEmail(appointment, isRecurring, duration,
                            instructorName, room, activityName, customer, profileID);

                    //For GSSP-243 send email with cc Associates/Managers/Leads
                    dataMap.put(AppConstants.EMAIL_TYPE_BCC, emailList);

                    // Send the email asynchronously (note: uses EMAIL_TEMPLATE_FOR_APPT, not EMAIL_TEMPLATE_FOR_SINGLE_APPT)
                    mailSenderService.sendMail(dataMap, "apptEmailTemplate.ftl");
                }
            }

            LOG.debug("Completed async email processing for create appointment");

        } catch (Exception e) {
            LOG.error("Error in async email processing for create appointment", e);
        }
    }

    @Override
    @Async
    public void sendEmailForCreateApptCRMFlowAsync(Appointment appointment, String isRecurring, int duration,
                                                   String instructorName, Room room, Activity activityname,
                                                   long profileID, String orderId) {

        LOG.debug("Starting async email processing for create appointment CRM flow");

        try {
            //TODO:: OLL-3811 If lesson type is Trail send email using CRM template to the customer and store users notification as normal
            //1.check the lesson type 2.If Trail then  form the object and body using CRM template 3. send email using CRM template
            //And also make sure customer email is not set if Trail Lesson type
            //For other lesson types send email using existing template

            if (String.valueOf(appointment.getActivity().getActivityId()).equals("140") ||
                    String.valueOf(appointment.getActivity().getActivityId()).equals("320")) {
                LessonScheduledDTO lessonDTO = createLessonScheduledDTO(appointment, "0", duration, instructorName, activityname, profileID, orderId);
                System.out.println("Appointment Type is Trail Lesson, so sending email using CRM template");

                try {
                    org.springframework.http.ResponseEntity<String> response = externalApiService.postJsonToUrl(crmUrl, crmApiKey, crmContentType, lessonDTO);
                } catch (Exception e) {
                    LOG.error("Error posting to CRM URL", e);
                }
            }

            //For GSSP-243 send email with cc Associates/Managers/Leads
            String[] emailList = getEmployeeEmailIdsbyProfileID(profileID);

            if (appointment.getCustomers() != null) {
                Iterator<Customer> customerIter = appointment.getCustomers().iterator();
                while (customerIter.hasNext()) {
                    Customer customer = customerIter.next();
                    Map<String, Object> dataMap = renderDataForCreateEmail(appointment, isRecurring, duration,
                            instructorName, room, activityname, customer, profileID);

                    //For GSSP-243 send email with cc Associates/Managers/Leads
                    dataMap.put(AppConstants.EMAIL_TYPE_BCC, emailList);

                    // Send the email asynchronously
                    mailSenderService.sendMail(dataMap, "apptEmailTemplate.ftl");
                }
            }

            LOG.debug("Completed async email processing for create appointment CRM flow");

        } catch (Exception e) {
            LOG.error("Error in async email processing for create appointment CRM flow", e);
        }
    }

    private void setEmailSubjectAndServiceType(Map<String, Object> dataMap, String cancelType,
                                               Long serviceId, String serviceType) {
        if (cancelType.trim().equalsIgnoreCase("1")) {
            if (serviceId == 0) {
                if (serviceType.trim().equalsIgnoreCase("UPDATE")) {
                    dataMap.put(AppConstants.SUBJECT, "Guitar Center rehearsal modified");
                    dataMap.put(SERVICE_TYPE, "modifyRehearsalNotAll");
                } else {
                    dataMap.put(AppConstants.SUBJECT, "Guitar Center rehearsal cancelled");
                    dataMap.put(SERVICE_TYPE, "cancelRehearsalNotAll");
                }
            } else {
                if (serviceType.trim().equalsIgnoreCase("UPDATE")) {
                    dataMap.put(AppConstants.SUBJECT, "Guitar Center lesson modified");
                    dataMap.put(SERVICE_TYPE, "modifyLessonNotAll");
                } else {
                    dataMap.put(AppConstants.SUBJECT, "Guitar Center lesson cancelled");
                    dataMap.put(SERVICE_TYPE, "cancelLessonNotAll");
                }
            }
        } else {
            if (serviceId == 0) {
                if (serviceType.trim().equalsIgnoreCase("UPDATE")) {
                    dataMap.put(AppConstants.SUBJECT, "Guitar Center rehearsal modified");
                    dataMap.put(SERVICE_TYPE, "modifyRehearsal");
                } else {
                    dataMap.put(AppConstants.SUBJECT, "Guitar Center rehearsal cancelled");
                    dataMap.put(SERVICE_TYPE, "cancelRehearsal");
                }
            } else {
                if (serviceType.trim().equalsIgnoreCase("UPDATE")) {
                    dataMap.put(AppConstants.SUBJECT, "Guitar Center lesson modified");
                    dataMap.put(SERVICE_TYPE, "modifyLesson");
                } else {
                    dataMap.put(AppConstants.SUBJECT, "Guitar Center lesson cancelled");
                    dataMap.put(SERVICE_TYPE, "cancelLesson");
                }
            }
        }
    }

    private String[] getEmployeeEmailIdsbyProfileID(long profileID) {
        try {
            // Use the same criterion as the original service
            com.guitarcenter.scheduler.dao.criterion.AppointmentCriterion<String> emailCriterion =
                    com.guitarcenter.scheduler.dao.criterion.AppointmentCriterion.findEmployeeEmailIdsbyProfileID(profileID);
            List<String> emailList = appointmentDAO.search(emailCriterion);
            String[] emailarray = null;

            if (null != emailList && emailList.size() > 0) {
                emailarray = emailList.toArray(new String[emailList.size()]);
            }
            return emailarray;
        } catch (Exception e) {
            LOG.error("Error getting employee email IDs for profile ID: " + profileID, e);
            return new String[0];
        }
    }

    private Map<String, Object> renderDataForModifySingleEmail(
            Appointment appointment, String modifyType, int duration, Customer customer, String instructorName, Room room, Activity activityName, Long profileID) {

        Map<String, Object> dataMap = renderBasicData(appointment, customer, duration);

        String dateTime = "";
        int dateTimeSplitIndex = 0;

        if (null != dataMap && dataMap.containsKey("date")) {
            dateTime = (String) dataMap.get("date");

            if (dateTime.indexOf("-") != -1) {
                dateTimeSplitIndex = dateTime.indexOf("-");

                if (dateTimeSplitIndex != 0) {
                    dataMap.put("time", dateTime.substring(dateTimeSplitIndex + 1));
                    dataMap.put("date", dateTime.substring(0, dateTimeSplitIndex));
                }
            }
        }
        //GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-starts
        dataMap.put("instructor", instructorName);
        dataMap.put("roomName", room.getProfileRoomName());
        dataMap.put("activityName", activityName.getActivityName());
        //GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-ends
        if ("1".equalsIgnoreCase(modifyType)) {
            if (appointment.getActivity().getService().getServiceId() == 0) {
                //changes for LES-151
                dataMap.put(AppConstants.SUBJECT, "Guitar Center rehearsal modified");
                dataMap.put(SERVICE_TYPE, "modifyRehearsal");
            } else {
                //changes for LES-151
                dataMap.put(AppConstants.SUBJECT, "Guitar Center lesson modified");
                dataMap.put(SERVICE_TYPE, "modifyLesson");
            }
        } else if ("3".equalsIgnoreCase(modifyType)) {
            if (appointment.getActivity().getService().getServiceId() == 0) {
                //changes for LES-151
                dataMap.put(AppConstants.SUBJECT, "Guitar Center rehearsal modified");
                dataMap.put(SERVICE_TYPE, "modifyRehearsalNotAll");
            } else {
                //changes for LES-151
                dataMap.put(AppConstants.SUBJECT, "Guitar Center lesson modified");
                dataMap.put(SERVICE_TYPE, "modifyLessonNotAll");
            }
        }
        return dataMap;
    }

    private Map<String, Object> renderDataForSingleCancelEmail(
            Appointment appointment, String cancelType, int duration, Customer customer, String instructorName, Activity activityname) {

        Map<String, Object> dataMap = renderBasicData(appointment, customer, duration);

        String dateTime = "";
        int dateTimeSplitIndex = 0;

        if (null != dataMap && dataMap.containsKey("date")) {
            dateTime = (String) dataMap.get("date");

            if (dateTime.indexOf("-") != -1) {
                dateTimeSplitIndex = dateTime.indexOf("-");

                if (dateTimeSplitIndex != 0) {
                    dataMap.put("time", dateTime.substring(dateTimeSplitIndex + 1));
                    dataMap.put("date", dateTime.substring(0, dateTimeSplitIndex));
                }
            }
        }
        //GSSP-278 Changes Made for mail confirmation ,adding activity,instructor room.-starts
        dataMap.put("instructor", instructorName);
        dataMap.put("activityName", activityname.getActivityName());
        //dataMap.put("cancelReason", cancelreason);
        //GSSP-278 Changes Made for mail confirmation ,adding activity,instructor room.-ends
        if ("1".equalsIgnoreCase(cancelType)) {
            if (appointment.getActivity().getService().getServiceId() == 0) {
                //changes for LES-151
                dataMap.put(AppConstants.SUBJECT, "Guitar Center rehearsal cancelled");
                dataMap.put(SERVICE_TYPE, "cancelRehearsal");
            } else {
                //changes for LES-151
                dataMap.put(AppConstants.SUBJECT, "Guitar Center lesson cancelled");
                dataMap.put(SERVICE_TYPE, "cancelLesson");
            }
        } else if ("3".equalsIgnoreCase(cancelType)) {
            if (appointment.getActivity().getService().getServiceId() == 0) {
                //changes for LES-151
                dataMap.put(AppConstants.SUBJECT, "Guitar Center rehearsal cancelled");
                dataMap.put(SERVICE_TYPE, "cancelRehearsalNotAll");
            } else {
                //changes for LES-151
                dataMap.put(AppConstants.SUBJECT, "Guitar Center lesson cancelled");
                dataMap.put(SERVICE_TYPE, "cancelLessonNotAll");
            }
        }
        return dataMap;
    }

    private Map<String, Object> renderDataForCreateEmail(
            Appointment appointment, String isRecurring, int duration, String instructorName, Room room, Activity activityName, Customer customer, long profileID) {

        Map<String, Object> dataMap = renderBasicData(appointment, customer, duration);

        //GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-starts
        dataMap.put("instructorName", instructorName);
        dataMap.put("roomName", room.getProfileRoomName());
        dataMap.put("activityName", activityName.getActivityName());
        //GSSP-278 Changes Made for create mail confirmation ,adding activity,instructor room.-end

        if ("false".equalsIgnoreCase(isRecurring)) {
            if (appointment.getActivity().getService().getServiceId() == 0) {
                //changes for LES-151
                dataMap.put(AppConstants.SUBJECT, "Rehearsal scheduled at Guitar Center");
                dataMap.put(SERVICE_TYPE, "newRehearsal");
            } else {
                //changes for LES-151
                dataMap.put(AppConstants.SUBJECT, "Lesson scheduled at Guitar Center");
                dataMap.put(SERVICE_TYPE, "newLesson");
            }
        } else {
            //changes for LES-151
            if (appointment.getActivity().getService().getServiceId() == 0) {
                dataMap.put(AppConstants.SUBJECT, "Rehearsals scheduled at Guitar Center");
                dataMap.put(SERVICE_TYPE, "newRehearsalRecurring");
            } else {
                //changes for LES-151
                dataMap.put(AppConstants.SUBJECT, "Lessons scheduled at Guitar Center");
                dataMap.put(SERVICE_TYPE, "newLessonRecurring");
            }
        }
        return dataMap;
    }

    private Map<String, Object> renderBasicData(Appointment appointment, Customer customer, float duration) {
        try {
            java.text.SimpleDateFormat format = new java.text.SimpleDateFormat("EEEE, MMM d, yyyy-hh:mm aaa");
            Map<String, Object> dataMap = new java.util.HashMap<String, Object>();
            if (customer.getPerson() == null) {
                customer = customerDAO.get(customer.getCustomerId(), DAOHelper.FETCH_PERSON);
            }
            Location location = getLocation(appointment);

            String customerName = customer.getPerson().getFirstName() + " " + customer.getPerson().getLastName();

            dataMap.put("customerEmail", customer.getPerson().getEmail());
            dataMap.put("customerName", customerName);
            dataMap.put("location", location.getLocationName());
            dataMap.put("date", format.format(appointment.getStartTime()));

            try {
                String updateByFirstName = appointment.getUpdatedBy().getFirstName();
                String updateByLastName = appointment.getUpdatedBy().getLastName();
                String updatedBy = (org.apache.commons.lang.StringUtils.isBlank(updateByFirstName) ? "" : (updateByFirstName.trim() + " ") + (org.apache.commons.lang.StringUtils.isBlank(updateByLastName) ? "" : updateByLastName));
                dataMap.put("updatedBy", updatedBy);
            } catch (Exception expected) {
                LOG.error("could not initialize proxy - no Session", expected);
            }

            String durationStr = "";
            if (duration >= 60) {
                if (60 == duration) {
                    durationStr = duration / 60 + " hr";
                } else {
                    durationStr = duration / 60 + " hrs";
                }
            } else {
                durationStr = duration + " Min";
            }
            durationStr = durationStr.replace(".0", "");

            dataMap.put("duration", durationStr);
            dataMap.put("phone", location.getPhone());

            return dataMap;
        } catch (Exception e) {
            LOG.error("Error in renderBasicData", e);
            return new java.util.HashMap<String, Object>();
        }
    }

    private Location getLocation(Appointment appointment) {
        try {
            Criterion<Location, Location> locationCriterion = LocationCriterion.getByProfileId(appointment.getLocationProfile().getProfileId());
            return locationDAO.get(locationCriterion);
        } catch (Exception e) {
            LOG.error("Error getting location for appointment", e);
            return null;
        }
    }

    private LessonScheduledDTO createLessonScheduledDTO(Appointment appointment, String modifyType, int duration, String instructorName, Activity activityName, long profileID, String orderId) {
        try {
            Customer customer = appointment.getCustomers().iterator().next();

            if (customer.getPerson() == null) {
                customer = customerDAO.get(customer.getCustomerId(), DAOHelper.FETCH_PERSON);
            }
            Location location = getLocation(appointment);

            LocationProfile locationProfileObj = getLocationProfilewithProfileId(appointment.getLocationProfile().getProfileId());

            LessonScheduledDTO lessonDTO = new LessonScheduledDTO();
            lessonDTO.setLessonsType("Trial"); //GSSP-278 code changes
            switch (modifyType) {
                case "0":
                    lessonDTO.setEvent("Scheduled");
                    break;
                case "1":
                    lessonDTO.setEvent("ReScheduled");
                    break;
                case "2":
                    lessonDTO.setEvent("Cancelled");
                    break;
                default:
                    lessonDTO.setEvent("Scheduled");
                    break;
            }
            // Debug logging for orderId and extracted value
            LOG.debug("Original orderId: {}", orderId);
            String extractedOrderId = extractOrderId(orderId);
            LOG.debug("Extracted orderId: {}", extractedOrderId);
            lessonDTO.setSfCustomerId1(extractedOrderId);//Need to set the Salesforce Customer ID if available
          // lessonDTO.setTestMyValue(extractedOrderId);
            LOG.debug("Set sfCustomerId to: {}", lessonDTO.getSfCustomerId1());
            lessonDTO.setLessonsRequestedDate(DateTimeUtil.formatDateForLesson(appointment.getStartTimeStr()));
            lessonDTO.setLessonsRequestedTime(DateTimeUtil.extractTimeForLesson(appointment.getStartTimeStr()));
            lessonDTO.setInstrument(activityName.getActivityName());
            lessonDTO.setStoreNumber(String.valueOf(location.getExternalId()));
            lessonDTO.setStoreStreet(String.valueOf(location.getAddress1()));
            lessonDTO.setStoreCity(String.valueOf(location.getCity()));
            lessonDTO.setStoreState(String.valueOf(location.getState()));
            lessonDTO.setStoreZip(String.valueOf(location.getZip()));
            lessonDTO.setLessonsDuration(String.valueOf(duration));
            lessonDTO.setLessonsFollowup("Y");
            Customer firstCustomer = appointment.getCustomers().iterator().next();
            lessonDTO.setLessonsFollowupName(customer.getPerson().getFirstName() + " " + customer.getPerson().getLastName());
            lessonDTO.setLessonsFollowupEmail(customer.getPerson().getEmail());
            lessonDTO.setLessonsFollowupPhone(DateTimeUtil.formatPhoneNumber(customer.getPerson().getPhone()));
            lessonDTO.setLessonsFollowupZipCode(String.valueOf(location.getZip()));
            lessonDTO.setComments(appointment.getNote());
            lessonDTO.setSource("GCSS");
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("MM-dd-yyyy hh:mm a");
            String formattedDate = sdf.format(new java.util.Date());
            lessonDTO.setUpdated(formattedDate);
            lessonDTO.setAppointmentId(String.valueOf(appointment.getAppointmentId()));
            lessonDTO.setCustomerId(String.valueOf(customer.getExternalId()));
            lessonDTO.setAppointmentId(String.valueOf(appointment.getAppointmentId()));
            //lessonDTO.setInstrument(String.valueOf(appointment.getActivity().getActivityId()));
            LOG.debug("Final sfCustomerId before returning: {}", lessonDTO.getSfCustomerId1());
            return lessonDTO;
        } catch (Exception e) {
            LOG.error("Error creating LessonScheduledDTO", e);
            return null;
        }
    }

    public  String extractOrderId(String input) {
        LOG.debug("extractOrderId called with input: {}", input);
        if (input == null) {
            LOG.debug("extractOrderId returning null - input was null");
            return null;
        }
        int firstUnderscore = input.indexOf('_');
        int lastUnderscore = input.lastIndexOf('_');
        if (firstUnderscore != -1 && lastUnderscore != -1 && lastUnderscore > firstUnderscore + 1) {
            String result = input.substring(firstUnderscore + 1, lastUnderscore);
            LOG.debug("extractOrderId returning extracted value: {}", result);
            return result;
        }
        LOG.debug("extractOrderId returning original input: {}", input);
        return input;
    }

    private LessonScheduledDTO createLessonScheduledForSMSDTO(String date, String startTime, String endTime,
                                                        List<Map<String, Object>> emailBodies, Long serviceId,
                                                        String cancelType, String instructorName, String serviceType,
                                                        String curCustName, long profileID, String activityName,
                                                        String roomName, Location location, UpdateSingleApptDTO updateSingleApptDTO
                                                        ) {
        LessonScheduledDTO lessonDTO = new LessonScheduledDTO();
        try {


            lessonDTO.setLessonsType("Subscription"); //GSSP-278 code changes
            switch (cancelType) {
                case "0":
                    lessonDTO.setEvent("Scheduled");
                    break;
                case "1":
                    lessonDTO.setEvent("ReScheduled");
                    break;
                case "2":
                    lessonDTO.setEvent("Cancelled");
                    break;
                default:
                    lessonDTO.setEvent("Scheduled");
                    break;
            }


            String inputDate = date; // date in YYYY-MM-DD
            SimpleDateFormat fromFormat = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat toFormat = new SimpleDateFormat("MM-dd-yyyy");
            Date parsedDate = fromFormat.parse(inputDate);
            String formattedDate = toFormat.format(parsedDate);
            lessonDTO.setLessonsRequestedDate(formattedDate);
            lessonDTO.setLessonsRequestedTime(startTime);
            lessonDTO.setInstrument(activityName);
            lessonDTO.setStoreNumber(location.getExternalId());
            lessonDTO.setStoreStreet(location.getAddress1()); // Assuming roomName is the street
            lessonDTO.setStoreCity(location.getCity()); // Not provided in the parameters
            lessonDTO.setStoreState(location.getState()); // Not provided in the parameters
            lessonDTO.setStoreZip(location.getZip()); // Not provided in the parameters
            //lessonDTO.setLessonsDuration((updateSingleApptDTO.getEndTime() - updateSingleApptDTO.getStartTime()); // Assuming endTime is duration
          java.time.format.DateTimeFormatter timeFormatter1 = DateTimeFormatter.ofPattern("h:mm a");
            long durationMinutes = Duration.between(
                    LocalTime.parse(startTime, timeFormatter1),
                    LocalTime.parse(endTime, timeFormatter1)
            ).toMinutes();
            if (durationMinutes < 0) durationMinutes += 24 * 60; // handle overnight

            lessonDTO.setLessonsDuration(String.valueOf(durationMinutes));
            lessonDTO.setLessonsFollowup("Y");
            lessonDTO.setLessonsFollowupName(curCustName);
           Object emailObj = emailBodies.get(0).get("email_type_to");
           if (emailObj instanceof String[]) {
               lessonDTO.setLessonsFollowupEmail(((String[]) emailObj)[0]);
           } else if (emailObj instanceof String) {
               lessonDTO.setLessonsFollowupEmail((String) emailObj);
           }
            // Not provided in the parameters
            lessonDTO.setLessonsFollowupPhone(updateSingleApptDTO.getCustomerPhone()); // Not provided in the parameters
            lessonDTO.setLessonsFollowupZipCode(location.getZip()); // Not provided in the parameters
            lessonDTO.setComments(updateSingleApptDTO.getNote()); // Not provided in the parameters
            lessonDTO.setSource("GCSS");
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("MM-dd-yyyy hh:mm a");
            String formattedDate5 = sdf.format(new java.util.Date());
            lessonDTO.setUpdated(formattedDate5);
            lessonDTO.setAppointmentId(String.valueOf(updateSingleApptDTO.getAptId()));
            lessonDTO.setCustomerId(String.valueOf(updateSingleApptDTO.getCustMemberID()));

            // Set Salesforce Customer ID if orderId is provided
         /*   if (orderId != null) {
                String extractedOrderId = extractOrderId(orderId);
                LOG.debug("Setting sfCustomerId from orderId: {} -> {}", orderId, extractedOrderId);
                lessonDTO.setSfCustomerId1(extractedOrderId);
            }*/
        } catch (Exception e) {
            LOG.error("Error creating LessonScheduledDTO", e);
            return null;
        }
        return lessonDTO;
    }




    private LocationProfile getLocationProfilewithProfileId(Long profileId) {
        try {
            return locationProfileDAO.get(profileId);
        } catch (Exception e) {
            LOG.error("Error getting LocationProfile for profileId: " + profileId, e);
            return null;
        }
    }
}