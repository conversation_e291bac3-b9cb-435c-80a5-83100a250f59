package com.guitarcenter.scheduler.service.impl;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_ROLE;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.EmployeeDAO;
import com.guitarcenter.scheduler.dao.LocationDAO;
import com.guitarcenter.scheduler.dao.PersonDAO;
import com.guitarcenter.scheduler.dao.PersonRoleDAO;
import com.guitarcenter.scheduler.dao.RoleDAO;
import com.guitarcenter.scheduler.dao.SiteDAO;
import com.guitarcenter.scheduler.dao.criterion.Criterion;
import com.guitarcenter.scheduler.dao.criterion.PersonRoleCriterion;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.model.Employee;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.PersonRole;
import com.guitarcenter.scheduler.model.Role;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.security.GCSSAuthenticationProviderDecorator;
import com.guitarcenter.scheduler.service.PersonRoleService;

@Service("personRoleService")
public class PersonRoleServiceImpl implements PersonRoleService {

	@Resource
	private PersonRoleDAO personRoleDAO;
	
	@Resource
	private LocationDAO locationDAO;
	
	@Resource 
	private PersonDAO personDAO;
	
	@Resource
	private RoleDAO roleDAO;
	
	@Resource
	private SiteDAO siteDAO;
	
	@Resource
	private EmployeeDAO employeeDAO;
	
	@Transactional
	@Override
	public PersonRole findByPersonLocation(long siteId, long personId,
			long locationId) {

		Criterion<PersonRole, PersonRole> criterion = PersonRoleCriterion.findByPersonLocation(siteId,personId,locationId);
		List<PersonRole> personRoles = personRoleDAO.search(criterion,  FETCH_ROLE);
		if(personRoles.size() >0){
			return personRoles.get(0);
		}
		
		return new PersonRole();
	}

	@Override
	public Set<PersonRole> findByPerson(long siteId, long personId) {
		Criterion<PersonRole, PersonRole> criterion = PersonRoleCriterion.findByPerson(siteId,personId);
		List<PersonRole> personRoles = personRoleDAO.search(criterion,  FETCH_ROLE|DAOHelper.FETCH_LOCATION);
		return new HashSet<PersonRole>(personRoles);
	}
	//created for GSSP-146
	@Override
	public List<PersonRole> findRolesByPersondIds(List<Long> personId) {
		Criterion<PersonRole, PersonRole> criterion = PersonRoleCriterion.findRolesByPersondIds(personId);
		List<PersonRole> personRoles = personRoleDAO.search(criterion,  FETCH_ROLE|DAOHelper.FETCH_LOCATION);
		return personRoles;
	}

	/**
	 * Returns a list of PersonRole instances that are associated by site and
	 * authentiction id.
	 * 
	 * Note: most child objects that are typically lazy-loaded are specified
	 *       since most interation with these instances will be outside of a
	 *       transaction.
	 * 
	 * @see GCSSAuthenticationProviderDecorator for use
	 */
    @Override
    public List<PersonRole> findByAuthId(long siteId, String authId) {
        Criterion<PersonRole, PersonRole> criterion = PersonRoleCriterion.findByAuthId(siteId,authId);
        return personRoleDAO.search(criterion,
                                    FETCH_ROLE|DAOHelper.FETCH_LOCATION|DAOHelper.FETCH_SITE|DAOHelper.FETCH_PERSON|DAOHelper.FETCH_LOCATION_PROFILE);
    }

	/**
	 * list contains elements: locationId
	 */
	@Transactional
	@Override
	public void updatePersonRole(long siteId, long personId, List<String> list,Person updateBy,Employee employee) {
	
		employeeDAO.update(employee, updateBy.getPersonId());
		
		Set<PersonRole> personRoles = findByPerson(siteId,personId);
		for (PersonRole personRole : personRoles) {
			personRoleDAO.delete(personRole);
		}
		Site site = siteDAO.get(siteId);

		for (String string : list) {
			String externalId = string.substring(0,string.indexOf("-")).trim();
			String roleName = string.substring(string.indexOf("-")+1, string.length()).trim();
			PersonRole personRole = null;
			Location locationExample = new Location();
			locationExample.setExternalId(externalId);
			Location location = locationDAO.get(locationExample);
			Person person = personDAO.get(personId);
			Role example = new Role();
			example.setRoleName(roleName);
			Role role = roleDAO.get(example);
			for (PersonRole personRole2 : personRoles) {
				if (personRole2.getPerson().getPersonId().equals(person.getPersonId()) && personRole2.getLocation().getLocationId().equals(location.getLocationId())) {
					personRole = personRole2;
				}
			}
			if (null == personRole) {
				personRole = new PersonRole();
			}
			personRole.setLocation(location);
			personRole.setPerson(person);
			personRole.setRole(role);
			personRole.setSite(site);
			personRoleDAO.save(personRole,updateBy.getPersonId());
		}
	}

}
