package com.guitarcenter.scheduler.service;

import org.springframework.http.ResponseEntity;

/**
 * Service interface for making external API calls
 * Provides methods to POST JSON data to external URLs with custom headers
 */
public interface ExternalApiService {
    
    /**
     * Posts JSON data to the specified URL with custom headers
     * 
     * @param url The target URL to post data to
     * @param apiKey The API key for authentication (will be set as x-api-key header)
     * @param contentType The content type (typically "application/json")
     * @param dtoObject The DTO object to be serialized to JSON and sent in the request body
     * @return ResponseEntity<String> containing the response from the external API
     * @throws Exception if there are issues with the HTTP request or JSON serialization
     */
    ResponseEntity<String> postJsonToUrl(String url, String apiKey, String contentType, Object dtoObject) throws Exception;
    
    /**
     * Convenience method specifically for posting lesson scheduled data to the Guitar Center API
     * 
     * @param apiKey The API key for authentication
     * @param lessonScheduledDTO The lesson scheduled DTO object
     * @return ResponseEntity<String> containing the response from the API
     * @throws Exception if there are issues with the HTTP request
     */
    ResponseEntity<String> postLessonScheduledData(String apiKey, Object lessonScheduledDTO) throws Exception;
    
    /**
     * Method with retry logic for robust API communication
     * 
     * @param url The target URL
     * @param apiKey The API key
     * @param contentType The content type
     * @param dtoObject The DTO object
     * @param maxRetries Maximum number of retry attempts
     * @return ResponseEntity<String> containing the response
     * @throws Exception if all retry attempts fail
     */
    ResponseEntity<String> postJsonToUrlWithRetry(String url, String apiKey, String contentType, 
                                                 Object dtoObject, int maxRetries) throws Exception;
}
