package com.guitarcenter.scheduler.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.webservice.dto.UpdateSingleApptDTO;

/**
 * Service interface for handling asynchronous email operations.
 * This service is designed to break circular dependencies by providing
 * a separate async layer for email operations.
 */
public interface AsyncEmailService {

    /**
     * Sends email for modify single appointment asynchronously.
     * This method runs in a separate thread and returns immediately.
     *
     * @param date The appointment date
     * @param startTime The start time
     * @param endTime The end time
     * @param emailBodies List of email data maps
     * @param serviceId The service ID
     * @param cancelType The cancellation type
     * @param instructorName The instructor name
     * @param serviceType The service type
     * @param curCustName The current customer name
     * @param profileID The profile ID
     * @param activityName The activity name
     * @param roomName The room name
     */
    void sendSMSForModifySingleApptAsync(String date, String startTime, String endTime,
                                         List<Map<String, Object>> emailBodies, Long serviceId,
                                         String cancelType, String instructorName, String serviceType,
                                         String curCustName, long profileID, String activityName,
                                         String roomName, UpdateSingleApptDTO updateSingleApptDTO);

    /**
     * Sends email for modify single appointment asynchronously (Appointment object version).
     * This method runs in a separate thread and returns immediately.
     *
     * @param appointment The appointment object
     * @param modifyType The modification type
     * @param duration The duration
     * @param instructorName The instructor name
     * @param room The room object
     * @param activityName The activity object
     * @param profileID The profile ID
     */
    void sendEmailForModifySingleApptAsync(Appointment appointment, String modifyType, int duration,
                                          String instructorName, Room room, Activity activityName,
                                          long profileID);

    /**
     * Sends email for cancel single appointment asynchronously.
     * This method runs in a separate thread and returns immediately.
     *
     * @param appointment The appointment object
     * @param cancelType The cancellation type
     * @param duration The duration
     * @param customerSet The set of customers
     * @param instructorName The instructor name
     * @param activityname The activity object
     * @param profileID The profile ID
     */
    void sendEmailForCancelSingleApptAsync(Appointment appointment, String cancelType, int duration,
                                          Set<Customer> customerSet, String instructorName,
                                          Activity activityname, long profileID);

    /**
     * Sends email for create appointment asynchronously.
     * This method runs in a separate thread and returns immediately.
     *
     * @param appointment The appointment object
     * @param isRecurring Whether the appointment is recurring
     * @param duration The duration
     * @param instructorName The instructor name
     * @param room The room object
     * @param activityName The activity object
     * @param profileID The profile ID
     */
    void sendEmailForCreateApptAsync(Appointment appointment, String isRecurring, int duration,
                                    String instructorName, Room room, Activity activityName,
                                    long profileID);

    /**
     * Sends email for create appointment with CRM flow asynchronously.
     * This method runs in a separate thread and returns immediately.
     *
     * @param appointment The appointment object
     * @param isRecurring Whether the appointment is recurring
     * @param duration The duration
     * @param instructorName The instructor name
     * @param room The room object
     * @param activityname The activity object
     * @param profileID The profile ID
     * @param orderId The order ID for CRM integration
     */
    void sendEmailForCreateApptCRMFlowAsync(Appointment appointment, String isRecurring, int duration,
                                           String instructorName, Room room, Activity activityname,
                                           long profileID, String orderId);
}
