package com.guitarcenter.scheduler.common.util;

public interface AppConstants {
	
	//=====================================status=======================================
	public static final String STAFF_STATUS = "Enable";
	public static final String STAFF_NO_STATUS = "Unable";
	public static final String INSTRUCTOR_STATUS = "Enable";
	public static final String INSTRUCTOR_NO_STATUS = "Unable";
	//=====================================status=======================================
	
	//Added for GSSP-199
	public static final String INSTRUCTOR_ENTERPRISE_STATUS = "A";
	public static final String INSTRUCTOR_AVAILABLE_STATUS = "Enabled";


	public static final String UPDATE_SUCCESS = "update success!";
	public static final String DELETE_SUCCESS = "delete success!";
	public static final String ERROR_STRING = "errorInfo";
	public static final String STORE_STRING = "store";
	public static final String DOMAIN_STRING = "domain";
	
	public static final String TIME_STRING = "hh:mm";
	public static final String[] WEEKEND_CHOSE = {"Sun","Mon","Tue","Wed","Thu","Fri","Sat"};
	public static final String[] INSTRUC_WEEKEND_CHOSE = {"Mon","Tue","Wed","Thu","Fri","Sat","Sun"};
	public static final String LOGIN_URL_STRING = "/login.jsp";
	public static final String MESSAGE_ERROR = "message_error";
	public final static String LOCATION_STRING = "location";
	public final static String PERSON_STRING = "person";

	
	
	public static final String SITE_ADMIN_STRING = "Site Admin";
	public static final String STUDIO_MANAGER_STRING = "Studio Manager";
	public static final String STUDIO_LEAD_STRING = "Studio Lead";
	public static final String STUDIO_ASSOCIATE_STRING = "Studio Associate";
	//Changes made for GSSP-199
	public static final String INSTRUCTOR_STRING = "Instructor";
	public static final String SITE_ID_SESSION_KEY = "siteId";
	public static final String J_USER_NAME = "j_username";
	public static final String J_PASSWORD = "j_password";
	public static final String ROLE_STRING="role";
	
	//Changes made for GSSP-209
	public static final String DISTRICT_MANAGER = "District Manager";
	public static final String REGIONAL_VICE_PRESIDENT = "Regional Vice President";
	
	public static final String PROPERTIES_STRING = "message.properties";
	
	public static final String SPLITOR_DASH		   	   = "-";
	public static final String SPLITOR_SLASH		   = "/";
	public static final String SPLITOR_COLON		   = ":";
	
	public static final String LOCATION_PROFILE_ID_SESSION_KEY	= "locationProfileId";
	public static final String LOCATION_ID_SESSION_KEY			= "location";
	public static final String PERSON_SESSION_KEY				= "person";
	
    /* These constants define the type of record being returned by the
     * search engine. E.g. SearchDTO.type (or JSON representation)
     * will contain one of these values.
     */
	public static final String SEARCH_LOCATION_TYPE_STRING = "location";
	public static final String SEARCH_CUSTOMER_TYPE_STRING = "customer";
	public static final String SEARCH_INSTRUCTOR_TYPE_STRING = "instructor";
    public static final String SEARCH_MORE_RESULTS_TYPE_STRING = "more";
    
    /*constants for email
     *
     */
    public static final String EMAIL_TYPE_TO = "email_type_to";
    public static final String EMAIL_TYPE_CC = "email_type_cc";
    public static final String EMAIL_TYPE_BCC = "email_type_bcc";
    public static final String FROM = "from";
    public static final String SUBJECT = "subject";
    public static final String EMAIL_HEADER_IMG_URL = "emailHeaderImgUrl";
    
    public static final String SUNDAY_AVAILIBILITY_TIME			= "sundayAvailibilityTime";
    public static final String MONDAY_AVAILIBILITY_TIME			= "mondayAvailibilityTime";
    public static final String TUESDAY_AVAILIBILITY_TIME		= "tuesdayAvailibilityTime";
    public static final String WEDNESDAY_AVAILIBILITY_TIME		= "wednesdayAvailibilityTime";
    public static final String THURSDAY_AVAILIBILITY_TIME		= "thursdayAvailibilityTime";
    public static final String FRIDAY_AVAILIBILITY_TIME			= "fridayAvailibilityTime";
    public static final String SATURDAY_AVAILIBILITY_TIME		= "saturdayAvailibilityTime";

    /**
     * TimeZone definition for system
     */
    public static final String DEFAULT_TIME_ZONE = "America/Los_Angeles";
    
    //ADded for Phase 2 POC
    public static final String[] STUDIO_HOURS_WEEKDAY_CHOSE = {"Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"};
   
 // constant for JobNotificationEamilService
    public static final String[] JOB_NOTIFICATION_EMAIL_SEND_LIST={"<EMAIL>"};
    
    
    public static final String[] JOB_NOTIFICATION_EMAIL_SEND_LIST_QA={"<EMAIL>"};
    
    //Changes made for gSSP-230
    public static final String[] GCSS_SUPPORT={"<EMAIL>"};
    
   //Changes made for GSSP-238
    public static final Integer NUMBER_OF_YEARS = 4;
    public static final String ALL_CONFLICT_APPOINTMENT_REPORT_FILE_NAME = "AllStoreConflictingAppointmentsReport.xls";
	
	//GSSP-240
    public static final String INSTRUCTOR_ACTIVITIES_EMAIL_SUBJECT = "Weekly Instructors and activities report"; 
    //GSSP-298
    public static final String INSTRUCTOR_ACTIVITIES_AVAILABILITY_EMAIL_SUBJECT = "Monthly Instructors activities and availability report";
    public static final String INSTR_ACTIVITIES_AVAILABLITY_FILE_NAME = "Monthly_Instructors_Availability_And_Activities_Report.xls";
    public static final String INSTRUCTOR_ACTIVITIES_EMAIL_BODY = "Please find the weekly Instructors and activities report.";    
	public static final String INSTR_ACTIVITIES_FILE_NAME ="All_stores_instructors_and_activities.xls";
	
	public static final String CUSTOMER_REPORT_DETAILS_FILE_NAME="GC_DailySubscriptionReport.xls";
	 public static final String DAILY_SUBCRPTION_REPORT_SUBJECT = "Daily Subscription Report "; 
	
	public static final String FILE_PATH ="/home/<USER>/";
	
	//GSSP-272
	public static final String INSTRUCTOR_FLOORTIME_FILE_NAME = "All_Stores_Weekly_Floor_Time_Report_";
	public static final String FILE_EXTENTION = ".xls";
	public static final String INSTRUCTOR_FLOORTIME_EMAIL_SUBJECT = "Weekly instructor floor time report";
//Changes made for -265
    
    public static final String INSTRUCTOR_REPORT_FILE_NAME = "InstructorScheduleReport.xls";
//Changes made for -267
    

    public static final String SUBJECT_LOG="Instructor(s) Schedule at store ";
    
	//GSSP-268 lack Appointment job issue start
	public static final String FLAG_1="This Profile is invalid";
	public static final String FLAG_2="This Instructor is invalid";
	public static final String FLAG_3="This room  is invalid";
	public static final String FLAG_4="The start time or the end time is invalid";
	public static final String FLAG_5="The room or The instructor has conflict at Start Time and End Time";
	//lack Appointment job issue end   
	
	
	//Changes made to GSSP-270
	public static final String SINGLE_CANCEL_REASON="N";
	
	public static final  String SINCLE_CANCEL="single";
	public static final  String ACTIVITY_NAME="In-Store Floor";
	//Gssp-275 ADP file reading changes
	public static final  String EXTENSTION="csv";
	
	//CRMI - 338 changes
	public static final String APPLICATION_DATA_FILE_NAME  = "CRM_GCSS_LESSON_APPT_DAT_";
 
}
