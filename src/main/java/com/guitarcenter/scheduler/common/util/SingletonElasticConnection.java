package com.guitarcenter.scheduler.common.util;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;

import javax.net.ssl.SSLContext;

import org.apache.http.HttpHost;
import org.apache.http.HttpRequestInterceptor;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.ssl.SSLContexts;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;

import com.amazonaws.auth.AWS4Signer;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;
import com.guitarcenter.scheduler.elesticsearch.AWSRequestSigningApacheInterceptor;

public class SingletonElasticConnection {
    private static volatile SingletonElasticConnection instance;
    private RestHighLevelClient client;
    private final AWSCredentialsProvider credentialsProvider = new DefaultAWSCredentialsProviderChain();

    private SingletonElasticConnection() {} // Private constructor

    public static SingletonElasticConnection getInstance() {
        if (instance == null) {
            synchronized (SingletonElasticConnection.class) {
                if (instance == null) {
                    instance = new SingletonElasticConnection();
                }
            }
        }
        return instance;
    }

    public RestHighLevelClient getConnection(Map<String, String> elasticConfig) {
        if (client == null) {
            synchronized (this) {
                if (client == null) {
                    client = createConnection(
                        elasticConfig.get("serviceName"),
                        elasticConfig.get("region"),
                        elasticConfig.get("aesEndpoint")
                    );
                }
            }
        }
        return client;
    }

    private RestHighLevelClient createConnection(String serviceName, String region, String aesEndpoint) {
        try {
            return createElasticsearchClient(serviceName, region, aesEndpoint);
        } catch (KeyManagementException | NoSuchAlgorithmException | KeyStoreException e) {
            throw new RuntimeException("Error creating Elasticsearch connection", e);
        }
    }

    private RestHighLevelClient createElasticsearchClient(String serviceName, String region, String aesEndpoint)
            throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
        SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(null, (x509Certificates, s) -> true).build();

        AWS4Signer signer = new AWS4Signer();
        signer.setServiceName(serviceName);
        signer.setRegionName(region);

        HttpRequestInterceptor interceptor = new AWSRequestSigningApacheInterceptor(serviceName, signer, credentialsProvider);

        return new RestHighLevelClient(
            RestClient.builder(HttpHost.create(aesEndpoint))
                .setHttpClientConfigCallback(hacb -> 
                    hacb.addInterceptorLast(interceptor)
                        .setSSLContext(sslContext)
                        .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                )
        );
    }

    public void close() {
        if (client != null) {
            synchronized (this) {
                if (client != null) {
                    try {
                        client.close();
                    } catch (IOException e) {
                        System.err.println("Error closing Elasticsearch client: " + e.getMessage());
                    } finally {
                        client = null;
                    }
                }
            }
        }
    }
}
