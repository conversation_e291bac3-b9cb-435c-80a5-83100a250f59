package com.guitarcenter.scheduler.dto;

import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import javax.xml.bind.annotation.XmlType;

@XmlRootElement(name = "EmployeeScheduleImport")
 
public class EmployeeScheduleDTO2 {

 
	private EmployeeScheduleImport employeeScheduleImport;

	@XmlElement(name = "ScheduleImport")
	public EmployeeScheduleImport getEmployeeScheduleImport() {
		return employeeScheduleImport;
	}

	public void setEmployeeScheduleImport(EmployeeScheduleImport employeeScheduleImport) {
		this.employeeScheduleImport = employeeScheduleImport;
	}

	 

	//@XmlElement(name = "ScheduleImport")
	 
 
	
	 
	 

}
