package com.guitarcenter.scheduler.dto;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Version;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.joda.time.DateTimeConstants;

 
public class AvailabilityValueDTO2 implements java.io.Serializable {

	private static final long	serialVersionUID	= 6553481074225642599L;
	
	
	private String				availabilityId;
	private String				instructor_id;
	private String				mondayStartTime;
	private String				mondayEndTime;
	private String				tuesdayStartTime;
	private String				tuesdayEndTime;
	private String				wednesdayStartTime;
	private String				wednesdayEndTime;
	private String				thursdayStartTime;
	private String				thursdayEndTime;
	private String				fridayStartTime;
	private String				fridayEndTime;
	private String				saturdayStartTime;
	private String				saturdayEndTime;
	private String				sundayStartTime;
	private String				sundayEndTime;
	private String				profilelId;
	
	private String				locationId;
	private String				insExternalId;



	public AvailabilityValueDTO2() {
	}



	public String getAvailabilityId() {
		return availabilityId;
	}



	public void setAvailabilityId(String availabilityId) {
		this.availabilityId = availabilityId;
	}



	public String getInstructor_id() {
		return instructor_id;
	}



	public void setInstructor_id(String instructor_id) {
		this.instructor_id = instructor_id;
	}



	public String getMondayStartTime() {
		return mondayStartTime;
	}



	public void setMondayStartTime(String mondayStartTime) {
		this.mondayStartTime = mondayStartTime;
	}



	public String getMondayEndTime() {
		return mondayEndTime;
	}



	public void setMondayEndTime(String mondayEndTime) {
		this.mondayEndTime = mondayEndTime;
	}



	public String getTuesdayStartTime() {
		return tuesdayStartTime;
	}



	public void setTuesdayStartTime(String tuesdayStartTime) {
		this.tuesdayStartTime = tuesdayStartTime;
	}



	public String getTuesdayEndTime() {
		return tuesdayEndTime;
	}



	public void setTuesdayEndTime(String tuesdayEndTime) {
		this.tuesdayEndTime = tuesdayEndTime;
	}



	public String getWednesdayStartTime() {
		return wednesdayStartTime;
	}



	public void setWednesdayStartTime(String wednesdayStartTime) {
		this.wednesdayStartTime = wednesdayStartTime;
	}



	public String getWednesdayEndTime() {
		return wednesdayEndTime;
	}



	public void setWednesdayEndTime(String wednesdayEndTime) {
		this.wednesdayEndTime = wednesdayEndTime;
	}



	public String getThursdayStartTime() {
		return thursdayStartTime;
	}



	public void setThursdayStartTime(String thursdayStartTime) {
		this.thursdayStartTime = thursdayStartTime;
	}



	public String getThursdayEndTime() {
		return thursdayEndTime;
	}



	public void setThursdayEndTime(String thursdayEndTime) {
		this.thursdayEndTime = thursdayEndTime;
	}



	public String getFridayStartTime() {
		return fridayStartTime;
	}



	public void setFridayStartTime(String fridayStartTime) {
		this.fridayStartTime = fridayStartTime;
	}



	public String getFridayEndTime() {
		return fridayEndTime;
	}



	public void setFridayEndTime(String fridayEndTime) {
		this.fridayEndTime = fridayEndTime;
	}



	public String getSaturdayStartTime() {
		return saturdayStartTime;
	}



	public void setSaturdayStartTime(String saturdayStartTime) {
		this.saturdayStartTime = saturdayStartTime;
	}



	public String getSaturdayEndTime() {
		return saturdayEndTime;
	}



	public void setSaturdayEndTime(String saturdayEndTime) {
		this.saturdayEndTime = saturdayEndTime;
	}



	public String getSundayStartTime() {
		return sundayStartTime;
	}



	public void setSundayStartTime(String sundayStartTime) {
		this.sundayStartTime = sundayStartTime;
	}



	public String getSundayEndTime() {
		return sundayEndTime;
	}



	public void setSundayEndTime(String sundayEndTime) {
		this.sundayEndTime = sundayEndTime;
	}



	public String getProfilelId() {
		return profilelId;
	}



	public void setProfilelId(String profilelId) {
		this.profilelId = profilelId;
	}



	public String getLocationId() {
		return locationId;
	}



	public void setLocationId(String locationId) {
		this.locationId = locationId;
	}



	public String getInsExternalId() {
		return insExternalId;
	}



	public void setInsExternalId(String insExternalId) {
		this.insExternalId = insExternalId;
	}


 

}
