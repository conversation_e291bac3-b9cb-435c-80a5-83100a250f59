package com.guitarcenter.scheduler.dto;

import java.util.Set;

import org.apache.commons.lang.StringUtils;

public class CustomerDTO {

	private Long recordId;
	private String site;
	private String updatedBy;
	private String customerStatus;
	private String fullName;
	private String firstName;
	private String lastName;
	private String updated;
	private String email;
	private String phone;
	private String status;
	private String externalId;
	private String externalSource;
	private Set<InstrumentDTO> instruments;
	private String instrumentType;
	
	private String parentFullName;
	
		
	//Added for GSSP-220
   private String  location_external_id;
   //POS-808 Added LessonCount and LastBookedDate
   private String lessonCounts;
   private String lastBookedDate;
   
   private String contractDay;
   private String posUpdated;
   private String customerId;
   

   
   
   
		
		
		public String getLocation_external_id() {
			return location_external_id;
		}



		public void setLocation_external_id(String location_external_id) {
			this.location_external_id = location_external_id;
		}
	
	//Changes made for LES_19
	private String badgeNumber;
	

	public String getBadgeNumber() {
		return badgeNumber;
	}

	public void setBadgeNumber(String badgeNumber) {
		this.badgeNumber = badgeNumber;
	}

	public CustomerDTO() {
		super();
	}

	/**
	 * @return the instrumentType
	 */
	public String getInstrumentType() {
		return instrumentType;
	}

	/**
	 * @param instrumentType
	 *            the instrumentType to set
	 */
	public void setInstrumentType(String instrumentType) {
		this.instrumentType = instrumentType;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getExternalId() {
		return externalId;
	}

	public void setExternalId(String externalId) {
		this.externalId = externalId;
	}

	public String getExternalSource() {
		return externalSource;
	}

	public void setExternalSource(String externalSource) {
		this.externalSource = externalSource;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public Long getRecordId() {
		return recordId;
	}

	public void setRecordId(Long recordId) {
		this.recordId = recordId;
	}

	public String getSite() {
		return site;
	}

	public void setSite(String site) {
		this.site = site;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public String getCustomerStatus() {
		return customerStatus;
	}

	public void setCustomerStatus(String customerStatus) {
		this.customerStatus = customerStatus;
	}

	public String getUpdated() {
		return updated;
	}

	public void setUpdated(String updated) {
		this.updated = updated;
	}

	public String getFullName() {
		return fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public Set<InstrumentDTO> getInstruments() {
		return instruments;
	}

	public void setInstruments(Set<InstrumentDTO> instruments) {
		this.instruments = instruments;
	}

	public void setFullName(String firstName, String lastName) {
		if (StringUtils.isNotBlank(firstName)
				|| StringUtils.isNotBlank(lastName)) {
			StringBuilder buf = new StringBuilder();
			if (StringUtils.isNotBlank(firstName)) {
				buf.append(firstName);
			}
			if (StringUtils.isNotBlank(firstName)
					&& StringUtils.isNotBlank(lastName)) {
				buf.append(" ");
			}
			if (StringUtils.isNotBlank(lastName)) {
				buf.append(lastName);
			}
			setFullName(buf.toString());
		}
	}



	/**
	 * @return the lessonCount
	 */
	public String getLessonCounts() {
		return lessonCounts;
	}



	/**
	 * @param lessonCount the lessonCount to set
	 */
	public void setLessonCounts(String lessonCounts) {
		this.lessonCounts = lessonCounts;
	}



	/**
	 * @return the lastBookedDate
	 */
	public String getLastBookedDate() {
		return lastBookedDate;
	}



	/**
	 * @param lastBookedDate the lastBookedDate to set
	 */
	public void setLastBookedDate(String lastBookedDate) {
		this.lastBookedDate = lastBookedDate;
	}



	 

	public String getParentFullName() {
		return parentFullName;
	}



	public void setParentFullName(String parentFullName) {
		this.parentFullName = parentFullName;
	}


	public String getContractDay() {
		return contractDay;
	}



	public void setContractDay(String contractDay) {
		this.contractDay = contractDay;
	}



	public String getPosUpdated() {
		return posUpdated;
	}



	public void setPosUpdated(String posUpdated) {
		this.posUpdated = posUpdated;
	}



	public String getCustomerId() {
		return customerId;
	}



	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}



	@Override
	public String toString() {
		return "CustomerDTO [recordId=" + recordId + ", site=" + site + ", updatedBy=" + updatedBy + ", customerStatus="
				+ customerStatus + ", fullName=" + fullName + ", firstName=" + firstName + ", lastName=" + lastName
				+ ", updated=" + updated + ", email=" + email + ", phone=" + phone + ", status=" + status
				+ ", externalId=" + externalId + ", externalSource=" + externalSource + ", instruments=" + instruments
				+ ", instrumentType=" + instrumentType + ", parentFullName=" + parentFullName
				+ ", location_external_id=" + location_external_id + ", lessonCounts=" + lessonCounts
				+ ", lastBookedDate=" + lastBookedDate + ", contractDay=" + contractDay + ", posUpdated=" + posUpdated
				+ ", customerId=" + customerId + ", badgeNumber=" + badgeNumber + "]";
	}



 


}
