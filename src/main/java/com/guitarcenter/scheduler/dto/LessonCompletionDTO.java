package com.guitarcenter.scheduler.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * DTO representing lesson completion details.
 * Author: Appesh
 * Date: 2025-04-23
 */

public class LessonCompletionDTO {
	

	    @JsonProperty("AppointmentId")
	    private Long appointmentId;

	    @JsonProperty("LessonsType")
	    private String lessonsType;

	    @JsonProperty("Event")
	    private String event;

	    @JsonProperty("SFCustomerID")
	    private String sfCustomerID;

	    @JsonProperty("LessonsCompletedDate")
	    private String lessonsCompletedDate;

	    @JsonProperty("LessonsCompletedTime")
	    private String lessonsCompletedTime;

	    @JsonProperty("StoreNumber")
	    private String storeNumber;

	    @JsonProperty("StoreStreet")
	    private String storeStreet;

	    @JsonProperty("StoreCity")
	    private String storeCity;

	    @JsonProperty("StoreState")
	    private String storeState;

	    @JsonProperty("StoreZip")
	    private String storeZip;

	    @JsonProperty("LessonsDuration")
	    private String lessonsDuration;

	    @JsonProperty("LessonsFollowup")
	    private String lessonsFollowup;

	    @JsonProperty("LessonsFollowupName")
	    private String lessonsFollowupName;

	    @JsonProperty("LessonsFollowupEmail")
	    private String lessonsFollowupEmail;

	    @JsonProperty("LessonsFollowupPhone")
	    private String lessonsFollowupPhone;

	    @JsonProperty("LessonsFollowupZipCode")
	    private String lessonsFollowupZipCode;

	    @JsonProperty("Comments")
	    private String comments;

	    @JsonProperty("Updated")
	    private String updated;

	    @JsonProperty("Source")
	    private String source;
    	
    
    
	public Long getAppointmentId() {
		return appointmentId;
	}
	public void setAppointmentId(Long appointmentId) {
		this.appointmentId = appointmentId;
	}
	public String getLessonsType() {
		return lessonsType;
	}
	public void setLessonsType(String lessonsType) {
		this.lessonsType = lessonsType;
	}
	public String getEvent() {
		return event;
	}
	public void setEvent(String event) {
		this.event = event;
	}
	public String getSfCustomerID() {
		return sfCustomerID;
	}
	public void setSfCustomerID(String sfCustomerID) {
		this.sfCustomerID = sfCustomerID;
	}
	public String getLessonsCompletedDate() {
		return lessonsCompletedDate;
	}
	public void setLessonsCompletedDate(String lessonsCompletedDate) {
		this.lessonsCompletedDate = lessonsCompletedDate;
	}
	public String getLessonsCompletedTime() {
		return lessonsCompletedTime;
	}
	public void setLessonsCompletedTime(String lessonsCompletedTime) {
		this.lessonsCompletedTime = lessonsCompletedTime;
	}
	public String getStoreNumber() {
		return storeNumber;
	}
	public void setStoreNumber(String storeNumber) {
		this.storeNumber = storeNumber;
	}
	public String getStoreStreet() {
		return storeStreet;
	}
	public void setStoreStreet(String storeStreet) {
		this.storeStreet = storeStreet;
	}
	public String getStoreCity() {
		return storeCity;
	}
	public void setStoreCity(String storeCity) {
		this.storeCity = storeCity;
	}
	public String getStoreState() {
		return storeState;
	}
	public void setStoreState(String storeState) {
		this.storeState = storeState;
	}
	public String getStoreZip() {
		return storeZip;
	}
	public void setStoreZip(String storeZip) {
		this.storeZip = storeZip;
	}
	public String getLessonsDuration() {
		return lessonsDuration;
	}
	public void setLessonsDuration(String lessonsDuration) {
		this.lessonsDuration = lessonsDuration;
	}
	public String getLessonsFollowup() {
		return lessonsFollowup;
	}
	public void setLessonsFollowup(String lessonsFollowup) {
		this.lessonsFollowup = lessonsFollowup;
	}
	public String getLessonsFollowupName() {
		return lessonsFollowupName;
	}
	public void setLessonsFollowupName(String lessonsFollowupName) {
		this.lessonsFollowupName = lessonsFollowupName;
	}
	public String getLessonsFollowupEmail() {
		return lessonsFollowupEmail;
	}
	public void setLessonsFollowupEmail(String lessonsFollowupEmail) {
		this.lessonsFollowupEmail = lessonsFollowupEmail;
	}
	public String getLessonsFollowupPhone() {
		return lessonsFollowupPhone;
	}
	public void setLessonsFollowupPhone(String lessonsFollowupPhone) {
		this.lessonsFollowupPhone = lessonsFollowupPhone;
	}
	public String getLessonsFollowupZipCode() {
		return lessonsFollowupZipCode;
	}
	public void setLessonsFollowupZipCode(String lessonsFollowupZipCode) {
		this.lessonsFollowupZipCode = lessonsFollowupZipCode;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	
	
	public LessonCompletionDTO() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	
	public String getUpdated() {
		return updated;
	}
	public void setUpdated(String updated) {
		this.updated = updated;
	}
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
	public LessonCompletionDTO(String lessonsType, String event, String sfCustomerID, String lessonsCompletedDate,
			String lessonsCompletedTime, String storeNumber, String storeStreet, String storeCity, String storeState,
			String storeZip, String lessonsDuration, String lessonsFollowup, String lessonsFollowupName,
			String lessonsFollowupEmail, String lessonsFollowupPhone, String lessonsFollowupZipCode, String comments) {
		super();
		this.lessonsType = lessonsType;
		this.event = event;
		this.sfCustomerID = sfCustomerID;
		this.lessonsCompletedDate = lessonsCompletedDate;
		this.lessonsCompletedTime = lessonsCompletedTime;
		this.storeNumber = storeNumber;
		this.storeStreet = storeStreet;
		this.storeCity = storeCity;
		this.storeState = storeState;
		this.storeZip = storeZip;
		this.lessonsDuration = lessonsDuration;
		this.lessonsFollowup = lessonsFollowup;
		this.lessonsFollowupName = lessonsFollowupName;
		this.lessonsFollowupEmail = lessonsFollowupEmail;
		this.lessonsFollowupPhone = lessonsFollowupPhone;
		this.lessonsFollowupZipCode = lessonsFollowupZipCode;
		this.comments = comments;
	}
    
    
}

