package com.guitarcenter.scheduler.dto;

import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * Simple value-object for Instrument.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

public class InstrumentDTO {
    private Long instrumentId;
    private String externalId;
    private String instrumentName;
    
    public Long getInstrumentId() {
        return instrumentId;
    }
    
    public void setInstrumentId(Long instrumentId) {
        this.instrumentId = instrumentId;
    }
    
    public String getExternalId() {
        return externalId;
    }
    
    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }
    
    public String getInstrumentName() {
        return instrumentName;
    }
    
    public void setInstrumentName(String instrumentName) {
        this.instrumentName = instrumentName;
    }
    
    public String toString() {
        return new ToStringBuilder(this)
            .append("instrumentId", getInstrumentId())
            .append("externalId", getExternalId())
            .append("instrumentName", getInstrumentName())
            .toString();
    }
}
