package com.guitarcenter.scheduler.elesticsearch;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Transient;

import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.guitarcenter.scheduler.common.util.ElasticConnecation;
import com.guitarcenter.scheduler.webservice.dto.InstructorAVLServiceResponseDTO;

public class InstructorAvailablityElasticUploader {
	
	
	@Transient
    Logger log = LoggerFactory.getLogger(InstructorAvailablityElasticUploader.class);
	
	/*@Value("${adp.elasticIndex}")
    private String elasticIndex;
	
	@Value("${adp.accessKeyId}")
	private String accessKeyId;


	@Value("${adp.secretKey}")
	private String secretKey;*/
	

	private static final Logger LOGGER = LoggerFactory.getLogger(InstructorAvailablityElasticUploader.class);
	
	ElasticConnecation ec = null;

	/*
	 * private static String serviceName = "es"; private static String region =
	 * "us-west-2"; private static String aesEndpoint =
	 * "https://search-gc-es-487-prod-rhanh3ea25k5getrxi3c3ltqba.us-west-2.es.amazonaws.com";
	 * // e.g. // https://search-mydomain.uswest-1.es.amazonaws.com
	 * 
	 * static final AWSCredentialsProvider credentialsProvider = new
	 * DefaultAWSCredentialsProviderChain();
	 */
	/*
	 * public static void main(String[] args) throws Exception {
	 * elasticInsertOrUpdate("{test4}"); }
	 */

	public  void elasticInsertOrUpdate(List<InstructorAVLServiceResponseDTO> result, String env,Map<String, String> elasticConnec,String jobName) throws SchedulerException  {
		
		
		if (log.isDebugEnabled()) {
			log.debug("InstructorAvailablityElasticUploader , elasticInsertOrUpdate");
			
		}
		LOGGER.error("elasticInsertOrUpdate  start"+jobName);
		
		//System.out.println("InstructorsElasticUploader:main() - Enter");
		
		String secretKey =elasticConnec.get("secretKey");
		String accessKeyId =elasticConnec.get("accessKeyId");
		String elasticIndex =elasticConnec.get("elasticIndex");
		
		
		/*String elasticIndex = null;
		
    	if(null != env && env.equalsIgnoreCase("Production")) {
			//System.out.println("Production env ");
			System.setProperty("aws.accessKeyId", "********************");
			System.setProperty("aws.secretKey", "qHd0Si2hoC91NdUhenwMfFPXhdZVvl8FMjd3mClK");
			elasticIndex = "lpp_instructor_master_prod";
			
		}else {
			//System.out.println("QA or local");
			System.setProperty("aws.accessKeyId", "********************");
			System.setProperty("aws.secretKey", "qHd0Si2hoC91NdUhenwMfFPXhdZVvl8FMjd3mClK");
			elasticIndex = "lpp_instructor_master_qa";
		}*/
		
		
    	 log.error("elasticIndexMAAInstAvailability elasticIndex = = "+elasticIndex);

    	 System.setProperty("aws.accessKeyId", accessKeyId);
		 System.setProperty("aws.secretKey", secretKey);
		//--  elasticIndex = "lpp_instructor_master_prod";
    	 
/*    	 System.setProperty("aws.accessKeyId", "********************");
			System.setProperty("aws.secretKey", "qHd0Si2hoC91NdUhenwMfFPXhdZVvl8FMjd3mClK");
			elasticIndex = "lpp_instructor_master_qa";*/
 		
		
		//RestHighLevelClient client = esClient(serviceName, region);
		//RestHighLevelClient client  = createSimpleElasticClient(serviceName, region);
		  ec = new ElasticConnecation();
		RestHighLevelClient client  = ec.getConnection(elasticConnec);
		/*
		 * try { client = esClientDisableSSL(serviceName, region); } catch
		 * (KeyManagementException | NoSuchAlgorithmException | KeyStoreException e1) {
		 * log.error(e1.toString()); }
		 */
		log.error("RestHighLevelClient  Connected");
		try {

			BulkRequest bulkRequest = new BulkRequest();
			if(result!=null)LOGGER.error("result before for loop"+result.size()); 
			for(InstructorAVLServiceResponseDTO obj:result) {
				if(null != obj) {
					Map<String, Object> storeDetailsMap = new HashMap<>();
					 ObjectMapper Obj = new ObjectMapper();
				      
					String jsonStr = Obj.writeValueAsString(obj);
					storeDetailsMap.put(obj.getId(), jsonStr);
					List<Object> resultList = new ArrayList<Object>();
					resultList.add(jsonStr);
					//if(2>1)throw new SchedulerException();
					IndexRequest indexRequest = new IndexRequest(elasticIndex).id("gc_" + obj.getId() +"_"+ obj.getStoreNumber())
                            .source(jsonStr,XContentType.JSON);
 
                    bulkRequest.add(new UpdateRequest(elasticIndex, "gc_" + obj.getId() +"_"+ obj.getStoreNumber()).doc(jsonStr,XContentType.JSON)
                            .upsert(indexRequest));
				}
			}

			LOGGER.error("result After for loop numberOfActions "+bulkRequest.numberOfActions());
			LOGGER.error("result client hashCode "+client.hashCode());
			BulkResponse bulkResponse = client.bulk(bulkRequest, RequestOptions.DEFAULT);
			

			LOGGER.error("bulkResponse.status() : "+new Timestamp(System.currentTimeMillis())+bulkResponse.status());
			 log.info("bulkResponse.status(): "+new Timestamp(System.currentTimeMillis())+bulkResponse.status());
			  //client.close();
			/*if (null != bulkRequest.requests()) {
				
				log.error("InstructorsElasticUploader:main() - Uploading the Pending Ones --> " + bulkRequest.requests().size());
				BulkResponse bulkResponseresponse = client.bulk(bulkRequest, RequestOptions.DEFAULT);
				 log.error("bulkResponseresponse.status():  - "+new Timestamp(System.currentTimeMillis())+":"+bulkResponseresponse.status());
			
			}*/

		} catch (Exception e) {
			LOGGER.error("elasticInsertOrUpdate  Excption "+e.getMessage());
			LOGGER.error(jobName+" elasticInsertOrUpdate Excption  Time   ="+new Timestamp(System.currentTimeMillis()));
			e.printStackTrace();
			try {
				client.close();
			} catch (IOException e1) {
				e1.printStackTrace();
			}
			throw new SchedulerException();
		} finally{
			try {
				client.close();
				//client = null;
			} catch (IOException e1) {
				e1.printStackTrace();
			}
			
		}

		//System.out.println("InstructorsElasticUploader:main() - Exit");
	}

	/*
	 * public static RestHighLevelClient esClientDisableSSL(String serviceName,
	 * String region) throws NoSuchAlgorithmException, KeyStoreException,
	 * KeyManagementException {
	 * 
	 * SSLContextBuilder sslBuilder = SSLContexts.custom() .loadTrustMaterial(null,
	 * (x509Certificates, s) -> true); final SSLContext sslContext =
	 * sslBuilder.build();
	 * 
	 * AWS4Signer signer = new AWS4Signer(); signer.setServiceName(serviceName);
	 * signer.setRegionName(region); HttpRequestInterceptor interceptor = new
	 * AWSRequestSigningApacheInterceptor(serviceName, signer, credentialsProvider);
	 * 
	 * return new
	 * RestHighLevelClient(RestClient.builder(HttpHost.create(aesEndpoint))
	 * .setHttpClientConfigCallback(hacb ->
	 * hacb.addInterceptorLast(interceptor).setSSLContext(sslContext).
	 * setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)));
	 * 
	 * 
	 * }
	 */

	
}
