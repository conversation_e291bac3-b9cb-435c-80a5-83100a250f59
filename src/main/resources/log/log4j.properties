log4j.rootLogger = WARN, schedulerlog

log4j.category.reportsLogger=DEBUG, reportsLog
log4j.additivity.reportsLogger=false

# Do not use console in production
#console is set to be a ConsoleAppender
#log4j.appender.console = org.apache.log4j.ConsoleAppender
#log4j.appender.console.layout = org.apache.log4j.PatternLayout
#log4j.appender.console.layout.ConversionPattern = %-d{yyyy-MM-dd HH:mm:ss} [%c]-[%p] %m%n

#file is set to output to a rolling appender
# - keep up to 10 old copies
# - limit to 50Mb
# NOTE: not using daily appender so that the system will automatically deal with
# log files. Client can change if they need something more robust
log4j.appender.schedulerlog = org.apache.log4j.RollingFileAppender
log4j.appender.schedulerlog.File = ${catalina.base}/logs/scheduler.log
log4j.appender.schedulerlog.maxBackupIndex=10
log4j.appender.schedulerlog.maxFileSize=50MB
log4j.appender.schedulerlog.layout = org.apache.log4j.PatternLayout
log4j.appender.schedulerlog.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss} [%c]-[%p] - %m%n

