<?xml version="1.0" encoding="UTF-8"?>
<!-- Definitions of common beans $Id: $ -->
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jee="http://www.springframework.org/schema/jee"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
                      http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
                      http://www.springframework.org/schema/jee
                      http://www.springframework.org/schema/jee/spring-jee-3.2.xsd">

	<!-- Datasource to use is defined by Tomcat -->
	<jee:jndi-lookup id="schedulerDB" jndi-name="java:comp/env/jdbc/schedulerDB"
		resource-ref="true" />

	<!-- Common hibernate properties; override in a profile for local changes -->
	<bean id="hibernateProperties"
		class="org.springframework.beans.factory.config.PropertiesFactoryBean">
		<property name="properties">
			<props>
				<prop key="hibernate.dialect">org.hibernate.dialect.Oracle10gDialect</prop>
				<prop key="hibernate.show_sql">false</prop>
				<prop key="hibernate.connection.autocommit">false</prop>
				<prop key="hibernate.current_session_context_class">org.springframework.orm.hibernate4.SpringSessionContext
				</prop>
				<prop key="hibernate.cache.use_second_level_cache">true</prop>
				<prop key="hibernate.cache.use_query_cache">true</prop>
				<prop key="hibernate.cache.region.factory_class">org.hibernate.cache.ehcache.EhCacheRegionFactory
				</prop>
			</props>
		</property>
	</bean>

	<!-- URL for connection to Solr server is defined in container JNDI -->
	<bean id="solrServer" class="org.apache.solr.client.solrj.impl.CloudSolrServer"
		destroy-method="shutdown">
		<constructor-arg>
			<jee:jndi-lookup jndi-name="java:comp/env/solrURL" />
		</constructor-arg>
		<property name="defaultCollection" value="collection1" />
	</bean>

	<!-- Add beans for email function -->
	<bean id="freeMarker"
		class="org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer">
		<property name="templateLoaderPath" value="classpath:mailtemplates" />
		<property name="freemarkerSettings">
			<props>
				<prop key="template_update_delay">0</prop>
				<prop key="default_encoding">UTF-8</prop>
				<prop key="locale">en_US</prop>
			</props>
		</property>
	</bean>



	<bean id="mailSender" class="org.springframework.mail.javamail.JavaMailSenderImpl">
		<property name="session">
			<jee:jndi-lookup id="javaMail" jndi-name="java:comp/env/mail/session" />
		</property>
	</bean>

	<bean id="emailJobtask"
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass"
			value="com.guitarcenter.scheduler.jobs.ReminderEmailJob" />
		<property name="durability" value="true" />
	</bean>

	<bean id="emailScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail">
			<ref bean="emailJobtask" />
		</property>
		<property name="cronExpression" value="0 0 3 * * ?" />
	</bean>
	
<!-- 	OLL-3732  -->
	
	<bean id="completedLessonsJobtask"
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass"
			value="com.guitarcenter.scheduler.jobs.CompletedLessonsJob" />
		<property name="durability" value="true" />
	</bean>

	<bean id="completedLessonsScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail">
			<ref bean="completedLessonsJobtask" />
		</property>
		<property name="cronExpression" value="0 */2 * ? * *" />
	</bean>
	
	
	
	<bean id="cRMAppointmentDataFileJob" 
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass"
			value="com.guitarcenter.scheduler.jobs.CRMAppointmentDataFileJob" />
		<property name="durability" value="true" />
	</bean>
	<bean id="cRMAppointmentDataFileScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail">
			<ref bean="cRMAppointmentDataFileJob" />
		</property>
		<property name="cronExpression" value="0 0 22 L * ?" />
	</bean>
	
	
	<bean id="cancelHoldAppointmentsJob" 
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass"
			value="com.guitarcenter.scheduler.jobs.CancelHoldAppointmentsJob" />
		<property name="durability" value="true" />
	</bean>
	<bean id="cancelHoldAppointmentsJobScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail">
			<ref bean="cancelHoldAppointmentsJob" />
		</property>
		<property name="cronExpression" value="0 0 22 L * ?" />
	
</bean>

	<bean id="masterScheduleReportJob" 
			class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
			<property name="jobClass"
				value="com.guitarcenter.scheduler.jobs.MasterScheduleReportJob" />
			<property name="durability" value="true" />
		</bean>
		<bean id="masterScheduleReportJobScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
			<property name="jobDetail">
				<ref bean="masterScheduleReportJob" />
			</property>
			<property name="cronExpression" value="0 0 22 L * ?" />
		
	</bean>
	
		<bean id="deleteDayforceInstructorApptRecordsJob" 
			class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
			<property name="jobClass"
				value="com.guitarcenter.scheduler.jobs.DeleteDayforceInstructorApptRecordsJob" />
			<property name="durability" value="true" />
		</bean>
		<bean id="deleteDayforceInstructorApptRecordsJobScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
			<property name="jobDetail">
				<ref bean="deleteDayforceInstructorApptRecordsJob" />
			</property>
			<property name="cronExpression" value="0 0 22 L * ?" />
		
	</bean>
		   
	 <bean id="getInstructorAvailabiltyJob" 
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass"
			value="com.guitarcenter.scheduler.jobs.GetInstructorAvailabiltyJob" />
		<property name="durability" value="true" />
	</bean>
	<bean id="getInstructorAvailabiltyJobScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail">
			<ref bean="getInstructorAvailabiltyJob" />
		</property>
		<property name="cronExpression" value="0 */2 * ? * *" />
	</bean>
	
	 <bean id="getInstructorFullAvailabiltyJob"
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass"
			value="com.guitarcenter.scheduler.jobs.GetInstructorFullAvailabiltyJob" />
		<property name="durability" value="true" />
	</bean>
	<bean id="getInstructorFullAvailabiltyJobScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail">
			<ref bean="getInstructorFullAvailabiltyJob" />
		</property>
		<property name="cronExpression" value="0 0 22 L * ?" />
	</bean>
	
	<bean id="disabledInstructorsGetAvailabiltyJob" 
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass"
			value="com.guitarcenter.scheduler.jobs.DisabledInstructorsGetAvailabiltyJob" />
		<property name="durability" value="true" />
	</bean>
	<bean id="disabledInstructorsGetAvailabiltyJobScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail">
			<ref bean="disabledInstructorsGetAvailabiltyJob" />
		</property>
		<property name="cronExpression" value="0 0 22 L * ?" />
	</bean>
	
	<bean id="dataBackUpMonthlyJob" 
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass"
			value="com.guitarcenter.scheduler.jobs.DataBackUpMonthlyJob" />
		<property name="durability" value="true" />
	</bean>
	<bean id="dataBackUpMonthlyJobScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail">
			<ref bean="dataBackUpMonthlyJob" />
		</property>
		<property name="cronExpression" value="0 0 22 L * ?" />
	</bean>
	
	
	<bean id="edwJob" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
            <property name="jobClass" value="com.guitarcenter.scheduler.jobs.EDWFileJob"/>
            <property name="durability" value="true"/>
        </bean>
        <bean id="edwJobScheduler"
              class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
           <property name="jobDetail" ref="edwJob"/>
           <property name="cronExpression" value="0 0 22 L * ?"/>
        </bean> 
		<!-- Add beans for ADP job -->
	 <bean id="adpJob" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
            <property name="jobClass" value="com.guitarcenter.scheduler.jobs.ADPFileJob"/>
            <property name="durability" value="true"/>
        </bean> 
           <bean id="adpJobScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
           <property name="jobDetail" ref ="adpJob" />
           <property name="cronExpression" value="0 0 22 L * ?"/>
        </bean>  
        <!--  GSSP-272 Instructor Floor Time job -->
        <bean id="instructorFloorTimeJob"
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass" value="com.guitarcenter.scheduler.jobs.InstructorFloorTimeJob" />
		<property name="durability" value="true" />
	    </bean>
	    
		<bean id="instructorFloorTimeJobScheduler"
		class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
			<property name="jobDetail" ref="instructorFloorTimeJob" />
			<property name="cronExpression" value="0 0 22 L * ?" />
		</bean> 
		
		<!--  GSSP-298 Instructor Activity And Availability job -->
        <bean id="instructorActivityAndAvailabilityJob"
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass" value="com.guitarcenter.scheduler.jobs.InstructorActivityAndAvailabilityJob" />
		<property name="durability" value="true" />
	    </bean>
	    
		<bean id="instructorActivityAndAvailabilityJobScheduler"
		class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
			<property name="jobDetail" ref="instructorActivityAndAvailabilityJob" />
			<property name="cronExpression" value="0 0 22 L * ?" />
		</bean> 
		
	<!-- Schedule daily processing of lacking appointment -->
	<bean id="lackingAppointmentJob"
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass" value="com.guitarcenter.scheduler.jobs.LackingAppointmentJob" />
		<property name="durability" value="true" />
	</bean>
	
	<!-- GSSP-336 change the lack appointment job timing to run every day at 3 AM   -->
	<bean id="lackingAppointmentJobScheduler"
		class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail" ref="lackingAppointmentJob" />
		<property name="cronExpression" value="0 0 3 * * ?" />
	</bean>
	
	<!-- Weekly job to generate InstructorActivities report - GSSP-240 -->
	<bean id="instructorActivitiesEmailJob"
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass" value="com.guitarcenter.scheduler.jobs.InstructorActivitiesEmailJob" />
		<property name="durability" value="true" />
	</bean>

	<!-- GSSP-240 - Job to run every Monday at 5 AM -->
	<bean id="instructorActivitiesJobScheduler"
			class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail">
			<ref bean="instructorActivitiesEmailJob" />
		</property>
		<property name="cronExpression" value="0 0 5 * * ?" />
	</bean>
	
	
		<bean id="dailySubscriptionReportEmailJob"
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass" value="com.guitarcenter.scheduler.jobs.DailySubscriptionReportEmailJob" />
		<property name="durability" value="true" />
	</bean>

	<!-- GSSP-240 - Job to run every Monday at 5 AM -->
	<bean id="dailySubscriptionReportJobScheduler"
			class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail">
			<ref bean="dailySubscriptionReportEmailJob" />
		</property>
		<property name="cronExpression" value="0 0 22 L * ?" />
	</bean>
	

	
	<!--  Changes made for GSSP-238 -->

	<bean id="conflictingAppointmentJob"
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass" value="com.guitarcenter.scheduler.jobs.ConflictingAppointmentJob" />
		<property name="durability" value="true" />
	</bean>
	

	
	<bean id="conflictingAppointmentJobScheduler"
		class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail" ref="conflictingAppointmentJob" />
		<property name="cronExpression" value="0 0 22 L * ?" />
	</bean>

	<!--  End of Changes made for GSSP-238 -->	
	
	
	<!--For GSSP-158  -->
	
		<bean id="easternTimeZone" class="java.util.TimeZone" factory-method="getTimeZone">
			<constructor-arg value="America/New_York"/>
		</bean>
		<bean id="centeralTimeZone" class="java.util.TimeZone" factory-method="getTimeZone">
			<constructor-arg value="America/Chicago"/>
		</bean>
		<bean id="mountTimeZone" class="java.util.TimeZone" factory-method="getTimeZone">
			<constructor-arg value="America/Phoenix"/>
		</bean>
		<bean id="pacificTimeZone" class="java.util.TimeZone" factory-method="getTimeZone">
			<constructor-arg value="America/Los_Angeles"/>
		</bean>
		
		<bean id="timeZoneHawai" class="java.util.TimeZone" factory-method="getTimeZone">
			<constructor-arg value="Pacific/Honolulu"/>
		</bean>
		<bean id="timeZoneAlaska" class="java.util.TimeZone" factory-method="getTimeZone">
			<constructor-arg value="America/Anchorage"/>
		</bean>
		<!--For America/Denver time zone added  -->
		<bean id="timeZoneArizona" class="java.util.TimeZone" factory-method="getTimeZone">
			<constructor-arg value="America/Denver"/>
		</bean>
		
	<bean id="instructorAppointmentJob"
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass" value="com.guitarcenter.scheduler.jobs.InstructorAppointmentJob" />
		<property name="durability" value="true" />
	</bean>
	
	 <bean id="instructorScheduleETJobScheduler"
		class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail" ref="instructorAppointmentJob" />
		<property name="cronExpression" value="0 0 20 * * ?" />
		<property name="timeZone"  ref="easternTimeZone"></property>
	</bean>
	
	 <bean id="instructorScheduleCTJobScheduler"
		class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail" ref="instructorAppointmentJob" />
		<property name="cronExpression" value="0 0 20 * * ?" />
		<property name="timeZone"  ref="centeralTimeZone"></property>
	</bean>
	
	 <bean id="instructorScheduleMTJobScheduler"
		class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail" ref="instructorAppointmentJob" />
		<property name="cronExpression" value="0 0 20 * * ?" />
		<property name="timeZone"  ref="mountTimeZone"></property>
	</bean>
	
	 <bean id="instructorSchedulePTJobScheduler"
		class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail" ref="instructorAppointmentJob" />
		<property name="cronExpression" value="0 0 20 * * ?" />
		<property name="timeZone"  ref="pacificTimeZone"></property>
	</bean>
	
	<bean id="instructorScheduleHNTJobScheduler"
		class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail" ref="instructorAppointmentJob" />
		<property name="cronExpression" value="0 0 20 * * ?" />
		<property name="timeZone"  ref="timeZoneHawai"></property>
	</bean>
	
	<bean id="instructorScheduleATJobScheduler"
		class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail" ref="instructorAppointmentJob" />
		<property name="cronExpression" value="0 0 20 * * ?" />
		<property name="timeZone"  ref="timeZoneAlaska"></property>
	</bean>
	
	<!--For America/Denver time zone added  -->
	<bean id="instructorScheduleMDTJobScheduler"
		class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail" ref="instructorAppointmentJob" />
		<property name="cronExpression" value="0 0 20 * * ?" />
		<property name="timeZone"  ref="timeZoneArizona"></property>
	</bean>
	 
	
	<!--  End of Changes made for GSSP-158 -->
	
	<!-- Add for GSSP-172  -  Notification for Scheduler Batch Job Success and Failure-->
	<bean id="jobNotificationEmailService"
		class="com.guitarcenter.scheduler.service.JobNotificationEmailServiceImpl">		
	</bean>
	
	<bean id="quartz" lazy-init="false" autowire="no"
		class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
		<property name="triggers">
			<list>
				<ref bean="getInstructorAvailabiltyJobScheduler" />

			<!--	<ref bean="completedLessonsScheduler" />-->

				<!-- <ref bean="masterScheduleReportJobScheduler" /> -->

		 <!--   <ref bean="dailySubscriptionReportJobScheduler" />  --> 
			<!-- <ref bean="instructorActivitiesJobScheduler" /> -->
			
			<!-- <ref bean="deleteDayforceInstructorApptRecordsJobScheduler" />  -->
			<!--  <ref bean="getInstructorFullAvailabiltyJobScheduler" />  -->
			  <!--  <ref bean="masterScheduleReportJobScheduler" /> -->   
			<!-- <ref bean="disabledInstructorsGetAvailabiltyJobScheduler" />  
			 
			<ref bean="getInstructorAvailabiltyJobScheduler" /> 
			 			
		 	<ref bean="cancelHoldAppointmentsJobScheduler" />  
		 	
			   <ref bean="emailScheduler" />
				<ref bean="lackingAppointmentJobScheduler" />
                <ref bean="cRMAppointmentDataFileScheduler" /> 
                <ref bean="dataBackUpMonthlyJobScheduler" />  
                 
				<ref bean="conflictingAppointmentJobScheduler" />
			 
				<ref bean="instructorScheduleETJobScheduler" />
				<ref bean="instructorScheduleCTJobScheduler" />
				<ref bean="instructorScheduleMTJobScheduler" />
				<ref bean="instructorSchedulePTJobScheduler" />
				<ref bean="instructorScheduleHNTJobScheduler" />
				<ref bean="instructorScheduleATJobScheduler" />
				 
				<ref bean="instructorScheduleMDTJobScheduler" />
				 
				<ref bean="instructorActivitiesJobScheduler" />
				 
			    <ref bean="adpJobScheduler" /> 
				
			    <ref bean="edwJobScheduler" /> 
			 
				<ref bean="instructorFloorTimeJobScheduler" />
		 
				<ref bean="instructorActivityAndAvailabilityJobScheduler"/>  -->  
				
				
			</list>
		</property>		
		<property name="schedulerContextAsMap">
			<map>
		 
			<entry key="dailySubscriptionReportService" value-ref="dailySubscriptionReportService" />
			<entry key="deleteDayforceInstructorApptRecordsService" value-ref="deleteDayforceInstructorApptRecordsService" />
			<entry key="getInstructorFullAvailabiltyService" value-ref="getInstructorFullAvailabiltyService" />
			<entry key="masterScheduleReportService" value-ref="masterScheduleReportService" />
				<entry key="appointmentEmailService" value-ref="appointmentEmailService" />
				<entry key="lackingAppointmentService" value-ref="lackingAppointmentService" />
				<entry key="cRMAppointmentDataFileService" value-ref="cRMAppointmentDataFileService" />
				<entry key="cancelHoldAppointmentsService" value-ref="cancelHoldAppointmentsService" />
				
				<entry key="dataBackUpMonthlyService" value-ref="dataBackUpMonthlyService" />
				<!--  Changes made for GSSP-238 -->
				<entry key="reportService" value-ref="reportService" />
				<entry key="getInstructorAvailabiltyService" value-ref="getInstructorAvailabiltyService" />
				
				
                     <!-- GSSP-240 -->
                    <entry key="instructorActivitiesService" value-ref="instructorActivitiesService" />
                    <!-- GSSP-298 -->
                <entry key="instructorActivityAndAvailabitityService" value-ref="instructorActivityAndAvailabitityService" />
				
			 
				<!-- Add for GSSP-172  -  Notification for Scheduler Batch Job Success and Failure-->
				 
				<entry key="jobNotificationEmailService" value-ref="jobNotificationEmailService" />
			</map>
		</property>	
	
	</bean>
	
	
	
	<!-- GSSP-228 Autowire added -->
	<bean id="availabilityUtil"
		class="com.guitarcenter.scheduler.common.util.AvailabilityUtil">	
	</bean>
	
	<bean id="activityAndServiceUtil"
		class="com.guitarcenter.scheduler.common.util.ActivityAndServiceUtil">	
	</bean>
	
	<bean id="profileService"
		class="com.guitarcenter.scheduler.model.ProfileService">	
	</bean>
	
	<bean id="profileActivity"
		class="com.guitarcenter.scheduler.model.ProfileActivity">	
	</bean>
	


</beans>
