<?xml version="1.0" encoding="UTF-8"?>
<!-- Initialisation file for Spring framework application context $Id: $ -->
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:jee="http://www.springframework.org/schema/jee"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
                        http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
                        http://www.springframework.org/schema/context
                        http://www.springframework.org/schema/context/spring-context-3.2.xsd">
        
    <!-- Bean definitions common to all environments -->
    <import resource="beans.xml"/>
    
	<!-- Spring security configuration file: keep all security configuration 
		here -->
	<import resource="security.xml" />

	<!-- Bring in service definitions -->
	<import resource="service.xml" />

	<!-- External beans and services; all defined in integrations package.
	     Could logically be part of service.xml but separated to avoid
	     conflicts as integration work happens in parallel with main
	     development.
	-->
	<import resource="integration.xml" />

    <!-- production configuration environment -->
    <import resource="production.xml"/>
    
    <!-- development configuration environment -->
    <import resource="develop.xml"/>
	
	<!-- development configuration environment -->
    <import resource="local.xml"/>

</beans>