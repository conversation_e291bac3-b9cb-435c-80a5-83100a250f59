<?xml version="1.0" encoding="UTF-8"?>
<!-- Initialisation file for Spring framework application context $Id: $ -->
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:sec="http://www.springframework.org/schema/security"
	xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
                        http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
                        http://www.springframework.org/schema/context
                        http://www.springframework.org/schema/context/spring-context-3.2.xsd
                        http://www.springframework.org/schema/security
                        http://www.springframework.org/schema/security/spring-security-3.1.xsd
                        http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
						http://www.springframework.org/schema/util
						http://www.springframework.org/schema/util/spring-util-3.2.xsd">

	<beans profile="production">
	   <!-- It is getting more and more complex to try to manage the
            per-environment properties just in JNDI, so add a mechanism to
            allow properties to be defined in Tomcat configuration directory on
            a server by server basis as necessary.
            
            Multiple files can be added to location if comma separated.
        -->
		<!--  GSSP-275 changes-->
        <context:property-placeholder
            ignore-resource-not-found="true"
            ignore-unresolvable="true"
            order="1"
            location="file:${catalina.base:.}/conf/edw.properties,file:${catalina.base:.}/conf/adp.properties,file:${catalina.base:.}/conf/pos.properties,file:${catalina.base:.}/conf/auth.properties,file:${catalina.base:.}/conf/mail.properties,file:${catalina.base:.}/conf/crm.properties"/>
            
 <!--<context:property-placeholder
            ignore-resource-not-found="true"
            ignore-unresolvable="true"
            order="2"
            location="file:${catalina.base:.}/conf/adp.properties"/>-->
        
        <!-- Production hibernate and ehcache properties -->
        <bean id="hibernateProperties"
              class="org.springframework.beans.factory.config.PropertiesFactoryBean">
            <property name="properties">
                <props>
                    <prop key="hibernate.dialect">org.hibernate.dialect.Oracle10gDialect</prop>
                    <prop key="hibernate.show_sql">false</prop>
                    <prop key="hibernate.connection.autocommit">false</prop>
                    <prop key="hibernate.current_session_context_class">org.springframework.orm.hibernate4.SpringSessionContext</prop>
                    <prop key="hibernate.cache.use_second_level_cache">true</prop>
                    <prop key="hibernate.cache.use_query_cache">true</prop>
                    <prop key="hibernate.cache.region.factory_class">org.hibernate.cache.ehcache.EhCacheRegionFactory</prop>
                    <prop key="net.sf.ehcache.configurationResourceName">ehcache/prod-ehcache.xml</prop>
                </props>
            </property>
        </bean>
        
		<!-- Schedule daily processing of EDW files -->
        <bean id="edwJob" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
            <property name="jobClass" value="com.guitarcenter.scheduler.jobs.EDWFileJob"/>
            <property name="durability" value="true"/>
        </bean>
        <bean id="edwJobScheduler"
              class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
           <property name="jobDetail" ref="edwJob"/>
           <property name="cronExpression" value="${edw.cron:0 0 6 * * ?}"/>
        </bean>
    <bean id="cRMAppointmentDataFileJob"
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass"
			value="com.guitarcenter.scheduler.jobs.CRMAppointmentDataFileJob" />
		<property name="durability" value="true" />
	</bean>
    <bean id="cRMAppointmentDataFileScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail">
			<ref bean="cRMAppointmentDataFileJob" />
		</property>
		<property name="cronExpression" value="${edw.cron:0 45 00 * * ?}"/>
	</bean>
	
	
	<bean id="cancelHoldAppointmentsJob" 
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass"
			value="com.guitarcenter.scheduler.jobs.CancelHoldAppointmentsJob" />
		<property name="durability" value="true" />
	</bean>
	<bean id="cancelHoldAppointmentsJobScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail">
			<ref bean="cancelHoldAppointmentsJob" />
		</property>
		<property name="cronExpression" value="${edw.cron:0 55 00 * * ?}"/>
	</bean>

	<bean id="masterScheduleReportJob" 
			class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
			<property name="jobClass"
				value="com.guitarcenter.scheduler.jobs.MasterScheduleReportJob" />
			<property name="durability" value="true" />
		</bean>
		<bean id="masterScheduleReportJobScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
			<property name="jobDetail">
				<ref bean="masterScheduleReportJob" />
			</property>
			<property name="cronExpression" value="${adp.cron:0 0 3 * * ?}"/>
		
	</bean>
	
	 <bean id="dailySubscriptionReportEmailJob"
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass" value="com.guitarcenter.scheduler.jobs.DailySubscriptionReportEmailJob" />
		<property name="durability" value="true" />
	</bean>

	<!-- GSSP-240 - Job to run every Monday at 5 AM -->
	<bean id="dailySubscriptionReportJobScheduler"
			class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail">
			<ref bean="dailySubscriptionReportEmailJob" />
		</property>
		<property name="cronExpression" value="${adp.cron:0 0 2 * * ?}"/>
	</bean>
	
	 <bean id="deleteDayforceInstructorApptRecordsJob" 
			class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
			<property name="jobClass"
				value="com.guitarcenter.scheduler.jobs.DeleteDayforceInstructorApptRecordsJob" />
			<property name="durability" value="true" />
		</bean>
		<bean id="deleteDayforceInstructorApptRecordsJobScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
			<property name="jobDetail">
				<ref bean="deleteDayforceInstructorApptRecordsJob" />
			</property>
			<property name="cronExpression" value="${adp.cron:0 0 2 * * ?}"/>
		
	</bean>	
	 <bean id="getInstructorAvailabiltyJob" 
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass"
			value="com.guitarcenter.scheduler.jobs.GetInstructorAvailabiltyJob" />
		<property name="durability" value="true" />
	</bean>
	<bean id="getInstructorAvailabiltyJobScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail">
			<ref bean="getInstructorAvailabiltyJob" />
		</property>
		<property name="cronExpression" value="${edw.cron:0 */5 * ? * *}"/>
	</bean>
	
		 <bean id="getInstructorFullAvailabiltyJob" 
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass"
			value="com.guitarcenter.scheduler.jobs.GetInstructorFullAvailabiltyJob" />
		<property name="durability" value="true" />
	</bean>
	<bean id="getInstructorFullAvailabiltyJobScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail">
			<ref bean="getInstructorFullAvailabiltyJob" />
		</property>
		<property name="cronExpression" value="${edw.cron:0 55 00 * * ?}"/>
	</bean>
	
	<bean id="disabledInstructorsGetAvailabiltyJob" 
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass"
			value="com.guitarcenter.scheduler.jobs.DisabledInstructorsGetAvailabiltyJob" />
		<property name="durability" value="true" />
	</bean>
	<bean id="disabledInstructorsGetAvailabiltyJobScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail">
			<ref bean="disabledInstructorsGetAvailabiltyJob" />
		</property>
		<property name="cronExpression" value="${edw.cron:0 */30 * ? * *}"/>
	</bean>
	
	 <!--  GSSP-277-->
	<bean id="dataBackUpMonthlyJob" 
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass"
			value="com.guitarcenter.scheduler.jobs.DataBackUpMonthlyJob" />
		<property name="durability" value="true" />
	</bean>
	<bean id="dataBackUpMonthlyJobScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<property name="jobDetail">
			<ref bean="dataBackUpMonthlyJob" />
		</property>
		<property name="cronExpression" value="${edw.cron:0 0 22 L * ?}"/>
	</bean>
	
        <!--  GSSP-275-->
        <bean id="adpJob" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
            <property name="jobClass" value="com.guitarcenter.scheduler.jobs.ADPFileJob"/>
            <property name="durability" value="true"/>
        </bean>
        <bean id="adpJobScheduler"
              class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
           <property name="jobDetail" ref="adpJob"/>
           <property name="cronExpression" value="${adp.cron:0 15 6 * * ?}"/>
        </bean>
		<bean id="emailScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
			<property name="jobDetail">
				<ref bean="emailJobtask" />
			</property>
			<property name="cronExpression" value="${mail.cron:0 0 3 * * ?}" />
		</bean>
		
		<!-- OLL-3732 -->
		
		<bean id="completedLessonsScheduler" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
			<property name="jobDetail">
				<ref bean="completedLessonsJobtask" />
			</property>
			<property name="cronExpression" value="${mail.cron:0 0 3 * * ?}" />
		</bean>
		
		
		
		<!--  GSSP-272 -->
        <bean id="instructorFloorTimeJob"
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass" value="com.guitarcenter.scheduler.jobs.InstructorFloorTimeJob" />
		<property name="durability" value="true" />
	    </bean>
	    
		<bean id="instructorFloorTimeJobScheduler"
		class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
			<property name="jobDetail" ref="instructorFloorTimeJob" />
			<property name="cronExpression" value="${floor.cron:0 30 5 ? * MON}" />
		</bean> 
		
		<!--  GSSP-298 -->
        <bean id="instructorActivityAndAvailabilityJob"
		class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
		<property name="jobClass" value="com.guitarcenter.scheduler.jobs.InstructorActivityAndAvailabilityJob" />
		<property name="durability" value="true" />
	    </bean>
	    
		<bean id="instructorActivityAndAvailabilityJobScheduler"
		class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
			<property name="jobDetail" ref="instructorActivityAndAvailabilityJob" />
			<property name="cronExpression" value="${floor.cron:0 0 1 1 * ?}" />
		</bean> 
		
		<!-- Add for GSSP-172  -  Notification for Scheduler Batch Job Success and Failure-->
		<bean id="jobNotificationEmailService"
			class="com.guitarcenter.scheduler.service.JobNotificationEmailServiceImpl">	
			
		</bean>
        
		<!-- Production redefines Quartz scheduler for a clustered environment
		     and adds EDW integration job
		-->
        <bean id="quartz" lazy-init="false" autowire="no"
              class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
            <property name="triggers">
                <list>
                
                  <ref bean="dailySubscriptionReportJobScheduler" />  
               <!--  <ref bean="deleteDayforceInstructorApptRecordsJobScheduler" /> -->
                  <ref bean="getInstructorFullAvailabiltyJobScheduler" />  
                 <ref bean="masterScheduleReportJobScheduler" /> 
                    <ref bean="emailScheduler" />
                    <ref bean="edwJobScheduler"/>
                     <ref bean="completedLessonsScheduler"/>
                   <ref bean="cancelHoldAppointmentsJobScheduler" /> 
                   <ref bean="getInstructorAvailabiltyJobScheduler" /> 
                   <ref bean="cRMAppointmentDataFileScheduler" />  
                   <ref bean="dataBackUpMonthlyJobScheduler" />
                   <ref bean="disabledInstructorsGetAvailabiltyJobScheduler" />   
                    	<!--  GSSP-275-->
                    <ref bean="adpJobScheduler"/>
                    <ref bean="lackingAppointmentJobScheduler" />
						<!--  GSSP-240 -->
					<ref bean="instructorActivitiesJobScheduler" />
					<!-- GSSP-272 -->
					<ref bean="instructorFloorTimeJobScheduler" />
					<!-- GSSP-298 -->
					<ref bean="instructorActivityAndAvailabilityJobScheduler"/>
					
                </list>
            </property>
            <property name="autoStartup" value="true"/>
            <property name="waitForJobsToCompleteOnShutdown" value="true"/>
            <property name="overwriteExistingJobs" value="true"/>
            <property name="dataSource" ref="schedulerDB"/>
            <property name="transactionManager" ref="transactionManager"/>
            <property name="schedulerContextAsMap">
                <map>
                
                
                <entry key="dailySubscriptionReportService" value-ref="dailySubscriptionReportService" />
                <entry key="deleteDayforceInstructorApptRecordsService" value-ref="deleteDayforceInstructorApptRecordsService" />
                <entry key="getInstructorFullAvailabiltyService" value-ref="getInstructorFullAvailabiltyService" />
                <entry key="masterScheduleReportService" value-ref="masterScheduleReportService" />
                    <entry key="appointmentEmailService"
                           value-ref="appointmentEmailService"/>
                    <entry key="locationIntegrationService"
                           value-ref="locationIntegrationService"/>
                    <entry key="employeeIntegrationService"
                           value-ref="employeeIntegrationService"/>
                    <entry key="lackingAppointmentService"
                           value-ref="lackingAppointmentService" />
                      <entry key="cRMAppointmentDataFileService" value-ref="cRMAppointmentDataFileService" />
                      <entry key="cancelHoldAppointmentsService" value-ref="cancelHoldAppointmentsService" />
                       
                      <entry key="dataBackUpMonthlyService" value-ref="dataBackUpMonthlyService" />    
                   
                        <entry key="getInstructorAvailabiltyService" value-ref="getInstructorAvailabiltyService" />   
                           <!-- GSSP-275 -->
					<entry key="instructorPersonalDetailsIntegrationService"
                           value-ref="instructorPersonalDetailsIntegrationService" />	   
                     <!-- GSSP-240 -->
                    <entry key="instructorActivitiesService"
                           value-ref="instructorActivitiesService" />
                       <!-- GSSP-272 -->   
                     <entry key="instructorFloorTimeService"
                        	value-ref="instructorFloorTimeService" />   
                        	 <!-- GSSP-298 -->   
                     <entry key="instructorActivityAndAvailabitityService"
                        	value-ref="instructorActivityAndAvailabitityService" />    
                        
					 
                      <!--  Added for GSSP-238 -->     
                     <entry key="reportService" value-ref="reportService" />
                    
                    <!-- Add for GSSP-172  -  Notification for Scheduler Batch Job Success and Failure-->
                    <entry key="jobNotificationEmailService" 
                        value-ref="jobNotificationEmailService" />
                        
                        
                </map>
            </property>
            <property name="quartzProperties">
                <props>
                    <prop key="org.quartz.scheduler.skipUpdateCheck">true</prop>
                    <prop key="org.quartz.jobStore.misfireThreshold">6000000</prop>
                    <prop key="org.quartz.jobStore.driverDelegateClass">org.quartz.impl.jdbcjobstore.oracle.OracleDelegate</prop>
                    <prop key="org.quartz.jobStore.isClustered">true</prop>
                    <prop key="org.quartz.jobStore.clusterCheckinInterval">20000</prop>
                    <prop key="org.quartz.scheduler.instanceId">AUTO</prop>
                </props>
            </property>
        </bean>
        
       
<!-- Removed for TIBCO-3299 Refactor GCS Lessons application to connect to EMS 8.4 via SSL 
     Production profile will use TIBCO for JMS  -->
       <!--  <bean id="connectionFactory"
              class="${pos.class:com.tibco.tibjms.TibjmsConnectionFactory}">
            <property name="serverUrl" value="${pos.serverUrl}"/>
            <property name="userName" value="${pos.username:}"/>
            <property name="userPassword" value="${pos.password:}"/>
        </bean> -->
     
<!-- Added for TIBCO-3299 Refactor GCS Lessons application to connect to EMS 8.4 via SSL 
     Production profile will use TIBCO for JMS  Changes--> 
	 
<bean id="connectionFactory" class="${pos.class:com.tibco.tibjms.TibjmsConnectionFactory}">
  <constructor-arg value="${pos.serverUrl}" />
  <constructor-arg><null/></constructor-arg>
  <constructor-arg>
    <util:map>
      <entry key="com.tibco.tibjms.factory.username"  value="${pos.username:}"/>
      <entry key="com.tibco.tibjms.factory.password" value="${pos.password:}"/>     
      <entry key="com.tibco.tibjms.ssl.vendor" value="j2se"/>
      <entry key="com.tibco.tibjms.ssl.enable_verify_host" value="false"/>
      <entry key="com.tibco.tibjms.ssl.enable_verify_hostname" value="false"/>     
      <entry key="com.tibco.tibjms.ssl.identity" value="${pos.sslidentity}"/>
      <entry key="com.tibco.tibjms.ssl.password" value="${pos.sslpassword:}"/>
    </util:map>
  </constructor-arg>
</bean>
        
        <!-- Authentication mechanisms for production.
             Notes: 1. Not using AD provider from Spring as it does not support
                       mixed domain userPrincipalName.
                       E.g. userPrincipalName values like
                            <EMAIL>
                       and  <EMAIL>
                       occur in the same tree
                    2. Subtree searching in AD provider has issues
        -->
        
        <!-- DOMESTIC -->
        <bean id="domesticContextSource"
              class="org.springframework.security.ldap.DefaultSpringSecurityContextSource">
            <constructor-arg value="${auth.domestic.url}"/>
            <property name="userDn" value="${auth.domestic.bindUser}"/>
            <property name="password" value="${auth.domestic.bindPassword}"/>
        </bean>
        <bean id="domesticProvider"
              class="org.springframework.security.ldap.authentication.LdapAuthenticationProvider">
            <constructor-arg>
                <bean class="org.springframework.security.ldap.authentication.BindAuthenticator">
                    <constructor-arg ref="domesticContextSource"/>
                    <property name="userSearch">
                        <bean class="org.springframework.security.ldap.search.FilterBasedLdapUserSearch">
                            <constructor-arg index="0" value="${auth.domestic.baseDN:}"/>
                            <constructor-arg index="1" value="${auth.domestic.filter:(&amp;(sAMAccountName={0})(objectClass=person))}"/>
                            <constructor-arg index="2" ref="domesticContextSource"/>
                        </bean>
                    </property>
                </bean>
            </constructor-arg>
        </bean>
        
        <!-- RETAIL -->
        <bean id="retailContextSource"
              class="org.springframework.security.ldap.DefaultSpringSecurityContextSource">
            <constructor-arg value="${auth.retail.url}"/>
            <property name="userDn" value="${auth.retail.bindUser}"/>
            <property name="password" value="${auth.retail.bindPassword}"/>
        </bean>
        <bean id="retailProvider"
              class="org.springframework.security.ldap.authentication.LdapAuthenticationProvider">
            <constructor-arg>
                <bean class="org.springframework.security.ldap.authentication.BindAuthenticator">
                    <constructor-arg ref="retailContextSource"/>
                    <property name="userSearch">
                        <bean class="org.springframework.security.ldap.search.FilterBasedLdapUserSearch">
                            <constructor-arg index="0" value="${auth.retail.baseDN:}"/>
                            <constructor-arg index="1" value="${auth.retail.filter:(&amp;(sAMAccountName={0})(objectClass=person))}"/>
                            <constructor-arg index="2" ref="retailContextSource"/>
                        </bean>
                    </property>
                </bean>
            </constructor-arg>
        </bean>
        
        <!-- MF -->
        <bean id="mfContextSource"
              class="org.springframework.security.ldap.DefaultSpringSecurityContextSource">
            <constructor-arg value="${auth.mf.url}"/>
            <property name="userDn" value="${auth.mf.bindUser}"/>
            <property name="password" value="${auth.mf.bindPassword}"/>
        </bean>
        <bean id="mfProvider"
              class="org.springframework.security.ldap.authentication.LdapAuthenticationProvider">
            <constructor-arg>
                <bean class="org.springframework.security.ldap.authentication.BindAuthenticator">
                    <constructor-arg ref="mfContextSource"/>
                    <property name="userSearch">
                        <bean class="org.springframework.security.ldap.search.FilterBasedLdapUserSearch">
                            <constructor-arg index="0" value="${auth.mf.baseDN:}"/>
                            <constructor-arg index="1" value="${auth.mf.filter:(&amp;(sAMAccountName={0})(objectClass=person))}"/>
                            <constructor-arg index="2" ref="mfContextSource"/>
                        </bean>
                    </property>
                </bean>
            </constructor-arg>
        </bean>
        
	</beans>
</beans>
