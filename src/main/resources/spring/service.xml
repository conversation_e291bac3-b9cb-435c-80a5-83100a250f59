<?xml version="1.0" encoding="UTF-8"?>
<!-- Define the service layer $Id: $ -->
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:jaxws="http://cxf.apache.org/jaxws"
	xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
                           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.2.xsd
                           http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.2.xsd
                           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.2.xsd
                           http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.2.xsd
                           http://cxf.apache.org/jaxws http://cxf.apache.org/schemas/jaxws.xsd
                           ">
	<context:annotation-config />

	<!-- All services are defined in service package -->
	<context:component-scan base-package="com.guitarcenter.scheduler.service" />
	<context:component-scan base-package="com.guitarcenter.scheduler.dao" />

	<!-- Enable async method execution with proxy-target-class to handle circular dependencies -->
	<task:annotation-driven executor="emailTaskExecutor" proxy-target-class="true" />

	<!-- Configure task executor for async email operations -->
	<task:executor id="emailTaskExecutor"
		pool-size="5-10"
		queue-capacity="100"
		rejection-policy="CALLER_RUNS" />

	<!-- sessionFactory -->
	<bean id="sessionFactory"
		class="org.springframework.orm.hibernate4.LocalSessionFactoryBean">
		<property name="dataSource">
			<ref bean="schedulerDB" />
		</property>
		<property name="hibernateProperties">
			<ref bean="hibernateProperties" />
		</property>
		<property name="packagesToScan">
			<list>
				<value>com.guitarcenter.scheduler.model</value>
			</list>
		</property>
		<property name="mappingResources">
			<list>
				<value>spring/validation.hbm.xml</value>
			</list>
		</property>
	</bean>
	<!-- Transactions will be annotation based, so enable and define a simple
		transaction manager; this may need to change to JTA for JMS integration later. -->
	<tx:annotation-driven transaction-manager="transactionManager" />
	<bean id="transactionManager"
		class="org.springframework.orm.hibernate4.HibernateTransactionManager">
		<property name="dataSource" ref="schedulerDB" />
		<property name="sessionFactory" ref="sessionFactory" />
	</bean>


	<!-- Add a customerization message resource -->
	<bean id="messageSource"
		class="org.springframework.context.support.ResourceBundleMessageSource">
		<property name="basenames">
			<list>
				<value>message</value>
			</list>
		</property>
	</bean>
</beans>