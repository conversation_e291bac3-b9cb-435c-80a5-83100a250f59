<?xml version="1.0" encoding="UTF-8"?>
<!-- DEV profile specific settings and overrides; used by offshore? $Id: 
	$ -->
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">

	<beans profile="local">
		<!-- Example override; DEV profile shows hibernate SQL -->
		<bean id="hibernateProperties"
			class="org.springframework.beans.factory.config.PropertiesFactoryBean">
			<property name="properties">
				<props>
					<prop key="hibernate.dialect">org.hibernate.dialect.Oracle10gDialect</prop>
					<!-- <prop key="hibernate.hbm2ddl.auto">update</prop> -->
					<prop key="hibernate.show_sql">true</prop>
					<prop key="hibernate.connection.autocommit">false</prop>
					<prop key="hibernate.current_session_context_class">org.springframework.orm.hibernate4.SpringSessionContext
					</prop>
					<prop key="hibernate.cache.use_second_level_cache">true</prop>
					<prop key="hibernate.cache.use_query_cache">true</prop>
					<prop key="hibernate.cache.region.factory_class">org.hibernate.cache.ehcache.EhCacheRegionFactory
					</prop>
				</props>
			</property>
		</bean>

		<!-- Use an embedded ActiveMQ instance for JMS in DEV profile -->
		<bean id="connectionFactory" class="org.apache.activemq.ActiveMQConnectionFactory">
			<property name="brokerURL"
				value="vm://pos?broker.persistent=false&amp;broker.useJmx=false" />
		</bean>

		<bean id="lackingAppointmentJobScheduler"
			class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
			<property name="jobDetail" ref="lackingAppointmentJob" />
			<property name="cronExpression" value="0 0 6 * * ?" />
		</bean>
		
		<!--  Changes made for GSSP-238 -->
		<bean id="conflictingAppointmentJobScheduler"
			class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
			<property name="jobDetail" ref="conflictingAppointmentJob" />
			<property name="cronExpression" value="0 0/2 * * * ?" />
		</bean>
		<!-- End of Changes made for GSSP-238 -->
		
		
	</beans>

</beans>
