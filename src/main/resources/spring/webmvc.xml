<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:mvc="http://www.springframework.org/schema/mvc"
	xmlns:p="http://www.springframework.org/schema/p" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans  
             http://www.springframework.org/schema/beans/spring-beans-3.0.xsd  
             http://www.springframework.org/schema/context   
             http://www.springframework.org/schema/context/spring-context-3.0.xsd  
             http://www.springframework.org/schema/aop   
             http://www.springframework.org/schema/aop/spring-aop-3.0.xsd  
             http://www.springframework.org/schema/tx   
             http://www.springframework.org/schema/tx/spring-tx-3.0.xsd  
             http://www.springframework.org/schema/mvc   
             http://www.springframework.org/schema/mvc/spring-mvc-3.2.xsd">

	<!-- Add all MVC controller implementations in controller package -->
	<context:component-scan base-package="com.guitarcenter.scheduler.controller,com.guitarcenter.scheduler.webservice" />

	<!-- define and convert the return type -->
	<mvc:annotation-driven
		content-negotiation-manager="contentNegotiationManager">
		<mvc:message-converters register-defaults="false">
			<!-- Use the HibernateAware mapper instead of the default -->
			<bean
				class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
				<property name="objectMapper">
					<bean
						class="com.guitarcenter.scheduler.dao.util.HibernateAwareObjectMapper" />
				</property>
			</bean>
			<bean
				class="org.springframework.http.converter.ByteArrayHttpMessageConverter" />
			<bean class="org.springframework.http.converter.StringHttpMessageConverter" />
			<bean
				class="org.springframework.http.converter.ResourceHttpMessageConverter" />
			<bean
				class="org.springframework.http.converter.xml.SourceHttpMessageConverter" />
			<bean class="org.springframework.http.converter.FormHttpMessageConverter" />
			<bean
				class="org.springframework.http.converter.xml.Jaxb2RootElementHttpMessageConverter" />
		</mvc:message-converters>
	</mvc:annotation-driven>

	<bean id="contentNegotiationManager"
		class="org.springframework.web.accept.ContentNegotiationManagerFactoryBean">
		<property name="favorPathExtension" value="false" />
		<property name="favorParameter" value="false" />
		<property name="ignoreAcceptHeader" value="false" />
		<property name="mediaTypes">
			<value>
				html=text/html
				json=application/json
				xml=application/xml
				*=*/*
			</value>
		</property>
	</bean>

	<mvc:resources mapping="/js/**" location="/js/" />
	<mvc:resources mapping="/css/**" location="/css/" />

	<mvc:default-servlet-handler />

	<!-- Configure framework to convert view resources to a path: foo -> /WEB-INF/views/foo.jsp -->
	<bean id="jspViewResolver"
		class="org.springframework.web.servlet.view.InternalResourceViewResolver">
		<property name="viewClass"
			value="org.springframework.web.servlet.view.JstlView" />
		<property name="prefix" value="/WEB-INF/views/" />
		<property name="suffix" value=".jsp" />
	</bean>
	
	<!-- Configure jasperreports view resolver -->
	<bean id="JasperViewResolver"
		class="org.springframework.web.servlet.view.ResourceBundleViewResolver">
		<property name="order" value="1" />
		<property name="basename" value="jasperreports.views" />
	</bean>
	
</beans>
