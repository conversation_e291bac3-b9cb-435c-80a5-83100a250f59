<?xml version="1.0" encoding="UTF-8"?>
<!-- Defines the integration points of the application
     $Id: $
 -->
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:oxm="http://www.springframework.org/schema/oxm"
	xmlns:jms="http://www.springframework.org/schema/jms"
	xmlns:p="http://www.springframework.org/schema/p"
	xmlns:jee="http://www.springframework.org/schema/jee"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
						http://www.springframework.org/schema/context
						http://www.springframework.org/schema/context/spring-context-3.2.xsd
						http://www.springframework.org/schema/oxm
						http://www.springframework.org/schema/oxm/spring-oxm-3.2.xsd
						http://www.springframework.org/schema/jms
						http://www.springframework.org/schema/jms/spring-jms-3.2.xsd
                        http://www.springframework.org/schema/jee
                        http://www.springframework.org/schema/jee/spring-jee-3.2.xsd">
	
	<!-- Bring in integration components -->
	<context:annotation-config />
	<context:component-scan base-package="com.guitarcenter.scheduler.integration"/>

	<!-- GC have provided XSD for POS provided customer records; using JiXB to
	     generate classes from XSD (maven) and to unmarshall the records from
	     POS XML format to bean.
	
	     Note that Phase 1 does not define any push requirements (scheduler -> POS)     
	 -->
	<oxm:jibx-marshaller id="posCustomerMarshaller"
                         target-class="com.guitarcenter.scheduler.integration.generated.SyncCustomer"/>
	    
	<bean id="posMessageConverter" class="org.springframework.jms.support.converter.MarshallingMessageConverter">
		<constructor-arg ref="posCustomerMarshaller"/>
	</bean>
	
	<bean id="posCustomerListener"
          class="org.springframework.jms.listener.adapter.MessageListenerAdapter">
    	<constructor-arg>
    		<bean class="com.guitarcenter.scheduler.integration.service.impl.CustomerIntegrationServiceImpl"/>
    	</constructor-arg>
	    <property name="defaultListenerMethod" value="process"/>
	   	<property name="messageConverter" ref="posMessageConverter"/>
    </bean>
    
    <jms:listener-container container-type="default"
    	connection-factory="connectionFactory"
    	acknowledge="transacted"
    	destination-type="queue"
    	cache="session">
    	<jms:listener ref="posCustomerListener"
    	              destination="${pos.queue:GCI.GC.LESSONS.PRV.CUSTOMER.INBOUND}"/>
    </jms:listener-container>
</beans>
