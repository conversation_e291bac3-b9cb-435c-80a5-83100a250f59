<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN" "http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd" >
<hibernate-mapping>
	<sql-query name="room.getRoomByAppointmentTime">  
        <![CDATA[  
			SELECT {r.*}
			FROM room r
			WHERE NOT EXISTS
			  (SELECT *
			  FROM appointment a
			  WHERE (a.start_time BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?)
			  OR a.end_time BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?)
			  OR TO_TIMESTAMP_TZ(?,?) BETWEEN a.start_time AND a.end_time
			  OR TO_TIMESTAMP_TZ(?,?) BETWEEN a.start_time AND a.end_time)
			  AND a.profile_id = ?
			  AND a.canceled in (? , ?) 
			  AND r.room_id = a.room_id(+)
			  ) and r.room_id=?
         ]]>
		<return alias="r" class="com.guitarcenter.scheduler.model.Room" />
	</sql-query>
	<sql-query name="room.getRoomByAppointmentRecurringTime">  
        <![CDATA[  
			SELECT {r.*}
			FROM room r
			WHERE NOT EXISTS
			  (SELECT *
			  FROM appointment a
			  WHERE TO_CHAR(start_time,'d') = TO_CHAR(to_date(?,?),'d')
			  AND TO_TIMESTAMP_TZ(TO_CHAR(start_time, ?), ?) BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?)
			  AND (a.start_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?,?)
			  AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '||?,?)
			  OR a.end_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?,?)
			  AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '||?,?)
			  OR TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?,?) BETWEEN a.start_time AND a.end_time
			  OR TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '||?,?) BETWEEN a.start_time AND a.end_time)
			  AND a.profile_id = ?
			   AND a.canceled in (? , ?) 
			  AND r.room_id = a.room_id(+)
			  )
			AND r.room_id=?
         ]]>
		<return alias="r" class="com.guitarcenter.scheduler.model.Room" />
	</sql-query>
	<sql-query name="activity.getActivityByAppointmentTime">  
        <![CDATA[  
			SELECT {ac.*}
			FROM activity ac
			WHERE NOT EXISTS
			  (SELECT *
			  FROM appointment a
			  WHERE (a.start_time BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?)
			  OR a.end_time BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?)
			  OR TO_TIMESTAMP_TZ(?,?) BETWEEN a.start_time AND a.end_time
			  OR TO_TIMESTAMP_TZ(?,?) BETWEEN a.start_time AND a.end_time)
			  AND a.profile_id = ?
			  AND a.canceled in (? , ?) 
			  AND ac.activity_id = a.activity_id(+)
			  ) and ac.activity_id = ?
         ]]>
		<return alias="ac" class="com.guitarcenter.scheduler.model.Activity" />
	</sql-query>
	<sql-query name="activity.getActivityByAppointmentRecurringTime">  
        <![CDATA[  
			SELECT {ac.*}
			FROM activity ac
			WHERE NOT EXISTS
			  (SELECT *
			  FROM appointment a
			  WHERE TO_CHAR(start_time,'d') = TO_CHAR(to_date(?,?),'d')
			  AND TO_TIMESTAMP_TZ(TO_CHAR(start_time, ?), ?) BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?)
			  AND (a.start_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?,?)
			  AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '||?,?)
			  OR a.end_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?,?)
			  AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '||?,?)
			  OR TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?,?) BETWEEN a.start_time AND a.end_time
			  OR TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '||?,?) BETWEEN a.start_time AND a.end_time)
			  AND a.profile_id = ?
			  AND a.canceled in (? , ?) 
			  AND ac.activity_id = a.activity_id(+)
			  )
			AND ac.activity_id = ?
         ]]>
		<return alias="ac" class="com.guitarcenter.scheduler.model.Activity" />
	</sql-query>
	<sql-query name="locationProfile.checkLocationProfileTime">  
        <![CDATA[  
			WITH t AS
			  (SELECT availability_id,
			    2 week_day,
			    monday_start_time start_time,
			    monday_end_time end_time
			  FROM availability
			  UNION
			  SELECT availability_id,
			    3,
			    TUESDAY_START_TIME ,
			    TUESDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    4,
			    WEDNESDAY_START_TIME ,
			    WEDNESDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    5,
			    THURSDAY_START_TIME ,
			    THURSDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    6,
			    FRIDAY_START_TIME ,
			    FRIDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    7,
			    SATURDAY_START_TIME ,
			    SATURDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    1,
			    SUNDAY_START_TIME ,
			    SUNDAY_end_TIME
			  FROM availability
			  )
			SELECT {l.*}
			FROM t,
			  location_profile l
			WHERE t.availability_id = l.availability_id
			AND t.week_day          = TO_CHAR(to_date(?,?),'d')
			AND TO_TIMESTAMP_TZ(?, ?) BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(t.start_time,?),?) AND TO_TIMESTAMP_TZ(TO_CHAR(t.end_time,?),?)
			AND TO_TIMESTAMP_TZ(?, ?) BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(t.start_time,?),?) AND TO_TIMESTAMP_TZ(TO_CHAR(t.end_time,?),?)
			and l.profile_id=?
         ]]>
		<return alias="l" class="com.guitarcenter.scheduler.model.LocationProfile" />
	</sql-query>
	<sql-query name="instructor.checkInstructorTime">  
        <![CDATA[  
			WITH t AS
			  (SELECT availability_id,
			    2 week_day,
			    monday_start_time start_time,
			    monday_end_time end_time
			  FROM availability
			  UNION
			  SELECT availability_id,
			    3,
			    TUESDAY_START_TIME ,
			    TUESDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    4,
			    WEDNESDAY_START_TIME ,
			    WEDNESDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    5,
			    THURSDAY_START_TIME ,
			    THURSDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    6,
			    FRIDAY_START_TIME ,
			    FRIDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    7,
			    SATURDAY_START_TIME ,
			    SATURDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    1,
			    SUNDAY_START_TIME ,
			    SUNDAY_end_TIME
			  FROM availability
			  )
			SELECT {i.*}
			FROM t,
			  instructor i
			WHERE t.availability_id = i.availability_id
			AND t.week_day          = TO_CHAR(to_date(?,?),'d')
			AND TO_TIMESTAMP_TZ(?, ?) BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(t.start_time,?),?) AND TO_TIMESTAMP_TZ(TO_CHAR(t.end_time,?),?)
			AND TO_TIMESTAMP_TZ(?, ?) BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(t.start_time,?),?) AND TO_TIMESTAMP_TZ(TO_CHAR(t.end_time,?),?)
			and i.instructor_id=?
         ]]>
		<return alias="i" class="com.guitarcenter.scheduler.model.Instructor" />
	</sql-query>
	<sql-query name="instructor.checkInstructorAppointmentRecurringTime">
        <![CDATA[
			SELECT {i.*}
			FROM instructor i
			WHERE NOT EXISTS
			  (SELECT *
			  FROM appointment a
			  WHERE TO_CHAR(start_time,'d') = TO_CHAR(to_date(?,?),'d')
			  AND TO_TIMESTAMP_TZ(TO_CHAR(start_time, ?), ?) BETWEEN TO_TIMESTAMP_TZ(?, ?) AND TO_TIMESTAMP_TZ(?, ?)
			  AND (a.start_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?, ?)
			  AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '|| ?, ?)
			  OR a.end_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?, ?)
			  AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time, ?)||' '||?, ?)
			  OR TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?,?) BETWEEN a.start_time AND a.end_time
			  OR TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '||?,?) BETWEEN a.start_time AND a.end_time)
			  AND a.canceled in (? , ?) 
			  AND i.instructor_id = a.instructor_id(+)
			  )
			AND i.instructor_id=?
         ]]>
		<return alias="i" class="com.guitarcenter.scheduler.model.Instructor" />
	</sql-query>
	<sql-query name="instructor.checkInstructorAppointmentTime">
        <![CDATA[
			SELECT {i.*}
			FROM instructor i
			WHERE NOT EXISTS
			  (SELECT *
			  FROM appointment a
			  WHERE (a.start_time BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?)
			  OR a.end_time BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?)
			  OR TO_TIMESTAMP_TZ(?,?) BETWEEN a.start_time AND a.end_time
			  OR TO_TIMESTAMP_TZ(?,?) BETWEEN a.start_time AND a.end_time)
			  AND a.canceled in (? , ?) 
			  AND i.instructor_id = a.instructor_id(+)
			  )
			AND i.instructor_id=?
         ]]>
		<return alias="i" class="com.guitarcenter.scheduler.model.Instructor" />
	</sql-query>
	<sql-query name="appointment.getAppointmentByRoomActivityProfile">
	      
        <![CDATA[  
			SELECT {a.*} FROM appointment a
			WHERE a.start_time BETWEEN sysdate AND add_months(sysdate,?)
			AND a.room_id    = ?
			AND a.activity_id=?
			AND a.profile_id = ?
			AND a.canceled in (? , ?)
         ]]>
		<return alias="a" class="com.guitarcenter.scheduler.model.Appointment" />
	</sql-query>
	<sql-query name="appointment.getAppointmentByRoomTemplateActivity">  
        <![CDATA[  
			SELECT {a.*} FROM appointment a, room r
			WHERE a.room_id = r.room_id
			AND a.start_time BETWEEN sysdate AND add_months(sysdate,?)
			AND r.room_template_id = ?
			AND a.activity_id=?
         ]]>
		<return alias="a" class="com.guitarcenter.scheduler.model.Appointment" />
	</sql-query>
	<sql-query name="appointment.getAppointmentByRoom">
        <![CDATA[  
			SELECT {a.*} FROM appointment a
			WHERE a.start_time BETWEEN sysdate AND add_months(sysdate,?)
			AND a.room_id    = ?
			AND a.profile_id = ?
			AND a.canceled in (? , ?) 
         ]]>
		<return alias="a" class="com.guitarcenter.scheduler.model.Appointment" />
	</sql-query>
	<sql-query name="appointment.getAppointmentByProfile">
        <![CDATA[  
			SELECT {a.*} FROM appointment a
			WHERE a.start_time BETWEEN sysdate AND add_months(sysdate,?)
			AND a.profile_id = ? AND a.canceled in (? , ?) 
         ]]>
		<return alias="a" class="com.guitarcenter.scheduler.model.Appointment" />
	</sql-query>
	<sql-query name="common.checkStartTime">
        <![CDATA[  
			select case when TO_TIMESTAMP_TZ(?,?) < sysdate 
			then 'false' else 'true' end flag from dual
         ]]>
	</sql-query>
	<sql-query name="room.getUpdateRoomByAppointmentTime">  
        <![CDATA[  
			SELECT CASE WHEN EXISTS
			  (SELECT *
			  FROM appointment a
			  WHERE (a.start_time BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?)
			  OR a.end_time BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?)
			  OR TO_TIMESTAMP_TZ(?,?) BETWEEN a.start_time AND a.end_time
			  OR TO_TIMESTAMP_TZ(?,?) BETWEEN a.start_time AND a.end_time)
			  AND a.profile_id = ?
			  AND a.canceled in (? , ?) 
			  AND a.room_id = ?
			  &REPLACECONDITION
			  )THEN 'false' else 'true' END flag FROM DUAL
         ]]>
	</sql-query>
	<sql-query name="room.getUpdateRoomByAppointmentRecurringTime">  
        <![CDATA[  
			SELECT CASE WHEN EXISTS
			  (SELECT *
			  FROM appointment a
			  WHERE TO_CHAR(start_time,'d') = TO_CHAR(to_date(?,?),'d')
			  AND TO_TIMESTAMP_TZ(TO_CHAR(start_time, ?), ?) BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?)
			  AND (a.start_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?,?)
			  AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '||?,?)
			  OR a.end_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?,?)
			  AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '||?,?)
			  OR TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?,?) BETWEEN a.start_time AND a.end_time
			  OR TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '||?,?) BETWEEN a.start_time AND a.end_time)
			  AND a.profile_id = ?
			  AND a.canceled in (? , ?) 
			  AND a.room_id = ?
			  &REPLACECONDITION
			  )THEN 'false' else 'true' END flag FROM DUAL
         ]]>
	</sql-query>
	<sql-query name="instructor.checkUpdateInstructorAppointmentRecurringTime">
        <![CDATA[
			SELECT CASE WHEN EXISTS
			  (SELECT *
			  FROM appointment a
			  WHERE TO_CHAR(start_time,'d') = TO_CHAR(to_date(?,?),'d')
			  AND TO_TIMESTAMP_TZ(TO_CHAR(start_time, ?), ?) BETWEEN TO_TIMESTAMP_TZ(?, ?) AND TO_TIMESTAMP_TZ(?, ?)
			  AND (a.start_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?, ?)
			  AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '|| ?, ?)
			  OR a.end_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?, ?)
			  AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time, ?)||' '||?, ?)
			  OR TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?,?) BETWEEN a.start_time AND a.end_time
			  OR TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '||?,?) BETWEEN a.start_time AND a.end_time)
			  AND a.canceled in (? , ?) 
			  AND a.instructor_id = ?
			  &REPLACECONDITION
			  )THEN 'false' else 'true' END flag FROM DUAL
         ]]>
    </sql-query>
	<sql-query name="instructor.checkUpdateInstructorAppointmentTime">
        <![CDATA[
			SELECT CASE WHEN EXISTS
			  (SELECT *
			  FROM appointment a
			  WHERE (a.start_time BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?)
			  OR a.end_time BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?)
			  OR TO_TIMESTAMP_TZ(?,?) BETWEEN a.start_time AND a.end_time
			  OR TO_TIMESTAMP_TZ(?,?) BETWEEN a.start_time AND a.end_time)
			  AND a.canceled in (? , ?) 
			  AND a.instructor_id = ?
			  &REPLACECONDITION
			  )THEN 'false' else 'true' END flag FROM DUAL
         ]]>
	</sql-query>
	<sql-query name="instructor.checkInstructorByProfileActivityId">
        <![CDATA[
			SELECT CASE WHEN EXISTS
			  (SELECT *
				FROM instructor_activities ia,
				  profile_activity pa
				WHERE ia.activity_id = pa.activity_id
				AND ia.instructor_id = ?
				AND ia.activity_id    = ?
				AND pa.profile_id    = ?
				AND pa.enabled = ?
			  )THEN 'true' else 'false' END flag FROM DUAL
         ]]>
	</sql-query>
	<sql-query name="customer.checkCustomerAppointmentTime">
        <![CDATA[
	        SELECT CASE WHEN EXISTS
			(SELECT *
				FROM appointment a, appointment_customers ac
				WHERE a.appointment_id = ac.appointment_id
				AND (a.start_time BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?)
				OR a.end_time BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?)
				OR TO_TIMESTAMP_TZ(?,?) BETWEEN a.start_time AND a.end_time
				OR TO_TIMESTAMP_TZ(?,?) BETWEEN a.start_time AND a.end_time)
				AND a.profile_id = ?
				AND a.canceled  in (? , ?) 
				AND ac.customer_id = ?
	        )THEN 'false' else 'true' END flag FROM DUAL
        ]]>
	</sql-query>
	<sql-query name="customer.checkCustomerAppointmentRecurringTime">
        <![CDATA[
	        SELECT CASE WHEN EXISTS
			(SELECT *
				FROM appointment a, appointment_customers ac
				WHERE a.appointment_id = ac.appointment_id
				AND TO_CHAR(a.start_time,'d') = TO_CHAR(to_date(?,?),'d')
				AND TO_TIMESTAMP_TZ(TO_CHAR(a.start_time, ?), ?) BETWEEN TO_TIMESTAMP_TZ(?, ?) AND TO_TIMESTAMP_TZ(?, ?)
				AND (a.start_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?, ?)
				AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '|| ?, ?)
				OR a.end_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?, ?)
				AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time, ?)||' '||?, ?)
				OR TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?,?) BETWEEN a.start_time AND a.end_time
				OR TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '||?,?) BETWEEN a.start_time AND a.end_time)
				AND a.profile_id = ?
				AND a.canceled in (? , ?) 
				AND ac.customer_id = ?
	        )THEN 'false' else 'true' END flag FROM DUAL
	        ]]>
	</sql-query>
	<sql-query name="customer.checkUpdateCustomerAppointmentTime">
        <![CDATA[
	        SELECT CASE WHEN EXISTS
			(SELECT *
				FROM appointment a, appointment_customers ac
				WHERE a.appointment_id = ac.appointment_id
				AND (a.start_time BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?)
				OR a.end_time BETWEEN TO_TIMESTAMP_TZ(?,?) AND TO_TIMESTAMP_TZ(?,?)
				OR TO_TIMESTAMP_TZ(?,?) BETWEEN a.start_time AND a.end_time
				OR TO_TIMESTAMP_TZ(?,?) BETWEEN a.start_time AND a.end_time)
				AND a.profile_id = ?
				AND a.canceled in (? , ?) 
				AND ac.customer_id = ?
				&REPLACECONDITION
	        )THEN 'false' else 'true' END flag FROM DUAL
        ]]>
	</sql-query>
	<sql-query name="customer.checkUpdateCustomerAppointmentRecurringTime">
        <![CDATA[
	        SELECT CASE WHEN EXISTS
			(SELECT *
				FROM appointment a, appointment_customers ac
				WHERE a.appointment_id = ac.appointment_id
				AND TO_CHAR(a.start_time,'d') = TO_CHAR(to_date(?,?),'d')
				AND TO_TIMESTAMP_TZ(TO_CHAR(a.start_time, ?), ?) BETWEEN TO_TIMESTAMP_TZ(?, ?) AND TO_TIMESTAMP_TZ(?, ?)
				AND (a.start_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?, ?)
				AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '|| ?, ?)
				OR a.end_time BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?, ?)
				AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time, ?)||' '||?, ?)
				OR TO_TIMESTAMP_TZ(TO_CHAR(a.start_time,?)||' '||?,?) BETWEEN a.start_time AND a.end_time
				OR TO_TIMESTAMP_TZ(TO_CHAR(a.end_time,?)||' '||?,?) BETWEEN a.start_time AND a.end_time)
				AND a.profile_id = ?
				AND a.canceled in (? , ?) 
				AND ac.customer_id = ?
				&REPLACECONDITION
	        )THEN 'false' else 'true' END flag FROM DUAL
	        ]]>
	</sql-query>
	
	<!-- GCSS-590 -->
	<sql-query name="timeoff.getTimeoffByTime">
        <![CDATA[
          SELECT {t.*}
		  FROM timeoff t
		  WHERE
		  (t.start_time BETWEEN TO_TIMESTAMP_TZ(?, ?) AND TO_TIMESTAMP_TZ(?, ?)
		  OR t.end_time BETWEEN TO_TIMESTAMP_TZ(?, ?) AND TO_TIMESTAMP_TZ(?, ?)
		  OR TO_TIMESTAMP_TZ(?, ?) BETWEEN t.start_time AND t.end_time
		  OR TO_TIMESTAMP_TZ(?, ?) BETWEEN t.start_time AND t.end_time)
		  AND t.instructor_id =?
         ]]>
		<return alias="t" class="com.guitarcenter.scheduler.model.Timeoff" />
	</sql-query>
	<sql-query name="timeoff.getTimeoffByRecurringTime">
        <![CDATA[
		SELECT CASE WHEN EXISTS
		(
          SELECT t.*
		  FROM timeoff t, ( &REPLACECONDITION ) temp
			WHERE
			(t.start_time BETWEEN TO_TIMESTAMP_TZ(temp.s, ?) AND TO_TIMESTAMP_TZ(temp.e, ?)
			OR t.end_time BETWEEN TO_TIMESTAMP_TZ(temp.s, ?) AND TO_TIMESTAMP_TZ(temp.e, ?)
			OR TO_TIMESTAMP_TZ(temp.s, ?) BETWEEN t.start_time AND t.end_time
			OR TO_TIMESTAMP_TZ(temp.e, ?) BETWEEN t.start_time AND t.end_time)
			AND t.instructor_id =?
		)THEN 'false' else 'true' END flag FROM DUAL
         ]]>
	</sql-query>
		
	<!-- GCSS-525 -->
	<sql-query name="timeoff.getTimeoffDTOByAvailabilityTime">
        <![CDATA[
		WITH a AS
			  (SELECT availability_id,
			    2 week_day,
			    monday_start_time start_time,
			    monday_end_time end_time
			  FROM availability
			  UNION
			  SELECT availability_id,
			    3,
			    TUESDAY_START_TIME ,
			    TUESDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    4,
			    WEDNESDAY_START_TIME ,
			    WEDNESDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    5,
			    THURSDAY_START_TIME ,
			    THURSDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    6,
			    FRIDAY_START_TIME ,
			    FRIDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    7,
			    SATURDAY_START_TIME ,
			    SATURDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    1,
			    SUNDAY_START_TIME ,
			    SUNDAY_end_TIME
			  FROM availability
			  )
		select a.start_time, a.end_time, i.instructor_id from a, instructor i where i.availability_id = a.availability_id and a.week_day  = TO_CHAR(to_date(:date, :format),'d')
		and i.location_id = :location
         ]]>
	</sql-query>
	
	<!-- GCSS-650 -->
	<sql-query name="onetime.checkOnetimeAvailabilityByProfileId">
        <![CDATA[
        SELECT CASE WHEN EXISTS
        (WITH a AS
			  (SELECT availability_id,
			    2 week_day,
			    monday_start_time start_time,
			    monday_end_time end_time
			  FROM availability
			  UNION
			  SELECT availability_id,
			    3,
			    TUESDAY_START_TIME ,
			    TUESDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    4,
			    WEDNESDAY_START_TIME ,
			    WEDNESDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    5,
			    THURSDAY_START_TIME ,
			    THURSDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    6,
			    FRIDAY_START_TIME ,
			    FRIDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    7,
			    SATURDAY_START_TIME ,
			    SATURDAY_end_TIME
			  FROM availability
			  UNION
			  SELECT availability_id,
			    1,
			    SUNDAY_START_TIME ,
			    SUNDAY_end_TIME
			  FROM availability
			  )
			SELECT a.start_time, a.end_time, a.week_day FROM a, location_profile l WHERE a.availability_id = l.availability_id
			AND a.week_day = TO_CHAR(to_date(?, ?),'d')
			AND TO_TIMESTAMP_TZ(?, ?) BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time, ?), ?) AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time, ?), ?)
			AND TO_TIMESTAMP_TZ(?, ?) BETWEEN TO_TIMESTAMP_TZ(TO_CHAR(a.start_time, ?), ?) AND TO_TIMESTAMP_TZ(TO_CHAR(a.end_time, ?), ?)
			AND l.profile_id = ?
		)THEN 'true' else 'false' END flag FROM DUAL
		]]>
	</sql-query>
	
	<!-- GCSS-684 -->
	<sql-query name="onetime.getOnetimeByTime">
        <![CDATA[
          SELECT {t.*}
		  FROM onetime t
		  WHERE TO_TIMESTAMP_TZ(?, ?) BETWEEN t.start_time AND t.end_time
		  AND TO_TIMESTAMP_TZ(?, ?) BETWEEN t.start_time AND t.end_time
		  AND t.instructor_id =?
         ]]>
		<return alias="t" class="com.guitarcenter.scheduler.model.Onetime" />
	</sql-query>
	
	<!-- GCSS-685 -->
	<sql-query name="onetime.checkOnetimeByTime">
        <![CDATA[
          SELECT {t.*}
		  FROM onetime t
		  WHERE
		  (t.start_time BETWEEN TO_TIMESTAMP_TZ(?, ?) AND TO_TIMESTAMP_TZ(?, ?)
		  OR t.end_time BETWEEN TO_TIMESTAMP_TZ(?, ?) AND TO_TIMESTAMP_TZ(?, ?)
		  OR TO_TIMESTAMP_TZ(?, ?) BETWEEN t.start_time AND t.end_time
		  OR TO_TIMESTAMP_TZ(?, ?) BETWEEN t.start_time AND t.end_time)
		  AND t.instructor_id =?
         ]]>
		<return alias="t" class="com.guitarcenter.scheduler.model.Onetime" />
	</sql-query>
	
	
	<!-- Added for Stored Procedure -->
	   <!-- <sql-query name="callRoomNonAvailableSlot" callable="true">
	
		<return alias="app" class="com.guitarcenter.scheduler.model.Appointment">
		   <return-property name="externalId" column="EXTERNAL_ID"/>
		 </return>    
	
		{? = CALL GETAPPOINTMENTS(?)}
	
		  	
	
    </sql-query>
    
    
    
 <sql-query name="callNASlotsFunc">
		
        	{ ? = call GET_NA_RM_SLTS_FNC(?,?,?,?) }
        	
        	<return-scalar column="noSlotsStr" type="org.hibernate.type.StringType"/>
        	
	</sql-query> -->
	
	
	<!--  Named Query for Stored Procedure -->
	<sql-query name="callNASlotsFunc" callable="true">
	
		<return alias="app" class="com.guitarcenter.scheduler.model.NativeString">
		   <return-property name="noSlotsStr" column="noSlotsStr"/>
		 </return>    
	
		{ ? = call GET_RM_SLTS_FNC(?,?,?) }
	
		  	
	
    </sql-query>
    
     <!-- GCSS-277 Start -->
    <sql-query name="callAppTabRecProcedure" callable="true">
 
  	<return alias="app" class="com.guitarcenter.scheduler.model.NativeString">
		   <return-property name="noSlotsStr" column="noSlotsStr"/>
		 </return> 
		{ ?=call GET_APPNT_BKP_FNC(?)}
 
    </sql-query>
    
    <sql-query name="callAppTabDropProcedure" callable="true">
 
  	<return alias="app" class="com.guitarcenter.scheduler.model.NativeString">
		   <return-property name="noSlotsStr" column="noSlotsStr"/>
		 </return> 
		{ ?=call GET_APPNT_BKP_TBL_DRP_FNC(?)}
 
    </sql-query>
    
    <sql-query name="callAppCustTabRecProcedure" callable="true">
 
  	<return alias="app" class="com.guitarcenter.scheduler.model.NativeString">
		   <return-property name="noSlotsStr" column="noSlotsStr"/>
		 </return> 
		{ ?=call GET_APPNT_CUST_BKP_FNC(?)}
 
    </sql-query>
    
    <sql-query name="callAppCustTabDropProcedure" callable="true">
 
  	<return alias="app" class="com.guitarcenter.scheduler.model.NativeString">
		   <return-property name="noSlotsStr" column="noSlotsStr"/>
		 </return> 
		{ ?=call GET_APPNT_CUST_BKP_TBL_DRP_FNC(?)}
 
    </sql-query>
    
    <sql-query name="callSpltFunc" callable="true">
	
		<return alias="app" class="com.guitarcenter.scheduler.model.NativeString">
		   <return-property name="noSlotsStr" column="noSlotsStr"/>
		 </return>    
	
		{ ? = call GET_RM_SLTS_SPLRMS_FNC(?,?,?) }
	
		  	
	
    </sql-query>
		
	 <!-- GCSS-277 End -->

</hibernate-mapping>