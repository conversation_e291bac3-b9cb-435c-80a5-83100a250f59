<?xml version="1.0" encoding="UTF-8"?>
<!-- Scheduler security configuration; relies on beans that have been defined
     in production.xml or develop.xml
-->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:sec="http://www.springframework.org/schema/security"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
	                       http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
		                   http://www.springframework.org/schema/security
		                   http://www.springframework.org/schema/security/spring-security-3.1.xsd">
		                         
    <!-- Define the basic security constraints for scheduler -->
    <sec:global-method-security secured-annotations="enabled" pre-post-annotations="enabled"/>
    <sec:http pattern="/login.jsp" security="none" />
    <sec:http pattern="/*/*/getActivityService.htm" security="none" />
    <sec:http pattern="/LessonsService/**" security="none" />
    <sec:http pattern="/InstructorService/**" security="none" />
    <sec:http pattern="/UpdateLessonService/**" security="none" />
    <sec:http pattern="/UpdateSingleAppt/**" security="none" />
    <sec:http pattern="/createAppointment/**" security="none" />
    <sec:http pattern="/createAppointmentWithCustomer/**" security="none" /> 
     <sec:http pattern="/updateAppointment/**" security="none" />  
    <sec:http pattern="/InstructorAvailabilty/**" security="none" />
    <sec:http pattern="/instructors/availability/**" security="none" />
    <sec:http pattern="/instructors/availability/simple/**" security="none" />
    <sec:http pattern="/InstructorActivity/**" security="none" />
    <sec:http pattern="/instructorportal/**" security="none" />
    <sec:http pattern="/ValidateAndGetLessonSeries/**" security="none" />
    <sec:http pattern="/LessonCancelService/**" security="none" />
    <sec:http pattern="/AppointmentStatusService/**" security="none" />
     <sec:http pattern="/getAppointmentDetailsByCustomer/**" security="none" />
    <sec:http pattern="/pauseServiceupdateAppointment/**" security="none" />
    <sec:http pattern="/css/**" security="none" />
    <sec:http pattern="/images/**" security="none" />
    <sec:http pattern="/js/**" security="none" />
    <sec:http pattern="/WEB-INF/views/error/**" security="none" />
    <sec:http pattern="/WEB-INF/views/includes/**" security="none" />
    <sec:http access-denied-page="/accessDenied.jsp" auto-config="false"
              entry-point-ref="loginUrlAuthenticationEntryPoint">
        <sec:intercept-url pattern="/**" access="IS_AUTHENTICATED_FULLY" />
        <sec:logout logout-success-url="/" invalidate-session="true"
		        logout-url="/j_spring_security_logout"/>
		<!-- The custom filter knows which bean to use based on information
		     embedded in the login form -->
		<sec:custom-filter position="FORM_LOGIN_FILTER" ref="gcssAuthenticationFilter"/>
	</sec:http>

    <bean id="loginUrlAuthenticationEntryPoint"
          class="org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint">
        <property name="loginFormUrl" value="/login.jsp"/>
    </bean>
    
    <!-- The GCSS authentication filter -->
    <bean id="gcssAuthenticationFilter"
          class="com.guitarcenter.scheduler.security.GCSSAuthenticationFilter">
        <property name="authenticationFailureHandler" ref="authenticationFailureHandler"/>
        <property name="authenticationSuccessHandler" ref="authenticationSuccessHandler"/>
        <property name="authenticationManager" ref="authenticationManager"/>
    </bean>
    
    <bean id="authenticationSuccessHandler"
          class="com.guitarcenter.scheduler.security.LoginAuthenticationSuccessHandler"/>
    <bean id="authenticationFailureHandler"
          class="com.guitarcenter.scheduler.security.LoginAuthenticationFailureHandler"/>
    
    <!-- Use a simple user authentication service -->
    <sec:user-service id="domesticUserService" properties="classpath:domestic.auth.dev"/>
    <sec:user-service id="retailUserService" properties="classpath:retail.auth.dev"/>
    <sec:user-service id="mfUserService" properties="classpath:mf.auth.dev"/>
    <bean id="domesticProvider"
          class="org.springframework.security.authentication.dao.DaoAuthenticationProvider">
        <property name="userDetailsService" ref="domesticUserService"/>
    </bean>
    <bean id="retailProvider"
          class="org.springframework.security.authentication.dao.DaoAuthenticationProvider">
        <property name="userDetailsService" ref="retailUserService"/>
    </bean>
          <bean id="mfProvider"
          class="org.springframework.security.authentication.dao.DaoAuthenticationProvider">
        <property name="userDetailsService" ref="mfUserService"/>
    </bean>     
    <!-- These beans wrap the 'real' domestic and retail providers.
    -->
    <bean id="domesticAuthenticationProvider"
          class="com.guitarcenter.scheduler.security.GCSSAuthenticationProviderDecorator">
        <property name="domain" value="DOMESTIC"/>
        <property name="authenticationProvider" ref="domesticProvider"/>
    </bean>
    <bean id="retailAuthenticationProvider"
          class="com.guitarcenter.scheduler.security.GCSSAuthenticationProviderDecorator">
        <property name="domain" value="RETAIL"/>
        <property name="authenticationProvider" ref="retailProvider"/>
    </bean>
       <bean id="mfAuthenticationProvider"
          class="com.guitarcenter.scheduler.security.GCSSAuthenticationProviderDecorator">
        <property name="domain" value="MF"/>
        <property name="authenticationProvider" ref="mfProvider"/>
    </bean>
    <sec:authentication-manager alias="authenticationManager">
        <sec:authentication-provider ref="domesticAuthenticationProvider"/>
        <sec:authentication-provider ref="retailAuthenticationProvider"/>
        <sec:authentication-provider ref="mfAuthenticationProvider"/>
    </sec:authentication-manager>
</beans>