# ${reportName}.(class)=org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView
# ${reportName}.url=/WEB-INF/views/reports/${reportName}.jasper
# 
# To use your report name replace placeholder(above).

#instructor schedule report
instructorScheduleReport.(class)=org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView
instructorScheduleReport.url=/WEB-INF/views/reports/instructor_report.jasper
instructorScheduleReport.reportDataKey=instructorReport

#instructor status schedule report
instructorStatusScheduleReport.(class)=org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView
instructorStatusScheduleReport.url=/WEB-INF/views/reports/instructor_status_report.jasper
instructorStatusScheduleReport.reportDataKey=instructorStatusReport

#rehearsalSchedule report
rehearsalScheduleReport.(class)=org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView
rehearsalScheduleReport.url=/WEB-INF/views/reports/rehearsalSchedule_report.jasper
masterScheduleReport.reportDataKey=parent_map

#rehearsalBooking report
rehearsalBookingReport.(class)=org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView
rehearsalBookingReport.url=/WEB-INF/views/reports/rehearsalBooking_report.jasper
rehearsalBookingReport.reportDataKey=parent_map

#masterSchedule report
masterScheduleReport.(class)=org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView
masterScheduleReport.url=/WEB-INF/views/reports/master_report.jasper
masterScheduleReport.reportDataKey=masterReport

#cancelledAppointment report
cancelledAppointmentReport.(class)=org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView
cancelledAppointmentReport.url=/WEB-INF/views/reports/cancelled_appointments_report.jasper
cancelledAppointmentReport.reportDataKey=cancelledAppointmentReport

#Added for NewInsAptReport _ June 2015 Enhancement
#instructor open appointments report
instructorOpenAppointmentsReport.(class)=org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView
instructorOpenAppointmentsReport.url=/WEB-INF/views/reports/instructor_open_appointments_report.jasper
instructorOpenAppointmentsReport.reportDataKey=openAppointmentsReport

#Added for GSSP-170, Conflicting Appointments by Instructor
conflictAppointmentsReportByInsrtuctor.(class)=org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView
conflictAppointmentsReportByInsrtuctor.url=/WEB-INF/views/reports/conflict_appointments_instructor_report.jasper
conflictAppointmentsReportByInsrtuctor.reportDataKey=conflictAppointmentsByInstructor

#Added for GSSP-170, Conflicting Appointments by Room
conflictAppointmentsReportByRoom.(class)=org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView
conflictAppointmentsReportByRoom.url=/WEB-INF/views/reports/conflict_appointments_room_report.jasper
conflictAppointmentsReportByRoom.reportDataKey=conflictAppointmentsByRoom

#For GSSP-161, Instructor outside appointments report
instructorOutsideAppointmentsReport.(class)=org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView
instructorOutsideAppointmentsReport.url=/WEB-INF/views/reports/instructor_outside_appointments_report.jasper
instructorOutsideAppointmentsReport.reportDataKey=outsideAppointmentsReport

#active Students report GSSP-185
activeStudentsReport.(class)=org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView
activeStudentsReport.url=/WEB-INF/views/reports/active_students_report.jasper
activeStudentsReport.reportDataKey=activeStudentsReport

#Student Check In report GSSP-203
studentCheckInReport.(class)=org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView
studentCheckInReport.url=/WEB-INF/views/reports/student_check_in_report.jasper
studentCheckInReport.reportDataKey=studentCheckInReport

#Student Check In report GSSP-205
inActiveStudentsReport.(class)=org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView
inActiveStudentsReport.url=/WEB-INF/views/reports/inactive_students_report.jasper
inActiveStudentsReport.reportDataKey=inActiveStudentsReport

#Added for GSSP-170, Conflicting Appointments by Instructor

#Instructor excel report

excelView.(class)=com.guitarcenter.scheduler.service.impl.ExcelBuilder
