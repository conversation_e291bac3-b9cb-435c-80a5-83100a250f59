<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<style type="text/css">
	body
	{ 
		border: thin solid gray;
		width: 750px;
	}	
	p
	{
		font-family: "Arial";
	}
</style>
</head>
<body>
<img src ="${emailHeaderImgUrl}"/>
<br/>
<br/>
&nbsp;&nbsp;${customerName},
<br/>
<br/>
<#switch serviceType>  
	<#case "newRehearsal">
	<#assign isRecurring=0>  
           &nbsp;&nbsp;Thank you for scheduling a rehearsal with Guitar Center. The details of your<br/>
           &nbsp;&nbsp;appointment are shown below.<br/>
           <#break>  
	<#case "newRehearsalRecurring">
	<#assign isRecurring=1>  
           &nbsp;&nbsp;Thank you for scheduling recurring rehearsals with Guitar Center. The details of your<br/>
           &nbsp;&nbsp;appointments are shown below.<br/>
           <#break>  
	<#case "newLesson">  
	<#assign isRecurring=0>
           &nbsp;&nbsp;Thank you for scheduling a lesson at Guitar Center. The details of your appointment<br/>
           &nbsp;&nbsp;are shown below.<br/>
           <#break>  
	<#case "newLessonRecurring">  
	<#assign isRecurring=1>
           &nbsp;&nbsp;Thank you for scheduling recurring lessons at Guitar Center. The details of your<br/>
           &nbsp;&nbsp;appointments are shown below.<br/>
           <#break>  
	
	<#case "modifyRehearsalAll">  
	<#assign isRecurring=1>
           &nbsp;&nbsp;The following recurring rehearsals have been modified, beginning with the date shown. The<br/>
           &nbsp;&nbsp;details of your new appointments are listed below.<br/>
           <#break>  
	
	<#case "modifyLessonAll">  
	<#assign isRecurring=1>
           &nbsp;&nbsp;The following recurring lessons have been modified, beginning with the date shown. The<br/>
		   &nbsp;&nbsp;details of your new appointments are listed below.<br/>
           <#break>  
	
	<#case "cancelRehearsalAll">
	<#assign isRecurring=1>
           &nbsp;&nbsp;The following recurring rehearsals have been cancelled, beginning with the date shown.<br/>
		   &nbsp;&nbsp;Please contact your local Guitar Center Studio when you're ready to reschedule.<br/>
           <#break> 
	
	<#case "cancelLessonAll">
	<#assign isRecurring=1>
			&nbsp;&nbsp;The following recurring lessons have been cancelled, beginning with the date shown. Please<br/>
			&nbsp;&nbsp;contact your local Guitar Center Studio when you're ready to reschedule.<br/>
           <#break> 
	<#default>
	<#assign isRecurring=0>  
			&nbsp;&nbsp;Your rehearsal is coming up at Guitar Center Studios. The details of your appointment are<br/>
			&nbsp;&nbsp;shown below.<br/>
</#switch>
<br/>
<br/>
&nbsp;&nbsp;Location: ${location}<br/>

&nbsp;&nbsp;Date: ${date}
<#if (isRecurring=1)>
, recurring
</#if>
<br/>
&nbsp;&nbsp;Duration: ${duration}<br/>
<#if serviceType == "modifyLessonAll"||serviceType == "cancelLessonAll" ||serviceType == "newLessonRecurring" ||serviceType == "newLesson">

&nbsp;&nbsp;Instructor Name: ${instructorName}<br/>
</#if>
<#if serviceType == "modifyLessonAll"||serviceType == "cancelLessonAll" ||serviceType == "newLessonRecurring" ||serviceType == "newLesson">
<#if serviceType == "modifyLessonAll" ||serviceType == "newLessonRecurring" ||serviceType == "newLesson">
&nbsp;&nbsp;Room Name: ${roomName}<br/>
</#if>
&nbsp;&nbsp;Lesson Type: ${activityName}<br/>

</#if>
<#if updatedBy?has_content>
&nbsp;&nbsp;Updated by: ${updatedBy}<br/>
</#if>

<br/>
<br/>
&nbsp;&nbsp;If you scheduled or updated online lesson(s), you will receive link soon.<br/> 
&nbsp;&nbsp;See you soon!<br/>
&nbsp;&nbsp;-Guitar Center<br/>
</body>
</html>
