<?xml version="1.0" encoding="UTF-8"?>
<!-- Ehcache configuration - production only
     
     Replication is enabled; do not use in a dev environment with many
     independent machines on the same sub-net!
-->
<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="http://ehcache.org/ehcache.xsd"
         updateCheck="false" monitoring="autodetect" dynamicConfig="true">
    
  <!-- Enable automatic RMI replication within sub-net -->
  <cacheManagerPeerProviderFactory
      class="net.sf.ehcache.distribution.RMICacheManagerPeerProviderFactory"
      properties="peerDiscovery=automatic, multicastGroupAddress=*********,multicastGroupPort=4446, timeToLive=1"/>
  <cacheManagerPeerListenerFactory
      class="net.sf.ehcache.distribution.RMICacheManagerPeerListenerFactory"/>
    
  <!-- Default cache -->
  <defaultCache maxEntriesLocalHeap="100" eternal="false"
                timeToIdleSeconds="120" timeToLiveSeconds="120"
                statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </defaultCache>

  <!-- Eternal caches; these are entities in limited number that change
       rarely and/or common to multiple locations
  -->
  <cache name="com.guitarcenter.scheduler.model.Activity"
         maxEntriesLocalHeap="20" eternal="true" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.CustomerStatus"
         maxEntriesLocalHeap="10" eternal="true" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.Instrument"
         maxEntriesLocalHeap="5" eternal="true" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.Role"
         maxEntriesLocalHeap="5" eternal="true" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.RoomNumber"
         maxEntriesLocalHeap="20" eternal="true" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.RoomSize"
         maxEntriesLocalHeap="5" eternal="true" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.RoomTemplate"
         maxEntriesLocalHeap="20" eternal="true" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.RoomType"
         maxEntriesLocalHeap="5" eternal="true" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.Service"
         maxEntriesLocalHeap="5" eternal="true" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>

  <!-- More volatile caches; regularly changing data. Mark the entities with
       a time-to-idle of 20 mins
  -->
  <cache name="com.guitarcenter.scheduler.model.Appointment"
         maxEntriesLocalHeap="1000" eternal="false"
         timeToIdleSeconds="1200" timeToLiveSeconds="0"
         memoryStoreEvictionPolicy="LRU" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.AppointmentSeries"
         maxEntriesLocalHeap="500" eternal="false"
         timeToIdleSeconds="1200" timeToLiveSeconds="0"
         memoryStoreEvictionPolicy="LRU" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.Availability"
         maxEntriesLocalHeap="100" eternal="false"
         timeToIdleSeconds="1200" timeToLiveSeconds="0"
         memoryStoreEvictionPolicy="LRU" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.Customer"
         maxEntriesLocalHeap="1000" eternal="false"
         timeToIdleSeconds="1200" timeToLiveSeconds="0"
         memoryStoreEvictionPolicy="LRU" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.Employee"
         maxEntriesLocalHeap="100" eternal="false"
         timeToIdleSeconds="1200" timeToLiveSeconds="0"
         memoryStoreEvictionPolicy="LRU" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.Instructor"
         maxEntriesLocalHeap="200" eternal="false"
         timeToIdleSeconds="1200" timeToLiveSeconds="0"
         memoryStoreEvictionPolicy="LRU" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.Location"
         maxEntriesLocalHeap="100" eternal="false"
         timeToIdleSeconds="1200" timeToLiveSeconds="0"
         memoryStoreEvictionPolicy="LRU" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.LocationProfile"
         maxEntriesLocalHeap="100" eternal="false"
         timeToIdleSeconds="1200" timeToLiveSeconds="0"
         memoryStoreEvictionPolicy="LRU" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.Person"
         maxEntriesLocalHeap="1000" eternal="false"
         timeToIdleSeconds="1200" timeToLiveSeconds="0"
         memoryStoreEvictionPolicy="LRU" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.PersonRole"
         maxEntriesLocalHeap="500" eternal="false"
         timeToIdleSeconds="1200" timeToLiveSeconds="0"
         memoryStoreEvictionPolicy="LRU" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.ProfileActivity"
         maxEntriesLocalHeap="500" eternal="false"
         timeToIdleSeconds="1200" timeToLiveSeconds="0"
         memoryStoreEvictionPolicy="LRU" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.ProfileService"
         maxEntriesLocalHeap="500" eternal="false"
         timeToIdleSeconds="1200" timeToLiveSeconds="0"
         memoryStoreEvictionPolicy="LRU" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
  <cache name="com.guitarcenter.scheduler.model.Room"
         maxEntriesLocalHeap="500" eternal="false"
         timeToIdleSeconds="1200" timeToLiveSeconds="0"
         memoryStoreEvictionPolicy="LRU" statistics="true">
    <cacheEventListenerFactory
        class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"/>
    <bootstrapCacheLoaderFactory
        class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
  </cache>
</ehcache>