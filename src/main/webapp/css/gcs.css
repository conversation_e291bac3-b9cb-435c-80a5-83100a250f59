/*Contatct css atTue Oct 22 2013 13:50:14 GMT+0800 (China Standard Time)*/
/**********************************************************************************************

Title: GCS
Date: June 2013

***********************************************************************************************
1.Reset
	1.1 Base
	1.2 Links
	1.3 Typography
	1.4 Lists
	1.5 Graphic
	1.6 Forms
	1.7 Html5 display definitions

2.Layout
	2.1 Gloabl Container
	2.2 Media query

3.Grid

4.Header

5.Container
	5.1 Login
	5.2 Site admin
	5.3 Common
	5.4 Instructor & staff

6.Navigation
	6.1 Top nav

7.Button
	7.1 Common
	7.2 Type
	7.3 Size
	7.4 Search button 
	7.5 Group buttons

8.Form
	8.1 Reset
	8.2 Size
	8.3 Form

9.Table
	9.1 Table set
	9.2 Tabel cell

10.Typography
	10.1 Global
	10.2 Link
	10.3 Title

11.Modal
	11.1 Modal set
	11.2 Modal header
	11.3 Modal body
	11.4 Modal footer

12.Notification

13.Util
	
14.Clendar
	14.1 Header
		1.1 Header buttons
	14.2 Body
		2.1 Left bar
		2.2 Right content

15.Pikaday

16.Select2

17.Print

**********************************************************************************************

1. Reset                                                                                    
============================================================================================*/
/* 1.1 Base
----------------------------------------------------------*/
*,
*:before,
*:after { 
	margin: 0;
	padding: 0;
	text-decoration: none;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

html{
	font-size: 100%;
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;
}

body{
	font-size: 100%;
	height: 100%;
}

/* 1.2 Links
----------------------------------------------------------*/
a{cursor: pointer;}

a:hover, a:active{outline: 0;}

a:focus{outline: none;}

/* 1.3 Typography
----------------------------------------------------------*/
fieldset, abbr, acronym{border: 0;}

address, caption, cite, code, dfn, em, th, var, small{
	font-style: normal; 
	font-weight: normal;
}

caption, th{text-align: left;}

/*h1, h2, h3, h4, h5, h6{font-weight: normal;}*/

q:before, q:after{content: ''; content: none;}

iframe{border: none;}

b, strong{font-weight: 700;}

/* 1.4 Lists
----------------------------------------------------------*/
ol,ul,li{list-style: none;}
ol > li, ul > li, dl > dt, dl > dd{float: left;}

/* 1.5 Graphic
----------------------------------------------------------*/
img {
	border: 0; 
	vertical-align: middle;
	max-width: 100%;
}

/* 1.6 Forms
----------------------------------------------------------*/
table{
	border-collapse: collapse;
	background-color: transparent;
	border-spacing: 0;
	max-width: 100%;
}

textarea{resize: none;}

button, input[type="button"], input[type="reset"], input[type="submit"], [role="button"]{
	cursor: pointer;
	-webkit-appearance: button;
	-moz-appearance: button;
}

/* 1.7 Html5 display definitions
----------------------------------------------------------*/
article, aside, details, figcaption, figure, footer, header, hgroup, nav, section{display: block;}

audio, canvas, video{
	display: inline-block; 
	*display: inline; 
	*zoom: 1;
}

audio:not([controls]){display: none;}

[hidden]{ display: none;}
/**********************************************************************************************
2.Layout
	2.1 Gloabl Container
	2.2 Media query
**********************************************************************************************/

/* 2.1 Gloabl Container
=======================================================================*/
.container {
	width: 985px;
	margin-right: auto;
	margin-left: auto;	
}

.container:before,
.container:after {
  display: table;
  line-height: 0;
  content: '';
}

.container:after {
  clear: both;
}

/* 2.2 Mediq query
==========================================================================*/
@media only screen and (min-width: 768px) {
	.container {
		width: 985px;
		margin-right: auto;
		margin-left: auto;	
	}
}


@media only screen and (min-width: 1840px) {
	.container {
		width: 1240px;
		margin-right: auto;
		margin-left: auto;	
	}
}

@media only screen and (max-width: 767px) {
	.container {
		width: 640px;
		margin-right: auto;
		margin-left: auto;	
	}
}

@media only screen and (max-width: 480px) {
	.container {
		width: 480px;
		margin-right: auto;
		margin-left: auto;	
	}
}
/**********************************************************************************************
3.Grid
**********************************************************************************************/
.row {
	width: 100%;
}

.row:before,
.row:after {
	display: table;
	line-height: 0;
	content: "";
}

.row:after {
	clear: both;
}

.row [class*="span"] {
	display: block;
	float: left;
	width: 100%;
	min-height: 16px;
}
.row .span27 {
	width: 64.44689%;
}

.row .span12 {
	width: 100%;
}

.row .span11 {
	width: 91.48936170212765%;
}

.row .span10 {
	width: 82.97872340425532%;
}

.row .span9 {
	width: 74.46808510638297%;
}

.row .span8 {
	width: 65.95744680851064%;
}

.row .span7 {
	width: 57.44680851063829%;
}

.row .span6 {
	width: 48.93617021276595%;
}

.row .span5 {
	width: 40.42553191489362%;
}

.row .span4 {
	width: 31.914893617021278%;
}

.row .span3 {
	width: 23.404255319148934%;
}

.row .span2 {
	width: 14.893617021276595%;
}

.row .span1 {
	width: 6.382978723404255%;
}

.row .offset12 {
	margin-left: 104.25531914893617%;
}

.row .offset11 {
	margin-left: 95.74468085106382%;
}

.row .offset10 {
	margin-left: 87.23404255319149%;
}

.row .offset9 {
	margin-left: 78.72340425531914%;
}

.row .offset8 {
	margin-left: 70.2127659574468%;
}

.row .offset7 {
	margin-left: 61.70212765957446%;
}

.row .offset6 {
	margin-left: 53.191489361702125%;
}

.row .offset5 {
	margin-left: 44.68085106382979%;
}

.row .offset4 {
	margin-left: 36.170212765957444%;
}

.row .offset3 {
	margin-left: 27.659574468085104%;
}

.row .offset2 {
	margin-left: 19.148936170212764%;
}

.row .offset1 {
	margin-left: 10.638297872340425%;
}
/**********************************************************************************************
4.Header
**********************************************************************************************/

.header {
	height: 113px;
	width: 100%;
	background:-moz-linear-gradient(top, #ffffff, #e5e5e5);
	background:-webkit-linear-gradient(top, #ffffff, #e5e5e5);
	background:-ms-linear-gradient(top, #ffffff, #e5e5e5);
	background:linear-gradient(top, #ffffff, #e5e5e5);
	border-bottom: 1px solid #b8b8b8;
}

.logo {
	display: block;
	width: 265px;
	height: 90px;
	float: left;
	margin-top: 12px;
	background: url("../images/logo.png") no-repeat 0 0 transparent;
}

.header-right {
	float: right;
	margin-top: 40px;
}

.header-right h4 {
	text-align: right;
	margin-top: 5px;
	font-size: 18px;
}

.profile-pic {
	display: block;
	float: left;
	padding: 1px;
	width: 40px;
	margin-right: 10px;
	-webkit-box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.2);
	box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.2);
	margin-top: 5px;
}


/*********************************************************************************************

5.Container
	5.1 Login
	5.2 Site admin
	5.3 Common
	5.4 Instructor & staff
**********************************************************************************************/

/*5.1 Login
==============================================================*/

.login-wrapper {
	background: url("../images/bg-login.png") no-repeat 622px 13px #FFFFFF;
	min-height: 680px;
}

.login-panel {
	width: 470px;
	margin-left: auto;
	margin-right: auto;
	margin-top: 33px;
	border: 1px solid #d8d5d5;
	border-radius: 3px;
	padding: 20px 83px;
	background: url("../images/bg-noise-1.png") repeat 0 0 transparent;
}

.login-panel .form-actions a {
	margin-left: 35px;
}

/* 5.2 Site admin
===============================================================================*/

.tab-title {
	width: 100%;
	padding: 20px 0 10px;
	border-bottom: 1px solid #e0e0e0;
	background: url("../images/bg-noise-1.png") repeat 0 0 transparent;
	margin-bottom: 12px;
}

.tab-title h1 {
	font-size: 18px;
	padding-left: 18px;
}

.box {
	border-radius: 4px;
	-webkit-border-radius: 4px;
	border: 1px solid #cccccc;
	margin-bottom: 20px;
}

.box-header {
	color: #999999;
	font-size: 16px;
	font-weight: bold;
	padding: 8px 13px;
	border-bottom: 1px solid #cccccc;
	background:-moz-linear-gradient(top, #f1f1f2, #dde0e3);
	background:-webkit-linear-gradient(top, #f1f1f2, #dde0e3);
	background:linear-gradient(top, #f1f1f2, #dde0e3);
	-webkit-border-radius: 4px 4px 0 0;
	border-radius: 4px 4px 0 0;
}

.box-header a {
	display: block;
	color: #cc0000;	
}

.box-header a:hover,
.box-header a:active,
.box-header a.active {
	color: #999999;
}

.box-body {
	padding: 15px 20px 25px;
}

.box-body .span6:first-child {
	border-right: 1px solid #cccccc;
}

.box-body .span6:last-child {
	padding-left: 20px;
}

.box-body .control-group {
	margin-bottom: 15px;
}

.activity-selected,
.service-selected {
	margin-right: 10px;
}

.icon-edit {
	background: url("../images/icons.png") 0 0 no-repeat transparent;
	display: block;
	height: 18px;
	width: 18px;
	margin-right: auto;
	margin-left: auto;
}

.tab-content {
	display: none;
}

.tab-content.active {
	display: block;
}

/* 5.3 Common
---------------------------------------------------------*/

section {
	margin-bottom: 35px;
}

section table[class*="table-list"]:nth-child(even) th {
	background-color: #f1f1f1;
}

/* 5.4 Instructor & staff
==========================================================================*/
.warn-tip {
	display: none;
	position: absolute;
	background: #cc0000;
	color: #fff;
	width: 175px;
	box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.6);
	z-index: 3;
}

.warn-tip .warn-wrap {
	position: relative;
}

.warn-tip .warn-info {
	padding: 10px;
}

.warn-tip .tri {
	position: absolute;
	left: 135px;
	width: 0px;
	height: 0px;
	border-style: solid;
	border-width: 10px 10px 0 10px;
	border-color: #cc0000 transparent transparent transparent;
}

/* 5.5 Report
==========================================================*/
.icon-calendar {
    display: block;
    background: url("../images/icons.png") no-repeat -40px -2px transparent;
    height: 16px;
    width: 16px;
    margin: 5px 0 0 15px;
}
/**********************************************************************************************

6.Navigation
	6.1 Top nav
**********************************************************************************************/
/* 6.1 Top nav 
=========================================================================================*/

.top-nav {
	background-color: #cc0000;
	-webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.4),
						inset 0px 2px 2px 0 rgba(0, 0, 0, 0.4);
	box-shadow: 0px 2px 2px 0 rgba(0, 0, 0, 0.4),
				inset 0px 2px 2px 0 rgba(0, 0, 0, 0.4);
	height: 40px;
	position: relative;
	top: -1px;
}

.top-nav:after {
	content: "";
	display: table;
	clear: both;
}

.top-nav-list {
	float: left;
}

.top-nav-list li {
	float: left;
	padding-right: 1px;
	background-color: #4d0606;
}

.top-nav-list li:first-child {
	border-left: 1px solid #ff0000;
	padding-left: 1px;
}

.top-nav-list li:last-child {
	border-right: 1px solid #ff0000;
}

.top-nav a {
	-webkit-box-shadow: inset 0px 2px 1px 0px rgba(0, 0, 0, 0.4);
	box-shadow: inset 0px 2px 1px 0px rgba(0, 0, 0, 0.4);
	display: block;
	background-color: #CC0000;
	padding: 10px 15px;
	border-left: 1px solid #ff0000;
	border-right: 1px solid #ff0000;
	color: #fff;
	font-size: 16px;
	font-weight: bold;
}

.top-nav-list li.active a,
.top-nav a:hover {
	background-color: #333333;
	border-right-color: #4d0606;
	border-left-color: #4d0606;
	color: #fff;
}
/**********************************************************************************************

7.Button
	7.1 Common
	7.2 Type
	7.3 Size
	7.4 Search button 
	7.5 Group buttons
**********************************************************************************************/

/* 7.1 Button common
----------------------------------------------------------*/
/*For GSSP-241 */ 
.btn1 {
	display: inline-block;
	padding: 5px 7px 7px;
	margin-bottom: 0;
	font-size: 14px;
	font-weight: bold;
	line-height: 16px;
	text-align: center;
	text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
	vertical-align: middle;
	cursor: pointer;
	background-color: #eef0f1;
	background-image: -moz-linear-gradient(top, #eef0f1, #cecfd1);
	background-image: -webkit-linear-gradient(top, #eef0f1, #cecfd1);
	background-image: linear-gradient(top, #eef0f1, #cecfd1);
	background-repeat: repeat-x;
	box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
	border: none;
	-webkit-border-radius: 3px;
	border-radius: 3px;
}
.btn {
	display: inline-block;
	padding: 5px 10px 7px;
	margin-bottom: 0;
	font-size: 14px;
	font-weight: bold;
	line-height: 16px;
	text-align: center;
	text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
	vertical-align: middle;
	cursor: pointer;
	background-color: #eef0f1;
	background-image: -moz-linear-gradient(top, #eef0f1, #cecfd1);
	background-image: -webkit-linear-gradient(top, #eef0f1, #cecfd1);
	background-image: linear-gradient(top, #eef0f1, #cecfd1);
	background-repeat: repeat-x;
	box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
	border: none;
	-webkit-border-radius: 3px;
	border-radius: 3px;
}

.btn:hover,
.btn:focus,
.btn:active,
.btn.active,
.btn.disabled {
	background-color: #cecfd1;
}

.btn[disabled] {
	opacity: 0.6;
	cursor: default;
}

.btn:hover,
.btn:focus {
	text-decoration: none;
	background-position: 0 -15px;
	-webkit-transition: background-position 0.1s linear;
	-moz-transition: background-position 0.1s linear;
	transition: background-position 0.1s linear;
}

/* 7.2 Button type
-----------------------------------------------------------------*/
.btn-important {
	text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
	color: #ffffff;
	background-image: -moz-linear-gradient(top, #cb0000, #9b0000);
	background-image: -webkit-linear-gradient(top, #cb0000, #9b0000);
	background-image: linear-gradient(top, #cb0000, #9b0000);
}

.btn-important:hover,
.btn-important:focus,
.btn-important:active,
.btn-important.active,
.btn-important.disabled,
.btn-important[disabled] {
	background-color: #9b0000;
}

.btn-primary {
	color: #333;
	background-image: -moz-linear-gradient(top, #fefeff, #e7e7e8);
	background-image: -webkit-linear-gradient(top, #fefeff, #e7e7e8);
	background-image: linear-gradient(top, #fefeff, #e7e7e8);
	border: 1px solid #b6b5b5;
	border-radius: 2px;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.btn-primary.disabled,
.btn-primary[disabled] {
	background-color: #e7e7e8;
}

/* 7.3 Button size
-----------------------------------------------------------------*/
.btn.btn-small {
	margin-top: 2px;
	font-size: 12px;
	padding: 4px 4px 6px;
	line-height: 9px;
	box-shadow: 0 -1px 0 0 #E2E1E1;
}

.btn.btn-medium {
	box-shadow: 0 -1px 0 0 #E2E1E1;
    font-size: 12px;
    line-height: 9px;
    margin-top: 0;
    padding: 6px 10px 8px;
}

.btn.btn-large {
	font-size: 16px;
	padding: 9px 25px;
	line-height: 20px;
}
/*For GSSP-241 */ 
.btn.btn-long1 {
	padding: 5px 20px 7px;
}
.btn.btn-long {
	padding: 5px 25px 7px;
}

/* 7.4 Search button
----------------------------------------------------------------*/
.btn-search {
	float: left;
	height: 33px;
	width: 37px;
	border: none;
	color: #fff;
	font-size: 16px;
	font-weight: bold;
	-webkit-border-radius: 0px 3px 3px 0;
	-moz-border-radius: 0px 3px 3px 0;
	border-radius: 0px 3px 3px 0;
	background-image: -moz-linear-gradient(top, #c71111, #de2c2c);
	background-image: -webkit-linear-gradient(top, #c71111, #de2c2c);
	background-image: linear-gradient(top, #c71111, #de2c2c);
	background-repeat: : repeat-y;
}

.icon-search {
	background: url("../images/icons.png") no-repeat 0px -16px transparent;
	display: block;
	height: 16px;
	width: 21px;
	margin-left: auto;
	margin-right: auto;
}

/* 7.5 Group buttons
---------------------------------------------------------------*/

.btn-group {
	display: inline-block;
    font-size: 0;
    position: relative;
    vertical-align: middle;
    white-space: nowrap;
}

.btn-group .btn {
	font-size: 12px;
	border-radius: 0;
	background-image: -moz-linear-gradient(top, #fdfdfd, #eeeeee);
	background-image: -webkit-linear-gradient(top, #fdfdfd, #eeeeee);
	background-image: linear-gradient(top, #fdfdfd, #eeeeee);
  	border-bottom: 1px solid #D3D3D3;
    border-right: 1px solid #D3D3D3;
    border-top: 1px solid #D3D3D3;
    text-shadow: none;
}

.btn-group .btn:hover,
.btn-group .btn.active,
.btn-group .btn.disabled,
.btn-group .btn[disabled] {
	background-color: #757575;
	background-position: 0 0;
	border-color: #484848;
	color: #fff;
	box-shadow: 0 0 13px 10px rgba(0, 0, 0, 0.75) inset;
}

.btn-group > .btn:first-child {
    border-bottom-left-radius: 2px;
    border-top-left-radius: 2px;
    border-left: 1px solid #D3D3D3;
}

.btn-group > .btn:last-child {
	border-bottom-right: 2px;
	border-top-right: 2px;
}
/**********************************************************************************************

8.Form
	8.1 Reset
	8.2 Size
	8.3 Form
**********************************************************************************************/

/* 8.1 Reset 
========================================================================*/
input, select, textarea {
	border: 2px solid #d9d9d9;

}

select {
	width: 133px;
}

input {
	padding: 2px 5px;
}

/* 8.2 Size
===========================================================================*/

.input-mini {
	width: 23px;
}

.input-small {
	width: 75px;
}

.input-xsmall{
	width: 100px;
}

.input-medium {
	width: 210px;
}

.input-large {
	width: 270px;
}

.input-xlarge {
	width: 300px;
}

.input-xxlarge {
	
}

.select-mini {
	/*width: 80px;*/
}

.select-small {
	width: 80px;
}

.select-medium {
	width: 210px;
}

.select-large {
	width: 300px;
}

/* 8.3 Form 
================================================================*/

legend {
	font-weight: bold;
	font-size: 2em;
	display: block;
	padding: 0;
	margin-bottom: 20px;
}

.form-horizontal {

}

.control-group {
	margin-bottom: 10px;
}

.form-inline label {
	display: inline-block;
	font-weight: bold;
    margin-bottom: 5px;
}

.form-horizontal label {
	line-height: 24px;
	/*width: 133px;*/
	width: 150px;
	font-weight: bold;
	display: block;
	float: left;
}

.form-horizontal .controls {
	/*float: left;*/
	/*margin-left: 10px;*/
	margin-left: 160px;
}

.control-group:before,
.control-group:after {
	display: table;
	line-height: 0;
	content: "";
}

.control-group:after {
	clear: both;
}

.form-actions {
	margin-top: 30px;
	padding: 10px 0;
}

.form-actions:before,
.form-actions:after {
	display: table;
	line-height: 0;
	content: "";
}

.form-actions:after {
	clear: both;
}

.form-search {
	
}

.search-query,
.form-search .tt-hint {
	float: left;
	-webkit-border-radius: 3px 0 0 3px;
	-moz-border-radius: 3px 0 0 3px;
	border-radius: 3px 0 0 3px;
	border: none;
	height: 32px;
	width: 258px;
}

.form-search .tt-hint:focus {
	box-shadow: none;
}

.tt-dropdown-menu {
	width: 100%;
	max-height: 200px;
	background: #fff;
	overflow-x: hidden;
	overflow-y: auto;
    box-shadow: 0 1px 3px 1px rgba(0, 0, 0, 0.3);
    margin-left: 1px;
    margin-top: 1px
}

.tt-suggestions a {
    padding: 3px 7px 4px;
	font-size: 13px;
	color: #333333;
	display: block;
	line-height: 14px;
}

.tt-is-under-cursor a,
.tt-suggestions a:hover {
	color: #333333;
	background: #ddd;
}

.icheckbox {
	background: url("../images/icons.png") no-repeat -1px -77px transparent;
    cursor: pointer;
    display: block;
    height: 17px;
    width: 13px;
    left: 0;
    position: absolute;
    top: -1px;
}

.icheckbox.checked {
	background-position: -16px -77px;
}

.icheckbox.disabled {
	cursor: default;
    opacity: 0.3;
}

.form-msg {
    border: 2px solid #bbbcbd;
    text-align: center;
    padding: 6px 0;
    background: #a2ea94;
    margin-bottom: 12px;
}

.form-msg.error {
    background: #CC0000;
    color: #fff;
}
/**********************************************************************************************

9.Table
	9.1 Table set
	9.2 Tabel cell
**********************************************************************************************/
/* 9.1 Tabel set
=========================================================================================*/

.table {
	width: 100%;
	border: 1px solid #d0d0d0;
	border-top-width: 0;
	border-collapse: separate;
}

.table-fixed {
	table-layout:fixed;
}

.table-list {
	border-top: 0;
}

.table.table-list th,
.table.table-list td {
	border-top: 0;
	padding: 12px 6px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.table.table-list th.cell-action {
	padding: 3px 10px;
}

.table.table-list .disabled th.cell-action a {
	color: #CCCCCC;
}

.table.table-list .disabled th.cell-action .icon-edit {
	background-position: -20px 0;
}

.table caption {
	-webkit-border-radius: 3px 3px 0 0;
	-moz-border-radius: 3px 3px 0 0;
	border-radius: 3px 3px 0 0;
	margin-bottom: -1px;
	height: 50px;
	line-height: 35px;
	padding: 8px 12px;
	text-align: left;
	font-size: 16px;
	font-weight: bold;
	color: #fff;
	background: -moz-linear-gradient(top, #3B3B3B, #333333);
	background: -webkit-linear-gradient(top, #3B3B3B, #333333);
	background: linear-gradient(top, #3B3B3B, #333333);
}

.table th,
.table td {
	text-align: left;
	vertical-align: top;
	border-top: 1px solid #d0d0d0;
	border-left: 1px solid #d0d0d0;
}

.table td {
	padding: 12px 10px;
	word-wrap: break-word;
}

.table th:first-child,
.table td:first-child {
	border-left: 0;
}

.table thead th {
	height: 30px;
	padding: 7px 10px;
	font-weight: bold;
	background: -moz-linear-gradient(top, #FFFFFF, #D9DADB);
	background: -webkit-linear-gradient(top, #FFFFFF, #D9DADB);
	background: linear-gradient(top, #FFFFFF, #D9DADB);
}

.table tbody > tr:nth-child(even) {
	background-color: #f1f1f1;
}

.table tr.table-edit-panel {
	background-color: #ffffff;
}

.table tr.table-edit-panel td {
	border-top: 1px solid #d0d0d0;
	white-space: normal;
}

.table tr.table-edit-actions td {
	box-shadow: 0px -1px 4px -1px rgba(0, 0, 0, 0.5);
	-webkit-box-shadow: 0px -1px 4px -1px rgba(0, 0, 0, 0.5);
	-moz-box-shadow: 0px -1px 4px -1px rgba(0, 0, 0, 0.5);
	background-color: #e7e8e8;
}

.table .disabled {
	color: #cccccc;
}

.table .cell-action {
	width: 4.4%;
	text-align: center;
}

.icon-remove {
	color: #fff;
	background: #B3B3B3;
	font-size: 12px;
	height: 14px;
	text-align: center;
	font-weight: bold;
	line-height: 14px;
	width: 14px;
	display: block;
	border-radius: 14px;
	float: left;
	margin-right: 7px;
}

.instructor-list .js-availability-wrap .span2 {
	width: 35px;
}

/* 9.2 Table cell
===============================================================================*/

.table .cell-120 {
	width: 120px;
}

.table .cell-90 {
	width: 90px;
}

.table .cell-70 {
	width: 70px;
}

.table .cell-60 {
	width: 60px;
}

.table .cell-1 {
	width: 6.382978723404255%;
}

.table .cell-50 {
	width: 50px;
}

.table .cell-100 {
	width: 100px;
}

.table .cell-115 {
	width: 115px;
}

.table .cell-116 {
	width: 116px;
}

.table .cell-210 {
	width: 210px;
}

.table .cell-220 {
	width: 220px;
}

.table .cell-160 {
	width: 160px;
}

.table .cell-150 {
	width: 150px;
}

.table .cell-170 {
	width: 170px;
}

.table .cell-190 {
	width: 190px;
}

.table .cell-250 {
	width: 250px;
}

.table .cell-320 {
	width: 320px;
}

.table .cell-590 {
	width: 590px;
}
/**********************************************************************************************

10.Typography
	10.1 Global
	10.2 Link
	10.3 Title
**********************************************************************************************

/* 10.1 Global
=====================================================*/

body {
	color: #333333;
	font-family: Arial;
	font-size: 13px;
}

/* 12.2 Link 
=======================================================*/
a {
	color: #cc0000;
}

a:hover,
a:focus {
	color: #b30000;
}

/* 10.3 Title
==========================================================*/
h1 {

}

h2 {

}

h3 {

}

h4 {
	font-size: 1.4em;
}

h5 {
	font-size: 1.2em;
}

h6 {
	font-size: 1em;
}
/**********************************************************************************************

11.Modal
    11.1 Modal set
	11.2 Modal header
	11.3 Modal body
	11.4 Modal footer
**********************************************************************************************/

/* 11.1 Modal set
----------------------------------------------------------*/

.modal {
	position: absolute;
	display: none;
	border: 1px solid #9a9a9a;
	background: #fff;
	z-index: 17;
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.67);
}

#modal_app {
    width: 470px;
}

/*Changes made for GSSP-241*/
#modal_conflicting_appointment {
    width: 800px;
}

#modal_edithour,
#modal_set_availability{
    width: 530px;
}

#modal_set_timeoff{
	width: 700px;
}

#modal_profile_timeoff{
	width: 777px;
}

#modal_set_availability{
	width: 610px;
}

#modal_set_availability.modal_one_time_availability{
	width: 850px;
	left: 310px !important;
}

.mask {
    z-index: 16;
    position: fixed;
    height: 100%;
    width: 100%;
    opacity: 0;
    display: none;
    top: 0;
}

.bootbox {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow-x: auto;
    position: fixed;
    background: transparent;
    border: none;
}

.bootbox .modal-dialog {
    width: 495px;
    margin-left: auto;
    margin-right: auto;
    padding-top: 200px;
    left: 50%;
}

.bootbox .modal-header:before,
.bootbox .modal-header:after {
    display: table;
    line-height: 0;
    content: "";
}

.bootbox .modal-header:after {
    clear: both;
}

.bootbox .bootbox-close-button {
    background: url("../images/icons.png") no-repeat scroll -40px -20px transparent;
    border: medium none;
    color: #FFFFFF;
    float: right;
    font-size: 20px;
    height: 20px;
    line-height: 2px;
    margin: 5px;
    padding-top: 0;
    text-align: center;
    width: 20px;
}

.bootbox .modal-title {
    float: left;
    color: #CC0000;
    font-size: 17px;
}

.bootbox .modal-content {
    background: #fff;
    box-shadow: 0 1px 2px 1px rgba(0, 0, 0, 0.6);
}

.bootbox .modal-body {
    font-weight: bold;
}

.bootbox .modal-footer {
    padding: 12px;
}

.bootbox .modal-footer .btn + .btn {
    margin-left: 10px;
}


/* 11.2 Modal header
----------------------------------------------------------*/
.modal-header {
	background-color: #ECECED;
    border-bottom: 1px solid #DDDDDE;
    font-size: 16px;
    font-weight: bold;
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    position: relative;
}

.modal-header h5 {
    font-size: 16px;
}

.modal-close {
	background: url("../images/icons.png") repeat scroll -40px -20px transparent;
    color: #FFFFFF;
    font-size: 12px;
    font-weight: normal;
    height: 20px;
    line-height: 20px;
    position: absolute;
    right: 8px;
    text-align: center;
    top: 6px;
    width: 20px;
}

/* 11.3 Modal body
------------------------------------------------------------*/
.modal-body {
	padding: 12px;
}

.modal-msg {
    border: 2px solid #bbbcbd;
    text-align: center;
    padding: 6px 0;
    background: #a2ea94;
    margin-bottom: 12px;
}
/*For GSSP-241 */ 
.modal-msg-app {
    border: 2px solid #bbbcbd;
    text-align: center;
    padding: 6px 0;
    background: #CC0000;
    margin-bottom: 12px;
}

.modal-msg.error {
    background: #CC0000;
    color: #fff;
}
/*For GSSP-144 Issue # 2  */
.modal-msg1 {
    text-align: center;
    padding: 6px 0;
    color: #CC0000;
    font-style: italic;
    font-weight: bold;
    margin-bottom: 1px;
}

/*For GSSP-170 */
#rep-modal {
    display: none;
    position: absolute;
    top: 45%;
    left: 46%;
    width: 300 px;	
    height: 300 px;	
    padding:0px 0px 0px;
    border: 0px solid #ababab;
    box-shadow:0px 0px 0px #ababab;
    border-radius:20px;
    background-color: white;
    z-index: 1002;
    text-align:center;
    overflow: auto;
}

/*For GSSP-170 */
#rep-fade {
    display: none;
    position:absolute;
    top: 0%;
    left: 0%;
    width: 100%;
    height: 50000%;
    background-color: #ababab;
    z-index: 1001;
    -moz-opacity: 0.8;
    opacity: .70;
    filter: alpha(opacity=80);
}

#modal_app .select2-container {
    width: 210px;
}

#modal_app .time-choice .select2-container {
    width: 145px;
}

.modal-body .form-horizontal label {
    width: 120px;
}

.modal-body .form-horizontal .controls {
    margin-left: 5px;
}

#modal_edithour .btn-group,
#modal_set_availability .btn-group,
#modal_set_timeoff .btn-group {
    box-shadow: 0 -1px 1px 0 rgba(0, 0, 0, 0.15);
}

#modal_profile_timeoff .btn-group {
    box-shadow: 0 -1px 1px 0 rgba(0, 0, 0, 0.15);
}

#modal_edithour .btn-group .btn,
#modal_set_availability .btn-group .btn {
    padding: 4px 8px 6px;
    width: 46px;
}

#modal_set_availability .dayDisabled{
    opacity: 0.5;
}

#modal_edithour .time-wrap,
#modal_set_availability .time-wrap{
    float: left;
    width: 150px;
    border: 2px solid #bfc0c1;
}

#modal_set_timeoff .time-wrap {
    float: left;
    width: 105px;
    border: 2px solid #d9d9d9;
}

#modal_profile_timeoff .time-wrap {
    float: left;
    width: 150px;
    border: 2px solid #bfc0c1;
}

#modal_profile_timeoff .row {
	width: 111%;
}

#modal_edithour .time-wrap:last-child,
#modal_set_availability .time-wrap:last-child {
    margin-left: 30px;
}

#modal_set_timeoff .time-wrap:last-child {
    margin-left: 0px;
}

#modal_profile_timeoff .time-wrap:last-child {
    margin-left: 22px;
}


#modal_profile_timeoff .time-wrap .pre,#modal_edithour .time-wrap .pre, 
#modal_set_availability .time-wrap .pre,
#modal_set_timeoff .time-wrap .pre {
    width: 46px;
    height: 22px;
    font-weight: bold;
    padding-left: 5px;
    line-height: 21px;
    display: block;
    float: left;
    border-right: 1px solid #bfc0c1;
    background:-moz-linear-gradient(top, #fdfdfd, #f1f1f1);
    background:-webkit-linear-gradient(top, #fdfdfd, #f1f1f1);
    background:linear-gradient(top, #fdfdfd, #f1f1f1);
}

/* For GSSP-161 */
#modal_profile_timeoff .pre,
#modal_set_availability .pre,
#report_form  .pre,
#modal_set_timeoff .pre {
    width: 46px;
    height: 22px;
    font-weight: bold;
    padding-left: 5px;
    line-height: 21px;
    display: block;
    float: left;
}

#modal_profile_timeoff .select2-container,
#modal_edithour .select2-container, 
#modal_set_availability .select2-container,
#modal_set_timeoff .select2-container {
    float: left;
    width: 100px;
}


#modal_edithour .select2-container .select2-choice > .select2-chosen,
#modal_set_availability .select2-container .select2-choice > .select2-chosen,
#modal_set_timeoff .select2-container .select2-choice > .select2-chosen{
    margin-right: 0;
}

#modal_profile_timeoff .select2-container .select2-choice > .select2-chosen{
    margin-right: -7px;
}

#modal_profile_timeoff .select2-container .select2-choice ,
#modal_edithour .select2-container .select2-choice,
#modal_set_availability .select2-container .select2-choice,
#modal_set_timeoff .select2-container .select2-choice {
    border: none;
}

#modal_profile_timeoff .select2-arrow,
#modal_edithour .select2-arrow,
#modal_set_availability .select2-arrow,
#modal_set_timeoff .select2-arrow{
    display: none;
}

#modal_profile_timeoff .studio-hour-list p:first-letter,
#modal_edithour .studio-hour-list p:first-letter,
#modal_set_availability .studio-hour-list p:first-letter,
#modal_set_timeoff .studio-hour-list p:first-letter {
     text-transform: uppercase;
}

.timeoff-list > p{
	font-size: 12px;
	display: block;
	overflow: hidden;
}

/* 11.4 Modal footer
--------------------------------------------------------------*/
/*****************************************************************

12.Notification

****************************************************************/

.notification,
.noti-error {
	position: fixed;
	width: 100%;
	top: 158px;
	height: 40px;
	line-height: 40px;
	z-index: 18;
	color: #fff;
	background: rgba(33, 149, 49, 0.87);
	font-size: 18px;
	display: none;
}

.noti-error {
	background: rgba(204, 0, 0, 0.8);
}

.noti-error .text-error {
	text-align: center;
}

.notification .container {
	position: relative;
	padding-left: 280px;
}

.icon-ok-noti {
	float: left;
	background: url("../images/icons.png") no-repeat scroll -64px 3px transparent;
	padding-left: 34px;
	margin-right: 22px;
	font-size: 22px;
	font-weight: bold;
}

.text-noti {
	float: left;
}

.icon-close-noti {
	position: absolute;
	background: url("../images/icons.png") repeat scroll -40px -20px transparent;
	height: 20px;
	width: 20px;
	right: 0;
	top: 10px;
	line-height: 20px;
	text-align: center;
	color: #fff;
	font-size: 15px;
}

.window-tip {
	display: none;
	position: fixed;
	z-index: 999;
	left: 48%;
	top: 10px;
	background: #FFC844;
	padding: 7px 10px;
    box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.5) inset;
    color: #FFFFFF;
    text-align: center;
}

.loader {
	width: 100px;
	height: 101px;
	border: 8px solid;
	border-top-color: hsl(154,100%,31%);
	border-left-color: hsl(216,87%,52%);
	border-bottom-color: hsl(8,66%,50%);
	border-right-color: hsl(42,100%,51%);
	border-radius: 50%;
	transform: rotate(45deg);
	margin: 30px auto;
}
.loader p {
	margin: 13px 0;
	display: inline-block;
	width: 107px;
	height: 107px;
	/* The background is used to specify the border background */
	background: linear-gradient(90deg, hsla(212,67%,36%,0) 0%,
	                                     hsla(207,69%,51%,0) 76%,
	                                     hsla(0,0%,100%,1) 85%,
	                                     hsla(0,0%,100%,1) 100%); /* W3C */
	/* Background origin is the padding box by default.
	Override to make the background cover the border as well. */
	-moz-background-origin: border;
	background-origin: border-box;
	/* A transparent border determines the width */
	border: 6px solid transparent;
	border-radius: 50%;
	box-shadow: inset -999px 0 0 #fff; /* The background color */
	transform: translate(-8px, 55px);
	animation: loading 1s linear infinite;
}

@keyframes loading {
	0% { transform: translate(-9px, -25px) rotate(0deg); }
	100% { transform: translate(-9px, -25px) rotate(360deg); }
}
/********************************************************************

13.Util

*********************************************************************/

.clearfix:before,
.clearfix:after {
	display: table;
	line-height: 0;
	content: "";
}

.clearfix:after {
	clear: both;
}

.hide,
.row .hide {
	display: none;
}

.show {
	display: block;
}

.left {
	float: left;
}

.right {
	float: right;
}

.mt5 {
	margin-top: 5px;
}

.mt10 {
	margin-top: 10px;
}

.mt20 {
	margin-top: 20px;
}

.mb10 {
	margin-bottom: 10px;
}

.mb20 {
	margin-bottom: 20px;
}

.mb30 {
	margin-bottom: 30px;
}

.mr10 {
	margin-right: 10px;
}

.mr20 {
	margin-right: 20px;
}

/* Added for NewInsAptReport _ June 2015 Enhancement */
.mr32 {
	margin-right: 32px;
}

/* For GSSP-170 */
.mr52 {
	margin-right: 52px;
}

/* For GSSP-161 */
.mr64 {
	margin-right: 64px;
}

.ml10 {
	margin-left: 10px;
}

.ml20 {
	margin-left: 20px;
}

.text-center {
	text-align: center;
	line-height: 1.8;
}

.text-right {
	text-align: right;
}

.text-left {
	text-align: left;
}

.relative {
	position: relative;
}

.absolute {
	position: absolute;
}
/*::-webkit-scrollbar {
    width: 12px;
}
 
::-webkit-scrollbar-track {

}
 
::-webkit-scrollbar-thumb {
    background: #dddddd; 
}
::-webkit-scrollbar-thumb:window-inactive {
	background: #dedede; 
}*/
/**********************************************************************************************

14.Clendar
	14.1 Header
		1.1 Header buttons
	14.2 Body
		2.1 Left bar
		2.2 Right content
**********************************************************************************************/

/* 14.1 Header
----------------------------------------------------------*/

.cal {
	-webkit-border-radius: 0 0 3px 3px;
	-moz-border-radius: 0 0 3px 3px;
	border-radius: 0 0 3px 3px;
}

.cal-header {
	-webkit-border-radius: 3px 3px 0 0;
	-moz-border-radius: 3px 3px 0 0;
	border-radius: 3px 3px 0 0;
	margin-bottom: -1px;
	height: 50px;
	line-height: 35px;
	padding: 8px 30px 8px 12px;
	text-align: left;
	font-size: 16px;
	font-weight: bold;
	color: #fff;
	background: -moz-linear-gradient(top, #3B3B3B, #333333);
	background: -webkit-linear-gradient(top, #3B3B3B, #333333);
	background: -ms-linear-gradient(top, #3B3B3B, #333333);
	background: linear-gradient(top, #3B3B3B, #333333);
}

/* 1.1 Header buttons
----------------------------------------------------------------------------*/
.btn-group-cal {
	border-top: 1px solid #656464;
	padding-bottom: 1px;
	border-bottom: 1px solid #656464;
	border-radius: 0 0 3px 3px;
}

.btn-group-cal > .btn-cal {
	position: relative;
	display: block;
	float: left;
	border-left: 1px solid #888787;
}

.btn-group-cal > .btn-cal + .btn-cal {
	margin-left: -1px;
}

.btn-group-cal .btn-cal:first-child {
	border-radius: 3px 0  0 3px;
	border-left: none;
}

.btn-group-cal .btn-cal:last-child {
	border-radius: 0 3px 3px 0;
	border-right: none;
}

.btn-cal {
	padding: 7px 12px 8px;
	width: 60px;
	height: 32px;
	line-height: 13px;
	display: inline-block;
	font-size: 12px;
	font-weight: bold;
	background-image: -moz-linear-gradient(top, #ffffff, #d5d6d8);
	background-image: -webkit-linear-gradient(top, #ffffff, #d5d6d8);
	background-image: -ms-linear-gradient(top, #ffffff, #d5d6d8);
	background-image: linear-gradient(top, #ffffff, #d5d6d8);
	text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
	background-repeat: repeat-x;
	border: none;
}

.btn-cal:active,
.btn-cal.active,
.btn-cal.disabled,
.btn-cal[disabled] {
	z-index: 2;
	border-right: none;
	background-color: #d5d6d8;
	background-position: 0 -15px;
	box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.5) inset;
	-webkit-transition: background-position 0.1s linear;
	-moz-transition: background-position 0.1s linear;
	transition: background-position 0.1s linear;
}

.btn-cal:hover,
.btn-cal:focus {
	background-color: #d5d6d8;
	text-decoration: none;
	background-position: 0 -15px;
	-webkit-transition: background-position 0.1s linear;
	-moz-transition: background-position 0.1s linear;
	transition: background-position 0.1s linear;
}

#cal_today {
	border-radius: 3px; 
	width: 90px;
	font-size: 13px;
}

.btn-cal-large {
	width: 90px;
}

.btn-group-cal > .btn-cal-pre,
.btn-group-cal > .btn-cal-next {
	padding: 0px;
	height: 32px;
	width: 20px;
}

.btn-group-cal .icon-prev,
.btn-group-cal .icon-next {
	display: block;
    height: 12px;
    width: 14px;
}

.btn-group-cal .icon-prev,
.btn-group-cal .icon-next {
	margin-left: auto;
	margin-right: auto;
	background: url("../images/icons.png") no-repeat scroll 0 0 transparent;
}

.btn-group-cal .icon-prev {
	background-position: 1px -52px;
}

.btn-group-cal .icon-next {
	background-position: -12px -52px;
}

.view-switch {
	margin-left: 144px;
}

.type-switch {
	margin-left: 15px;
}

/* 14.2 Body
==================================================================*/
.cal-body {
	background-color: #ebebeb;
	padding: 10px 10px 50px;
}

.disabled-cell {
	position: absolute;
	background: #E1E1E1;
	opacity: 0.5; 
}

/* 2.1 Left bar
-----------------------------------------------------------------*/
.cal-left {
	float: left;
	width: 23.523316062176167%;
	margin-right: 10px;
}

.cal-left h2 {
	font-size: 15px;
	padding: 10px 0 15px;
	font-weight: normal;
}

/* 2.2 Right content
--------------------------------------------------------------------*/
.cal-content {
	float: left;
	width: 73.57512953367875%;
	height: 630px;
	overflow: auto;
	position: relative;
}

.timeline {
	z-index: 2;
	position: absolute;
	width: 52px;
	border-left: 1px solid #d0d0d0;
	border-right: 1px solid #d0d0d0;
}

.timepoint {
	background: #fff;
	height: 60px;
	display: block;
	text-align: center;
}

.timepoint p {
	position: relative;
	top: 51px;
}

.timeline .timepoint:last-child {
	border-bottom: 1px solid #d0d0d0;
	color: transparent;
}

.cal-title {
	width: 100%;
	table-layout: fixed;
	border-collapse: separate;
	border-spacing: 0;
	z-index: 3;
}

.cal-title th {
	height: 50px;
	width: 95px;
	border-top: 1px solid #d0d0d0;
	border-bottom: 1px solid #d0d0d0;
	border-right: 1px solid #d0d0d0;
	background: -moz-linear-gradient(top, #FFFFFF, #D9DADB);
	background: -webkit-linear-gradient(top, #FFFFFF, #D9DADB);
	background: linear-gradient(top, #FFFFFF, #D9DADB);
	text-align: center;
	font-weight: bold;
	word-wrap: break-word;
}

.cal-title th:first-child {
	width: 52px;
	border-left: 1px solid #d0d0d0;
}

.cal-table {
	margin-left: 50px;
	position: relative;
}

.calendar {
	background-color: #ffffff;
	table-layout: fixed;
	width: 100%;
	border: 1px solid #d0d0d0;
	border-top-width: 0;
	border-collapse: separate;
	border-radius: 3px 3px 3px 3px;
	position: relative;
}

.calendar thead th {
	height: 50px;
	width: 95px;
	border-top: 1px solid #d0d0d0;
	border-bottom: 1px solid #d0d0d0;
	background: -moz-linear-gradient(top, #FFFFFF, #D9DADB);
	background: -webkit-linear-gradient(top, #FFFFFF, #D9DADB);
	background: linear-gradient(top, #FFFFFF, #D9DADB);
	text-align: center;
	font-weight: bold;
}

.calendar tbody tr:first-child td {
	border-top: none;
}

.calendar tbody tr:nth-child(even) td {
	border-top: 1px dotted #d0d0d0;
}

.calendar th,
.calendar td {
	text-align: center;
	vertical-align: middle;
	border-top: 1px solid #d0d0d0;
	border-left: 1px solid #d0d0d0;
}

.calendar td {
	background: #fff;
	width: 95px;
	height: 30px;
}

.calendar th:first-child,
.calendar td:first-child {
	border-left: 0;
}

.calendar.calendar-month td {
	width: 107px;
	height: 92px;
}

.calendar.calendar-month  tr:nth-child(even) td {
	border-top: 1px solid #d0d0d0;
}

.cal-item {
	color: #ffffff;
	background-color: #4987c4;
	border: 1px solid #4782bb;
	display: table;
	font-size: 11px;
	box-shadow: 0 0 3px 1px rgba(255, 255, 255, 0.50) inset;
	position: absolute;
	cursor: pointer;
	overflow: hidden;
	z-index: 1;
	word-break: break-word;
}

.cal-item-disabled {
	position: absolute;
	background: #B5B5B5;
	opacity: 0.5; 
}

.cal-item p {
	padding-left: left;
	text-indent: 5px;
	/*text-align: center;*/
	display: table-cell;
  	/*vertical-align: middle;*/
}

.cal-item.day {
	width: 93px;
}

.cal-item.week {
	display: block;
	overflow: hidden;
}

.cal-item.month {
	position: static;
	height: 12px;
	display: block;
	overflow: hidden;
	text-align: left;
	margin-bottom: 2.5px;
	line-height: 10px;
	width: 95px;
}

.filter-list,
.customer-info {
	margin-top: 10px;
	background: #fff;
	border: 1px solid #dedede;
	border-radius: 3px;
}

.filter-list ul,
.customer-info ul {
	padding: 14px 12px;
	border-bottom: 1px solid #dedede;
}

.filter-list .main-filter,
.customer-info h5 {
	font-weight: bold;
	font-size: 14px;
	margin-bottom: 10px;
	margin-left: 0;
}

.customer-info h5 {
	margin-top: 10px;
	padding: 0 10px;
	margin-bottom: 0;
}

.customer-info h5 a {
	font-weight: normal;
}

.filter-list ul li,
.customer-info ul li {
	padding-left: 20px;
	margin-bottom: 5px;
	float: none;
	position: relative;
	margin-left: 16px;
	word-wrap: break-word;
}

.customer-info ul li {
	padding-left: 0px;
	margin-left: 0;
	word-wrap: break-word;
}

.month-item,
.week-item {
	height: 100%;
	text-align: left;
}

.month-item .js-to-day {
	cursor: pointer;
}

.month-item p {
	height: 16px;
	margin-bottom: 4px;
}

.disabled .month-item {
	color: #999999;
}

.month-item.active,
.month-item.active.selected {
	border: 1px solid #cc0f0f;
}

.month-item.active p,
.month-item.active.selected p {
	background-color: #cc0f0f;
	color: #ffffff;
}

.month-item.selected {
	border: 1px solid #666666;
}

.month-item.selected p {
	background-color: #666666;
	color: #ffffff;
}

.item-more {
	font-size: 10px;
	float: right;
	color: #666666;
	padding-right: 4px;
}

.icon-item-more {
	position: relative;
	top: 12px;
	margin-left: 4px;
	width: 0px;
	height: 0px;
	border-style: solid;
	border-width: 8px 4px 0 4px;
	border-color: #666666 transparent transparent transparent;
}

.studio-hour .span5 {
	word-break: break-all;
}
/********************************************************
15.Pikaday
********************************************************/

.pika-single {
    z-index: 15;
    display: block;
    position: relative;
    width: 100%;
    color: #333;
    background: #fff;
    border-bottom-color: #bbb;
}

.pika-single.is-hidden {
    display: none;
}

.pika-single.is-bound {
    position: absolute;
    z-index: 18;
    /*box-shadow: 0 5px 15px -5px rgba(0,0,0,.5);*/
}

.pika-title {
    margin-left: 1px;
    margin-right: 1px;
    background-color: #40413f;
    color: #ffffff;
    position: relative;
    text-align: center;
    margin-bottom: 2px;
    padding: 3px 0;
}

.pika-label {
    display: inline-block;
    position: relative;
    z-index: 9999;
    overflow: hidden;
    margin: 0;
    padding: 1px 3px 0px;
    font-size: 14px;
    line-height: 20px;
    font-weight: bold;
}
.pika-title select {
    cursor: pointer;
    position: absolute;
    z-index: 9998;
    margin: 0;
    left: 0;
    top: 5px;
    opacity: 0;
}

.pika-prev,
.pika-next {
    display: block;
    cursor: pointer;
    position: relative;
    outline: none;
    border: 0;
    padding: 0;
    width: 13px;
    height: 15px;
    white-space: nowrap;
    text-indent: 100%;
    overflow: hidden;
    top: 7px;
}

.pika-prev:hover,
.pika-next:hover {
    opacity: .9;
}

.pika-prev,
.is-rtl .pika-next {
    float: left;
    background: url("../images/icons.png") no-repeat 1px -64px transparent;
    left: 5px;
}

.pika-next,
.is-rtl .pika-prev {
    float: right;
    background: url("../images/icons.png") no-repeat -14px -63px transparent;
    right: 5px;
}

.pika-prev.is-disabled,
.pika-next.is-disabled {
    cursor: default;
    opacity: .2;
}

.pika-select {
    display: inline-block;
}

.pika-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 1px;
    border: 0;
    background-color: #ffffff;
}

.pika-table th,
.pika-table td {
    width: 14.285714285714286%;
    background-color: #20211e;
}

.pika-table th {
    color: #ffffff;
    font-size: 9px;
    line-height: 25px;
    font-weight: bold;
    text-align: center;
}

.pika-button {
    font-weight: bold;
    cursor: pointer;
    display: block;
    outline: none;
    border: 0;
    margin: 0;
    width: 100%;
    padding: 5px;
    color: #ffffff;
    font-size: 11px;
    line-height: 10px;
    text-align: center;
    background: #20211e;
}

.is-today .pika-button,
.is-today.is-selected .pika-button {
    background-color: #CC0000;
}

.is-selected .pika-button {
    color: #fff;
    font-weight: bold;
    background: #666666;
}

.is-disabled .pika-button {
    pointer-events: none;
    cursor: default;
    color: #999;
    opacity: .3;
}

.pika-button:hover {
    background: #666666;
}

.is-bound.pika-single {
    width: 210px;
    padding: 1px 0 0;
}

.is-bound.pika-single .pika-title {
    margin: 0;
    padding: 7px 0;
}

.is-bound.pika-single .pika-table {
    border-spacing: 0;
    border-collapse: collapse;
}

.is-bound.pika-single .pika-table th {
    background: #E3E3E3;
    color: #999999;
    line-height: 20px;
    font-size: 10px;
}

.is-bound.pika-single .pika-table td {
    background: #FBFBFB;
    border: 1px solid #DDDDDD;
}

.is-bound.pika-single .pika-button {
    background: #FBFBFB;
    color: #3A3A3A;
    padding: 7px;
}

.is-bound.pika-single .is-today .pika-button {
    background: #E3E3E3;
}

.is-bound.pika-single .is-selected .pika-button {
    background: #c4c4c4;
}

.is-bound.pika-single .pika-button:hover {
    background: #E3E3E3;
}
/******************************************************************************
16.Select2 3.4.1
******************************************************************************/
.select2-container {
    margin: 0;
    position: relative;
    display: inline-block;
    vertical-align: middle;
    background: #fff;
}

.select2-container .select2-choice {
    display: block;
    padding: 0 0 0 8px;
    overflow: hidden;
    position: relative;
    
    border: 2px solid #d9d9d9;

    white-space: nowrap;
    line-height: 22px;
    color: #444;
    text-decoration: none;
}

.select2-container.select2-drop-above .select2-choice {
    border-bottom-color: #aaa;

    -webkit-border-radius:0 0 4px 4px;
       -moz-border-radius:0 0 4px 4px;
            border-radius:0 0 4px 4px;

    background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eeeeee), color-stop(0.9, white));
    background-image: -webkit-linear-gradient(center bottom, #eeeeee 0%, white 90%);
    background-image: -moz-linear-gradient(center bottom, #eeeeee 0%, white 90%);
    background-image: linear-gradient(top, #eeeeee 0%,#ffffff 90%);
}

.select2-container.select2-allowclear .select2-choice .select2-chosen {
    margin-right: 42px;
}

.select2-container .select2-choice > .select2-chosen {
    margin-right: 26px;
    display: block;
    overflow: hidden;

    white-space: nowrap;

    -ms-text-overflow: ellipsis;
        text-overflow: ellipsis;
}

.select2-container .select2-choice abbr {
    display: none;
    width: 12px;
    height: 12px;
    position: absolute;
    right: 24px;
    top: 8px;

    font-size: 1px;
    text-decoration: none;

    border: 0;
    background: url('../images/select2.png') right top no-repeat;
    cursor: pointer;
    outline: 0;
}

.select2-container.select2-allowclear .select2-choice abbr {
    display: inline-block;
}

.select2-container .select2-choice abbr:hover {
    background-position: right -11px;
    cursor: pointer;
}

.select2-drop-undermask {
    border: 0;
    margin: 0;
    padding: 0;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 9998;
    background-color: transparent;
}

.select2-drop-mask {
    border: 0;
    margin: 0;
    padding: 0;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 9998;
    /* styles required for IE to work */
    background-color: #fff;
    opacity: 0;
}

.select2-drop {
    width: 100%;
    margin-top: 1px;
    margin-left: 1px;
    position: absolute;
    z-index: 9999;
    top: 100%;
    background: #fff;
    box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.3);
}

.select2-drop-auto-width {
    border-top: 1px solid #aaa;
    width: auto;
}

.select2-drop-auto-width .select2-search {
    padding-top: 4px;
}

.select2-drop.select2-drop-above {
    margin-top: 1px;
    border-top: 1px solid #aaa;
    border-bottom: 0;

    -webkit-border-radius: 4px 4px 0 0;
       -moz-border-radius: 4px 4px 0 0;
            border-radius: 4px 4px 0 0;

    -webkit-box-shadow: 0 -4px 5px rgba(0, 0, 0, .15);
       -moz-box-shadow: 0 -4px 5px rgba(0, 0, 0, .15);
            box-shadow: 0 -4px 5px rgba(0, 0, 0, .15);
}

.select2-drop-active {
}

.select2-drop.select2-drop-above.select2-drop-active {
    /*border-top: 1px solid #5897fb;*/
}

.select2-container .select2-choice .select2-arrow {
    border-left: 1px solid #d9d9d9;
    display: inline-block;
    width: 18px;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
}

.select2-container .select2-choice .select2-arrow b {
    display: block;
    width: 100%;
    height: 100%;
    background: url("../images/icons.png") no-repeat scroll -1px -30px transparent;
}

.select2-search {
    display: none;
    width: 100%;
    min-height: 26px;
    margin: 0;
    padding-left: 4px;
    padding-right: 4px;

    position: relative;
    z-index: 10000;

    white-space: nowrap;
}

.select2-search input {
    width: 100%;
    height: auto !important;
    min-height: 26px;
    padding: 4px 20px 4px 5px;
    margin: 0;

    outline: 0;
    font-family: sans-serif;
    font-size: 1em;

    border: 1px solid #aaa;
    -webkit-border-radius: 0;
       -moz-border-radius: 0;
            border-radius: 0;

    -webkit-box-shadow: none;
       -moz-box-shadow: none;
            box-shadow: none;

    background: #fff url('../images/select2.png') no-repeat 100% -22px;
    background: url('../images/select2.png') no-repeat 100% -22px, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, white), color-stop(0.99, #eeeeee));
    background: url('../images/select2.png') no-repeat 100% -22px, -webkit-linear-gradient(center bottom, white 85%, #eeeeee 99%);
    background: url('../images/select2.png') no-repeat 100% -22px, -moz-linear-gradient(center bottom, white 85%, #eeeeee 99%);
    background: url('../images/select2.png') no-repeat 100% -22px, linear-gradient(top, #ffffff 85%, #eeeeee 99%);
}

.select2-drop.select2-drop-above .select2-search input {
    margin-top: 4px;
}

.select2-search input.select2-active {
    background: #fff url('../images/select2-spinner.gif') no-repeat 100%;
    background: url('../images/select2-spinner.gif') no-repeat 100%, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, white), color-stop(0.99, #eeeeee));
    background: url('../images/select2-spinner.gif') no-repeat 100%, -webkit-linear-gradient(center bottom, white 85%, #eeeeee 99%);
    background: url('../images/select2-spinner.gif') no-repeat 100%, -moz-linear-gradient(center bottom, white 85%, #eeeeee 99%);
    background: url('../images/select2-spinner.gif') no-repeat 100%, linear-gradient(top, #ffffff 85%, #eeeeee 99%);
}


/* results */
.select2-results {
    max-height: 200px;
    position: relative;
    overflow-x: hidden;
    overflow-y: auto;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
}

.select2-results ul.select2-result-sub {
    margin: 0;
    padding-left: 0;
}

.select2-results ul.select2-result-sub > li .select2-result-label { padding-left: 20px }
.select2-results ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 40px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 60px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 80px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 100px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 110px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 120px }

.select2-results li {
    float: none;
}

.select2-results li.select2-result-with-children > .select2-result-label {
}

.select2-results .select2-result-label {
    padding: 3px 7px 4px;
    margin: 0;
    cursor: pointer;

    min-height: 1em;

    -webkit-touch-callout: none;
      -webkit-user-select: none;
         -moz-user-select: none;
              user-select: none;
}

.select2-results .select2-highlighted {
    background: #dddddd;
    /*color: #fff;*/
}

.select2-results li em {
    background: #feffde;
    font-style: normal;
}

.select2-results .select2-highlighted em {
    background: transparent;
}

.select2-results .select2-highlighted ul {
    background: white;
    color: #000;
}


.select2-results .select2-no-results,
.select2-results .select2-searching,
.select2-results .select2-selection-limit {
    background: #f4f4f4;
    display: list-item;
}

/*
disabled look for disabled choices in the results dropdown
*/
.select2-results .select2-disabled.select2-highlighted {
    color: #666;
    background: #f4f4f4;
    display: list-item;
    cursor: default;
}
.select2-results .select2-disabled {
  background: #f4f4f4;
  display: list-item;
  cursor: default;
}

.select2-results .select2-selected {
    display: none;
}

.select2-more-results.select2-active {
    background: #f4f4f4 url('../images/select2-spinner.gif') no-repeat 100%;
}

.select2-more-results {
    background: #f4f4f4;
    display: list-item;
}

/* disabled styles */

.select2-container.select2-container-disabled .select2-choice {
    background-color: #f4f4f4;
    background-image: none;
    border: 2px solid #ddd;
    cursor: default;
}

.select2-container.select2-container-disabled .select2-choice .select2-arrow {
    background-color: #f4f4f4;
    background-image: none;
    border-left: 0;
}

.select2-container.select2-container-disabled .select2-choice abbr {
    display: none;
}


/* multiselect */

.select2-container-multi .select2-choices {
    /*height: auto !important;*/
    height: 1%;
    margin: 0;
    padding: 0;
    position: relative;

    border: 2px solid #D9D9D9;
    cursor: text;
    overflow: hidden;

    /*background-color: #fff;
    background-image: -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(1%, #eeeeee), color-stop(15%, #ffffff));
    background-image: -webkit-linear-gradient(top, #eeeeee 1%, #ffffff 15%);
    background-image: -moz-linear-gradient(top, #eeeeee 1%, #ffffff 15%);
    background-image: linear-gradient(top, #eeeeee 1%, #ffffff 15%);*/
}

.select2-locked {
  padding: 3px 5px 3px 5px !important;
}

.select2-container-multi .select2-choices {
    min-height: 26px;
}

.select2-container-multi.select2-container-active .select2-choices {

}
.select2-container-multi .select2-choices li {
    float: left;
    list-style: none;
}
.select2-container-multi .select2-choices .select2-search-field {
    margin: 0;
    padding: 0;
    white-space: nowrap;
}

.select2-container-multi .select2-choices .select2-search-field input {
    padding: 2px 7px;
    margin: 1px 0;
    color: #666;
    outline: 0;
    border: 0;
    -webkit-box-shadow: none;
       -moz-box-shadow: none;
            box-shadow: none;
    background: transparent !important;
}

.select2-container-multi .select2-choices .select2-search-field input.select2-active {
    background: #fff url('../images/select2-spinner.gif') no-repeat 100% !important;
}

.select2-default {
    color: #999 !important;
}

.select2-container-multi .select2-choices .select2-search-choice {
    padding: 3px 5px 3px 18px;
    margin: 3px 0 3px 5px;
    position: relative;

    line-height: 13px;
    color: #333;
    cursor: default;
    border: 1px solid #aaaaaa;

    -webkit-border-radius: 3px;
       -moz-border-radius: 3px;
            border-radius: 3px;

    -webkit-box-shadow: 0 0 2px #ffffff inset, 0 1px 0 rgba(0,0,0,0.05);
       -moz-box-shadow: 0 0 2px #ffffff inset, 0 1px 0 rgba(0,0,0,0.05);
            box-shadow: 0 0 2px #ffffff inset, 0 1px 0 rgba(0,0,0,0.05);

    -webkit-background-clip: padding-box;
       -moz-background-clip: padding;
            background-clip: padding-box;

    -webkit-touch-callout: none;
      -webkit-user-select: none;
         -moz-user-select: none;
              user-select: none;

    background-color: #e4e4e4;
    background-image: -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(20%, #f4f4f4), color-stop(50%, #f0f0f0), color-stop(52%, #e8e8e8), color-stop(100%, #eeeeee));
    background-image: -webkit-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
    background-image: -moz-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
    background-image: linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
}
.select2-container-multi .select2-choices .select2-search-choice .select2-chosen {
    cursor: default;
}
.select2-container-multi .select2-choices .select2-search-choice-focus {
    background: #d4d4d4;
}

.select2-search-choice-close {
    display: block;
    width: 12px;
    height: 13px;
    position: absolute;
    right: 3px;
    top: 4px;

    font-size: 1px;
    outline: none;
    background: url('../images/select2.png') right top no-repeat;
}

.select2-container-multi .select2-search-choice-close {
    left: 3px;
}

.select2-container-multi .select2-choices .select2-search-choice .select2-search-choice-close:hover {
  background-position: right -11px;
}
.select2-container-multi .select2-choices .select2-search-choice-focus .select2-search-choice-close {
    background-position: right -11px;
}

/* disabled styles */
.select2-container-multi.select2-container-disabled .select2-choices{
    background-color: #f4f4f4;
    background-image: none;
    border: 1px solid #ddd;
    cursor: default;
}

.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice {
    padding: 3px 5px 3px 5px;
    border: 1px solid #ddd;
    background-image: none;
    background-color: #f4f4f4;
}

.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice .select2-search-choice-close {    display: none;
    background:none;
}
/* end multiselect */


.select2-result-selectable .select2-match,
.select2-result-unselectable .select2-match {
    text-decoration: underline;
}

.select2-offscreen, .select2-offscreen:focus {
    clip: rect(0 0 0 0);
    width: 1px;
    height: 1px;
    border: 0;
    margin: 0;
    padding: 0;
    overflow: hidden;
    position: absolute;
    outline: 0;
    left: 0px;
}

.select2-display-none {
    display: none;
}

.select2-measure-scrollbar {
    position: absolute;
    top: -10000px;
    left: -10000px;
    width: 100px;
    height: 100px;
    overflow: scroll;
}
/* Retina-ize icons */

@media only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (min-resolution: 144dpi)  {
  .select2-search input, .select2-search-choice-close, .select2-container .select2-choice abbr, .select2-container .select2-choice .select2-arrow b {
      background-image: url('select2x2.png') !important;
      background-repeat: no-repeat !important;
      background-size: 60px 40px !important;
  }
  .select2-search input {
      background-position: 100% -21px !important;
  }
}

/**********************************************************************************************

17.Print
	1. Print set copy form bootstrap 3.0
**********************************************************************************************/
/* 1. Print set
=========================================================================================*/

@media print {
  * {
    color: #000 !important;
    text-shadow: none !important;
    background: transparent !important;
    box-shadow: none !important;
  }
  a,
  a:visited {
    text-decoration: underline;
  }
  a[href]:after {
    content: " (" attr(href) ")";
  }
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  .ir a:after,
  a[href^="javascript:"]:after,
  a[href^="#"]:after {
    content: "";
  }
  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }
  thead {
    display: table-header-group;
  }
  tr,
  img {
    page-break-inside: avoid;
  }
  img {
    max-width: 100% !important;
  }
  @page  {
    margin: 2cm .5cm;
  }
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  h2,
  h3 {
    page-break-after: avoid;
  }
  .navbar {
    display: none;
  }
  .table td,
  .table th {
    background-color: #fff !important;
  }
  .btn > .caret,
  .dropup > .btn > .caret {
    border-top-color: #000 !important;
  }
  .label {
    border: 1px solid #000;
  }
  .table {
    border-collapse: collapse !important;
  }
  .table-bordered th,
  .table-bordered td {
    border: 1px solid #ddd !important;
  }
}

@media print {
  .visible-print {
    display: block !important;
  }
  tr.visible-print {
    display: table-row !important;
  }
  th.visible-print,
  td.visible-print {
    display: table-cell !important;
  }
  .hidden-print {
    display: none !important;
  }
  tr.hidden-print {
    display: none !important;
  }
  th.hidden-print,
  td.hidden-print {
    display: none !important;
  }
}

 
 #rep-load-modal1 {
    display: none;
    position: absolute;
    top: 45%;
    left: 46%;
    width: 300 px;	
    height: 300 px;	
    padding:0px 0px 0px;
    border: 0px solid #ababab;
    box-shadow:0px 0px 0px #ababab;
    border-radius:20px;
    background-color: white;
    z-index: 1002;
    text-align:center;
    overflow: auto;
}
#rep-load-fade1 {
    display: none;
    position:absolute;
    top: 0%;
    left: 0%;
    width: 100%;
    height: 50000%;
    background-color: #ababab;
    z-index: 1001;
    -moz-opacity: 0.8;
    opacity: .70;
    filter: alpha(opacity=80);
}

/*For GSSP-170 */
#rep-load-modal {
    display: none;
    position: absolute;
    top: 45%;
    left: 46%;
    width: 300 px;	
    height: 300 px;	
    padding:0px 0px 0px;
    border: 0px solid #ababab;
    box-shadow:0px 0px 0px #ababab;
    border-radius:20px;
    background-color: white;
    z-index: 1002;
    text-align:center;
    overflow: auto;
}

/*For GSSP-170 */
#rep-load-fade {
    display: none;
    position:absolute;
    top: 0%;
    left: 0%;
    width: 100%;
    height: 50000%;
    background-color: #ababab;
    z-index: 1001;
    -moz-opacity: 0.8;
    opacity: .70;
    filter: alpha(opacity=80);
}

/*FOR GSSP-364 */

#instructor_sch_table caption { 
	line-height: 75px;
	padding: 0;
	text-align: center;
	border-radius: 5px 5px 0 0;
	height: 75px;
	font-size: 16px;
	font-weight: bold;
	color: #fff;
	background: -moz-linear-gradient(top, #3B3B3B, #333333);
	background: -webkit-linear-gradient(top, #3B3B3B, #333333);
	background: linear-gradient(top, #3B3B3B, #333333);
	border-bottom: 8px solid #cd2418;
}

#instructor_sch_table thead {
	border-top: 1px solid #333;
}

.schedule-header {
    display: inline-block;
    padding-right: 15px;
    line-height: 50px;
}

.container.instructor-schedule {
	padding: 0 25px;
	width: 100%;
}

#instructor_sch_table  {
	border: 1px solid #ccc;
	border-collapse: collapse;
	margin: 0;
	padding: 0;
	width: 100%;
	table-layout: fixed;
}

#instructor_sch_table caption {
	font-size: 1.5em;
	margin-top: 15px;
}

#instructor_sch_table tr {
	background-color: #f8f8f8;
	padding: .35em;
}

#instructor_sch_table  th,
#instructor_sch_table  td {
	font-weight: bold;
	font-size: 13px;
	padding: .625em 0;
	text-align: center;
}

#instructor_sch_table th {
	height: 45px;
	border: 2px solid #a19d9d;
	background: -moz-linear-gradient(top, #3B3B3B, #333333);
	background: -webkit-linear-gradient(top, #3B3B3B, #333333);
	background: linear-gradient(top, #3B3B3B, #333333);
	color: #fff;
	font-weight: bold;
	border-top: 0;
}

#instructor_sch_table td {
	height: 45px;
	border: 2px solid #a19d9d;
	color: #000;
	font-weight: bold;
}

#instructor_sch_table .btn-default {
	background-color: #cd2418;
	border: 0;
	border-radius: 4px;
	color: #fff;
	display: inline-block;
	height: 35px;
	line-height: 35px;
	font-weight:bold;
	width: 80%;
}

#instructor_sch_table .btn-default.yes {
	background-color: #078700;
}

#instructor_sch_table .launch-header button {
	width: 80%;
}

#instructor_sch_table .launch-header button.disabled {
	background-color: #757474;
}

#instructor_sch_table .completed-header .btn-default {
    display: inline-block;
    width: 35%;
    margin-left: 8px;
    margin-right: 8px;

}

#instructor_sch_table  .btn-circle {
	min-width: 35px;
	max-width: 120px;
	text-decoration: none;
	display: inline-block;
	outline: none;
	cursor: pointer;
	border-style: none;
	color: white;
	background-color: #eaeaea;
	border-radius: 100%;
	overflow: none;
	text-align: center;
	padding: 0;
	height: 35px;
	line-height: 35px;
	border: 1px solid #666;
}


#instructor_sch_table .btn-circle::before {
    content: '';
    display: inline-block;
    vertical-align: middle;
    padding-top: 100%;

}

#instructor_sch_table .btn-circle::after{
	content: '+';
	display: inline-block;
	width: 22px;
	font-size: 28px;
	line-height: 27px;
	height: 35px;
	vertical-align: middle;
	color: #333;
	-webkit-text-fill-color: white;
	-webkit-text-stroke-width: 1px;
	-webkit-text-stroke-color: #333;
	font-weight: bold;
}

/*Breakpoints for instructor table */


@media screen and (max-width: 1385px) {
  #instructor_sch_table  {
    border: 0;
  }

  #instructor_sch_table  caption {
    font-size: 1.3em;
  }

  #instructor_sch_table tbody {
  	display: flex;
  	flex-wrap: wrap;
  }
  
  #instructor_sch_table  thead {
    border: none;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
  }
  
  #instructor_sch_table tr {
  	border-top: 1px solid #a19d9d;
    display: block;
    margin-bottom: .625em;
    padding: 0;
    width: 33%;
  }
  
  #instructor_sch_table td {
  	border: 0;
    display: block;
    font-size: .9em;
    /*! text-align: right; */
    padding: 0;
    height: 45px;
    line-height: 45px;
    /*! text-align: center; */
  }

  #instructor_sch_table td span {
  	border: 1px solid #a19d9d;
  	border-top:0;
	height: 45px;
	line-height: 45px;
  	display: inline-block;
  	text-align: center;
  	width: 60%;
  }
  
  #instructor_sch_table  td::before {
    /*
    * aria-label has no advantage, it won't be read inside a table
    content: attr(aria-label);
    */
    content: attr(data-label);
    font-weight: bold;
    text-transform: uppercase;
    width: 40%;
    /*! margin-right: 15px; */
    text-align: right;
    display: inline-block;
    /*! float: left; */
	background: -moz-linear-gradient(top, #3B3B3B, #333333);
	background: -webkit-linear-gradient(top, #3B3B3B, #333333);
	background: linear-gradient(top, #3B3B3B, #333333);
	border: 1px solid #a19d9d;
	border-top: 0;
    color: #fff;
    height: 45px;
    text-align: center;
    float: left;
  }
  
  #instructor_sch_table  td:last-child {
    border-bottom: 0;
  }
  #instructor_sch_table td button {
  	margin-top: 5px;
  }
}

.container.instructor-schedule-container { 
	width: 100%;
	padding: 0 25px;
}

#modal_edit_instructor.modal {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1040;
	display: none;
	overflow: auto;
	overflow-y: auto;
	overflow-y: scroll;
	background: none;
	box-shadow: none;
	border: none;
	padding-top: 120px;

}

#modal_edit_instructor.modal .modal-header {
	text-align: center;
}

#modal_edit_instructor.modal .close {
	float: left;
	margin-top: 4px;
	width: 22px;
}

#modal_edit_instructor.modal h4 {
	display: inline-block;
}

#modal_edit_instructor.modal button.submit-remarks {
	float: right;
	margin-right: 9px;
}

#modal_edit_instructor.modal .modal-dialog {
	width: 450px;
	background-color: #fff;
	height: 230px;
    margin: auto;
    border: 1px solid #333;
}

#modal_edit_instructor.modal .modal-body {
	padding: 15px 20px 0;

}

#modal_edit_instructor.modal .modal-footer {
	padding: 20px;

}

#modal_edit_instructor.modal #edit_instructor_comments {
	height: 105px;
	width: 400px;
}

@media screen and (min-width: 768px) {
  #modal_edit_instructor.modal  .modal-dialog {
    right: auto;
    left: 50%;
  }



@media screen and (max-width: 1141px) {
	 #instructor_sch_table tr { 
	 	width: 50%;
	 }

} 

@media screen and (max-width: 900px) { 
	#instructor_sch_table caption {
		height: 160px;
	}
	#instructor_sch_table caption span { 
		width: 40%;
	}

}


@media screen and (max-width: 725px) {
	 #instructor_sch_table tr { 
	 	width: 100%;
	 }

}


@media screen and (max-width: 625px) { 
	#instructor_sch_table caption {
		height: 200px;
	}
	#instructor_sch_table caption .schedule-header { 
		line-height: 30px;
		margin-top:10px;
		width: 100%;
	}

}


@media screen and (min-width: 900px) {

	.schedule-header:after {
		content: '';
		border-right: 3px solid #fff;
		padding-left: 30px;
	}

	.schedule-header.instructor-id:after {
		display:none;
	}
}


@media screen and (min-width: 1386px) {

	#instructor_sch_table th span {
		word-wrap: break-word;
	}

	.duartion-header, 
	.type-header, 
	.time-header{
		width: 80px;
	}
	.room-header {
		width: 100px;
	}
	.location-header {
        width: 120px;
    }
    .book-status-header {
        width: 80px;
    }
    .name-header {
        width: 120px;
    }
    .remarks-header,
	.notes-header {
		width: 90px;
	}
	.email-header {
		width: 225px;
	}
    .error-info-header {
        width: 120px;
    	white-space: normal;
    }
    .pos-ref-header {
        width: 100px;
    }
    .appointment-id-header {
        width: 90px;
    }
	.phone-header {
		width: 95px;
	}

	#instructor_sch_table tr:nth-child(even) {
		background-color: #eae8ea;
	}  

}

@media only screen and (min-width: 1142px) and (max-width: 1385px)  {
	#instructor_sch_table caption {
		width: 99%;
	}

}
 