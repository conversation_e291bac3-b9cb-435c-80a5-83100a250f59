define(function(){return Hash=function(){this.separate=",",this.data={},this._hashChange()},Hash.prototype={get:function(a){return void 0===a?this.data:this.data[a]},add:function(a){var b=this;for(var c in a)b.data[c]=a[c];b._change()},remove:function(a){var b=this;delete b.data[a],b._change()},clear:function(){var a=this;a.data={},location.hash=""},_change:function(){var a=this,b=a.data,c=[];for(var d in b)c.push(d+"="+b[d]);location.hash=c.join(a.separate)},_hashChange:function(){var a=this;window.addEventListener("hashchange",function(){a._hashData()},!1)},_hashData:function(){var a=this,b=location.hash.substring(1).split(a.separate);if(a.data={},""!=b)for(var c=0;c<b.length;c++){var d=b[c].split("="),e=d[0],f=d[1];a.data[e]=f}}},new Hash});