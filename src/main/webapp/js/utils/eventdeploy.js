define(['jquery'], function($) {
	function Eventdeploy(opts) {
		var self = this;
		if(opts && opts.constructor === Array) {
			opts.forEach(function(obj, index) {
				var evt, delegate;
				evt = obj['events'];
				delegate = obj['delegate'];	
				self.Deploy(evt, delegate);
			});
		}
	}
	Eventdeploy.prototype = {
		Deploy: function(evt, delegate) {
			var eventName, selector, fn;
			for(var e in evt) {
				eventName = e.split(' ')[0];
	            selector = $.trim(e.split(eventName)[1]);
	            if(selector === 'document') selector = document;
	            if(selector === 'window') selector = window;
	            fn = evt[e];
	            if(delegate !== undefined) { 
	            	$(delegate).on(eventName, selector, fn);
            	} else {
	            	$(selector).on(eventName, fn);
				}
			}
		},
		Destroy: function(evt, delegate) {
			var eventName, selector;
			eventName = evt.split(' ')[0];
            selector = $.trim(evt.split(eventName)[1]);
			if(selector === 'document') selector = document;
            if(selector === 'window') selector = window;
            if(delegate !== undefined) { 
            	$(delegate).off(eventName, selector);
        	} else {
            	$(selector).off(eventName);
			}
		}
	}
	return Eventdeploy;
});