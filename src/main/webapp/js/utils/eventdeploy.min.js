define(["jquery"],function(a){function b(a){var b=this;a&&a.constructor===Array&&a.forEach(function(a){var c,d;c=a.events,d=a.delegate,b.Deploy(c,d)})}return b.prototype={Deploy:function(b,c){var d,e,f;for(var g in b)d=g.split(" ")[0],e=a.trim(g.split(d)[1]),"document"===e&&(e=document),"window"===e&&(e=window),f=b[g],void 0!==c?a(c).on(d,e,f):a(e).on(d,f)},Destroy:function(b,c){var d,e;d=b.split(" ")[0],e=a.trim(b.split(d)[1]),"document"===e&&(e=document),"window"===e&&(e=window),void 0!==c?a(c).off(d,e):a(e).off(d)}},b});