define(function() {
	Hash = function(opts) {
		this.separate = ','
		this.data = {};
		// this._hashData()
		this._hashChange();
	}
	Hash.prototype = {
		get: function(val) {
			var self = this;
			if(val === undefined) {
				return this.data;
			} else {
				return this.data[val];
			}
		},
		add: function(obj) {
			var self = this;
			for(var i in obj) {
				self.data[i] = obj[i];
			}	
			self._change();
		},
		remove: function(key) {
			var self = this;
			delete self.data[key];
			self._change();
		},
		clear: function() {
			var self = this;
			self.data = {};
			location.hash = '';
		},
		_change: function() {
			var self = this,
				data = self.data,
				hash = [];
			for(var i in data) {
				hash.push(i+'='+data[i]);
			}
			location.hash = hash.join(self.separate);
		},
		_hashChange: function() {
			var self = this;
			window.addEventListener('hashchange', function(){
				self._hashData();
			}, false);
		},
		_hashData: function() {
			var self = this,
				hash = location.hash.substring(1).split(self.separate);
			self.data = {};
			if(hash != '') {
				for(var i = 0; i < hash.length; i++) {
					var obj = hash[i].split('='),
						key = obj[0],
						value = obj[1];	
					self.data[key] = value;
				}
			}
		}
	}
	return new Hash;
});