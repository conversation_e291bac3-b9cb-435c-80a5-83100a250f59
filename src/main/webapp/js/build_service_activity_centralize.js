({
	baseUrl: '.',
	// dir: 'js-build',
	name: "app/scripts/service_activity_centralize",
	out: "app/scripts/service_activity_centralize.min.js",
	paths: {

		/* Vendor */
		'jquery': 'vendor/jquery/jquery.min',
		'moment': 'vendor/libs/moment',
		'mustache': 'vendor/libs/mustache.min',
		'pikaday': 'vendor/libs/pikaday',
		'icheck': 'vendor/libs/jquery.icheck.min',
		'select2': 'vendor/libs/select2.min',
		'typeahead': 'vendor/libs/typeahead.min',
		'modal': 'vendor/libs/bootstrap.modal.min',
		'bootbox': 'vendor/libs/bootbox.min',
		'backbone': 'vendor/backbone/backbone',
		'underscore': 'vendor/underscore/underscore',

		/* Modules */
		'calendar': 'app/modules/calendar',
		'customer': 'app/modules/customer',
		'instructor': 'app/modules/instructor',
		'staff': 'app/modules/staff',
		'service': 'app/modules/service',
		'activity': 'app/modules/activity',
		'room': 'app/modules/room',
		'report': 'app/modules/report',
		'studio': 'app/modules/studio',
		'filter': 'app/modules/filter',
		'appointment': 'app/modules/appointment',
		'appointment_modal': 'app/modules/appointment_modal',
		'edithour_modal': 'app/modules/edithour_modal',
		'search': 'app/modules/search',
		'notification': 'app/modules/common/notification',
		/* Modules for centralize */
		'room_centralize': 'app/modules/centralize/room_centralize',
		'service_centralize': 'app/modules/centralize/service_centralize',
		'activity_centralize': 'app/modules/centralize/activity_centralize',
				/*GSSP-211 CHANGES*/
		'dual_instructor_centralize': 'app/modules/centralize/dual_instructor_centralize',		

		/* Utils */
		'eventdeploy': 'utils/eventdeploy',
		'hash': 'utils/hash'
	},
	shim: {
		'backbone': {
			deps: ['underscore', 'jquery'],
			exports: 'Backbone'
		},
		'underscore': {
			exports: '_'
		},
		'moment': {
			deps: ['jquery'],
			exports: 'moment'
		},
		'mustache': {
			exports: 'Mustache'
		},
		'icheck': {
			deps: ['jquery'],
			exports: 'iCheck'
		},
		'modal': {
			deps: ['jquery']
		},
		'bootbox': {
			deps: ['jquery', 'modal'],
			exports: 'bootbox'
		},
		'pikaday': {
			deps: ['moment'],
			exports: 'Pikaday'
		},
		'select2': {
			deps: ['jquery'],
			exports: 'select2'
		},
		'typeahead': {
			deps: ['jquery'],
			exports: 'typeahead'
		}
	},
	// modules: [
	// 	{
	// 		name: 'app/scripts/schedule',
	// 	},
	// 	{
	// 		name: 'app/scripts/room_template_manage',
	// 	},
	// 	{
	// 		name: 'app/scripts/service_activity_centralize',
	// 	},
	// 	{
	// 		name: 'app/scripts/studio_centralize',
	// 	}
	// ]
})