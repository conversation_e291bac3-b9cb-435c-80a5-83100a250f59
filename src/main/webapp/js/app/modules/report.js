define([
	'jquery',
	'moment',
	'select2', 
	'pikaday', 
	'underscore', 
	'backbone', 
	'mustache',
	'notification'
], 
function($, moment, select2, Pikaday, _, Backbone, Mustache, notification) {
	function Report(opts) {
		// Default set
		this.defaults = {
			//generate report url
			url_masterScheduleReport: 'report/masterScheduleReport.htm',
			url_rehearsalBookingReport: 'report/rehearsalBookingReport.htm',
			url_rehearsalScheduleReport: 'report/rehearsalScheduleReport.htm',
			url_instructorScheduleReport: 'report/instructorScheduleReport.htm',
			
			//Instructor Schedule Status
			url_instructorStatusScheduleReport: 'report/instructorAppointmentStatusScheduleReport.htm',
			
			url_cancelledAppointments: 'report/cancelledAppointments.htm',
			/* Added for NewInsAptReport _ June 2015 Enhancement */
			url_instructorOpenAppointmentsReport: 'report/instructorOpenAppointmentsReport.htm',
			//For GSSP-170
			url_conflictingAppointmentsReportByInsrtuctor: 'report/conflictingAppointmentsReportByInsrtuctor.htm',
			url_conflictingAppointmentsReportByRoom: 'report/conflictingAppointmentsReportByRoom.htm',
			url_cancel_conflict_appointment: 'report/cancelConflictAppointment.htm',
			//For GSSP-161
			url_instructorOutsideAppointmentsReport: 'report/instructorOutsideAppointmentsReport.htm',
			//Added new Active Students Report for GSSP-185
			url_ActiveStudentsReport: 'report/activeStudentsReport.htm',
			//Added new Active Students Report for GSSP-203
			url_StudentsCheckInReport: 'report/StudentCheckInReport.htm',
			//Added new InActive Students report for GSSP-205
			url_InActiveStudentsReport: 'report/inActiveStudents.htm',
			//Added new InActive Students report for GSSP-213
			url_ProfileDetailsReport: 'excelReport',
			
			//print report url
			url_print_masterScheduleReport: "printMasterScheduleReport.htm",
			url_print_rehearsalBookingReport: "printRehearsalBookingReport.htm",
			url_print_rehearsalScheduleReport: "printRehearsalScheduleReport.htm",
			url_print_instructorScheduleReport: 'printInstructorScheduleReport.htm',
			
			url_print_instructorStatusScheduleReport: 'printInstructorStatusScheduleReport.htm',
			
			url_print_cancelledAppointments: 'printCacnelledAppointments.htm',
			url_print_activeStudentsReport: 'printActiveStudentsReport.htm',
			//added for GSSP_203
			url_print_StudentsCheckInReport: 'printStudentCheckInReport.htm',
			//added for GSSP-205
			url_print_inActiveStudentsReport: 'printInActiveStudentsReport.htm',
			/* Added for NewInsAptReport _ June 2015 Enhancement */
			url_print_instructorOpenAppointmentsReport: 'printInstructorOpenAppointmentsReport.htm',
			//For GSSP-170
			url_print_conflictingAppointmentsReportByInsrtuctor: 'printConflictAppointmentsReportByInsrtuctor.htm',
			url_print_conflictingAppointmentsReportByRoom: 'printConflictAppointmentsReportByRoom.htm',
			//For GSSP-161
			url_print_instructorOutsideAppointmentsReport: 'printInstructorOutsideAppointmentsReport.htm',
			
			//For GSSP-185
			url_fetch_activitylist: 'report/loadActivityList.htm',
			
			//generate csv file for lesson
			url_generate_csv_for_lesson: 'generateLessonCSV.htm',
			
			//To Generate Excel Report-GSSP184
			url_generate_excel_masterScheduleReport: "excel/MasterScheduleReport",
			url_generate_excel_cancelledAppointments: 'excel/cancelledAppointments',
			url_generate_excel_instructorScheduleReport: 'excel/instructorScheduleReport',
			url_generate_excel_instructorScheduleStatusReport: 'excel/instructorAppointmentStatusScheduleReport',
			url_generate_excel_instructorOpenAppointmentsReport: 'excel/instructorOpenAppointmentsReport',
			url_generate_excel_conflictingAppointmentsReportByRoom: 'excel/conflictingAppointmentsReportByRoom',
			url_generate_excel_conflictingAppointmentsReportByInsrtuctor: 'excel/conflictingAppointmentsReportByInsrtuctor',
			url_generate_excel_instructorOutsideAppointmentsReport: 'excel/instructorOutsideAppointmentsReport',
			url_generate_excel_rehearsalBookingReport: 'excel/rehearsalBookingReport',
			url_generate_excel_rehearsalScheduleReport: 'excel/rehearsalScheduleReport',
			url_generate_excel_activeStudentsReport: 'excel/activeStudentsReport',
			//added for GSSP-203
			url_generate_excel_studentCheckInReport: 'excel/studentCheckInReport',
			//added for GSSP-205
			url_generate_excel_inActiveStudentsReport: 'excel/inActveStudentsReport',
			//added for GSSP-213
			url_generate_excel_profileDetails: 'excel/excelProfileReport',
			//added for GSSP-210
			url_generate_excel_appointmentHistory: 'excel/appointmentHistoryReport',
			formTarget: $('#report_form'),
			formTarget2: $('#report_form2'),
			EL: $('.report-list'),
			$printButton: $('.js-print'),
			$title: $('.report-title'),
			$generateExcel: $('.js-excel')
		}
		this.init(opts);
	}

	Report.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			this.conf = $.extend(this.defaults, opts);
			this.reportForm();
			//added for new activestudents report GSSP-185
			var self = this,
			CONF = self.conf,
			EL = CONF.EL;
			
		},
		
		/*
		 * Init startDate, endDate and inputExternalId
		 */
		initDate: function() {
			$('input[name="startDate"]').val(moment(new Date()).format('L'));
			$('input[name="endDate"]').val(moment(new Date()).format('L'));
			/* Added for NewInsAptReport _ June 2015 Enhancement */
			$('input[name="inputExternalId"]').val('');
			/* Added for NewInsAptReport _ June 2015 Enhancement */
			$('input[name="instructorName"]').val('');
			//Added new Active Students Report for GSSP-185
			$('input[name="activityType"]').val('');
			//added for GSSP-210
			$('input[name="externalId"]').val('');
			$('input[name="externalId"]').keypress(function (e) {
			     //To restrict to digit only for Instructor # field
			    if (e.which != 8 && e.which != 0 && (e.which < 48 || e.which > 57)) {
			    	return false;
			    }
			});
			$('input[name="inputExternalId"]').keypress(function (e) {
			     //To restrict to digit only for Instructor # field
			    if (e.which != 8 && e.which != 0 && (e.which < 48 || e.which > 57)) {
			    	return false;
			    }
			});
		},
		/*
		* Render report form
		*/
		reportForm: function() {
			var self = this,
				CONF = self.conf,
				NewReport_Model;
			NewReport_Model = Backbone.Model.extend({
				initialize: function() {
					var _this = this,
						startDate, 
						endDate, 
						args,
						reportType,
						//For GSSP-170
						reportSubType,
						//For GSSP-161
						dayType,
						tFrom,
						tTo,
						//Added new Active Students Report for GSSP-185
						activityType,
						//added for GSSP-210
						externalId,
						/* Added for NewInsAptReport _ June 2015 Enhancement */
						inputExternalId,
						instructorName,
						formEL = _this.formEL;
					
					/* Modified for NewInsAptReport _ June 2015 Enhancement */
					formEL.reportType.select2().select2('val', '').on('change', function(e) {
						formEL.inputExternalId.val('');
						formEL.instructorName.val('');
						//For GSSP-170
						formEL.reportSubType.select2('val', '');
						//Added new Active Students Report for GSSP-185
						formEL.activityType.el.select2('val', '');
						//For GSSP-161, Added
						formEL.dayType.select2('val', '');
						formEL.tFrom.select2('val', '');
						formEL.tTo.select2('val', '');
						//added for GSSP-210
						formEL.externalId.val('');
						formEL.startDate.on(function() {
							self.cleanReport();
							_this.changePrintUrl();
							_this.changeExcelUrl();
						});
						formEL.endDate.on(function() {
							self.cleanReport();
							_this.changePrintUrl();
							_this.changeExcelUrl();
						});
						self.cleanReport();
						
						type = formEL.reportType.val(),
						_this.changePrintUrl();
						_this.changeExcelUrl();
						
						if(type == 10)
						{
							$.ajax({
								url: CONF.url_fetch_activitylist,
								type: 'GET',
								success: function(result) {
									formEL.activityType.el.select2('val', '').select2({
										data: result,						
										id: function(obj) {
											return obj.activityId;
										},
										formatResult: function(obj) {
											return obj.activityName;
										},
										formatSelection: function(obj) {
											return obj.activityName;
										}
									}).select2('readonly', false);
									
									formEL.activityType.el.show();
									
								
								}
							});
							
							
						}	
						
					});
					//For GSSP-170
					formEL.reportSubType.select2().select2('val', '').on('change', function(e) {
						self.cleanReport();
						_this.changePrintUrl();
						_this.changeExcelUrl();
					});
					/* For GSSP-161, Added */
					formEL.dayType.select2().select2('val', '').on('change', function(e) {
						self.cleanReport();
						_this.changePrintUrl();
						_this.changeExcelUrl();
					});
					formEL.startDate.on('change', function() {
						self.cleanReport();
						_this.changePrintUrl();
						_this.changeExcelUrl();
					});
					formEL.endDate.on('change', function() {
						self.cleanReport();
						_this.changePrintUrl();
						_this.changeExcelUrl();
					});
					//Added new Active Students Report for GSSP-185
					formEL.activityType.el.select2('val', '').on('change', function(e) {
						self.cleanReport();
						_this.changePrintUrl();
						_this.changeExcelUrl();
					});
					formEL.instructorName.on('input', function() {
						self.cleanReport();
						_this.changePrintUrl();
						_this.changeExcelUrl();
					});
					startDate = new Pikaday({
						format: 'MM/DD/YYYY',
						field: formEL.startDate[0]
					});
					endDate = new Pikaday({
						format: 'MM/DD/YYYY',
						field: formEL.endDate[0]
					});
					
					/* For GSSP-161, Added */
					formEL.tFrom.select2().select2('val', '').on('change', function(e) {
						self.cleanReport();
						_this.changePrintUrl();
						_this.changeExcelUrl();
					});
					/* For GSSP-161, Added */
					formEL.tTo.select2().select2('val', '').on('change', function(e) {
						self.cleanReport();
						_this.changePrintUrl();
						_this.changeExcelUrl();
					});
					/* Added for NewInsAptReport _ June 2015 Enhancement */
					formEL.inputExternalId.on('input', function() {
						self.cleanReport();
						_this.changePrintUrl();
						_this.changeExcelUrl();
					});
					formEL.instructorName.on('input', function() {
						self.cleanReport();
						_this.changePrintUrl();
						_this.changeExcelUrl();
					});
					//GSSP-334 Identified the issue and fixed.
				/*	startDate = new Pikaday({
						format: 'MM/DD/YYYY',
						field: formEL.startDate[0]
					});
					endDate = new Pikaday({
						format: 'MM/DD/YYYY',
						field: formEL.endDate[0]
					});*/
					//added for GSSP-213
					formEL.externalId.on('input', function() {
						self.cleanReport();
						_this.changePrintUrl();
						_this.changeExcelUrl();
					});
					
					$('.js-report-date').text(moment().format('dddd, MMMM D, YYYY'));
					$('.js-report-time').text(moment().format('hh:mm:ss A'));
				},
				/*
				* Change the print button url
				*/
				changePrintUrl: function() {
					var _this = this,
						formEL = _this.formEL,
						type = formEL.reportType.val(),
						//For GSSP-170
						subType = formEL.reportSubType.val(),
						//For GSSP-161
						dayType = formEL.dayType.val(),
						tFrom = formEL.tFrom.val(),
						tTo = formEL.tTo.val(),
						startDate = formEL.startDate.val(),
						endDate = formEL.endDate.val(),
						//Added new Active Students Report for GSSP-185
						activityType = formEL.activityType.el.val(),
						//added for GSSP-210
						externalId = formEL.externalId.val(),
						/* Added for NewInsAptReport _ June 2015 Enhancement */
						inputExternalId = formEL.inputExternalId.val(),
						instructorName = formEL.instructorName.val(),
						//added for GSSP-210
						margs = '&externalId=' + externalId + '?startDate=' + startDate + '&endDate=' + endDate,
						args = '?startDate=' + startDate + '&endDate=' + endDate,
						//Added new Students Report for GSSP-203
						rrargs = '?startDate=' + startDate + '&endDate=' + endDate,				
						//Added new Active Students Report for GSSP-185
						iargs = '?startDate=' + startDate + '&endDate=' + endDate  + '&instructorName=' +instructorName+'&activityType=' + activityType,
						/* Added for NewInsAptReport _ June 2015 Enhancement */
						pargs = '?startDate=' + startDate + '&endDate=' + endDate + '&inputExternalId=' + inputExternalId + '&instructorName=' +instructorName,
						/* For GSSP-161, Added */
						oargs = '?tFrom=' + tFrom + '&tTo=' + tTo + '&inputExternalId=' + inputExternalId + '&dayType=' + dayType,
						$printButton = CONF.$printButton,
						url;
						
					switch(type) {
						case '1':
							url = CONF.url_print_masterScheduleReport + args;
							break;
						case '2':
							url = CONF.url_print_rehearsalBookingReport + args;
							break;
						case '3':
							url = CONF.url_print_rehearsalScheduleReport + args;
							break;
						case '4':
							url = CONF.url_print_instructorScheduleReport + args;
							break;
						case '5':
							url = CONF.url_print_cancelledAppointments + args;
							break;
						/* Added for NewInsAptReport _ June 2015 Enhancement */
						case '6':
							url = CONF.url_print_instructorOpenAppointmentsReport + pargs;
							break;
						/* Modified for NewInsAptReport _ June 2015 Enhancement */
						case '7':
							url = CONF.url_generate_csv_for_lesson + args;
							break;
						//For GSSP-170
						case '8':
							if(subType == 1) {
								url = CONF.url_print_conflictingAppointmentsReportByInsrtuctor + args;
							} else {
								url = CONF.url_print_conflictingAppointmentsReportByRoom + args;
							}
							break;
						//For GSSp-161, Added
						case '9':
							url = CONF.url_print_instructorOutsideAppointmentsReport + oargs;
							break;
							//Added new Active Students Report for GSSP-185	
						case '10':
							url = CONF.url_print_activeStudentsReport + iargs;
							break;
							//added for GSSP-203
						case '11':
							url = CONF.url_print_StudentsCheckInReport + rrargs;
							break;
						case '12':
							url = CONF.url_print_inActiveStudentsReport;
							break;
						case '13':
							url = CONF.url_generate_excel_profileDetails + args;
							break;
						case '14':
							url = CONF.url_generate_excel_appointmentHistory + margs;
							break;
						case '15':
							url = CONF.url_print_instructorStatusScheduleReport + args;
							break;
					}
					$printButton.attr('href', url);
				},
				
				/*
				 * Change excel url
				 * 
				 */
				//For GSSP-184
				
				changeExcelUrl: function() {
					var _this = this,
						formEL = _this.formEL,
						type = formEL.reportType.val(),
						
						subType = formEL.reportSubType.val(),
						
						dayType = formEL.dayType.val(),
						//Added new active students report  GSSP-185
						activityType=formEL.activityType.el.val(),
						tFrom = formEL.tFrom.val(),
						tTo = formEL.tTo.val(),
						startDate = formEL.startDate.val(),
						endDate = formEL.endDate.val(),
						externalId = formEL.externalId.val(),
						inputExternalId = formEL.inputExternalId.val(),
						instructorName = formEL.instructorName.val(),
						args = '?startDate=' + startDate + '&endDate=' + endDate,
					//Added new active students report  GSSP-185
						iargs = '?startDate=' + startDate + '&endDate=' + endDate + '&activityType=' + activityType + '&instructorName=' +instructorName,
						
						pargs = '?startDate=' + startDate + '&endDate=' + endDate + '&inputExternalId=' + inputExternalId + '&instructorName=' +instructorName,
						
						oargs = '?tFrom=' + tFrom + '&tTo=' + tTo + '&inputExternalId=' + inputExternalId + '&dayType=' + dayType,
						//added for GSSP-210
						margs = '&externalId=' + externalId + '?startDate=' + startDate + '&endDate=' + endDate,
						$generateExcel = CONF.$generateExcel,
						url;
						
					switch(type) {
						case '1':
							url = CONF.url_generate_excel_masterScheduleReport + args;
							break;
						case '2':
							url = CONF.url_generate_excel_rehearsalBookingReport + args;
							break;
						case '3':
							url = CONF.url_generate_excel_rehearsalScheduleReport + args;
							break;
						case '4':
							url = CONF.url_generate_excel_instructorScheduleReport + args;
							break;
						case '5':
							url = CONF.url_generate_excel_cancelledAppointments + args;
							break;
						
						case '6':
							url = CONF.url_generate_excel_instructorOpenAppointmentsReport + pargs;
							break;
						
						case '7':
							url = CONF.url_generate_csv_for_lesson + args;
							break;
						
						case '8':
							if(subType == 1) {
								url = CONF.url_generate_excel_conflictingAppointmentsReportByInsrtuctor + args;
							} else {
								url = CONF.url_generate_excel_conflictingAppointmentsReportByRoom + args;
							}
							break;
						
						case '9':
							url = CONF.url_generate_excel_instructorOutsideAppointmentsReport + oargs;
							break;
						case '10':
							url = CONF.url_generate_excel_activeStudentsReport + iargs;
							break;
						case '11':
							url = CONF.url_generate_excel_studentCheckInReport + args;
							break;
						case '12':
							url = CONF.url_generate_excel_inActiveStudentsReport;
							break;
						case '13':
							url = CONF.url_generate_excel_profileDetails + args;
							break;
						case '14':
							url = CONF.url_generate_excel_appointmentHistory + margs;
							break;
						case '15':
							url = CONF.url_generate_excel_instructorScheduleStatusReport + args;
							break;
							
							
					}
					$generateExcel.attr('href', url);
				},
				
				
				/*
				* Clean the form filed
				*/
				clear: function() {
					var _this = this,
						formEL = _this.formEL;
					formEL.reportType.select2('val', '');
					//For GSSP-170
					formEL.reportSubType.select2('val', '');
					//For GSSP-161
					formEL.dayType.select2('val', '');
					formEL.tFrom.select2('val', '');
					formEL.tTo.select2('val', '');
					CONF.$title.text('Report');
					formEL.startDate.val('');
					formEL.endDate.val('');
					//Added new Active Students Report for GSSP-185
					formEL.activityType.el.select2('val', '');
				},
				/*
				* Return all params in report form
				*/
				getParams: function() {
					var _this = this,
						formEL = _this.formEL,
						urlVal,
						reportTitle,
						//added for GSSP-210
						externalId = formEL.externalId.val(),
						/* Added for NewInsAptReport _ June 2015 Enhancement */
						inputExternalId = formEL.inputExternalId.val(),
						instructorName = formEL.instructorName.val(),
						typeValue = formEL.reportType.select2('val'),
						//For GSSP-170
						subTypeValue = formEL.reportSubType.select2('val'),
						//For GSSP-161
						dayType = formEL.dayType.select2('val');
						tFrom = formEL.tFrom.select2('val'),
						tTo = formEL.tTo.select2('val'); 
						//Added new Active Students Report for GSSP-185
						activityType = formEL.activityType.el.val(),
						typeValue = formEL.reportType.select2('val');
					if(typeValue == 1) {
						urlVal = CONF.url_masterScheduleReport;
						reportTitle = 'Daily Master Reporting';
					} else if(typeValue == 2) {
						urlVal = CONF.url_rehearsalBookingReport;
						reportTitle = 'Scheduled Rehearsal Reporting';
					} else if(typeValue == 3) {
						urlVal = CONF.url_rehearsalScheduleReport;
						reportTitle = 'Daily Rehearsal Reporting';
					} else if(typeValue == 4) {
						urlVal = CONF.url_instructorScheduleReport;
						reportTitle = 'Daily Studio Reporting';
					} else if(typeValue == 5){
						urlVal = CONF.url_cancelledAppointments;
						reportTitle = "Cancelled Appointments Reporting";
					} 
					/* Added for NewInsAptReport _ June 2015 Enhancement */
					else if(typeValue == 6){
						urlVal = CONF.url_instructorOpenAppointmentsReport;
						reportTitle = "Instructor Open Appointments Reporting";
					} 
					//For GSSP-170
					else if(typeValue == 8){
						if(subTypeValue == 1) {
							urlVal = CONF.url_conflictingAppointmentsReportByInsrtuctor;
							reportTitle = "Conflicting Appointments By Instructor Reporting";
						} else {
							urlVal = CONF.url_conflictingAppointmentsReportByRoom;
							reportTitle = "Conflicting Appointments By Room Reporting";
						}
					}
					
					/* For GSSP-161, Added */
					else if(typeValue == 9){
						urlVal = CONF.url_instructorOutsideAppointmentsReport;
						reportTitle = "Instructor Outside Appointments Reporting";
					}
					//Added new Active Students Report for GSSP-185
					else if(typeValue == 10){
						urlVal = CONF.url_ActiveStudentsReport;
						reportTitle = "Active Students Reporting";
					}
					//Added new Students Check In Report for GSSP-203
					else if(typeValue == 11){
						urlVal = CONF.url_StudentsCheckInReport;
						reportTitle = "Student Check In Sheet Reporting";
					}
					//Added new InActive Students Report for GSSP-205
					else if(typeValue == 12){
						urlVal = CONF.url_InActiveStudentsReport;
						reportTitle = "InActive Students Reporting";
					}
					else if(typeValue == 13){
						urlVal = CONF.url_generate_excel_profileDetails;
						reportTitle = "";
					}
					else if(typeValue == 14){
						urlVal = CONF.url_generate_excel_appointmentHistory;
						reportTitle = "Appoinment History Reportings";
					}
					else if(typeValue == 15) {
						urlVal = CONF.url_instructorStatusScheduleReport;
						reportTitle = 'Instructor Schedule Status Reporting';
					}
					else {
						urlVal = CONF.url_generate_csv_for_lesson;
						reportTitle = "";
					}
					return {
						reportTitle: reportTitle,
						url: urlVal,
						startDate: formEL.startDate.val(),
						endDate: formEL.endDate.val(),
						reportType: typeValue,
						//For GSSP-170
						reportSubType: subTypeValue,
						//Added new Active Students Report for GSSP-185
						activityType: activityType,
						//For GSSP-161
						dayType: dayType,
						tFrom: tFrom,
						tTo: tTo,
						externalId: externalId,
						/* Added for NewInsAptReport _ June 2015 Enhancement */
						instructorName: instructorName,
						inputExternalId: inputExternalId
					}
				},
				formEL: {
					'reportType': CONF.formTarget.find('select[name="reportType"]'),
					//For GSSP-170
					'reportSubType': CONF.formTarget.find('select[name="reportSubType"]'),
					'startDate': CONF.formTarget.find('input[name="startDate"]'),
					'endDate': CONF.formTarget.find('input[name="endDate"]'),
					//For GSSP-161
					'dayType': CONF.formTarget.find('select[name="dayType"]'),
					'tFrom': CONF.formTarget.find('select[name="tFrom"]'),
					'tTo': CONF.formTarget.find('select[name="tTo"]'),
					//Added new Active Students Report for GSSP-185
					
					'activityType': {
						el: CONF.formTarget.find('input[name="activityType"]'),						
						setAll: function(val) {
							this.el.select2({
								placeholder: 'Select Lesson Type',
								data: val,
								id: function(obj) {
									return obj.activityId;
								},
								formatResult: function(obj) {
									return obj.activityName;
								},
								formatSelection: function(obj) {
									return obj.activityName;
								}
							});
							return this;
						},
						set: function(val) {
							if(typeof val ==='object') {
								this.el.select2('data', val).select2('readonly', false);
							}
							if(typeof val === 'string' || typeof val === 'number') {
								this.el.select2('val', val).select2('readonly', false);								
							}
							return this;
						},
						clear: function() {
							this.el.select2('val', '').select2('readonly', false);						
							return this;
						},
						needValid: true
					},	
					//added for GSSP-210
					'externalId': CONF.formTarget.find('input[name="externalId"]'),
					/* Added for NewInsAptReport _ June 2015 Enhancement */
					'instructorName' : CONF.formTarget2.find('input[name="instructorName"]'),
					'inputExternalId': CONF.formTarget.find('input[name="inputExternalId"]')
				}
			});
			
			//For GSSP-170
			NewReport_View = Backbone.View.extend({
				
				el: $('.report-list'),
					
				initialize: function() {
					return this;
				},
				events: {
					'click .js-cancel-report': 'cancel',
				},	
				
				cancel: function(e) {					 					
					var $this = $(e.currentTarget),
						appointmentId = $this.data('appointmentid'),
						recurringStatus = $this.data('recurringstatus'),
						message = 'Would you like to cancel this appointment? <br><br> <input type="checkbox" name="notifyCustomer" id="notify">&nbsp;Send Email to Customer';
					if('Y' == recurringStatus) {
						message = 'Would you like to cancel this occurrence, or all in series? <br><br> <input type="checkbox" name="notifyCustomer" id="notify">&nbsp;Send Email to Customer';
						$('.js-cancel-all').show();
					}

		            bootbox.dialog({
		                title: 'Cancel Appointment',
		                message: message,
		                
		                buttons: {
		                    cancelOne: {
		                        label: 'Cancel this appointment',
		                        className: 'btn btn-important',
		                        callback: function() {		                       
		                    	   		                    	   		    
		                    	   var enabledMail  = false;
		                    	   if($('#notify').is(':checked'))
		                    		{
		                    			enabledMail  = true;
		                    		}                	   	                  	    

		                      	    $.ajax({
		                                url: CONF.url_cancel_conflict_appointment,
		                                type: 'POST',
		                                data:  {
		                                    'appointmentId': appointmentId,		                                  
		                                    'cancelType' : 'single',
		                                    'enableMail' : enabledMail
		                                },
		                                success: function(result) {	                                	
		                                	
		                                	if(result.status) {
	                                			$('.js-create-report').click();
		                                		         	
		                                	} else {
		                                		notification.autoHideError(result.message);
		                                	}
		                                }
		                            });
		                        }
		                    },
		                    cancelAll: {
		                    	label: 'Cancel all appointments',
		                    	className: 'btn btn-important js-cancel-all',
		                    	callback: function() {
		                    		var enabledMail  = false;
			                    	if($('#notify').is(':checked'))
			                    	{
			                    		enabledMail  = true;
			                    	}
			                    	
		                            $.ajax({
		                                url: CONF.url_cancel_conflict_appointment,
		                                type: 'POST',
		                                data:  {
		                                	'appointmentId': appointmentId,		                                  
		                                    'cancelType' : 'more',
		                                    'enableMail' : enabledMail
		                                },
		                                success: function(result) {
		                                	
		                                	if(result.status) {		                                		
		                                		$('.js-create-report').click();
			                               	} else {
			                               		notification.autoHideError(result.message);
			                               	}
		                                }
		                            });
		                    	}
		                    },
		                    cancle: {
		                        label: 'Cancel',
		                        className: 'btn btn-primary',
		                        callback: function() {
		                        	return;
		                        }
		                    }
		                },
		            	
		            	
		            });
		            
		            if('Y' !== recurringStatus) {
		            	$('.js-cancel-all').hide();
		            }

				},			
				
				render: function (html) { 
					this.$el.html(html);
			    }
			});
			
			self.newreport = new NewReport_Model;
			//For GSSP-170
			self.View = new NewReport_View;
			
			return self;
		},
		/*
		* Render report list
		*/
		renderList: function() {
			var self = this,
				CONF = self.conf,
				EL = CONF.EL,
				$printButton = CONF.$printButton,
				$generateExcel=CONF.$generateExcel,
				params,
				tmpl;

			if(!self.validateDate()) {
				notification.autoHideError('Date Range Invalid!');
				return;
			}

			params = self.newreport.getParams();

			if(params.reportType == '') {
				notification.autoHideError('Select report type!');
				return;
			}
			if(params.reportType == 1) {
				tmpl = $('#tmpl_master_schedule_report').html();
			} else if(params.reportType == 2) {
				tmpl = $('#tmpl_rehearsal_booking_report').html();
			} else if(params.reportType == 3) {
				tmpl = $('#tmpl_rehearsal_schedule_report').html();
			} else if(params.reportType == 4){
				tmpl = $('#tmpl_instructor_schedule_report').html();
			} else if(params.reportType == 15){
				tmpl = $('#tmpl_instructor_status_schedule_report').html();
			} else if(params.reportType == 5) {
				tmpl = $('#tmpl_cancelled_appointment_report').html();
			} 
			/* Added for NewInsAptReport _ June 2015 Enhancement */
			else if(params.reportType == 6) {
				if(params.inputExternalId == '' && params.instructorName == '') {
					notification.autoHideError('Please Enter Instructor ID or Instructor Name!');
					return;
				}
				tmpl = $('#tmpl_instructor_open_appointments_report').html();
			}
			//For GSSP-170
			else if(params.reportType == 8) {
				if(params.reportSubType == '') {
					notification.autoHideError('Select report sub type!');
					return;
				}
				if(!self.conflictValidateDate(365)) {
					notification.autoHideError('Date duration cannot exceed 365 days!');
					return;
				}
				if(params.reportSubType == 1) {
					tmpl = $('#tmpl_conflicting_appointments_report_by_instructor').html();
				} else {
					tmpl = $('#tmpl_conflicting_appointments_report_by_room').html();
				}
			}
			//For GSSP-161
			else if(params.reportType == 9) {
				if(params.inputExternalId == '') {
					notification.autoHideError('Please Enter Instructor ID!');
					return;
				}
				if(params.dayType == '') {
					notification.autoHideError('Select report Day!');
					return;
				}
				if(params.tFrom == '') {
					notification.autoHideError('Select From Time!');
					return;
				}
				if(params.tTo == '') {
					notification.autoHideError('Select To Time!');
					return;
				}
				if(!self.outsideValidateDate()) {
					notification.autoHideError('Time Range Invalid!');
					return;
				}
				tmpl = $('#tmpl_instructor_outside_appointments_report').html();
			}
			//Added new Active Students Report for GSSP-185
			else if(params.reportType == 10) {
				if(params.activityType == ''&& params.instructorName == '') {
					notification.autoHideError('Please Select Lesson Type or Enter Instructor Name!');
					return;
				}
								
				tmpl = $('#tmpl_active_students_report').html();
			}
			//added for new student report GSSP-203
			else if(params.reportType == 11) {
				if(!self.conflictValidateDate(365)) {
					notification.autoHideError('Date duration cannot exceed 365 days!');
					return;
				}
				tmpl = $('#tmpl_student_check_in_report').html();
			}
			//added for InActive Students report GSSP-205
			else if(params.reportType == 12) {
				tmpl = $('#tmpl_inactive_students_report').html();
			}
			
			/* Modified for NewInsAptReport _ June 2015 Enhancement */
			if(params.reportType != 7 && params.reportType != 13 && params.reportType != 14) {
				
				//For GSSP-170
				$('#rep-modal').show();
				$('#rep-fade').show();
				
				$.ajax({
					url: params.url,
					type: 'POST',
					data: {
						startDate: params.startDate,
						endDate: params.endDate,
						//For GSSP-161
						dayType: params.dayType,
						tFrom: params.tFrom,
						tTo: params.tTo,
						//Added new Active Students Report for GSSP-185
						activityType: params.activityType,
						//added for GSSP-210
						externalId: params.externalId,
						/* Added for NewInsAptReport _ June 2015 Enhancement */
						instructorName: params.instructorName,
						inputExternalId: params.inputExternalId
					},
					success: function(result) {
						
						//For GSSP-170
						$('#rep-modal').hide();
						$('#rep-fade').hide();
						
						/**
						 * Update the timestamp if report is regenerated.
						 */
						$('.js-report-time').text(moment().format('hh:mm:ss A'));
						
						//show the print Report button after generate the report For GSSP-184
						$printButton.show();
						if((params.reportType!=1)&&(params.reportType!=7))
						{
						$generateExcel.show();
						}
					/*	if((params.reportType==15))
						{
						$generateExcel.hide();
						}*/
						CONF.$title.text(params.reportTitle);
						var html = '';
						result.forEach(function(obj) {
							html += Mustache.render(tmpl, obj);
						});
						//EL.html(html);
						
						//For GSSP-170
						self.View.render(html);
					}
				});
			} else {
				
				if(params.reportType == 7)
				{	
					var form = $('#report_form');
					form.attr('action', CONF.url_generate_csv_for_lesson);
					form.submit();
				}					
				else if(params.reportType == 13)
				
				{
					if(!self.conflictValidateDate(6)) {
						notification.autoHideError('Date duration cannot exceed 7 days!');
						return;
					}
					var form = $('#report_form');
					form.attr('action', CONF.url_generate_excel_profileDetails);
					form.submit()
				}
				else if(params.reportType == 14)
				{
					if(params.externalId == '') {
						notification.autoHideError('Please Enter Customer External ID!');
						return;
					}
					if(!self.conflictValidateDate(7)) {
						notification.autoHideError('Date duration cannot exceed 7 days!');
						return;
					}
					var form = $('#report_form');
					form.attr('action', CONF.url_generate_excel_appointmentHistory);
					form.submit()
				}
			}
		},
		/*
		* Validate the startDate & endDate
		*/
		validateDate: function() {
			var newreport = this.newreport,
				params = newreport.getParams(),
				startDate = params.startDate,
				endDate = params.endDate;	
			if(startDate == '') startDate = moment().format('L');
			if(endDate == '') endDate = moment().format('L');
			return moment(startDate).isBefore(endDate) || moment(startDate).isSame(endDate);
		},
		/*
		* For GSSP-170, The date range shouldn't be more than 365 days for conflict appointments report
		*/
		conflictValidateDate: function(dayrange) {
			var newreport = this.newreport,
				params = newreport.getParams(),
				startDate = params.startDate,
				endDate = params.endDate,
				rangeVal = moment(startDate, "MM/DD/YYYY").add('days', dayrange).format('L');
			if(startDate == '') {
				startDate = moment().format('L');
				rangeVal = moment(startDate, "MM/DD/YYYY").add('days', dayrange).format('L');
			}
			if(endDate == '') endDate = moment().format('L');
			return moment(endDate).isBefore(rangeVal) || moment(endDate).isSame(rangeVal);
		},
		/*
		* For GSSP-161, The time range validation
		*/
		outsideValidateDate: function() {
			var newreport = this.newreport,
				params = newreport.getParams(),
				startDate = moment(params.tFrom, "HH:mm"),
				endDate = moment(params.tTo, "HH:mm");
			return moment(startDate).isBefore(endDate) || moment(startDate).isSame(endDate);
		},
		/*
		* Clean the report data
		*/
		cleanReport: function() {
			var self = this,
				CONF = self.conf,
				EL = CONF.EL,
				$printButton = CONF.$printButton,
				$generateExcel=CONF.$generateExcel,
				formEL = self.formEL,
				/* Added for NewInsAptReport _ June 2015 Enhancement */
				params;
			$("[name='startDate']").attr("disabled",false);
			$("[name='endDate']").attr("disabled",false);
			/* Added for NewInsAptReport _ June 2015 Enhancement */
			params = self.newreport.getParams();
			
			/* Added for NewInsAptReport _ June 2015 Enhancement */
			$("#instructorIdHeadLine").hide();//hide the 'Instructor #'' headline if report type other than open appointments for instructor
			$("#inputExternalId").hide();//hide the 'Instructor #'' text box if report type other than open appointments for instructor
			$("#instructorNameHeadLine").hide();
			$("#instructorName").hide();
			$("#orHeadLine").hide();
			$("#instructorORHeadLine").hide();
			$("#checking").hide();
			//GSSP-210 changes
			$("#customerIdHeadLine").hide();
			$("#externalId").hide();
			//For GSSP-170
			$("#conflictHeadLine").hide();
			$("#conflictId").hide();
			
			//For GSSP-161, Added
			$("#dayHeadLine").hide();
			$("#tRangeheadLine").hide();
			$("#dRangeheadLine").show();
			$("#dayId").hide();
			$("#sDate").show();
			$("#eDate").show();
			$("#tRange").hide();
			//Added new Active Students Report for GSSP-185
			$("#activityIdHeadLine").hide();
			$("#activityType").hide();
			
			
			
			
			
			
			/* Added for NewInsAptReport _ June 2015 Enhancement */
			//GSSP-190, Added
			if(params.reportType == 6) {
				$("#instructorIdHeadLine").show();
				$("#instructorName").show();
				$("#orHeadLine").show();
				$("#inputExternalId").show();
				$("#instructorNameHeadLine").show();
				$("#instructorORHeadLine").show();
				$("#checking").show();
			};
			
			//For GSSP-170
			if(params.reportType == 8) {
				$("#conflictHeadLine").show();
				$("#conflictId").show();
			};
			
			//For GSSP-161, Added
			if(params.reportType == 9) {
				$("#instructorIdHeadLine").show();
				$("#inputExternalId").show();
				$("#dayHeadLine").show();
				$("#tRangeheadLine").show();
				$("#dRangeheadLine").hide();
				$("#dayId").show();
				$("#sDate").hide();
				$("#eDate").hide();
				$("#tRange").show();	
			};
			//Added new Active Students Report for GSSP-185
			if(params.reportType == 10) {
				$("#activityIdHeadLine").show();
				$("#instructorName").show();
				$("#orHeadLine").show();
				$("#activityIdHeadLine").show();
				$("#instructorNameHeadLine").show();
				$("#instructorORHeadLine").show();
				$("#instructorORHeadLine").show();
				$("#activityType").show();
				$("#checking").show();
				$("#sDate").show();
				$("#eDate").show();
			};	
			if(params.reportType == 11) {
				$("#sDate").show();
				$("#eDate").show();
			};
			if(params.reportType == 12) {
				$("[name='startDate']").attr("disabled",true);
				$("[name='endDate']").attr("disabled",true);				
			};
			if(params.reportType == 14) {
				$("#customerIdHeadLine").show();
				$("#externalId").show();	
				$("#sDate").show();
				$("#eDate").show();
			};
			
			EL.empty();//clean the generated report information
			$printButton.hide();//hide the 'print Report' button
			$generateExcel.hide();//hide the GenerateExcel report button
			/**
			 * Update the timestamp since reporttype or time range changed
			 */
			$('.js-report-time').text(moment().format('hh:mm:ss A'));
		}
	}
	return Report;
});