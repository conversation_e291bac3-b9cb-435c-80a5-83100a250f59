define([
	'jquery', 
	'select2', 
	'icheck', 
	'bootbox',
	'mustache',
	'underscore', 
	'backbone',
	'edithour_modal',
	'timeoff_modal',
	'notification'
	
], 
function($, select2, iCheck, bootbox, Mustache, _, Backbone, EdithourModal, TimeoffModal, notification) {
	function Instructor(opts) {
		// De<PERSON><PERSON> set
		this.defaults = {
			url_fetch_collection: 'instructor/loadInstructorList.htm',

			url_fetch_model: 'instructor/loadInstructorDetail.htm',
			
			url_fetch_oneTimeModel: 'onetime/loadonetimes.htm',

			url_update_model: 'updateInstructor.htm',

			EL: $('.instructor-list')
		}

		this.is_changed = is_changed;

		this.init(opts);
	}

	Instructor.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			this.conf = $.extend(this.defaults, opts);

			var self = this;

			self.edithour_modal = new EdithourModal({
				EL: {
					$modal: $('#modal_set_availability'),
					$mask: $('.mask')
				}
			});
			
			self.timeoff_modal = new TimeoffModal({
				EL:{
					$modal: $('#modal_set_timeoff'),
					$mask: $('.mask')
				}
			})

			// Update the availability hours
			$(document).on('click', '.btn-update-availability', function(e) {
				self.edithour_modal.update(e);
			});
			
			$(document).on('click', '.js-close-availability-modal', function() {
				self.edithour_modal.hideModal();
			});
			
			//Update the time off 
			$(document).on('click', '.btn-update-timeoff', function(e){
				self.timeoff_modal.update(e);
			});
			
			$(document).on('click', '.js-close-timeoff-modal', function(e){
				self.timeoff_modal.hideModal();
			});
			
			var CONF = self.conf,
				Instructor_Model, Instructor_View, Instructor_Collection,
				Activity_View,InstructorMode_View;

			// Define instructor model
			Instructor_Model = Backbone.Model.extend({
				methodUrl: {
					'read': CONF.url_fetch_model,
					'create': CONF.url_update_model,
					'update': CONF.url_update_model,
					'delete': ''
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				},
				initialize: function() {

				},
			});

			// Define instrucotr view
			Instructor_View = Backbone.View.extend({
				initialize: function() {
					var _this = this;
					return this;
				},
				events: {
					'click .js-edit-instructor': 'edit',
					'click .close-edit-panel': 'close',
					'click .js-update-instructor': 'update',
					'click .js-set-availability': 'setAvailability',
					'click .js-set-timeoff': 'setTimeoff',
					'ifChanged input[name="active"]': 'changeActive',
				},
				tagName: 'table',
				className: 'table table-list table-fixed ',
				template: $('#tmpl_instructor').html(),
				render: function() {
					this.$el.html(Mustache.render(this.template, this.model.toJSON()));
					return this;
				},
				// Edit instrucotor
				edit: function(e) {
					var _this = this,
						$this = $(e.currentTarget);

					if($this.hasClass('editing')) return;
					// If has chaned, popup the modal
					if(self.is_changed) {
						bootbox.dialog({
							title: 'Warning!',
							message: 'You have unsaved information. Do you want to continue?',
							buttons: {
								success: {
									label: 'Continue',
									className: 'btn btn-important',
									callback: function() {
										CONF.EL.find('.editing').removeClass('editing');
										CONF.EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
										self.hideWarn();
										_this.getOne();
									}
								},
								cancle: {
									label: 'Cancel',
									className: 'btn btn-primary',
									callback: function() {
										self.showWarn();
										return;
									}
								}
							}
						});
					} else {
						CONF.EL.find('.editing').removeClass('editing');
						CONF.EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
						$this.addClass('editing');
						_this.getOne();
					}
				},
				// Ajax request detail instructor
				getOne: function() {
					var _this = this;
					$.ajax({
						url: CONF.url_fetch_model,
						type: 'GET',
						data: {
							id: _this.model.get('id')
						},
						success: function(result) {
							// If session time out will get the login.html page
							// So, if response text has the string 'DOCTYPE' will reload page to jump to the login page
							if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
							_this.model.set(result);
							_this.editRender();
							_this.$el.find('tr').eq(0).addClass('disabled').nextAll().show();
							
							self.is_changed = false;
							is_changed = self.is_changed;
						},
						error: function(err) {
							notification.autoHideError(err);
						}
					});
				},
				// Open the edit panel
				editRender: function() {
					var	activity_view = new Activity_View({model: this.model}),
						time = '',
						timeoff = '',
						oneTime = '',
						weeklist = this.model.get("editHourShowList").list,
						/**
						 * for story GCSS-590
						 */
						timeofflist = this.model.get("timeOffs"),
						ontTimeList = this.model.get("onetimes");
						
					
					weeklist.forEach(function(obj) {
						var startTime = obj.startTimeString,
							endTime = obj.endTimeString,
							weekDay = obj.weekDay;
						if(startTime != '') {
							time += '<p class="row"><span class="span2">' + weekDay + ':</span>' + startTime + ' - ' + endTime  + '</p>';
						}
					});
					
					if(timeofflist.length > 0){
						for(i in timeofflist){
							timeoff += '<p class="row">' + timeofflist[i].timeOffStartToEnd  + '</p>';
						}
					}
					
					if(ontTimeList.length > 0){
						for(i in ontTimeList){
							oneTime += '<p class="row">' + ontTimeList[i].onetimeStartToEnd  + '</p>';
						}
					}
					
					var	instructorMode_view = new InstructorMode_View({model: this.model});
					this.$el.find('.js-availability-wrap').empty().html(time);
					this.$el.find('.js-timeoff-wrap').empty().html(timeoff);
					this.$el.find('.js-oneTimeAvailability-wrap').empty().html(oneTime);
					this.$el.find('.js-activity-wrap').empty().append(activity_view.render().el);
					this.$el.find('.js-instructor-mode-wrap').empty().append(instructorMode_view.renderInsMd().el);
					if(this.model.get('active')) {
						this.$el.find('input[name="active"]').iCheck().iCheck('indeterminate');
					} else {
						this.$el.find('input[name="active"]').iCheck().iCheck('determinate');
					}
				},
				// Close the edit panel
				close: function() {
					this.$el.find('.js-edit-instructor').removeClass('editing');
					self.hideWarn();
					this.$el.find('tr').eq(0).removeClass('disabled').nextAll().hide();
					this.$el.find('input[name="active"]').iCheck('destroy');
					
					self.is_changed = false;
					is_changed = self.is_changed;

					return this;
				},
				// Update the instrucotor
				update: function() {
					var _this = this;
					this.$el.find('.edit-instructor').removeClass('editing');
					this.$el.find('.js-update-instructor').attr('disabled', true);//For GCSS-341 to avoid 'double click'
					self.hideWarn();
					this.model.sync('create', this.model, {
						success: function(result) {
							if(result.status) {
								_this.model.set(result.object);
								_this.render();
								_this.close();
							} else {
								notification.autoHideError(result.message);
							}
							_this.$el.find('.js-update-instructor').attr('disabled', false);//For GCSS-341 to avoid 'double click'
						}
					});
				},
				setAvailability: function() {
					self.edithour_modal.showModal($.extend(true, {}, this.model));
				},
				setTimeoff: function() {
					self.timeoff_modal.showModal($.extend(true, {}, this.model));
				},
				changeActive: function(e) {
					
					self.is_changed = true;
					is_changed = self.is_changed;
					
					if(e.currentTarget.checked) {
						this.model.set('active', false);
					} else {
						this.model.set('active', true);
					}	
				},
				remove: function() {

				}
			});
			// Define the instructor collection
			Instructor_Collection = Backbone.Collection.extend({
				model: Instructor_Model,
				methodUrl: {
					'read': CONF.url_fetch_collection,
					'create': '',
					'update': '',
					'delete': ''
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				},
			});
			
			//---GSSP Instructor Mode update changes
			InstructorMode_View = Backbone.View.extend({
	
				className: '',	
				templateInsMd: $('#tmpl_instructor_mode').html(),
				renderInsMd: function() {
					this.$el.html(Mustache.render(this.templateInsMd, this.model.toJSON()));
					var insMode = this.model.get("serviceModeName");
					 model = this.model;
					this.$el.find('input[name="instructorMode"]').select2({
						placeholder: 'Select Instructor Mode',
						data: this.model.get('notSelectedServiceMode'),
						id: function(obj) {
							return obj.serviceModeId;
						},
						formatResult: function(obj) {
							return obj.serviceModeName;
						}, 
						formatSelection: function(obj) {
				 
							return obj.serviceModeName;
						}
	 
						
					}).select2('val', this.model.get('instructorMode')).on('select2-selecting', function(e) {
						
						self.is_changed = true;
						is_changed = self.is_changed;
						model.set('instructorMode', e.val);
						 
					});
  
					return this;	
				}
			});	
			
		

			
			// Define activity view in the instructor model
			Activity_View = Backbone.View.extend({
				events: {
					'click .js-btn-add': 'add',
					'click .js-remove-activity': 'remove'
				},
				className: '',
				template: $('#tmpl_activity').html(),
				subTemplate: $('#tmpl_activity_item').html(),
				render: function() {
					this.$el.html(Mustache.render(this.template, this.model.toJSON()));
					this.$el.find('input[name="activities"]').select2({
						placeholder: 'Select Activity',
						data: this.model.get('notSelectedActivities'),
						id: function(obj) {
							return obj.activityId;
						},
						formatResult: function(obj) {
							return obj.activityName;
						}, 
						formatSelection: function(obj) {
							return obj.activityName;
						}
					});

					return this;	
				},
				// Add an activity form the selectbox
				add: function() {
					var value = this.$el.find('input[name="activities"]').val(),
						select = this.$el.find('input[name="activities"]'),
						target = this.$el.find('.js-activity-list'),
						tmpl = this.subTemplate,
						model = this.model;
					if(value === '') return;

					_.each(model.get('notSelectedActivities'), function(obj, index) {
						if(obj.activityId == value) {
							model.get('activitys').push(obj);
							target.append(Mustache.render(tmpl, obj));
						}
					});

					var unselected_activity = _.filter(model.get('notSelectedActivities'), function(obj, i) {
						return obj.activityId !== value*1;
					});
					model.set('notSelectedActivities', unselected_activity);

					select.select2('val', '').select2({
						placeholder: 'Select Activity',
						data: this.model.get('notSelectedActivities'),
						id: function(obj) {
							return obj.activityId;
						},
						formatResult: function(obj) {
							return obj.activityName;
						}, 
						formatSelection: function(obj) {
							return obj.activityName;
						}
					});

					self.is_changed = true;
					is_changed = self.is_changed;
				},
				// Remove an acvitity
				// TODO
				// Remove the ralated servive
				remove: function(e) {
					var model = this.model,
						select = this.$el.find('input[name="activities"]'),
						id = $(e.currentTarget).data('id');

					var unselected_activity = _.filter(model.get('activitys'), function(obj, index) {
						return obj.activityId === id*1;
					});
					model.get('notSelectedActivities').push(unselected_activity[0]);

					var selected_activity = _.filter(model.get('activitys'), function(obj, index) {
						return obj.activityId !== id*1;
					});
					model.set('activitys', selected_activity);

					select.select2('val', '').select2({
						placeholder: 'Select Activity',
						data: model.get('notSelectedActivities'),
						id: function(obj) {
							return obj.activityId;
						},
						formatResult: function(obj) {
							return obj.activityName;
						}, 
						formatSelection: function(obj) {
							return obj.activityName;
						}
					});

					$(e.currentTarget).parent().fadeOut(function() {
						$(this).remove();
					});

					self.is_changed = true;
					is_changed = self.is_changed;
				}
			});

			var	instructor_collection = new Instructor_Collection;

			self.Model = Instructor_Model;
			self.View = Instructor_View;
			self.Collection = Instructor_Collection;

			self.instructor_collection = instructor_collection;

			_.bindAll(self, 'addAll', 'addOne');

			instructor_collection.on('set', self.addAll);
			instructor_collection.on('add', self.addOne);

			return self;
		},
		/*
		* Render the instructor list
		*/
		renderList: function() {
			var self = this;

			// Show loadings
			self.showLoadTip();
			
			self.is_changed = false;
			is_changed = self.is_changed;
			
			self.getAll();
			return self;
		},
		/*
		* Ajax request to get all instructor
		*/
		getAll: function() {
			var self = this,
				CONF = self.conf;
			CONF.EL.empty();
			self.instructor_collection.reset();
			$.ajax({
				url: CONF.url_fetch_collection,
				type: 'GET',
				success: function(result) {
					self.instructor_collection.reset();
					self.instructor_collection.set(result.listInstructorDtos);	
				}
			});
		},
		/*
		* Add all instructor in this collection
		*/
		addAll: function() {
			var self = this;
			self.instructor_collection.each(self.addOne);
		},
		/*
		* Add one instructor
		*/
		addOne: function(model) {
			var self = this,
				EL = self.conf.EL;
			var view = new self.View({model: model});

			EL.append(view.render().el);
		},
		/*
		* Show the warn information
		*/
		showWarn: function() {
				var $target = $('.instructor-list tr.disabled').nextAll().find('.js-update-instructor'),
				right = 220,
				top = $target.offset().top - $target.height() - $('.warn-tip').height();
			$('.warn-tip').css({
				right: right,
				top: top
			}).show();
		},
		/*
		* Hide the warn information
		*/
		hideWarn: function() {
			$('.warn-tip').hide();
		},
		/*
		* Show a loading tip
		*/
		showLoadTip: function() {
			var self = this,
				EL = self.conf.EL;
			EL.append('<div class="loader"><p></p></div>');
		}
	}
	return Instructor;
});