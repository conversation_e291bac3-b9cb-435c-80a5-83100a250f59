define([
      'jquery',
      'moment',
      'select2',
      'icheck',
      'pikaday',
      'mustache',
      'underscore',
      'backbone',
      'notification',
      'pagination',
      'instructorschedule'
    ],
    function($,moment, select2, iCheck,Pikaday, Mustache, _, Backbone, notification,Pagination) {
      function CustomerDetail(opts) {
        // Default set
        this.defaults = {
          url_fetch_customer_info: 'customer/info.htm',

         // url_fetch_lessons_history: 'customer/lessons/history.htm',

          //url_fetch_internal_remarks: 'customer/internal_remarks.htm',

          url_fetch_customer_appointments: 'customer/appointments.htm',
          url_send_reminder: 'customer/sendReminder.htm',
          url_update_model: 'updateInstructorScheduleByStaff.htm',

          EL: {
             customer_info :$('.js-customer-detail .customer-detail-info'),
           // lessons_history: $('.js-customer-detail .lessons-history-list'),
            //internal_remarks: $('.js-customer-detail .internal-remarks-list'),
            customer_appointments: $('.js-customer-detail .customer-appointments-table')

            //internal_remarks_pagination: $('.js-customer-detail .internal-remarks-pagination'),
            //lesson_history_pagination: $('.js-customer-detail .lesson-history-pagination')
          },
          formTarget: $('#customer_appointments_form'),
        }

        this.is_changed = is_changed;

        this.init(opts);
      }

      CustomerDetail.prototype = {
        /*
        * Initialize
        * @param {Object} the options
        */
        init: function(opts) {
          this.conf = $.extend(this.defaults, opts);
          this.initCustomerAppointmentsForm();
          var self = this;

          var CONF = self.conf,
              CustomerDetail_Model, 
              ReminderModal_View,
              StatusModal_View,
             appointmentFormInit = self.appointFormInit, 
             appointFormInit = true; 


          // Define Customer info model
          CustomerDetail_Model = Backbone.Model.extend();


          var customer_detail_model = new CustomerDetail_Model;
         // var lessons_history_collection = new Backbone.Collection;
          //var internal_remarks_collection = new Backbone.Collection;
          var customer_appointments_collection = new Backbone.Collection;

          customer_detail_model.on('change',self.customerInfoRefresh,self);
          //lessons_history_collection.on('add',self.lessonsHistoryCollectionRefresh,self);
          //internal_remarks_collection.on('add',self.internalRemarksCollectionRefresh,self);
          customer_appointments_collection.on('add',self.customerAppointmentsRefresh,self);

          self.CustomerDetail_View = Backbone.View.extend({
            tagName: 'div',
            className: 'grid-center',
            template: $('#tmpl_customer_detail_info').html(),
            render: function() {
              this.$el.html(Mustache.render(this.template, this.model.toJSON()));
              return this;
            }
          });
   /*       self.LessonsHistory_View = Backbone.View.extend({
            tagName: 'tbody',
            className: 'customer-appointment-list',
            template: $('#tmpl_customer_lessons_history_view').html(),
            render: function() {
              this.$el.html(Mustache.render(this.template, this.model.toJSON()));
              return this;
            }
          });
          self.InternalRemarks_View = Backbone.View.extend({
            tagName: 'div',
            className: 'grid remarks-expanded',
            template: $('#tmpl_customer_internal_remark_view').html(),
            render: function() {
              this.$el.html(Mustache.render(this.template, this.model.toJSON()));
              return this;
            }
          });*/
          self.CustomerAppointments_View = Backbone.View.extend({
            events: {
              'click .email-header a':'sendReminder',
              'click .show-remarks': 'showRemarks',
              'click .show-notes' : 'showNotes', 
		          'click .submit-remarks' : 'saveRemarks', 
		          'click .close': 'teardown' ,
		          'click .status-modal' : 'statusModal',
					'click .lesson-completed' : 'completeLessonStatus',
					'click .undo-status' : 'undoLessonStatus'
		        },
            tagName: 'tbody',
            className: 'customer-appointment-list',
            template: $('#tmpl_customer_appointments').html(),
            render:function(){
              this.model.set({cid:this.model.cid});
              this.$el.append(Mustache.render(this.template, this.model.toJSON()));
              return this;
            },
            showRemarks: function(e) {
              e.preventDefault();
              var _this = this,
                  el = $(_this.el),
                  thisClicked = $(e.currentTarget);
              if (!thisClicked.attr('data-clicked') || thisClicked.attr('data-clicked') == 'false') {
                el.find('.remarks-hidden').show();
                thisClicked.text('Hide Remarks').attr('data-clicked', 'true');
              }
              else {
                el.find('.remarks-hidden').hide();
                thisClicked.text('Show Remarks').attr('data-clicked', 'false');
              }
            },
            showNotes: function(e) {
              e.preventDefault();
              var _this = this,
              el = $(_this.el),
              thisClicked = $(e.currentTarget);
              if (!thisClicked.attr('data-clicked') || thisClicked.attr('data-clicked') == 'false') {
                thisClicked.text('Hide Notes').attr('data-clicked', 'true');
                el.find('.notes-hidden').show();
              }
              else {
                el.find('.notes-hidden').hide();
                thisClicked.text('Show Notes').attr('data-clicked', 'false');
              }
            }, 
            statusModal: function(e) {
				e.preventDefault();
				var	status_modal_view = new StatusModal_View({
					model: this.model

				});

				status_modal_view.show(); 


			},
			 completeLessonStatus: function(e) {
	            	
	            	e.preventDefault();
					var _this = this,
					_model = _this.model,
					thisClicked = $(e.currentTarget), 
					completedEl = thisClicked.closest('span'),
					appointmentId = _model.get('appointmentId'),
					instructorFirstName = _model.get('instructorId'),
					statusTableDataEl = $('.completed-header[data-id-status="'+appointmentId+'"]'),
					appointmentStatus = 'Completed';
					var cid = thisClicked.attr("cid");
					var model = self.customer_appointments_collection.get(cid);
					this.model.set({'showStatus' : appointmentStatus});
					var ajaxparams ={
							'showStatus' : appointmentStatus,
							'appointmentId' : appointmentId
					}
					//this.model.save({}, {
					 $.ajax({
			                  url: CONF.url_update_model,
			                  type: 'POST',
			                  data:	 {
			                	  params:JSON.stringify(ajaxparams)
			                	  },		                	  
			                success: function (model, response) {
							completedEl.find('.lesson-status-msg').text('Completed').show();
							completedEl.find('.btn-default').hide();
							completedEl.find('.undo-wrapper').show();
	                    	console.log('saved status and send ajax request');
							
						},
						error: function (model, response) {
							console.log("error");
						}
					}); 
				},
				undoLessonStatus: function(e) {
					e.preventDefault();
					var _this = this,
					_model = _this.model,
					thisClicked = $(e.currentTarget),
					appointmentId = _model.get('appointmentId'),
					statusTableDataEl = $('.completed-header[data-id-status="'+appointmentId+'"]'),
					completedEl = thisClicked.closest('span');					
					var cid = thisClicked.attr("cid");
					var model = self.customer_appointments_collection.get(cid);
					this.model.set({'showStatus' : ''});
					var ajaxparams =
						{
							'showStatus' : '',
							'appointmentId' : appointmentId
						}
					//this.model.save({}, {
						$.ajax({
			                  url: CONF.url_update_model,
			                  type: 'POST',
			                  data : {  params:JSON.stringify(ajaxparams)
			                	  
			                  },
			                success: function (model, response) {
							completedEl.find('.undo-wrapper, .lesson-status-msg').hide();
							completedEl.find('.btn-default').show();
	                    	console.log('saved status and send ajax request');
			                 },
			                 error: function (model, response) {
							console.log("error");
						}
					});
				},
            sendReminder:function(e){
              e.preventDefault();
              var thisClicked = $(e.currentTarget);
              var _this = this;
              var _model = _this.model;
                        var cid = thisClicked.attr("cid");
              var model = self.customer_appointments_collection.get(cid);
                        console.log(model);
              model.unset("cid");
              thisClicked.closest('.reminder-link-container').addClass('loading-spinner');
              $.ajax({
                  url: CONF.url_send_reminder,
                  type: 'POST',
                  data: {
                      params:JSON.stringify(model.attributes)
                  },
                  success: function(result) {
                    //make sure user is not logged out
                    if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
                    if (result == false) {
                      var	reminder_modal_view = new ReminderModal_View({
                        model: _model
                      }),
                      reminderStatusMsg = 'Failed to Send Reminder, please try again.';
                    } 
                    else if (result == true){
                      var	reminder_modal_view = new ReminderModal_View({
                        model: _model
          
                      }), 
                      reminderStatusMsg = 'Reminder Sent Successfully!';
                    }
                    reminder_modal_view.show(reminderStatusMsg); 
                    thisClicked.closest('.reminder-link-container').removeClass('loading-spinner');
                  }
                });
              }
          });
          
          StatusModal_View = Backbone.View.extend({
      	    
		        id: 'modal_status_instructor',
		        className: 'modal fade hide instructor-schedule-modal',
		        template: $('#tmpl_customer_status_modal').html(),
		        events: {
		          'hidden': 'teardown', 
		          'click .submit-status' : 'saveStatus',
		           'click .close': 'close' 
		        },
		        
				initialize: function(appointmentId) {
					var _this = this, 
						appointmentId = appointmentId;
					  _this.render();
					return this;
		        },
				
				show: function() {
					var _this = this,
					_model = _this.model;
					elModal = this.$el.modal(),
					pastStatus = _model.get('showStatus');	 
		       	  this.$el.attr('data-backdrop','static');
				  this.$el.modal('show');
					if (pastStatus != null && pastStatus != '') {
						elModal.find('#js-lesson-status').val(pastStatus);
					}
		        },

		        teardown: function() {
		          this.$el.data('modal', null);
		          this.$el.modal.remove();
		        },

		        render: function() {
		          this.$el.html(Mustache.render(this.template));
		          return this;
		        },
		        
		        renderView: function(template) {
		          this.$el.html(template());
		          this.$el.modal({show:false}); // dont show modal on instantiation
				}, 
				
				saveStatus: function(e) {
					var _this = this,
						_model = _this.model;
						elModal = this.$el.modal(),
						appointmentId = _model.get('appointmentId'),
						appointmentStatus = $("#js-lesson-status option:selected").text(),
						statusTableDataEl = $('.completed-header[data-id-status="'+appointmentId+'"]'),

						this.model.set({'showStatus' : appointmentStatus});

						var ajaxparams ={
									'showStatus' : appointmentStatus,
									'appointmentId' : appointmentId
							}
							 $.ajax({
					                  url: CONF.url_update_model,
					                  type: 'POST',
					                  data: {
					                	  params:JSON.stringify(ajaxparams)						                  },
							success: function (model, response) {
								console.log("Status submitted successfullly");
								elModal.find('.modal-body').html('<p class="submit-success-msg">Status Submitted Successfully!</p>');
								$('.modal-footer').hide();
								statusTableDataEl.find('.lesson-status-msg').text(appointmentStatus).show();
								statusTableDataEl.find('.btn-default').hide();
								statusTableDataEl.find('.undo-wrapper').show();
							},
							error: function (model, response) {
								console.log("error");
							}
						});
		        }
		        
			 });
          
          
          ReminderModal_View = Backbone.View.extend({
	    
		        id: 'modal_reminder_instructor',
		        className: 'modal fade hide instructor-schedule-modal',
		        template: $('#tmpl_customer_detail_reminder_modal').html(),
		        events: {
		          'hidden': 'teardown', 
		          'click .submit-status' : 'saveReminderCount',
		           'click .close': 'close' 
		        },
		        
            initialize: function(appointmentId) {
              var _this = this, 
                appointmentId = appointmentId;
                _this.render();
              return this;
                },
            
            show: function(reminderStatusMsg) {
              var _this = this,
              _model = _this.model;
              elModal = this.$el.modal(),
              //pastStatus = _model.get('showStatus');	 
                  this.$el.attr('data-backdrop','static');
              this.$el.modal('show');
              this.$el.find('.email-success-msg').text(reminderStatusMsg);
                },

                teardown: function() {
                  this.$el.data('modal', null);
                  this.$el.modal.remove();
                },

                render: function() {
                  this.$el.html(Mustache.render(this.template));
                  return this;
                },
                
                renderView: function(template) {
                  this.$el.html(template());
                  this.$el.modal({show:false}); // dont show modal on instantiation
            }, 
            
            saveReminderCount: function(e) {
              var _this = this,
                _model = _this.model;
                elModal = this.$el.modal(),
                appointmentId = _model.get('appointmentId'),
                reminderTableDataEl = $('.email-header[data-id-email-reminder="'+appointmentId+'"]'),

                this.model.set({'sentReminderCount' : appointmentStatus});

                this.model.save({}, {
                  success: function (model, response) {
                    console.log("Status submitted successfullly");
                    //elModal.find('.modal-body').html('<p class="submit-success-msg">Reminder Sent Successfully!</p>');
                    $('.modal-footer').hide();
                  },
                  error: function (model, response) {
                    console.log("error");
                  }
                });
                }
		        
		     });

          self.customer_detail_model = customer_detail_model;
         // self.lessons_history_collection = lessons_history_collection;
          //self.internal_remarks_collection = internal_remarks_collection;
          self.customer_appointments_collection = customer_appointments_collection;

          $('.js-customer-detail .js-query-customer-appointments').click(function () {
            self.getCustomerAppointmentsCollection();
          });
          return self;
        },
        /*
        * Render the Booked Appointments list
        */
        renderPage: function(customerId) {
          if(customerId == null){
            return;
          }
          var self = this, 
              CONF = self.conf; 
          if (self.appointmentFormInit == false) {
            self.resetAppointmentDateRange();
          }
          self.customerId= customerId;

          // Show loadings
          self.showLoadTip();

          self.is_changed = false;
          is_changed = self.is_changed;
          appointmentHeader = $('.appointment-header')
          self.emptyAllEL();
          CONF.EL.customer_appointments.append(appointmentHeader);


          self.getCustomerDetailInfo({
            customerId:self.customerId
          });
          // self.getLessonsHistoryCollection();
          // self.getInternalRemarksCollection();
          self.getCustomerAppointmentsCollection();
          return self;
        },
        /*
        * Ajax request to get customer detail info
        */
        getCustomerDetailInfo: function(queryParams) {
          var self = this,
              CONF = self.conf;
          $.ajax({
            url: CONF.url_fetch_customer_info,
            type: 'GET',
            data:queryParams,
            success: function(result) {
              console.log(result);
              self.customer_detail_model.clear();
              self.customer_detail_model.set(result);
            }
          });
        },
       /* getLessonsHistoryCollection: function(queryParams){
          var self = this,
              CONF = self.conf;

          $.ajax({
            url: CONF.url_fetch_lessons_history,
            type: 'GET',
            data:{
              params: JSON.stringify(queryParams)
            },
            success: function(result) {
              self.lessons_history_collection.reset();
              self.lessons_history_collection.set(result.data);
              self.generateAndRenderLessonHistoryPagination(result);
            }
          });
        },
        getInternalRemarksCollection: function(queryParams){
          var self = this,
              CONF = self.conf;

          $.ajax({
            url: CONF.url_fetch_internal_remarks,
            type: 'GET',
            data:{
              params: JSON.stringify(queryParams)
            },
            success: function(result) {
              self.internal_remarks_collection.reset();
              self.internal_remarks_collection.set(result.data);
              //self.generateAndRenderInternalRemarkPagination(result);
            }
          });
        },*/
        getCustomerAppointmentsCollection:function(){
          var self = this,
              CONF = self.conf, 
               EL = CONF.EL.customer_appointments;

          var queryDateData = this.customer_appointments_date_query.getParams();
          var ajaxParams = {
            startDatetime : queryDateData.startDate,
            endDatetime : queryDateData.endDate,
            customerId:self.customerId
          }

          $.ajax({
            url: CONF.url_fetch_customer_appointments,
            type: 'GET',
            data:{
              params: JSON.stringify(ajaxParams)
            },
            success: function(result) {
              EL.find('tbody.customer-appointment-list').remove();
              self.customer_appointments_collection.reset();
              self.customer_appointments_collection.set(result);
            }
          });
        },
        /*
        * set model
        */
        customerInfoRefresh: function(model) {
          var self = this,
              EL = self.conf.EL.customer_info;
          var view = new self.CustomerDetail_View({model: model});
          EL.html(view.render().el);
        },
        /**
         * set lesson history collections
         * @param element
         */
        /*lessonsHistoryCollectionRefresh: function(element){
          var self = this;
          var EL = self.conf.EL.lessons_history;

          var date2Str = element.get('date2');
          if(date2Str){
            var dateArr = date2Str.split('@');
            element.set({date2: moment(dateArr[0]).format("MMMM Do, YYYY hh:mm A "+ "["+dateArr[1]+"]")})
          }

          //convert single model code here
          var view = new self.LessonsHistory_View({model: element});
          EL.append(view.render().el);
        },
        internalRemarksCollectionRefresh: function(element){
          var self = this;
          var EL = self.conf.EL.internal_remarks;
          //convert single model code here

          var remarkDateStr = element.get('remarkDate');
          if(remarkDateStr){
            var arr = remarkDateStr.split('@');
            element.set({remarkDate: moment(arr[0]).format("MMMM Do, YYYY hh:mm A "+ "["+arr[1]+"]")})
          }

          var view = new self.InternalRemarks_View({model: element});
          EL.append(view.render().el);
        },*/
        customerAppointmentsRefresh:function(element){
          console.log(element)
          var self = this;
          var EL = self.conf.EL.customer_appointments;
          //convert single model code here
          var view = new self.CustomerAppointments_View({model: element});
          
          //EL.find('tbody').remove();
          appointmentHeader = EL.find('.appointment-header');
          EL.append(view.render().el);
        },
        emptyAllEL: function(){
          for(var a in this.conf.EL){
            this.conf.EL[a].empty();
          }
        },
        // /*
        // * Show the warn information
        // */
        // showWarn: function() {
        //   var $target = $('.booked-appointments-list tr.disabled').nextAll().find('.js-update-booked-appointments'),
        //       right = 220,
        //       top = $target.offset().top - $target.height() - $('.warn-tip').height();
        //   $('.warn-tip').css({
        //     right: right,
        //     top: top
        //   }).show();
        // },
        /*
        * Hide the warn information
        */
        hideWarn: function() {
          $('.warn-tip').hide();
        },
        /*
        * Show a loading tip
        */
        showLoadTip: function() {
          var self = this;
          for(var a in self.conf.EL){
            self.conf.EL[a].append('<div class="loader"><p></p></div>');
          }
        },
        /*generateAndRenderLessonHistoryPagination:function(opt){
          var self = this;
          var pagination = new Pagination({
            pageInfo:opt,
            pageQuery:function(pageParams){
              self.getLessonsHistoryCollection(pageParams);
            }
          });
          var EL = self.conf.EL.lesson_history_pagination;
          EL.empty();
          EL.append( pagination.pageHtml());
        },
        /**
         * generate and render pagination module
         */
       /* generateAndRenderInternalRemarkPagination:function(opt){
          var self = this;
          var pagination = new Pagination({
            pageInfo:opt,
            pageQuery:function(pageParams){
              self.getInternalRemarksCollection(pageParams);
            }
          });
          var EL = self.conf.EL.internal_remarks_pagination;
          EL.empty();
          EL.append( pagination.pageHtml());
        },*/
        resetAppointmentDateRange: function () {
          var self = this, 
              CONF = self.conf,
              apptFormEl = CONF.formTarget,
              startDate = apptFormEl.find('input[name="startDate"]'),
              endDate = apptFormEl.find('input[name="endDate"]'),
               monthAgo = new Date((new Date().getTime() - 1000*60*60*24*30));
               startDate.val(moment(monthAgo).format('MM/DD/YYYY'));
               endDate.val(moment().format('MM/DD/YYYY'));
              
               
        },
        initCustomerAppointmentsForm:function () {
          var self = this,
              CONF = self.conf,
              Customer_Appointments_Query_Model;
          Customer_Appointments_Query_Model = Backbone.Model.extend({
            formEL: {
              'startDate': CONF.formTarget.find('input[name="startDate"]'),
              'endDate': CONF.formTarget.find('input[name="endDate"]')
            },
            /*
            * Return all params in report form
            */
            getParams: function () {
              var _this = this,
                  formEL = _this.formEL;
              return {
                startDate: formEL.startDate.val(),
                endDate: formEL.endDate.val()
              }
            },
            clear: function(){
              var _this = this,
                  formEL = _this.formEL;
              formEL.startDate.val('');
              formEL.endDate.val('');
            },
            initialize: function() {
              var _this = this,
                  startDate,
                  endDate,
                  formEL = _this.formEL;
              startDate = new Pikaday({
                format: 'MM/DD/YYYY',
                field: formEL.startDate[0]
              });
              endDate = new Pikaday({
                format: 'MM/DD/YYYY',
                field: formEL.endDate[0]
              });

              var monthAgo = new Date((new Date().getTime() - 1000*60*60*24*30));
              formEL.startDate.val(moment(monthAgo).format('MM/DD/YYYY'));
              formEL.endDate.val(moment().format('MM/DD/YYYY'));
            }
          });
          self.customer_appointments_date_query = new Customer_Appointments_Query_Model;
          self.appointmentFormInit = false;
          return self;
        }
      }
      return CustomerDetail;
    }
    );

