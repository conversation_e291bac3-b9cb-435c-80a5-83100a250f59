define([
	'jquery',
	'moment',
	'select2', 
	'icheck', 
	'mustache', 
	'underscore', 
	'backbone',
	'pikaday',
	'notification'
],
function($, moment, select2, iCheck, Mustache, _, Backbone, Pikaday, notification) {
	function Timeoff(opts) {
		// Default set
		this.defaults = {
			url_update: 'timeoff/updateTimeoff.htm',
			url_remove: 'timeoff/deletetimeoff.htm',
			url_load: 'timeoff/loadtimeoffs.htm',
			url_load: 'timeoff/loadtimeoffs.htm',
			url_fetch_instructor_start_time : 'timeoff/getStartTimeByInstructors.htm',
			
			EL: {
				$modal: $('#modal_set_timeoff'),
				$mask: $('.mask')
			}
		}

		// Default versionString
		this.currentVersion = undefined;
		
		this.init(opts);
	}
	Timeoff.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			this.conf = $.extend({}, this.defaults, opts);			
					
			var self = this,
				CONF = self.conf, 
				EL = self.conf.EL,
				$modal = EL.$modal,
				Timeoff_Model, Modal,
				formEL, modal,dateFrom,dateTo;

			// Define time off model
			Timeoff_Model = Backbone.Model.extend({

			});

			self.timeoff_model = new Timeoff_Model;
			// Define modal model
			Modal = Backbone.Model.extend({
				defaults: {

				},
				formEL: {
					'dateFrom': {
						el: $modal.find('input[name="timeOffStartDate"]')
					},
					'dateTo': {
						el: $modal.find('input[name="timeOffendDate"]')
					},
					'timeFrom': {
						el: $modal.find('input[name="timeOffFrom"]'),
						set: function(val) {
							this.el.select2('val', val);
						},
						clear: function() { 
							this.el.select2('val', '');	
							this.el.select2('enable', false);
						},
						setAll: function(val) {
							this.el.select2({
								data: val
							});
							return this;
						}
					},
					'timeTo': {
						el: $modal.find('input[name="timeOffTo"]'),
						set: function(val) {
							this.el.select2('val', val);
						},	
						clear: function() { 
							this.el.select2('val', '');	
							this.el.select2('enable', false);
						},
						setAll: function(val) {
							this.el.select2({
								data: val
							});
							return this;
						}
					
					}
				}
			});
		
			modal = new Modal;
			self.modal = modal;
			formEL = modal.formEL;
		    
			dateFrom = new Pikaday({
				format: 'MM/DD/YYYY',
				field: formEL.dateFrom.el[0]
			});
			
			dateTo = new Pikaday({
				format: 'MM/DD/YYYY',
				field: formEL.dateTo.el[0]
			});
			
		
			// function to the time off
			formEL.dateFrom.el.on('change', function(e) {
				
			    //Changes made for GSSP-188
				var value = this.value;
				
				var id = self.instructorModel.get('id');
				
				formEL.timeFrom.clear();		
						
				$.ajax({

					url: CONF.url_fetch_instructor_start_time,
					type: 'GET',
					data: ({'instructorId':id,'startDate':value}),
					success: function(result) {
						
						if(result.startTimeDTOs && result.startTimeDTOs.length > 0) {
							
							 
							formEL.timeFrom.setAll(result.startTimeDTOs);
							
							formEL.timeFrom.el.select2('enable', true);
						}		
						else
						{
							notification.autoHideError(result.message);
						}	
					
					}	
						
				});
				
				//End of Changes made for GSSP-188
				
			});
			
			
		  formEL.dateTo.el.on('change', function(e) {
				
			    //Changes made for GSSP-188
				var value = this.value;
					
				var id = self.instructorModel.get('id');
				
				formEL.timeTo.clear();		
			
				$.ajax({

					url: CONF.url_fetch_instructor_start_time,
					type: 'GET',
					data: ({'instructorId':id,'startDate':value}),
					success: function(result) {					
											
						if(result.startTimeDTOs && result.startTimeDTOs.length > 0) {
						
							formEL.timeTo.setAll(result.startTimeDTOs);
							
							formEL.timeTo.el.select2('enable', true);
							
						}
						
						else
						{
							notification.autoHideError(result.message);
						}	
										
					}	
						
				});
				
				//End of Changes made for GSSP-188
				
			});
		
				
		},

		/*
		 * Init 
		 */
		initDate: function() {
			$('input[name="startDate"]').val(moment(new Date()).format('L'));
			$('input[name="endDate"]').val(moment(new Date()).format('L'));
		},
		
		/*
		* Show the time info on the right of this Modal
		*/
		
		renderTimeOffList: function(obj){
			var self = this,
				CONF = self.conf,
				EL = self.conf.EL,
				$modal = EL.$modal,
				timeoff = '',
				timeofflist = obj;
				

			if(timeofflist.length > 0){
				for(i in timeofflist){
					timeoff += '<p><a class="icon icon-remove js-remove-timeoff" data-id="'+timeofflist[i].timeoffId+'" href="javascript:;">x</a>' + timeofflist[i].timeOffStartToEnd + '</p>';
				}
			}
			
			$modal.find('.timeoff-list').empty().html(timeoff);
			
			$modal.find('.js-remove-timeoff').on('click',function(){
				
				var timeoffId = $(this).attr('data-id')*1;
				
				$.ajax({
					url: CONF.url_remove,
					type: 'POST',
					data: {'timeoffId':timeoffId},
					success: function(result) {
							var timeoff_tmp ='',
								timeoffList_tmp = result.timeoffs;
						if(result.status){
							
							self.renderTimeOffList(result.timeoffs);
							
							if(timeoffList_tmp.length > 0){
								for(i in timeoffList_tmp){
										timeoff_tmp += '<p class="row">' + timeoffList_tmp[i].timeOffStartToEnd  + '</p>';
								}
							}
							$('.js-timeoff-wrap').empty().html(timeoff_tmp);
							
						}else{
							notification.autoHideError(result.message);
						}
					}
				});
			});
			
		},
		
		/*
		 * Show the pop and set the timeoff
		 */
		showModal:function(obj){
			var self = this,
				CONF = self.conf,
				EL = CONF.EL,
				$mask = EL.$mask,
				$modal = EL.$modal;
			
			// Set the position of Modal
			var win_height = $(window).height(),
				win_width = $(window).width(),
				m_width = 533,
				m_height = 246,
				top = (win_height - m_height) / 2 + $(window).scrollTop(),
				left = (win_width - m_width) / 2;
	
			if(top < 0) top = 10;
			if(left < 0) left = 10;
			$modal.css({
				left: left,
				top: top
			});
			
			this.instructorModel = obj;
						
			$.ajax({
				url: CONF.url_load,
				type: 'GET',
				data: {'instructorId':obj.id},
				success: function(result) {
					self.renderTimeOffList(result.timeoffs);
				}
			});
			
				
			$modal.show();
			$mask.show();
		},
		
		/*
		 * update the time off
		 */
		update: function(e){
			var self = this,
				CONF = self.conf,
				formEL = self.modal.formEL,
				dateFrom = formEL.dateFrom.el.val(),
				dateTo = formEL.dateTo.el.val(),
				timeFrom = formEL.timeFrom.el.val(),
				timeTo = formEL.timeTo.el.val();
				id = self.instructorModel.get('id'),
				data = {'fromTime':timeFrom,'fromDate':dateFrom,'toTime':timeTo,'toDate':dateTo,'instructorId':id};
			
			/**
			 * Disable the update button to avoid double click
			 */
			var $updateButton=$('.btn-update-timeoff');
			$updateButton.prop('disabled', true);
			
			$.ajax({
				url: CONF.url_update,
				type: 'POST',
				contentType: 'application/json',
				data: JSON.stringify(data),
				success: function(result) {
					if(result.status){
						var timeoffList_tmp = result.dto,
							timeoff_tmp = '',
							timeoffpop_tmp = '',
							timeoffList_tmp2='';
						
						if(timeoffList_tmp.length > 0){
							for(i in timeoffList_tmp){
								timeoff_tmp += '<p class="row">' + timeoffList_tmp[i].timeOffStartToEnd  + '</p>';
							}
						}
						$('.js-timeoff-wrap').empty().html(timeoff_tmp);
						
						/**
						 * For GCSS-618,do not hide the modal after update
						 * 1.Build timeoff and render in the timeoff modal immediately
						 * 2.Clean the date and time selected
						 */
						if(timeoffList_tmp.length > 0){
							for(i in timeoffList_tmp){
								timeoffList_tmp2 += '<p><a class="icon icon-remove js-remove-timeoff" data-id="'+timeoffList_tmp[i].timeoffId+'" href="javascript:;">x</a>' + timeoffList_tmp[i].timeOffStartToEnd + '</p>';
							}
						}
						$('.timeoff-list').empty().html(timeoffList_tmp2);
						self.cleanDate();
						self.cleanTime();
						self.renderTimeOffList(result.dto);
						
					}else{
						notification.autoHideError(result.message);
					}
					/**
					 * Enable the update button
					 */
					$updateButton.prop('disabled', false);
				}
			});
		},
		/*
		 * Hide the Modal and clean the data in the Modal
		 */
		hideModal: function(){
			var self = this,
			EL = self.conf.EL,
			formEL = self.modal.formEL;
			
			EL.$modal.hide();
			EL.$mask.hide();
			/**
			 * Clean date and time selected
			 */
			self.cleanDate();
			self.cleanTime();
			
			
			
		},
		/**
		 * Clean the date selected
		 */
		cleanDate: function() {
			var self = this,
			EL = self.conf.EL,
			formEL = self.modal.formEL;
			formEL.dateFrom.el.val('');
			formEL.dateTo.el.val('');
		},
		/**
		 * Clean the time selected
		 */
		cleanTime: function() {
			var self = this,
			EL = self.conf.EL,
			formEL = self.modal.formEL;
			formEL.timeFrom.set('');
			formEL.timeTo.set('');		
			
			formEL.timeFrom.clear();
			formEL.timeTo.clear();
			
		}
	}
	return Timeoff;
});