define(['jquery'], function($) {
	
	function List() {

	}

	List.prototype = {
		/*
		* Slidedown to show the form 
		*/
		showForm: function() {
			var CONF = this.conf;
			CONF.formTarget.parent().slideDown();
			return this;
		},
		/*
		* Slideup to hide the form
		*/
		hideForm: function() {
			var CONF = this.conf;
			CONF.formTarget.parent().slideUp();
			CONF.formTarget.find('.form-msg').hide();
			CONF.formTrigger.removeClass('active');
			this.newactivity.clear();
			return this;
		},
		/*
		* Hide the warn tip
		*/
		hideWarn: function() {
			$('.warn-tip').hide();
		},
		/*
		* Show the warn tip, when user have changed the form filed
		*/
		showWarn: function() {
			var EL = this.conf.EL,
				$target = EL.find('tr.disabled').nextAll().find('.js-update-item'),
				right = 220,
				top = $target.offset().top - $target.height() - $('.warn-tip').height();
			$('.warn-tip').css({
				right: right,
				top: top
			}).show();
		}
	}

	return List;
});