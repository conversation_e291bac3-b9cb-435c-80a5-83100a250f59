define(['jquery', 'backbone' , 'bootbox', 'mustache'], function($, Backbone, bootbox, Mustache) {
	var View = Backbone.View.extend({
		initialize: function() {
			return this;
		},
		tagName: 'table',
		className: 'table table-list table-fixed',
		render: function() {
			this.$el.html(Mustache.render(this.template, this.model.toJSON()));
			return this;
		},
		edit: function(e, module) {
			var _this = this,
				EL = module.conf.EL,
				$this = $(e.currentTarget);

			if($this.hasClass('editing')) return;

			if(module.is_changed) {
				bootbox.dialog({
					title: 'Warning!',
					message: 'You have unsaved information. Do you want to continue?',
					buttons: {
						success: {
							label: 'Continue',
							className: 'btn btn-important',
							callback: function() {
								EL.find('.editing').removeClass('editing');
								EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
								module.hideWarn();
								_this.getOne();
							}
						},
						cancle: {
							label: 'Cancel',
							className: 'btn btn-primary',
							callback: function() {
								module.showWarn();
								return;
							}
						}
					}
				});
			} else {
				EL.find('.editing').removeClass('editing');
				EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
				$this.addClass('editing');
				_this.getOne();		
			}
		},
		/*
		* Ajax request to get the detail activity 
		*/
		getOne: function(url, id) {
			var _this = this;
			$.ajax({
				url: CONF.url_fetch_activity_detail,
				type: 'GET',
				data: {
					id: _this.model.get('activityId'),
				},
				success: function(result) {
					// If session time out will get the login.html page
					// So, if response text has the string 'DOCTYPE' will reload page to jump to the login page
					if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
					_this.model.set(result);
					_this.editRender();
					_this.$el.find('tr').eq(0).addClass('disabled').nextAll().show();
				}
			});
		},
	});

	return View;
});