define([
      'jquery',
      'underscore',
      'backbone',
      'mustache',
    ],
    function($,_, Backbone,Mustache) {

      /**
       * opts:{
       *    pageInfo:page model data for template render,
            pageQuery:function for ajax query
       * }
       * @param opts
       * @constructor
       */
  function Pagination(opts) {
    this.init(opts)
  }

  Pagination.prototype = {
    init:function (opts) {
      var self = this;

      self.pageQuery = opts.pageQuery;
      self.pageInfo = opts.pageInfo;

      var PaginationView = Backbone.View.extend({
        initialize: function() {
          return this;
        },
        // tagName: 'tr',
        template: $("#tmpl_pagination").html(),
        events: {
          'click .pre-page': 'prePage',
          'click .next-page': 'nextPage',
        },
        render: function() {
          this.$el.html(Mustache.render(this.template, this.model.toJSON()));
          return this;
        },
        prePage:function (e) {
          self.pageQuery({
            targetPage:self.model.get('prePage'),
            pageSize:self.model.get('pageSize')
          })
        },
        nextPage:function (e) {
          self.pageQuery({
            targetPage:self.model.get('nextPage'),
            pageSize:self.model.get('pageSize')
          })
        }
      })

      self.model = new Backbone.Model(self.pageInfo);
      self.View = PaginationView;
    },
    pageHtml:function(){

      var paginationView = new this.View({model: this.model});
      console.log(paginationView);
      return paginationView.render().el;
    },
    pageModel:function () {
      return this.model;
    }
  }

  return Pagination;
});