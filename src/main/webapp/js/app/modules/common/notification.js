define(['jquery'], function() {
	var $notification = $('.notification'),
		$error = $('.noti-error'),
		notification;
	/*
	* Global notifation
	*/	
	notification = {
		/*
		* Show success message
		* @param {String} message
		*/
		show: function(msg) {
			$notification.show().find('.text-noti').text(msg);
		},
		/*
		* Hide success message
		*/
		hide: function() {
			$notification.fadeOut();
		},
		/*
		* Auto hide the success message
		* @param {String} message
		* @param {Number} delay to hide
		*/
		autoHide: function(msg, deday) {
			var self = this;
			self.show(msg);
			setTimeout(function() {
				self.hide();
			}, deday || 5000);
		},
		/*
		* Show error message
		* @param {String} message
		*/
		showError: function(msg) {
			$error.show().find('.text-error').text(msg);
		},
		/*
		* Hide the error message
		*/
		hideError: function() {
			$error.fadeOut();
		},
		/*
		* Auto hide the error message
		* @param {String} message
		* @param {Number} delay to hide
		*/
		autoHideError: function(msg, deday) {
			var self = this;
			self.showError(msg);
			setTimeout(function() {
				self.hideError();
			}, deday || 5000);
		}
	}
	
	// Delegate bind hide function
	$(document).on('click', '.notification', function(e) {
		notification.hide();
	});

	// Auto hide the exsit notification
	if($notification.hasClass('show')) {
		setTimeout(function() {
			notification.hide();	
		}, 5000);
	}

	// Add to the windown
	window.notification = notification;	

	return notification;
});