define([
	'jquery', 
	'select2', 
	'icheck', 
	'bootbox',
	'mustache',
	'underscore', 
	'backbone',
	'customeredit_modal',
	'studiohour_modal',
	'typeahead',
	'hash'
], function($, select2, iCheck, bootbox, Mustache, _, Backbone,CustomereditModal,StudiohourModal, typeahead, hash) {
	function Customer(opts) {
		// <PERSON><PERSON>ult set
		this.defaults = {
				
			url_fetch_collection: 'searchAllCustomerList.htm',
			
			url_customer_quick_search: 'customerQuickSearch.htm',

			url_customer_search: 'customerSearch.htm',
			
			url_get_customer_detail_info: 'getCustomerDetailInfo.htm',
				
			EL: $('.customer-list'),
			
			$quickSearch: $('.search-customer')
			
		}	

		this.init(opts);
	}

	Customer.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			this.conf = $.extend(this.defaults, opts);
			var self = this,
				CONF = self.conf,
				$quickSearch = CONF.$quickSearch,
				Customer_Model, Customer_View, Customer_Empty_View,
				Customer_Collection, template;
			//
			
			 self.customereditModal = new CustomereditModal({
				EL: {
					$modal: $('#modal_edit_customers'),
					$mask: $('.mask')
				}
			});
			 
			 //GSSP-246 -Quick search Analysis with the EXTERNAL ID
				template = Mustache.compile('<a href="javascript:;">{{fullName}} {{externalId}} {{status}}</a>');
			 // Typeahead customer search 
			$quickSearch.typeahead({
				recordId: 'recordId',
				valueKey: 'fullName',
				remote: {
					url: CONF.url_customer_quick_search,
					filter: function(result) {
						return result;
					},
					replace: function(url, urlEncodedQuery) {
						if ($("#studio-customers").is(':checked')) {
							return url + '?studioCustomers=true&searchCriteria=' + urlEncodedQuery;
						} else {
							return url + '?searchCriteria=' + urlEncodedQuery;
						}
					}
				},
				template: template,
				limit: 4,
				//GSSP-202 changes
				minLength: 2,
				engine: Mustache,
				
			}).on('typeahead:selected', function(e, data) {
				if(data) {
					
					if(data.type === 'more') {
						self.search();
						return;
					};
					
					// Add the customerId in the hash
					hash.add({customer: data.recordId});
					//GSSP-246 -Quick search Analysis with the EXTERNAL ID
					var customerTmpl = Mustache.render('<li><a data-id="{{recordId}}" class="js-show-customer-detail-view" href="javascript:;">{{fullName}}</a></li><li>{{email}}</li><li>Phone {{phone}}</li><li>GC ID {{externalId}}</li><li>Lesson Count {{lessonCount}}</li><li>{{instrumentType}}</li>', data);
					$('.filter-list').hide().next().show();

					// Store the customer info in the DOM
					$('.customer-info').data('customer', data);
					$('.customer-info ul').html(customerTmpl);
					
					self.conf.gotoCalendar();
				}
			});

			// Define customer model
			Customer_Model = Backbone.Model.extend({
				methodUrl: {
					'read': CONF.url_fetch_collection
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				}
			});
			
		 
			// Define customer view
			Customer_View = Backbone.View.extend({
				initialize: function() {
					return this;
				},
				events: {
					
					'click .js-view-schedule': 'viewSchedule',
					'click .js-create-appointment': 'createAppointment',
					'click .close-edit-panel': 'close',
					'click .js-edit-customer': 'editCustomer',
					'click .customer-detail-view': 'customerDetailView'
				},
				tagName: 'table',
				className: 'table table-list table-fixed',
				template: $('#tmpl_customer').html(),
				render: function() {
					this.$el.html(Mustache.render(this.template, this.model.toJSON()));
					return this;
				},
				/*
				* View schedule in schudule tab
				* @param {DOM} dom element clicked
				*/
				viewSchedule: function(e) {
					var _this = this,
						CONF = self.conf,
						$this = $(e.currentTarget),
						customerId = $this.data('id');
					hash.add({
						customer: customerId
					});
					$.ajax({
						url: CONF.url_get_customer_detail_info,
						type: 'GET',
						data:  {
							'customerId': customerId
						},
						success: function(result) {
							_this.model.set(result.customerInfo);
							_this.renderCustomerInfo();
							$('.js-show-schedule').click();
						}
					});
				},
				/*
				* Show create appointment modal in schdule tab
				* @param {DOM} dom element clicked
				*/
				createAppointment: function(e) {
					var $this = $(e.currentTarget),
						customerId = $this.data('id'),
						customerName = $this.data('name');
					hash.add({
						customer: customerId
					});

					this.renderCustomerInfo();

					hash.add({filter: ''});
					self.conf.createAppointment(e);
					/**
					 * For GCSS-495,revmoe the style of the date that is not today on calendar control of schedule page
					 * when user wants to view customer page.
					 */ 
					self.conf.gotoToday();
				},
				/*
				* Render the customer info in the left bar
				*/
				renderCustomerInfo: function() {
					//GSSP-246 -Quick search Analysis with the EXTERNAL ID
					var HTML = '<li><a data-id="{{recordId}}" class="js-show-customer-detail-view" href="javascript:;">{{fullName}}</a></li><li>{{email}}</li><li>Phone {{phone}}</li><li>GC ID {{externalId}}</li><li>Lesson Count {{lessonCount}}</li><li>{{instrumentType}}</li>',
						customerTmpl = Mustache.render(HTML, this.model.toJSON());
					$('.filter-list').hide().next().show();
					// Store the customer info in the DOM
					$('.customer-info').data('customer', this.model.toJSON());
					$('.customer-info ul').html(customerTmpl);
				},
				customerDetailView: function (e) {
					var $this = $(e.currentTarget),
							customerId = $this.data('id');
					self.conf.gotoCustomerDetail(customerId);
				},
				editCustomer: function(e) {
					var $this = $(e.currentTarget),
					customerId = $this.data('id');
					var searchCustCriteria = $('input[name="searchCustCriteria"]').val();	 
					self.conf.gotoCustomerEdit(customerId,searchCustCriteria);
				}
			});

			// Define an empty customer view
			Customer_Empty_View = Backbone.View.extend({
				initialize: function() {
					return this;
				},
				tagName: 'table',
				className: 'table table-list table-fixed',
				template: $('#tmpl_emptycustomer').html(),
				render: function() {
					this.$el.html(Mustache.render(this.template));
					return this;
				}
			});
			
			// Define the customer collection
			Customer_Collection = Backbone.Collection.extend({
				model: Customer_Model,
				methodUrl: {
					'read': CONF.url_fetch_collection
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				}
			});

			var customer_collection = new Customer_Collection;

			self.Model = Customer_Model;
			self.View = Customer_View;
			self.Empty_View = Customer_Empty_View;
			self.Collection = Customer_Collection;

			self.customer_collection = customer_collection;

			_.bindAll(self, 'addAll', 'addOne', 'syncDone');

			customer_collection.on('set', self.addAll);
			customer_collection.on('add', self.addOne);
			customer_collection.on('sync', self.syncDone);
		},
		/*
		* Render customer list
		*/
		renderList: function() {
			var self = this;
			// Show loadings
			self.showLoadTip();
			self.is_changed = false;
			self.getAll();	
		},
		/*
		* Quick search customer
		* @param {String} Search Criteria
		*/
		search: function(query) {
			var self = this,
				CONF = self.conf,
				searchCriteria;
			if(query) {
				searchCriteria = query;
			} else {
				searchCriteria = $('.search-customer').val()
			}
		
			
			
			if(searchCriteria === '') {
				searchCriteria =$('input[name="searchCustCriteria"]').val();
			}
			/**
			 * For GCSS-580,doesn't trigger search functionality if less than 3 characters are present
			 */
			$('input[name="searchCustCriteria"]').val(searchCriteria);
			//GSSP-202 changes
			if(searchCriteria.length >= 2) {
				$.ajax({
					url: CONF.url_customer_search,
					type: 'GET',
					data: {
						searchCriteria: searchCriteria,
						studioCustomers: $("#studio-customers").is(':checked')
					},
					success: function(result) {
						CONF.EL.empty();
						self.customer_collection.reset();
						self.customer_collection.set(result);
						// Make sure there is a chance to report on empty results
						//changes made for GSSP-294
						if (self.customer_collection.length == 0) {
							self.conf.EL.append(new self.Empty_View().render().el);
						}
					}
				});
			}
			//$modal.find('input[name="searchCustCriteria"]').val(searchCriteria);
			//_this.model.set('input[name="searchCustCriteria"]').val(searchCriteria);
			//self.Customer_Model.set('searchCustCriteria', searchCriteria);
			
		},
		/*
		* Fetch all customer
		*/
		getAll: function() {
			var self = this;
			self.conf.EL.empty();
			self.customer_collection.reset();
			self.customer_collection.fetch();
		},
		/*
		* Add all customer to collection
		*/
		addAll: function() {
			var self = this;
			self.customer_collection.each(self.addOne);
		},
		/*
		* Add one customer
		*/
		addOne: function(model) {
			var self = this,
				EL = self.conf.EL,
				view;
			view = new self.View({model: model});

			EL.append(view.render().el);
		},
		/*
		* Show a loading tip
		*/
		showLoadTip: function() {
			var self = this,
				EL = self.conf.EL;
			EL.append('<div class="loader"><p></p></div>');
		},
		/* Called on sync complete
		 */
		syncDone: function() {
			var self = this,
				EL = self.conf.EL;
			/* If the collection returned is empty, then render the empty
			 * message view too.
			 */
			if (self.customer_collection.length == 0) {
				EL.append(new self.Empty_View().render().el);
			}
		}
	}
	return Customer;
});