define([
	'jquery',
	'moment',
	'backbone',
	'select2', 
	'icheck' ,
	'pikaday',
	'hash',
	'bootbox',
	'notification',
	'conflictingappointments_modal'

	],
//	Changes made for GSSP-241
	function($, moment, Backbone, select2, iCheck, Pikaday, hash, bootbox, notification,ConflictingAppointments_Model) {
	// Record the status of isrecurring 
	var originStatus ,finalStatus = '';

	function AppointmentModal(opts) {
		// Default set
		this.defaults = {
				url_create: 'calendar/createAppointment.htm',
				url_update: 'calendar/updateAppointment.htm',
				url_get: 'calendar/getAppointmentById.htm',
				url_get_cancel_reason:'calendar/cancelReasonList.htm',
				url_search: 'calendar/quickSearch.htm',
				url_cancel_appointment: 'calendar/cancelAppointment.htm',

				url_fetch_service:'calendar/loadService.htm',
				url_fetch_activity: 'calendar/loadActivitiesByService.htm',
				url_fetch_instructor: 'calendar/loadInstructorByActivity.htm',
				url_fetch_listByService: 'calendar/getServiceById.htm',

				url_fetch_servicelist_by_room_or_instructor: 'calendar/loadServiceListByRoomOrInstructor.htm',
				url_fetch_activitylist_by_appointmentmodel: 'calendar/loadActivityListByAppointmentModel.htm',
				url_fetch_instructor_or_room_list_onchange: 'calendar/loadInstructorOrRoomListOnChange.htm',

				EL: $('#modal_app'),


		}

		this.attrs = {};

		this.init(opts);
	}

	AppointmentModal.prototype = {
			/*
			 * Initialize
			 * @param {Object} the options
			 */
			init: function(opts) {
				this.conf = $.extend({}, this.defaults, opts);

				var self = this,
				CONF = self.conf,
				EL = CONF.EL,
				ATTRS = this.attrs,
				Appointment_Model, appointment_model, Modal;

				//Changes made for GSSP-241
				EL.find('.js-conflict-app').attr('disabled', true);

			// Define the appointment model
			Appointment_Model = Backbone.Model.extend({
				defaults: {
					isRecurring: 'false', //For GCSS-360
					instructorId: '',
					roomId: ''
				}	
			});
			
			appointment_model = new Appointment_Model;

			self.appointment_model = appointment_model;
			
			// Define the modal with Backbone Model
			// FormEL are the appointment form filed elements
			Modal = Backbone.Model.extend({
				defaults: {},
				formEL: {
					'appointmentSeriesId': {
						el: EL.find('input[name="appointmentSeriesId"]'),
						set: function(val) {
							appointment_model.set('appointmentSeriesId', val);
							return this;
						},
						clear: function() {
							appointment_model.unset('appointmentSeriesId');
							return this;
						},
						needValid: false
					},
					'appointmentId': {
						el: EL.find('input[name="appointmentId"]'),
						set: function(val) {
							this.el.val(val);
							appointment_model.set('appointmentId', val);
							return this;
						},
						clear: function() {
							this.el.val('');
							appointment_model.unset('appointmentId');
							return this;
						},
						needValid: false
					},
					'customerId': {
						el: EL.find('input[name="customerId"]'),
						setAll:function(val) {
							this.el.select2('data', val);
							return this;
						},
						set: function(val) {
							var customerList = ATTRS.customerList,
								customers = [],
								ids = '';
							if(!customerList || customerList.length === 0) {
								if(typeof val !== 'object' || val === undefined) {
									this.el.select2('data', []);
									return;
								} 
								else {
									customerList = new Array(val);
								}
							}
							customerList.forEach(function(obj, list) {
								customers.push({
									id: obj.recordId,
									recordId: obj.recordId,
									externalId: obj.externalId,
									text: obj.fullName,
									fullName: obj.fullName
								});
								ids += obj.recordId + ',';
							});
							//GSSP-202 changes
							ids = ids.substring(0, ids.length-1);
							this.el.select2('data', customers);
							appointment_model.set('customerId', ids);
							return this;
						},
						clear: function() {
							this.el.select2('val', '');
							delete ATTRS.customerList;
							appointment_model.unset('customerId');
							return this;
						},
						needValid: true
					},
					'serviceId': { 
						el: EL.find('input[name="serviceId"]'),
						set: function(val) {
							this.el.select2('val', val);
							appointment_model.set('serviceId', val);
							return this;
						},
						clear: function() {
							//For GCSSS-337:disabled service displayed in create appointment form
							this.el.select2('val', '').select2({
								placeholder: 'Select Service Type',
								ajax: {
									url: CONF.url_fetch_service,
									results: function(data, page) {
										return {
											results: data
										}
									}
								},
								id: function(obj) {
									return obj.serviceId;
								},
								formatResult: function(obj) {
									return obj.serviceName;
								},
								formatSelection: function(obj) {
									return obj.serviceName;
								}
							});
							appointment_model.unset('serviceId');
							return this;
						},
						setAll: function(val) {
							this.el.select2({
								placeholder: 'Select Service Type',
								data: val,
								id: function(obj) {
									return obj.serviceId;
								},
								formatResult: function(obj) {
									return obj.serviceName;
								},
								formatSelection: function(obj) {
									return obj.serviceName;
								}
							});
							return this;
						},
						needValid: true
					},
					'bandName': {
						el: EL.find('input[name="bandName"]'),
						wrap: EL.find('.bandName-control'),
						set: function(val) {
							this.el.val(val);
							appointment_model.set('bandName', val);
							return this;
						},
						clear: function() {
							this.el.val('');
							appointment_model.unset('bandName');
							return this;
						},
						needValid: true
					},
						//GSSp 250
						'cancelReason': {
							el: EL.find('input[name="cancelReason"]'),
							wrap: EL.find('.cancelList-control'),
							setAll: function(val) {
								this.el.select2({
									placeholder: 'Select Cancel Reason Type',
									data: val,
									id: function(obj) {
										return obj.appointmentcancelreasonID;
									},
									formatResult: function(obj) {
										return obj.cancelReason;
									},
									formatSelection: function(obj) {
										return obj.cancelReason;
									}
								});
								return this;
							},
							set: function(val) {
								if(typeof val ==='object') {
									this.el.select2('data', val).select2('readonly', false);
								}
								if(typeof val === 'string' || typeof val === 'number') {
									this.el.select2('val', val).select2('readonly', false);
									appointment_model.set('cancelReason', val);
								}
								return this;
							},
							clear: function() {
								this.el.select2('val', '').select2('readonly', false);
								appointment_model.unset('cancelReason');
								return this;
							},
							needValid: true
						},	
					'activityId': {
						el: EL.find('input[name="activityId"]'),
							wrap: EL.find('.activityId-control'),						setAll: function(val) {
							this.el.select2({
								placeholder: 'Select Lesson Type',
								data: val,
								id: function(obj) {
									return obj.activityId;
								},
								formatResult: function(obj) {
									return obj.activityName;
								},
								formatSelection: function(obj) {
									return obj.activityName;
								}
							});
							return this;
						},
						set: function(val) {
							if(typeof val ==='object') {
								this.el.select2('data', val).select2('readonly', false);
							}
							if(typeof val === 'string' || typeof val === 'number') {
								this.el.select2('val', val).select2('readonly', false);
								appointment_model.set('activityId', val);
							}
							return this;
						},
						clear: function() {
							this.el.select2('val', '').select2('readonly', false);
							appointment_model.unset('activityId');
							return this;
						},
						needValid: true
					},	
					'instructorId': {
						el: EL.find('input[name="instructorId"]'),
						wrap: EL.find('.instructor-control'),
						setAll: function(val) {
							this.el.select2('close').select2('val', '').select2({
								placeholder: 'Select an Instructor',
								data: val,
								id: function(obj) {
									return obj.instructorId;
								},
								formatResult: function(obj) {
									return obj.instructorName;
								},
								formatSelection: function(obj) {
									return obj.instructorName;
								}
							});
							return this;
						},
						set: function(val) {
							if(val && val.instructorId !== null) {
								this.el.select2('data', val).select2('readonly', false);
								appointment_model.set('instructorId', val.instructorId);
							}
							return this;
						},
						clear: function() {
							this.el.select2('val', '').select2('readonly', false);
							appointment_model.unset('instructorId');
							return this;
						},
						needValid: true
					},
					'startDate': {
						el: EL.find('input[name="startDate"]'),
						set: function(val) {
							this.el.val(val);
							appointment_model.set('startDate', val);
							return this;
						},
						clear: function() {
							this.el.val('');
							appointment_model.unset('startDate');
							return this;
						},
						needValid: true
					},
					'endDate': {
						el: EL.find('input[name="endDate"]'),
						wrap: EL.find('.endDate-control'),
						set: function(val) {
							this.el.val(val || '');
							appointment_model.set('endDate', val || '');
							return this;
						},
						open: function() {
							appointment_model.set('endDate', this.el.val());
							return this;
						},
						clear: function() {
							this.el.val('');
							this.wrap.hide();
							appointment_model.unset('endDate');
							return this;
						},
						needValid: false 
					},
					'isRecurring': {
						el: EL.find('input[name="isRecurring"]'),
						set: function(val) {
							if(val == 'true') {
								this.el.iCheck('check');
							} else {
								this.el.iCheck('uncheck');
							}
							appointment_model.set('isRecurring', val);
							return this;
						},
						clear: function() {
							this.el.iCheck('uncheck');
							appointment_model.set('isRecurring', 'false');//For GCSS-360
							return this;
						},
						disabled: function() {
							this.el.iCheck('disable');
							return this;
						},
						enabled: function() {
							this.el.iCheck('enable');
							return this;
						},
						needValid: false
					},
					'startTime': {
						el: EL.find('input[name="startTime"]'),
						set: function(val) {
							this.el.select2('val', val);
							appointment_model.set('startTime', val);
							return this;
						},
						clear: function() {
							this.el.select2('val', '');
							appointment_model.unset('startTime');
							return this;
						},
						setAll: function(val) {
							this.el.select2({
								data: val
							});
							return this;
						},
						needValid: true
					},
					'duration': {
						el: EL.find('input[name="duration"]'),
						set: function(val) {
							this.el.select2('val', val);
							appointment_model.set('duration', val);
							return this;
						},
						setAll: function(val) {
							this.el.select2({data: val});
							return this;
						},
						clear: function() {
							this.el.select2('val', '');
							appointment_model.unset('duration');
							return this;
						},
						needValid: true
					},
					'roomId': {
						el: EL.find('input[name="roomId"]'),
						setAll: function(val) {
							this.el.select2('close').select2('val', '').select2({
								placeholder: 'Select Room',
								data: val,
								id: function(obj) {
									return obj.roomId;
								},
								formatResult: function(obj) {
									return obj.profileRoomName;
								},
								formatSelection: function(obj) {
									return obj.profileRoomName;
								}
							});
							return this;
						},
						set: function(val) {
							if(val.roomId !== null) {
								this.el.select2('data', val).select2('readonly', false);
								appointment_model.set('roomId', val.roomId);
							}
							return this;
						},
						setId: function(val) {
							this.el.select2('val', val).select2('readonly', false);
							appointment_model.set('roomId', val);
							return this;
						},
						clear: function() {
							this.el.select2('val', '').select2('readonly', false);
							appointment_model.unset('roomId');
							return this;
						},
						needValid: true	
					},
					'note': {
						el: EL.find('textarea[name="note"]'),
						set: function(val) {
							this.el.val(val).text(val);
							appointment_model.set('note', val);
							return this;
						},
						clear: function() {
							this.el.val('').text('');
							appointment_model.set('note');
							return this;
						},
						needValid: false 
					},
					'parentFullName': {
						el: EL.find('input[name="parentFullName"]'),
						wrap: EL.find('.parentFullName-control'),
						set: function(val) {
							this.el.val(val);
							appointment_model.set('parentFullName', val);
							return this;
						},
						clear: function() {
							this.el.val('');
							appointment_model.unset('parentFullName');
							return this;
						},
						needValid: true
					},
					'phone': {
						el: EL.find('input[name="phone"]'),
						wrap: EL.find('.phone-control'),
						set: function(val) {
							this.el.val(val);
							appointment_model.set('phone', val);
							return this;
						},
						clear: function() {
							this.el.val('');
							appointment_model.unset('phone');
							return this;
						},
						needValid: true
					},
					'email' : {
						el : EL.find('input[name="email"]'),
						wrap: EL.find('.email-control'),
						set : function(val) {
							this.el.val(val);
							appointment_model.set('email',val);
							return this;
						},
						clear : function() {
							this.el.val('');
							appointment_model.unset('email');
							return this;
						},
						needValid : true
					},
				},
								
				initialize: function() {
					var formEL = this.formEL,
						_this = this;

					// Typeahead to multiple choose the customers
					formEL.customerId.el.select2({
						placeholder: 'Enter a customer name',
						multiple: true,
						//GSSP-202 changes
						minimumInputLength: 2,
						ajax: {
							url: CONF.url_search,
							dataType: 'json',
							data: function(term, page){
								return {
									name: term,
									customers: this.val()
								}
							},
							results: function(data, page) {
								// Return more message
								//GSSP-202 changes
								if(data.length > 3) {
									$('.select-more').remove();
									$('#select2-drop').append('<li class="select-more">'+data[data.length-1].status+'</li>');
									return {
									//GSSP-202 changes
										results: data.slice(0, data.length-1)
									}
								} else {
									$('.select-more').remove();
									return {
										results: data
									}
								}
							}
						},
						id: function(customer) {
							return customer.recordId;
						},
						formatResult: function(customer) {
							if(customer.recordId !== null) {
								var customerExternalId = customer.externalId || '',
									formattedId = '('+customerExternalId+')';
								//GSSP-246 -Quick search Analysis with the EXTERNAL ID
								return customer.fullName + ' ' + formattedId;
							} else {
								return customer.status;
							}
						},
						formatSelection: function(customer) {
							var customerExternalId = customer.externalId || '',
									formattedId = '('+customerExternalId+')';
							if(customer.recordId !== null) {
								return customer.fullName + ' ' + formattedId;
							}
						},
						dropdownCssClass: 'dropdown-menu'
					});
					// When the input is empty
					// Remove the 'more' message
					$(document).on('keyup', '#s2id_customer_list input', function(e) {
						if(this.value === '') {
							$('.select-more').remove();
						}
					});
					$(document).on('focus', '#s2id_customer_list input', function(e) {
						if(this.value === '') {
							$('.select-more').remove();
						}
					});

					var startDate_pikaday = new Pikaday({
							format: 'MM/DD/YYYY',
							field: formEL.startDate.el[0]
						}),
						endDate_pikaday = new Pikaday({
							format: 'MM/DD/YYYY',
							field: formEL.endDate.el[0]
						});
					formEL.startDate.el.on('change', function(e) {
						_this.requestForm('startDate', e.currentTarget.value, appointment_model.get('appointmentId'));
					}).on('focus', function() {
						startDate_pikaday.setDate(this.value, true, true);
					});
					formEL.endDate.el.on('focus', function() {
						endDate_pikaday.setDate(new Date(this.value));
					}).on('change', function(e) {
						_this.requestForm('endDate', e.currentTarget.value, appointment_model.get('appointmentId'));
					});
					
					// Aajx request to get the service
					formEL.serviceId.el.select2({
						data: [],
						id: function(obj) {
							return obj.serviceId;
						},
						formatResult: function(obj) {
							return obj.serviceName;
						},
						formatSelection: function(obj) {
							return obj.serviceName;
						}
					}).on('change', function(e) {
						// Service changed will effect the activity form field
						var value = e.currentTarget.value;
						formEL.activityId.el.select2('readonly', true);
						$.ajax({
							url: CONF.url_fetch_activitylist_by_appointmentmodel,
							type: 'GET',
							data: {
								data: JSON.stringify($.extend(self.appointment_model.attributes, {serviceId: value})),
								type: hash.get('filter')
							},
							success: function(result) {
								
								var serviceDTO = result.serviceDTO,
									activityDTOs = result.activityDTOs,
								    role = result.role;
								//Logic to hide Recurring checkbox when service type online
								//console.log("role.roleId========="+role.roleId);
								if(serviceDTO.serviceId == "20"  && role.roleId != "0"){
									
									//console.log("serviceDTO.roleId------"+result.Role);	
									appointment_model.set('isRecurring', 'false')
									$('#isRecurring').hide();
									formEL.endDate.set(appointment_model.get('endDate')).wrap.hide();
									//EL.find('.modal-msg1').text(urmsg).show();
								}
								else{
									//console.log("serviceDTO.serviceId*******"+serviceDTO.serviceId);
									//appointment_model.set('isRecurring', 'true')
									$('#isRecurring').show();
									formEL.endDate.set(appointment_model.get('endDate')).wrap.show();
									
								}

								CONF.EL.find('.activityId-control label').text(serviceDTO.serviceName + ' Type:');
								formEL.activityId.el.select2('val', '').select2({
									data: activityDTOs,
									placeholder: 'Select ' + serviceDTO.serviceName + ' Type',
									id: function(obj) {
										return obj.activityId;
									},
									formatResult: function(obj) {
										return obj.activityName;
									},
									formatSelection: function(obj) {
										return obj.activityName;
									}
								}).select2('readonly', false);
								
								formEL.activityId.clear();//clear the activity model when service changed
								
								if('rehearsal' == serviceDTO.serviceName.toLowerCase()) {
									formEL.bandName.clear().wrap.show();
								} else {
									formEL.bandName.clear().wrap.hide();
								}
								
							}
						});
					});
					formEL.activityId.el.select2({
						placeholder: 'Select Lesson Type',
						data: [],
						id: function(activity) {
							return activity.activityId;
						},
						formatResult: function(activity) {
							return activity.activityName;
						},
						formatSelection: function(activity) {
							return activity.activityName;
						}
					}).on('change', function(e) {
						// Activity changed will effect instrucotor&room form field
						var value = this.value;
						
					
						//appointment_model = value.appointment_model,
						formEL.instructorId.el.select2('enable', false);
						formEL.roomId.el.select2('enable', false);
						formEL.startTime.el.select2('enable', false);
						formEL.duration.el.select2('enable', false);
						$.ajax({
//							url: CONF.url_fetch_instructor,
							url: CONF.url_fetch_instructor_or_room_list_onchange,
							type: 'GET',
							data: {
								data: JSON.stringify($.extend(self.appointment_model.attributes, {activityId: value})),
								type: hash.get('filter'),
								appId: appointment_model.get('appointmentId')
							},
							success: function(result) { 
								
								if(result.startTimeDTOs && result.startTimeDTOs.length > 0) {
									formEL.startTime.setAll(result.startTimeDTOs);
								}
								
								//279 GSSP-  To display the message in bottom of the pop -up
								if( value == "140" || value == "320" ) {	
									
									if($('#isRecurringCheckBox').parent().hasClass('checked')){
										$('#isRecurringCheckBox').parent().removeClass('checked');
										appointment_model.set('isRecurring', 'false');
									}
									$('#isRecurring').hide();
									formEL.endDate.clear().wrap.hide();
								    //LES-631 Activity changed from Jump Start to Trial Lesson 
									var urmsg = 'No Recurring allowed for In-Store/Online Trial Lesson Type.';
									EL.find('.modal-msg1').text(urmsg).show();
								}else {	
								if(result.serviceId != "20"){								
									$('#isRecurring').show();
									//formEL.endDate.set(appointment_model.get('endDate')).wrap.show();
									EL.find('.modal-msg1').text(urmsg).hide();
									}
								}
																
								if(0 != result.durationDTOs.length) {
								//279-GSSP bug fix in duration reshuffle.
									formEL.duration.set(null);
									formEL.duration.setAll(result.durationDTOs);
								}
								
								if(result.appointmentID !== null && result.appointmentID !== '') {//edit
									if(result.instructorDTOs) {
										if(result.instructorDTOs.length > 0) {
											formEL.instructorId.setAll(result.instructorDTOs).set(result.instructorDTOs[0]).wrap.show();
										} else {
											formEL.instructorId.setAll([]).set('').wrap.show();
										}
									} else {
										formEL.instructorId.clear().wrap.hide();
									}
									if(result.roomDTOs && result.roomDTOs.length > 0) {
										formEL.roomId.setAll(result.roomDTOs).set(result.roomDTOs[0]);
									} else {
										formEL.roomId.setAll([]).setId('');
									}
								} else {//create
									if(hash.get('filter')) {
										if(hash.get('filter').toLowerCase() === 'room' || hash.get('filter').toLowerCase() === null) {
											if(undefined !== result.instructorDTOs) {
												if(result.instructorDTOs.length > 0) {
													formEL.instructorId.setAll(result.instructorDTOs).set(result.instructorDTOs[0]).wrap.show();
												} else {
													formEL.instructorId.setAll([]).set('').wrap.show();
												}
											} else {
												formEL.instructorId.clear().wrap.hide();
											}
										} else if(hash.get('filter').toLowerCase() === 'instructor') {
											if(result.roomDTOs && result.roomDTOs.length > 0) {
												formEL.roomId.setAll(result.roomDTOs).set(result.roomDTOs[0]);
											} else {
												//There is no need to call method show,remove it.
												formEL.roomId.setAll([]).set('');
											}
										}
									} else {
										//Render instructor list and room list when create appointment from week view or month view
										if(result.instructorDTOs) {
											if(result.instructorDTOs.length > 0) {
												formEL.instructorId.setAll(result.instructorDTOs).set(result.instructorDTOs[0]).wrap.show();
											} else {
												formEL.instructorId.setAll([]).set('').wrap.show();
											}
										} else {
											formEL.instructorId.clear().wrap.hide();
										}
										if(result.roomDTOs && result.roomDTOs.length > 0) {
											formEL.roomId.setAll(result.roomDTOs).set(result.roomDTOs[0]);
										} else {
											formEL.roomId.setAll([]).setId('');
										}
									}
								}
								formEL.instructorId.el.select2('enable', true);
								formEL.roomId.el.select2('enable', true);
								formEL.startTime.el.select2('enable', true);
								formEL.duration.el.select2('enable', true);
							}
						});
					});

					formEL.instructorId.el.select2({
						data: [],
						id: function(instructor) {
							return instructor.instructorId;
						},
						formatResult: function(instructor) {
							return instructor.instructorName;
						},
						formatSelection: function(instructor) {
							return instructor.instructorName;
						}
					}).on('change', function(e) {
                        _this.requestForm('instructorId', e.currentTarget.value, appointment_model.get('appointmentId'));
                    });

					formEL.startTime.el.select2({
						data: []
					}).on('change', function(e) {
						_this.requestForm('startTime', e.currentTarget.value, appointment_model.get('appointmentId'));
					});

					formEL.duration.el.select2({
                        data: []
                    }).on('change', function(e) {
						_this.requestForm('duration', e.currentTarget.value, appointment_model.get('appointmentId'));
					});

					formEL.roomId.el.select2({
						data: []
					});

						// Form filed change function to set the value into the model
						for(var f in formEL) {
							formEL[f].el.on('change', function() {
								appointment_model.set(this.name, this.value);	
							});
						}

						formEL.isRecurring.el.iCheck().on('ifClicked', function() {
							formEL.isRecurring.el.iCheck('toggle');
						
							
							if(this.checked) {
								appointment_model.set('isRecurring', 'true');
								formEL.endDate.set(appointment_model.get('endDate')).wrap.show();
								} else {
								appointment_model.set('isRecurring', 'false');
								formEL.endDate.clear().wrap.hide();
							}
						});
					},
					requestForm: function(key, value, appId) {
						var _this = this,
						CONF = self.conf,
						attrs = {};
					
					attrs[key] = value;
					$.ajax({
						url: CONF.url_fetch_instructor_or_room_list_onchange,
						data: {
							data: JSON.stringify($.extend(self.appointment_model.attributes, attrs)),
							type: hash.get('filter'),
							appId: appId
						},
						success: function(result) {
							_this.renderInstructorOrRoom(key, result);
						}
					});
				},
				renderInstructorOrRoom: function(key, result) {
					var formEL = this.formEL;
					
					//Set the startTime list
					if(result.startTimeDTOs && result.startTimeDTOs.length > 0) {
						formEL.startTime.setAll(result.startTimeDTOs);
					}
					
					if(result.appointmentID !== null && result.appointmentID !== '') {//edit
						if(result.instructorDTOs) {
							if(result.instructorDTOs.length > 0) {
								formEL.instructorId.setAll(result.instructorDTOs).set(result.instructorDTOs[0]).wrap.show();
							} else {
								formEL.instructorId.setAll([]).set('').wrap.show();
							}
						} else {
							formEL.instructorId.clear().wrap.hide();
						}
						
//						var instructorList = result.instructorDTOs;
//						if(instructorList && instructorList.length > 0) {
//							formEL.instructorId.setAll(result.instructorDTOs).set(result.instructorDTOs[0]).wrap.show();
//						}
						var roomList = result.roomDTOs;
						if(roomList && roomList.length > 0) {
							formEL.roomId.setAll(result.roomDTOs).set(result.roomDTOs[0]);
						} else {
							formEL.roomId.setAll([]);
						}
					} else { //create
						var viewFilter = hash.get('filter');
						if(viewFilter) {
							if(viewFilter.toLowerCase() === 'room') {
								if(result.instructorDTOs && result.instructorDTOs.length > 0) {
									formEL.instructorId.setAll(result.instructorDTOs).set(result.instructorDTOs[0]).wrap.show();
								} else {
									formEL.instructorId.setAll(result.instructorDTOs || []);
								}
							} else if(viewFilter.toLowerCase() === 'instructor') {
								var roomList = result.roomDTOs;
								if(roomList && roomList.length > 0) {
									formEL.roomId.setAll(result.roomDTOs).set(result.roomDTOs[0]);
								} else {
									formEL.roomId.setAll(result.roomDTOs || []);
								}
							} 
						} else { //Not room view or instructor view
							var instructorList = result.instructorDTOs;
							var roomList = result.roomDTOs;
							if(instructorList && instructorList.length > 0) {
								formEL.instructorId.setAll(result.instructorDTOs).set(result.instructorDTOs[0]).wrap.show();
							} else {
								formEL.instructorId.setAll([]).clear();//For GCSS-485
							}
							if(roomList && roomList.length > 0) {
								formEL.roomId.setAll(result.roomDTOs).set(result.roomDTOs[0]);
							} else {
								formEL.roomId.setAll([]).clear();//For GCSS-485
							}
						}
					}
					
				}
			});

				this.modal = new Modal;

				$(document).on('click', '.modal-close.modal-app-close', function(e) {
					self.hide();
				});


				//Changes made for GSSP-241

				self.conflicting_model = new ConflictingAppointments_Model({
					EL:{
						$modal: $('#modal_conflicting_appointment'),
						$mask: $('.mask')
					}
				})


			

				self.cancel_appointment_model = new ConflictingAppointments_Model({
					EL:{
						$modal: $('#modal_cancel_appointment'),
						$mask: $('.mask')
					}
				})



				$(document).on('click', '.js-close-conflicting-appointment-modal', function(e){
					self.conflicting_model.hideModal();
				});

				// Changes made for GSSP-250	changed for the click function of radio button
				$(document).on('click', '.js-cancel-type', function(e){

					var recStatus = $("input:radio[name='Cancel_appointment']:checked").val();


					$.ajax({
						url: CONF.url_get_cancel_reason,						
						type: 'POST',
						data:  {
							'recStatus': recStatus
						},

						success: function(result) {

							

							$('#cancel_reason_form #select-medium').html("");
							
							$('#cancel_reason_form #select-medium').append($("<option  selected disabled hidden></option>")
									.attr("appointmentcancelreasonID", 0).text("Select Cancel Reason"));
							
							$.each(result.cancelReasonList,function(index,json){


								$('#cancel_reason_form #select-medium').append($("<option></option>")
										.attr("appointmentcancelreasonID", json.appointmentcancelreasonID).text(json.cancelReason));
							});  

							$('#cancel_reason_form #select-medium').attr('disabled', false);
							
							$('#cancel_reason_form #cancelAppointment').attr('disabled', false);
						}
					});
				});
			return this;
		},
		/*
		* Create the appointment function
		* @param {DOM} the clicked dom element
		*/
		create: function(target) {
			var self = this,
				EL = self.conf.EL,
				modal = self.modal,
				formEL = modal.formEL,
				customerData,
				data,
				params = {};

				//Empty the duration drop down list when open appointment dialog
				formEL.duration.el.select2({
					placeholder: 'Select Duration',
					data: []
				});
				//If is room view currently,empty the instructor drop down list else empty the room drop down list
				var dayViewType = hash.get('filter');
				if(dayViewType) {
					if(dayViewType.toLowerCase() === 'room') {
						formEL.instructorId.el.select2({
							placeholder: 'Select an Instructor',
							data: []
						});
					} else if(dayViewType.toLowerCase() === 'instructor') {
						formEL.roomId.el.select2({
							placeholder: 'Select Room',
							data: []
						});
					}
				}			
				// Enable isrecurring
				$('#isRecurring').show();//TO reset the hidden recurring in create appointments pop up -GSSP 279
				formEL.isRecurring.enabled();

				// The default labble of activity type : lesson type
				EL.find('.activityId-control label').text('Lesson Type:');

				customerData = $('.customer-info').data('customer');
				if(customerData) {
					formEL.customerId.setAll(new Array(customerData)).set(customerData);
				}

				data = $(target).data();
				// Take the time & instructor or room into the modal form
				if(data !== null) {
					var timeline = [
						'05:00',
						'05:30',
						'06:00',
						'06:30',
						'07:00',
						'07:30',
						'08:00',
						'08:30',
						'09:00',
						'09:30',
						'10:00', 
						'10:30',
						'11:00', 
						'11:30',
						'12:00', 
						'12:30',
						'13:00', 
						'13:30',
						'14:00', 
						'14:30',
						'15:00', 
						'15:30',
						'16:00', 
						'16:30',
						'17:00', 
						'17:30',
						'18:00', 
						'18:30',
						'19:00',
						'19:30',
						'20:00', 
						'20:30',
						'21:00', 
						'21:30',
						'22:00',
						'22:30',
						'23:00',
						'23:30'],
						time = timeline[data.time],
						view = hash.get('view'),
						date = moment(hash.get('date'));

					// Filter item will effect the appointment modal when create
					// Preselected the data into the appointment modal form filed
					var filter = $('.filter-list');

					if(filter.find('input[name="instructor"]:checked').length === 1) {
						var instructor = {
								instructorId: filter.find('input[name="instructor"]:checked').val(),
								instructorName: filter.find('input[name="instructor"]:checked').data('name')
						}
						formEL.instructorId.setAll(new Array(instructor)).set(instructor);
					} else if(data.instructorid !== undefined) {
						var instructor = {
								instructorId: data.instructorid,
								instructorName: $('#instructor_'+data.instructorid).text()
						}
						hash.add({filter: 'instructor'});
						formEL.instructorId.setAll(new Array(instructor)).set(instructor);
					}

					if(filter.find('input[name="room"]:checked').length === 1) {
						var room = {
								roomId: filter.find('input[name="room"]:checked').val(),
								profileRoomName: filter.find('input[name="room"]:checked').data('name')
						}
						formEL.roomId.setAll(new Array(room)).set(room);
					} else if(data.roomid !== undefined) {
						var room = {
								roomId: data.roomid,
								profileRoomName: $('#room_'+ data.roomid).text()
						}
						hash.add({filter: 'room'});
						formEL.roomId.setAll(new Array(room)).set(room);
					}

					if(filter.find('input[name="appointmentType"]:checked').length === 1) {
						var activity = {
								activityId: filter.find('input[name="appointmentType"]:checked').val(),
								activityName: filter.find('input[name="appointmentType"]:checked').data('name')
						}

						formEL.activityId.setAll(new Array(activity)).set(activity.activityId);
					}

					if(filter.find('input[name="serviceType"]:checked').length === 1) {
						var currentService = filter.find('input[name="serviceType"]:checked').val(),
						serviceArr = [];
						filter.find('input[name="serviceType"]').each(function() {
							serviceArr.push({
								serviceId: $(this).val(),
								serviceName: $(this).data('name')
							});
						});					
						//GSSP-261 start
						CONF = self.conf,
						appointment_model = self.appointment_model;
						var value = $('input[name="serviceType"]:checked').val();
						formEL.activityId.el.select2('readonly', true);
						$.ajax({
							url: CONF.url_fetch_activitylist_by_appointmentmodel,
							type: 'GET',
							data: {
								data: JSON.stringify($.extend(self.appointment_model.attributes, {serviceId: value})),
								type: hash.get('filter')
							},
							success: function(result) {
								
								var serviceDTO = result.serviceDTO,
									activityDTOs = result.activityDTOs;

								CONF.EL.find('.activityId-control label').text(serviceDTO.serviceName + ' Type:');
								formEL.activityId.el.select2('val', '').select2({
									data: activityDTOs,
									placeholder: 'Select ' + serviceDTO.serviceName + ' Type',
									id: function(obj) {
										return obj.activityId;
									},
									formatResult: function(obj) {
										return obj.activityName;
									},
									formatSelection: function(obj) {
										return obj.activityName;
									}
								}).select2('readonly', false);
								
								formEL.activityId.clear();//clear the activity model when service changed
								
								if('rehearsal' == serviceDTO.serviceName.toLowerCase()) {
									formEL.bandName.clear().wrap.show();
								} else {
									formEL.bandName.clear().wrap.hide();
								}
							}
						});//261 End
						formEL.serviceId.setAll(serviceArr).set(currentService);
					}

					if(view === 'week') {
						var weekday = data.weekday,
						currentDay = date.days();
						if(weekday == currentDay) {
							date = date.format('MM/DD/YYYY');
						} else if(weekday < currentDay) {
							date = date.subtract('days', currentDay - weekday).format('MM/DD/YYYY');
						} else if(weekday > currentDay) {
							date = date.add('days', weekday - currentDay).format('MM/DD/YYYY');
						}
					} else {
						date = date.format('MM/DD/YYYY');
					}

					formEL.startDate.set(date);
					formEL.startTime.set(time);
					params.type = hash.get('filter');
					params.data = JSON.stringify(self.appointment_model.attributes);
					params.time = time;
				} else {
					hash.add({filter: ''});

					formEL.instructorId.el.select2({
						placeholder: 'Select an Instructor',
						data: []
					});

					formEL.roomId.el.select2({
						placeholder: 'Select Room',
						data: []
					});
				}

				self.show('create', params);

				return self;
			},
			/*
			 * Show modal when edit appointment
			 * @param {DOM} the dom element user clicked
			 */
			edit: function(target) {
				var self = this,
				appId = $(target).data('appointmentid');

				self.show('edit', {appId: appId});
				return self;
			},
			/*
			 * Submit the appointment modal both create or edit
			 */
			submit: function(e) {
				var self = this,
				CONF = self.conf,
				EL = CONF.EL,
				modal = self.modal,
				recurringStatus = '',
				data,
				$btn = $(e.currentTarget);
				// Determine isrecurring
				if(undefined !== originStatus && undefined !== finalStatus) {
					if(self.appointment_model.get('isRecurring') == 'true') {
						finalStatus = 1;
					} else {
						self.appointment_model.unset('endDate');
						finalStatus = 0;
					}
					if(0 == originStatus && 0 == finalStatus) {
						recurringStatus = 1;
					}
					if(1 == originStatus && 1 == finalStatus) {
						recurringStatus = 2;
					} 
					if(1 == originStatus && 0 == finalStatus) {
						recurringStatus = 3;
					}
				}
				self.appointment_model.set('recurringStatus', recurringStatus);
				data = self.appointment_model.attributes;
				$btn.attr('disabled', true);
				$.ajax({
					url: EL.find('form').attr('action'),
					type: 'POST',
					data:  {
						'data': JSON.stringify(data),
					},
					success: function(result) {
						// If session time out will get the login.html page
						// So, if response text has the string 'DOCTYPE' will reload page to jump to the login page
						if(typeof result === 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
						if(result.status) {

							/**
							 * For GCSS-525,remove the gray cell after appointment being updated only when operation is successful
							 */
							$('.cal-item-disabled').remove();

							self.hide();
							// Submit success callback
							CONF.afterCreate();
						} else {


							//Changes made for GSSP-241

							if(null != result.isConflicting && result.isConflicting)
							{	
								EL.find('.js-conflict-app').attr('disabled', false);

							}	

							EL.find('.modal-msg').addClass('error').text(result.message).show();
						}
						$btn.attr('disabled', false);
					}
				});
			},
			/*
			 * Set the data into the form filed when edit appointment
			 */
			dataSet: function() {
				var self = this,
				EL = self.conf.EL,
				appointment_model = self.appointment_model,
				appointmentDetail = self.attrs.appointmentDetail,
				serviceList = self.attrs.serviceList,
				customerList = self.attrs.customerList,
				cancelReasonList = self.attrs.cancelReasonList,
				activityList = self.attrs.activityList,
				instructorList = self.attrs.instructorList,
				roomList = self.attrs.roomList,
				durationList = self.attrs.durationList,
				formEL = self.modal.formEL,
				startTimeDTOs = self.attrs.startTimeDTOs,//For GCSS-412,no startTime
				selectedStartTime = self.attrs.selectedStartTime;//For GCSS-412,no startTime
				
				formEL.appointmentId.set(appointment_model.get('appointmentId'));
				formEL.serviceId.setAll(serviceList).set(appointment_model.get('serviceId'));

				if(customerList.length !== 0) {
					formEL.customerId.setAll(customerList).set(customerList);
				}//GSSP-279 changes for update
			    //LES-631 Activity changed from Jump Start to Trial Lesson 
				var removeactivity = appointmentDetail.activityName
				if(removeactivity != "Trial Lesson" || removeactivity != "Online Trial Lesson"){
					activityList.splice( $.inArray(removeactivity, activityList), 1 );					
				}else  {
					activityList =activityList.splice( $.inArray(removeactivity, activityList), 1 );
					}
				//If the activity of a created appointment had been disabled, this make it enable be enable to display 
				if(activityList.length !== 0) {
					
						
						formEL.activityId.setAll(activityList).set({
							activityId: appointmentDetail.activityId,
							activityName: appointmentDetail.activityName
						})
					
				} else {
					formEL.activityId.clear();
				}
				
				//If the activity of a created appointment had been disabled, this make it enable be enable to display
				if(0 != instructorList.length) {
					formEL.instructorId.setAll(instructorList).set({
						instructorId: appointmentDetail.instructorId,
						instructorName: appointmentDetail.instructorName
					}).wrap.show();
				} else {
					formEL.instructorId.clear().wrap.hide();
				}

				if(0 != durationList.length) {
					formEL.duration.setAll(durationList).set(appointment_model.get('duration'));
				}


				formEL.startDate.set(appointment_model.get('startDate'));
//				formEL.startTime.set(appointment_model.get('startTime'));
				formEL.startTime.setAll(startTimeDTOs).set(selectedStartTime);//For GCSS-412,no startTime
				formEL.roomId.setAll(roomList).setId(appointment_model.get('roomId'));

				formEL.bandName.set(appointment_model.get('bandName'));
				formEL.note.set(appointment_model.get('note'));
				//GSSP-279 Bottom message display
				if(appointment_model.get('isRecurring') == 'true' && (appointment_model.get('activityId') != '140' || appointment_model.get('activityId') != '320')) {
					originStatus = 1;

					//For GCSS-426, keep the recurring time on top of the appointment modal shown as the same as startTime
					var msg = 'This appointment occurs every ' + moment(appointment_model.get('startDate')).format('dddd') + ' ' + moment(appointment_model.get('startDate') + ' ' + appointment_model.get('startTime')).format('hh:mm A');

					//For GSSP-144 Issue # 2, to update a single appointment in a series of Reccuring appointments
					var urmsg = 'Uncheck recurring appointment box to update this appointment only.';

					EL.find('.modal-msg').removeClass('error').text(msg).show();
					//For GSSP-144 Issue # 2
					EL.find('.modal-msg1').text(urmsg).show();				
					formEL.endDate.set(appointment_model.get('endDate')).wrap.show();
					formEL.isRecurring.enabled().set('true');
				}  else if ((appointment_model.get('activityId') == '320' || appointment_model.get('activityId') == '140') &&appointment_model.get('isRecurring') != 'true'){
					originStatus = 0;
				    //LES-631 Activity changed from Jump Start to Trial Lesson 
					var recurringmsg = 'No Recurring allowed for In-Store/Online Trial Lesson Type';
					formEL.endDate.clear().wrap.hide();
					formEL.isRecurring.disabled().set('false');
					EL.find('.modal-msg').text(msg).hide();
					//For GSSP-144 Issue # 2
					EL.find('.modal-msg1').text(urmsg).hide();
					EL.find('.modal-msg1').text(recurringmsg).show();	
				}else{
					originStatus = 0;
					formEL.endDate.clear().wrap.hide();
					formEL.isRecurring.disabled().set('false');
					EL.find('.modal-msg').text(msg).hide();
					//For GSSP-144 Issue # 2
					EL.find('.modal-msg1').text(urmsg).hide();
					EL.find('.modal-msg1').text(recurringmsg).hide();	
				}				
			},
			/*
			 * Clear all the data in the form filed when close modal
			 */
			dataClear: function() {
				var self = this,
				modal = self.modal,
				formEL = self.modal.formEL;

				for(var f in modal.formEL) {
					formEL[f].clear();
				}
			},
			/*
			 * Show appointment modal
			 * @param {String} 'edit' or 'create'
			 * @param {String} appointmentId
			 */
			show: function(type, params) {
				var self = this,
				EL = self.conf.EL,
				CONF = this.conf,
				formEL = this.modal.formEL,
				$mask = $('.mask');
				// Set the modal position
				var win_height = $(window).height(),
				win_width = $(window).width();
				var m_width = 470,
				m_height = 495,
				top = (win_height - m_height) / 2 + $(window).scrollTop(),
				left = (win_width - m_width) / 2;
				if(top < 0) top = 10;
				if(left < 0) left = 10;
				EL.css({
					left: left,
					top: top
				});

				//Changes made for GSSP-241
				EL.find('.js-conflict-app').attr('disabled', true);

				if(type === 'edit') {

					EL.find('.js-conflict-app').hide();//For GCSS-241
					$.ajax({
						url: CONF.url_get,
						type: 'GET',
						data: {
							appointmentId: params.appId
						},
						success: function(result) {
							// If session time out will get the login.html page
							// So, if response text has the string 'DOCTYPE' will reload page to jump to the login page
							if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();

							// Clear the previous model
							self.appointment_model.clear();

							// Set model 
							for(var f in self.modal.formEL) {
								self.appointment_model.set(f, result.appointment[f]);
							}

							// Store some data 
							$.extend(self.attrs, {
								appointmentDetail: result.appointment,
								customerList: result.customerList,
								activityList: result.activityList,
								instructorList: result.instructorList,
								serviceList: result.serviceList,
								roomList: result.roomList,
								durationList: result.durationDTOs,
								startTimeDTOs: result.startTimeDTOs,//For GCSS-412,no startTime
								selectedStartTime: result.selectedStartTime,//For GCSS-412,no startTime
								customerDetails: result.appointment.customerDetails
								
							});
								$('#isRecurring').show();	//TO reset the hidden recurring -GSSP 279
										// Change the label by value from server
							var serviceDTO = result.serviceDTO;
													
							$('.activityId-control label').text(serviceDTO.serviceName + ' Type:');
							
							//Changes made for GSSP-229
							formEL.customerId.el.select2('readonly', true);	
							
							formEL.parentFullName.clear().wrap.hide();
							formEL.phone.clear().wrap.hide();
							formEL.email.clear().wrap.hide();
							
							if(result.customerList.length !== 0){
								var parentFullName = result.appointment.customerDetails.parentFullName;
								var phone = result.appointment.customerDetails.phone;
								var email = result.appointment.customerDetails.email;
								
								for(var i =0; i<= result.customerList.length; i++){
								if(parentFullName !== null){	
								formEL.parentFullName.set(parentFullName).wrap.show();								
								}else{
									formEL.parentFullName.clear().wrap.hide();
								}
								if(phone !== null){
								formEL.phone.set(phone).wrap.show();								
								}else{
									formEL.phone.clear().wrap.hide();
								}
								if(email !== null){
									formEL.email.set(email).wrap.show();								
									}else{
										formEL.email.clear().wrap.hide();
									}
							}
							}
							

							if('rehearsal' == serviceDTO.serviceName.toLowerCase()) {
								//formEL.bandName.clear();
								formEL.bandName.wrap.show();
							} else {
								formEL.bandName.clear().wrap.hide();
								
							}
							// Set & show the data in the modal
							self.dataSet();

							// Change the modal title & button text & form action
							EL.find('form').attr('action', CONF.url_update);
							EL.find('.modal-header h5').text('Edit Appointment');
							EL.find('.btn-submit-app').text('Update Appointment');
							EL.show();
							$mask.show();

							//For GCSS-435
							//Validate if the appointment is a historical one,if so,just provide a 'Cancel' button, else provide eidt and update functionality
							var appointmentInstance = result.appointment;
							var validateDate = moment((appointmentInstance.startDate + ' ' + appointmentInstance.startTime), "MM/DD/YYYY HH:mm").isAfter(moment());
							if(!validateDate) {
								
								if(!validateDate && appointmentInstance.activityId !='320' && appointmentInstance.activityId !='140' ){
								EL.find('.btn-submit-app').hide();
								EL.find('.js-cancel-appointment').hide();
								EL.find('.js-cancel-appointment-associate').hide();
								//For GSSP-144 Issue # 2
								EL.find('.modal-msg1').hide();
								}else{
									EL.find('.btn-submit-app').hide();
									EL.find('.js-update-appointment').hide();
									EL.find('.js-cancel-appointment').css({'margin-left': '-105px'}).show();
									EL.find('.js-cancel-appointment-associate').show();
									//For GSSP-144 Issue # 2
									EL.find('.modal-msg1').hide();
								
								}

								//Check if the startTimeDTOs list contains the current appointment startTime
								if(result.startTimeDTOs && result.startTimeDTOs.length > 0) {
									var exists = false;
									result.startTimeDTOs.forEach(function(item) {
										if(item === result.selectedStartTime) {
											exists = true;
										}
									});
									if(!exists) {
										var label = result.selectedStartTimeLabel;
										var startTimeDTOs = [];
										var dto = {};
										dto.id = result.selectedStartTime;
										dto.text = label;
										startTimeDTOs.push(dto);
										formEL.startTime.setAll(startTimeDTOs).set(result.selectedStartTime);//For GCSS-412,no startTime
									}
								} else {
									//Build the startTimeDTOs list with only item
									var label = result.selectedStartTimeLabel;
									var startTimeDTOs = [];
									var dto = {};
									dto.id = result.selectedStartTime;
									dto.text = label;
									startTimeDTOs.push(dto);
									formEL.startTime.setAll(startTimeDTOs).set(result.selectedStartTime);//For GCSS-412,no startTime
								}
							} else {
								EL.find('.btn-submit-app').show();
								EL.find('.js-cancel-appointment').css({'margin-left': '-375px'}).show();
								EL.find('.js-cancel-appointment-associate').css({'margin-right': '5px'}).show();
							}
							//End of GCSS-435
						}
					});
				} else {

					EL.find('.js-conflict-app').show();//For GCSS-241

					// Change the modal title & button text & form action
					EL.find('form').attr('action', CONF.url_create);
					EL.find('.modal-header h5').text('Create an Appointment');
					EL.find('.btn-submit-app').text('Create Appointment');
					EL.find('.js-cancel-appointment').hide();//For GCSS-435
					EL.find('.js-cancel-appointment-associate').hide();//For GCSS-435
					EL.find('.btn-submit-app').show();//For GCSS-435
					EL.find('.modal-msg').hide();
					//For GSSP-144 Issue # 2
					EL.find('.modal-msg1').hide();


					//Changes made for GSSP-229
					formEL.customerId.el.select2('readonly', false);		

					formEL.activityId.wrap.show();
					formEL.instructorId.wrap.show();

					formEL.bandName.needValid = false;
					formEL.parentFullName.needValid = false;
					formEL.phone.needValid = false;
					formEL.email.needValid = false;
					
					formEL.bandName.clear().wrap.hide();
					formEL.parentFullName.clear().wrap.hide();
					formEL.phone.clear().wrap.hide();
					formEL.email.clear().wrap.hide();
					EL.show();
					$mask.show();

					//For GCSS-412
					//Empty the activity drop down list when open appointment dialog
					formEL.activityId.el.select2({
						placeholder: 'Select Lesson Type',
						data: []
					});
					formEL.serviceId.el.select2('enable', false);
					$.ajax({
						url: CONF.url_fetch_servicelist_by_room_or_instructor,
						data: params,
						success: function(result) {
							//Fill in the service drop down list
							if(result.serviceList.length) {
								formEL.serviceId.setAll(result.serviceList).el.select2('enable', true);
							}
							
							//Fill in the time drop down list by profile
							if(result.startTimeDTOs.length > 0) {

								//For GCSS-482
								//If no matchable key in startTimeDTOs list then plus 15 minutes for that time for calendar view only provide like 9:00 or 9:30 
								//E.G 9:30 to 9:45
								if(params.time) {
									if(!_.contains(_.pluck(result.startTimeDTOs, 'id'), params.time)) {
										params.time = params.time.split(':')[0] + ':' + (params.time.split(':')[1]*1 + 15);
									}
								}

								formEL.startTime.setAll(result.startTimeDTOs).set(params.time);
							}
						}
					});
					//End of GCSS-412
				}
				return self;
			},
			//GSSP-250 changes for pop up

			cancel: function(index,obj) {
				var self = this,
				CONF = self.conf,
				EL = self.conf.EL,
				modal = self.modal,
				formEL = modal.formEL,
				appointment_model = self.appointment_model,
				ActivityId = appointment_model.get('activityId');
				ServiceId =appointment_model.get('serviceId')
				//GSSP-279 adding  for cancel pop up
				//GSSP-322 adding Guitar center university to avoid cancel reason
				if(ActivityId !== "400" && ActivityId !== "20" && ActivityId !== "120" && ActivityId !== "100" && ActivityId !== "140" && ActivityId !== "320" && ActivityId !== "200" &&ServiceId === 1) {
					EL = CONF.EL,
					$modal = EL.$modal,
					formEL = this.modal.formEL,
					data = self.appointment_model.attributes,
					
					appointmentId = appointment_model.get('appointmentId'),

					is_recurring = appointment_model.get('isRecurring'),
					data = self.appointment_model.attributes,
					message = 'Please select reason  for cancelling these  Lesson/Rehearsal? ';
							
					if('true' == is_recurring) {
						message = 'Please select reason  for cancelling these  Lesson/Rehearsal? ';
						$('#cancelAppointmentone').show();
						$('#cancelAppointmentall').show();
											}   
					if(undefined !== originStatus && undefined !== finalStatus) {
						if(self.appointment_model.get('isRecurring') == 'true') {
							finalStatus = 1;
						} else {
							self.appointment_model.unset('endDate');
							finalStatus = 0;
						}
						if(0 == originStatus && 0 == finalStatus) {
							recurringStatus = 1;
						}
						if(1 == originStatus && 1 == finalStatus) {
							recurringStatus = 2;
						} 
						if(1 == originStatus && 0 == finalStatus) {
							recurringStatus = 3;
						}
					}				


					self.appointment_model.set('recurringStatus', recurringStatus);
					data = self.appointment_model.attributes;
					var reasonId =  $("#select-medium").find(':selected').attr('appointmentcancelreasonID');
					var dialog =bootbox.dialog({
						title: 'Cancel Appointment',
						size: "large",
						message: '<form class="cancel_reason_form" id = "cancel_reason_form" role="cancel_reason_form " style="height:150px">'+
									'<p>Please select reason  for cancelling these  Lesson/Rehearsal? </p><br><br>'+
									'<input type="radio" id="cancelAppointmentone" class="js-cancel-type" name="Cancel_appointment" value="single" ><span id="cancelonelable">Cancel this Appointment</span></button> &nbsp;&nbsp;'+
									'<input type="radio" id="cancelAppointmentall" class="js-cancel-type" name="Cancel_appointment" value="more" ><span id="cancelalllable">Cancel all Appointment</span></button>'+ 
									'<br><br>'+
									'<table style="width:100%">'+
                                     '<tr>'+
									'<td><select disabled name="cancelReason" class="select-medium select2" id = "select-medium"><option selected disabled hidden>Select Cancel Reason</option></select></td>'+	
									'<td><button type="button" id="cancelAppointment" disabled="disabled" class=" btn btn-important btn-long1 btn-submit-app" style="padding-top:12px;margin-left:5px;margin-right:10px"><span class="ui-button-text">Submit</span></button></td>'+
									'<td><button type="button" id="cancel"  class=" btn  btn-primary btn-close-modal btn-close-app-modal" style="padding-top:7px;  margin-left:-6px;margin-right:20px float: right">Cancel</button></div></td>'+
									'</tr>'+
									'</table >'+
									'</form>',
					});	
					
									
					$("#cancel").click(function(){
						dialog.modal('hide');
					}); 
					$("#cancelAppointment").click(function() {
						$(this).prop("disabled",false);
						
						var  cancelReason =  $("#select-medium").find(':selected').attr('appointmentcancelreasonID');
						Canceltype=$('input[name="Cancel_appointment"]:checked').val();
						if(undefined !== Canceltype) {
							Canceltype=$('input[name="Cancel_appointment"]:checked').val()
						}
						else{
							Canceltype='single'						
						}
						if( "0" !== cancelReason ) {
							$.ajax({
								url: CONF.url_cancel_appointment,
								type: 'POST',
								data:  {
									'data': JSON.stringify(data),
									'cancelType':  Canceltype,
									'cancelReason' : cancelReason
								},
								success: function(result) {
									if(result.status) {
										$('.cal-item-disabled').remove();
										self.hide();
										CONF.afterCreate();
										dialog.modal('hide');
									} else {
										notification.autoHideError(result.message);
										
									}
								}
							});
						
						}
						else{							
							notification.autoHideError("Please select the reason for cancellation");
						}	
					});

					
					
					if('true' !== is_recurring) {
						//GSSP-250 changges for caption and single appointment pop up
						$("#cancelAppointment span").text("Cancel appointment");
						$('#cancelAppointmentall').hide();
						$('#cancelalllable').hide();
						$('#cancelAppointmentone').hide();
						$('#cancelonelable').hide();
						$('#cancel_reason_form #select-medium').attr('disabled', false);						
						$('#cancel_reason_form #cancelAppointment').attr('disabled', false);
						var recStatus = 'single';
						$.ajax({
							url: CONF.url_get_cancel_reason,						
							type: 'POST',
							data:  {
								'recStatus': recStatus,
								
							},
							success: function(result) {

								$('#cancel_reason_form #select-medium').html("");
								$('#cancel_reason_form #select-medium').append($("<option  selected disabled hidden></option>")
										.attr("appointmentcancelreasonID", 0).text("Select Cancel Reason"));
								
								$.each(result.cancelReasonList,function(index,json){
									
									$('#cancel_reason_form #select-medium').append($("<option></option>")
											.attr("appointmentcancelreasonID", json.appointmentcancelreasonID).text(json.cancelReason));
								});  

								}
						});
					}
					}else{
					appointmentId = appointment_model.get('appointmentId'),

					is_recurring = appointment_model.get('isRecurring'),
					data = self.appointment_model.attributes,
					message = 'Would you like to cancel this appointment?';
					if('true' == is_recurring) {
						message = "Would you like to cancel this occurrence, or all in series?";
						$('.js-cancel-all').show();
					} 
					//For sending email when canceling an appointment
					if(undefined !== originStatus && undefined !== finalStatus) {
						if(self.appointment_model.get('isRecurring') == 'true') {
							finalStatus = 1;
						} else {
							self.appointment_model.unset('endDate');
							finalStatus = 0;
						}
						if(0 == originStatus && 0 == finalStatus) {
							recurringStatus = 1;
						}
						if(1 == originStatus && 1 == finalStatus) {
							recurringStatus = 2;
						} 
						if(1 == originStatus && 0 == finalStatus) {
							recurringStatus = 3;
						}
					}
					self.appointment_model.set('recurringStatus', recurringStatus);
					data = self.appointment_model.attributes;
					//End of For sending email when canceling an appointment
					bootbox.dialog({
						title: 'Cancel Appointment',
						message: message,
						buttons: {
							cancelOne: {
								label: 'Cancel this appointment',
								className: 'btn btn-important',
								callback: function() {
									$.ajax({
										url: CONF.url_cancel_appointment,
										type: 'POST',
										data:  {
											'data': JSON.stringify(data),
											'cancelType': 'single',
												'cancelReason' : 0
										},
										success: function(result) {
											if(result.status) {

												/**
												 * remove the grayout layour as the appointment has been cancelled.
												 */
												$('.cal-item-disabled').remove();
												
												self.hide();
												CONF.afterCreate();
											} else {
												notification.autoHideError(result.message);
											}
										}
									});
								}
							},
							cancelAll: {
								label: 'Cancel all appointments',
								className: 'btn btn-important js-cancel-all',
								callback: function() {
									$.ajax({
										url: CONF.url_cancel_appointment,
										type: 'POST',
										data:  {
											'data': JSON.stringify(data),
											'cancelType': 'more',
											'cancelReason' : 0
										},
										success: function(result) {
											if(result.status) {

												/**
												 * remove the grayout layour as the appointment has been cancelled.
												 */
												$('.cal-item-disabled').remove();
												
												self.hide();
												CONF.afterCreate();
											} else {
												notification.autoHideError(result.message);
											}
										}
									});
								}                  
							},
							cancle: {
								label: 'Cancel',
								className: 'btn btn-primary',
								callback: function() {
									return;
								}
							}
						}

					});



					//For GCSS-435,if this appointment is not recurring then hide the 'Cancel all appointment' button
					if('true' !== is_recurring) {
						$('.js-cancel-all').hide();
					}
				}
			},

			//Changes made for GSSP-241
			conflictAppoointments: function() {
				var self = this,
				CONF = self.conf,
				appointment_model = self.appointment_model;

				self.conflicting_model.showModal($.extend(true, {}, appointment_model.attributes));
 
			},


			/*
			 * Hide the appointment modal
			 */
			 hide: function() {
				 var self = this,
				 EL = self.conf.EL;

				 EL.find('modal-msg').hide();
				 EL.hide();
				 $('.mask').hide();
				 self.dataClear();
				 return self;
			 }
	}
	return AppointmentModal;
});