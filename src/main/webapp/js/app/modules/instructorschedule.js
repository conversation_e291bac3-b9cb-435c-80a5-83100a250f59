define([
	'jquery', 
	'select2', 
	'icheck', 
	'bootbox',
	'mustache',
	'underscore', 
	'backbone',
	'edithour_modal',
	'timeoff_modal',
	'notification'
	
], 
function($, select2, iCheck, bootbox, Mustache, _, Backbone, EdithourModal, TimeoffModal, notification) {
	function Instructorschedule(opts) {
		// De<PERSON>ult set
		this.defaults = {
			url_fetch_collection: 'instructor/loadInstructorList.htm',
			
			url_fetch_instructor_sch_collection: 'instructor/loadInstructorScheduleList.htm',

			url_fetch_model: 'instructor/loadInstructorDetail.htm',
			
			url_fetch_oneTimeModel: 'onetime/loadonetimes.htm',

			url_update_model: 'updateInstructorSchedule.htm',

			url_send_reminder: 'instructor/sendReminder.htm',

			EL: $('.instructor-list-schedule')
		}

		this.is_changed = is_changed;
		this.init(opts);
	}

	Instructorschedule.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			this.conf = $.extend(this.defaults, opts);

			var self = this;

			self.queryParams = {
				queryDate:new Date()
			}

			self.edithour_modal = new EdithourModal({
				EL: {
					$modal: $('#Xmodal_set_availability'),
					$mask: $('.mask')
				}
			});
			
			self.timeoff_modal = new TimeoffModal({
				EL:{
					$modal: $('#Xmodal_set_timeoff'),
					$mask: $('.mask')
				}
			})


			// Update the availability hours
			$(document).on('click', '.btn-update-availability_remv', function(e) {
				self.edithour_modal.update(e);
			});
			
			$(document).on('click', '.js-close-availability-modal_remv', function() {
				self.edithour_modal.hideModal();
			});
			
			//Update the time off 
			$(document).on('click', '.btn-update-timeoff_remv', function(e){
				self.timeoff_modal.update(e);
			});
			
			$(document).on('click', '.js-close-timeoff-modal_remv', function(e){
				self.timeoff_modal.hideModal();
			});

			$(document).on('click','.btn-day-prev',function (e) {
				self.queryPreDate();
			})

			$(document).on('click','.btn-day-next',function (e) {
				self.queryNextDate();
			})


			var CONF = self.conf,
				Instructor_Model, Instructor_View, Instructor_Collection,
				Activity_View, RemarksModal_View, StatusModal_View, ReminderModal_View;

			// Define instructor model
			Instructor_Model = Backbone.Model.extend({
				methodUrl: {
					'read': CONF.url_fetch_instructor_sch_collection,
					'create': CONF.url_update_model,
					'update': CONF.url_fetch_instructor_sch_collection,
					'delete': ''
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				},
				parse: function(data) {
					delete data["message"];
					delete data["status"];
					delete data["object"]
					return data
				},
				initialize: function() {
 
				}
			});



			// Define instrucotr view
			Instructor_View = Backbone.View.extend({
				initialize: function() {
					var _this = this;
					return this;
				},
				events: {
					'click .js-edit-instructor_remv': 'edit',
					'click .close-edit-panel_remv': 'close',
					'click .js-update-instructor_remv': 'update',
					'click .email-header a':'sendReminder',
					'click .js-set-availability_remv': 'XsetAvailability_remv',
					'click .js-set-timeoff_remv': 'XsetTimeoff',
					'ifChanged input[name="active_remv"]': 'changeActive_remv', 
					'click .remarks-modal' : 'remarksModal', 
					'click .status-modal' : 'statusModal', 
					'click .notes-modal' : 'notesModal',
					'click .lesson-completed' : 'completeLessonStatus',
					'click .undo-status' : 'undoLessonStatus',
					'click .musicprodigy-modal' : 'musicprodigyModal'
				},
				tagName: 'tr',
				className: '',
				template: $('#tmpl_instructor_sch').html(),
				render: function() {
				    this.model.set({cid:this.model.cid});
					this.$el.html(Mustache.render(this.template, this.model.toJSON()));

					return this
				},
				// Edit instrucotor
				edit: function(e) {
					var _this = this,
						$this = $(e.currentTarget);

					if($this.hasClass('editing')) return;
					// If has chaned, popup the modal
					if(self.is_changed) {
						bootbox.dialog({
							title: 'Warning!',
							message: 'You have unsaved information. Do you want to continue?',
							buttons: {
								success: {
									label: 'Continue',
									className: 'btn btn-important',
									callback: function() {
										CONF.EL.find('.editing').removeClass('editing');
										CONF.EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
										self.hideWarn();
										_this.getOne();
									}
								},
								cancle: {
									label: 'Cancel',
									className: 'btn btn-primary',
									callback: function() {
										self.showWarn();
										return;
									}
								}
							}
						});
					} else {
						CONF.EL.find('.editing').removeClass('editing');
						CONF.EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
						$this.addClass('editing');
						_this.getOne();
					}
				},
				// Ajax request detail instructor
				getOne: function() {
					var _this = this;
					$.ajax({
						url: CONF.url_fetch_model,
						type: 'GET',
						data: {
							id: _this.model.get('id')
						},
						success: function(result) {
							// If session time out will get the login.html page
							// So, if response text has the string 'DOCTYPE' will reload page to jump to the login page
							if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
							_this.model.set(result);
							_this.editRender();
							_this.$el.find('tr').eq(0).addClass('disabled').nextAll().show();
							
							self.is_changed = false;
							is_changed = self.is_changed;
						},
						error: function(err) {
							notification.autoHideError(err);
						}
					});
				},
				remarksModal: function(e) {
					e.preventDefault();
					var	remarks_modal_view = new RemarksModal_View({
						model: this.model

					});
					
					remarks_modal_view.show(); 


				},
				statusModal: function(e) {
					e.preventDefault();
					var	status_modal_view = new StatusModal_View({
						model: this.model

					});

					status_modal_view.show(); 


				},
				notesModal: function(e) {
					e.preventDefault();
						var	notes_modal_view = new NotesModal_View({
							model: this.model

						});
						this.model.set({'submit' : 'x'});
						notes_modal_view.show(); 


				
				},
				completeLessonStatus: function(e) {
					e.preventDefault();
					var completed_modal_View = new CompletedModal_View({
						model: this.model
					});
					completed_modal_View.show(); 
					var _this = this,
					_model = _this.model,
					thisClicked = $(e.currentTarget), 
					completedEl = thisClicked.closest('span'),
					appointmentStatus = 'Completed';

					this.model.set({'showStatus' : appointmentStatus});
					 this.model.set({'submit' : 'x'});
					this.model.save({}, {
						success: function (model, response) {
							completedEl.find('.lesson-status-msg').text('Completed').show();
							completedEl.find('.btn-default').hide();
							completedEl.find('.undo-wrapper').show();
                        	console.log('saved status and send ajax request')
							
						},
						error: function (model, response) {
							console.log("error");
						}
					});
				},
				undoLessonStatus: function(e) {
					e.preventDefault();
					var _this = this,
					_model = _this.model,
					thisClicked = $(e.currentTarget), 
					completedEl = thisClicked.closest('span')

					this.model.set({'showStatus' : ''});
					this.model.set({'submit' : 'x'});
					this.model.save({}, {
						success: function (model, response) {
							completedEl.find('.undo-wrapper, .lesson-status-msg').hide();
							completedEl.find('.btn-default').show();
                        	console.log('saved status and send ajax request')
							
						},
						error: function (model, response) {
							console.log("error");
						}
					});
				},
				// Open the edit panel
				editRender: function() {
					var	activity_view = new Activity_View({model: this.model}),
						time = '',
						timeoff = '',
						oneTime = '',
						weeklist = this.model.get("editHourShowListX").list,
						/**
						 * for story GCSS-590
						 */
						timeofflist = this.model.get("timeOffs"),
						ontTimeList = this.model.get("onetimes");
						
					
					weeklist.forEach(function(obj) {
						var startTime = obj.startTimeString,
							endTime = obj.endTimeString,
							weekDay = obj.weekDay;
						if(startTime != '') {
							time += '<p class="row"><span class="span2">' + weekDay + ':</span>' + startTime + ' - ' + endTime  + '</p>';
						}
					});
					
					if(timeofflist.length > 0){
						for(i in timeofflist){
							timeoff += '<p class="row">' + timeofflist[i].timeOffStartToEnd  + '</p>';
						}
					}
					
					if(ontTimeList.length > 0){
						for(i in ontTimeList){
							oneTime += '<p class="row">' + ontTimeList[i].onetimeStartToEnd  + '</p>';
						}
					}
					
					this.$el.find('.js-availability-wrapX').empty().html(time);
					this.$el.find('.js-timeoff-wrapX').empty().html(timeoff);
					this.$el.find('.js-oneTimeAvailability-wrapX').empty().html(oneTime);
					this.$el.find('.js-activity-wrapX').empty().append(activity_view.render().el);
					if(this.model.get('activeX')) {
						this.$el.find('input[name="activeX"]').iCheck().iCheck('indeterminate');
					} else {
						this.$el.find('input[name="activeX"]').iCheck().iCheck('determinate');
					}
				},
				// Close the edit panel
				close: function() {
					this.$el.find('.js-edit-instructor-remv').removeClass('editing');
					self.hideWarn();
					this.$el.find('tr').eq(0).removeClass('disabled').nextAll().hide();
					this.$el.find('input[name="Xactive"]').iCheck('destroy');
					

					self.is_changed = false;
					is_changed = self.is_changed;

					return this;
				},
				// Update the instrucotor
				update: function() {
					var _this = this;
					this.$el.find('.edit-instructor_remv').removeClass('editing');
					this.$el.find('.js-update-instructor_remv').attr('disabled', true);//For GCSS-341 to avoid 'double click'
					self.hideWarn();
					this.model.sync('create', this.model, {
						success: function(result) {
							if(result.status) {
								_this.model.set(result.object);
								_this.render();
								_this.close();
							} else {
								notification.autoHideError(result.message);
							}
							_this.$el.find('.js-update-instructor_remv').attr('disabled', false);//For GCSS-341 to avoid 'double click'
						}
					});
				},
				XsetAvailability_remv: function() {
					//self.edithour_modal.showModal($.extend(true, {}, this.model));
				},
				XsetTimeoff: function() {
					//self.timeoff_modal.showModal($.extend(true, {}, this.model));
				},
				changeActive: function(e) {
					self.is_changed = true;
					is_changed = self.is_changed;
					
					if(e.currentTarget.checked) {
						this.model.set('activeX', false);
					} else {
						this.model.set('activeX', true);
					}	
				},
				remove: function() {

				},
				musicprodigyModal:function(e) {
					var _this = this,
						$this = $(e.currentTarget);

					//if($this.hasClass('editing')) return;
					// If has chaned, popup the modal
					//if(self.is_changed) {
						bootbox.dialog({
							title: 'Alert!',
							message: 'This will open new tab. Do you want to continue?',
							buttons: {
								success: {
									label: 'Continue',
									className: 'btn btn-important',
									callback: function() {
										//CONF.EL.find('.editing').removeClass('editing');
										//CONF.EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
										self.hideWarn();
										_this.getLink();
									}
								},
								cancle: {
									label: 'Cancel',
									className: 'btn btn-primary',
									callback: function() {
										//self.showWarn();
										return;
									}
								}
							}
						});
					/*} else {
						CONF.EL.find('.editing').removeClass('editing');
						CONF.EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
						$this.addClass('editing');
						_this.getOne();
					}*/
				},
				getLink: function() {
					var _this = this;
					//var studentId= _this.model.get('cid');
					//var teacherId = _this.model.get('id'),
					var studentId = _this.model.attributes.customerExternalId,
						teacherId = _this.model.attributes.instructorId;

					 function asyncPostForPortalLinks() {
					 
						console.log('_this.model.attributes.customerExternalId'+_this.model.attributes.customerExternalId);
						console.log('studentId'+studentId);
						var response  =fetch('https://t0e0p8b8rk.execute-api.us-west-2.amazonaws.com/default/v1/lessons/details', {
								method: 'POST',
								headers: {
									'Content-Type': 'application/json;charset=UTF-8',
									'X-API-Key': 'q6gbbkZXAy19dPcDugYO21U3KyPHQw6T7bdoLL4R'
								},
								body: JSON.stringify({
									"persona" : "instructor",

									"studentId" : studentId,
					
									"instructorId" : teacherId 
								})
							})
							.then(function (response){
								return response.json()
							})
							.then(function (data){
								if (data.mpUrl) {
									window.open(data.mpUrl, "_blank");
								}
								else {
									bootbox.dialog({
										title: 'Alert!',
										message: 'Music Prodigy data not available for this lesson, please try again.',
										buttons: {
											success: {
												label: 'Try Again',
												className: 'btn btn-important',
												callback: function() {
													self.hideWarn();
													_this.getLink();
													
												}
											},
											cancle: {
												label: 'Cancel',
												className: 'btn btn-primary',
												callback: function() {
													//self.showWarn();
													return;
												}
											}
										}
									});
								}
							})
							.catch(function (error){ 
							console.error('Error:', error);
						});
											
					}			
					asyncPostForPortalLinks()
					
				},
                sendReminder:function(e){
					e.preventDefault();
					var thisClicked = $(e.currentTarget);
					var _this = this;
					var _model = _this.model;
                    var cid = thisClicked.attr("cid");
					var model = self.instructor_collection.get(cid);
                    console.log(model);
					model.unset("cid");
					thisClicked.closest('.reminder-link-container').addClass('loading-spinner');
                    $.ajax({
                        url: CONF.url_send_reminder,
                        type: 'POST',
                        data: {
                            params:JSON.stringify(model.attributes)
                        },
                        success: function(result) {
							//make sure user is not logged out
							if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
							if (result == false) {
								var	reminder_modal_view = new ReminderModal_View({
									model: _model
								}),
								reminderStatusMsg = 'Failed to Send Reminder, please try again.';
							} 
                            else if (result == true){
								var	reminder_modal_view = new ReminderModal_View({
									model: _model
		
								}), 
								reminderStatusMsg = 'Reminder Sent Successfully!';
                            }
							reminder_modal_view.show(reminderStatusMsg); 
							thisClicked.closest('.reminder-link-container').removeClass('loading-spinner');
                        }
                    });
                }
			});
			
			// Define the instructor collection
			Instructor_Collection = Backbone.Collection.extend({
				model: Instructor_Model,
				methodUrl: {
					'read': CONF.url_fetch_collection,
					'create': '',
					'update': '',
					'delete': ''
				},
				initialize: function() {
					
				}, 
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				},
			});

			//Define remarks modal view in the instructor model

		    RemarksModal_View = Backbone.View.extend({
		    	collection: Instructor_Collection,
	    		model: Instructor_Model,	        
	    		id: 'modal_edit_instructor',
		        className: 'modal fade hide instructor-schedule-modal',
		        dataAttribute: 'data-backdrop="static',
		        template: $('#tmpl_instructor_comment_modal').html(),
		        events: {
		          'hidden': 'teardown', 
		          'click .submit-remarks' : 'saveRemarks', 
		          'click .close': 'teardown' 
		        },
		        
		        initialize: function(appointmentId) {
					var _this = this, 
						appointmentId = appointmentId;
					  _this.render();
					return this;
		        },

		        show: function() {
					var _this = this,
					_model = _this.model;
					elModal = this.$el.modal(),
					pastRemarks = _model.get('comments');	 
		       	  this.$el.attr('data-backdrop','static');
				  this.$el.modal('show');
				  this.model.set({'submit' : 'x'});
					elModal.find('#edit_instructor_comments').val(pastRemarks);
					elModal.find('#edit_instructor_comments').text(pastRemarks)

		        },

		        teardown: function() {
		          this.$el.data('modal', null);
		          this.remove();
		        },

		        render: function() {
		          this.$el.html(Mustache.render(this.template));
		          return this;
		        },
		        
		        renderView: function(template) {
		          this.$el.html(template());
		          this.$el.modal({show:false}); // dont show modal on instantiation
		        }, 

		        saveRemarks: function(e) {
					var _this = this,
						_model = _this.model;
						elModal = this.$el.modal(),
						appointmentId = _model.get('appointmentId'),
						showStatus = _model.get('showStatus'), 
						studentNote = _model.get('studentNote'),
						appointmentRemarks = elModal.find('#edit_instructor_comments').val();

						this.model.set({'comments' : appointmentRemarks})

						this.model.save({}, {
							success: function (model, response) {
								console.log("comment submitted successfullly");
								$('.modal-footer').hide();
								elModal.find('.modal-body').html('<p class="submit-success-msg">Comment Submitted Successfully!</p>');
								remarksTableDataEl = $('.remarks-header[data-id-remarks="'+appointmentId+'"]');
								remarksTableDataEl.find('.add-remarks').hide();
								remarksTableDataEl.find('.edit-remarks').show();
							},
							error: function (model, response) {
								console.log("error");
							}
						});
		        }
		     });
    	    //notes to student modal
    	    NotesModal_View = Backbone.View.extend({
	    
		        id: 'modal_notes_editor',
		        className: 'modal fade hide instructor-schedule-modal',
		        template: $('#tmpl_instructor_notes_modal').html(),
		        events: {
		          'hidden': 'teardown', 
		          'click .submit-notes' : 'saveNotes',
		           'click .close': 'close' 
		        },
		        
				initialize: function(appointmentId) {
					var _this = this, 
						appointmentId = appointmentId;
					  _this.render();
					return this;
		        },
				
				show: function() {
					var _this = this,
					_model = _this.model;
					elModal = this.$el.modal(),
					pastNotes = _model.get('studentNote');
					//pastLessonStatus = _model.get('lessonStatus');
					//pastNextLessonStatus = _model.get('nextLessonStatus');
					pastAssignment = _model.get('assignment');
					pastPracticeNotes = _model.get('practiceNotes');
					pastRemarks = _model.get('remarks');
					//pastAnyRemarks = _model.get('anyRemarks');
					//pastRate = _model.get('rate');
					//console.log("getstars selected  ---  "+pastRate);
		       	  this.$el.attr('data-backdrop','static');
				  this.$el.modal('show');
		 
					elModal.find('#edit_student_notes').val(pastNotes);
					elModal.find('#edit_student_notes').text(pastNotes);
					//elModal.find('#lesson_status').val(pastLessonStatus);
					//elModal.find('#lesson_status').text(pastLessonStatus);
					//elModal.find('#next_lesson_status').val(pastNextLessonStatus);
					//elModal.find('#next_lesson_status').text(pastNextLessonStatus);
					elModal.find('#assignment').val(pastAssignment);
					elModal.find('#assignment').text(pastAssignment);
					elModal.find('#practice_notes').val(pastPracticeNotes);
					elModal.find('#practice_notes').text(pastPracticeNotes);
					elModal.find('#remarks').val(pastRemarks);
					elModal.find('#remarks').text(pastRemarks);
					//elModal.find("input[name=rate][value=" + pastRate + "]").attr('checked', 'checked');
					//elModal.find('#rates').val(pastRate);
					//_model.set('rate', pastRate);
					//elModal.find('#rates').text(pastRate);
					//rate = elModal.find('input[name="rate"]:checked').val();
					//.iCheck().iCheck('determinate');
					//this.$el.find('input[name="rate"]').iCheck().iCheck(pastRate);
					
					//console.log("paststars selected"+pastRate);
					

		        },

		        teardown: function() {
		          this.$el.data('modal', null);
		          this.$el.modal.remove();
		        },

		        render: function() {
		          this.$el.html(Mustache.render(this.template));
		          return this;
		        },
		        
		        renderView: function(template) {
		          this.$el.html(template());
		          this.$el.modal({show:false}); // dont show modal on instantiation
				}, 
				
				saveNotes: function(e) {
					var _this = this,
						_model = _this.model;
						elModal = this.$el.modal(),
						appointmentId = _model.get('appointmentId'),
						showStatus = _model.get('showStatus'), 
						appointmentRemarks = _model.get('comments'),
						appointmentNotes = elModal.find('#edit_student_notes').val();
						//lessonStatus = elModal.find('#lesson_status').val();
						//nextLessonStatus = elModal.find('#next_lesson_status').val();
						assignment = elModal.find('#assignment').val();
						practiceNotes = elModal.find('#practice_notes').val();
						remarks = elModal.find('#remarks').val();
						//rate = elModal.find('input[name="rate"]:checked').val();
						
						this.model.set({'submit' : 'submit_val'});

						this.model.set({'studentNote' : appointmentNotes});
						//this.model.set({'lessonStatus' : lessonStatus});
						//this.model.set({'nextLessonStatus' : nextLessonStatus});
						this.model.set({'assignment' : assignment});
						this.model.set({'practiceNotes' : practiceNotes});
						this.model.set({'remarks' : remarks});
						//this.model.set({'rate' : rate});
						//console.log("value to be set in db"+rate);
						

						this.model.save({}, {
							success: function (model, response) {
								console.log("notes submitted successfullly");
								elModal.find('.modal-body').html('<p class="submit-success-msg">Note Submitted Successfully!</p>');
								$('.modal-footer').hide();
								notesTableDataEl = $('.notes-header[data-id-student-notes="'+appointmentId+'"]');
								console.log("notes data :"+notesTableDataEl);
								notesTableDataEl.find('.add-notes').hide();
								notesTableDataEl.find('.edit-notes').show();
							},
							error: function (model, response) {
								console.log("error");
							}
						});
		        }

		     });


    	    StatusModal_View = Backbone.View.extend({
	    
		        id: 'modal_status_instructor',
		        className: 'modal fade hide instructor-schedule-modal',
		        template: $('#tmpl_instructor_status_modal').html(),
		        events: {
		          'hidden': 'teardown', 
		          'click .submit-status' : 'saveStatus',
		           'click .close': 'close' 
		        },
		        
				initialize: function(appointmentId) {
					var _this = this, 
						appointmentId = appointmentId;
					  _this.render();
					return this;
		        },
				
				show: function() {
					var _this = this,
					_model = _this.model;
					elModal = this.$el.modal(),
					pastStatus = _model.get('showStatus');	 
		       	  this.$el.attr('data-backdrop','static');
				  this.$el.modal('show');
					if (pastStatus != null && pastStatus != '') {
						elModal.find('#js-lesson-status').val(pastStatus);
					}
		        },

		        teardown: function() {
		          this.$el.data('modal', null);
		          this.$el.modal.remove();
		        },

		        render: function() {
		          this.$el.html(Mustache.render(this.template));
		          return this;
		        },
		        
		        renderView: function(template) {
		          this.$el.html(template());
		          this.$el.modal({show:false}); // dont show modal on instantiation
				}, 
				
				saveStatus: function(e) {
					var _this = this,
						_model = _this.model;
						elModal = this.$el.modal(),
						appointmentId = _model.get('appointmentId'),
						appointmentStatus = $("#js-lesson-status option:selected").text(),
						statusTableDataEl = $('.completed-header[data-id-status="'+appointmentId+'"]'),

						this.model.set({'showStatus' : appointmentStatus});

						this.model.save({}, {
							success: function (model, response) {
								console.log("Status submitted successfullly");
								elModal.find('.modal-body').html('<p class="submit-success-msg">Status Submitted Successfully!</p>');
								$('.modal-footer').hide();
								statusTableDataEl.find('.lesson-status-msg').text(appointmentStatus).show();
								statusTableDataEl.find('.btn-default').hide();
								statusTableDataEl.find('.undo-wrapper').show();
							},
							error: function (model, response) {
								console.log("error");
							}
						});
		        }
		        
			 });
			 

    	    CompletedModal_View = Backbone.View.extend({
	    
		        id: 'modal_status_instructor',
		        className: 'modal fade hide instructor-schedule-modal',
		        template: $('#tmpl_instructor_completed_modal').html(),
		        events: {
		          'hidden': 'teardown', 
		          'click .submit-completed' : 'saveStatus',
		           'click .close': 'close' 
		        },
		        
				initialize: function(appointmentId) {
					var _this = this, 
						appointmentId = appointmentId;
					  _this.render();
					return this;
		        },
				
				show: function() {
 
					var _this = this,
					_model = _this.model;
					elModal = this.$el.modal(),
					//pastStatus = _model.get('showStatus');	 
					//pastLessonStatus = _model.get('lessonStatus');
					//pastNextLessonStatus = _model.get('nextLessonStatus');
					pastAssignment = _model.get('assignment');
					pastPracticeNotes= _model.get('practiceNotes');
					pastRemarks = _model.get('remarks');
					//pastRate = _model.get('rate');
		       	  	this.$el.attr('data-backdrop','static');
		       	  	this.$el.modal('show');
		 
					//elModal.find('#edit_student_notes').val(pastNotes);
					//elModal.find('#edit_student_notes').text(pastNotes);
					//elModal.find('#lesson_status').val(pastLessonStatus);
					//elModal.find('#lesson_status').text(pastLessonStatus);
					//elModal.find('#next_lesson_status').val(pastNextLessonStatus);
					//elModal.find('#next_lesson_status').text(pastNextLessonStatus);
					elModal.find('#assignment').val(pastAssignment);
					elModal.find('#assignment').text(pastAssignment);
					elModal.find('#practice_notes').val(pastPracticeNotes);
					elModal.find('#practice_notes').text(pastPracticeNotes);					
					elModal.find('#remarks').val(pastRemarks);
					elModal.find('#remarks').text(pastRemarks);
					//elModal.find("input[name=rate][value=" + pastRate + "]").attr('checked', 'checked');
					//elModal.find('#rates').val(pastRate);
					//_model.set('rate', pastRate);
					//elModal.find('#rates').text(pastRate);
					//rate = elModal.find('input[name="rate"]:checked').val();
					//.iCheck().iCheck('determinate');
					//this.$el.find('input[name="rate"]').iCheck().iCheck(pastRate);
					
					//console.log("paststars selected"+pastRate);
					

		        },

		        teardown: function() {
		          this.$el.data('modal', null);
		          this.$el.modal.remove();
		        },

		        render: function() {
		          this.$el.html(Mustache.render(this.template));
		          return this;
		        },
		        
		        renderView: function(template) {
		          this.$el.html(template());
		          this.$el.modal({show:false}); // dont show modal on instantiation
				}, 
				
				
				saveStatus: function(e) {
					var _this = this,
						_model = _this.model;
						elModal = this.$el.modal(),
						appointmentId = _model.get('appointmentId'),
						showStatus = _model.get('showStatus'), 
						appointmentRemarks = _model.get('comments'),
						appointmentNotes = elModal.find('#edit_student_notes').val();
						//lessonStatus = elModal.find('#lesson_status').val();
						//nextLessonStatus = elModal.find('#next_lesson_status').val();
						assignment = elModal.find('#assignment').val();
						practiceNotes = elModal.find('#practice_notes').val();
						remarks = elModal.find('#remarks').val();
						//rate = elModal.find('input[name="rate"]:checked').val();
		 
						this.model.set({'submit' : 'submit_val'});
						this.model.set({'studentNote' : appointmentNotes});
						//this.model.set({'lessonStatus' : lessonStatus});
						//this.model.set({'nextLessonStatus' : nextLessonStatus});
						this.model.set({'assignment' : assignment});
						this.model.set({'practiceNotes' : practiceNotes});
						this.model.set({'remarks' : remarks});
						//this.model.set({'rate' : rate});
						//console.log("value to be set in db"+rate);
						var _this = this,
						_model = _this.model,
						thisClicked = $(e.currentTarget), 
						completedEl = thisClicked.closest('span'),
						appointmentStatus = 'Completed';

						this.model.set({'showStatus' : appointmentStatus});

						this.model.save({}, {
							success: function (model, response) {
								console.log("Completed submitted successfullly");
								completedEl.find('.lesson-status-msg').text('Completed').show();
								elModal.find('.modal-body').html('<p class="submit-success-msg">Completed Submitted Successfully!</p>');
								$('.modal-footer').hide();
								completedEl = $('.notes-header[data-id-status="'+appointmentId+'"]');
								//console.log("notes data :"+completedEl);
								completedEl.find('.add-notes').hide();
								completedEl.find('.edit-notes').show();
							},
							error: function (model, response) {
								console.log("error");
							}
						});
		        }
		        
			 });
			     	    
			 ReminderModal_View = Backbone.View.extend({
	    
		        id: 'modal_reminder_instructor',
		        className: 'modal fade hide instructor-schedule-modal',
		        template: $('#tmpl_instructor_reminder_modal').html(),
		        events: {
		          'hidden': 'teardown', 
		          'click .submit-status' : 'saveReminderCount',
		           'click .close': 'close' 
		        },
		        
				initialize: function(appointmentId) {
					var _this = this, 
						appointmentId = appointmentId;
					  _this.render();
					return this;
		        },
				
				show: function(reminderStatusMsg) {
					var _this = this,
					_model = _this.model;
					elModal = this.$el.modal(),
					//pastStatus = _model.get('showStatus');	 
		       	  this.$el.attr('data-backdrop','static');
				  this.$el.modal('show');
				  this.$el.find('.email-success-msg').text(reminderStatusMsg);
		        },

		        teardown: function() {
		          this.$el.data('modal', null);
		          this.$el.modal.remove();
		        },

		        render: function() {
		          this.$el.html(Mustache.render(this.template));
		          return this;
		        },
		        
		        renderView: function(template) {
		          this.$el.html(template());
		          this.$el.modal({show:false}); // dont show modal on instantiation
				}, 
				
				saveReminderCount: function(e) {
					var _this = this,
						_model = _this.model;
						elModal = this.$el.modal(),
						appointmentId = _model.get('appointmentId'),
						reminderTableDataEl = $('.email-header[data-id-email-reminder="'+appointmentId+'"]'),

						this.model.set({'sentReminderCount' : appointmentStatus});

						this.model.save({}, {
							success: function (model, response) {
								console.log("Status submitted successfullly");
								//elModal.find('.modal-body').html('<p class="submit-success-msg">Reminder Sent Successfully!</p>');
								$('.modal-footer').hide();
							},
							error: function (model, response) {
								console.log("error");
							}
						});
		        }
		        
		     });


			
			// Define activity view in the instructor model
			Activity_View = Backbone.View.extend({
				events: {
					'click .js-btn-add_remv': 'add',
					'click .js-remove-activity_remv': 'remove'
				},
				className: '',
				template: $('#tmpl_activity_remv').html(),
				subTemplate: $('#tmpl_activity_item_remv').html(),
				render: function() {
					this.$el.html(Mustache.render(this.template, this.model.toJSON()));
					this.$el.find('input[name="activities_remv"]').select2({
						placeholder: 'Select ActivityX',
						data: this.model.get('notSelectedActivities'),
						id: function(obj) {
							return obj.activityId;
						},
						formatResult: function(obj) {
							return obj.activityName;
						}, 
						formatSelection: function(obj) {
							return obj.activityName;
						}
					});

					return this;	
				},
				// Add an activity form the selectbox
				add: function() {
					var value = this.$el.find('input[name="activities_remv"]').val(),
						select = this.$el.find('input[name="activities_remv"]'),
						target = this.$el.find('.js-activity-list_remv'),
						tmpl = this.subTemplate,
						model = this.model;
					if(value === '') return;

					_.each(model.get('notSelectedActivities_remv'), function(obj, index) {
						if(obj.activityId == value) {
							model.get('activitys_remv').push(obj);
							target.append(Mustache.render(tmpl, obj));
						}
					});

					var unselected_activity = _.filter(model.get('notSelectedActivities'), function(obj, i) {
						return obj.activityId !== value*1;
					});
					model.set('notSelectedActivities', unselected_activity);

					select.select2('val', '').select2({
						placeholder: 'Select ActivityX',
						data: this.model.get('notSelectedActivities'),
						id: function(obj) {
							return obj.activityId;
						},
						formatResult: function(obj) {
							return obj.activityName;
						}, 
						formatSelection: function(obj) {
							return obj.activityName;
						}
					});

					self.is_changed = true;
					is_changed = self.is_changed;
				},
				// Remove an acvitity
				// TODO
				// Remove the ralated servive
				remove: function(e) {
					var model = this.model,
						select = this.$el.find('input[name="activitiesX"]'),
						id = $(e.currentTarget).data('id');

					var unselected_activity = _.filter(model.get('activitysX'), function(obj, index) {
						return obj.activityId === id*1;
					});
					model.get('notSelectedActivities').push(unselected_activity[0]);

					var selected_activity = _.filter(model.get('activitysX'), function(obj, index) {
						return obj.activityId !== id*1;
					});
					model.set('activitysX', selected_activity);

					select.select2('val', '').select2({
						placeholder: 'Select ActivityX',
						data: model.get('notSelectedActivitiesX'),
						id: function(obj) {
							return obj.activityId;
						},
						formatResult: function(obj) {
							return obj.activityName;
						}, 
						formatSelection: function(obj) {
							return obj.activityName;
						}
					});

					$(e.currentTarget).parent().fadeOut(function() {
						$(this).remove();
					});

					self.is_changed = true;
					is_changed = self.is_changed;
				}
			});


			var	instructor_collection = new Instructor_Collection;

			self.Model = Instructor_Model;
			self.View = Instructor_View;
			self.Collection = Instructor_Collection;

			self.instructor_collection = instructor_collection;

			_.bindAll(self, 'addAll', 'addOne');

			instructor_collection.on('set', self.addAll);
			instructor_collection.on('add', self.addOne);

			return self;
		},
		/*
		* Render the instructor list
		*/
		renderList: function() {
			var self = this;

			// Show loadings
			self.showLoadTip();
			
			self.is_changed = false;
			is_changed = self.is_changed;
			
			self.getAll();
			return self;
		},
		//--------------------GSSP-363  starts 
		renderInstructorScheduleList: function() {
			var self = this;
			
			// Show loadings
			self.showLoadTip();
			
			self.is_changed = false;
			is_changed = self.is_changed;

			self.getInstructorScheduleAll();

			//lets refresh the instructor collection and re-render the view every 60 seconds
			//this is to show launch button becoming active and email reminder being disabled 
			//without page refresh
			if (window.instructorScheduleInterval == null && $('.tab-Instructor_schedule').hasClass('active') == true) {

				window.instructorScheduleInterval = setInterval(function() {
					//clear refresh instructor interval if not on instructor tab
					if ($('.tab-Instructor_schedule').hasClass('active') != true) { 
						clearInterval(window.instructorScheduleInterval);
						window.instructorScheduleInterval = null;
					}
					else  {
						self.getInstructorScheduleAll();
						//console.log('test')
					}
									
			 		}, 300000);
			}
			return self;
		},
		getInstructorScheduleAll: function() {
			var self = this,
				CONF = self.conf, 
				dateObject = self.queryParams.queryDate,
				queryDate = JSON.stringify({queryDate:self.dateToString(dateObject)});
			CONF.EL.empty();
			self.instructor_collection.reset();
			if ($('.tab-Instructor_schedule').hasClass('active') != true) {
				clearInterval(window.instructorScheduleInterval);
			}
  			console.log(self.queryParams.queryDate);
			$.ajax({
				url: CONF.url_fetch_instructor_sch_collection,
				type: 'GET',
		        data:{
		          params: queryDate
		        },
				success: function(result) {
					self.instructorSchedulerHeaderAppend(result, dateObject);
					self.instructor_collection.reset();
					self.instructor_collection.set(result.instructorLessonLinkDTO);	
				}
			});
		},
		instructorSchedulerHeaderAppend:function(data, dateObject){
			//make sure user is not logged out
			if(typeof data == 'string' && data.indexOf('DOCTYPE') !== -1) location.reload();
			if(data){
				var instructorStartTimeDurationStr = '';
				if (data.instructorLessonLinkDTO.length <= 0) {
					instructorStartTimeDurationStr = "No Appointments Today";
				}
				else {
					instructorStartTimeDurationStr = data.dayStartEndTime + ' ' + data.timeZone;
				}
				 var newDate = new Date(dateObject); 
					var convertedDate = newDate.toLocaleString('default', {month: 'long'}) + " " + newDate.getDate() + ", " + newDate.getFullYear();
					var instructorViewEl = $('#instructor_sch_table');

				instructorViewEl.find('.instructor-hours').text(instructorStartTimeDurationStr);
				instructorViewEl.find('.schedule-header .schedule-date').text(convertedDate);
			}
			else {
				console.log('no data available')
			}
		},
		//-------------------------GSSP-363  Ends
		/*
		* Ajax request to get all instructor
		*/
		getAll: function() {
			var self = this,
				CONF = self.conf
			CONF.EL.empty();
			self.instructor_collection.reset();
			$.ajax({
				url: CONF.url_fetch_collection,
				type: 'GET',
				success: function(result) {
					self.instructor_collection.reset();
					self.instructor_collection.set(result.listInstructorDtos);	
				}
			});
		},
		/*
		* Add all instructor in this collection
		*/
		addAll: function() {
			var self = this;
			self.instructor_collection.each(self.addOne);
		},
		/*
		* Add one instructor
		*/
		addOne: function(model) {
			var self = this,
				EL = self.conf.EL;
			var view = new self.View({model: model});

			EL.append(view.render().el);
		},
		/*
		* Show the warn information
		*/
		showWarn: function() {
				var $target = $('.instructor-list_remv tr.disabled').nextAll().find('.js-update-instructor_remv'),
				right = 220,
				top = $target.offset().top - $target.height() - $('.warn-tip').height();
			$('.warn-tip').css({
				right: right,
				top: top
			}).show();
		},
		/*
		* Hide the warn information
		*/
		hideWarn: function() {
			$('.warn-tip').hide();
		},
		/*
		* Show a loading tip
		*/
		showLoadTip: function() {
			var self = this,
				EL = self.conf.EL;
			EL.append('<div class="loader"><p></p></div>');
		},
		/**
		 * query pre date's instructor schedule list
		 */
		queryPreDate:function () {
			var time=this.queryParams.queryDate.getTime()-24*60*60*1000;
			this.queryParams.queryDate = new Date(time);
			this.renderInstructorScheduleList();
		},
		queryNextDate:function () {
			var time=this.queryParams.queryDate.getTime()+24*60*60*1000;
			this.queryParams.queryDate = new Date(time);
			this.renderInstructorScheduleList();
		},
		dateToString: function (date) {
			var year = date.getFullYear();
			var month = (date.getMonth() + 1).toString();
			var day = (date.getDate()).toString();
			if (month.length == 1) {
				month = "0" + month;
			}
			if (day.length == 1) {
				day = "0" + day;
			}
			var dateTime = year + "-" + month + "-" + day;
			return dateTime;
		},

	}
	return Instructorschedule;
});