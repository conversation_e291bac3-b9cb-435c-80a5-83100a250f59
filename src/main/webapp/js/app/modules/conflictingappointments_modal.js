define([
	'jquery',
	'moment',
	'select2', 
	'icheck', 
	'mustache', 
	'underscore', 
	'backbone',
	'pikaday',
	'notification'
],
function($, moment, select2, iCheck, Mustache, _, Backbone, Pikaday, notification) {
	function ConflictingAppointments(opts) {
		// Default set
		this.defaults = {

				url_get_conflicting_rooms : 'conflicts/getConflictingAppointments.htm',							
			
			EL: {
				$modal: $('#modal_conflicting_appointment'),
				$mask: $('.mask')
			}
		}

		// Default versionString
		this.currentVersion = undefined;
		
		this.init(opts);
	}
	ConflictingAppointments.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			this.conf = $.extend({}, this.defaults, opts);			
					
			var self = this,
				CONF = self.conf, 
				EL = self.conf.EL,
				$modal = EL.$modal,
				ConflictingAppointments_Model, Modal,
				formEL, modal;

			// Define time off model
			ConflictingAppointments_Model = Backbone.Model.extend({

			});

			self.conflicting_model = new ConflictingAppointments_Model;
			// Define modal model
			Modal = Backbone.Model.extend({
				defaults: {

				},
				
			});		
			
			modal = new Modal;
			self.modal = modal;
			formEL = modal.formEL;
			
		},

	
		
		/*
		* Show the time info on the right of this Modal Changes
		*/
	
		
		
		renderConflictingList: function(result){
				var self = this,
				CONF = self.conf,
				EL = self.conf.EL,
				$modal = EL.$modal,
				conflictingAppointmentObject = result;
			
				
							conflicts = '<table height="40" width="100%" style="color: #ffffff;background-color: #CC0000;border: 3px solid #bbbcbd;"><tr>';
							conflicts +='<th><center>Number of Conflicting Appointments : ' + conflictingAppointmentObject.conflictingappointmentcount + '</center></th>';							
							conflicts += '</tr></table><br><br>'
							conflicts += '<table class="table table-fixed"><tr >';
							if (conflictingAppointmentObject.roomConflict)
							{
								conflicts += '<th class="cell-150"><b>Room Name</b></th>';
							}
							else
							{
								conflicts += '<th class="cell-150"><b>Instructor Name</b></th>'
							}	
							conflicts += '<th><b>Date</b></th>';
							conflicts += '<th class="cell-150"><b>Time Frame</b></th>';
							conflicts += '<th><b>Duration</b></th>';
							conflicts += '<th><b>Activity Type</b></th>';
							conflicts += '<th><b>Customer Name</b></th></tr>';
							
							
						
					if(conflictingAppointmentObject.conflictingappointmentlist.length > 0){
							for(i in conflictingAppointmentObject.conflictingappointmentlist){
								
								conflicts += '<tr>';
								conflicts += '<td width="250">' + conflictingAppointmentObject.conflictingappointmentlist[i].roomName + '</td>';
								conflicts += '<td width="170">' + conflictingAppointmentObject.conflictingappointmentlist[i].date1  +  '</td>';
								conflicts += '<td width="200">' + conflictingAppointmentObject.conflictingappointmentlist[i].timeFrame + '</td>';
								conflicts += '<td width="150">' + conflictingAppointmentObject.conflictingappointmentlist[i].duration  + '</td>';
								conflicts += '<td width="150">' + conflictingAppointmentObject.conflictingappointmentlist[i].activityName  + '</td>';
								conflicts += '<td width="150">' + conflictingAppointmentObject.conflictingappointmentlist[i].customers[0].person.firstName +' '+ conflictingAppointmentObject.conflictingappointmentlist[i].customers[0].person.lastName + '</td>';
								conflicts += '</tr>';
								
							}
							
							conflicts += '</table>'
					}
							
						$modal.find('.conflicting-List').empty().html(conflicts);
								
				
		},
		
		/*
		 * Show the pop and set the timeoff
		 */
		showModal:function(obj){
			var self = this,
				CONF = self.conf,
				EL = CONF.EL,
				$mask = EL.$mask,
				$modal = EL.$modal;
			
			// Set the position of Modal
			var win_height = $(window).height(),
				win_width = $(window).width(),
				m_width =750,
				m_height = 700,
				top = (win_height - m_height) / 2 + $(window).scrollTop(),
				left = (win_width - m_width) / 2;
	
			if(top < 0) top = 10;
			if(left < 0) left =10;
			$modal.css({
				left: left,
				top: top,				
			});
		
			
			
			this.instructorModel = obj;
			
			if(obj.instructorId == '' ||  obj.roomId  == '' || obj.startDate == '' || obj.startTime  == ''|| obj.duration == '')
			{
				noError = "Please provide the instructor, room, start date and duration fields for generating the conflicting appointments"	;
				
				$modal.find('.conflicting-List').empty();
				$modal.find('.modal-msg').html(noError).show();
				
				$modal.show();
				$mask.show();
				
				return;
			}	
					
		$.ajax({
				url: CONF.url_get_conflicting_rooms,
				type: 'GET',
				data: {
					'instructorId':obj.instructorId,
					'roomId':obj.roomId,						
					'startDate':obj.startDate,
					'endDate':obj.endDate,
					'startTime':obj.startTime,
					'duration':obj.duration,
					'isRecurring': obj.isRecurring},
				success: function(result) {
					
					if(result.status)
					{	
						$modal.find('.modal-msg').hide();
						self.renderConflictingList(result);
					}
					else
					{
							noError = "No Conflicting appointments for this room/instructor"	;
							$modal.find('.conflicting-List').empty();
							$modal.find('.modal-msg').html(noError).show();
							
							

					}					
					$modal.show();
					$mask.show();
					
				}
			});
				
		},
		
		/*
		 * Hide the Modal and clean the data in the Modal
		 */
		hideModal: function(){
			var self = this,
			EL = self.conf.EL,
			formEL = self.modal.formEL;
			
			//GSSP-241
			EL.$modal.hide();
			EL.$mask.hide();
			/**
			 * Clean date and time selected
			 */
		
			
			
		}
	}	
	return ConflictingAppointments;
});