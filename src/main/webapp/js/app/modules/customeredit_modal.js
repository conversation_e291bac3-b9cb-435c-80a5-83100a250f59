define([
	'jquery',
	'moment',
	'select2', 
	'icheck', 
	'mustache', 
	'underscore', 
	'backbone',
	'pikaday',
	'notification'
],
function($, moment, select2, iCheck, Mustache, _, Backbone, Pikaday, notification ) {
	function Customeredit(opts) {
		// Default set
		this.defaults = {
				
				url_update: 'customer/updateCustomerEditDetailsInfo.htm',
				url_load: 'customer/getCustomerEditDetailsInfo.htm',
				url_remove: 'profileTimeoff/deleteProfileTimeoff.htm',
				url_fetch_profile_start_time : 'profileTimeoff/getStartTimeByProfile.htm',
				url_cancel_load : 'profileTimeoff/loadCancelProfileTimeoffs.htm',
				
				 
			EL: {
				$modal: $('#modal_edit_cust'),
				$mask: $('.mask')
			}
		}

		// Default versionString
		this.currentVersion = undefined;
		this.init(opts);
	}
	Customeredit.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
			
		init: function(opts) {
			this.conf = $.extend({}, this.defaults, opts);			
			
			var self = this,
				CONF = self.conf, 
				EL = self.conf.EL,
				$modal = EL.$modal,
				Customeredit_Modal, Modal,
				formEL, modal,dateFrom;

			// Define time off model
			Customeredit_Modal = Backbone.Model.extend({

			});

			self.Customeredit_Modal = new Customeredit_Modal;
			// Define modal model
			Modal = Backbone.Model.extend({
				defaults: {

				},
 
				formEL: {
					'dateFrom': {
						el: $modal.find('input[name="timeOffStartDate"]')
					},
					'customerFullName': {
						el: $modal.find('input[name="customerFullName"]'),
						set: function(val) {
							this.el.val(val).text(val);
							Customeredit_Modal.set('customerFullName', val);
							return this;
						},
						clear: function() {
							this.el.val('').text('');
							appointment_model.set('customerFullName');
							return this;
						},
						needValid: false 
					},
					'gcId': {
						el: $modal.find('input[name="gcId"]'),
						set: function(val) {
							this.el.val(val).text(val);
							Customeredit_Modal.set('gcId', val);
							return this;
						},
						clear: function() {
							this.el.val('').text('');
							Customeredit_Modal.set('gcId');
							return this;
						},
						needValid: false 
					},
					'secondaryEmail': {
						el: $modal.find('input[name="secondaryEmail"]'),
						set: function(val) {
							this.el.val(val).text(val);
							Customeredit_Modal.set('secondaryEmail', val);
							return this;
						},
						
						clear: function() {
							this.el.val('').text('');
							Customeredit_Modal.set('secondaryEmail');
							return this;
						},
						needValid: false 
					},
					'searchCustCriteria': {
						el: $modal.find('input[name="searchCustCriteria"]'),
						set: function(val) {
							this.el.val(val).text(val);
							Customeredit_Modal.set('searchCustCriteria', val);
							return this;
						},
						
						clear: function() {
							this.el.val('').text('');
							Customeredit_Modal.set('searchCustCriteria');
							return this;
						},
						needValid: false 
					},
					'phoneNumber': {
						el: $modal.find('input[name="phoneNumber"]'),
						set: function(val) {
							this.el.val(val).text(val);
							Customeredit_Modal.set('phoneNumber', val);
							return this;
						},
						
						clear: function() {
							this.el.val('').text('');
							Customeredit_Modal.set('phoneNumber');
							return this;
						},
						needValid: false 
					}
				}
			});
		
			modal = new Modal;
			self.modal = modal;
			formEL = modal.formEL;
		    
			dateFrom = new Pikaday({
				format: 'MM/DD/YYYY',
				field: formEL.dateFrom.el[0]
			});
			
 		 
		
		}, 

		/*
		 * Init 
		 */
		initDate: function() {
 
		},
		//customereditModal = new CustomereditModal,
		  
		/*
		* Show the time info on the right of this Modal
		*/
		
		renderTimeOffList: function(obj){
			var self = this,
				CONF = self.conf,
				EL = self.conf.EL,
				$modal = EL.$modal,
				timeoff = '',
				timeofflist = obj;
 
 
			 
			//formEL.customerFullName.val(timeofflist.customerFullName).attr('readonly', '');
			$modal.find('input[name="customerFullName"]').val(timeofflist.customerFullName).attr('readonly', '');
			$modal.find('input[name="email"]').val(timeofflist.email).attr('readonly', '');
			$modal.find('input[name="secondaryEmail"]').val(timeofflist.secondaryEmail);
			$modal.find('input[name="phoneNumber"]').val(timeofflist.phoneNumber).attr('readonly', '');
			$modal.find('input[name="gcId"]').val(timeofflist.gcId);
			
			$modal.find('#customerFullNamelb').text(timeofflist.customerFullName);
			$modal.find('#emaillb').text(timeofflist.email);
			$modal.find('#customerStatuslb').text(timeofflist.customerStatus);
			$modal.find('#customerExternalIdlb').text(timeofflist.customerExternalId);
			$modal.find('#lessonsCountlb').text(timeofflist.lessonsCount);
			$modal.find('#phoneNumberlb').text(timeofflist.phoneNumber);
			
		 
		 
			
		},
		
		
		/*
		 * Show the pop and set the timeoff
		 */
		showModal:function(custId,searchCustCriteria){
	 
			var self = this,
				CONF = self.conf,
				EL = CONF.EL,
				$mask = EL.$mask,
				$modal = EL.$modal;
			
			// Set the position of Modal
			var win_height = $(window).height(),
				win_width = $(window).width(),
				m_width = 533,
				m_height = 246,
				top = (win_height - m_height) / 2 + $(window).scrollTop(),
				left = (win_width - m_width) / 2;
	
			if(top < 0) top = 10;
			if(left < 0) left = 10;
			$modal.css({
				left: left,
				top: top
			});
			
			//this.instructorModel = obj 
		  //$this = $(custId.currentTarget);
						
			$.ajax({
				url: CONF.url_load,
				type: 'GET',
				data: {'customerId':custId},
				success: function(result) { 
					self.renderTimeOffList(result);
					self.Customeredit_Modal.set(result);
					$modal.find('input[name="searchCustCriteria"]').val(searchCustCriteria);
				}
			});
			
				
			$modal.show();
			$mask.show();
		},
		
		//var self = this;
	   /*	events: {
			 
				'click .js-set-timeoff-pr': 'setTimeoffpr',
			},*/
		/*
		 * update the time off
		 */
		update: function(e){
			var self = this,
				CONF = self.conf,
				formEL = self.modal.formEL,
				customerFullName=formEL.customerFullName.el.val(),
				gcId=formEL.gcId.el.val(),
				secondaryEmail=formEL.secondaryEmail.el.val(),
				phoneNumber=formEL.phoneNumber.el.val(),
	 
				id = 0,
				data = {'customerFullName':customerFullName,'gcId':gcId,'secondaryEmail':secondaryEmail,'phoneNumber':phoneNumber};
			
			/**
			 * Disable the update button to avoid double click
			 */
			var $updateButton=$('.btn-update-edit-cust');
			$updateButton = $(e.currentTarget);
			$updateButton.prop('disabled', true);
 
			$.ajax({
				url: CONF.url_update,
				type: 'POST',
				contentType: 'application/json',
				data: JSON.stringify(data),
				success: function(result) {
 
					if(result.status){
						var timeoffList_tmp = result.dto,
							timeoff_tmp = '',
							timeoffpop_tmp = '',
							timeoffList_tmp2='';
 
						//self.renderTimeOffList(result.dto);
						self.hideModal();
						
					}else{
						notification.autoHideError(result.message);
 
					}
					/**
					 * Enable the update button
					 */
					$updateButton.prop('disabled', false);
				}
			});
		},
		/*
		 * Hide the Modal and clean the data in the Modal
		 */
		hideModal: function(e){
			var self = this,
			CONF = self.conf,
			EL = CONF.EL,
			formEL = self.modal.formEL;
 
			$.ajax({
				url: CONF.url_cancel_load,
				type: 'GET',
				data: {'siteID':1},
				success: function(result) {
				if(result.status) {
					var set,
						timeStr = '';
				 
				} else {
					notification.autoHideError(result.message);
				}
			}
		});

			//--------------------------------------Rajkumar End---------------------------------
			EL.$modal.hide();
			EL.$mask.hide();
			/**
			 * Clean date and time selected
			 */
			self.cleanDate();
			self.cleanTime();
			$('.btn-search-customer').trigger('click');
			
			
		},
		/**
		 * Clean the date selected
		 */
		cleanDate: function() {
			var self = this,
			EL = self.conf.EL,
			formEL = self.modal.formEL;
			formEL.dateFrom.el.val('');
		},
		/**
		 * Clean the time selected
		 */
		cleanTime: function() {
			var self = this,
			EL = self.conf.EL,
			formEL = self.modal.formEL;
			 
			
		}
	}
	return Customeredit;
});