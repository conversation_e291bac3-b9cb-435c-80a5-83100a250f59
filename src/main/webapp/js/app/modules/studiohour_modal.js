define([
    	'jquery',
    	'moment',
    	'select2', 
    	'icheck', 
    	'mustache', 
    	'underscore', 
    	'backbone',
    	'hash',
    	'notification'
    ],
    function($, moment, select2, iCheck, Mustache, _, Backbone, hash, notification) {
    	function Studiohour(opts) {
    		// Default set
    		this.defaults = {
    			url_update: 'calendar/updateStudioHour.htm',
    			url_get: 'calendar/loadStudioUpdatePage.htm',

    			EL: {
    				$modal: $('#modal_edithour'),
    				$mask: $('.mask')
    			}
    		}

    		// Default active day
    		this.activeDay = 'sunday';

    		// Default versionString
    		this.currentVersion = undefined;
    		
    		this.init(opts);
    	}
    	Studiohour.prototype = {
    		/*
    		* Initialize
    		* @param {Object} the options
    		*/
    		init: function(opts) {
    			this.conf = $.extend({}, this.defaults, opts);
    			var self = this,
    				EL = self.conf.EL,
    				$modal = EL.$modal,
    				Hours_Model, Modal,
    				formEL, modal;

    			// Define hours model
    			Hours_Model = Backbone.Model.extend({

    			});

    			self.hours_model = new Hours_Model;
    			// Define modal model
    			Modal = Backbone.Model.extend({
    				defaults: {

    				},
    				formEL: {
    					'date': {
    						wrap: $modal.find('.btn-group'),
    						el: $modal.find('.btn-group .btn'),
    						weekList: ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
    					},
    					'timeFrom': {
    						el: $modal.find('select[name="timeFrom"]'),
    						set: function(val) {
    							this.el.select2('val', val);
    						}
    					},
    					'timeTo': {
    						el: $modal.find('select[name="timeTo"]'),
    						set: function(val) {
    							this.el.select2('val', val);
    						}
    					},
    					'unavaliable': {
    						el: $modal.find('input[name="unavailable"]'),
    						set: function(val) {
    							if(val) {
    								this.el.iCheck('check');
    							} else {
    								this.el.iCheck('uncheck');
    							}
    						}
    					}
    				},
    				/*
    				* Return the start time
    				* @param {String} day of the week
    				*/
    				getStartTime: function(key) {
    					var formEL = this.formEL,
    						date = formEL.date,
    						key = date.weekList.indexOf(key);
    					return self.hours_model.attributes.list[key].startTimeString;
    				},
    				/*
    				* Return the end time
    				* @param {String} day of the week
    				*/
    				getEndTime: function(key) {
    					var formEL = this.formEL,
    						date = formEL.date,
    						key = date.weekList.indexOf(key);
    					return self.hours_model.get('list')[key].endTimeString;
    				},
    				/*
    				* Set the start time
    				* @param {String} day of week
    				* @param {String} time string
    				*/
    				setStartTime: function(key, val) {
    					var formEL = this.formEL,
    						date = formEL.date,
    						key = date.weekList.indexOf(key),
    						list;
    					list = _.clone(self.hours_model.get('list'));
    					list[key].startTimeString = val;
    					self.hours_model.set({list: list});
    				},
    				/*
    				* Set the end time
    				* @param {String} day of week
    				* @param {String} time string
    				*/
    				setEndTime: function(key, val) {
    					var formEL = this.formEL,
    						date = formEL.date,
    						key = date.weekList.indexOf(key),
    						list;
    					list = _.clone(self.hours_model.get('list'));
    					list[key].endTimeString = val;
    					self.hours_model.set({list: list});
    				},
    				/*
    				* Return the Unavaliable
    				* @param {String} day of the week
    				*/
    				getUnavaliable: function(key) {
    					var formEL = this.formEL,
    						date = formEL.date,
    						key = date.weekList.indexOf(key);
    					return self.hours_model.attributes.list[key].unavaliable;
    				},
    				/*
    				* Set the Unavaliable
    				* @param {String} day of the week
    				* @param {Boolean} if Unavaliable
    				*/
    				setUnavaliable: function(key, val) {
    					var formEL = this.formEL,
    						date = formEL.date,
    						key = date.weekList.indexOf(key),
    						list;
    					list = _.clone(self.hours_model.get('list'));
    					list[key].unavaliable = val;
    					self.hours_model.set({list: list});
    				}
    			});
    		
    			modal = new Modal;
    			self.modal = modal;
    			formEL = modal.formEL;

    			// Change function to change the activeday's data
    			formEL.timeFrom.el.select2().on('change', function(e) {
    				modal.setStartTime(self.activeDay, this.value);
    			});
    			formEL.timeTo.el.select2().on('change', function(e) {
    				modal.setEndTime(self.activeDay, this.value);
    			});
    			formEL.unavaliable.el.iCheck().on('ifClicked', function() {
    				formEL.unavaliable.el.iCheck('toggle');
    				if(this.checked) {
    					modal.setUnavaliable(self.activeDay, true);
    				} else {
    					modal.setUnavaliable(self.activeDay, false);
    				}
    			});

    			// Everyday click render the current data to the select
    			formEL.date.el.click(function() {
    				var $this = $(this),
    					startTime,
    					endTime,
    					unavaliable,
    					day;
    				$this.addClass('active').siblings().removeClass('active');

    				day = $this.data('day');

    				self.activeDay = day;
    				startTime = modal.getStartTime(day);
    				endTime = modal.getEndTime(day);
    				unavaliable = modal.getUnavaliable(day);//xxx
    				formEL.timeTo.set(endTime);
    				formEL.timeFrom.set(startTime);
    				formEL.unavaliable.set(unavaliable);//xxx
    			});
    			
    		},
    		/*
    		* Show the time info on the right of this Modal
    		*/
    		renderTimeList: function() {
    			var self = this,
    				EL = self.conf.EL,
    				$modal = EL.$modal,
    				time = '',
    				weeklist = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];

    			for(var i = 0; i < weeklist.length; i++) {
    				var startTime = self.hours_model.attributes.list[i].startTimeString,
    					endTime = self.hours_model.attributes.list[i].endTimeString,
    					weekDay = self.hours_model.attributes.list[i].weekDay;
    				if(startTime != '') {
    					time += '<p>' + weekDay + ': ' + startTime + ' - ' + endTime + '</p>';
    				}
    			}
    			$modal.find('.studio-hour-list').html(time);
    		},
    		/*
    		* Show the Modal and set the hours_model
    		* @param {Object} the hours_model
    		*/
    		showModal: function(obj) {
    			var self = this,
    				CONF = self.conf,
    				EL = CONF.EL,
    				$mask = EL.$mask,
    				$modal = EL.$modal;
    			// Set the position of Modal
    			var win_height = $(window).height(),
    				win_width = $(window).width(),
    				m_width = 533,
    				m_height = 246,
    				top = (win_height - m_height) / 2 + $(window).scrollTop(),
    				left = (win_width - m_width) / 2;

    			if(top < 0) top = 10;
    			if(left < 0) left = 10;
    			$modal.css({
    				left: left,
    				top: top
    			});
    			// If there is no obj, ajax request to get one
    			if(obj == undefined) {
    				$.ajax({
    					url: CONF.url_get,
    					type: 'GET',
    					success: function(result) {
    						if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
    						self.hours_model.clear();
    						self.hours_model.set(result);
    						
    						//For GCSS-382
    						$modal.find('.sunday-hour').trigger('click');
    						
    						self.renderTimeList();
    						$modal.show();
    						$mask.show();
    					}
    				});
    			} else {
    				// If this is a new model, clear the old, set the new one
    				if(self.hours_model.get('idString') != obj.idString || self.currentVersion == self.hours_model.get('versionString')) {
    					self.hours_model.clear();
    					self.temp_hours_model = $.extend(true, [], obj.list);
    					self.hours_model.set(obj);
    					self.currentVersion = obj.versionString;
    				}
    				
    				//For GCSS-382
    				$modal.find('.sunday-hour').trigger('click');
    				
    				self.renderTimeList();
    				$modal.show();
    				$mask.show();
    			}
    		},
    		/*
    		* Update the studio hour
    		*/
    		update: function(e) {
    			var self = this,
    				CONF = self.conf,
    				EL = CONF.EL,
    				$modal = EL.$modal,
    				data = self.hours_model.attributes,
    				$btn = $(e.currentTarget);
    			$btn.attr('disabled', true);
    			$.ajax({
    				url: CONF.url_update,
    				type: 'POST',
    				contentType: 'application/json',
    				data: JSON.stringify(data),
    				success: function(result) {
    					if(result.status) {
    						var set,
    							timeStr = '';
    						// If it's availability update
    						if(result.editHourShowDTOs && result.editHourShowDTOs.length > 0) {
    							set = result.editHourShowDTOs;
    							// Build up the new time info
    							set.forEach(function(obj) {
    								var startTime = obj.startTimeString,
    									endTime =  obj.endTimeString,
    									weekDay = obj.weekDay;
    								if(startTime != '') {
    									timeStr += '<p class="row"><span class="span2">' + weekDay + ':</span>' + startTime + ' - ' + endTime  + '</p>';
    								}	
    							});
    							// Render the new time info
    							$('.js-availability-wrap:visible').html(timeStr);
    							$('.instructor-list tr.disabled').find('th').eq(3).text(result.availabilityString);
    							// Update the version
    							self.hours_model.set('versionString', result.availabilityversion);
    							self.temp_hours_model = $.extend(true, [], set);
    							self.hours_model.set('list', set);
    							self.hideModal();
    						} else {
    							set = result.set;
    							// Build up the new time info
    							set.forEach(function(obj) {
    								timeStr += Mustache.render('<div class="row"><span class="span5">{{dayString}}</span><span class="span7">{{timeString}}</span></div>', obj);
    							});
    							//For GCSS-482,reloading the calendar page after modifying the edit hour model
    							if($('.top-nav-list .active').hasClass('js-show-schedule')) {
    								var viewType = hash.get('view');
    								switch(viewType) {
    									case 'day':
    										var filterType = hash.get('filter');
    										if(filterType === 'room') {
    											$('#cal_filter_room').trigger('click');
    										} else {
    											$('#cal_filter_instructor').trigger('click');
    										}
    										break;
    									case 'week':
    										$('#cal_view_week').trigger('click');
    										break;
    									default:
    										break;
    								}
    							}
    							// Render the new time info
    							$('.studio-hour').html(timeStr);
    							self.hideModal();
    						}
    					} else {
    						notification.autoHideError(result.message);
    					}
    					$btn.attr('disabled', false);
    				}
    			});
    		},
    		/*
    		* Hide the Modal and clean the data in the Modal
    		*/
    		hideModal: function() {
    			var self = this,
    				EL = self.conf.EL,
    				formEL = self.modal.formEL;
    			
    			//!!!Make a deep copy in order to avoid effect of data changing on prototype model
    			self.hours_model.set('list', $.extend(true, [], self.temp_hours_model));
    			
    			EL.$modal.hide();
    			EL.$mask.hide();
    			EL.$modal.find('.btn-group .active').removeClass('active');
    			formEL.unavaliable.el.iCheck('uncheck');
    			formEL.timeFrom.set('');
    			formEL.timeTo.set('');
    		},
    	}
    	return Studiohour;
    });