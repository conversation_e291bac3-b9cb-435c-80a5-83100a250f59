define([
	'jquery',
	'moment',
	'select2', 
	'icheck', 
	'mustache', 
	'underscore', 
	'backbone',
	'hash',
	'pikaday',
	'notification'
],
function($, moment, select2, iCheck, Mustache, _, Backbone, hash, Pikaday, notification) {
	function Edithour(opts) {
		// Default set
		this.defaults = {
			url_update: 'calendar/updateStudioHour.htm',
			//url_updateOnline: 'onetime/updateOnLine2.htm',
			url_get: 'calendar/loadStudioUpdatePage.htm',
			//url_get: 'calendar/loadInstructorAvailability.htm',
			url_remove: 'onetime/deleteOneTime.htm',
			url_online_remove: 'onetime/deleteOnLineTime.htm',
			url_load: 'onetime/loadonetimes.htm',
			url_online_load: 'onetime/loadOnLineTimes.htm',
			url_updateOnline: 'onetime/updateOnLineAvailable.htm',
			
			url_instore_load: 'instore/loadInstoreTimes.htm',
			url_instore_remove: 'instore/deleteInstoreTime.htm',
			url_updateInstore: 'instore/updateInstoreAvailable.htm',

			EL: {
				$modal: $('#modal_edithour'),
				$mask: $('.mask')
			}
		}

		// Default active day
		this.activeDay = 'sunday';

		// Default versionString
		this.currentVersion = undefined;

		this.groupIndex = 0;
		
		this.oneTimeActive = false;
		
		this.onLineActive = false;
		
		this.instoreActive = false;
		
		this.instructorId = 0;
		
		this.init(opts);
	}
	Edithour.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			this.conf = $.extend({}, this.defaults, opts);
			var self = this,
				EL = self.conf.EL,
				$modal = EL.$modal,
				Hours_Model,OnLinehours_Model, Modal,
				formEL, modal;

			// Define hours model
			Hours_Model = Backbone.Model.extend({

			});

			self.hours_model = new Hours_Model;
			
			OnLinehours_Model = Backbone.Model.extend({

			});

			self.onLinehours_Model = new OnLinehours_Model;
			// Define modal model
			Modal = Backbone.Model.extend({
				defaults: {

				},
				formEL: {
					'date': {
						wrap: $modal.find('.btn-group'),
						el: $modal.find('.btn-group .btn'),
						weekList: ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
					},
					'timeFrom': {
						el: $modal.find('select[name="timeFrom"]'),
						set: function(val) {
							this.el.select2('val', val);
						}
					},
					'timeTo': {
						el: $modal.find('select[name="timeTo"]'),
						set: function(val) {
							this.el.select2('val', val);
						}
					},
					'onetimeFrom': {
						el: $modal.find('select[name="onetimeFrom"]'),
						set: function(val) {
							this.el.select2('val', val);
						}
					},
					'onetimeTo': {
						el: $modal.find('select[name="onetimeTo"]'),
						set: function(val) {
							this.el.select2('val', val);
						}
					},
					'unavaliable': {
						el: $modal.find('input[name="unavailable"]'),
						set: function(val) {
							if(val) {
								this.el.iCheck('check');
							} else {
								this.el.iCheck('uncheck');
							}
						}
					},
					'oneTimeavailable': {
						el: $modal.find('input[name="oneTimeavailable"]'),
						set: function(val) {
							if(val) {
								this.el.iCheck('check');
							} else {
								this.el.iCheck('uncheck');
							}
						}
					},
					'onLineAvailable': {
						el: $modal.find('input[name="onLineAvailable"]'),
						set: function(val) {
							if(val) {
								this.el.iCheck('check');
							} else {
								this.el.iCheck('uncheck');
							}
						}
					},
					'instoreAvailable': {
						el: $modal.find('input[name="instoreAvailable"]'),
						set: function(val) {
							if(val) {
								this.el.iCheck('check');
							} else {
								this.el.iCheck('uncheck');
							}
						}
					},
					'selectDate': {
						el: $modal.find('input[name="selectDate"]')
					}
				},
				/*
				* Return the start time
				* @param {String} day of the week
				*/
				getStartTime: function(key) {
					var formEL = this.formEL,
						date = formEL.date,
						key = date.weekList.indexOf(key);
					return self.hours_model.get('editHourShowList').list[key].startTimeString;
				},
				/*
				* Return the end time
				* @param {String} day of the week
				*/
				getEndTime: function(key) {
					var formEL = this.formEL,
						date = formEL.date,
						key = date.weekList.indexOf(key);
					return self.hours_model.get('editHourShowList').list[key].endTimeString;
				},
				
				/*
				* Return the start time
				* @param {String} day of the week
				*/
				getStartNewTime: function(key) {
					var formEL = this.formEL,
						date = formEL.date,
						key = date.weekList.indexOf(key);
					return self.hours_model.get('editHourShowList').insAvl24HrsList[key].startTimeString;
				},
				/*
				* Return the end time
				* @param {String} day of the week
				*/
				getEndNewTime: function(key) {
					var formEL = this.formEL,
						date = formEL.date,
						key = date.weekList.indexOf(key);
					return self.hours_model.get('editHourShowList').insAvl24HrsList[key].endTimeString;
				},
				/*
				* Set the start time
				* @param {String} day of week
				* @param {String} time string
				*/
				setStartTime: function(key, val) {
					var formEL = this.formEL,
						date = formEL.date,
						key = date.weekList.indexOf(key),
						list;
					list = _.clone(self.hours_model.get('editHourShowList').list);
					list[key].startTimeString = val;
					self.hours_model.set({list: list});
				},
				/*
				* Set the end time
				* @param {String} day of week
				* @param {String} time string
				*/
				setEndTime: function(key, val) {
					var formEL = this.formEL,
						date = formEL.date,
						key = date.weekList.indexOf(key),
						list;
					list = _.clone(self.hours_model.get('editHourShowList').list);
					list[key].endTimeString = val;
					self.hours_model.set({list: list});
				},
				/*
				* Return the Unavaliable
				* @param {String} day of the week
				*/
				getUnavaliable: function(key) {
					var formEL = this.formEL,
						date = formEL.date,
						key = date.weekList.indexOf(key);
					return self.hours_model.get('editHourShowList').list[key].unavaliable;
				},
				/*
				* Set the Unavaliable
				* @param {String} day of the week
				* @param {Boolean} if Unavaliable
				*/
				setUnavaliable: function(key, val) {
					var formEL = this.formEL,
						date = formEL.date,
						key = date.weekList.indexOf(key),
						list;
					list = _.clone(self.hours_model.get('editHourShowList').list);
					list[key].unavaliable = val;
					self.hours_model.set({list: list});
				},
				/*
				* Return the Unavaliable
				* @param {String} day of the week
				*/
				getOneTimeavailable: function(key) {
					var formEL = this.formEL,
						date = formEL.date,
						key = date.weekList.indexOf(key);
					return self.hours_model.get('onetimes').onetimeStartToEnd;
				},
				
				//--Raj =======
				getOnLineAvailable: function(key) {
					var formEL = this.formEL,
						date = formEL.date,
						key = date.weekList.indexOf(key);
					return self.hours_model.get('onetimes').onetimeStartToEnd;
				},
				/*
				* Set the Unavaliable
				* @param {String} day of the week
				* @param {Boolean} if Unavaliable
				*/
				setOneTimeavailable: function(key, val) {
					var formEL = this.formEL,
						date = formEL.date,
						key = date.weekList.indexOf(key),
						list;
					list = _.clone(self.hours_model.get('onetimes'));
					list[key].onetimeStartToEnd = val;
					self.hours_model.set({list: list});
				},
				setOnLineAvailable: function(key, val) {
					var formEL = this.formEL,
						date = formEL.date,
						key = date.weekList.indexOf(key),
						list;
					list = _.clone(self.hours_model.get('onetimes'));
					list[key].onetimeStartToEnd = val;
					self.hours_model.set({list: list});
				}
			});
		
			modal = new Modal;
			self.modal = modal;
			formEL = modal.formEL;

			// Change function to change the activeday's data
			formEL.timeFrom.el.select2().on('change', function(e) {
				modal.setStartTime(self.activeDay, this.value);
			});
			formEL.timeTo.el.select2().on('change', function(e) {
				modal.setEndTime(self.activeDay, this.value);
			});
			formEL.onetimeFrom.el.select2().on('change', function(e) {
				//GCSS-670
				//modal.setStartTime(self.activeDay, this.value);
			});
			formEL.onetimeTo.el.select2().on('change', function(e) {
				//GCSS-670
				//modal.setEndTime(self.activeDay, this.value);
			});
			formEL.unavaliable.el.iCheck().on('ifClicked', function() {
				formEL.unavaliable.el.iCheck('toggle');
				if(this.checked) {
					modal.setUnavaliable(self.activeDay, true);
				} else {
					modal.setUnavaliable(self.activeDay, false);
				}
			});
			formEL.oneTimeavailable.el.iCheck().on('ifClicked', function() {
				formEL.oneTimeavailable.el.iCheck('toggle');
				if(this.checked) {
					$modal.find('.hour-select').addClass('dayDisabled');
					formEL.oneTimeavailable.el.iCheck('enable');
					formEL.onLineAvailable.el.iCheck('disable');
					formEL.instoreAvailable.el.iCheck('disable');
					formEL.unavaliable.el.iCheck('disable');
					$modal.find('.jq-selectDate').show();
					$modal.find('.jq-availability').hide();
					$modal.find('.one-time-wrap').show();
					$modal.find('.availability-wrap').hide();
					
					$modal.find('.breakLineWeb').hide();
					$modal.find('.breakLineWebins').hide();
					$modal.find('.breakLineWebonl').hide();
					
					self.conf.url_update = 'onetime/updateOnetime.htm';
					this.groupIndex = $modal.find('.hour-select').find('button.active').index();
					$modal.find('.hour-select').find('button.active').removeClass('active');
					$('#modal_set_availability').addClass('modal_one_time_availability');
					formEL.onetimeTo.set('');
					formEL.onetimeFrom.set('');
					self.oneTimeActive = true;
					self.onLineActive = false;
					self.instoreActive = false;
				} else {
					$modal.find('.hour-select').find('button:nth-child('+ (this.groupIndex+1) +')').trigger('click');
					$modal.find('.hour-select').removeClass('dayDisabled');
					
					formEL.oneTimeavailable.el.iCheck('enable');
					formEL.onLineAvailable.el.iCheck('enable');
					formEL.instoreAvailable.el.iCheck('enable');
					formEL.unavaliable.el.iCheck('enable');
					 
					$modal.find('.jq-selectDate').hide();
					$modal.find('.jq-availability').show();
					$modal.find('.one-time-wrap').hide();
					$modal.find('.availability-wrap').show();
					
					$modal.find('.breakLineWeb').show();
					$modal.find('.breakLineWebins').show();
					$modal.find('.breakLineWebonl').show();
					
					self.conf.url_update = 'calendar/updateStudioHour.htm';
					$('#modal_set_availability').removeClass('modal_one_time_availability');
					self.oneTimeActive = false;
					self.onLineActive = false;
					self.instoreActive = false;
				}
			});
			
			//--Raj =======
			formEL.onLineAvailable.el.iCheck().on('ifClicked', function() {
				formEL.onLineAvailable.el.iCheck('toggle');
				if(this.checked) {
					//$modal.find('input[name="oneTimeavailable"]').iCheck().iCheck('disable');
					formEL.unavaliable.el.iCheck('disable');
					formEL.oneTimeavailable.el.iCheck('disable');
					formEL.onLineAvailable.el.iCheck('enable');
					formEL.instoreAvailable.el.iCheck('disable');
	 				//$modal.find('.time-wrap').show();					
					self.conf.url_updateOnline = 'onetime/updateOnLineAvailable.htm';
					
					
					$modal.find('.jq-availability').hide();
					$modal.find('.jq-displayDate').hide();
					$modal.find('.one-time-wrap').hide();
					$modal.find('.availability-wrap').hide();
					$modal.find('.instore-time-wrap').hide();
					$modal.find('.onlineView-time-wrap').hide();
			
					$modal.find('.instoreView-time-wrap').show();
					$modal.find('.online-time-wrap').show();
					$modal.find('.online-time-wrap').show();
					$modal.find('.jq-selectDate').show();
					
					$modal.find('.breakLineWeb').hide();
					$modal.find('.breakLineWebins').hide();
					$modal.find('.breakLineWebonl').show();
					
					self.oneTimeActive = false;
					self.instoreActive = false;
					self.onLineActive = true;
 
				} else {
					$modal.find('.online-time-wrap').hide();
					//$modal.find('.hour-select').find('button:nth-child('+ (this.groupIndex+1) +')').trigger('click');
					/*$modal.find('input[name="unavailable"]').iCheck().iCheck('enable');
					$modal.find('input[name="oneTimeavailable"]').iCheck().iCheck('enable');
					$modal.find('input[name="onLineAvailable"]').iCheck().iCheck('uncheck');*/
					//$modal.find('input[name="oneTimeavailable"]').iCheck().iCheck('enable');
					formEL.oneTimeavailable.el.iCheck('enable');
					formEL.onLineAvailable.el.iCheck('enable');
					formEL.instoreAvailable.el.iCheck('enable');
					formEL.unavaliable.el.iCheck('enable');
					$modal.find('.jq-selectDate').hide();
					$modal.find('.jq-availability').show();
					//$modal.find('.one-time-wrap').hide();
					$modal.find('.availability-wrap').show();
					self.conf.url_update = 'calendar/updateStudioHour.htm';
					$modal.find('.jq-displayDate').show();
					$modal.find('.online-time-wrap').hide();
					$modal.find('.onlineView-time-wrap').hide();
					$modal.find('.instore-time-wrap').hide();
					$modal.find('.instoreView-time-wrap').hide();
					$modal.find('.breakLineWeb').show();
					$modal.find('.breakLineWebins').show();
					$modal.find('.breakLineWebonl').show();
					self.oneTimeActive = false;
					self.onLineActive = false;
					self.instoreActive = false;
				}
			});
			
			//--Raj =======
			formEL.instoreAvailable.el.iCheck().on('ifClicked', function() {
				formEL.instoreAvailable.el.iCheck('toggle');
				if(this.checked) {
					//$modal.find('input[name="oneTimeavailable"]').iCheck().iCheck('disable');
					formEL.unavaliable.el.iCheck('disable');
					formEL.oneTimeavailable.el.iCheck('disable');
					formEL.onLineAvailable.el.iCheck('disable');
					formEL.instoreAvailable.el.iCheck('enable');
					
					
					$modal.find('.instore-time-wrap').show();
					
					self.conf.url_updateInstore = 'instore/updateInstoreAvailable.htm';
					$modal.find('.jq-selectDate').show();
					//$modal.find('input[name="onLineAvailable"]').iCheck().iCheck('uncheck');
					$modal.find('.jq-availability').hide();
					$modal.find('.jq-displayDate').hide();
					//$modal.find('.time-wrap').show();
					$modal.find('.one-time-wrap').hide();
					$modal.find('.availability-wrap').hide();
					
					$modal.find('.instore-time-wrap').show();
					$modal.find('.onlineView-time-wrap').show();
					$modal.find('.instoreView-time-wrap').hide();
					$modal.find('.online-time-wrap').hide();
					
					$modal.find('.breakLineWeb').hide();
					$modal.find('.breakLineWebins').show();
					$modal.find('.breakLineWebonl').hide();
					
					self.oneTimeActive = false;
					self.onLineActive = false;
					self.instoreActive = true;
 
				} else {
					  
					//$modal.find('.online-time-wrap').hide();
					//$modal.find('.hour-select').find('button:nth-child('+ (this.groupIndex+1) +')').trigger('click');
					/*$modal.find('input[name="unavailable"]').iCheck().iCheck('enable');
					$modal.find('input[name="oneTimeavailable"]').iCheck().iCheck('enable');
					$modal.find('input[name="onLineAvailable"]').iCheck().iCheck('uncheck');
					$modal.find('input[name="instoreAvailable"]').iCheck().iCheck('uncheck');*/

					formEL.unavaliable.el.iCheck('enable');
					formEL.oneTimeavailable.el.iCheck('enable');
					formEL.onLineAvailable.el.iCheck('enable');
					//formEL.instoreAvailable.el.iCheck('enable');
					 
					$modal.find('.jq-selectDate').hide();
					$modal.find('.jq-availability').show();
					//$modal.find('.one-time-wrap').hide();
					$modal.find('.availability-wrap').show();
					$modal.find('.jq-displayDate').show();
					self.conf.url_update = 'calendar/updateStudioHour.htm';
					//url_update: 'calendar/updateStudioHour.htm',
					$modal.find('.online-time-wrap').hide();
					$modal.find('.onlineView-time-wrap').hide();
					$modal.find('.instore-time-wrap').hide();
					$modal.find('.instoreView-time-wrap').hide();
					$modal.find('.breakLineWeb').show();
					$modal.find('.breakLineWebins').show();
					$modal.find('.breakLineWebonl').show();
					self.oneTimeActive = false;
					self.onLineActive = false;
					self.instoreActive = false;
				}
			});
			
			//#########################--------Rajkumar -------##################################
			//#########################--------Rajkumar -------##################################
			formEL.selectDate.el.on('change', function() {
				//_this.changePrintUrl();
			});
			selectDate = new Pikaday({
				format: 'MM/DD/YYYY',
				field: formEL.selectDate.el[0]
			});

			// Everyday click render the current data to the select
			formEL.date.el.click(function() {
				var $this = $(this),
					startTime,
					endTime,
					startNewTime,
					endNewTime,
					unavaliable,
					day;
				
				$this.addClass('active').siblings().removeClass('active');

				day = $this.data('day');
				self.activeDay = day;
				startTime = modal.getStartTime(day);
				endTime = modal.getEndTime(day);
				startNewTime = modal.getStartNewTime(day);
				endNewTime = modal.getEndNewTime(day);
				unavaliable = modal.getUnavaliable(day);//xxx
				formEL.timeTo.set(endTime);
				formEL.timeFrom.set(startTime);
				//$modal.find('select[name="onetimeFrom"]').val(startTime)
				//console.log(' set endTime IIIII '+self.oneTimeActive);
				if(!self.oneTimeActive){
				formEL.onetimeTo.set(endNewTime);
				formEL.onetimeFrom.set(startNewTime);
				}
				formEL.unavaliable.set(unavaliable);//xxx
			});
			
		},
		renderOneTime: function(obj){
			var self = this,
				CONF = self.conf,
				EL = self.conf.EL,
				$modal = EL.$modal,
				oneTime = '',
				oneTimeList = obj;
			
			for(var i = 0; i < oneTimeList.length; i++){
				if(oneTimeList[i].onetimeStartToEnd!=''){
					oneTime += '<p><a class="icon icon-remove js-remove-oneTime" data-id="'+oneTimeList[i].onetimeId+'" href="javascript:;">x</a>' + oneTimeList[i].onetimeStartToEnd + '</p>';
				}
			}
			
			$modal.find('.one-time-list').html(oneTime);

			
			$modal.find('.js-remove-oneTime').on('click',function(){
				var onetimeId = $(this).attr('data-id')*1;
					_this = $(this)
				self.removeForId(onetimeId,_this);
			});
		},
		
		/******
		 * New Oneline render
		 */
		renderOnline: function(obj){
			var self = this,
				CONF = self.conf,
				EL = self.conf.EL,
				brk = '',
				$modal = EL.$modal,
				onlineTime = '',
				onlineList = obj;
			
			for(var i = 0; i < onlineList.length; i++){
				if(onlineList[i].onetimeStartToEnd!=''){
					onlineTime += '<p><a class="icon icon-remove js-remove-onlineTime" data-id="'+onlineList[i].onlineAvailabilityId+'" href="javascript:;">x</a>' + onlineList[i].onetimeStartToEnd + '</p>';
					brk += '<br>';
				}
			}
 
			$modal.find('.online-time-list').html(onlineTime);
			$('.breakLineWebonl').html(brk);

			//js-remove-onlineTime
			$modal.find('.js-remove-onlineTime').on('click',function(){
				var onlinetimeId = $(this).attr('data-id')*1;
					_this = $(this)
					self.removeForOnLineId(onlinetimeId,_this);
			});
		},
		
		renderOnlineView: function(obj){
			var self = this,
				CONF = self.conf,
				EL = self.conf.EL,
				$modal = EL.$modal,
				brk = '',
				onlineTime = '',
				onlineList = obj;
 
			for(var i = 0; i < onlineList.length; i++){
				if(onlineList[i].onetimeStartToEnd!=''){
					onlineTime += '<p>' + onlineList[i].onetimeStartToEnd + '</p>';
					brk += '<br>';
					
					//onlineTime += '<p><a class="icon icon-remove js-remove-onlineTime" data-id="'+onlineList[i].onlineAvailabilityId+'" href="javascript:;"></a>' + onlineList[i].onetimeStartToEnd + '</p>';
				}
			}
			//$('.breakLineWebonl').html(brk);
			$modal.find('.onlineView-time-list').html(onlineTime);
			$('.breakLineWebonl').html(brk);
 
		},
		/******
		 * New In Store render
		 */
		renderInstore: function(obj){
			var self = this,
				CONF = self.conf,
				EL = self.conf.EL,
				$modal = EL.$modal,
				brk = '',
				instoreTime = '',
				instoreList = obj;
			
			for(var i = 0; i < instoreList.length; i++){
				if(instoreList[i].onetimeStartToEnd!=''){
					instoreTime += '<p><a class="icon icon-remove js-remove-instoreTime" data-id="'+instoreList[i].instoreAvailabilityId+'" href="javascript:;">x</a>' + instoreList[i].onetimeStartToEnd + '</p>';
					brk += '<br>';
				}
			}

			
			$modal.find('.instore-time-list').html(instoreTime);
			$('.breakLineWebonl').html(brk); 

			//js-remove-onlineTime
			$modal.find('.js-remove-instoreTime').on('click',function(){
				var instoretimeId = $(this).attr('data-id')*1;
					_this = $(this)
					self.removeForInstoreId(instoretimeId,_this);
			});
		},
		
		renderInstoreView: function(obj){
			var self = this,
				CONF = self.conf,
				EL = self.conf.EL,
				$modal = EL.$modal,
				brk = '',
				instoreTime = '',
				instoreList = obj;
 
			for(var i = 0; i < instoreList.length; i++){
				if(instoreList[i].onetimeStartToEnd!=''){
					instoreTime += '<p>' + instoreList[i].onetimeStartToEnd + '</p>';
					brk += '<br>';
					//instoreTime += '<p><a class="icon icon-remove js-remove-instoreTime" data-id="'+instoreList[i].instoreAvailabilityId+'" href="javascript:;"></a>' + instoreList[i].onetimeStartToEnd + '</p>';
				}
			}
			$('.breakLineWebins').html(brk);
			$modal.find('.instoreView-time-list').html(instoreTime);
			
 
		},
		/*
		* Show the time info on the right of this Modal
		*/
		renderTimeList: function() {
			var self = this,
				EL = self.conf.EL,
				$modal = EL.$modal,
				time = '',
				brk = '',
				oneTime = '',
				weeklist = self.temp_hours_model,
				//weekSlotlist = self.temp_slothours_model,
				oneTimeList = self.hours_model.get('onetimes');
			
			if(self.onLineActive){

			for(var i = 0; i < weeklist.length; i++) {
				var startTime = weeklist[i].startTimeString,
					endTime = weeklist[i].endTimeString,
					brk = '',
					weekDay = weeklist[i].weekDay;
				if(startTime != '') {
					//time += '<p>' + weekDay + ': ' + startTime + ' - ' + endTime + '</p>';
					//time +=   weekDay + ': ' + startTime + ' - ' + endTime ;
					time += '<p><a class="icon icon-remove js-remove-oneTime" data-id="111" href="javascript:;">x</a>' + weekDay + ': ' + startTime + ' - ' + endTime + '</p>';
				}
				brk += '<br>';
			}
		}else{
			for(var i = 0; i < weeklist.length; i++) {
				var startTime = weeklist[i].startTimeString,
					endTime = weeklist[i].endTimeString,
					brk = '',
					weekDay = weeklist[i].weekDay;
				if(startTime != '') {
					 time += '<p>' + weekDay + ': ' + startTime + ' - ' + endTime + '</p>';
					//time +=   weekDay + ': ' + startTime + ' - ' + endTime ;
					//time += '<p><a class="icon icon-remove js-remove-oneTime" data-id="111" href="javascript:;">x</a>' + weekDay + ': ' + startTime + ' - ' + endTime + '</p>';
				}
				brk += '<br>';
			}
		}
			
			for(var i = 0; i < oneTimeList.length; i++){
				var onetimeString = self.hours_model.get('onetimes')[i];
				 
				if(onetimeString.onetimeStartToEnd!=''){
					oneTime += '<p><a class="icon icon-remove js-remove-oneTime" data-id="'+onetimeString.onetimeId+'" href="javascript:;">x</a>' + onetimeString.onetimeStartToEnd + '</p>';
				}
				 
			}
			
			$modal.find('.studio-hour-list').html(time);
			$modal.find('.one-time-list').html(oneTime);
			$('.breakLineWeb').html(brk);

			
			$modal.find('.js-remove-oneTime').on('click',function(){
				var onetimeId = $(this).attr('data-id')*1,
					_this = $(this);
				self.removeForId(onetimeId,_this);
			});
		},
		/*
		 * remove one time for the id
		 */
		removeForId: function(id,obj){
			var self = this,
				CONF = self.conf,
				EL = CONF.EL,
				$mask = EL.$mask,
				$modal = EL.$modal,
				_this = obj;
			
			$.ajax({
				url: CONF.url_remove,
				type: 'POST',
				data: {'oneTimeId':id},
				success: function(result) {
					if(result.status){
						$.ajax({
							url: CONF.url_load,
							type: 'POST',
							data: {'instructorId':self.instructorId},
							success: function(newresult) {
								var onetime_tmp ='',
								pop_onetime_tmp = '',
								onetimeList_tmp = newresult.onetimes;
								self.renderOneTime(newresult.onetimes);
								if(onetimeList_tmp.length > 0){
									for(i in onetimeList_tmp){
										onetime_tmp += '<p class="row">' + onetimeList_tmp[i].onetimeStartToEnd  + '</p>';
										}
								}
								$('.js-oneTimeAvailability-wrap').empty().html(onetime_tmp);
								$('.breakLineWeb').html('<br>');
							}
						});
					}else{
						notification.autoHideError(result.message);
					}
				}
			});
		},
		
		/*
		 * remove On line time for the id
		 */
		removeForOnLineId: function(id,obj){
			var self = this,
				CONF = self.conf,
				EL = CONF.EL,
				$mask = EL.$mask,
				$modal = EL.$modal,
				_this = obj;
			
			$.ajax({
				url: CONF.url_online_remove,
				type: 'POST',
				data: {'onLineTimeId':id},
				success: function(result) {
					if(result.status){
						$.ajax({
							url: CONF.url_online_load,
							type: 'POST',
							data: {'instructorId':self.instructorId},
							success: function(newresult) {
								var onlinetime_tmp ='',
								pop_onlinetime_tmp = '',
								onlinetimeList_tmp = newresult.onlinetimes;
								
								self.renderOnline(newresult.onlinetimes);
								if(onlinetimeList_tmp.length > 0){
									for(i in onlinetimeList_tmp){
										onlinetime_tmp += '<p class="row">' + onlinetimeList_tmp[i].onetimeStartToEnd  + '</p>';
										}
								}
								//$('.js-oneTimeAvailability-wrap').empty().html(onetime_tmp);
								$('.onlineView-time-list').empty().html(onlinetime_tmp);
								$('.breakLineWeb').html('<br>');
								
							}
						});
					}else{
						notification.autoHideError(result.message);
					}
				}
			});
		},
		
		/*
		 * remove IN STORE time for the id
		 */
		removeForInstoreId: function(id,obj){
			var self = this,
				CONF = self.conf,
				EL = CONF.EL,
				$mask = EL.$mask,
				$modal = EL.$modal,
				_this = obj;
			
			$.ajax({
				url: CONF.url_instore_remove,
				type: 'POST',
				data: {'instoreTimeId':id},
				success: function(result) {
					if(result.status){
						$.ajax({
							url: CONF.url_instore_load,
							type: 'POST',
							data: {'instructorId':self.instructorId},
							success: function(newresult) {
								var instoretime_tmp ='',
								pop_instoretime_tmp = '',
								instoretimeList_tmp = newresult.instoretimes;
								
								self.renderInstore(newresult.instoretimes);
								if(instoretimeList_tmp.length > 0){
									for(i in instoretimeList_tmp){
										instoretime_tmp += '<p class="row">' + instoretimeList_tmp[i].onetimeStartToEnd  + '</p>';
										}
								}

								//$('.js-oneTimeAvailability-wrap').empty().html(onetime_tmp);
								$('.instoreView-time-list').empty().html(instoretime_tmp);
								$('.breakLineWeb').html('<br>');
								
							}
						});
					}else{
						notification.autoHideError(result.message);
					}
				}
			});
		},
		/*
		* Show the Modal and set the hours_model
		* @param {Object} the hours_model
		*/
		showModal: function(obj) {
			var self = this,
				CONF = self.conf,
				EL = CONF.EL,
				$mask = EL.$mask,
				$modal = EL.$modal;
			// Set the position of Modal
			var win_height = $(window).height(),
				win_width = $(window).width(),
				m_width = 533,
				m_height = 246,
				top = (win_height - m_height) / 2 + $(window).scrollTop(),
				left = (win_width - m_width) / 2;

			if(top < 0) top = 10;
			if(left < 0) left = 10;
			$modal.css({
				left: left,
				top: top
			});
			
			this.instructorModel = obj;
			this.instructorModel.set('idVal',obj.get('id'));
			// If there is no obj, ajax request to get one
			if(obj == undefined) {
				$.ajax({
					url: CONF.url_get,
					type: 'GET',
					success: function(result) {
						if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
						self.hours_model.clear();
						self.hours_model.set(result);
						
						//For GCSS-382
						$modal.find('.sunday-hour').trigger('click');
						
						self.renderTimeList();
						$modal.show();
						$mask.show();
					}
				});
			} else {
				// If this is a new model, clear the old, set the new one
				//GCSS-683
				/*console.log();
				console.log(' set self.oneTimeActive    ------  '+self.oneTimeActive);
				console.log(' set self.onLineActive    ------  '+self.onLineActive);
				console.log(' set self.instoreActive    ------  '+self.instoreActive);*/
				//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
				if(!self.oneTimeActive){
					if(self.hours_model.get('id') != obj.get('id') || self.currentVersion == self.hours_model.get('versionString')) {
						self.hours_model.clear();
						self.temp_hours_model = $.extend(true, [], obj.get('editHourShowList').list);
						self.hours_model.set(obj.attributes);
						self.currentVersion = obj.versionString;
					}
					this.instructorId = obj.id;
					
					$.ajax({
						url: CONF.url_online_load,
						type: 'POST',
						data: {'instructorId':obj.id},
						success: function(result) {
							self.renderOnline(result.onlinetimes);
						}
					});
					
					$.ajax({
						url: CONF.url_instore_load,
						type: 'POST',
						data: {'instructorId':obj.id},
						success: function(result) {
							self.renderInstoreView(result.instoretimes);
						}
					});
					
					$modal.find('.sunday-hour').trigger('click');
					
					self.renderTimeList();
					$modal.show();
					$mask.show();
					
				} 
				//^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
				if(!self.oneTimeActive){
					if(self.hours_model.get('id') != obj.get('id') || self.currentVersion == self.hours_model.get('versionString')) {
						self.hours_model.clear();
						self.temp_hours_model = $.extend(true, [], obj.get('editHourShowList').list);
						self.hours_model.set(obj.attributes);
						self.currentVersion = obj.versionString;
					}
					this.instructorId = obj.id;
					
					$.ajax({
						url: CONF.url_instore_load,
						type: 'POST',
						data: {'instructorId':obj.id},
						success: function(result) {
							self.renderInstore(result.instoretimes);
						}
					});
					
					$.ajax({
						url: CONF.url_online_load,
						type: 'POST',
						data: {'instructorId':obj.id},
						success: function(result) {
							self.renderOnlineView(result.onlinetimes);
						}
					});
					
					$modal.find('.sunday-hour').trigger('click');
					
					self.renderTimeList();
					$modal.show();
					$mask.show();
					
				}
				//^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
				if(2>1)  {
				if(self.hours_model.get('id') != obj.get('id') || self.currentVersion == self.hours_model.get('versionString')) {
					self.hours_model.clear();
					self.temp_hours_model = $.extend(true, [], obj.get('editHourShowList').list);
					self.hours_model.set(obj.attributes);
					self.currentVersion = obj.versionString;
				}
				//modle can not set 
				//self.hours_model.set(obj);
				//self.temp_hours_model = $.extend(true, [], obj.get('editHourShowList').list);
				
				this.instructorId = obj.id;
				
				$.ajax({
					url: CONF.url_load,
					type: 'POST',
					data: {'instructorId':obj.id},
					success: function(result) {
						self.renderOneTime(result.onetimes);
					}
				});
				
				//For GCSS-382
				$modal.find('.sunday-hour').trigger('click');
				
				self.renderTimeList();
				$modal.show();
				$mask.show();
				//this.instructorModel = obj;
			}
			}
		},
		/*
		* Update the studio hour
		*/
		update: function(e) {
 
			var self = this,
				CONF = self.conf,
				EL = CONF.EL,
				$modal = EL.$modal,
				$btn = $(e.currentTarget);
				formEL = self.modal.formEL,
				selectDate = formEL.selectDate.el.val(),
				//GCSS-670
				_timeFrom = formEL.onetimeFrom.el.val(),
				_timeTo = formEL.onetimeTo.el.val();
				
				$btn.attr('disabled', true);

				var idVal = self.instructorModel.get('idVal');
		
				var data = {'startDate':selectDate,'fromTime':_timeFrom,'toTime':_timeTo,'instructorId':idVal};

			if(self.oneTimeActive){

				//GCSS-670
				
				$.ajax({
					url: CONF.url_update,
					type: 'POST',
					contentType: 'application/json',
					data: JSON.stringify(data),
					success: function(result) {
						var onetime_tmp ='',
							pop_onetime_tmp = '';
						if(result.status) {
							
							if(result.dto.length > 0){
								for(i in result.dto){
									onetime_tmp += '<p class="row">' + result.dto[i].onetimeStartToEnd  + '</p>';
									pop_onetime_tmp += '<p><a class="icon icon-remove js-remove-oneTime" data-id="'+result.dto[i].onetimeId+'" href="javascript:;">x</a>' + result.dto[i].onetimeStartToEnd + '</p>';
								}
							}
							
							$('.js-oneTimeAvailability-wrap').empty().html(onetime_tmp);
							$('.one-time-list').empty().html(pop_onetime_tmp);
							$('.breakLineWeb').html('<br>');

							
							$modal.find('.js-remove-oneTime').on('click',function(){
								var onetimeId = $(this).attr('data-id')*1,
									_this = $(this);
								self.removeForId(onetimeId,_this);
							});
							
							formEL.selectDate.el.val('');
							formEL.onetimeTo.set('');
							formEL.onetimeFrom.set('');
							
						}else {
							notification.autoHideError(result.message);
						}
						$btn.attr('disabled', false);
					}
				});
			}else if(self.onLineActive && !self.oneTimeActive && !self.instoreActive){
		 	
			//##################################################################
 
				var dateStrTm =  $modal.find('select[name="onetimeFrom"]').val();
				this.groupIndex = $modal.find('.hour-select').find('button.active').index();
				var weekDay = this.groupIndex;
				data = {'startDate':selectDate,'fromTime':_timeFrom,'toTime':_timeTo,'instructorId':idVal,'weekDay':weekDay};
	
				$.ajax({
					url: CONF.url_updateOnline,
					type: 'POST',
					contentType: 'application/json',
					data: JSON.stringify(data),
					success: function(result) {
						var onlinetime_tmp ='',
						pop_onlinetime_tmp = '';
					if(result.status) {
		
						if(result.dto && result.dto.length > 0){
							set = result.dto;
							
							for(i in result.dto){
								//onlinetime_tmp += '<p class="row">' + result.dto[i].onetimeStartToEnd  + '</p>';
								//pop_onlinetime_tmp += '<p><a class="icon icon-remove js-remove-onlineTime" data-id="'+result.dto[i].onlineAvailabilityId+'" href="javascript:;">x</a> '  + result.dto[i].weekDay + ': ' + result.editHourShowDTOs[i].startTimeString + ' - ' + result.editHourShowDTOs[i].endTimeString  +  '</p>';
								pop_onlinetime_tmp += '<p><a class="icon icon-remove js-remove-onlineTime" data-id="'+result.dto[i].onlineAvailabilityId+'" href="javascript:;">x</a>' + result.dto[i].onetimeStartToEnd + '</p>';
							}
							for(i in result.dto){
								onlinetime_tmp += '<p class="row">' + result.dto[i].onetimeStartToEnd  + '</p>';
							}
						}
						
						//$('.js-oneTimeAvailability-wrap').empty().html(onetime_tmp);
						$('.online-time-list').empty().html(pop_onlinetime_tmp);
						$('.onlineView-time-list').empty().html(onlinetime_tmp);
						//$('.breakLineWeb').html('<br>');
						
						 

						
						$modal.find('.js-remove-onlineTime').on('click',function(){
							var onlinetimeId = $(this).attr('data-id')*1,
								_this = $(this);
							self.removeForOnLineId(onlinetimeId,_this);
						});
						
						//formEL.selectDate.el.val('');
						//formEL.onetimeTo.set('');
						//formEL.onetimeFrom.set('');
						
					}else {
						notification.autoHideError(result.message);
					}
					$btn.attr('disabled', false);
					}
				
				});	
	 
	
				//############################################################################
			}
			else if(!self.onLineActive && !self.oneTimeActive && self.instoreActive){

				//##################################################################

				var dateStrTm =  $modal.find('select[name="onetimeFrom"]').val();
				this.groupIndex = $modal.find('.hour-select').find('button.active').index();
				var weekDay = this.groupIndex;
				data = {'startDate':selectDate,'fromTime':_timeFrom,'toTime':_timeTo,'instructorId':idVal,'weekDay':weekDay};
				$.ajax({
					url: CONF.url_updateInstore,
					type: 'POST',
					contentType: 'application/json',
					data: JSON.stringify(data),
					success: function(result) {
						var instoretime_tmp ='',
						pop_instoretime_tmp = '';
					if(result.status) {
			
						if(result.dto && result.dto.length > 0){
							set = result.dto;

							for(i in result.dto){
								//instoretime_tmp += '<p class="row">' + result.dto[i].onetimeStartToEnd  + '</p>';
								//pop_onlinetime_tmp += '<p><a class="icon icon-remove js-remove-onlineTime" data-id="'+result.dto[i].onlineAvailabilityId+'" href="javascript:;">x</a> '  + result.dto[i].weekDay + ': ' + result.editHourShowDTOs[i].startTimeString + ' - ' + result.editHourShowDTOs[i].endTimeString  +  '</p>';
								pop_instoretime_tmp += '<p><a class="icon icon-remove js-remove-instore" data-id="'+result.dto[i].instoreAvailabilityId+'" href="javascript:;">x</a>' + result.dto[i].onetimeStartToEnd + '</p>';
							}
							
							for(i in result.dto){
								instoretime_tmp += '<p class="row">' + result.dto[i].onetimeStartToEnd  + '</p>';
							}
						}
						
						//$('.js-oneTimeAvailability-wrap').empty().html(onetime_tmp);
						$('.instore-time-list').empty().html(pop_instoretime_tmp);
						$('.instoreView-time-list').empty().html(instoretime_tmp);
						//$('.breakLineWeb').html('<br>');
						
						$modal.find('.js-remove-instore').on('click',function(){
							var instoretimeId = $(this).attr('data-id')*1,
								_this = $(this);
							self.removeForInstoreId(instoretimeId,_this);
						});
						
						//formEL.selectDate.el.val('');
						//formEL.onetimeTo.set('');
						//formEL.onetimeFrom.set('');
						
					}else {
						notification.autoHideError(result.message);
					}
					$btn.attr('disabled', false);
					}
				
				});	
				 
			}else{
				
				data = self.hours_model.get('editHourShowList');				
				$.ajax({
					url: CONF.url_update,
					type: 'POST',
					contentType: 'application/json',
					data: JSON.stringify(data),
					success: function(result) {
						if(result.status) {
							var set,
								timeStr = '';
							// If it's availability update
							if(result.editHourShowDTOs && result.editHourShowDTOs.length > 0) {
								set = result.editHourShowDTOs;
								// Build up the new time info
								set.forEach(function(obj) {
									var startTime = obj.startTimeString,
										endTime =  obj.endTimeString,
										weekDay = obj.weekDay;
									if(startTime != '') {
										timeStr += '<p class="row"><span class="span2">' + weekDay + ':</span>' + startTime + ' - ' + endTime  + '</p>';
									}	
								});
								// Render the new time info
								$('.js-availability-wrap:visible').html(timeStr);
								$('.instructor-list tr.disabled').find('th').eq(3).text(result.availabilityString);
								// Update the version
								self.hours_model.set('versionString', result.availabilityversion);
								self.temp_hours_model = $.extend(true, [], set);
								self.hours_model.set('list', set);
								data.list = set;
								self.hideModal();
							} else {
								set = result.set;
								// Build up the new time info
								set.forEach(function(obj) {
									timeStr += Mustache.render('<div class="row"><span class="span5">{{dayString}}</span><span class="span7">{{timeString}}</span></div>', obj);
								});
								//For GCSS-482,reloading the calendar page after modifying the edit hour model
								if($('.top-nav-list .active').hasClass('js-show-schedule')) {
									var viewType = hash.get('view');
									switch(viewType) {
										case 'day':
											var filterType = hash.get('filter');
											if(filterType === 'room') {
												$('#cal_filter_room').trigger('click');
											} else {
												$('#cal_filter_instructor').trigger('click');
											}
											break;
										case 'week':
											$('#cal_view_week').trigger('click');
											break;
										default:
											break;
									}
								}
								// Render the new time info
								$('.studio-hour').html(timeStr);
								self.hideModal();
							}
						} else {
							notification.autoHideError(result.message);
						}
						$btn.attr('disabled', false);
					}
				});
			}
		},
		/*
		* Hide the Modal and clean the data in the Modal
		*/
		hide: function(e) {
			

			var self = this,
			EL = self.conf.EL,
			formEL = self.modal.formEL;
			
			EL.$modal.hide();
			EL.$mask.hide();
			/**
			 * Clean date and time selected
			 */
		/*	self.cleanDate();
			self.cleanTime();*/
			
			
			
		
			/*
			var self = this,
				EL = self.conf.EL,
				formEL = self.modal.formEL;
			
			//!!!Make a deep copy in order to avoid effect of data changing on prototype model
			self.hours_model.set('list', $.extend(true, [], self.temp_hours_model));
			
			EL.$modal.hide();
			EL.$mask.hide();
			EL.$modal.find('.btn-group .active').removeClass('active');
			formEL.unavaliable.el.iCheck('uncheck');
			formEL.selectDate.el.val('');
			formEL.timeTo.set('');
			formEL.timeFrom.set('');
			formEL.onetimeTo.set('');
			formEL.onetimeFrom.set('');
			this.groupIndex = 0;
			
		*/},
		hideModal: function() {

			var self = this,
				EL = self.conf.EL,
				formEL = self.modal.formEL;
			
			//!!!Make a deep copy in order to avoid effect of data changing on prototype model
			self.hours_model.set('list', $.extend(true, [], self.temp_hours_model));
			
			EL.$modal.hide();
			EL.$mask.hide();
			EL.$modal.find('.btn-group .active').removeClass('active');
			formEL.unavaliable.el.iCheck('uncheck');
			formEL.selectDate.el.val('');
			formEL.timeTo.set('');
			formEL.timeFrom.set('');
			formEL.onetimeTo.set('');
			formEL.onetimeFrom.set('');
			this.groupIndex = 0;
		}
	}
	return Edithour;
});