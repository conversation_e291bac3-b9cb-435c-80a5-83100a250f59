define(
['jquery', 'moment', 'mustache', 'hash'], 
function($, moment, Mustache, hash){
	function Calendar(opts) {
		// The default set
		this.defaults = {
			url_room: 'calendar/loadByRoom.htm',
			url_instructor: 'calendar/loadOnDefault.htm',
			url_week: 'calendar/loadByWeek.htm',
			url_month: 'calendar/loadByMonth.htm',

			url_customer_day: 'calendar/loadAppointmentByCustomer.htm',
			url_customer_week: 'calendar/loadAppointmentsByCustomerOnWeek.htm',
			url_customer_month: 'calendar/loadAppointmentsByCustomerOnMonth.htm',

			EL: {
				$cal_wrap: $('#cal_content'),
				$cal_header: $('.cal-header'),
				$cal_title: $('.cal-title'),
				$cal_table: $('.cal-table'),
				$timeline: $('.timeline'),
			}
		}

		// Calendar default view
		this.VIEW = 'day';
		
		this.init(opts);
	}
	Calendar.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			this.conf = $.extend({}, this.defaults, opts);
			this.scroll();
		},
		/*
		* Calendar scrolling, the title and timeline fixed
		*/
		scroll: function() {
			var self = this,
				EL = self.conf.EL,
				$cal_title = EL.$cal_title,
		        $cal_table = EL.$cal_table,
		        $timeline = EL.$timeline,
		        offset = 50;

			//GSSP-334 Changes
		
			// Update the availability hours
			$(document).on('click', '.btn-update-availability', function(e) {
				self.edithour_modal.update(e);
			});
			
			$(document).on('click', '.js-close-availability-modal', function() {
				self.edithour_modal.hideModal();
			});
			
			
			
		    // FIXME
		    // Scrolling will blink on window XP
			
			
	 	
	   
			EL.$cal_wrap.scroll(function(){
			    var $this = $(this),
			    	scrollTop = $this.scrollTop(),
			        scrollLeft = $this.scrollLeft();
			    if(scrollTop > 0){
			        $cal_table.css({
			            top: offset
			        });
			        $timeline.css({
			            top: offset 
			        });
			        $cal_title.css({
			            position: 'absolute',
			            top: scrollTop,
			        });
			    } 
			    if(scrollLeft > 0){
			        $timeline.css({
			            left: scrollLeft,
			        });
			    } 
			    if(scrollLeft === 0) {
			        $timeline.css({
			            left: 0
			        });
			    }
			    if(scrollTop === 0) {
			        $timeline.css({
			            top: 0
			        });
			        $cal_table.css({
			            top: 0
			        });
			        $cal_title.css({
			            position: 'static'
			        });
			    }
			});
		},
		/*
		* Set the calendar view
		*/
		setView: function(view) {
			var self = this;
			self.VIEW = view;
		},
		/*
		* Render month view
		* Render in the backend
		* @param {String} result the html response
		*/
		generateMonthView: function(result) {
			var self = this,
				EL = self.conf.EL,
				$cal_header = EL.$cal_header,
				$cal_wrap = EL.$cal_wrap,
				$cal_title = EL.$cal_title,
				$cal_table = EL.$cal_table;

			$('.calendar-month').remove();	
			$cal_header.find('#cal_view_month').addClass('active').siblings().removeClass('active');	
			$cal_header.find('.type-switch').hide();
			$cal_title.hide();
			$cal_wrap.children('.row').hide();
			$cal_wrap.append(result);
			//For GCSS-498,render the 'Red' today cell by client side instead of server side
			var today = moment(new Date()).format('MM/DD/YYYY');
			$('.calendar-month').find('.js-to-day[data-date="'+today+'"]').parent().addClass('active selected')
		},
		/*
		* Render Week view 
		* @param {Object} result the json response
		*/
		generateWeekView: function(result) {
			var self = this,
				conf = self.conf,
				startTime = 6,
				endTime = 44,
				EL = self.conf.EL,
				$cal_wrap = EL.$cal_wrap,
				$cal_header = EL.$cal_header,
				$cal_title = EL.$cal_title,
				$cal_table = EL.$cal_table,
				$timeline = EL.$timeline,
				$table_wrap = $('<table class="calendar"><thead></thead><tbody></tbody'),
				timeArr = [],
				THEAD = '',
				TD = '',
				TR = '',
				date, weekList;

			date = moment(hash.get('date'));
			weekList = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

			// Empty the old calendar
			$cal_table.empty().append($table_wrap);
			$cal_header.find('#cal_view_week').addClass('active').siblings().removeClass('active');
			$cal_header.find('.type-switch').hide();
			$('.cal-item').remove();
			$('.calendar-month').remove();

			// Generate table td 
			for(var i = 0; i < weekList.length; i++) {
				TD += '<td data-weekday="'+i+'"></td>';
			}
			// Set th content & width
			$.each(weekList, function(index, obj) {
				THEAD += '<th>'+weekList[index]+'</th>';
			});

			// Generate tr
			for(var i = startTime; i < endTime; i++) {
				TR += '<tr>'+ TD +'</tr>';
				if(i < 12) {
					timeArr.push(i +' am');
				}
				else if(i === 12) {
					timeArr.push('Noon');
				} 
				else {
					timeArr.push((i-12) + ' pm');
				}
			}

			// Generate timeline
			self.generateTimeline(startTime, endTime, timeArr);

			// Appent thead & tbody & timeline to table
			$cal_title.html('<tr><th></th>' + THEAD + '</tr>').show();
			$cal_table.find('tbody').html(TR);
			$cal_table.find('tbody tr').each(function(index, obj) {
				// Set avaliable cell to click & store time,rooms or appointments info
				$(obj).find('td').addClass('available').data('time', index);
			});
			$cal_wrap.children('.row').show();
		},
		/*
		* Render Day view 
		* @param {Object} result the json response
		*/
		generateDayView: function(result) {
			var self = this,
				conf = self.conf,
				startTime = 6,
				endTime = 44,
				EL = self.conf.EL,
				$cal_wrap = EL.$cal_wrap,
				$cal_header = EL.$cal_header,
				$cal_title = EL.$cal_title,
				$cal_table = EL.$cal_table,
				$timeline = EL.$timeline,
				hash_view = hash.get('view'),
				hash_filter = hash.get('filter'),
				filter,
				view = self.VIEW,
				$table_wrap = $('<table class="calendar"><thead></thead><tbody></tbody'),
				timeArr = [],
				THEAD = '',
				TD = '',
				TR = '',
				instructors = result.instructorList || [],
				rooms = result.roomList || [];

			/**
			 * For GCSS-565,make the instructor view as the default view
			 */
			if(filter === undefined) filter = hash_filter || 'instructor';

			if(view === undefined) view = hash_view;

			// Empty the old calendar
			$cal_table.empty().append($table_wrap);
			$cal_header.find('#cal_view_day').addClass('active').siblings().removeClass('active');
			$cal_header.find('.type-switch').show();
			$('.calendar-month').remove();

			if(filter === 'room') {
				$('#cal_filter_room').addClass('active').siblings().removeClass('active');

				// Generate table td 
				for(var i = 0; i < rooms.length; i++) {
					TD += '<td data-roomid="'+rooms[i].roomId+'"></td>';
				}

				// Set th content & width
				// When < 6 th, set the width auto
				if(rooms.length < 6) {
					$.each(rooms, function(index, obj) {
						THEAD += Mustache.render('<th id="room_{{roomId}}" class="room-title" style="width: auto;">{{profileRoomName}}</th>', obj);
					});
				} else if(rooms.length === 6) {
					$.each(rooms, function(index, obj) {
						THEAD += Mustache.render('<th id="room_{{roomId}}" class="room-title" style="width: 107px;">{{profileRoomName}}</th>', obj);
					});
				} else {
					$.each(rooms, function(index, obj) {
						THEAD += Mustache.render('<th id="room_{{roomId}}" class="room-title">{{profileRoomName}}</th>', obj);	
					});
				}
			} else {
				$('#cal_filter_instructor').addClass('active').siblings().removeClass('active');

				// Generate table td 
				for(var i = 0; i < instructors.length; i++) {
					TD += '<td data-instructorid="'+instructors[i].instructorId+'"></td>';
				}

				// Set th content & width
				if(instructors.length < 6) {
					$.each(instructors, function(index, obj) {
						THEAD += Mustache.render('<th id="instructor_{{instructorId}}" class="instructor-title" style="width: auto;">{{person.firstName}} {{person.lastName}}</th>', obj);
					});
				} 
				else if(instructors.length === 6) {
					$.each(instructors, function(index, obj) {
						THEAD += Mustache.render('<th id="instructor_{{instructorId}}" class="instructor-title" style="width: 107px;">{{person.firstName}} {{person.lastName}}</th>', obj);
					});
				} else {
					$.each(instructors, function(index, obj) {
						THEAD += Mustache.render('<th id="instructor_{{instructorId}}" class="instructor-title">{{person.firstName}} {{person.lastName}}</th>', obj);	
					});
				}
			}

			// Generate table tr
			for(var i = startTime; i < endTime; i++) {
				TR += '<tr>'+ TD +'</tr>';
				if(i < 12) {
					timeArr.push(i +' am');
				}
				else if(i === 12) {
					timeArr.push('Noon');
				} 
				else {
					timeArr.push((i-12) + ' pm');
				}
			}

			// Generate timeline
			self.generateTimeline(startTime, endTime, timeArr);
		
			$cal_title.html('<tr><th></th>' + THEAD + '</tr>').show();
			$cal_table.find('tbody').append(TR);
			$cal_table.find('tbody tr').each(function(index, obj) {
				// Set avaliable cell to click & store time,rooms or appointments info
				$(obj).find('td').addClass('available').data('time', index);
			});
			$cal_wrap.children('.row').show();
		},
		/*
		* Generate a timeline at left of the calendar 
		* @param {Int} startTime the startTime of timeline start
		* @param {Int} endTime the endTime of timeline end
		* @param {Array} timeArr the text of timeline
		*/
		generateTimeline: function(startTime, endTime, timeArr) {
			var self = this,
				timepoint = '',
				$timeline = self.conf.EL.$timeline;
			for(var i = 0; i < 19; i++) {
				timepoint += '<span class="timepoint"><p>' + timeArr[i] + '</p></span>';
			}
			$timeline.html(timepoint);
		},
		/*
		* Show the day view trigger the getCalendar function
		* @param {String} date the calendar's startDate
		* @param {Object} params the parameters get form left filter bar
		*/
		dayView: function(date, params) {
			var self = this;
			self.setView('day');
			//The code after '||' is for GCSS-393
			self.getCalendar(date || moment().format('L'), params);
			self.showDate(date);	
		},
		weekView: function(date, params) {
			var self = this;
			self.setView('week');
			//The code after '||' is for GCSS-393
			self.getCalendar(date || moment().format('L'), params);
			self.showDate(date);
		},
		monthView: function(date, params) {
			var self = this;
			self.setView('month');
			//The code after '||' is for GCSS-393
			self.getCalendar(date || moment().format('L'), params);
			self.showDate(date);
		},
		/*
		* Previous day or week or month
		* @param {String} date the calendar's startDate
		* @param {Object} params the parameters get form left filter bar
		*/
		prev: function(date, params) {
			var self = this,
				view = self.VIEW,
				nowDate;
			switch(view) {
				case 'day':
					nowDate = moment(date).subtract('days', 1).format('L');
					break;
				case 'week':
					nowDate = moment(date).subtract('weeks', 1).format('L');
					break;
				case 'month':
					nowDate = moment(date).subtract('months', 1).format('L');
					break;
			}
			self.getCalendar(nowDate, params);
			self.showDate(nowDate);
		},
		/*
		* Next day or week or month
		* @param {String} date the calendar's startDate
		* @param {Object} params the parameters get form left filter bar
		*/
		next: function(date, params) {
			var self = this,
				view = self.VIEW,
				nowDate;
			switch(view) {
				case 'day':
					nowDate = moment(date).add('days', 1).format('L');
					break;
				case 'week':
					nowDate = moment(date).add('weeks', 1).format('L');
					break;
				case 'month':
					nowDate = moment(date).add('months', 1).format('L');
					break;
			}
			self.getCalendar(nowDate, params);
			self.showDate(nowDate);	
		},
		/*
		* Show the calendar of today
		* @param {Object} params the parameters get form left filter bar
		*/
		gotoToday: function(params) {
			var self = this,
				date = moment().format('L');
			self.setView('day');
			self.getCalendar(date, params);
			self.showDate(date);
		},
		/*
		* Show the calendar include the appointment
		* @param {String} date the start date
		* @param {Object} params the parameters get form left filter bar
		* @param {String} filterType the day view filter 'room' or 'instructor'
		*/
		getCalendar: function(date, params, filterType) {
			var self = this,
				conf = self.conf,
				EL = conf.EL,
				$cal_wrap = EL.$cal_wrap,
				startDate = hash.get('date') || date,
				hash_view = hash.get('view'),
				hash_filter = hash.get('filter'),
				hash_customer = hash.get('customer'),
				url,
				filterType,
				view = self.VIEW;

			/**
			 * For GCSS-565,make the instructor view as the default view
			 */
			if(filterType === undefined) filterType = hash_filter || 'instructor';
			
			if(view === undefined) view = hash_view;
			if(hash_customer !== undefined) $.extend(params, {customer: hash_customer});
			switch(view) {
				case 'day':
					var data = $.extend(params, {date: date});
					// If it's a customer calendar view
					// Change the default url
					if(hash_customer !== undefined) {
						
						//For GCSS-482
						if(filterType === 'room' || filterType === '') {
							data = $.extend(data, {type: 'room'});
						} else {
							data = $.extend(data, {type: 'instructor'});
						}
						
						url = conf.url_customer_day;
					} else {
						if(filterType === 'room') {
							url = conf.url_room;
						} else {
							url = conf.url_instructor;
						}
					}
					// Store the data in the hash
					hash.add({
						date: date,
						view: view,
						filter: filterType
					});
					$.ajax({
						url: url,
						type: 'GET',
						data: data,
						success: function(result) {
							// If session time out will get the login.html page
							// So, if response text has the string 'DOCTYPE' will reload page to jump to the login page
							if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
							// Remove all  old appiontment
                            $('.cal-item').remove();
                            // Append the calendar
							self.generateDayView(result);
							
							//For GCSS-482Append the mask to cover the disabeld cell
							self.generateDisabledArea(result);
							
							var	appointments = result.appointmentList;
							
							/**
							 * The unselected appointment list,for gcss-525
							 */
							var unselectedAppointments = result.unselectedAppointments;
							
							var instructorList = result.instructorList;
							
							var roomList = result.roomList;
							
							// Append the appointments,it's a callback from appointment
							conf.handler(instructorList, roomList, unselectedAppointments, appointments, view, $cal_wrap, filterType);
						}
					});
					break;
				case 'week':
					var endDate = moment(date).add('weeks', 1).format('L'),
						data = $.extend(params, {date: date + '-' + endDate});
					hash.add({
						date: date,
						endDate: endDate,
						view: view
						//filter: filterType
					});
					// If it's a customer calendar view
					// Change the default url
					if(hash_customer !== undefined) {
						url = conf.url_customer_week;
					} else {
						url = conf.url_week;
					}
					$.ajax({
						url: url,
						type: 'GET',
						data: data,
						success: function(result) {
							// If session time out will get the login.html page
							// So, if response text has the string 'DOCTYPE' will reload page to jump to the login page
							if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
							$('.cal-item').remove();
							self.generateWeekView();
							
							//For GCSS-482
							self.generateDisabledArea(result);
							
							conf.handler([], [], [], result.resultMap, view, $cal_wrap, filterType);
						}
					});
					break;
				case 'month':
					var endDate = moment(date).add('months', 1).format('L'),
						data = $.extend(params, {date: date + '-' + endDate});
					hash.add({
						date: date,
						view: view
//						filter: filterType
					});
					// If it's a customer calendar view
					// Change the default url
					if(hash_customer !== undefined) {
						url = conf.url_customer_month;
					} else {
						url = conf.url_month;
					}
					$.ajax({
						url: url,
						type: 'GET',
						data: data,
						success: function(result) {
							// If session time out will get the login.html page
							// So, if response text has the string 'DOCTYPE' will reload page to jump to the login page
							if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
							$('.cal-item').remove();
							self.generateMonthView(result);
							$('.month-item p[data-date="'+ date +'"]').parent().addClass('selected');
						}
					});
					break;
			}
		},
		/*
		* Show the current date on the leftbar
		* @param {String} date
		*/
		showDate: function(date) {
			var self = this,
				formatDate,
				view = self.VIEW;
			switch(view) {
				case 'day':
					formatDate = moment(date).format('dddd, MMMM D, YYYY');
					break;
				case 'week':
					formatDate = moment(date).format('dddd, MMMM D, YYYY');
					break;
				case 'month':
					formatDate = moment(date).format('MMMM YYYY');
					break;
			}
			$('.js-cal-date').text(formatDate);
		},
		/**
		 *  For GCSS-482,generate the disabled mask to cover the calendar cell, which can not be clicked to create appointment.
		 *  @param {Object} Ajax response result
		 */
		generateDisabledArea: function(result) {
			var self = this,
				$cal_title = self.conf.EL.$cal_title,
				offset_left = $cal_title.find('th').eq(0).outerWidth(),
				width_th = $cal_title.find('th').eq(1).outerWidth(),
				maskHTML = '<div class="disabled-cell"></div>',
				startHour,
				startMinute,
				top,
				left;
			
			//Execute the code only when at least one more room was selected
			if(result.roomList && result.roomList.length) {
				if(result.roomViewUnavailableHourDTOs) {
					_.forEach(result.roomViewUnavailableHourDTOs, function(item) {
						renderRoomMask(item, result.roomList.length);
					});
				}
			}
			//Execute the code only when at least one more instructor was selected
			if(result.instructorList && result.instructorList.length) {
				if(result.instructorViewUnavailableHourDTOs) {
					_.forEach(result.instructorViewUnavailableHourDTOs, function(item) {
						renderInstructorMask(item, result.instructorList);
					});
				}
			}
			// Execute the code when week view			
			if(result.weekViewUnavailableHourDTOs && result.weekViewUnavailableHourDTOs.length) {
				_.forEach(result.weekViewUnavailableHourDTOs, function(item, index) {
					renderWeekMask(item, index);
				});
			}
			
			 
			function renderWeekMask(item, index) {
				_.forEach(item.list, function(subItem) {
					var $mask = $(maskHTML);
					
					startHour = subItem.startTime.split(':')[0],
					startMinute = subItem.startTime.split(':')[1],
					top = (startHour*1 - 5) * 60 + startMinute*1, //based on startTime ex. here it is 7 changed for LES-637
					left = width_th * index;
					
					$mask.css({
						top: top,
						left: left,
						height: subItem.duration,
						width: width_th
					}).appendTo(self.conf.EL.$cal_table.find('.calendar'));
				});
			}
			
			function renderRoomMask(item, len) {
				var $mask = $(maskHTML);
				
				startHour = item.startTime.split(':')[0],
				startMinute = item.startTime.split(':')[1],
				top = (startHour*1 - 5) * 60 + startMinute*1;//based on startTime ex. here it is 7 changed for LES-637
				
				$mask.css({
					top: top,
					height: item.duration,
					width: width_th * len
				}).appendTo(self.conf.EL.$cal_table.find('.calendar'));
			}
			
			function renderInstructorMask(item, instructorList) {
				var order = $cal_title.find('th').index($('#instructor_' + item.instructorId));
					
				left = width_th * (order - 1);
				_.forEach(item.list, function(subItem) {
					var $mask = $(maskHTML);
					
					startHour = subItem.startTime.split(':')[0],
					startMinute = subItem.startTime.split(':')[1],
					top = (startHour*1 - 5) * 60 + startMinute*1; //based on startTime ex. here it is 7 changed for LES-637
					
					var $length = instructorList.length;
					if($length > 3 && $length < 6 && (item.instructorId != instructorList[0].instructorId)) {
						left = left*1 + 2;
					}
				
					$mask.css({
						top: top,
						left: left,
						height: subItem.duration,
						width: width_th
					}).appendTo(self.conf.EL.$cal_table.find('.calendar'));
				});
			}
		}
	}
	return Calendar;
});