define([
	'jquery', 
	'moment',
	'mustache'
], 
function($, moment, Mustache) {
	function Appointment(opts) {
		// <PERSON><PERSON><PERSON> set
		this.defaults = {
		}

		this.init(opts);
	}

	Appointment.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			this.conf = $.extend({}, this.defaults, opts);
		},	
		/*
		* Traversal the appointment, and append to the calendar
		* @param {Object} appointments the result from the ajax request
		* @param {String} viwe the view of current calendar
		* @param {Object} wrapper the wrapper of appointment here is $('#cal_content')
		* @param {String} filter the filterType of dayview 'room' or 'instructor'
		*/
		showAll: function(instructorList, roomList, grayedoutAppointments, selectedAppointments, view, wrapper, filter) {
			var self = this,
				startTime = 6;
			switch(view) {
				case 'day':
					// Traversal the appointments & set the position
					if(selectedAppointments === null || selectedAppointments.length === 0) { $('.cal-item').remove();}
					
					var $cal_title = $('.cal-title th');
					$.each(selectedAppointments, function(index, obj) {
						var appointment_customerName = "";
						$.each(obj.customers,function(index,customer){
							//Added the Validation of null value on firstName or lastName,GCSS-499
							var customerName = (customer.person.firstName === null ? '' : customer.person.firstName) + " " + (customer.person.lastName === null ? '' : customer.person.lastName);
							if(index < obj.customers.length-1){
								appointment_customerName += customerName+"("+customer.lessonCounts+")";
							}else{
								appointment_customerName += customerName+"("+customer.lessonCounts+")";
							}
						});
						
						/**
						 * For gcss-567,add activity type in the blue appointment area of the calendar view - under student/client name.
						 */
						appointment_customerName += '<br/>' + (obj.activity.activityName || '');
						//Changes made for 15 minute change-GSSP 254
						if(obj.activity.activityId === 120 || obj.activity.activityId=== 140 || obj.activity.activityId=== 320)//Changes made for GSSP-279
						{	
							appointment_customerName = obj.activity.activityName;
							
							if(obj.activity.activityId=== 140 || obj.activity.activityId === 320){
								bgColor = '#90EE90'; //The Green color
								border = '1px solid #90EE90';
							}
						}	
						
						var left, top, width, height,
							date = moment(obj.startTimeStr),
							width_th = $cal_title.eq(1).outerWidth(),
							height_th = $cal_title.eq(1).outerHeight(),
							offset_left = $cal_title.eq(0).outerWidth(),
							hour = date.hours(),
							mini = date.minutes();

						/**
						 * For gcss-574,add different color to identify single appointment and recurring appointment
						 */
						var bgColor = '#339900'; //The green color
						var border = '1px solid #1E8643';
						
									
						if(obj.appointmentSeries.isRecurring.toString() === 'Y') {
							bgColor = '#4987c4'; //The blue color
							border = '1px solid #4782bb';
							
							if(obj.canceled === 'H'){
								bgColor = '#103961'; //The Navy blue color
								border = '1px solid #091e33';
							}
							
							//Changes made for adding orange color for online recurring appointment
							if(obj.activity.service.serviceId == '20'){
								bgColor = '#ff7f00'; //The Orange color
								border = '1px solid #da463b';
								
								if(obj.canceled === 'H'){
									bgColor = '#103961'; //The Navy blue color
									border = '1px solid #091e33';
								}
								
							}
							//Rajkumar check undefined
							if(obj.customers[0] !== undefined) {
								if(obj.customers[0].lessonCounts == '0'){
									bgColor = '#FFCA33'; //The Yellow color
									border = '1px solid #e6e600';
								}
								if(obj.activity.activityId=== 120 || obj.activity.activityId === 320){
									bgColor = '#90EE90'; //The Yellow color
									border = '1px solid #90EE90';
								}
							}
						}	
						
						//Changes made for adding purple color for online single appointment 
						if(obj.appointmentSeries.isRecurring.toString() === 'N' && obj.activity.service.serviceId == '20') {
							bgColor = '#ba55d3'; //The Purple color
							border = '1px solid #da463b';
							
							if(obj.canceled === 'H'){
								bgColor = '#103961'; //The Navy blue color
								border = '1px solid #091e33';
							}
							if(obj.customers[0] !== undefined) {
							if(obj.customers[0].lessonCounts == '0'){
								bgColor = '#FFCA33'; //The Yellow color
								border = '1px solid #e6e600';
							}
							if(obj.activity.activityId=== 140 || obj.activity.activityId === 320){
								bgColor = '#90EE90'; //The Green color
								border = '1px solid #90EE90';
							}
							}
						}else if(obj.appointmentSeries.isRecurring.toString() === 'N'){
							bgColor = '#339900'; //The green color
							border = '1px solid #1E8643';
							
							if(obj.canceled === 'H'){
								bgColor = '#103961'; //The Navy blue color
								border = '1px solid #091e33';
							}
							if(obj.customers[0] !== undefined) {
							if(obj.customers[0].lessonCounts == '0'){
								bgColor = '#FFCA33'; //The Yellow color
								border = '1px solid #e6e600';
							}
							if(obj.activity.activityId=== 140 || obj.activity.activityId === 320){
								bgColor = '#90EE90'; //The Green color
								border = '1px solid #90EE90';
							}
							}
							
						}
												
						//end of changes made for adding purple color for online single appointment 
						//249
						if(obj.isUpdatedByCustomer===true)
						{
							bgColor = '#643b5b'; //The Yellow color
							border = '1px solid #704266';
							
							if(obj.customers[0] !== undefined) {
							if(obj.customers[0].lessonCounts == '0'){
								bgColor = '#FFCA33'; //The Yellow color
								border = '1px solid #e6e600';
							}
							}
						}
						
						//Changes maded for 15 minute change--GSSP 254
						if(obj.activity.activityId ===20 || obj.activity.activityId=== 120 || obj.activity.activityId=== 684 || obj.activity.activityId === 360 || obj.activity.activityId === 400 || obj.activity.activityId === 401) {
							bgColor = '#d20105'; //The Red color
							border = '1px solid #da463b';
							if(obj.customers[0] !== undefined) {
							if(obj.customers[0].lessonCounts == '0'){
								bgColor = '#FFCA33'; //The Yellow color
								border = '1px solid #e6e600';
							}
							 
							if(obj.activity.activityId=== 140 || obj.activity.activityId === 320){
								bgColor = '#90EE90'; //The Green color
								border = '1px solid #90EE90';
							}
							}
							
						}	
						// Set the appointment position
						// Set the filter as room when filter is empty string
						if(filter === 'room' || filter === '') {
							var order = $cal_title.index($('#room_' + obj.room.roomId));
							left = offset_left + width_th * (order - 1);
						} else {
							// NOTICE
							// Some apponiment has no instructor
							// Set the appointment left -100% to hide this
							var instructor = obj.instructor;
							if(instructor) {
								var order = $cal_title.index($('#instructor_' + obj.instructor.instructorId));
								left = offset_left + width_th * (order - 1);
							} else {
								left = '-100%';
							}
						}
						// Top: the table_header plus the first blank cell height plus the offsetTop plus the minite
						top = height_th + 60 + (hour - startTime) * 60 + mini;
						
						/**
						 * For GCSS-621
						 * Remove the 5px to make appointment fill the cell of view completely.
						 */
						width = width_th;
						height = obj.duration;
						
						if(instructorList) {
							var $length = instructorList.length;
							if(obj.instructor) {
								if($length > 3 && $length < 6 && (obj.instructor.instructorId != instructorList[0].instructorId)) {
									if(obj.instructor.instructorId === instructorList[1].instructorId) {
										left = left*1 + 1;
									} else {
										left = left*1 + 2;
									}
								}
							}
						} else if(roomList) {
							var $length = roomList.length;
							if($length > 3 && $length < 6 && (obj.room.roomId != roomList[0].roomId)) {
								if(obj.room.roomId === roomList[1].roomId) {
									left = left*1 + 1;
								} else {
									left = left*1 + 2;
								}
							}
						}

						var cal_item = $('<div class="cal-item"><p></p></div>'),
							text = '<div style="height:'+height+'px">'+appointment_customerName+'</div>';
						
						cal_item.data('appointmentid', obj.appointmentId)
							.css({
							background: bgColor,
							border: border,
							position: 'abusolute',
							top: top,
							left: left,
							width: width,
							height: height
						}).find('p').append(text);
						wrapper.append(cal_item);
					});
					
					/**
					 * For gcss-525,gray out the appointment list that not fit the filter
					 */
					if(grayedoutAppointments === undefined || grayedoutAppointments === null || grayedoutAppointments.length === 0) { $('.cal-item-disabled').remove(); return;}
					
					$.each(grayedoutAppointments, function(index, obj) {
						
						var left, top, width, height,
							date = moment(obj.startTimeStr),
							width_th = $cal_title.eq(1).outerWidth(),
							height_th = $cal_title.eq(1).outerHeight(),
							offset_left = $cal_title.eq(0).outerWidth(),
							hour = date.hours(),
							mini = date.minutes();

						//Set the appointment position.Set the filter as room when filter is empty string
						if(filter === 'room' || filter === '') {
							var order = $cal_title.index($('#room_' + obj.room.roomId));
							left = offset_left + width_th * (order - 1);
						} else {
							// NOTICE:Some apponiment has no instructor.Set the appointment left -100% to hide this
							var instructor = obj.instructor;
							if(instructor) {
								var order = $cal_title.index($('#instructor_' + obj.instructor.instructorId));
								left = offset_left + width_th * (order - 1);
							} else {
								left = '-100%';
							}
						}
						// Top: the table_header plus the first blank cell height plus the offsetTop plus the minite
						top = height_th + 60 + (hour - startTime) * 60 + mini;
						width = width_th;
						height = obj.duration;
						
						if(instructorList) {
							var $length = instructorList.length;
							if(obj.instructor) {
								if($length > 3 && $length < 6 && (obj.instructor.instructorId != instructorList[0].instructorId)) {
									if(obj.instructor.instructorId === instructorList[1].instructorId) {
										left = left*1 + 1;
									} else {
										left = left*1 + 2;
									}
								}
							}
						} else if(roomList) {
							var $length = roomList.length;
							if($length > 3 && $length < 6 && (obj.room.roomId != roomList[0].roomId)) {
								if(obj.room.roomId === roomList[1].roomId) {
									left = left*1 + 1;
								} else {
									left = left*1 + 2;
								}
							}
						}

						var cal_item = $('<div class="cal-item-disabled"></div>');
						
						cal_item.css({
							position: 'abusolute',
							top: top,
							left: left,
							width: width,
							height: height
						});
						wrapper.append(cal_item);
					});
					break;
				case 'week':
					// Traversal the response
					// appointments is a Object contains sunday to monday data
					for(var weekday in selectedAppointments) {
						// Timeline is a Object cotains 7:00 to 22:00 data
						var timeline = selectedAppointments[weekday];
						for(var t in timeline) {
							// applist is a Array, has the appointment in the timeline
							var applist = timeline[t];
							// Set the appointment position
							for(var i = 0; i < applist.length; i++) {
								var app = applist[i];
								// Top: timepoint minus timeStart plus the header height plus the startMinite
								var top = (t*1 - startTime) * 60 + 110 + moment(app.startTime).format('mm')*1,
									left, width, height,
									width_th = 95,
									height_th = 50;
//		
//								width = width_th / applist.length;
//								height = app.duration;
//								left = 50 + i * width + weekday * 95 + 2;
							//gssp-154 changes	
							var count = 0;
							for(var j = 0; j < applist.length; j++) {
							 var app1 = applist[j];
							 if((app1.startTime > app.startTime || app1.startTime == app.startTime) && app1.startTime < app.endTime
							   || app1.endTime > app.startTime && (app1.endTime < app.endTime || app1.endTime == app.endTime)
							   || (app.startTime > app1.startTime || app.startTime == app1.startTime) && (app.endTime < app1.endTime || app.endTime == app1.endTime)) count = count + 1;
							}

							if (count > 1) {
							 width = width_th / applist.length;
							 left = 50 + i * width + weekday * 95 + 2;
							}
							else {
							 width = width_th;
							 left = 50 + weekday * 95 + 2;
							}
							
							//var temp = app.customers[0].find(a => a.lessonCounts === "0");
							//width = width_th / applist.length;
							height = app.duration;
							//left = 50 + i * width + weekday * 95 + 2;

								var appointment_customerName = "";
								
								/**
								 * For gcss-574,add different color to identify single appointment and recurring appointment
								 */
								var bgColor = '#339900'; //The green color
								var border = '1px solid #1E8643';
								
											
								if(app.appointmentSeries.isRecurring.toString() === 'Y') {
									bgColor = '#4987c4'; //The blue color
									border = '1px solid #4782bb';
									
									if(app.canceled === 'H'){
										bgColor = '#103961'; //The Navy blue color
										border = '1px solid #091e33';
									}
									
									//Changes made for adding orange color for online recurring appointment
									if(app.activity.service.serviceId == '20'){
										bgColor = '#ff7f00'; //The Orange color
										border = '1px solid #da463b';
										
										if(app.canceled === 'H'){
											bgColor = '#103961'; //The Navy blue color
											border = '1px solid #091e33';
											
											
											
										}
										
										}  
									if(app.customers.lessonCounts == '0'){
										bgColor = '#FFCA33'; //The Yellow color
										border = '1px solid #e6e600';
										
									}
								}	
								
								//Changes made for adding purple color for online single appointment 
								if(app.appointmentSeries.isRecurring.toString() === 'N' && app.activity.service.serviceId == '20') {
									bgColor = '#ba55d3'; //The Purple color
									border = '1px solid #da463b';
									
									if(app.canceled === 'H'){
										bgColor = '#103961'; //The Navy blue color
										border = '1px solid #091e33';
									
									}
									
									if(app.customers.lessonCounts == '0'){
										bgColor = '#FFCA33'; //The Yellow color
										}
									
								}
								
								else if(app.appointmentSeries.isRecurring.toString() === 'N'){
									bgColor = '#339900'; //The green color
									border = '1px solid #1E8643';
									
									if(app.canceled === 'H'){
										bgColor = '#103961'; //The Navy blue color
										border = '1px solid #091e33';
														
									}
									 
									if(app.customers.lessonCounts == '0'){
										bgColor = '#FFCA33'; //The Yellow color
										border = '1px solid #e6e600';
								
									}
									
								}
														
								//end of changes made for adding purple color for online single appointment 
								//249
								if(app.isUpdatedByCustomer===true)
								{
									bgColor = '#643b5b'; //The Yellow color
									border = '1px solid #704266';
									if(app.customers.lessonCounts == '0'){
										bgColor = '#FFCA33'; //The Yellow color
										border = '1px solid #e6e600';
									}
								}
								
								//Changes maded for 15 minute change--GSSP 254
								if(app.activity.activityId ===20 || app.activity.activityId=== 120 || app.activity.activityId=== 684 || app.activity.activityId === 360 || app.activity.activityId === 400 || app.activity.activityId === 401) {
									bgColor = '#d20105'; //The Red color
									border = '1px solid #da463b';
									if(app.customers.lessonCounts == '0'){
										bgColor = '#FFCA33'; //The Yellow color
										border = '1px solid #e6e600';
									}
									if(app.activity.activityId=== 140 || app.activity.activityId === 320){
										bgColor = '#90EE90'; //The Green color
										border = '1px solid #90EE90';
									}
									
								}
								
								$.each(app.customers,function(index,customer){
									//Added the Validation of null value on firstName or lastName,GCSS-499
									var customerName = (customer.person.firstName === null ? '' : customer.person.firstName) + " " + (customer.person.lastName === null ? '' : customer.person.lastName);
									if(index < app.customers.length-1){
										appointment_customerName += customerName+"("+customer.lessonCounts+")"+",";
										if(customer.lessonCounts == '0'){
										bgColor = '#FFCA33'; //The Yellow color
										border = '1px solid #e6e600';
										
									}
										
									}else{
										appointment_customerName += customerName+"("+customer.lessonCounts+")";
										if(customer.lessonCounts == '0'){
											bgColor = '#FFCA33'; //The Yellow color
											border = '1px solid #e6e600';
											
										}
									}
								});
								
								/**
								 * For gcss-567,add activity type in the blue appointment area of the calendar view - under student/client name.
								 */
								appointment_customerName += '<br/>' + (app.activity.activityName || '');

								//Changes made for 15 minute change-GSSP 254							
								if(app.activity.activityId=== 120 || app.activity.activityId=== 140 || app.activity.activityId=== 320)//Changes made for GSSP-279
								{	
									appointment_customerName = app.activity.activityName;
									//var bgColor = '#90EE90';
								}
								if(app.activity.activityId=== 140 || app.activity.activityId === 320){
									bgColor = '#90EE90'; //The Green color
									border = '1px solid #90EE90';
								}
								var cal_item = $('<div class="cal-item week"></div>'),
									text = Mustache.render('<p><div style="height:'+height+'px">'+appointment_customerName+'</div></p>', app);

								cal_item.data('appointmentid', app.appointmentId)
									.css({
									background: bgColor,
									border: border,
									top: top,
									left: left,
									width: width,
									height: height
								}).append(text);
								wrapper.append(cal_item);
							}
						}
					}
					
//					/**
//					 * Grayout the cells when single instructor is selected
//					 */
//					// Traversal the response
//					// appointments is a Object contains data from sunday to monday
//					for(var weekday in grayedoutAppointments) {
//						// Timeline is an Object cotains data from 9:00 to 22:00
//						var timeline = grayedoutAppointments[weekday];
//						for(var t in timeline) {
//							// applist is a Array, has the appointment in the timeline
//							var applist = timeline[t];
//							// Set the appointment position
//							for(var i = 0; i < applist.length; i++) {
//								var app = applist[i];
//								// Top: timepoint minus timeStart plus the header height plus the startMinite
//								var top = (t*1 - startTime) * 60 + 110 + moment(app.startTime).format('mm')*1,
//									left, width, height,
//									width_th = 95,
//									height_th = 50;
//
//								width = width_th / applist.length;
//								height = app.duration;
//								left = 50 + i * width + weekday * 95 + 2;
//								
//								var cal_item = $('<div class="cal-item-disabled"></div>');
//
//								cal_item.data('appointmentid', app.appointmentId).css({
//									top: top,
//									left: left,
//									width: width,
//									height: height
//								});
//								wrapper.append(cal_item);
//							}
//						}
//					}
					break;
				case 'month':
					break;
			}
		}
	}
	return Appointment;
});