define([
	'jquery', 
	'select2', 
	'icheck', 
	'bootbox',
	'mustache',
	'underscore', 
	'backbone',
	'notification'
], function($, select2, iCheck, bootbox, Mustache, _, Backbone, notification) {
	function Room(opts) {
		// Default set
		this.defaults = {
			url_fetch_roomTemplate: 'roomTemplate/loadRoomTemplate.htm',

			url_change_roomTemplate: 'room/selectRoomTemplate.htm',

			url_fetch_services: 'roomTemplate/loadRoomTemplateServices.htm',

			// url_fetch_activities: 'room/loadRoomActivities.htm',

			url_create_room_template: 'roomTemplate/createTemplateRoom.htm',

			url_fetch_room: 'room/loadRoom.htm',

			url_fetch_detail_room: 'room/getEditRoom.htm',

			url_delete_room: 'room/deleteRoom.htm',
			
			url_update_room: 'room/editRoom.htm',

			url_create_room: 'room/createRoom.htm',

			url_fetch_unselected_service: 'room/loadRoomServices.htm',

			url_fetch_unselected_activity: 'room/loadRoomActivities.htm',

			formTarget: $('.js-room-form'),

			EL: $('.room-list')
		}	

		this.init(opts);

		this.is_edit = false;

		this.is_changed = is_changed;
	}

	Room.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			var self = this;
			self.conf = $.extend(self.defaults, opts);
		},
		/*
		* Generate the add room form
		*/
		newRoomForm: function() {
			var self = this,
				CONF = self.conf,
				NewRoom_Model;

			// Define the form model
			NewRoom_Model = Backbone.Model.extend({
				initialize: function() {
					var _this = this,
						formEL = this.formEL;
					formEL.roomTemplate.select2().on('change', function(e) {
						var value = $(e.currentTarget).val();
						// When roomtemplate chaned
						// Freeze the related select
						formEL.roomNumber.select2('readonly', true);
						formEL.roomSize.select2('readonly', true);
						formEL.servicesList.select2('readonly', true);
						formEL.activityList.select2('readonly', true);
						// Request the roomtemplate relate items
						$.ajax({
							url: CONF.url_change_roomTemplate,
							type: 'GET',
							data: {
								roomTemplateId: value
							},
							success: function(result) {
								// Clear prev data
								CONF.formTarget.find('.js-service-list').empty();
								CONF.formTarget.find('.js-activity-list').empty();
								formEL.services.val('');
								formEL.activities.val('');

								var canSplit = result.canSplit,
									isShowRoomSize = result.isShowRoomSize,
									roomNumberList = result.roomNumberList,
									roomTemplate = result.roomTemplate,
									roomSizeList = result.roomSizeList,
									activityList = result.activityList,
									serviceList = result.serviceList,
									selectedServiceList = result.selectedServiceList,
									selectedActivityList = result.selectedActivityList;

								// Set the related data
								formEL.roomNumber.select2({
									data: roomNumberList,
									id: function(obj) {
										return obj.roomNumberId
									},
									formatResult: function(obj) {
										return obj.roomNumber;
									},
									formatSelection: function(obj) {
										return obj.roomNumber;
									}
								}).select2('readonly', false);

								formEL.roomSize.select2({
									data: roomSizeList,
									id: function(obj) {
										return obj.roomSizeId
									},
									formatResult: function(obj) {
										return obj.roomSizeName;
									},
									formatSelection: function(obj) {
										return obj.roomSizeName;
									}
								}).select2('data', result.roomTemplate.roomSize).select2('readonly', false);

								formEL.servicesList.select2({
									placeholder: 'Select Available Services',
									data: serviceList,
									id: function(obj) {
										return obj.serviceId
									},
									formatResult: function(obj) {
										return obj.serviceName;
									},
									formatSelection: function(obj) {
										return obj.serviceName;
									}
								}).select2('readonly', false);
								var services = [];
								CONF.formTarget.find('.js-service-list').empty();
								formEL.services.val('');
								selectedServiceList.forEach(function(obj) {
									var tmpl = $('#tmpl_service_item').html();
									CONF.formTarget.find('.js-service-list').append(Mustache.render(tmpl, obj));
									services.push(obj.serviceId);
								});
								formEL.services.val(services.toString());
								formEL.activityList.select2({
									placeholder: 'Select Available Activities',
									data: activityList,
									id: function(obj) {
										return obj.activityId
									},
									formatResult: function(obj) {
										return obj.activityName;
									},
									formatSelection: function(obj) {
										return obj.activityName;
									}
								}).select2('readonly', false);
								var activities = [];
								CONF.formTarget.find('.js-activity-list').empty();
								formEL.activities.val('');
								selectedActivityList.forEach(function(obj) {
									var tmpl = $('#tmpl_activity_item').html();
									CONF.formTarget.find('.js-activity-list').append(Mustache.render(tmpl, obj));
									activities.push(obj.activityId);
								});
								formEL.activities.val(activities.toString());
								if(roomTemplate.isSplitRoom == 'Y') {
									formEL.isSplitRoom.iCheck('enable').iCheck('check');
								} else {
									formEL.isSplitRoom.iCheck('enable').iCheck('uncheck');
								}
								if(!canSplit) {
									formEL.isSplitRoom.attr('disabled', true).iCheck('disable');
									CONF.formTarget.find('.js-split-room-wrap').hide();
								} else {
									formEL.isSplitRoom.attr('disabled', false).iCheck('enable');
									CONF.formTarget.find('.js-split-room-wrap').show();
								}
								if(isShowRoomSize) {
									formEL.roomSize.attr('disabled', false).select2('enable', true);
									CONF.formTarget.find('.js-room-size-wrap').show();
								} else {
									formEL.roomSize.attr('disabled', true).select2('enable', false);
									CONF.formTarget.find('.js-room-size-wrap').hide();
								}
							}
						});
					});
					
					// Default form set
					formEL.roomNumber.select2({data: []})
					formEL.roomSize.select2({data: []});
					formEL.isSplitRoom.iCheck();
					formEL.servicesList.select2({
						placeholder: 'Select Available Services',
						data: [] 
					});
					formEL.activityList.select2({
						placeholder: 'Select Available Activities',
						data: []
					});
					formEL.enabled.iCheck();

					_.bindAll(this, 'addService','removeService', 'addActivity', 'removeActivity');

					// Add service button function
					CONF.formTarget.find('.js-add-service-item').on('click', function() {
						_this.addService();
					});

					// Remove service button function
					CONF.formTarget.find('.js-service-list').on('click', '.js-remove-service', function(e) {
						$(this).parent().fadeOut(function() {
							$(this).remove();
						});
						_this.removeService($(this).data('id'));
					});

					// Add activity button funciton
					CONF.formTarget.find('.js-add-activity-item').on('click', function() {
						_this.addActivity();
					});

					// Remove activity button function
					CONF.formTarget.find('.js-activity-list').on('click', '.js-remove-activity', function(e) {
						$(this).parent().fadeOut(function() {
							$(this).remove();
						});
						_this.removeActivity($(this).data('id'));
					});

				},
				/*
				* Service changed, Ajax request to get related data
				*/
				changeService: function() {
					var _this = this,
						formEL = _this.formEL;
					formEL.servicesList.select2('readonly', true);
					$.ajax({
						url: CONF.url_fetch_unselected_service,
						data: {
							roomTemplateId: formEL.roomTemplate.select2('val'),
							serviceString: formEL.services.val()
						},
						success: function(result) {
							formEL.servicesList.select2('val', '').select2({
								placeholder: 'Select Available Services',
								data: result.serviceList,
								id: function(obj) {
									return obj.serviceId;
								},
								formatResult: function(obj) {
									return obj.serviceName;
								},
								formatSelection: function(obj) {
									return obj.serviceName;
								}
							}).select2('val', '').select2('readonly', false);
							_this.changeActivity();
						}
					});
				},
				/*
				* Activity changed, Ajax request to get related data
				*/
				changeActivity: function() {
					var formEL = this.formEL;
					formEL.activityList.select2('readonly', true);
					$.ajax({
						url: CONF.url_fetch_unselected_activity,
						data: {
							roomTemplateId: formEL.roomTemplate.select2('val'),
							activityString: formEL.activities.val(),
							serviceString: formEL.services.val()
						},
						success: function(result) {
							formEL.activityList.select2('val', '').select2({
								placeholder: 'Select Available Activities',
								data: result.activityList,
								id: function(obj) {
									return obj.activityId;
								},
								formatResult: function(obj) {
									return obj.activityName;
								},
								formatSelection: function(obj) {
									return obj.activityName;
								}
							}).select2('val', '').select2('readonly', false);
							CONF.formTarget.find('.js-activity-list').empty();
							formEL.activities.val('');
							var tmpl = $('#tmpl_activity_item').html(),
								activityList = [];
								activityHTML = '';
							result.selectedActivityList.forEach(function(obj) {
								activityHTML += Mustache.render(tmpl, obj);
								activityList.push(obj.activityId);
								CONF.formTarget.find('.js-activity-list').html(activityHTML);
							});
							formEL.activities.val(activityList.toString());
						}
					});
				},
				/*
				* Add service function
				* !Notice Operate the String as param not Object
				*/
				addService: function() {
					var formEL = this.formEL,
						tmpl = $('#tmpl_service_item').html(),
						data = formEL.servicesList.select2('data');

					if(data == null || data.length == 0) return;
					CONF.formTarget.find('.js-service-list').append(Mustache.render(tmpl, data));
					var val = formEL.services.val().split(',');
					val.push(data.serviceId);
					if(val[0] == '') val.shift();
					formEL.services.val(val.toString());

					this.changeService();
				},
				/*
				* Remove service funciton
				* !Notice Operate the String as param not Object
				*/
				removeService: function(id) {
					var formEL = this.formEL,
						val = formEL.services.val().split(',');
						currentVal = _.filter(val, function(num) {
						return num != id;
					});
					formEL.services.val(currentVal.toString());
					this.changeService();
				},
				/*
				* Add activity function
				* !Notice Operate the String as param not Object
				*/
				addActivity: function() {
					var formEL = this.formEL,
						tmpl = $('#tmpl_activity_item').html(),
						data = formEL.activityList.select2('data');

					if(data == null || data.length == 0) return;
					CONF.formTarget.find('.js-activity-list').append(Mustache.render(tmpl, data));
					var val = formEL.activities.val().split(',');
					val.push(data.activityId);
					if(val[0] == '') val.shift();
					formEL.activities.val(val.toString());
					this.changeActivity();
				},
				/*
				* Remove activity function
				* !Notice Operate the String as param not Object
				*/
				removeActivity: function(id) {
					var formEL = this.formEL,
						val = formEL.activities.val().split(',');
						currentVal = _.filter(val, function(num) {
						return num != id;
					});
					formEL.activities.val(currentVal.toString());
					this.changeActivity();
				},
				/*
				* Clear the form data, when form closed
				*/
				clear: function() {
					var formEL = this.formEL;
					formEL.roomTemplate.select2('val', '');
					formEL.roomNumber.select2('val', '').select2({
						data: []
					}).select2('readonly', false);;
					formEL.roomSize.attr('disabeld', false).select2('val', '').select2('readonly', false);
					CONF.formTarget.find('.js-room-size-wrap').show();
					formEL.servicesList.select2('val', '').select2({
						placeholder: 'Select Available Services',
						data: []
					}).select2('readonly', false);;
					formEL.activityList.select2('val', '').select2({
						placeholder: 'Select Available Activities',
						data: []
					}).select2('readonly', false);;
					formEL.services.val('');
					formEL.activities.val('');
					formEL.roomName.val('');
					CONF.formTarget.find('.js-service-list').empty();
					CONF.formTarget.find('.js-activity-list').empty();
					formEL.isSplitRoom.attr('disabled', false).iCheck('enable').iCheck('uncheck');
					CONF.formTarget.find('.js-split-room-wrap').show();
					formEL.enabled.iCheck('check');
				},
				defaults: {

				},
				formEL: {
					'roomTemplate': CONF.formTarget.find('select[name="roomTemplate"]'),
					'roomNumber': CONF.formTarget.find('input[name="roomNumber"]'),
					'roomName':CONF.formTarget.find('input[name="profileRoomName"]'),
					'roomSize': CONF.formTarget.find('input[name="roomSize"]'),
					'isSplitRoom':CONF.formTarget.find('input[name="isSplitRoom"]'),
					'services': CONF.formTarget.find('input[name="services"]'),
					'servicesList': CONF.formTarget.find('input[name="servicesList"]'),
					'activities': CONF.formTarget.find('input[name="activities"]'),
					'activityList': CONF.formTarget.find('input[name="activityList"]'),
					'enabled': CONF.formTarget.find('input[name="enabled"]')
				}
			});

			self.newroom = new NewRoom_Model;
			return self;	
		},
		/*
		* Slidedown to show the form 
		*/
		showForm: function() {
			var CONF = this.conf;
			CONF.formTarget.parent().slideDown();
		},
		/*
		* Slideup to hide the form
		*/
		hideForm: function() {
			var CONF = this.conf;
			CONF.formTarget.parent().slideUp();
			$('.js-show-room-form').removeClass('active');
			CONF.formTarget.find('.form-msg').hide();
			this.newroom.clear();
		},
		/*
		* Render room list
		*/
		renderList: function() {
			var self = this,
				CONF = self.conf,
				Room_Model, Room_View, Room_Collection;

			// Init the is_change status 
			self.is_changed = false;
			is_changed = self.is_changed;

			// Show loadings
			self.showLoadTip();
			
			// Define room model
			Room_Model = Backbone.Model.extend({
				methodUrl: {
					'read': CONF.url_fetch_model,
					'create': CONF.url_create_room,
					'update': CONF.url_update_room,
					'delete': CONF.url_delete_room
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				},
				defaults: {
				}
			});

			// Define room view
			Room_View = Backbone.View.extend({
				initialize: function() {
					return this;
				},
				events: {
					'click .js-edit-room': 'edit',
					'click .js-update-room': 'update',
					'click .js-delete-room': 'delete',
					'click .close-edit-panel': 'close',
					'click .js-add-service': 'addService',
					'click .js-remove-service': 'removeService',
					'click .js-add-activity': 'addActivity',
					'click .js-remove-activity': 'removeActivity',
					'change input[name="roomTemplate"]': 'ChangeTemplate',
					'ifChanged input[name="enabled"]': 'changeActive',
					'ifChanged input[name="isSplitRoom"]': 'changeSplit'
				},
				tagName: 'table',
				className: 'table table-list table-fixed',
				template: $('#tmpl_room').html(),
				render: function() {
					this.$el.html(Mustache.render(this.template, this.model.toJSON()));
					return this;
				},
				edit: function(e) {
					var _this = this,
						$this = $(e.currentTarget);

					if($this.hasClass('editing')) return;

					if(self.is_changed) {
						bootbox.dialog({
							title: 'Warning!',
							message: 'You have unsaved information. Do you want to continue?',
							buttons: {
								success: {
									label: 'Continue',
									className: 'btn btn-important',
									callback: function() {
										$('.room-list .editing').removeClass('editing');
										$('.room-list tr.disabled').removeClass('disabled').nextAll().hide();
										self.hideWarn();
										_this.getOne();
									}
								},
								cancle: {
									label: 'Cancel',
									className: 'btn btn-primary',
									callback: function() {
										self.showWarn();
										return;
									}
								}
							}
						});
					} else {
						$('.room-list .editing').removeClass('editing');
						$('.room-list tr.disabled').removeClass('disabled').nextAll().hide();
						$this.addClass('editing');
						_this.getOne();		
					}
				},
				/*
				* Ajax request to get the detail room 
				*/
				getOne: function() {
					var _this = this;
					$.ajax({
						url: CONF.url_fetch_detail_room,
						type: 'GET',
						data: {
							roomId: _this.model.get('roomId')
						},
						success: function(result) {
							if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
							// TODO
							_this.model.set(result.room);
							_this.editRender(result.roomDetail, result.canSplit, result.isShowRoomSize);
							_this.$el.find('tr').eq(0).addClass('disabled').nextAll().show();
							
							self.is_changed = false;
							is_changed = self.is_changed;
							
							return _this;
						}
					});
				},
				/*
				* Open the edit panel, render the form filed
				* @param {Object} room detail data
				*/
				editRender: function(roomDetail, canSplit, isShowRoomSize) {
					var _this = this,
						tmpl_service_item = $('#tmpl_service_item').html(),
						tmpl_activity_item = $('#tmpl_activity_item').html();

					this.$el.find('input[name="roomTemplate"]').select2({
						data: roomDetail.roomTemplate,
						id: function(obj) {
							return obj.roomTemplateId;
						},
						formatResult: function(obj) {
							return obj.roomTemplateName;
						},
						formatSelection: function(obj) {
							return obj.roomTemplateName;
						}
					}).select2('val', this.model.get('roomTemplate')).on('select2-selecting', function(e) {
						_this.model.set('roomTemplate', e.val);
					});

					this.$el.find('input[name="roomNumber"]').select2({
						data: roomDetail.roomNumber,
						id: function(obj) {
							return obj.roomNumberId;
						},
						formatResult: function(obj) {
							return obj.roomNumber;
						},
						formatSelection: function(obj) {
							return obj.roomNumber;
						}
					}).select2('val', this.model.get('roomNumber')).on('select2-selecting', function(e) {
						
						self.is_changed = true;
						is_changed = self.is_changed;
						
						_this.model.set('roomNumber', e.val);
					});

					//If the roomSize list is empty,hide the roomsize
					if(roomDetail.roomSize.length > 0) {
						this.$el.find('input[name="roomSize"]').select2({
							placeholder: 'Choose Room Size',
							data: roomDetail.roomSize,
							id: function(obj) {
								return obj.roomSizeId;
							},
							formatResult: function(obj) {
								return obj.roomSizeName;
							},
							formatSelection: function(obj) {
								return obj.roomSizeName;
							}
						}).select2('val', this.model.get('roomSize')).on('select2-selecting', function(e) {
							
							self.is_changed = true;
							is_changed = self.is_changed;
							
							_this.model.set('roomSize', e.val);
						});
					} else {
						_this.$el.find('.js-room-size-wrap').hide();
						_this.model.set('roomSize', null)
					}

					this.$el.find('input[name="servicesList"]').select2({
						data: roomDetail.unSelectedServices,
						id: function(obj) {
							return obj.serviceId;
						},
						formatResult: function(obj) {
							return obj.serviceName;
						},
						formatSelection: function(obj) {
							return obj.serviceName;
						} 	
					});

					this.$el.find('.js-service-list').empty();
					_.each(this.model.get('serviceList'), function(obj) {
						_this.$el.find('.js-service-list').append(Mustache.render(tmpl_service_item, obj));
					});

					this.$el.find('input[name="activityList"]').select2({
						data: roomDetail.unSelectedActivities,
						id: function(obj) {
							return obj.activityId;
						},
						formatResult: function(obj) {
							return obj.activityName;
						},
						formatSelection: function(obj) {
							return obj.activityName;
						} 	
					});

					this.$el.find('.js-activity-list').empty();
					_.each(this.model.get('activityList'), function(obj) {
						_this.$el.find('.js-activity-list').append(Mustache.render(tmpl_activity_item, obj));
					});

					this.$el.find('input[name="profileRoomName"]').val(this.model.get('profileRoomName'))
						.on('change', function() {
							
							self.is_changed = true;
							is_changed = self.is_changed;
							
							_this.model.set('profileRoomName', this.value);
						});

					if(this.model.get('enabled')) {
						this.$el.find('input[name="enabled"]').iCheck().iCheck('uncheck');
					} else {
						this.$el.find('input[name="enabled"]').iCheck().iCheck('check');
					}
					
					if(this.model.get('isSplitRoom')) {
						this.$el.find('input[name="isSplitRoom"]').iCheck().iCheck('determinate');
					} else {
						this.$el.find('input[name="isSplitRoom"]').iCheck().iCheck('indeterminate');
					}
					if(!canSplit) {
						this.$el.find('.js-split-room-wrap').hide();
					}
					if(!isShowRoomSize) {
						this.$el.find('.js-room-size-wrap').hide();
					}
										
					this.$el.find('.js-edit-room').addClass('editing');
				},
				/*
				* Render the eidt panel form, when room template has changed
				*/
				ChangeTemplate: function(e) {
					
					//bug:If roomTemplate is changed,show a warning dialog
					self.is_changed = true;
					is_changed = self.is_changed;
					
					var _this = this,
						value = $(e.currentTarget).val();
					$.ajax({
						url: CONF.url_change_roomTemplate,
						data: {
							roomTemplateId: value
						},
						success: function(result) {
							var roomNumberList = result.roomNumberList,
								roomTemplate = result.roomTemplate,
								roomSizeList = result.roomSizeList,
								activityList = result.activityList,
								serviceList = result.serviceList,
								selectedServiceList = result.selectedServiceList,
								selectedActivityList = result.selectedActivityList,
								canSplit = result.canSplit,
								isShowRoomSize = result.isShowRoomSize;
							
							console.log(_this.model.attributes)
							if(_this.model.get('parentRoom')) {
								_this.$el.find('.js-split-room-wrap').hide();
								_this.$el.find('.js-room-size-wrap').hide();
								_this.model.unset('isSplitRoom');
								_this.model.set('roomSize', null);
							} else {
								//For GCSS-376
								console.log(canSplit)
								if(!canSplit) {
									_this.$el.find('.js-split-room-wrap').hide();
									_this.model.unset('isSplitRoom');
								} else {
									_this.$el.find('.js-split-room-wrap').show();
									//Check if need to mark the 'splite' checkbox as 'checked' if there is no parent room of current room
									if(roomTemplate.isSplitRoom === 'Y') {
										_this.$el.find('input[name="isSplitRoom"]').iCheck('check');
									} else {
										_this.$el.find('input[name="isSplitRoom"]').iCheck('uncheck');
									}
								}
								if(!isShowRoomSize) {
									_this.$el.find('.js-room-size-wrap').hide();
									_this.model.set('roomSize', null);
								} else {
									_this.$el.find('.js-room-size-wrap').show();
									_this.$el.find('input[name="roomSize"]').select2({
										placeholder: 'Choose Room Size',
										data: roomSizeList,
										id: function(obj) {
											return obj.roomSizeId;
										},
										formatResult: function(obj) {
											return obj.roomSizeName;
										},
										formatSelection: function(obj) {
											return obj.roomSizeName;
										}
									}).select2('val', roomTemplate.roomSize.roomSizeId).select2('enable', true);//set the roomSize value to the dropdown list
									_this.model.set('roomSize', roomTemplate.roomSize.roomSizeId);// set the roomSize value to the model
								}
								//End of GCSS-376
							}

							_this.$el.find('input[name="servicesList"]').select2('val', '').select2({
								data: serviceList,
								id: function(obj) {
									return obj.serviceId;
								},
								formatResult: function(obj) {
									return obj.serviceName;
								},
								formatSelection: function(obj) {
									return obj.serviceName;
								} 	
							});
							_this.$el.find('.js-service-list').empty();
							_this.model.set('services', '');
							var services = [];
							selectedServiceList.forEach(function(obj) {
								var tmpl = $('#tmpl_service_item').html();
								_this.$el.find('.js-service-list').append(Mustache.render(tmpl, obj));
								services.push(obj.serviceId);
							});
							_this.model.set('services', services.toString());

							_this.$el.find('input[name="activityList"]').select2('val', '').select2({
								data: activityList,
								id: function(obj) {
									return obj.activityId;
								},
								formatResult: function(obj) {
									return obj.activityName;
								},
								formatSelection: function(obj) {
									return obj.activityName;
								} 	
							});
							_this.$el.find('.js-activity-list').empty();
							_this.model.set('activities', '');
							var activities = [];
							selectedActivityList.forEach(function(obj) {
								var tmpl = $('#tmpl_activity_item').html();
								_this.$el.find('.js-activity-list').append(Mustache.render(tmpl, obj));
								activities.push(obj.activityId);
							});
							_this.model.set('activities', activities.toString());
				
							if(roomTemplate.enabled === 'Y') {
								_this.$el.find('input[name="enabled"]').iCheck('uncheck');
							} else {
								_this.$el.find('input[name="enabled"]').iCheck('check');
							}
						}
					});
				},
				/*
				* Change active function
				*/
				changeActive: function(e) {
					var _this = this;
					
					self.is_changed = true;
					is_changed = self.is_changed;
					
					if(e.currentTarget.checked) {
						_this.model.set('enabled', null);	
					} else {
						_this.model.set('enabled', 'on');
					}
				},
				/*
				* Change split room function
				*/
				changeSplit: function(e) {
					
					//bug:Show warning dialog if split checkbox is changed
					self.is_changed = true;
					is_changed = self.is_changed;
					
					var _this = this;
					if(e.currentTarget.checked) {
						_this.model.set('isSplitRoom', 'on');
					} else {
						_this.model.unset('isSplitRoom');
					}
				},
				/*
				* Change service function
				* Ajax request to get the unselected service
				*/
				changeService: function() {
					var _this = this;
					_this.$el.find('input[name="servicesList"]').select2('readonly', true);
					$.ajax({
						url: CONF.url_fetch_unselected_service,
						data: {
							roomTemplateId: _this.$el.find('input[name="roomTemplate"]').select2('val'),
							serviceString: _this.model.get('services')
						},
						success: function(result) {
							_this.$el.find('input[name="servicesList"]').select2('val', '').select2({
								data: result.serviceList,
								id: function(obj) {
									return obj.serviceId;
								},
								formatResult: function(obj) {
									return obj.serviceName;
								},
								formatSelection: function(obj) {
									return obj.serviceName;
								}
							}).select2('readonly', false);
							_this.changeActivity();
						}
					});
				},
				/*
				* Change activity function
				* Ajax request to get the unselected activity
				*/
				changeActivity: function() {
					var _this = this;
					_this.$el.find('input[name="activityList"]').select2('readonly', true);
					$.ajax({
						url: CONF.url_fetch_unselected_activity,
						data: {
							roomTemplateId: _this.$el.find('input[name="roomTemplate"]').select2('val'),
							activityString: _this.model.get('activities'),
							serviceString: _this.model.get('services')
						},
						success: function(result) {
							_this.$el.find('input[name="activityList"]').select2('val', '').select2({
								data: result.activityList,
								id: function(obj) {
									return obj.activityId;
								},
								formatResult: function(obj) {
									return obj.activityName;
								},
								formatSelection: function(obj) {
									return obj.activityName;
								}
							}).select2('readonly', false);

							var js_activity_list = _this.$el.find('.js-activity-list'),
								tmpl = $('#tmpl_activity_item').html(),
								list_height = 0,
								activityList = [],
								activityHTML = '';
							
							result.selectedActivityList.forEach(function(obj) {
								activityHTML += Mustache.render(tmpl, obj);
								activityList.push(obj.activityId);
								list_height += 16;
							});
							
							js_activity_list.css({
								height: list_height
							}).empty()
							_this.model.unset('activities');
							
							js_activity_list.html(activityHTML);
							_this.model.set('activities', activityList.toString());
						}
					});
				},
				/*
				* Add service function
				* !Notice Operate the String as param not Object
				*/
				addService: function() {
					var data = this.$el.find('input[name="servicesList"]').select2('data'),
						services = this.model.get('services').split(','),
						tmpl = $('#tmpl_service_item').html();

					if(data == null || data.length == 0) return;
					this.model.get('serviceList').push(data);
					
					services.push(data.serviceId);
					if(services[0] == '') services.shift();
					this.model.set('services', services.toString());
					
					this.changeService();

					this.$el.find('.js-service-list').append(Mustache.render(tmpl, data));

					self.is_changed = true;
					is_changed = self.is_changed;
				},
				/*
				* Remove service function
				* !Notice Operate the String as param not Object
				*/
				removeService: function(e) {
					var id = $(e.currentTarget).data('id'),
						services = this.model.get('services').split(','),
						currentList = _.filter(this.model.get('serviceList'), function(obj) {
							return obj.serviceId != id;
						});
					
					this.model.set('serviceList', currentList);

					var currentService = _.filter(services, function(num) {
						return num != id;
					});

					this.model.set('services', currentService.toString());

					this.changeService();

					$(e.currentTarget).parent().fadeOut(function() {
						$(this).remove();
					});

					self.is_changed = true;
					is_changed = self.is_changed;
				},
				/*
				* Add activity function
				* !Notice Operate the String as param not Object
				*/
				addActivity: function() {
					var data = this.$el.find('input[name="activityList"]').select2('data'),
						activities = this.model.get('activities').split(','),
						tmpl = $('#tmpl_activity_item').html();

					if(data == null || data.length == 0) return;
					this.model.get('activityList').push(data);
					
					activities.push(data.activityId);
					if(activities[0] == '') activities.shift();
					this.model.set('activities', activities.toString());
					
					this.changeActivity();

					this.$el.find('.js-activity-list').html(Mustache.render(tmpl, data));

					self.is_changed = true;
					is_changed = self.is_changed;
				},
				/*
				* Remove activity function
				* !Notice Operate the String as param not Object
				*/
				removeActivity: function(e) {
					var id = $(e.currentTarget).data('id'),
						activities = this.model.get('activities').split(','),
						currentList = _.filter(this.model.get('activityList'), function(obj) {
							return obj.activityId != id;
						});
					
					this.model.set('activityList', currentList);

					var currentActivity = _.filter(activities, function(num) {
						return num != id;
					});

					this.model.set('activities', currentActivity.toString());

					this.changeActivity();

					$(e.currentTarget).parent().fadeOut(function() {
						$(this).remove();
					});

					self.is_changed = true;
					is_changed = self.is_changed;
				},
				/*
				* Update the room
				*/
				update: function() {
					var _this = this;
					this.$el.find('.js-update-room').attr('disabled', true);//For GCSS-341 to avoid 'double click'
					this.model.sync('update', this.model, {
						success: function(result) {
							if(result.status) {
								_this.model.set(result.dto);
								self.getAll();
								_this.close();
							} else {
								notification.autoHideError(result.message);
							}
							_this.$el.find('.js-update-room').attr('disabled', false);//For GCSS-341 to avoid 'double click'
						}
					});
				},
				/*
				* Delete the room
				*/
				delete: function() {
					var _this = this;
					bootbox.dialog({
						title: 'Warning!',
						message: 'Are you sure to delete this room ?',
						buttons: {
							success: {
								label: 'Delete',
								className: 'btn btn-important',
								callback: function() {
									_this.model.destroy({
										success: function(model, result) {
											$.ajax({
												type: 'GET',
												url: CONF.url_delete_room,
												data: {
													roomId: _this.model.get('roomId')
												},
												success: function(result) {
													if(result.status) {
//														_this.$el.fadeOut(function() {
//															this.remove();
//														});
														self.getAll();
														
														self.is_changed = false;
														is_changed = self.is_changed;
														
													} else {
														notification.autoHideError(result.message);
													}
												}
											});
										}
									});
									self.hideWarn();
								}
							},
							cancle: {
								label: 'Cancel',
								className: 'btn btn-primary',
								callback: function() {
									//If user click on cancel button,don't show warn message for nothing modified
									//self.showWarn();
									return;
								}
							}
						}
					});
				},
				/*
				* Close the edit panel, destory all the select2 or icheck
				*/
				close: function() {
					this.$el.find('.js-edit-room').removeClass('editing');
					
					self.is_changed = false;
					is_changed = self.is_changed;
					
					self.hideWarn();
					this.$el.find('tr').eq(0).removeClass('disabled').nextAll().hide();

					this.$el.find('input[name="roomTemplate"]').select2('destroy').off('select2-selecting');
					this.$el.find('input[name="roomNumber"]').select2('destroy').off('select2-selecting');;
					this.$el.find('input[name="roomSize"]').select2('destroy').off('select2-selecting');;
					this.$el.find('input[name="isSplitRoom"]').iCheck('destroy');
					this.$el.find('input[name="servicesList"]').select2('destroy').off('select2-selecting');;
					this.$el.find('input[name="activityList"]').select2('destroy').off('select2-selecting');;
					this.$el.find('input[name="enabled"]').iCheck('destroy');
					this.$el.find('input[name="profileRoomName"]').val('').off('change');

					return this;
				}
			});
			
			// Define room collection
			Room_Collection = Backbone.Collection.extend({
				model: Room_Model,
				methodUrl: {
					'read': CONF.url_fetch_room,
					'create': '',
					'update': '',
					'delete': ''
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				},
			});

			var room_collection = new Room_Collection;

			self.Model = Room_Model;
			self.View = Room_View;
			self.Collection = Room_Collection;

			self.room_collection = room_collection;

			_.bindAll(self, 'addAll', 'addOne');

			room_collection.on('set', self.addAll);
			room_collection.on('add', self.addOne);

			self.getAll();
		},
		/*
		* Fetch all rooms	
		*/	
		getAll: function() {
			var self = this,
				CONF = self.conf;
			CONF.EL.empty();	
			self.room_collection.reset();
			self.room_collection.fetch();
		},
		/*
		* Add all rooms
		*/
		addAll: function() {
			var self = this;
			self.room_collection.each(self.addOne);
		},
		/*
		* Add one room, init room view
		*/
		addOne: function(model) {
			var self = this,
				EL = self.conf.EL;
			var view = new self.View({model: model});

			EL.append(view.render().el);
		},
		/*
		* Show the warn tip, when user have changed the form filed
		*/
		showWarn: function() {
			var EL = this.conf.EL,
				$target = EL.find('tr.disabled').nextAll().find('.js-update-room'),
				right = 220,
				top = $target.offset().top - $target.height() - $('.warn-tip').height();
			$('.warn-tip').css({
				right: right,
				top: top
			}).show();
		},
		/*
		* Hide the warn tip
		*/
		hideWarn: function() {
			$('.warn-tip').hide();
		},
		/*
		* Show a loading tip
		*/
		showLoadTip: function() {
			var self = this,
				EL = self.conf.EL;
			EL.append('<div class="loader"><p></p></div>');
		},
		/*
		* Submit the form add new room
		*/
		createRoom: function(callback) {
			var self = this,
				data = this.conf.formTarget.serializeArray(),
				room = new self.Model;

			_.each(data, function(obj, index) {
				room.set(obj['name'], obj['value']);
			});
			
//			if(self.conf.formTarget.find('input[name="isSplitRoom"][disabled]').length > 0) {
//				room.set('isSplitRoom', self.conf.formTarget.find('input[name="isSplitRoom"][disabled]').val());
//			}

			// Remove the no needed param
			room.unset('servicesList');
			room.unset('activityList');
			room.sync('create', room, {
				success: function(result) {
					if(result.status) {
						self.hideForm();
						self.getAll();
						
						self.is_changed = false;
						is_changed = self.is_changed;
						
					} else {
						self.conf.formTarget.find('.form-msg').text(result.message).show();
					}
				}
			});
		}	
	}
	return Room;
});