define([
	'jquery',
	'moment',
	'icheck',
	'underscore',
	'backbone', 
	'mustache',
	'hash'
], 
function($, moment, iCheck, _, Backbone, Mustache, hash) {
	function Filter(opts) {
		// Default Set
		this.defaults = {
			url_get_filter_service: 'calendar/generateDynamicFilterFromService.htm',
			url_get_filter_activity: 'calendar/generateDynamicFilterFromActivity.htm',
			url_get_all_filter: 'calendar/findLatestFilter.htm',
			url_get_stored_filter: 'findStoredFilter.htm',

			EL: {
				$filterBar: $('.filter-list'),
				$customerInfo: $('.customer-info')
			}	
		}
		
		/**
		 * For bug 603
		 * Defined cache data to store all the data as base data.
		 */
		this.cache={
			serviceCache: {},
			instructorCache: {},
			roomCache: {},
			activityCache: {}
		};
		
		this.init(opts);
	}
	Filter.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			this.conf = $.extend({}, this.defaults, opts);

			var self = this,
				CONF = self.conf,
				EL = CONF.EL,
				$filterBar = EL.$filterBar,
				Filter_Model, Filter_View;

			// Define filter model
			Filter_Model = Backbone.Model.extend({
				initialize: function() {
					var _this = this,
						formEL = _this.formEL;
					// Service select all function
					formEL.serviceTypeChoose.iCheck();
					formEL.serviceType.iCheck();

					// Instructor select all function
					formEL.instructorChoose.iCheck();
					formEL.instructor.iCheck();

					// Room select all function 
					formEL.roomChoose.iCheck();
					formEL.room.iCheck();
					// AppointmentType select all function
					formEL.appointmentTypeChoose.iCheck();
					formEL.appointmentType.iCheck();
					
					//For bug 603
					var cache = self.cache,
						instructorCache = cache.instructorCache,
						roomCache = cache.roomCache,
						activityCache = cache.activityCache;

					// Filter itme change to sync the filter
					$filterBar.find('.js-serviceType-filter').on('ifClicked', 'input[type=checkbox]', function(e) {
						var $this = $(this),
							checked;
						$this.iCheck('toggle');
						checked = this.checked;
						setTimeout(function(){
							if($this.hasClass('js-filter')) {
								if(checked) {
									$filterBar.find('input[name="serviceType"]').iCheck('check');
								} else {
									$filterBar.find('input[name="serviceType"]').iCheck('uncheck');
								}
							} else {
								if($filterBar.find('.js-serviceType-filter li').not('.main-filter').find('input:not(:checked)').length > 0) {
									formEL.serviceTypeChoose.iCheck('uncheck');
								} else {
									formEL.serviceTypeChoose.iCheck('check');
								}
							}
							self.renderFilter(CONF.url_get_filter_service);
						}, 300);
					});
					$filterBar.find('.js-appointmentType-filter').on('ifClicked', 'input[type=checkbox]', function(e) {
						var $this = $(this),
							checked;
						$this.iCheck('toggle');
						checked = this.checked;
						
						/**
						 * GCSS-603
						 * Marking the activity select status in cache.
						 * If checked, marked as true.
						 */
						var activityId = e.currentTarget.value;
						if(checked) {
							activityCache[activityId] = true;
						} else {
							activityCache[activityId] = false;
						}
						
						setTimeout(function(){
							if($this.hasClass('js-filter')) {
								if(checked) {
									
									/**
									 * GCSS-603
									 * While parent filter is checked, all the child data set will be marked as true.
									 */
									$filterBar.find('input[name="appointmentType"]').each(function() {
										var activityId = $(this).val();
										activityCache[activityId] = true;
									});
									
									$filterBar.find('input[name="appointmentType"]').iCheck('check');
								} else {
									/**
									 * GCSS-603
									 * While parent filter is unchecked, all the child data set will be marked as false.
									 */
									$filterBar.find('input[name="appointmentType"]').each(function() {
										var activityId = $(this).val();
										activityCache[activityId] = false;
									});
									
									$filterBar.find('input[name="appointmentType"]').iCheck('uncheck');
								}
							} else {
								if($filterBar.find('.js-appointmentType-filter li').not('.main-filter').find('input:not(:checked)').length > 0) {
									formEL.appointmentTypeChoose.iCheck('uncheck');
								} else {
									formEL.appointmentTypeChoose.iCheck('check');
								}
							}
							self.renderFilter(CONF.url_get_filter_activity);
						}, 300);
					});
					$filterBar.find('.js-instructor-filter').on('ifClicked', 'input[type=checkbox]', function(e) {
						var $this = $(this),
							checked;
						$this.iCheck('toggle');
						checked = this.checked;
						
						/**
						 * GCSS-603
						 * Marking the instructor select status in cache.
						 * If checked, marked as true.
						 */
						var instructorId = $(this).val();
						if(checked) {
							instructorCache[instructorId] = true;
						} else {
							instructorCache[instructorId] = false;
						}
						
						setTimeout(function(){
							if($this.hasClass('js-filter')) {
								if(checked) {
									/**
									 * GCSS-603
									 * While parent filter is checked, all the child data set will be marked as true.
									 */
									$filterBar.find('input[name="instructor"]').each(function() {
										instructorCache[$(this).val()] = true;
									});
									
									$filterBar.find('input[name="instructor"]').iCheck('check');
								} else {
									/**
									 * GCSS-603
									 * While parent filter is unchecked, all the child data set will be marked as false.
									 */
									$filterBar.find('input[name="instructor"]').each(function() {
										instructorCache[$(this).val()] = false;
									});
									
									$filterBar.find('input[name="instructor"]').iCheck('uncheck');
								}
							} else {
								if($filterBar.find('.js-instructor-filter li').not('.main-filter').find('input:not(:checked)').length > 0) {
									formEL.instructorChoose.iCheck('uncheck');
								} else {
									formEL.instructorChoose.iCheck('check');
								}
							}
							self.renderCalendar();
						}, 300);
					});
					$filterBar.find('.js-room-filter').on('ifClicked', 'input[type=checkbox]', function(e) {
						var $this = $(this),
							checked;
						$this.iCheck('toggle');
						checked = this.checked;
						
						/**
						 * GCSS-603
						 * Marking the room select status in cache.
						 * If checked, marked as true.
						 */
						var roomId = e.currentTarget.value;
						if(checked) {
							roomCache[roomId] = true;
						} else {
							roomCache[roomId] = false;
						}
						
						setTimeout(function(){
							if($this.hasClass('js-filter')) {
								if(checked) {
									/**
									 * GCSS-603
									 * While parent filter is checked, all the child data set will be marked as true.
									 */
									$filterBar.find('input[name="room"]').each(function() {
										var roomId = $(this).val();
										roomCache[roomId] = true;
									});
									
									$filterBar.find('input[name="room"]').iCheck('check');
								} else {
									/**
									 * GCSS-603
									 * While parent filter is unchecked, all the child data set will be marked as false.
									 */
									$filterBar.find('input[name="room"]').each(function() {
										var roomId = $(this).val();
										roomCache[roomId] = false;
									});
									
									$filterBar.find('input[name="room"]').iCheck('uncheck');
								}
							} else {
								if($filterBar.find('.js-room-filter li').not('.main-filter').find('input:not(:checked)').length > 0) {
									formEL.roomChoose.iCheck('uncheck');
								} else {
									formEL.roomChoose.iCheck('check');
								}
							}
							self.renderCalendar();
						}, 300);
					});

					$filterBar.show();
				},
				defaults: {

				},
				formEL: {
					'serviceTypeChoose': $filterBar.find('input[name="servie-choose"]'),
					'serviceType': $filterBar.find('input[name="serviceType"]'),
					'instructorChoose': $filterBar.find('input[name="instructor-choose"]'),
					'instructor': $filterBar.find('input[name="instructor"]'),
					'roomChoose': $filterBar.find('input[name="room-choose"]'),
					'room': $filterBar.find('input[name="room"]'),
					'appointmentTypeChoose': $filterBar.find('input[name="appointment-choose"]'),
					'appointmentType': $filterBar.find('input[name="appointmentType"]')
				},
				/*
				* Return all selected serviceType
				*/
				getSeriveType: function() {
					var arr = [];
					$filterBar.find('input[name="serviceType"]:checked').each(function() {
						arr.push(this.value);
					});
					return arr.toString();
				},
				/*
				* Return all selected Instructor
				*/
				getInstructor: function() {
					var arr = [];
					$filterBar.find('input[name="instructor"]:checked').each(function() {
						arr.push(this.value);
					});
					return arr.toString();
				},
				/*
				* Return all selected Room
				*/
				getRoom: function() {
					var arr = [];
					$filterBar.find('input[name="room"]:checked').each(function() {
						arr.push(this.value);
					});
					return arr.toString();
				},
				/*
				* Return all selected AppointmentType
				*/
				getAppointmentType: function() {
					var arr = [];
					$filterBar.find('input[name="appointmentType"]:checked').each(function() {
						arr.push(this.value);
					});
					return arr.toString();
				},
				
				/*
				 * For gcss-525,get the unselected service type
                 * Return the id array of service type which are not selected
                 */
                getUnSelectedServiceType: function() {
                     var arr = [];
                     $filterBar.find( 'input[name="serviceType"]:not(:checked)' ).each(function() {
                    	 arr.push( this.value);
                     });
                     return arr.toString();
                },
                /*
                 * For gcss-525,get the unselected activity type
                 * Return the id array of activity type which are not selected
                 */
                getUnSelectedActivityType: function() {
                	var arr = [];
                    $filterBar.find( 'input[name="appointmentType"]:not(:checked)' ).each(function() {
                    	arr.push( this.value);
                    });
                    return arr.toString();
                },
                /*
                 * For gcss-525,get the unselected room
                 * Return the id array of room type which are not selected
                 */
                getUnselectedRoom: function() {
                	var arr = [];
                	$filterBar.find( 'input[name="room"]:not(:checked)' ).each(function() {
                		arr.push( this.value);
                	});
                	return arr.toString();
                },
                /*
                 * For gcss-525,get the unselected instructor
                 * Return the id array of instructor type which are not selected
                 */
                getUnselectedInstructor: function() {
                	var arr = [];
                	$filterBar.find( 'input[name="instructor"]:not(:checked)' ).each(function() {
                		arr.push( this.value);
                	});
                	return arr.toString();
                },
                /**
                 * GCSS-603
                 * Return all the unselected activities in cache. 
                 */
                getUnSelectedActivityFromCache: function() {
                	var cache = self.cache,
                		activityCache = cache.activityCache,
                		arr = [];
                	$.each(activityCache, function(key, value) {
                		if(!value) arr.push(key);
                	});
                	return arr.toString();
                }
			});
			self.filter_model = new Filter_Model;
			
		},
		/*
		* Init the filter, render a new filter
		*/
		initFilter: function(viewType, filterType) {
			var self = this,
				CONF = self.conf,
				$filterBar = CONF.EL.$filterBar;
			
			$.ajax({
				//url: CONF.url_get_all_filter,
				url: CONF.url_get_stored_filter,
				data: self.getFilter(),
				success: function(result) {
					// If session time out will get the login.html page
					// So, if response text has the string 'DOCTYPE' will reload page to jump to the login page
					if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();

					var serviceTypeHTML = '',
						instructorHTML = '',
						activityHTML = '',
						roomHTML = '';

					var cache = self.cache,
						serviceCache = cache.serviceCache,
						instructorCache = cache.instructorCache,
						roomCache = cache.roomCache,
						activityCache = cache.activityCache;
					
					// Render the serviceType
					var serviceCount = 0;
					result.serviceList.forEach(function(obj) {
						
						/**
						 * GCSS-603
						 * Initial service,Instructor,Activity,Room
						 * 1.If current service is not exist in cache, adding a new one to current cache.
						 * 2.If current service exits, marking status to true which means it's default to select.
						 */
						var key = obj.serviceId;
						if(undefined === serviceCache[key]) {
							serviceCache[key] = true;
						}
						
						if(obj.isSelectedService === 'Y') {
							serviceCount += 1;
							serviceTypeHTML += '<li><input name="serviceType" checked type="checkbox" data-name="'+obj.serviceName+'" value="'+obj.serviceId+'">'+obj.serviceName+'</li>';
						} else {
							serviceTypeHTML += '<li><input name="serviceType" type="checkbox" data-name="'+obj.serviceName+'" value="'+obj.serviceId+'">'+obj.serviceName+'</li>';
						}
					});
					/**
					 * For gcss-575,select the 'Services Types' checkbox only when all services are selected
					 */
					if(serviceCount === result.serviceList.length) {
						$filterBar.find('input[name="servie-choose"]').iCheck('check');
					}
					$filterBar.find('.js-serviceType-filter li').first()
						.nextAll().remove()
						.end().after(serviceTypeHTML)
						.end().nextAll().iCheck();
					
					// Render the instructor
					var instructorCount = 0;
					result.instructorList.forEach(function(obj) {
						
						var key = obj.instructorId;
						if(undefined === instructorCache[key]) {
							instructorCache[key] = true;
						}
						
						if(obj.isSelectedInstructor === 'Y') {
							instructorCount += 1;
							instructorHTML += '<li><input name="instructor" checked type="checkbox" data-name="'+obj.instructorName+'" value="'+obj.instructorId+'">'+obj.instructorName+'</li>';
						} else {
							instructorHTML += '<li><input name="instructor" type="checkbox" data-name="'+obj.instructorName+'" value="'+obj.instructorId+'">'+obj.instructorName+'</li>';
						}
					});
					/**
					 * For gcss-575,select the 'Instructor' checkbox only when all instructors are selected
					 */
					if(instructorCount === result.instructorList.length) {
						$filterBar.find('input[name="instructor-choose"]').iCheck('check');
					}
					$filterBar.find('.js-instructor-filter li').first()
						.nextAll().remove()
						.end().after(instructorHTML)
						.end().nextAll().iCheck();
					
					// Render the appointmentType
					if(result.activityList) {
						var activityCount = 0;
						result.activityList.forEach(function(obj) {
							
							var key = obj.activityId;
							if(undefined === activityCache[key]) {
								activityCache[key] = true;
							}
							
							if(obj.isSelectedActivity === 'Y') {
								activityCount += 1;
								activityHTML += '<li><input name="appointmentType" checked type="checkbox" data-name="'+obj.activityName+'" value="'+obj.activityId+'">'+obj.activityName+'</li>';
							} else {
								activityHTML += '<li><input name="appointmentType" type="checkbox" data-name="'+obj.activityName+'" value="'+obj.activityId+'">'+obj.activityName+'</li>';
							}
						});
						/**
						 * For gcss-575,select the 'Appointment Type' checkbox only when all activities are selected
						 */
						if(activityCount === result.activityList.length) {
							$filterBar.find('input[name="appointment-choose"]').iCheck('check');
						}
						$filterBar.find('.js-appointmentType-filter li').first()
							.nextAll().remove()
							.end().after(activityHTML)
							.end().nextAll().iCheck();
					}
					
					// Render the room
					var roomCount = 0;
					result.roomList.forEach(function(obj) {
						
						var key = obj.roomId;
						if(undefined === roomCache[key]) {
							roomCache[key] = true;
						}
						
						if(obj.isSelectedRoom === 'Y') {
							roomCount += 1;
							roomHTML += '<li><input name="room" checked type="checkbox" data-name="'+obj.profileRoomName+'" value="'+obj.roomId+'">'+obj.profileRoomName+'</li>';
						} else {
							roomHTML += '<li><input name="room" type="checkbox" data-name="'+obj.profileRoomName+'" value="'+obj.roomId+'">'+obj.profileRoomName+'</li>';
						}
					});
					/**
					 * For gcss-575,select the 'Room' checkbox only when all rooms are selected
					 */
					if(roomCount === result.roomList.length) {
						$filterBar.find('input[name="room-choose"]').iCheck('check');
					}
					$filterBar.find('.js-room-filter li').first()
						.nextAll().remove()
						.end().after(roomHTML)
						.end().nextAll().iCheck();

					/**
					 * Render the new calendar self
					 * RenderCalendar('day', filterType);
					 * 
					 * For bug,In order to keep the same view,add the 'viewType' parameter to this method
					 */
					self.renderCalendar(viewType, filterType);
				}
			});
		},
		/*
		* Render the new filter
		* @param {String} url to request
		*/
		renderFilter: function(url) {
			var self = this,
				CONF = self.conf,
				$filterBar = CONF.EL.$filterBar;
			
			var cache = self.cache,
				serviceCache = cache.serviceCache,
				instructorCache = cache.instructorCache,
				roomCache = cache.roomCache,
				activityCache = cache.activityCache;

			// Ajax request to get new filter
			$.ajax({
				url: url,
				type: 'GET',
				data: self.getFilter(),
				success: function(result) {
					// If session time out will get the login.html page
					// So, if response text has the string 'DOCTYPE' will reload page to jump to the login page
					if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
					var instructorHTML = '',
						activityHTML = '',
						roomHTML = '';
					// Render the instructor
					result.instructorDTOs.forEach(function(obj) {
						
						/**
						 * For GCSS-603
						 * To instructor/room/activity
						 * If the instructor/room/activity status is true, setting the checkbox to check.
						 */
						if(instructorCache[obj.instructorId]) {
							instructorHTML += '<li><input name="instructor" checked type="checkbox" data-name="'+obj.instructorName+'" value="'+obj.instructorId+'">'+obj.instructorName+'</li>';
						} else {
							instructorHTML += '<li><input name="instructor" type="checkbox" data-name="'+obj.instructorName+'" value="'+obj.instructorId+'">'+obj.instructorName+'</li>';
						}
						
					});
					
					/**
					 *  For GCSS-603
					 *  Value 'on' means parent filter. If 'on' is true, check the parement checkbox.
					 */
					if(instructorCache['on']) {
						$filterBar.find('.js-instructor-filter li').first().iCheck('check');
					} else {
						$filterBar.find('.js-instructor-filter li').first().iCheck('uncheck');
					}
					
					$filterBar.find('.js-instructor-filter li').first()
						.nextAll().remove()
						.end().after(instructorHTML)
						.end().nextAll().iCheck();
					
					if($filterBar.find('.js-instructor-filter input:not(:first):not(:checked)').length === 0) {
						$filterBar.find('.js-instructor-filter li').first().iCheck('check');
					} else {
						$filterBar.find('.js-instructor-filter li').first().iCheck('uncheck');
					}
					// Render the appointmentType
					if(result.activityDTOs) {
						result.activityDTOs.forEach(function(obj) {
							
							/**
							 * GCSS-603
							 */
							if(activityCache[obj.activityId]) {
								activityHTML += '<li><input name="appointmentType" checked type="checkbox" data-name="'+obj.activityName+'" value="'+obj.activityId+'">'+obj.activityName+'</li>';
							} else {
								activityHTML += '<li><input name="appointmentType" type="checkbox" data-name="'+obj.activityName+'" value="'+obj.activityId+'">'+obj.activityName+'</li>';
							}
							
						});
						$filterBar.find('.js-appointmentType-filter li').first()
							.nextAll().remove()
							.end().after(activityHTML)
							.end().nextAll().iCheck();
					}
					
					/**
					 * GCSS-603
					 * Refer to instructor section.
					 */
					if(activityCache['on']) {
						$filterBar.find('.js-appointmentType-filter li').first().iCheck('check');
					} else {
						$filterBar.find('.js-appointmentType-filter li').first().iCheck('uncheck');
					}
					
					if($filterBar.find('.js-appointmentType-filter input:not(:first):not(:checked)').length === 0) {
						$filterBar.find('.js-appointmentType-filter li').first().iCheck('check');
					} else {
						$filterBar.find('.js-appointmentType-filter li').first().iCheck('uncheck');
					}
					
					// Render the room
					result.roomDTOs.forEach(function(obj) {
						
						/**
						 * GCSS-603
						 * Refer to instructor section.
						 */
						if(roomCache[obj.roomId]) {
							roomHTML += '<li><input name="room" checked type="checkbox" data-name="'+obj.profileRoomName+'" value="'+obj.roomId+'">'+obj.profileRoomName+'</li>';
						} else {
							roomHTML += '<li><input name="room" type="checkbox" data-name="'+obj.profileRoomName+'" value="'+obj.roomId+'">'+obj.profileRoomName+'</li>';
						}
					});
					$filterBar.find('.js-room-filter li').first()
						.nextAll().remove()
						.end().after(roomHTML)
						.end().nextAll().iCheck();
					
					/**
					 * GCSS-603
					 * Refer to instructor section.
					 */
					if(roomCache['on']) {
						$filterBar.find('.js-room-filter li').first().iCheck('check');
					} else {
						$filterBar.find('.js-room-filter li').first().iCheck('uncheck');
					}
					
					if($filterBar.find('.js-room-filter input:not(:first):not(:checked)').length === 0) {
						$filterBar.find('.js-room-filter li').first().iCheck('check');
					} else {
						$filterBar.find('.js-room-filter li').first().iCheck('uncheck');
					}
					// Render the new calendar
					self.renderCalendar();
				}
			});
		},
		/*
		* Render a calendar when filter changed
		*/
		renderCalendar: function(view, filter) {
			var self = this,
				CONF = self.conf,
				date = hash.get('date') || moment().format('L'), 
				view = (undefined === view) ? hash.get('view') : view,
				filterType = (undefined === filter) ? hash.get('filter') : filter,
				params = self.getFilter();
				
			/**
			 * Remove the grayout layout,for gcss-525
			 */
			$('.cal-item-disabled').remove();	
			
			CONF.handler(date, view, params, filterType);
		},
		/*
		* Return the filter checkbox data
		*/
		getFilter: function() {
			var self = this,
				params = {};
			params = {
				instructors: self.filter_model.getInstructor(),
				rooms: self.filter_model.getRoom(),
				services: self.filter_model.getSeriveType(),
				appointments: self.filter_model.getAppointmentType(),
				
				 /**
                 * For gcss-525,the unselected service,room,instructor,activity array
                 */
				unselectedServices: self.filter_model.getUnSelectedServiceType(),
				unselectedActivities: self.filter_model.getUnSelectedActivityType(),
				unselectedRooms: self.filter_model.getUnselectedRoom(),
				unselectedInstructors: self.filter_model.getUnselectedInstructor(),
				/**
				 * GCSS-603
				 */
				unselectedActivitiesFromCache: self.filter_model.getUnSelectedActivityFromCache()
			}
			return params;
		},
		/*
		* Hide the filterbar
		*/
		hide: function() {
			var self = this,
				EL = self.conf.EL,
				$filterBar = EL.$filterBar,
				$customerInfo = EL.$customerInfo;
			$filterBar.hide();
			$customerInfo.show();
		},
		/*
		* Show the filterbar
		*/
		show: function() {
			var self = this,
				EL = self.conf.EL,
				$filterBar = EL.$filterBar,
				$customerInfo = EL.$customerInfo;
			$customerInfo.hide();
			$filterBar.show();
		}
	}
	return Filter;
});