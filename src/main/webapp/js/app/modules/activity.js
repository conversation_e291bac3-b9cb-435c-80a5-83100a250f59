define([
	'jquery', 
	'select2', 
	'icheck', 
	'bootbox',
	'mustache',
	'underscore',
	'backbone',
], function($, select2, iCheck, bootbox, Mustache, _, Backbone) {
	function Activity(opts) {
		// Default set
		this.defaults = {
			url_fetch_collection: 'profileActivity/loadProfileActivityList.htm',

			url_fetch_services: 'roomTemplate/loadRoomTemplateServices.htm',

			url_create_model: 'profileActivity/addProfileActivity.htm',
			
			url_fetch_activity_detail: 'service/loadActivityDetail.htm',

			url_update_activity: 'service/updateActivity.htm',
			
			url_delete_activity: 'profileActivity/deleteProfileActivity.htm',

			url_activity_typeahead: 'profileActivity/searchActivityByName.htm',

			url_initProfileActivity: 'profileActivity/initProfileActivity.htm',

			EL: $('.activity-list'),

			formTarget: $('.js-activity-form'),

			formTrigger: $('.js-show-activity-form')
		}	

		this.init(opts);

		this.is_form_init = false;

		this.is_changed = is_changed;
	}

	Activity.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			this.conf = $.extend(this.defaults, opts);
		},
		/*
		* Generate the add activity form
		*/
		newActivityForm: function() {
			if(this.is_form_init) return;

			var self = this,
				CONF = self.conf,
				NewActivity_Model;

			// Define form model
			NewActivity_Model = Backbone.Model.extend({
				// Initialize the form filed change function
				initialize: function() {
					var _this = this,
						formEL = this.formEL;

					formEL.activityId.select2({
						placeholder: 'Enter Activity Name',
						multiple: true,
						maximumSelectionSize: 1,
						minimumInputLength: 1,
						ajax: {
							url: CONF.url_activity_typeahead,
							dataType: 'json',
							data: function(term, page){
								return {
									activityName: term
								}
							},
							results: function(data, page) {
								return {
									results: data
								}
							}
						},
						id: function(obj) {
							return obj.activityId;
						},
						formatResult: function(obj) {
							return obj.activityName;
						},
						formatSelection: function(obj) {
							return obj.activityName;
						},
						dropdownCssClass: 'dropdown-menu'
					}).on('change', function(e) {
						var value = e.val[0];
						if(value && value !== '') {
							$.ajax({
								url: CONF.url_initProfileActivity,
								data: {
									activityId: value
								},
								success: function(result) {
									formEL.serviceType.select2('data', result.service).select2('readonly', true);
									formEL.minAttenders.val(result.minAttender).attr('readonly', '');
									formEL.maxAttenders.val(result.maxAttender).attr('readonly', '');
									formEL.instructor.select2('val', result.instructor).select2('readonly', true);
									formEL.minimumDuration.select2('data', {
										id: result.minimumDuration,
										text: result.minimumDurationText
									}).select2('readonly', true);
									formEL.maxmumDuration.select2('data', {
										id: result.maxmumDuration,
										text: result.maxmumDurationText
									}).select2('readonly', true);
									if(result.enable) {
										formEL.enable.iCheck('check');
									} else {
										formEL.enable.iCheck('uncheck');
									}
								}
							});
						} else {
							_this.clear();
						}
					});
					formEL.enable.iCheck();
					formEL.instructor.select2();
					formEL.minimumDuration.select2();
					formEL.maxmumDuration.select2();
					formEL.serviceType.select2({
						placeholder: 'Select Service Type',
						data: [],
						id: function(obj) {
							return obj.id;
						},
						formatResult: function(obj) {
							return obj.serviceName;
						},
						formatSelection: function(obj) {
							return obj.serviceName;
						}
					});

					self.is_form_init = true;
				},
				/*
				* Clear the form data when closed
				*/
				clear: function() {
					var formEL = this.formEL;
					formEL.activityId.select2('val', '');
					formEL.serviceType.select2('val', '').select2({
						placeholder: 'Select Service Type',
						data: [],
						id: function(obj) {
							return obj.id;
						},
						formatResult: function(obj) {
							return obj.serviceName;
						},
						formatSelection: function(obj) {
							return obj.serviceName;
						}
					}).select2('readonly', false);
					formEL.minAttenders.val('').removeAttr('readonly');
					formEL.maxAttenders.val('').removeAttr('readonly');
					formEL.minimumDuration.select2('val', '').select2('readonly', false);
					formEL.maxmumDuration.select2('val', '').select2('readonly', false);
					formEL.instructor.select2('val', '').select2('readonly', false);
					formEL.enable.iCheck('enable').iCheck('uncheck');
				},
				defaults: {

				},
				formEL: {
					'activityId': CONF.formTarget.find('input[name="activityId"]'),
					'serviceType': CONF.formTarget.find('input[name="serviceType"]'),
					'minAttenders': CONF.formTarget.find('input[name="minAttenders"]'),
					'maxAttenders': CONF.formTarget.find('input[name="maxAttenders"]'),
					'minimumDuration': CONF.formTarget.find('select[name="minimumDuration"]'),
					'maxmumDuration': CONF.formTarget.find('select[name="maxmumDuration"]'),
					'enable': CONF.formTarget.find('input[name="enable"]'),
					'instructor': CONF.formTarget.find('select[name="instructor"]')
				}
			});
			self.newactivity = new NewActivity_Model;
			return self;
		},
		/*
		* Slidedown to show the form 
		*/
		showForm: function() {
			var CONF = this.conf;
			CONF.formTarget.parent().slideDown();
			return this;
		},
		/*
		* Slideup to hide the form
		*/
		hideForm: function() {
			var CONF = this.conf;
			CONF.formTarget.parent().slideUp();
			CONF.formTarget.find('.form-msg').hide();
			CONF.formTrigger.removeClass('active');
			this.newactivity.clear();
			return this;
		},
		/*
		* Render the activity list
		*/
		renderList: function() {
			var self = this,
				CONF = self.conf,
				EL = CONF.EL,
				Activity_Model, Activity_View, Activity_Collection;

			// Init the is_change status 
			self.is_changed = false;
			is_changed = self.is_changed;

			// Show loadings
			self.showLoadTip();

			// The activity model
			Activity_Model = Backbone.Model.extend({
				methodUrl: {
					'read': CONF.url_fetch_model,
					'create': CONF.url_create_model,
					'update': CONF.url_update_activity,
					'delete': CONF.url_delete_activity
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				}
			});
			// The activity view
			Activity_View = Backbone.View.extend({
				initialize: function() {
					return this;
				},
				events: {
					'click .edit-activity': 'edit',
					'click .close-edit-panel': 'close',
					'ifChanged input[name="active"]': 'changeActive',
					'click .js-update-activity': 'update',
					'click .js-delete-activity': 'delete'
				},
				tagName: 'table',
				className: 'table table-list table-fixed',
				template: $('#tmpl_activity_list').html(),
				render: function() {
					this.$el.html(Mustache.render(this.template, this.model.toJSON()));
					return this;
				}, 
				edit: function(e) {
					var _this = this,
						$this = $(e.currentTarget);

					if($this.hasClass('editing')) return;

					if(self.is_changed) {
						bootbox.dialog({
							title: 'Warning!',
							message: 'You have unsaved information. Do you want to continue?',
							buttons: {
								success: {
									label: 'Continue',
									className: 'btn btn-important',
									callback: function() {
										EL.find('.editing').removeClass('editing');
										EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
										self.hideWarn();
										_this.getOne();
									}
								},
								cancle: {
									label: 'Cancel',
									className: 'btn btn-primary',
									callback: function() {
										self.showWarn();
										return;
									}
								}
							}
						});
					} else {
						EL.find('.editing').removeClass('editing');
						EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
						$this.addClass('editing');
						_this.getOne();		
					}
				},
				/*
				* Ajax request to get the detail activity 
				*/
				getOne: function() {
					var _this = this;
					$.ajax({
						url: CONF.url_fetch_activity_detail,
						type: 'GET',
						data: {
							id: _this.model.get('activityId'),
						},
						success: function(result) {
							// If session time out will get the login.html page
							// So, if response text has the string 'DOCTYPE' will reload page to jump to the login page
							if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
							_this.model.set(result);
							_this.editRender();
							_this.$el.find('tr').eq(0).addClass('disabled').nextAll().show();
							
							self.is_changed = false;
							is_changed = self.is_changed;
						}
					});
				},
				/*
				* Render the edit panel, bind change function to the form filed
				*/
				editRender: function() {
					this.$el.find('.edit-activity').addClass('editing');

					this.$el.find('input[name="name"]').val(this.model.get('activityName'));
					this.$el.find('input[name="serviceType"]').select2({
						data: [],
						id: function(obj) {
							return obj.serviceId
						},
						formatResult: function(obj) {
							return obj.serviceName;
						},
						formatSelection: function(obj) {
							return obj.serviceName;
						}
					}).select2('data', this.model.get('serviceDTO')).select2('readonly', true);

					this.$el.find('select[name="requireInstructor"]').select2()
						.select2('val', this.model.get('requiresInstructor')).select2('readonly', true);

					this.$el.find('input[name="minDuration"]').select2({
						data: []
					}).select2('data', {text: this.model.get('minimumDurationText'), value: this.model.get('minimumDuration')})
						.select2('readonly', true);

					this.$el.find('input[name="maxDuration"]').select2({
						data: []
					}).select2('data', {text: this.model.get('maxmumDurationText'), value: this.model.get('maxmumDuration')})
						.select2('readonly', true);

					this.$el.find('input[name="minimunAttendees"]').val(this.model.get('minimunAttendees'));

					this.$el.find('input[name="maximunAttendees"]').val(this.model.get('maximunAttendees'));
					
					if(this.model.get('enable')) {
						this.$el.find('input[name="active"]').iCheck().iCheck('uncheck');
					} else {
						this.$el.find('input[name="active"]').iCheck().iCheck('check');
					}
				},
				/*
				* Close the edit panel, destory all form filed
				*/
				close: function() {
					this.$el.find('.edit-activity').removeClass('editing');

					this.$el.find('input[name="serviceType"]').select2('destroy')
					this.$el.find('select[name="requireInstructor"]').select2('destroy');
					this.$el.find('select[name="minDuration"]').select2('destroy');
					this.$el.find('select[name="maxDuration"]').select2('destroy');
					this.$el.find('input[name="active"]').iCheck('destroy');

					self.hideWarn();
					this.$el.find('tr').eq(0).removeClass('disabled').nextAll().hide();

					self.is_changed = false;
					is_changed = self.is_changed;

					return this;
				},
				/*
				* Update the activity
				*/
				update: function() {
					var _this = this;
					this.$el.find('.js-update-activity').attr('disabled', true);//For GCSS-341 to avoid 'double click'
					this.model.sync('update', this.model, {
						success: function(result) {
							if(result.status) {
								_this.model.set(result.object);
								// _this.render();
								// TODO
								/**
								 * The next line called the Service/Activity tab click which would check 'is_changed'
								 */
								is_changed = false;
								$('.js-show-service-activity').click();
								
								_this.close();
								_this.$el.find('.edit-service').removeClass('editing');
								self.hideWarn();
							} else {
								notification.autoHideError(result.message);
							}
							_this.$el.find('.js-update-activity').attr('disabled', false);//For GCSS-341 to avoid 'double click'
						}
					});
				},
				/*
				* delete the activity
				*/
				delete: function() {
					var _this = this;
					bootbox.dialog({
						title: 'Warning!',
						message: 'Are you sure to Delete this Activity?',
						buttons: {
							success: {
								label: 'Delete',
								className: 'btn btn-important',
								callback: function() {
									$.ajax({
										type: 'GET',
										url: CONF.url_delete_activity,
										data: {
											activityId: _this.model.get('activityId')
										},
										success: function(result) {
											if(result.status) {
												_this.$el.fadeOut(function() {
													this.remove();
												});
											} else {
												notification.autoHideError(result.message);
											}
										}
									});	
								}
							},
							cancle: {
								label: 'Cancel',
								className: 'btn btn-primary'
							}
						}
					});
				},
				/*
				* 'Enable' filed change function
				*/
				changeActive: function(e) {
					
					self.is_changed = true;
					is_changed = self.is_changed;
					
					if(e.currentTarget.checked) {
						this.model.set('enable', false);
					} else {
						this.model.set('enable', true);
					}	
				}
			});
			// Activity collection
			Activity_Collection = Backbone.Collection.extend({
				model: Activity_Model,
				methodUrl: {
					'read': CONF.url_fetch_collection,
					'create': '',
					'update': '',
					'delete': ''
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				},
			});

			var activity_collection = new Activity_Collection;

			self.Model = Activity_Model;
			self.View = Activity_View;
			self.Collection = Activity_Collection;

			self.activity_collection = activity_collection;

			_.bindAll(self, 'addAll', 'addOne');

			activity_collection.on('set', self.addAll);
			activity_collection.on('add', self.addOne);

			self.getAll();	

		},
		/*
		* Fetch all activity	
		*/	
		getAll: function() {
			var self = this;
			self.conf.EL.empty();
			self.activity_collection.reset();
			self.activity_collection.fetch();
		},
		/*
		* Add all activity
		*/
		addAll: function() {
			var self = this;
			self.activity_collection.each(self.addOne);
		},
		/*
		* Add one activity, init activity view
		*/
		addOne: function(model) {
			var self = this,
				EL = self.conf.EL,
				view = new self.View({model: model});

			EL.append(view.render().el);
		},
		/*
		* Show a loading tip
		*/
		showLoadTip: function() {
			var self = this,
				EL = self.conf.EL;
			EL.append('<div class="loader"><p></p></div>');
		},
		/*
		* Hide the warn tip
		*/
		hideWarn: function() {
			$('.warn-tip').hide();
		},
		/*
		* Show the warn tip, when user have changed the form filed
		*/
		showWarn: function() {
			var EL = this.conf.EL,
				$target = EL.find('tr.disabled').nextAll().find('.js-update-activity'),
				right = 220,
				top = $target.offset().top - $target.height() - $('.warn-tip').height();
			$('.warn-tip').css({
				right: right,
				top: top
			}).show();
		},
		/*
		* Submit the form add new activity
		* TODO
		* callback will refresh the page
		*/
		create: function(callback) {
			var self = this,
				formTarget = self.conf.formTarget,
				data = formTarget.serializeArray(),
				activity = new self.Model;

			_.each(data, function(obj, index) {
				activity.set(obj['name'], obj['value']);
			});
			activity.sync('create', activity, {
				success: function(result) {
					if(result.status) {
						self.hideForm();
						// TODO
						// self.getAll();
						$('.js-show-service-activity').click();
						
						self.is_changed = false;
						is_changed = self.is_changed;
						
					} else {
						self.conf.formTarget.find('.form-msg').text(result.message).show();
					}
				}
			});
		}
	}

	// $.extend(true, Activity.prototype, new List, Proto)

	return Activity;
});