define([
	'jquery',
	'moment',
	'select2', 
	'icheck', 
	'mustache', 
	'underscore', 
	'backbone',
	'pikaday',
	'notification'
],
function($, moment, select2, iCheck, Mustache, _, Backbone, Pikaday, notification ) {
	function Profiletimeoff(opts) {
		// De<PERSON>ult set
		this.defaults = {
				
				url_update: 'profileTimeoff/updateProfileTimeoff.htm',
				url_load: 'profileTimeoff/loadProfileTimeoffs.htm',
				url_remove: 'profileTimeoff/deleteProfileTimeoff.htm',
				url_fetch_profile_start_time : 'profileTimeoff/getStartTimeByProfile.htm',
				url_cancel_load : 'profileTimeoff/loadCancelProfileTimeoffs.htm',
				
				 
			EL: {
				$modal: $('#modal_profile_timeoff'),
				$mask: $('.mask')
			}
		}

		// Default versionString
		this.currentVersion = undefined;
		this.init(opts);
	}
	Profiletimeoff.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
			
		init: function(opts) {
			this.conf = $.extend({}, this.defaults, opts);			
			
			var self = this,
				CONF = self.conf, 
				EL = self.conf.EL,
				$modal = EL.$modal,
				Profiletimeoff_Modal, Modal,
				formEL, modal,dateFrom;

			// Define time off model
			Profiletimeoff_Modal = Backbone.Model.extend({

			});

			self.Profiletimeoff_Modal = new Profiletimeoff_Modal;
			// Define modal model
			Modal = Backbone.Model.extend({
				defaults: {

				},
				formEL: {
					'dateFrom': {
						el: $modal.find('input[name="timeOffStartDate"]')
					},
					'timeFrom': {
						el: $modal.find('input[name="timeOffFrom"]'),
						set: function(val) {
							this.el.select2('val', val);
						},
						clear: function() { 
							this.el.select2('val', '');	
							this.el.select2('enable', false);
						},
						setAll: function(val) {
							this.el.select2({
								data: val
							});
							return this;
						}
					},
					'timeTo': {
						el: $modal.find('input[name="timeOffTo"]'),
						set: function(val) {
							this.el.select2('val', val);
						},	
						clear: function() { 
							this.el.select2('val', '');	
							this.el.select2('enable', false);
						},
						setAll: function(val) {
							this.el.select2({
								data: val
							});
							return this;
						}
					
					}
				}
			});
		
			modal = new Modal;
			self.modal = modal;
			formEL = modal.formEL;
		    
			dateFrom = new Pikaday({
				format: 'MM/DD/YYYY',
				field: formEL.dateFrom.el[0]
			});
			
	
			
		 
			// function to the time off
			formEL.dateFrom.el.on('change', function(e) {
				
			    //Changes made for GSSP-188
				var value = this.value;

				formEL.timeTo.clear();
				formEL.timeFrom.clear();
				
				$.ajax({

					url: CONF.url_fetch_profile_start_time,
					type: 'GET',
					data: ({'startDate':value}),
					success: function(result) {
						if(result.startTimeDTOs && result.startTimeDTOs.length > 0) {
							
							formEL.timeFrom.setAll(result.startTimeDTOs);
							formEL.timeFrom.el.select2('enable', true);
							formEL.timeTo.setAll(result.startTimeDTOs);							
							formEL.timeTo.el.select2('enable', true);

						}		
						else
						{
							notification.autoHideError(result.message);
						}	
					}	
				
				});
				
				//End of Changes made for GSSP-188
				
			});
					
				
		}, 

		/*
		 * Init 
		 */
		initDate: function() {
			$('input[name="startDate"]').val(moment(new Date()).format('L'));
			$('input[name="endDate"]').val(moment(new Date()).format('L'));
		},
		//profiletimeoffModal = new ProfiletimeoffModal,
		  
		/*
		* Show the time info on the right of this Modal
		*/
		
		renderTimeOffList: function(obj){
			var self = this,
				CONF = self.conf,
				EL = self.conf.EL,
				$modal = EL.$modal,
				timeoff = '',
				timeofflist = obj;
			if(timeofflist.length > 0){
				for(i in timeofflist){   
					timeoff += '<p><a class="icon icon-remove js-remove-timeoff" data-id="'+timeofflist[i].profiletimeoffId+'" href="javascript:;">x</a>' + timeofflist[i].timeOffStartToEnd + '</p>';
				}
			}
			
			$modal.find('.timeoff-list').empty().html(timeoff);
			
			$modal.find('.js-remove-timeoff').on('click',function(){
				
				var timeoffId = $(this).attr('data-id')*1;
				
				$.ajax({
					url: CONF.url_remove,
					type: 'POST',
					data: {'profileTimeoffId':timeoffId},
					success: function(result) {
							var timeoff_tmp ='',
								timeoffList_tmp = result.profileTimeoffs;
						if(result.status){
							self.renderTimeOffList(result.profileTimeoffs);
							if(timeoffList_tmp.length > 0){
								for(i in timeoffList_tmp){
										timeoff_tmp += '<p class="row">' + timeoffList_tmp[i].timeOffStartToEnd  + '</p>';
								}
							}
							$('.js-timeoff-wrap').empty().html(timeoff_tmp);
							
						}else{
							notification.autoHideError(result.message);
						}
					}
				});
			});
			
		},
		
		/*
		 * Show the pop and set the timeoff
		 */
		showModal:function(obj){

			var self = this,
				CONF = self.conf,
				EL = CONF.EL,
				$mask = EL.$mask,
				$modal = EL.$modal;
			
			// Set the position of Modal
			var win_height = $(window).height(),
				win_width = $(window).width(),
				m_width = 533,
				m_height = 246,
				top = (win_height - m_height) / 2 + $(window).scrollTop(),
				left = (win_width - m_width) / 2;
	
			if(top < 0) top = 10;
			if(left < 0) left = 10;
			$modal.css({
				left: left,
				top: top
			});
			
			//this.instructorModel = obj 
		 $this = $(obj.currentTarget);
						
			$.ajax({
				url: CONF.url_load,
				type: 'GET',
				data: {'siteID':1},
				success: function(result) { 
					self.renderTimeOffList(result.profileTimeoffs);
				}
			});
			
				
			$modal.show();
			$mask.show();
		},
		
		//var self = this;
	   /*	events: {
			 
				'click .js-set-timeoff-pr': 'setTimeoffpr',
			},*/
		/*
		 * update the time off
		 */
		update: function(e){
			var self = this,
				CONF = self.conf,
				formEL = self.modal.formEL,
				dateFrom = formEL.dateFrom.el.val(),
				timeFrom = formEL.timeFrom.el.val(),
				timeTo = formEL.timeTo.el.val();
				id = 0,
				data = {'fromTime':timeFrom,'fromDate':dateFrom,'toTime':timeTo};
			
			/**
			 * Disable the update button to avoid double click
			 */
			var $updateButton=$('.btn-update-timeoff-pr');
			$updateButton.prop('disabled', true);
			
			$.ajax({
				url: CONF.url_update,
				type: 'POST',
				contentType: 'application/json',
				data: JSON.stringify(data),
				success: function(result) {
					if(result.status){
						var timeoffList_tmp = result.dto,
							timeoff_tmp = '',
							timeoffpop_tmp = '',
							timeoffList_tmp2='';
						
						if(timeoffList_tmp.length > 0){
							for(i in timeoffList_tmp){
								timeoff_tmp += '<p class="row">' + timeoffList_tmp[i].timeOffStartToEnd  + '</p>';
							}
						}
						$('.js-timeoff-wrap').empty().html(timeoff_tmp);
						
						/**
						 * For GCSS-618,do not hide the modal after update
						 * 1.Build timeoff and render in the timeoff modal immediately
						 * 2.Clean the date and time selected
						 */
						if(timeoffList_tmp.length > 0){
							for(i in timeoffList_tmp){
								timeoffList_tmp2 += '<p><a class="icon icon-remove js-remove-timeoff" data-id="'+timeoffList_tmp[i].profiletimeoffId+'" href="javascript:;">x</a>' + timeoffList_tmp[i].timeOffStartToEnd + '</p>';
							}
						}
						$('.timeoff-list').empty().html(timeoffList_tmp2);
						self.cleanDate();
						self.cleanTime();
						self.renderTimeOffList(result.dto);
						
					}else{
						notification.autoHideError(result.message);
					}
					/**
					 * Enable the update button
					 */
					$updateButton.prop('disabled', false);
				}
			});
		},
		/*
		 * Hide the Modal and clean the data in the Modal
		 */
		hideModal: function(e){
			var self = this,
			CONF = self.conf,
			EL = CONF.EL,
			formEL = self.modal.formEL;
 
			$.ajax({
				url: CONF.url_cancel_load,
				type: 'GET',
				data: {'siteID':1},
				success: function(result) {
				if(result.status) {
					var set,
						timeStr = '';
					// If it's availability update
					if(result.loadProfileTimeOff && result.loadProfileTimeOff.length > 0) {
						set = result.loadProfileTimeOff;
						// Build up the new time info
						set.forEach(function(obj) {
							var startTime = obj.dayString,
								endTime =  obj.timeString,
								weekDay = obj.dayString;
							if(startTime != '') {
								timeStr += '<p class="row"><span class="span5">'+weekDay+'</span> <span class="span7">'+endTime+'</span></p>'
							}	
						});
						
						$('.studio-time-off-hour').html(timeStr);

					} else {
						set = result.set;
						timeStr = '';
						 $('.studio-time-off-hour').html(timeStr);
	
					}
				} else {
					notification.autoHideError(result.message);
				}
			}
		});

			//--------------------------------------Rajkumar End---------------------------------
			EL.$modal.hide();
			EL.$mask.hide();
			/**
			 * Clean date and time selected
			 */
			self.cleanDate();
			self.cleanTime();
			
			
			
		},
		/**
		 * Clean the date selected
		 */
		cleanDate: function() {
			var self = this,
			EL = self.conf.EL,
			formEL = self.modal.formEL;
			formEL.dateFrom.el.val('');
		},
		/**
		 * Clean the time selected
		 */
		cleanTime: function() {
			var self = this,
			EL = self.conf.EL,
			formEL = self.modal.formEL;
			formEL.timeFrom.set('');
			formEL.timeTo.set('');		
			
			formEL.timeFrom.clear();
			formEL.timeTo.clear();
			
		}
	}
	return Profiletimeoff;
});