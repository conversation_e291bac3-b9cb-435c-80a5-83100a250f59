define([
	'jquery', 
	'mustache',
	'underscore', 
	'backbone',
	'notification'
], function($, Mustache, _, Backbone, notification) {
	function Studio(opts) {
		// <PERSON><PERSON><PERSON> set
		this.defaults = {
			url_fetch_collection: 'location/findAllStudios.htm',

			url_searchStudio: 'location/locationSearch.htm',

			EL: $('.studio-list')
		}	

		this.init(opts);

		this.is_edit = false;

		this.is_changed = false;
	}

	Studio.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			var self = this;
			self.conf = $.extend(self.defaults, opts);
		},
		/*
		* Render the studio list
		*/
		renderList: function() {
			var self = this,
				CONF = self.conf,
				Studio_Model, Studio_View, Studio_Collection;

			self.is_changed = false;

			// Define studio model
			Studio_Model = Backbone.Model.extend({
				methodUrl: {
					'read': CONF.url_fetch_model,
					'create': CONF.url_create_model,
					'update': CONF.url_update_model,
					'delete': ''
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				}
			});

			// Define studio view
			Studio_View = Backbone.View.extend({
				initialize: function() {
					return this;
				},
				events: {
					'click .edit-studio': 'edit',
					'click .close-edit-panel': 'close',
				},
				tagName: 'table',
				className: 'table table-list table-fixed',
				template: $('#tmpl_studio').html(),
				render: function() {
					this.$el.html(Mustache.render(this.template, this.model.toJSON()));
					return this;
				}
			});

			// Define studio collection
			Studio_Collection = Backbone.Collection.extend({
				model: Studio_Model,
				methodUrl: {
					'read': CONF.url_fetch_collection,
					'create': '',
					'update': '',
					'delete': ''
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				},
			});

			var studio_collection = new Studio_Collection;

			self.Model = Studio_Model;
			self.View = Studio_View;
			self.Collection = Studio_Collection;

			self.studio_collection = studio_collection;

			_.bindAll(self, 'addAll', 'addOne');

			studio_collection.on('set', self.addAll);
			studio_collection.on('add', self.addOne);

			self.getAll();	
		},
		/*
		* Search studio function
		*/
		search: function() {
			var self = this,
				CONF = self.conf;
			$.ajax({
				url: CONF.url_searchStudio,
				type: 'GET',
				data: {
					searchCriteria: $('.search-query').val()
				},
				success: function(result) {
					//GCSS-660
					if(result.length < 1){
						CONF.EL.html('<table class="table table-list table-fixed"><tr><th cosplan="4">No matching locations have been found. Please search again.</th></tr></table>')
					}else{
						CONF.EL.empty();
						self.studio_collection.reset();
						self.studio_collection.set(result)
				    }
				}
			});
		},
		/*
		* Fetch all studios
		*/
		getAll: function() {
			var self = this;
			self.conf.EL.empty();
			self.studio_collection.reset();
			self.studio_collection.fetch();
		},
		/*
		* Add all studios to the collection
		*/
		addAll: function() {
			var self = this;
			self.studio_collection.each(self.addOne);
		},
		/*
		* Add one studio, init one stuido view
		*/
		addOne: function(model) {
			var EL = this.conf.EL,
				view = new this.View({model: model});

			EL.append(view.render().el);
		},
		/*
		* Update the stuido status, enable or disable
		* @param {String} the url request
		* @param {Boolean} it's enable or disable
		*/
		update: function(url, enable) {
			if(enable) {
				updateProfile(url);
			} else {
				bootbox.dialog({
					title: 'Disable Studio Profile',
					message: 'Are you sure you want to disable the Studio Profile ?\n Studio Profile will no longer be available for Studio Admin.',
					buttons: {
						success: {
							label: 'Disable',
							className: 'btn btn-important',
							callback: function() {
								updateProfile(url);
							}
						},
						cancle: {
							label: 'Cancel',
							className: 'btn btn-primary',
							callback: function() {
								return;
							}
						}
					}
				});
			}
			function updateProfile(url) {
				$.ajax({
					url: url,
					success: function(result) {
						if(result.status) {
							setTimeout(function() {
								location.reload();
							}, 3000);
						} else {
							notification.autoHideError(result.msg);
						}
					}
				});	
			}
		}
	}
	return Studio;
});