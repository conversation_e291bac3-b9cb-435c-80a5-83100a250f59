define([
	'jquery', 
	'select2', 
	'icheck', 
	'bootbox',
	'mustache',
	'underscore',
	'backbone',
	'notification'
], function($, select2, iCheck, bootbox, Mustache, _, Backbone, notification) {
	function Activity(opts) {
		// De<PERSON><PERSON> set
		this.defaults = {
			url_fetch_collection: 'centralized/loadActivityList.htm',

			url_fetch_services: 'roomTemplate/loadRoomTemplateServices.htm',

			url_create_model: 'centralized/saveActivity.htm',
			
			url_fetch_activity_detail: 'centralized/loadActivity.htm',

			url_update_activity: 'centralized/updateActivity.htm',

			url_delete_activity: 'centralized/deleteActivity.htm',

			url_get_instructor_required: 'centralized/getDefaultInstrctor.htm',

			EL: $('.activity-list'),

			formTarget: $('.js-activity-form')
		}	

		this.init(opts);

		this.is_edit = false;

		this.is_changed = false;
	}

	Activity.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			var self = this;
			self.conf = $.extend(self.defaults, opts);
		},
		/*
		* Generate the add activity form
		*/
		newActivityTemplateForm: function() {
			var self = this,
				CONF = self.conf,
				NewActivity_Model;

			// Define form model
			NewActivity_Model = Backbone.Model.extend({
				initialize: function() {
					var _this = this,
						formEL = this.formEL;

					formEL.enable.iCheck();
					formEL.instructor.select2();//For GCSS-373
					formEL.minimumDuration.select2();
					formEL.maxmumDuration.select2();
					formEL.minAttenders.on('change', function() {
						if($(this).val() !== '') {
							formEL.maxAttenders.attr('disabled', false);
						} else {
							formEL.maxAttenders.val('1').attr('disabled', true);
						}
					});
					// ServiceType is dynamic
					formEL.serviceId.select2({
						ajax: {
							url: CONF.url_fetch_services,
							data: function(term, page) {
								return {
									serviceString: ''
								}
							},
							results: function(data, page) {
								return {
									results: data.serviceList
								}
							}
						},
						id: function(obj) {
							return obj.serviceId;
						},
						formatResult: function(obj) {
							return obj.serviceName;
						},
						formatSelection: function(obj) {
							return obj.serviceName;
						}
					}).on('change', function() {
						_this.changeRequiresInstructor($(this).val());
					});
				},
				/*
				* When change the service type, set the instructor default related value
				* @param {String} the select value
				*/
				changeRequiresInstructor: function(val) {
					var formEL = this.formEL;
					$.ajax({
						type: 'post',
						url: CONF.url_get_instructor_required,
						data: {
							id: val
						},
						success: function(result) {
							formEL.instructor.select2('val', result);
						}
					})
				},
				/*
				* Clear the form data when closed
				*/
				clear: function() {
					var formEL = this.formEL;
					formEL.activityName.val('');
					formEL.serviceId.select2('val', '');
					formEL.minimumDuration.select2('val', '');
					formEL.maxmumDuration.select2('val', '');
					formEL.minAttenders.val('');
					formEL.maxAttenders.attr('disabled', true).val('1');
					formEL.enable.iCheck('uncheck');
					formEL.instructor.select2('val', ''); //For GCSS-373
				},
				defaults: {

				},
				formEL: {
					'activityName': CONF.formTarget.find('input[name="activityName"]'),
					'serviceId': CONF.formTarget.find('input[name="serviceId"]'),
					'minimumDuration': CONF.formTarget.find('select[name="minimumDuration"]'),
					'maxmumDuration': CONF.formTarget.find('select[name="maxmumDuration"]'),
					'minAttenders': CONF.formTarget.find('input[name="minAttenders"]'),
					'maxAttenders': CONF.formTarget.find('input[name="maxAttenders"]'),
					'enable': CONF.formTarget.find('input[name="enable"]'),
					'instructor': CONF.formTarget.find('select[name="instructor"]')
				}
			});

			self.newactivity = new NewActivity_Model;
			return self;	
		},
		/*
		* Slidedown to show the form 
		*/
		showForm: function() {
			var CONF = this.conf;
			CONF.formTarget.parent().slideDown();
		},
		/*
		* Slideup to hide the form
		*/
		hideForm: function() {
			var CONF = this.conf;
			CONF.formTarget.parent().slideUp();
			CONF.formTarget.find('.form-msg').hide();
			$('.js-show-activity-form').removeClass('active');
			this.newactivity.clear();
		},
		/*
		* Render the activity list
		*/
		renderList: function() {
			var self = this,
				CONF = self.conf,
				Activity_Model, Activity_View, Activity_Collection;

			// Init the is_change status 
			self.is_changed = false;

			// The activity model
			Activity_Model = Backbone.Model.extend({
				methodUrl: {
					'read': CONF.url_fetch_model,
					'create': CONF.url_create_model,
					'update': CONF.url_update_activity,
					'delete': ''
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				}
			});

			// The activity view
			Activity_View = Backbone.View.extend({
				initialize: function() {
					return this;
				},
				events: {
					'click .edit-activity': 'edit',
					'click .close-edit-panel': 'close',
					'change input[name="name"]': 'changeName',
					'change input[name="minimunAttendees"]': 'changeMinimunAttendees',
					'change input[name="maximunAttendees"]': 'changeMaximunAttendees',
					'change select[name="minDuration"]': 'changeMinimumDuration',
					'change select[name="maxDuration"]': 'changeMaxmumDuration',
					'change select[name="requireInstructor"]': 'changeRequiresInstructor',
					'ifChanged input[name="active"]': 'changeActive',
					'ifChanged input[name="globalChange"]': 'changeGlobal',
					'click .js-delete-activity': 'delete',
					'click .js-update-activity': 'update'
				},
				tagName: 'table',
				className: 'table table-list table-fixed',
				template: $('#tmpl_activity_list').html(),
				render: function() {
					this.$el.html(Mustache.render(this.template, this.model.toJSON()));
					return this;
				}, 
				edit: function(e) {
					var _this = this,
						$this = $(e.currentTarget);

					if($this.hasClass('editing')) return;

					if(self.is_changed) {
						bootbox.dialog({
							title: 'Warning!',
							message: 'You have unsaved information. Do you want to continue?',
							buttons: {
								success: {
									label: 'Continue',
									className: 'btn btn-important',
									callback: function() {
										CONF.EL.find('.editing').removeClass('editing');
										CONF.EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
										self.hideWarn();
										_this.getOne();
										//To avoid show warning dialog if before going to edit another acitivity with a previous unsaved and modified activity
										self.is_changed = false;
									}
								},
								cancle: {
									label: 'Cancel',
									className: 'btn btn-primary',
									callback: function() {
										self.showWarn();
										return;
									}
								}
							}
						});
					} else {
						CONF.EL.find('.editing').removeClass('editing');
						CONF.EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
						$this.addClass('editing');
						_this.getOne();		
					}
				},
				/*
				* Ajax request to get the detail activity 
				*/
				getOne: function() {
					var _this = this;
					$.ajax({
						url: CONF.url_fetch_activity_detail,
						type: 'GET',
						data: {
							id: _this.model.get('activityId'),
						},
						success: function(result) {
							// If session time out will get the login.html page
							// So, if response text has the string 'DOCTYPE' will reload page to jump to the login page
							if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
							
							//If the record had been deleted,show the message from server and remove th invalid record
							if(result.message) {
								notification.autoHideError(result.message);
								_this.$el.fadeOut(function(){
									this.remove();
								});
								return;
							}
							_this.model.set(result);
							_this.editRender();
							_this.$el.find('tr').eq(0).addClass('disabled').nextAll().show();
						}
					});
				},
				/*
				* Render the edit panel, bind change function to the form filed
				*/
				editRender: function() {
					this.$el.find('.edit-activity').addClass('editing');
					this.$el.find('input[name="name"]').val(this.model.get('activityName'));
					this.$el.find('input[name="globalChange"]').iCheck().iCheck('uncheck');
					this.$el.find('input[name="serviceType"]').select2({
							data: [],
							id: function(obj) {
								return obj.serviceId
							},
							formatResult: function(obj) {
								return obj.serviceName;
							},
							formatSelection: function(obj) {
								return obj.serviceName;
							}
					}).select2('data', this.model.get('serviceDTO')).select2('readonly', true);
					this.$el.find('select[name="requireInstructor"]').select2()
						.select2('val', this.model.get('requiresInstructor'));
					this.$el.find('select[name="minDuration"]').select2()
						.select2('val', this.model.get('minimumDuration'));
					this.$el.find('select[name="maxDuration"]').select2()
						.select2('val', this.model.get('maxmumDuration'));
					this.$el.find('input[name="minimunAttendees"]').val(this.model.get('minimunAttendees'));
					this.$el.find('input[name="maximunAttendees"]').val(this.model.get('maximunAttendees'));
					if(this.model.get('enable')) {
						this.$el.find('input[name="active"]').iCheck().iCheck('uncheck');
					} else {
						this.$el.find('input[name="active"]').iCheck().iCheck('check');
					}
				},
				/*
				* Close the edit panel, destory all form filed
				*/
				close: function() {
					this.$el.find('.edit-activity').removeClass('editing');

					this.$el.find('input[name="serviceType"]').select2('destroy')
					this.$el.find('select[name="requireInstructor"]').select2('destroy');
					this.$el.find('select[name="minDuration"]').select2('destroy');
					this.$el.find('select[name="maxDuration"]').select2('destroy');
					this.$el.find('input[name="active"]').iCheck('destroy');

					self.hideWarn();
					this.$el.find('tr').eq(0).removeClass('disabled').nextAll().hide();

					self.is_changed = false;

					return this;
				},
				/*
				* Update the activity
				*/
				update: function() {
					var _this = this;
					this.$el.find('.js-update-activity').attr('disabled', true);//For GCSS-341 to avoid 'double click'
					this.model.sync('update', this.model, {
						success: function(result) {
							if(result.status) {
								_this.model.set(result.object);
								_this.render();
								_this.close();
								self.hideWarn();
							} else {
								notification.autoHideError(result.message);
							}
							_this.$el.find('.js-update-activity').attr('disabled', false);//For GCSS-341 to avoid 'double click'
						}
					});
				},
				/*
				* Delete the activity
				*/
				delete: function() {
					var _this = this;
					bootbox.dialog({
						title: 'Warning!',
						message: 'Are you sure to Delete this Activity?',
						buttons: {
							success: {
								label: 'Delete',
								className: 'btn btn-important',
								callback: function() {
									$.ajax({
										type: 'GET',
										url: CONF.url_delete_activity,
										data: {
											id: _this.model.get('activityId')
										},
										success: function(result) {
											if(result.status) {
												_this.$el.fadeOut(function() {
													this.remove();
												});
											} else {
												notification.autoHideError(result.message);
											}
										}
									});	
								}
							},
							cancle: {
								label: 'Cancel',
								className: 'btn btn-primary'
							}
						}
					});
				},
				changeName: function(e) {
					self.is_changed = true;
					this.model.set('activityName', $(e.currentTarget).val());
					return this;
				},
				changeMinimunAttendees: function(e) {
					self.is_changed = true;
					this.model.set('minimunAttendees', $(e.currentTarget).val());
					return this;
				},
				changeMaximunAttendees: function(e) {
					self.is_changed = true;
					this.model.set('maximunAttendees', $(e.currentTarget).val());
					return this;
				},
				changeMinimumDuration: function(e) {
					self.is_changed = true;
					this.model.set('minimumDuration', e.val);
					return this;
				},
				changeMaxmumDuration: function(e) {
					self.is_changed = true;
					this.model.set('maxmumDuration', e.val);
					return this;
				},
				changeRequiresInstructor: function(e) {
					self.is_changed = true;
					this.model.set('requiresInstructor', e.val);
					return this;
				},
				changeGlobal: function(e) {
					self.is_changed = true;
					var _this = this;
					if(e.currentTarget.checked) {
						this.model.set('globalChange', true);
					} else {
						this.model.set('globalChange', false);
					}
				},
				changeActive: function(e) {
					self.is_changed = true;
					if(e.currentTarget.checked) {
						this.model.set('enable', false);
					} else {
						this.model.set('enable', true);
					}	
				}
			});
			
			// Define activity collection
			Activity_Collection = Backbone.Collection.extend({
				model: Activity_Model,
				methodUrl: {
					'read': CONF.url_fetch_collection,
					'create': '',
					'update': '',
					'delete': ''
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				},
			});

			var activity_collection = new Activity_Collection;

			self.Model = Activity_Model;
			self.View = Activity_View;
			self.Collection = Activity_Collection;

			self.activity_collection = activity_collection;

			_.bindAll(self, 'addAll', 'addOne');

			activity_collection.on('set', self.addAll);
			activity_collection.on('add', self.addOne);

			self.getAll();	

		},
		/*
		* Fetch all activity	
		*/	
		getAll: function() {
			var self = this;
			self.conf.EL.empty();
			self.activity_collection.reset();
			self.activity_collection.fetch();
		},
		/*
		* Add all activity
		*/
		addAll: function() {
			var self = this;
			self.activity_collection.each(self.addOne);
		},
		/*
		* Add one activity, init activity view
		*/
		addOne: function(model) {
			var	EL = this.conf.EL,
				view = new this.View({model: model});

			EL.append(view.render().el);
		},
		/*
		* Hide the warn tip
		*/
		hideWarn: function() {
			$('.warn-tip').hide();
		},
		/*
		* Show the warn tip, when user have changed the form filed
		*/
		showWarn: function() {
			var EL = this.conf.EL,
				$target = EL.find('tr.disabled').nextAll().find('.js-update-activity'),
				right = 220,
				top = $target.offset().top - $target.height() - $('.warn-tip').height();
			$('.warn-tip').css({
				right: right,
				top: top
			}).show();
		},
		/*
		* Submit the form add new activity
		*/
		create: function(callback) {
			var self = this,
				CONF = self.conf,
				data = CONF.formTarget.serializeArray(),
				activity;

			activity = new self.Model;

			_.each(data, function(obj, index) {
				activity.set(obj['name'], obj['value']);
			});
			if(CONF.formTarget.find('input[name="maxAttenders"][disabled]').length > 0) {
				activity.set('maxAttenders', '1');
			}

			activity.sync('create', activity, {
				success: function(result) {
					if(result.status) {
						self.hideForm();
						self.getAll();
						self.is_changed = false;
					} else {
						CONF.formTarget.find('.form-msg').text(result.message).show();
					}
				}
			});
		}
	}
	return Activity;
});