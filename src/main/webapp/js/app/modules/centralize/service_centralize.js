define([
	'jquery', 
	'select2', 
	'icheck', 
	'bootbox',
	'mustache',
	'underscore', 
	'backbone',
	'notification'
], function($, select2, iCheck, bootbox, Mustache, _, Backbone, notification) {
	function Service(opts) {
		// De<PERSON>ult set
		this.defaults = {
			url_fetch_collection: 'centralized/loadServiceList.htm',

			url_create_model: 'centralized/createService.htm',
			
			url_fetch_service_detail: 'centralized/loadServiceDetail.htm',
			url_update_serivce: 'centralized/updateService.htm',
			url_delete_service: 'centralized/deleteService.htm',

			formTarget: $('.js-service-form'),
			EL: $('.service-list')
		}	

		this.init(opts);

		this.is_edit = false;

		this.is_changed = false;
	}

	Service.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			var self = this;
			self.conf = $.extend(self.defaults, opts);
		},
		/*
		* Generate the add service form
		*/
		newServiceTemplateForm: function() {
			var self = this,
				CONF = self.conf,
				NewService_Model;

			// Define form model
			NewService_Model = Backbone.Model.extend({
				initialize: function() {
					var formEL = this.formEL;
					formEL.instructor.select2(); //For GCSS-373
					formEL.enable.iCheck();
				},
				/*
				* Clear the form filed, when form closed
				*/
				clear: function() {
					var formEL = this.formEL;
					formEL.serviceName.val('');
					formEL.instructor.select2('val', '');//For GCSS-373
					formEL.enable.iCheck('uncheck');
				},
				formEL: {
					'serviceName': CONF.formTarget.find('input[name="serviceName"]'),
					'instructor':  CONF.formTarget.find('select[name="instructor"]'),
					'enable':  CONF.formTarget.find('input[name="enable"]')
				}
			});

			self.newservice = new NewService_Model;
			return self;	
		},
		/*
		* Slidedown to show the form 
		*/
		showForm: function() {
			var	CONF = this.conf;
			CONF.formTarget.parent().slideDown();
		},
		/*
		* Slideup to hide the form
		*/
		hideForm: function() {
			var	CONF = this.conf;
			CONF.formTarget.parent().slideUp();
			CONF.formTarget.find('.form-msg').hide();
			$('.js-show-service-form').removeClass('active');
			this.newservice.clear();
		},
		/*
		* Render the service list
		*/
		renderList: function() {
			var self = this,
				CONF = self.conf,
				Service_Model, Service_View, Service_Collection;

			// Init the is_change status 
			self.is_changed = false;

			// Define service model
			Service_Model = Backbone.Model.extend({
				methodUrl: {
					'read': CONF.url_fetch_model,
					'create': CONF.url_create_model,
					'update': CONF.url_update_serivce,
					'delete': ''
				},
				defaults: {
					enable: false
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				}
			});

			// Define service view
			Service_View = Backbone.View.extend({
				initialize: function() {
					return this;
				},
				events: {
					'click .edit-service': 'edit',
					'click .close-edit-panel': 'close',
					'change input[name="serviceName"]': 'changeName',
					'ifChanged input[name="active"]': 'changeActive',
					'change select[name="requireInstructor"]': 'changeRequireinstructor',
					'click .js-delete-service': 'delete',
					'ifChanged input[name="globalChange"]': 'changeGlobal',
					'click .js-update-service': 'update'
				},
				tagName: 'table',
				className: 'table table-list table-fixed',
				template: $('#tmpl_service').html(),
				render: function() {
					this.$el.html(Mustache.render(this.template, this.model.toJSON()));
					return this;
				},
				edit: function(e) {
					var _this = this,
						$this = $(e.currentTarget);

					if($this.hasClass('editing')) return;

					if(self.is_changed) {
						bootbox.dialog({
							title: 'Warning!',
							message: 'You have unsaved information. Do you want to continue?',
							buttons: {
								success: {
									label: 'Continue',
									className: 'btn btn-important',
									callback: function() {
										CONF.EL.find('.editing').removeClass('editing');
										CONF.EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
										self.hideWarn();
										_this.getOne();
									}
								},
								cancle: {
									label: 'Cancel',
									className: 'btn btn-primary',
									callback: function() {
										self.showWarn();
										return;
									}
								}
							}
						});
					} else {
						CONF.EL.find('.editing').removeClass('editing');
						CONF.EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
						$this.addClass('editing');
						_this.getOne();		
					}
				},
				/*
				* Ajax request to get service detail
				*/
				getOne: function() {
					var _this = this;
					$.ajax({
						url: CONF.url_fetch_service_detail,
						type: 'GET',
						data: {
							id: _this.model.get('serviceId')
						},
						success: function(result) {
							// If session time out will get the login.html page
							// So, if response text has the string 'DOCTYPE' will reload page to jump to the login page
							if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
							
							//If the record had been deleted,show the message from server and remove th invalid record
							if(result.message) {
								notification.autoHideError(result.message);
								_this.$el.fadeOut(function(){
									this.remove();
								});
								return;
							}
							
							_this.model.set(result);
							_this.editRender();
							_this.$el.find('tr').eq(0).addClass('disabled').nextAll().show();
							self.is_changed = false;
						}
					});
				},
				/*
				* Render the edit panel, bind change function to the form filed
				*/
				editRender: function() {
					this.$el.find('.edit-service').addClass('editing');
					this.$el.find('input[name="serviceName"]').val(this.model.get('serviceName'));
					this.$el.find('select[name="requireInstructor"]').select2().select2('val', this.model.get('instructor'));
					if(this.model.get('enable')) {
						this.$el.find('input[name="active"]').iCheck().iCheck('indeterminate');
					} else {
						this.$el.find('input[name="active"]').iCheck().iCheck('determinate');
					}
					this.$el.find('input[name="globalChange"]').iCheck().iCheck('uncheck');
				},
				/*
				* Close the edit panel, destory all form filed
				*/
				close: function() {
					this.$el.find('.edit-service').removeClass('editing');
					this.$el.find('tr').eq(0).removeClass('disabled').nextAll().hide();
					this.$el.find('select[name="requireInstructor"]').select2('destroy');
					this.$el.find('input[name="active"]').iCheck('destroy');
					self.hideWarn();
					self.is_changed = false;
					return this;
				},
				changeName: function(e) {
					this.model.set('serviceName', $(e.currentTarget).val());
					self.is_changed = true;
					return this;
				},
				changeRequireinstructor: function(e) {
					this.model.set('instructor', e.val);
					self.is_changed = true;
					return this;
				},
				changeGlobal: function(e) {
					self.is_changed = true;
					var _this = this;
					if(e.currentTarget.checked) {
						this.model.set('globalChange', true);
					} else {
						this.model.set('globalChange', false);
					}
				},
				changeActive: function(e) {
					self.is_changed = true;
					if(e.currentTarget.checked) {
						this.model.set('enable', false);
					} else {
						this.model.set('enable', true);
					}	
				},
				/*
				* Update the service model
				*/
				update: function(callback) {
					var _this = this;
					this.$el.find('.js-update-service').attr('disabled', true);//For GCSS-341 to avoid 'double click'
					this.model.sync('update', this.model, {
						success: function(result) {
							if(result.status) {
								_this.model.set(result.object);
								// Reload this page
								location.href = location.href;
							} else {
								notification.autoHideError(result.message);
							}
							_this.$el.find('.js-update-service').attr('disabled', false);//For GCSS-341 to avoid 'double click'
						}
					});
				},
				/*
				* Delete this service
				*/
				delete: function() {
					var _this = this;
					bootbox.dialog({
						title: 'Warning!',
						message: 'Are you sure to Delete this Service?',
						buttons: {
							success: {
								label: 'Delete',
								className: 'btn btn-important',
								callback: function() {
									$.ajax({
										type: 'GET',
										url: CONF.url_delete_service,
										data: {
											id: _this.model.get('serviceId')
										},
										success: function(result) {
											if(result.status) {
												_this.$el.fadeOut(function() {
													this.remove();
													location.reload();
												});
											} else {
												notification.autoHideError(result.message);
											}
										}
									});	
								}
							},
							cancle: {
								label: 'Cancel',
								className: 'btn btn-primary'
							}
						}
					});
				}
			});
			
			// Define service colletion
			Service_Collection = Backbone.Collection.extend({
				model: Service_Model,
				methodUrl: {
					'read': CONF.url_fetch_collection,
					'create': '',
					'update': '',
					'delete': ''
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				},
			});

			var service_collection = new Service_Collection;

			self.Model = Service_Model;
			self.View = Service_View;
			self.Collection = Service_Collection;

			self.service_collection = service_collection;

			_.bindAll(self, 'addAll', 'addOne');

			service_collection.on('set', self.addAll);
			service_collection.on('add', self.addOne);

			self.getAll();
		},
		/*
		* Fetch all services add to collection
		*/
		getAll: function() {
			var self = this;
			self.conf.EL.empty();
			self.service_collection.reset();
			self.service_collection.fetch();
		},
		/*
		* Add all services
		*/
		addAll: function() {
			var self = this;
			self.service_collection.each(self.addOne);
		},
		/*
		* Add one service, init service view
		* @param {Object} Backbone mdoel
		*/
		addOne: function(model) {
			var	EL = this.conf.EL,
				view = new this.View({model: model});

			EL.append(view.render().el);
		},
		/*
		* Hide the warn tip
		*/
		hideWarn: function() {
			$('.warn-tip').hide();
		},
		/*
		* Show the warn tip, when user have changed the form filed
		*/
		showWarn: function() {
			var EL = this.conf.EL,
				$target = EL.find('tr.disabled').nextAll().find('.js-update-service'),
				right = 220,
				top = $target.offset().top - $target.height() - $('.warn-tip').height();
			$('.warn-tip').css({
				right: right,
				top: top
			}).show();
		},
		/*
		* Submit the form add new activity
		*/
		create: function() {
			var self = this,
				CONF = self.conf,
				data = CONF.formTarget.serializeArray(),
				service = new self.Model;

			_.each(data, function(obj, index) {
				service.set(obj['name'], obj['value']);
			});

			// Remove no needed param
			service.unset('activityList');

			service.sync('create', service, {
				success: function(result) {
					if(result.status) {
						self.hideForm();
						self.getAll();
						self.is_changed = false;
					} else {
						CONF.formTarget.find('.form-msg').text(result.message).show();
					}
				}
			});
		},
	}
	return Service;
});