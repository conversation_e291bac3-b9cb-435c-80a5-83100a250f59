define([
	'jquery', 
	'select2', 
	'icheck', 
	'bootbox',
	'mustache',
	'underscore', 
	'backbone',
	'notification'
], function($, select2, iCheck, bootbox, Mustache, _, Backbone, notification) {
	function DualInstructor(opts) {
		// <PERSON><PERSON><PERSON> set
		this.defaults = {
			
			url_update_serivce: 'centralized/updateDualInstructor.htm',
			url_delete_service: 'centralized/deleteDualInstructor.htm',
		
			formTarget: $('.js-dual-form'),
			
			EL: $('.js-dual-form')

		
		}	

		this.init(opts);
		
	}

	DualInstructor.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			
			var self = this;
			self.conf = $.extend(self.defaults, opts);
			
			/*this.conf = $.extend(this.defaults, opts);
			this.DualInstructorForm();
			
			var self = this,
			CONF = self.conf,
			EL = CONF.EL;*/
			
		
		},
		/*
		* Generate the add service form
		*/
		DualInstructorForm: function() {
			var self = this,
				CONF = self.conf,
				DualInstructor_Model;

			// Define form model
			DualInstructor_Model = Backbone.Model.extend({
				initialize: function() {
					
					var _this = this,
					formEL = _this.formEL;
				},	
				/*
				* Clear the form filed, when form closed
				*/
				clear: function() {
					var formEL = this.formEL;
					formEL.externalId.val('');									
				},
				
				
				getParams: function() {
					var _this = this,
						formEL = _this.formEL,						
						inputExternalId = formEL.externalId.val(),						
						locationId = formEL.locationId.val();
									
					return {
						externalId: inputExternalId,
						locationId: locationId						
					}
				},
				
				resetParams: function(){
					
					var _this = this,									
					formEL = _this.formEL;
					
					
					formEL.locationId.val('-1');
					formEL.externalId.val('');
					
				},
				
				formEL: {
					'externalId': CONF.formTarget.find('input[name="instructorExternalID"]'),
					'locationId':  CONF.formTarget.find('select[name="location"]')					
				}				
			});
			
			
			self.dualinstructormodel = new DualInstructor_Model;
		
			return self;
			
			

	},
	
	
	showForm: function() {
		var self = this,		
		newreport = this.dualinstructormodel,		
		params = newreport.getParams(),
		CONF = self.conf,
		EL = CONF.EL,
		externalId = params.externalId,
		locationId = params.locationId;	
		
		if(params.externalId == '') {
			notification.autoHideError('Please enter Instructor ID and select the location');
			return;
		}

		if(params.locationId == -1) {
			notification.autoHideError('Please enter Instructor ID and select the location');
			return;
		}
		
		
		   $.ajax({
               url: CONF.url_update_serivce,
               type: 'POST',
               data:  {
                   'locationId': locationId,		                                  
                   'externalId' : externalId
                   
               },
               success: function(result) {	                                	
                	notification.autoHideError(result.message);
             
               }
           });
	},
	
	
	removeAccess: function() {
		var self = this,		
		newreport = this.dualinstructormodel,		
		params = newreport.getParams(),
		CONF = self.conf,
		EL = CONF.EL,
		externalId = params.externalId,
		locationId = params.locationId;	
		
		if(params.externalId == '') {
			notification.autoHideError('Please enter Instructor ID and select the location');
			return;
		}

		if(params.locationId == -1) {
			notification.autoHideError('Please enter Instructor ID and select the location');
			return;
		}
		
		
		   $.ajax({
               url: CONF.url_delete_service,
               type: 'POST',
               data:  {
                   'locationId': locationId,		                                  
                   'externalId' : externalId
                   
               },
               success: function(result) {	                                	
                	notification.autoHideError(result.message);
             
               }
           });
	},
	
	reset: function () {
		var _this = this,
		newreport = _this.dualinstructormodel;
		
		newreport.resetParams();
		
		
		
	}
  }		
	return DualInstructor;
});