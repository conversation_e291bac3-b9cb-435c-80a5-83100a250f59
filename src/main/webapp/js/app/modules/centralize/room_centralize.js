define([
	'jquery', 
	'select2', 
	'icheck', 
	'bootbox',
	'mustache',
	'underscore', 
	'backbone',
	'notification'
], function($, select2, iCheck, bootbox, Mustache, _, Backbone, notification) {
	function Room(opts) {
		// Default set
		this.defaults = {
			url_fetch_roomTemplate: 'roomTemplate/loadRoomTemplate.htm',

			url_fetch_services: 'roomTemplate/loadRoomTemplateServices.htm',

			url_fetch_activity: 'roomTemplate/loadRoomTemplateActivities.htm',

			url_create_room_template: 'roomTemplate/createTemplateRoom.htm',

			url_fetch_model: 'roomTemplate/loadRoomTempDetail.htm',

			url_update_room_template: 'roomTemplate/updateRoomTemplate.htm',

			url_delete_room_template: 'roomTemplate/deleteRoomTemplate.htm',

			url_get_unselected_activity: 'roomTemplate/getUnselectedActivities.htm',
			
			url_get_room_type: 'roomTemplate/selectRoomType.htm',

			formTarget: $('.js-room-template-form'),
			

			EL: $('.room-list')
		}	
		
		_.templateSettings = {
			interpolate: /\{\{(.+?)\}\}/g
		};
		
		_.helper = function(obj, type) {
			//For GCSS-394
			if(type === 'split') {
				if(obj === 'Y') {
					return 'Yes';
				} else {
					return 'N/A';
				}
			} else if(type === 'roomSize'){
				if(obj === '') {
					return 'N/A';
				} else {
					return obj;
				}
			} else {
				if(obj === 'Y') {
					return 'Enabled';
				} else {
					return 'Disabled';
				}
			}
		}
		
		this.init(opts);

		this.is_edit = false;

		this.is_changed = false;
	}

	Room.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			var self = this;
			self.conf = $.extend(self.defaults, opts);
		},
		/*
		* Generate the add roomtemplate form
		*/
		newTemplateForm: function() {
			var self = this,
				CONF = self.conf,
				NewRoom_Model;

			NewRoom_Model = Backbone.Model.extend({
				initialize: function() {
					var _this = this,
						formEL = _this.formEL;
					// If roomType is lesson hide the room size
					formEL.roomType.select2().on('change', function(e) {
						//For GCSS-363
						formEL.roomSize.select2('enable', false);
						$.ajax({
							url: CONF.url_get_room_type,
							data: {
								roomTypeId: $(this).val()
							},
							success: function(result) {
								if(result.roomSizeList.length) {
									formEL.roomSize.select2({
										data: result.roomSizeList,
										id: function(obj) {
											return obj.roomSizeId;
										},
										formatResult: function(obj) {
											return obj.roomSizeName;
										},
										formatSelection: function(obj) {
											return obj.roomSizeName;
										}
									}).select2('enable', true);
									formEL.roomSize.parent().parent().show();
								} else {
									formEL.roomSize.select2('val', '').select2('enable', true);
									formEL.roomSize.parent().parent().hide();
								}
								
							}
						});
						//End of GCSSS-363
						if($(this).find('option:selected').data('split') == 'Y') {
							formEL.isSplitRoom.attr('disabled', false).iCheck('enable');
							CONF.formTarget.find('.js-split-room-wrap').show();
						} else {
							formEL.isSplitRoom.attr('disabled', true).iCheck('disable');
							CONF.formTarget.find('.js-split-room-wrap').hide();
						}
					});
					formEL.roomSize.select2({
						data: []
					});
					formEL.isSplitRoom.iCheck();
					formEL.enabled.iCheck().iCheck('check');

					this.changeService();

					_.bindAll(this, 'addService','removeService');

					// Add service function
					CONF.formTarget.find('.js-add-service').on('click', function() {
						_this.addService();
					});

					// Remove service function
					CONF.formTarget.find('.js-service-list').on('click', '.js-remove-service', function(e) {
						$(this).parent().fadeOut(function() {
							$(this).remove();
						});
						_this.removeService($(this).data('id'));
					});

					// Add activity function
					CONF.formTarget.find('.js-add-activity').on('click', function() {
						_this.addActivity();	
					});

					// Remove activity function
					CONF.formTarget.find('.js-activity-list').on('click', '.js-remove-activity', function(e) {
						$(this).parent().fadeOut(function() {
							$(this).remove();
						});
						_this.removeActivity($(this).data('id'));
					});
				},
				/*
				* Dynamic to get the service
				*/
				changeService: function() {
					var _this = this;
					var formEL = _this.formEL;
					formEL.servicesList.select2('val', '').select2({
						placeholder: 'Select Service',
						data: [],
					}).select2('readonly', true);
					$.ajax({
						url: CONF.url_fetch_services,
						type: 'GET',
						data: {
							serviceString: formEL.services.val()
						},
						success: function(result) {
							formEL.servicesList.select2({
								placeholder: 'Select Service',
								data: result.serviceList,
								id: function(obj) {
									return obj.serviceId;
								},
								formatResult: function(obj) {
									return obj.serviceName;
								},
								formatSelection: function(obj) {
									return obj.serviceName;
								}
							}).select2('val', '').select2('readonly', false);
							_this.changeActivity();
						}
					});
				},
				/*
				* Add service
				*/
				addService: function() {
					var formEL = this.formEL,
						tmpl = $('#tmpl_service_item').html(),
						data = formEL.servicesList.select2('data');

					if(data == null || data.length == 0) return;
					CONF.formTarget.find('.js-service-list').append(Mustache.render(tmpl, data));
					var val = formEL.services.val().split(',');
					val.push(data.serviceId);
					if(val[0] == '') val.shift();
					formEL.services.val(val.toString());
					this.changeService();
				},
				/*
				* Remove service
				*/
				removeService: function(id) {
					var formEL = this.formEL,
						val = formEL.services.val().split(',');
						currentVal = _.filter(val, function(num) {
						return num != id;
					});
					formEL.services.val(currentVal.toString());
					this.changeService();
				},
				/*
				* Dynamic to get the activity
				*/
				changeActivity: function() {
					var formEL = this.formEL;
					formEL.activityList.select2('val', '').select2({
						placeholder: 'Select Activity',
						data: []
					}).select2('readonly', true);
					$.ajax({
						url: CONF.url_fetch_activity,
						type: 'GET',
						data: {
							serviceString: formEL.services.val(),
							activityString: formEL.activities.val()
						},
						success: function(result) {
							formEL.activityList.select2('val', '').select2({
								placeholder: 'Select Activity',
								data: result.activityList,
								id: function(obj) {
									return obj.activityId;
								},
								formatResult: function(obj) {
									return obj.activityName;
								},
								formatSelection: function(obj) {
									return obj.activityName;
								}
							}).select2('val', '').select2('readonly', false);
							CONF.formTarget.find('.js-activity-list').empty();
							formEL.activities.val('');
							var tmpl = $('#tmpl_activity_item').html(),
								activityList = [];
								activityHTML = '';
							result.selectedActivityList.forEach(function(obj) {
								activityHTML += Mustache.render(tmpl, obj);
								activityList.push(obj.activityId);
								CONF.formTarget.find('.js-activity-list').html(activityHTML);
							});
							formEL.activities.val(activityList.toString());
						}
					});
				},
				/*
				* Add activity
				*/
				addActivity: function() {
					var formEL = this.formEL,
						tmpl = $('#tmpl_activity_item').html(),
						data = formEL.activityList.select2('data');

					if(data == null || data.length == 0) return;
					CONF.formTarget.find('.js-activity-list').append(Mustache.render(tmpl, data));
					var val = formEL.activities.val().split(',');
					val.push(data.activityId);
					if(val[0] == '') val.shift();
					formEL.activities.val(val.toString());
					this.changeActivity();
				},
				/*
				* Remove activity
				*/
				removeActivity: function(id) {
					var formEL = this.formEL,
						val = formEL.activities.val().split(',');
						currentVal = _.filter(val, function(num) {
						return num != id;
					});
					formEL.activities.val(currentVal.toString());
					this.changeActivity();
				},
				/*
				* Clear the form data, when form closed
				*/
				clear: function() {
					var formEL = this.formEL;
					formEL.roomType.select2('val', '');
					
					//For GCSS-363
					formEL.roomSize.select2('val', '').select2('enable', true);
					formEL.roomSize.parent().parent().show();
					
					formEL.services.val('');
					formEL.activities.val('');
					this.changeService();
					formEL.roomTemplateName.val('');
					CONF.formTarget.find('.js-service-list').empty();
					CONF.formTarget.find('.js-activity-list').empty();
					formEL.isSplitRoom.iCheck('uncheck').iCheck('enable');
					CONF.formTarget.find('.js-room-split-wrap').show();
				},
				formEL: {
					'roomTemplateName': CONF.formTarget.find('input[name="roomTemplateName"]'),
					'roomType': CONF.formTarget.find('select[name="roomType"]'),
					'roomSize': CONF.formTarget.find('input[name="roomSize"]'),
					'isSplitRoom': CONF.formTarget.find('input[name="isSplitRoom"]'),
					'activities': CONF.formTarget.find('input[name="activities"]'),
					'activityList': CONF.formTarget.find('input[name="activityList"]'),
					'services': CONF.formTarget.find('input[name="services"]'),
					'servicesList': CONF.formTarget.find('input[name="servicesList"]'),
					'enabled': CONF.formTarget.find('input[name="enabled"]')
				}
			});

			self.newroom = new NewRoom_Model;
			return self;
		},
		/*
		* Slidedown to show the form 
		*/
		showForm: function() {
			var CONF = this.conf;
			CONF.formTarget.parent().slideDown();
		},
		/*
		* Slideup to hide the form
		*/
		hideForm: function() {
			var CONF = this.conf;
			CONF.formTarget.parent().slideUp();
			CONF.formTarget.find('.form-msg').hide();
			
			//This make it to show 'roomSize' field when click to add roomTemplate
			CONF.formTarget.find('select[name="roomSize"]').parent().parent().show();
			//This make it to show 'split' field when click to add roomTemplate
			CONF.formTarget.find('.js-split-room-wrap').show();
			//CONF.formTarget.find('input[name="isSplitRoom"]').parent().parent().parent().parent().parent().show();
			
			this.newroom.clear();
			$('.js-show-form').removeClass('active');
		},
		/*
		* Render room list
		*/
		renderList: function() {
			var self = this,
				CONF = self.conf,
				Room_Model, Room_View, Room_Collection;

			// Define room model
			Room_Model = Backbone.Model.extend({
				methodUrl: {
					'read': CONF.url_fetch_model,
					'create': CONF.url_create_room_template,
					'update': CONF.url_update_room_template,
					'delete': ''
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				}
			});

			// Define room view
			Room_View = Backbone.View.extend({
				initialize: function() {
					return this;
				},
				events: {
					'click .js-edit-room': 'edit',
					'click .close-edit-panel': 'close',
					'click .js-update-room': 'update',
					'click .js-delete-roomTemplate': 'delete',
					'change input[name="roomTemplateName"]': 'changeTemplateName',
					'change input[name="roomType"]': 'changeRoomType',
					'change input[name="roomSize"]': 'changeSize',
					'click .js-add-service': 'addService',
					'click .js-remove-service': 'removeService',
					'click .js-add-activity': 'addActivity',
					'click .js-remove-activity': 'removeActivity',
					'ifChanged input[name="globalChange"]': 'changeGlobal',
					'ifChanged input[name="enabled"]': 'changeActive',
					'ifChanged input[name="isSplitRoom"]': 'changeSplit'
				},
				tagName: 'table',
				className: 'table table-list table-fixed',
				template: $('#tmpl_room').html(),
				render: function() {
//					this.$el.html(Mustache.render(this.template, this.model.toJSON()));
					this.$el.html(_.template(this.template, this.model.toJSON()));
					return this;
				},
				edit: function(e) {
					var _this = this,
						$this = $(e.currentTarget);

					if($this.hasClass('editing')) return;

					if(self.is_changed) {
						bootbox.dialog({
							title: 'Warning!',
							message: 'You have unsaved information. Do you want to continue?',
							buttons: {
								success: {
									label: 'Continue',
									className: 'btn btn-important',
									callback: function() {
										CONF.EL.find('.editing').removeClass('editing');
										CONF.EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
										self.hideWarn();
										_this.getOne();
									}
								},
								cancle: {
									label: 'Cancel',
									className: 'btn btn-primary',
									callback: function() {
										self.showWarn();
										return;
									}
								}
							}
						});
					} else {
						CONF.EL.find('.editing').removeClass('editing');
						CONF.EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
						$this.addClass('editing');
						_this.getOne();		
					}
				},
				/*
				* Ajax request to get the detail room 
				*/
				getOne: function() {
					var _this = this;
					$.ajax({
						url: CONF.url_fetch_model,
						type: 'GET',
						data: {
							roomTemplateId: _this.model.get('roomTemplateId')
						},
						success: function(result) {
							// If session time out will get the login.html page
							// So, if response text has the string 'DOCTYPE' will reload page to jump to the login page
							if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
							
							//If the record had been deleted,show the message from server and remove th invalid record
							if(result.message) {
								notification.autoHideError(result.message);
								_this.$el.fadeOut(function(){
									this.remove();
								});
								return;
							}
							if(result.isSplitRoom ==='N') result.isSplitRoom = null;
							_this.model.set(result);
							_this.editRender();
							_this.$el.find('tr').eq(0).addClass('disabled').nextAll().show();
							self.is_changed = false;
							return _this;
						}
					});
				},
				/*
				* Open the edit panel, render the form filed
				* @param {Object} room detail data
				*/
				editRender: function() {
					var _this = this,
						tmpl_service_item = $('#tmpl_service_item').html(),
						tmpl_activity_item = $('#tmpl_activity_item').html();

					_this.$el.find('input[name="roomTemplateName"]').val(_this.model.get('roomTemplateName'));

					_this.$el.find('input[name="roomType"]').select2({
						data: _this.model.get('roomTypeList'),
						id: function(obj) {
							return obj.roomTypeId;
						},
						formatResult: function(obj) {
							return obj.roomType;
						},
						formatSelection: function(obj) {
							return obj.roomType;
						}
					}).select2('val', _this.model.get('roomTypeId'));
					if(_this.model.get('roomSizeList').length) {
						_this.$el.find('input[name="roomSize"]').select2({
							data: _this.model.get('roomSizeList'),
							id: function(obj) {
								return obj.roomSizeId;
							},
							formatResult: function(obj) {
								return obj.roomSizeName;
							},
							formatSelection: function(obj) {
								return obj.roomSizeName;
							}
						}).select2('val', _this.model.get('roomSizeId'));
					} else {
						_this.$el.find('.js-room-size-wrap').hide();
					}
					
					var canSplitRoom = _.filter(_this.model.get('roomTypeList'), function(obj) {
						return obj.roomTypeId == _this.model.get('roomTypeId');
					});
					if(canSplitRoom[0].canSplitRoom == 'N') {
						_this.$el.find('.js-split-room-wrap').hide();
					}
					this.$el.find('input[name="servicesList"]').select2({
						data: _this.model.get('unSelectedServices'),
						id: function(obj) {
							return obj.serviceId;
						},
						formatResult: function(obj) {
							return obj.serviceName;
						},
						formatSelection: function(obj) {
							return obj.serviceName;
						} 	
					});

					this.$el.find('.js-service-list').empty();
					_.each(this.model.get('selectedServices'), function(obj) {
						_this.$el.find('.js-service-list').append(Mustache.render(tmpl_service_item, obj));
					});

					this.$el.find('input[name="activityList"]').select2({
						data: _this.model.get('unSelectedActivities'),
						id: function(obj) {
							return obj.activityId;
						},
						formatResult: function(obj) {
							return obj.activityName;
						},
						formatSelection: function(obj) {
							return obj.activityName;
						} 	
					});

					this.$el.find('.js-activity-list').empty();
					_.each(this.model.get('selectedActivities'), function(obj) {
						_this.$el.find('.js-activity-list').append(Mustache.render(tmpl_activity_item, obj));
					});

					this.$el.find('input[name="globalChange"]').iCheck().iCheck('uncheck');

					if(this.model.get('enabled') == 'Y') {
						this.$el.find('input[name="enabled"]').iCheck().iCheck('uncheck');
					} else {
						this.$el.find('input[name="enabled"]').iCheck().iCheck('check');
					}
					if(this.model.get('isSplitRoom') == 'Y') {
						this.$el.find('input[name="isSplitRoom"]').iCheck().iCheck('check');
					} else if(this.model.get('parentRoom')) {
						this.$el.find('input[name="isSplitRoom"]').iCheck().iCheck('disable');
					} else {
						this.$el.find('input[name="isSplitRoom"]').iCheck().iCheck('uncheck');
					}
										
					this.$el.find('.js-edit-room').addClass('editing');
				},
				changeTemplateName: function(e) {
					self.is_changed = true;
					this.model.set('roomTemplateName', $(e.currentTarget).val());
				},
				changeRoomType: function(e) {
					self.is_changed = true;
					var _this = this,
						roomSize = this.$el.find('input[name="roomSize"]'),
						roomSizeWrapper = this.$el.find('.js-room-size-wrap');
					
					if($(e.currentTarget).select2('data').canSplitRoom == 'Y') {
						_this.$el.find('.js-split-room-wrap').show();
					} else {
						_this.$el.find('.js-split-room-wrap').hide();
					}
					
					_this.model.set('roomTypeId', $(e.currentTarget).val());
					
					roomSize.select2('enable', false);
					$.ajax({
						url: CONF.url_get_room_type,
						data: {
							roomTypeId: _this.model.get('roomTypeId')
						},
						success: function(result) {
							_this.model.set('roomSizeId' ,'');
							if(result.roomSizeList.length) {
								roomSize.select2('val', '').select2({
									data: result.roomSizeList,
									id: function(obj) {
										return obj.roomSizeId;
									},
									formatResult: function(obj) {
										return obj.roomSizeName;
									},
									formatSelection: function(obj) {
										return obj.roomSizeName;
									}
								}).select2('enable', true);
								roomSizeWrapper.show();
							} else {
								roomSize.select2('val', '').select2('enable', true);
								roomSizeWrapper.hide();
							}
							
						}
					});
				},
				changeSize: function(e) {
					self.is_changed = true;
					this.model.set('roomSizeId', $(e.currentTarget).val());
				},
				changeGlobal: function(e) {
					self.is_changed = true;
					if(e.currentTarget.checked) {
						this.model.set('globalChange', true);
					} else {
						this.model.set('globalChange', false);
					}
				},
				changeActive: function(e) {
					self.is_changed = true;
					var _this = this;
					if(e.currentTarget.checked) {
						_this.model.set('enabled', null);	
					} else {
						_this.model.set('enabled', 'Y');
					}
				},
				changeSplit: function(e) {
					self.is_changed = true;
					var _this = this;
					if(e.currentTarget.checked) {
						_this.model.set('isSplitRoom', 'on');
					} else {
						_this.model.set('isSplitRoom', null);
					}
				},
				changeService: function() {
					var _this = this;
					self.is_changed = true;
					_this.$el.find('input[name="servicesList"]').select2('val', '').select2({
						data: _this.model.get('unSelectedServices'),
						id: function(obj) {
							return obj.serviceId;
						},
						formatResult: function(obj) {
							return obj.serviceName;
						},
						formatSelection: function(obj) {
							return obj.serviceName;
						}
					})
				},
				changeActivity: function() {
					self.is_changed = true;
					var _this = this;
					_this.$el.find('input[name="activityList"]').select2('val', '').select2({
						data: _this.model.get('unSelectedActivities'),
						id: function(obj) {
							return obj.activityId;
						},
						formatResult: function(obj) {
							return obj.activityName;
						},
						formatSelection: function(obj) {
							return obj.activityName;
						}
					});
				},
				addService: function() {
					self.is_changed = true;
					var _this = this,
						data = this.$el.find('input[name="servicesList"]').select2('data'),
						unSelectedServices,
						tmpl = $('#tmpl_service_item').html();

					if(data == null || data.length == 0) return;

					unSelectedServices = _.filter(this.model.get('unSelectedServices'), function(obj) {
						return obj.serviceId != data.serviceId;
					});
					this.model.set('unSelectedServices', unSelectedServices);
					this.model.get('selectedServices').push(data);
					
					this.changeService();

					$.ajax({
						url: CONF.url_get_unselected_activity,
						type: 'GET',
						data: {
							id: data.serviceId
						},
						success: function(result) {
							_this.model.set('unSelectedActivities' , _this.model.get('unSelectedActivities').concat(result));
							console.log(_this.model.get('unSelectedActivities'))
							_this.changeActivity();
						}	
					});

					this.$el.find('.js-service-list').append(Mustache.render(tmpl, data));
				},
				removeService: function(e) {
					self.is_changed = true;
					var id = $(e.currentTarget).data('id'),
						unSelectedServices =  _.filter(this.model.get('selectedServices'), function(obj) {
							return obj.serviceId == id;
						}),
						unSelectedActivities = _.filter(this.model.get('unSelectedActivities'), function(obj) {
							return obj.service.serviceId != id;
						}),
						currentList = _.filter(this.model.get('selectedServices'), function(obj) {
							return obj.serviceId != id;
						});

					this.$el.find('.js-remove-activity[data-service="'+id+'"]').click();

					this.model.set('selectedServices', currentList);
					this.model.set('unSelectedServices', this.model.get('unSelectedServices').concat(unSelectedServices));
					this.model.set('unSelectedActivities', unSelectedActivities);

					this.changeActivity();

					this.changeService();

					$(e.currentTarget).parent().fadeOut(function() {
						$(this).remove();
					});
				},
				addActivity: function() {
					self.is_changed = true;
					var data = this.$el.find('input[name="activityList"]').select2('data'),
						unSelectedActivities,
						tmpl = $('#tmpl_activity_item').html();

					if(data == null || data.length == 0) return;

					unSelectedActivities = _.filter(this.model.get('unSelectedActivities'), function(obj) {
						return obj.activityId != data.activityId;
					});

					this.model.set('unSelectedActivities', unSelectedActivities);
					this.model.get('selectedActivities').push(data);
					
					this.changeActivity();

					this.$el.find('.js-activity-list').append(Mustache.render(tmpl, data));
				},
				removeActivity: function(e) {
					self.is_changed = true;
					var id = $(e.currentTarget).data('id'),
						unSelectedActivities =  _.filter(this.model.get('selectedActivities'), function(obj) {
							return obj.activityId == id;
						}),
						currentList = _.filter(this.model.get('selectedActivities'), function(obj) {
							return obj.activityId != id;
						});
					
					this.model.set('selectedActivities', currentList);
					this.model.set('unSelectedActivities', this.model.get('unSelectedActivities').concat(unSelectedActivities));

					this.changeActivity();

					$(e.currentTarget).parent().fadeOut(function() {
						$(this).remove();
					});
				},
				/*
				* Update the room
				*/
				update: function() {
					var _this = this;
					this.$el.find('.js-update-room').attr('disabled', true);//For GCSS-341 to avoid 'double click'
					this.model.sync('update', this.model, {
						success: function(result) {
							if(result.status) {
								_this.model.set(result.object);
//								_this.render();
								self.getAll();
								_this.close();
							} else {
								notification.autoHideError(result.message);
							}
							_this.$el.find('.js-update-room').attr('disabled', false);//For GCSS-341 to avoid 'double click'
						}
					});
				},
				/*
				* Delete the room
				*/
				delete: function() {
					var _this = this;	
					bootbox.dialog({
						title: 'Warning!',
						message: 'Are you sure to Delete this Room Template?',
						buttons: {
							success: {
								label: 'Delete',
								className: 'btn btn-important',
								callback: function() {
									console.log(_this.model.attributes)
									$.ajax({
										type: 'GET',
										url: CONF.url_delete_room_template,
										data: {
											roomTemplateId: _this.model.get('roomTemplateId')
										},
										success: function(result) {
											if(result.status) {
												_this.$el.fadeOut(function() {
													this.remove();
												});
											} else {
												notification.autoHideError(result.message);
											}
										}
									});	
								}
							},
							cancle: {
								label: 'Cancel',
								className: 'btn btn-primary'
							}
						}
					});
				},
				/*
				* Close the edit panel, destory all the select2 or icheck
				*/
				close: function() {
					this.$el.find('.js-edit-room').removeClass('editing');
					self.is_changed = false;
					self.hideWarn();
					this.$el.find('tr').eq(0).removeClass('disabled').nextAll().hide();

					this.$el.find('input[name="roomTemplateName"]').val('');
					this.$el.find('input[name="roomSize"]').select2('destroy');
					this.$el.find('input[name="isSplitRoom"]').iCheck('destroy');
					this.$el.find('input[name="servicesList"]').select2('destroy');
					this.$el.find('input[name="activityList"]').select2('destroy');
					this.$el.find('input[name="globalChange"]').iCheck('destroy');
					this.$el.find('input[name="enabled"]').iCheck('destroy');
				}
			});
			
			// Define room collection
			Room_Collection = Backbone.Collection.extend({
				model: Room_Model,
				methodUrl: {
					'read': CONF.url_fetch_roomTemplate,
					'create': '',
					'update': '',
					'delete': ''
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				},
			});

			var room_collection = new Room_Collection;

			self.Model = Room_Model;
			self.View = Room_View;
			self.Collection = Room_Collection;

			self.room_collection = room_collection;

			_.bindAll(self, 'addAll', 'addOne');

			room_collection.on('set', self.addAll);
			room_collection.on('add', self.addOne);

			self.getAll();

		},
		/*
		* Fetch all rooms	
		*/
		getAll: function() {
			var self = this,
				CONF = self.conf;
			CONF.EL.empty();	
			self.room_collection.reset();
			self.room_collection.fetch();
		},
		/*
		* Add all rooms
		*/
		addAll: function() {
			var self = this;
			self.room_collection.each(self.addOne);
		},
		/*
		* Add one room, init room view
		*/
		addOne: function(model) {
			var	EL = this.conf.EL,
				view = new this.View({model: model});

			EL.append(view.render().el);
		},
		/*
		* Show the warn tip, when user have changed the form filed
		*/
		showWarn: function() {
			var EL = this.conf.EL,
				$target = EL.find('tr.disabled').nextAll().find('.js-update-room'),
				right = 220,
				top = $target.offset().top - $target.height() - $('.warn-tip').height();
			$('.warn-tip').css({
				right: right,
				top: top
			}).show();
		},
		/*
		* Hide the warn tip
		*/
		hideWarn: function() {
			$('.warn-tip').hide();
		},
		/*
		* Submit the form add new room
		*/
		createRoom: function(callback) {
			var self = this,
				CONF = self.conf,
				data = CONF.formTarget.serializeArray(),
				room = new self.Model;

			_.each(data, function(obj, index) {
				room.set(obj['name'], obj['value']);
			});

			// Remove no needed param
			room.unset('servicesList');
			room.unset('activityList');

			room.sync('create', room, {
				success: function(result) {
					if(result.status) {
						self.hideForm();
						self.getAll();
						self.is_changed = false;
					} else {
						CONF.formTarget.find('.form-msg').text(result.message).show();
					}
				}
			});
		}
	}
	return Room;
});