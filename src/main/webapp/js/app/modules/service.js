define([
	'jquery', 
	'select2', 
	'icheck', 
	'bootbox',
	'mustache',
	'underscore', 
	'backbone',
	'notification'
], function($, select2, iCheck, bootbox, Mustache, _, Backbone, notification) {
	function Service(opts) {
		// De<PERSON>ult set
		this.defaults = {
			url_fetch_collection: 'profileService/loadProfileServiceList.htm',

			url_create_model: 'profileService/addProfileService/saveProService.htm',
			url_fetch_activity: 'profileService/addProfileService/addSelectedActivity.htm',

			url_getActivityByService: 'profileService/addProfileService/loadActivitiesInServiceList.htm',
			
			url_fetch_services: 'profileService/addProfileService/loadAddedServiceList.htm',
			url_fetch_service_detail: 'profileService/loadProfileServiceDetail.htm',
			url_update_serivce: 'profileService/updatepProfileService.htm',
			url_delete_service: 'profileService/loadProfileServiceDetail.htm',
			url_delete_serivce: 'profileService/deleteProfileService.htm',

			formTarget: $('.js-service-form'),

			EL: $('.service-list')
		}	

		this.init(opts);

		this.is_form_init = false;

		this.is_changed = is_changed;
	}

	Service.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			var self = this;
			self.conf = $.extend(self.defaults, opts);
		},
		/*
		* Generate the add service form
		*/
		newServiceForm: function() {
			if(this.is_form_init) return;

			var self = this,
				CONF = self.conf,
				NewService_Model;

			// Define the form model
			NewService_Model = Backbone.Model.extend({
				initialize: function() {
					var _this = this,
						formEL = this.formEL;

					// ServiceType is dynamic to show
					// This changed will render new activity select
					formEL.serviceType.select2({
						ajax: {
							url: CONF.url_fetch_services,
							data: function(term, page) {
								return {
									// serviceString: ''
								}
							},
							results: function(data, page) {
								return {
									results: data
								}
							}
						},
						id: function(obj) {
							return obj.serviceId;
						},
						formatResult: function(obj) {
							return obj.serviceName;
						},
						formatSelection: function(obj) {
							return obj.serviceName;
						}
					}).on('change', function(e) {
						var value = $(this).val();
						$.ajax({
							url: CONF.url_getActivityByService,
							data: {
								serviceId: value
							},
							success: function(result) {
								formEL.activity.val('');
								CONF.formTarget.find('.js-activity-list').empty();

								formEL.activityList.select2('val', '').select2({
									placeholder: 'Select Available Activity',
									data: result,
									id: function(obj) {
										return obj.activityId
									},
									formatResult: function(obj) {
										return obj.activityName;
									},
									formatSelection: function(obj) {
										return obj.activityName;
									}
								});
							}
						});
					});
					formEL.activityList.select2({
						placeholder: 'Select Available Activity',
						data: [],
						id: function(obj) {
							return obj.activityId;
						},
						formatResult: function(obj) {
							return obj.activityName;
						},
						formatSelection: function(obj) {
							return obj.activityName;
						}
					});
					formEL.enable.iCheck();

					_.bindAll(this, 'addActivity','removeActivity');

					// Add activity button function
					CONF.formTarget.find('.js-add-activity-item').on('click', function(e) {
						e.preventDefault();
						if(formEL.activityList.select2('val') == '') return;
						_this.addActivity();
						_this.changeActivity();
					});

					// Remove acvitiy button function
					CONF.formTarget.find('.js-activity-list').on('click', '.js-remove-activity', function(e) {
						$(this).parent().fadeOut(function() {
							$(this).remove();
						});
						_this.removeActivity($(this).data('id'));
						_this.changeActivity();
					});

					self.is_form_init = true;
				},
				/*
				* Change activity function
				* Ajax request to get new available activity
				*/
				changeActivity: function() {
					var formEL = this.formEL,
						CONF = self.conf;
					formEL.activityList.select2('readonly', true);

					$.ajax({
						url: CONF.url_fetch_activity,
						data: {
							activityString: formEL.activity.val(),
							serviceId: formEL.serviceType.val()
						},
						success: function(result) {
							var data = [];
							if(result.activityList && result.activityList.length > 0) {
								data = result.activityList
							}
							formEL.activityList.select2('val', '').select2({
								placeholder: 'Select Available Activity',
								data: data,
								id: function(obj) {
									return obj.activityId;
								},
								formatResult: function(obj) {
									return obj.activityName;
								},
								formatSelection: function(obj) {
									return obj.activityName;
								}
							}).select2('readonly', false);	 
						}
					});
				},
				/*
				* Add activity function
				*/
				addActivity: function() {
					var formEL = this.formEL,
						tmpl = $('#tmpl_activity_item').html(),
						data = formEL.activityList.select2('data'),
						CONF = self.conf;

					if(data == null || data.length == 0) return;
					CONF.formTarget.find('.js-activity-list').append(Mustache.render(tmpl, data));
					var val = formEL.activity.val().split(',');
					val.push(data.activityId);
					if(val[0] == '') val.shift();
					formEL.activity.val(val.toString());
				},
				/*
				* Remove activity function
				* @param {String} the removed activityId
				*/
				removeActivity: function(id) {
					var formEL = this.formEL,
						val = formEL.activity.val().split(',');
						currentVal = _.filter(val, function(num) {
						return num != id;
					});
					formEL.activity.val(currentVal.toString())
				},
				/*
				* Clear the form filed, when form closed
				*/
				clear: function() {
					var formEL = this.formEL,
						CONF = self.conf;
					formEL.serviceType.select2('val', '');
					formEL.activityList.select2('val', '');
					formEL.activity.val('');
					formEL.enable.iCheck('uncheck');
					CONF.formTarget.find('.js-activity-list').empty();
				},
				defaults: {
					enable: ''
				},
				formEL: {
					'serviceType': CONF.formTarget.find('input[name="serviceType"]'),
					'activityList':  CONF.formTarget.find('input[name="activityList"]'),
					'activity': CONF.formTarget.find('input[name="activity"]'),
					'enable':  CONF.formTarget.find('input[name="enable"]')
				}
			});

			self.newservice = new NewService_Model;
			return self;
		},
		/*
		* Slidedown to show the form 
		*/
		showForm: function() {
			var	CONF = this.conf;
			CONF.formTarget.parent().slideDown();
		},
		/*
		* Slideup to hide the form
		*/
		hideForm: function() {
			var	CONF = this.conf;
			CONF.formTarget.parent().slideUp();
			CONF.formTarget.find('.form-msg').hide();
			$('.js-show-service-form').removeClass('active');
			this.newservice.clear();
		},
		/*
		* Render the service list
		*/
		renderList: function() {
			var self = this,
				CONF = self.conf,
				Service_Model, Service_View, Service_Collection,
				Activity_View;

			// Init the is_change status 
			self.is_changed = false;
			is_changed = self.is_changed;

			// Show loadings
			self.showLoadTip();

			// Defien service model
			Service_Model = Backbone.Model.extend({
				methodUrl: {
					'read': CONF.url_fetch_model,
					'create': CONF.url_create_model,
					'update': CONF.url_update_serivce,
					'delete': CONF.url_delete_serivce
				},
				defaults: {
					enable: false
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				}
			});
			// Define service view
			Service_View = Backbone.View.extend({
				initialize: function() {
					return this;
				},
				events: {
					'click .edit-service': 'edit',
					'click .close-edit-panel': 'close',
					'ifChanged input[name="active"]': 'changeActive',
					'click .js-delete-service': 'delete',
					'click .js-update-service': 'update'
				},
				tagName: 'table',
				className: 'table table-list table-fixed',
				template: $('#tmpl_service').html(),
				render: function() {
					this.$el.html(Mustache.render(this.template, this.model.toJSON()));
					return this;
				},
				edit: function(e) {
					var _this = this,
						$this = $(e.currentTarget);

					if($this.hasClass('editing')) return;

					if(self.is_changed) {
						bootbox.dialog({
							title: 'Warning!',
							message: 'You have unsaved information. Do you want to continue?',
							buttons: {
								success: {
									label: 'Continue',
									className: 'btn btn-important',
									callback: function() {
										CONF.EL.find('.editing').removeClass('editing');
										CONF.EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
										self.hideWarn();
										_this.getOne();
									}
								},
								cancle: {
									label: 'Cancel',
									className: 'btn btn-primary',
									callback: function() {
										self.showWarn();
										return;
									}
								}
							}
						});
					} else {
						CONF.EL.find('.editing').removeClass('editing');
						CONF.EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
						$this.addClass('editing');
						_this.getOne();		
					}
				},
				/*
				* Ajax request to get service detail
				*/
				getOne: function() {
					var _this = this;
					$.ajax({
						url: CONF.url_fetch_service_detail,
						type: 'GET',
						data: {
							id: _this.model.get('serviceId')
						},
						success: function(result) {
							// If session time out will get the login.html page
							// So, if response text has the string 'DOCTYPE' will reload page to jump to the login page
							if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
							_this.model.set(result);
							_this.editRender();
							_this.$el.find('tr').eq(0).addClass('disabled').nextAll().show();
							
							self.is_changed = false;
							is_changed = self.is_changed;
						}
					});
				},
				/*
				* Render the edit panel, bind change function to the form filed
				*/
				editRender: function() {
					var	activity_view = new Activity_View({model: this.model});

					this.$el.find('.edit-service').addClass('editing');
					this.$el.find('select[name="requireInstructor"]').select2();
					this.$el.find('select[name="activityAvailable"]').select2();
					
					if(this.model.get('enable')) {
						this.$el.find('input[name="active"]').iCheck().iCheck('indeterminate');
					} else {
						this.$el.find('input[name="active"]').iCheck().iCheck('determinate');
					}
					this.$el.find('.js-activity-wrap').empty().append(activity_view.render().el);
				},
				/*
				* Close the edit panel, destory all form filed
				*/
				close: function() {
					this.$el.find('.edit-service').removeClass('editing');
					this.$el.find('tr').eq(0).removeClass('disabled').nextAll().hide();
					this.$el.find('select[name="requireInstructor"]').select2('destroy');
					this.$el.find('select[name="activityAvailable"]').select2('destroy');
					this.$el.find('input[name="active"]').iCheck('destroy');
					self.hideWarn();
					
					self.is_changed = false;
					is_changed = self.is_changed;
					
					return this;
				},
				/*
				* Change active function
				*/
				changeActive: function(e) {
					
					self.is_changed = true;
					is_changed = self.is_changed;
					
					if(e.currentTarget.checked) {
						this.model.set('enable', false);
					} else {
						this.model.set('enable', true);
					}	
				},
				/*
				* Update the service model
				*/
				update: function(callback) {
					var _this = this;
					this.model.unset('avaibleAct');
					this.$el.find('.js-update-service').attr('disabled', true);//For GCSS-341 to avoid 'double click'
					this.model.sync('update', this.model, {
						success: function(result) {
							if(result.status) {
								_this.model.set(result.object);
								// TODO
								// self.getAll();
								
								/**
								 * The next line called the Service/Activity tab click which would check 'is_changed'
								 */
								is_changed = false;
								
								$('.js-show-service-activity').click();
								_this.$el.find('.edit-service').removeClass('editing');
								self.hideWarn();
							} else {
								notification.autoHideError(result.message);
							}
							_this.$el.find('.js-update-service').attr('disabled', false);//For GCSS-341 to avoid 'double click'
						}
					});
				},
				/*
				* delete the service model
				*/
				delete: function(callback) {
					var _this = this;
					bootbox.dialog({
						title: 'Warning!',
						message: 'Are you sure to Delete this Service?',
						buttons: {
							success: {
								label: 'Delete',
								className: 'btn btn-important',
								callback: function() {
									$.ajax({
										type: 'GET',
										url: CONF.url_delete_serivce,
										data: {
											serviceId: _this.model.get('serviceId')
										},
										success: function(result) {
											if(result.status) {
												_this.$el.fadeOut(function() {
													this.remove();
												});
											} else {
												notification.autoHideError(result.message);
											}
										}
									});	
								}
							},
							cancle: {
								label: 'Cancel',
								className: 'btn btn-primary'
							}
						}
					});
				}
			});
			
			Activity_View = Backbone.View.extend({
				events: {
					'click .js-btn-add': 'add'
					//'click .js-remove-activity': 'remove'
				},
				className: '',
				template: $('#tmpl_service_activity').html(),
				subTemplate: $('#tmpl_service_activity_item').html(),
				render: function() {
					this.$el.html(Mustache.render(this.template, this.model.toJSON()));
					this.$el.find('input[name="activities"]').select2({
						data: this.model.get('notSelecteDtos'),
						id: function(obj) {
							return obj.activityId;
						},
						formatResult: function(obj) {
							return obj.activityName;
						}, 
						formatSelection: function(obj) {
							return obj.activityName;
						}
					});
					return this;	
				},
				add: function() {
					var value = this.$el.find('input[name="activities"]').val(),
						select = this.$el.find('input[name="activities"]'),
						target = this.$el.find('.js-activity-list'),
						tmpl = this.subTemplate,
						model = this.model;
					if(value === '') return;

					_.each(model.get('notSelecteDtos'), function(obj, index) {
						if(obj.activityId == value) {
							model.get('activityDTOs').push(obj);
							target.append(Mustache.render(tmpl, obj));
						}
					});
					
					var unselected_activity = _.filter(model.get('notSelecteDtos'), function(obj, i) {
						return obj.activityId !== value*1;
					});
					model.set('notSelecteDtos', unselected_activity);

					select.select2({
						data: this.model.get('notSelecteDtos'),
						id: function(obj) {
							return obj.activityId;
						},
						formatResult: function(obj) {
							return obj.activityName;
						}, 
						formatSelection: function(obj) {
							return obj.activityName;
						}
					});
					
					self.is_changed = true;
					is_changed = self.is_changed;
				}
//				remove: function(e) {
//					var model = this.model,
//						select = this.$el.find('input[name="activities"]'),
//						id = $(e.currentTarget).data('id');
//
//					var unselected_activity = _.filter(model.get('activitys'), function(obj, index) {
//						return obj.activityId === id*1;
//					});
//					model.get('notSelectedActivities').push(unselected_activity[0]);
//
//					var selected_activity = _.filter(model.get('activitys'), function(obj, index) {
//						return obj.activityId !== id*1;
//					});
//					model.set('activitys', selected_activity);
//
//					console.log(model.get('notSelectedActivities'))
//					console.log(model.get('activitys'))
//
//					select.select2({
//						data: model.get('notSelectedActivities'),
//						id: function(obj) {
//							return obj.activityId;
//						},
//						formatResult: function(obj) {
//							return obj.activityName;
//						}, 
//						formatSelection: function(obj) {
//							return obj.activityName;
//						}
//					});
//
//					$(e.currentTarget).parent().fadeOut(function() {
//						$(this).remove();
//					});
//
//					self.is_changed = true;
//				}
			});

			Service_Collection = Backbone.Collection.extend({
				model: Service_Model,
				methodUrl: {
					'read': CONF.url_fetch_collection,
					'create': '',
					'update': '',
					'delete': ''
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				},
			});

			var service_collection = new Service_Collection;

			self.Model = Service_Model;
			self.View = Service_View;
			self.Collection = Service_Collection;

			self.service_collection = service_collection;

			_.bindAll(self, 'addAll', 'addOne');

			service_collection.on('set', self.addAll);
			service_collection.on('add', self.addOne);

			self.getAll();
		},
		/*
		* Fetch all services add to collection
		*/
		getAll: function() {
			var self = this;
			self.conf.EL.empty();
			self.service_collection.reset();
			self.service_collection.fetch();
		},
		/*
		* Add all services
		*/
		addAll: function() {
			var self = this;
			self.service_collection.each(self.addOne);
		},
		/*
		* Add one service, init service view
		* @param {Object} Backbone mdoel
		*/
		addOne: function(model) {
			var	EL = this.conf.EL,
				view = new this.View({model: model});

			EL.append(view.render().el);
		},
		/*
		* Show a loading tip
		*/
		showLoadTip: function() {
			var self = this,
				EL = self.conf.EL;
			EL.append('<div class="loader"><p></p></div>');
		},
		/*
		* Show the warn tip, when user have changed the form filed
		*/
		showWarn: function() {
			var $target = $('.service-list tr.disabled').nextAll().find('.js-update-service'),
				right = 220,
				top = $target.offset().top - $target.height() - $('.warn-tip').height();
			$('.warn-tip').css({
				right: right,
				top: top
			}).show();
		},
		/*
		* Hide the warn tip
		*/
		hideWarn: function() {
			$('.warn-tip').hide();
		},
		/*
		* Submit the form add new activity
		* TODO
		* callback will refresh the page
		*/
		create: function() {
			var self = this,
				data = $('.js-service-form').serializeArray(),
				service = new self.Model;

			_.each(data, function(obj, index) {
				service.set(obj['name'], obj['value']);
			});
			// Remove no needed param form model
			service.unset('activityList');
			service.sync('create', service, {
				success: function(result) {
					if(result.status) {
						self.hideForm();
						// TODO
						// self.getAll();
						$('.js-show-service-activity').click();
						
						self.is_changed = false;
						is_changed = self.is_changed;
						
					} else {
						self.conf.formTarget.find('.form-msg').text(result.message).show();
					}
				}
			});
		}
	}
	return Service;
});