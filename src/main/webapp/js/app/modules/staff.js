define([
	'jquery', 
	'select2', 
	'icheck', 
	'bootbox',
	'mustache',
	'underscore', 
	'backbone'
], function($,select2, iCheck, bootbox, Mustache, _, Backbone) {
	function Staff(opts) {
		// Defualt set
		this.defaults = {
			url_fetch_collection: 'instructor/loadStaffList.htm', 
			url_fetch_model: 'instructor/loadStaffDetail.htm',
			url_update_model: 'instructor/updateStaff.htm',
			//created for GSSP-288
			url_fetch_collection_staffManager:'centralized/loadStaffList.htm',
			url_updateManager_model: 'centralized/updateStaffManager.htm',

			EL: $('.staff-list'),

			formTarget: ''
		}
		
		this.init(opts);
		this.is_changed = false;
	}

	Staff.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			var self = this;
			self.conf = $.extend(self.defaults, opts);
		},
		/*
		* Render the staff list
		*/
		renderList: function(value) {
			var self = this,
				CONF = self.conf,
				Staff_Model, Staff_View, Staff_Collection,
				Location_View;
			// Init the is_change status 
			self.is_changed = false;
			is_changed = self.is_changed;

			// Show loadings
			self.showLoadTip();

			// Define staff model
			Staff_Model = Backbone.Model.extend({
				methodUrl: {
					'read': CONF.url_fetch_model,
					'create': CONF.url_updateManager_model,
					'update': CONF.url_update_model,
					'delete': ''
				},
				defaults: {

				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				}
			});

			// Define staff view
			Staff_View = Backbone.View.extend({
				initialize: function() {
					return this;
				},
				events: {
					'click .edit-staff': 'edit',
					'click .close-edit-panel': 'close',
					'click .js-update-staff': 'update',
					'ifChanged input[name="active"]': 'changeActive',
				},
				tagName: 'table',
				className: 'table table-list table-fixed ',
				template: $('#tmpl_staff').html(),
				render: function() {
					this.$el.html(Mustache.render(this.template, this.model.toJSON()));
					return this;
				},
				edit: function(e) {
					var _this = this,
						$this = $(e.currentTarget);
					if($this.hasClass('editing')) return;

					if(self.is_changed) {
						bootbox.dialog({
							title: 'Warning!',
							message: 'You have unsaved information. Do you want to continue?',
							buttons: {
								success: {
									label: 'Continue',
									className: 'btn btn-important',
									callback: function() {
										CONF.EL.find('.editing').removeClass('editing');
										CONF.EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
										self.hideWarn();
										_this.getOne();
									}
								},
								cancle: {
									label: 'Cancel',
									className: 'btn btn-primary',
									callback: function() {
										self.showWarn();
										return;
									}
								}
							}
						});
					} else {
						CONF.EL.find('.editing').removeClass('editing');
						CONF.EL.find('tr.disabled').removeClass('disabled').nextAll().hide();
						$this.addClass('editing');
						_this.getOne();		
					}
				},
				/*
				* Ajax request to get staff detail
				*/
				getOne: function() {
					var _this = this;
					$.ajax({
						url: CONF.url_fetch_model,
						type: 'GET',
						data: {
							id: _this.model.get('id')
						},
						success: function(result) {
							// If session time out will get the login.html page
							// So, if response text has the string 'DOCTYPE' will reload page to jump to the login page
							if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
							_this.model.set(result);
							_this.editRender();
							_this.$el.find('tr').eq(0).addClass('disabled').nextAll().show();
							
							self.is_changed = false;
							is_changed = self.is_changed;
							
						}
					});
				},
				/*
				* Render the edit panel, bind change function to the form filed
				*/
				editRender: function() {
					var location_view = new Location_View({model: this.model});

					this.$el.find('.edit-staff').addClass('editing');
					this.$el.find('.js-location-wrap').empty().append(location_view.render().el);
					if(this.model.get('active')) {
						this.$el.find('input[name="active"]').iCheck().iCheck('indeterminate');
					} else {
						this.$el.find('input[name="active"]').iCheck().iCheck('determinate');
					}
				},
				/*
				* Close the edit panel, destory all form filed
				*/
				close: function() {
					this.$el.find('.edit-staff').removeClass('editing');
					this.$el.find('tr').eq(0).removeClass('disabled').nextAll().hide();
					this.$el.find('input[name="active"]').iCheck('destory');
					
					self.is_changed = false;
					is_changed = self.is_changed;
					
					self.hideWarn();
					return this;
				},
				/*
				* Change active function
				*/
				changeActive: function(e) {
					
					self.is_changed = true;
					is_changed = self.is_changed;
					
					if(e.currentTarget.checked) {
						this.model.set('active', false);
					} else {
						this.model.set('active', true);
					}
				},
				/*
				* Update the staff model
				* changes made for GSSP-288
				*/
				update: function() {
					var _this = this;					
					this.$el.find('.edit-staff').removeClass('editing');
					self.hideWarn();
					this.$el.find('.js-update-staff').attr('disabled', true);//For GCSS-341 to avoid 'double click'
					//changes made for GSSP-288
					if(value){
					this.model.sync('create', this.model,{
						success: function(result) {
							if(result.status) {
								_this.model.set(result.object);
								_this.render();
								_this.close();
							} else {
								notification.autoHideError(result.message);
							}
							_this.$el.find('.js-update-staff').attr('disabled', false);//For GCSS-341 to avoid 'double click'
						}
					});
					}
					else{
						this.model.sync('update', this.model,{
							success: function(result) {
								if(result.status) {
									_this.model.set(result.object);
									_this.render();
									_this.close();
								} else {
									notification.autoHideError(result.message);
								}
								_this.$el.find('.js-update-staff').attr('disabled', false);//For GCSS-341 to avoid 'double click'
							}
						});
						}
					}
				
			});
			
			// Define staff collection
			Staff_Collection = Backbone.Collection.extend({
				model: Staff_Model,
				methodUrl: {
					'read': CONF.url_fetch_collection,
					'create': '',
					'update': '',
					'delete': ''
				},		
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				},			
			});

			// Define location view
			Location_View = Backbone.View.extend({
				className: 'row',
				events: {
					'click .js-btn-add': 'add',
					'click .js-remove-role': 'remove'
				},
				template: $('#tmpl_location').html(),
				subTemplate: $('#tmpl_role_item').html(),
				render: function() {
					this.$el.html(Mustache.render(this.template, this.model.toJSON()));
					this.$el.find('input[name="location"]').select2({
						data: this.model.get('notSelectedLocation')
					});
					this.$el.find('input[name="role"]').select2({
						data: this.model.get('roles'),
						id: function(obj) {
							return obj.roleId;
						},
						formatResult: function(obj) {
							return obj.roleName;
						}, 
						formatSelection: function(obj) {
							return obj.roleName;
						}
					});
					return this;
				},
				/*
				* Add location role
				*/
				add: function() {
					var locationSelect = this.$el.find('input[name="location"]'),
						roleSelect = this.$el.find('input[name="role"]'),
						target = this.$el.find('.js-role-list'),
						value = locationSelect.val(),
						roleValue = roleSelect.select2('data'),
						tmpl = this.subTemplate,
						model = this.model;
					if(locationSelect.select2('val') === '' || roleSelect.select2('val') === '') return;
					
					_.each(model.get('notSelectedLocation'), function(obj, index) {
						if(obj.id == value) {
							obj.roleToLocation = value + ' - ' + roleValue['roleName'];
							model.get('selectedLocationsList').push(obj);
							target.append(Mustache.render(tmpl, obj));
						}
					});

					var unselect_location = _.filter(model.get('notSelectedLocation'), function(obj, index) {
						return obj.id != value;
					});

					model.set('notSelectedLocation', unselect_location);

					locationSelect.select2('val', '').select2({
						data: this.model.get('notSelectedLocation')
					});

					self.is_changed = true;
					is_changed = self.is_changed;
				},
				/*
				* Remove location role
				*/
				remove: function(e) {
					var locationSelect = this.$el.find('input[name="location"]'),
						roleSelect = this.$el.find('input[name="role"]'),
						model = this.model,
						id = $(e.currentTarget).data('id');

					var unselected_location = _.filter(model.get('selectedLocationsList'), function(obj, index) {
						return obj.id == id*1;
					});
					model.get('notSelectedLocation').push(unselected_location[0]);

					var selected_location = _.filter(model.get('selectedLocationsList'), function(obj, index) {
						return obj.id != id*1;
					});
					model.set('selectedLocationsList', selected_location);

					locationSelect.select2('val', '').select2({
						data: this.model.get('notSelectedLocation')
					});

					$(e.currentTarget).parent().fadeOut(function() {
						$(this).remove();
					});

					self.is_changed = true;
					is_changed = self.is_changed;
				}
			});		
			var staff_collection = new Staff_Collection;

			self.Model = Staff_Model;
			self.View = Staff_View;
			self.Collection = Staff_Collection;
			self.staff_collection = staff_collection;
			_.bindAll(self, 'addAll', 'addOne');
			staff_collection.on('set', self.addAll);
			staff_collection.on('add', self.addOne);
			//GSSP-288 staff only checkbox function
			$('#togBtn').change(function()
					{
				self.getAll();
					});
			if(value)
			{  
				self.getAllForStaffManager();
			}
			else
			{
				self.getAll();
			}
			return self;
		},
		/*
		* Fetch all staff add to collection
		* created for GSSP-288
		*/
		getAllForStaffManager: function() {
            var self = this,
            CONF = self.conf;
            //GSSP-285 Loading part
            $('#rep-load-fade1').show();
            $('#rep-load-modal1').show();
            CONF.EL.empty();
            $.ajax({
                  url: CONF.url_fetch_collection_staffManager,
                  type: 'GET',
                  success: function(result) {
                        $('#rep-load-modal1').hide();
                        $('#rep-load-fade1').hide();
                        self.staff_collection.reset();
                        self.staff_collection.set(result.listStaffDtos);      
                        console.log(result);
                  },error:function(result){//GSSP-285, in case if result failed for error function also we are hiding the loader instead of the loading.
                        $('#rep-load-modal1').hide();
                        $('#rep-load-fade1').hide();
                  }
            });
      },
      getAll: function() {
            var self = this,
            checkStatus = $('#togBtn')[0].checked,/*staff only check box GSSP-288*/
            CONF = self.conf;
            $('#rep-load-fade').show();
            $('#rep-load-modal').show();
            CONF.EL.empty();
                  $.ajax({
                        url: CONF.url_fetch_collection,
                        type: 'POST',
                        data:
                        {
                              'checkStatus' : checkStatus 
                        },                                  
                        success: function(result) {
                              $('#rep-load-modal').hide();
                        $('#rep-load-fade').hide();
                        self.staff_collection.reset();
                              self.staff_collection.set(result.listStaffDtos);      
                        }
                        ,error:function(result){//GSSP-285, in case if result failed for error function also we are hiding the loader instead of the loading.
                        $('#rep-load-modal').hide();
                        $('#rep-load-fade').hide();
                  }
                  });
      },
		/*
		* Add all staff
		*/
		addAll: function() {
			var self = this;
			self.staff_collection.each(self.addOne);
		},
		/*
		* Add one staff, init staff view
		* @param {Object} Backbone mdoel
		*/
		addOne: function(model) {
			var	EL = this.conf.EL,
				view = new this.View({model: model});

			EL.append(view.render().el);
		},
		/*
		* Hide the warn tip
		*/
		hideWarn: function() {
			$('.warn-tip').hide();
		},
		/*
		* Show the warn tip, when user have changed the form filed
		*/
		showWarn: function() {
			var $target = $('.staff-list tr.disabled').nextAll().find('.js-update-staff'),
				right = 220,
				top = $target.offset().top - $target.height() - $('.warn-tip').height();
			$('.warn-tip').css({
				right: right,
				top: top
			}).show();
		},
		/*
		* Show a loading tip
		*/
		showLoadTip: function() {
			var self = this,
				EL = self.conf.EL;
			EL.append('<div class="loader"><p></p></div>');
		}
	}
	return Staff;
});