define([
	'jquery',
	'moment',
	'select2',
	'pikaday',
	'icheck',
	'mustache',
	'underscore',
	'backbone',
	'edithour_modal',
	'timeoff_modal',
	'notification'
],
function($,moment, select2, Pikaday, iCheck, Mustache, _, Backbone, EdithourModal, TimeoffModal, notification) {
	function BookedAppointments(opts) {
		// De<PERSON>ult set
		this.defaults = {
			url_fetch_collection: 'bookedAppointments/loadBookedAppointments.htm',

			url_resubmit_booked_appointment: 'bookedAppointments/reSubmitBookedAppointments.htm',

			EL: $('.booked-Appointments-list'),

			formTarget: $('#booked_appointment_form'),
		}

		this.is_changed = is_changed;

		this.init(opts);
	}

	BookedAppointments.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			this.conf = $.extend(this.defaults, opts);
      this.bookedAppointmentsForm();
			var self = this;

			self.edithour_modal = new EdithourModal({
				EL: {
					$modal: $('#Xmodal_set_availability'),
					$mask: $('.mask')
				}
			});

			self.timeoff_modal = new TimeoffModal({
				EL:{
					$modal: $('#Xmodal_set_timeoff'),
					$mask: $('.mask')
				}
			})

			// Update the availability hours
			$(document).on('click', '.btn-update-availability', function(e) {
				self.edithour_modal.update(e);
			});

			$(document).on('click', '.js-close-availability-modal', function() {
				self.edithour_modal.hideModal();
			});

			//Update the time off
			$(document).on('click', '.btn-update-timeoff', function(e){
				self.timeoff_modal.update(e);
			});

			$(document).on('click', '.js-close-timeoff-modal', function(e){
				self.timeoff_modal.hideModal();
			});

			var CONF = self.conf,
				BookedAppointments_Model, BookedAppointments_View, BookedAppointments_Collection;

			// Define Booked Appointments model
			BookedAppointments_Model = Backbone.Model.extend({
				methodUrl: {
					'read': CONF.url_fetch_model,
					'create': '',
					'update': '',
					'delete': ''
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				},
				initialize: function() {

				},
			});

			// Define Booked Appointments view
			BookedAppointments_View = Backbone.View.extend({
				initialize: function() {
					var _this = this;
					return this;
				},
				events: {
					'click .booked-appointment-resubmit': 'resubmit',
					'click .js-edit-booked-appointments': 'edit',
					'click .close-edit-panel': 'close',
					'click .js-update-booked-appointments': 'update',
					'click .js-booked-set-availability': 'setBookedAvailability',
					'click .js-booked-set-timeoff': 'setBookedTimeoff',
					'ifChanged input[name="active"]': 'changeActive',
				},
				tagName: 'tr',
				className: '',
				template: $('#tmpl_booked_appointments').html(),
				render: function() {
					this.$el.html(Mustache.render(this.template, this.model.toJSON()));
					return this;
				},
				/*
				 * booked appointment resubmit
					* @param target
					*/
				resubmit:function(e){
					var _this = this, $this = $(e.currentTarget);
					console.log(JSON.stringify(_this.model.attributes))
					$.ajax({
						url: CONF.url_resubmit_booked_appointment,
						type: 'POST',
						data: {
							params:JSON.stringify(_this.model.attributes)
						},
						success: function(result) {
							console.log(result)
							// If session time out will get the login.html page
							// So, if response text has the string 'DOCTYPE' will reload page to jump to the login page
							// if(typeof result == 'string' && result.indexOf('DOCTYPE') !== -1) location.reload();
							// _this.model.set(result);
							// _this.editRender();
							// _this.$el.find('tr').eq(0).addClass('disabled').nextAll().show();
							//
							// self.is_changed = false;
							// is_changed = self.is_changed;
						},
						error: function(err) {
							notification.autoHideError(err);
						}
					});
				},
				// Ajax request detail Booked Appointments
				editRender: function() {
					return this;
				},
				// Close the edit panel
				close: function() {
					this.$el.find('.js-edit-booked-appointments').removeClass('editing');
					self.hideWarn();
					this.$el.find('tr').eq(0).removeClass('disabled').nextAll().hide();
					this.$el.find('input[name="active"]').iCheck('destroy');

					self.is_changed = false;
					is_changed = self.is_changed;

					return this;
				},
				// Update the Booked Appointments
				update: function() {
					var _this = this;
					this.$el.find('.edit-booked-appointments').removeClass('editing');
					this.$el.find('.js-update-booked-appointments').attr('disabled', true);
					self.hideWarn();
					this.model.sync('create', this.model, {
						success: function(result) {
							if(result.status) {
								_this.model.set(result.object);
								_this.render();
								_this.close();
							} else {
								notification.autoHideError(result.message);
							}
							_this.$el.find('.js-update-booked-appointments').attr('disabled', false);//For GCSS-341 to avoid 'double click'
						}
					});
				},
				setBookedAvailability: function() {
					self.edithour_modal.showModal($.extend(true, {}, this.model));
				},
				setBookedTimeoff: function() {
					self.timeoff_modal.showModal($.extend(true, {}, this.model));
				},
				changeActive: function(e) {

					self.is_changed = true;
					is_changed = self.is_changed;

					if(e.currentTarget.checked) {
						this.model.set('active', false);
					} else {
						this.model.set('active', true);
					}
				},
				remove: function() {

				}
			});
			// Define the Booked Appointments collection
			BookedAppointments_Collection = Backbone.Collection.extend({
				model: BookedAppointments_Model,
				methodUrl: {
					'read': CONF.url_fetch_collection,
					'create': '',
					'update': '',
					'delete': ''
				},
				sync: function(method, model, options) {
					if (model.methodUrl && model.methodUrl[method.toLowerCase()]) {
						options = options || {};
						options.url = model.methodUrl[method.toLowerCase()];
					}
					Backbone.sync(method, model, options);
				},
			});

			var	bookedAppointments_collection = new BookedAppointments_Collection;

			self.Model = BookedAppointments_Model;
			self.View = BookedAppointments_View;
			self.Collection = BookedAppointments_Collection;

			self.bookedAppointments_collection = bookedAppointments_collection;

			_.bindAll(self, 'addAll', 'addOne');

			bookedAppointments_collection.on('set', self.addAll);
			bookedAppointments_collection.on('add', self.addOne);

			return self;
		},
    bookedAppointmentsForm:function(){
      var self = this,
          CONF = self.conf,
          BookedAppointments_Query_Model;
      BookedAppointments_Query_Model = Backbone.Model.extend({
        formEL: {
          'startDate': CONF.formTarget.find('input[name="startDate"]'),
          'endDate': CONF.formTarget.find('input[name="endDate"]'),
          // 'tFrom': CONF.formTarget.find('select[name="tFrom"]'),
          // 'tTo': CONF.formTarget.find('select[name="tTo"]'),
        },
        /*
				* Return all params in report form
				*/
        getParams: function () {
					var _this = this,
							formEL = _this.formEL;
          // tFrom = formEL.tFrom.select2('val');
          // tTo = formEL.tTo.select2('val');
          return {
            startDate: formEL.startDate.val(),
            endDate: formEL.endDate.val(),
            // tFrom: tFrom,
            // tTo: tTo
          }
        },
        clear: function(){
        	var _this = this,
						formEL = _this.formEL;
          // formEL.tFrom.select2('val', '');
          // formEL.tTo.select2('val', '');
          formEL.startDate.val('');
          formEL.endDate.val('');
        },
        initialize: function() {
          var _this = this,
              startDate,
              endDate,
              // tFrom,
              // tTo,
              formEL = _this.formEL;
          // formEL.tFrom.select2('val', '');
          // formEL.tTo.select2('val', '');
					// formEL.tFrom.select2();
					// formEL.tTo.select2();
          startDate = new Pikaday({
            format: 'MM/DD/YYYY',
            field: formEL.startDate[0]
          });
          endDate = new Pikaday({
            format: 'MM/DD/YYYY',
            field: formEL.endDate[0]
          });

          // $('.js-report-time').text(moment().format('hh:mm:ss A'));
        },
      });
      self.bookedAppointmentQuery = new BookedAppointments_Query_Model;
      return self;
    },
		/*
		* Render the Booked Appointments list
		*/
		renderList: function() {
			var self = this;

			// Show loadings
			self.showLoadTip();

			self.is_changed = false;
			is_changed = self.is_changed;

			self.getAll();
			return self;
		},
		/*
		* Ajax request to get all Booked Appointments
		*/
		getAll: function() {
			var self = this,
				CONF = self.conf;
			CONF.EL.empty();
			self.bookedAppointments_collection.reset();
			var querySourceData = this.bookedAppointmentQuery.getParams();
			var dateReg = /^\d{1,2}\/\d{1,2}\/\d{4}$/;
			if(querySourceData.startDate.search(dateReg)==-1||querySourceData.endDate.search(dateReg)==-1){
				self.initDate();
				querySourceData = self.bookedAppointmentQuery.getParams();
			}
			var ajaxParams = {
				startDatetime : querySourceData.startDate,
				endDatetime : querySourceData.endDate
			}
			$.ajax({
				url: CONF.url_fetch_collection,
				type: 'GET',
				data:{
					params: JSON.stringify(ajaxParams)
				},
				success: function(result) {
					self.bookedAppointments_collection.reset();
					self.bookedAppointments_collection.set(result.appointmentBookDTO);
				}
			});
		},
    initDate: function(){
      $('input[name="startDate"]').val(moment(new Date()).format('L'));
      $('input[name="endDate"]').val(moment(new Date()).format('L'));
    },
		/*
		* Add all booked appointments in this collection
		*/
		addAll: function() {
			var self = this;
			self.bookedAppointments_collection.each(self.addOne);
		},
		/*
		* Add one booked appointment
		*/
		addOne: function(model) {
			var self = this,
				EL = self.conf.EL;
			var view = new self.View({model: model});

			EL.append(view.render().el);
		},
		/*
		* Show the warn information
		*/
		showWarn: function() {
				var $target = $('.booked-appointments-list tr.disabled').nextAll().find('.js-update-booked-appointments'),
				right = 220,
				top = $target.offset().top - $target.height() - $('.warn-tip').height();
			$('.warn-tip').css({
				right: right,
				top: top
			}).show();
		},
		/*
		* Hide the warn information
		*/
		hideWarn: function() {
			$('.warn-tip').hide();
		},
		/*
		* Show a loading tip
		*/
		showLoadTip: function() {
			var self = this,
				EL = self.conf.EL;
			EL.append('<div class="loader"><p></p></div>');
		}
	}
	return BookedAppointments;
});
