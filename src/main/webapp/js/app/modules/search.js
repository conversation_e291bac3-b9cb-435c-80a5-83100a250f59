define(['jquery', 'typeahead', 'mustache', 'hash'], function($, typeahead, Mustache, hash){
	function Search(opts) {
		// <PERSON><PERSON><PERSON> set
		this.defaults = {
			url_qucikSearch: 'calendar/quickSearch.htm',

			url_searchCustomer: 'searchCustomerByNameOrMail.htm',
			url_getCustomer: 'loadCustomerListFromTab.htm',
			url_getAppointments: 'calendar/loadAppointmentByCustomer.htm',

			url_searchStudio: 'location/locationSearch.htm',

			EL: {
				$searchInput: $('.search-query'),
				$searchForm: $('.form-search'),
				$quickSearch: $('.quick-search')
			}
		}
		this.init(opts);
	}
	Search.prototype = {
		/*
		* Initialize
		* @param {Object} the options
		*/
		init: function(opts) {
			this.conf = $.extend({}, this.defaults, opts);
			return this;
		},
		/*
		* Scheduler page the quicksearch form
		* Bind typeahead function
		*/
		quickSearch: function() {
			var self = this,
				CONF = self.conf,
				EL = CONF.EL,
				template,
				$quickSearch = EL.$quickSearch;
			//GSSP-246 -Quick search Analysis with the EXTERNAL ID
			template = Mustache.compile('<a href="javascript:;">{{fullName}} {{externalId}} {{status}}</a>');
			
			$quickSearch.typeahead({
				recordId: 'recordId',
				valueKey: 'fullName',
				remote: {
					url: CONF.url_qucikSearch + '?name=%QUERY',
					filter: function(result) {
						return result;
					}
				},
				template: template,
				limit: 4,
				//GSSP-202 changes
				minLength: 2,
				engine: Mustache,
			}).on('typeahead:selected', function(e, data) {
				if(data) {
					if(data.type === 'more') {
						$('.btn-quick-search').click();

						//For GCSS-491
						$('.search-customer').typeahead('setQuery', '');

						return; //If click 'more',never add the customer info into hash
					}

					// Add the customerId in the hash
					hash.add({customer: data.recordId});
					//GSSP-246 -Quick search Analysis with the EXTERNAL ID
					var customerTmpl = Mustache.render('<li><a data-id="{{recordId}}" class="js-show-customer-detail-view" href="javascript:;">{{fullName}}</a></li><li>{{email}}</li><li>Phone {{phone}}</li><li>GC ID {{externalId}}</li><li>Lesson Count {{lessonCount}}</li><li>{{instrumentType}}</li>', data);
					$('.filter-list').hide().next().show();

					// Store the customer info in the DOM
					$('.customer-info').data('customer', data);
					$('.customer-info ul').html(customerTmpl);

					// Callback
					CONF.handler();

					$('.search-customer').typeahead('setQuery', '');
				}
			});
		}
	}
	return Search;
});