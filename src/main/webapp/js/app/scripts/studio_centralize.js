require.config({
	baseUrl: 'js',
	paths: {
		/* Vendor */
		'jquery': 'vendor/jquery/jquery.min',
		'mustache': 'vendor/libs/mustache.min',
		'pikaday': 'vendor/libs/pikaday.min',
		'moment': 'vendor/libs/moment',
		'icheck': 'vendor/libs/jquery.icheck.min',
		'select2': 'vendor/libs/select2.min',
		'typeahead': 'vendor/libs/typeahead.min',
		'modal': 'vendor/libs/bootstrap.modal.min',
		'bootbox': 'vendor/libs/bootbox.min',
		'backbone': 'vendor/backbone/backbone-min',
		'underscore': 'vendor/underscore/underscore-min',

		/* Modules */
		'calendar': 'app/modules/calendar',
		'filter': 'app/modules/filter',
		'appointment': 'app/modules/appointment',
		'appointment_modal': 'app/modules/appointment_modal',
		'service': 'app/modules/service',
		'activity': 'app/modules/activity',
		'studio': 'app/modules/studio',
		'edithour_modal': 'app/modules/edithour_modal',
		'search': 'app/modules/search',
		'notification': 'app/modules/common/notification',

		/* Utils */
		'eventdeploy': 'utils/eventdeploy',
		'hash': 'utils/hash'
	},
	shim: {
		'backbone': {
			deps: ['underscore', 'jquery'],
			exports: 'Backbone'
		},
		'underscore': {
			exports: '_'
		},
		'icheck': {
			deps: ['jquery'],
			exports: 'iCheck'
		},
		'modal': {
			deps: ['jquery']
		},
		'bootbox': {
			deps: ['jquery', 'modal'],
			exports: 'bootbox'
		},
		'pikaday': {
			deps: ['moment'],
			exports: 'Pikaday'
		},
		'select2': {
			deps: ['jquery'],
			exports: 'select2'
		},
		'typeahead': {
			deps: ['jquery'],
			exports: 'typeahead'
		}
	}
});

require([
	'jquery', 
	'studio',
	'eventdeploy'
], 
function($, Studio, Eventdeploy) {
	var ed = new Eventdeploy,
		studio = new Studio;

	studio.renderList();

	ed.Deploy({
		'click .btn-search': function(e) {
			e.preventDefault();
			studio.search();
		}
	});
});