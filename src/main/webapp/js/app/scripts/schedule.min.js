/*! jQuery v2.0.3 | (c) 2005, 2013 jQuery Foundation, Inc. | jquery.org/license
//@ sourceMappingURL=jquery.min.map
*/

//! license : MIT

/*!
 * mustache.js - Logic-less {{mustache}} templates with JavaScript
 * http://github.com/janl/mustache.js
 */

/*!
 * iCheck v0.9.1 jQuery plugin, http://git.io/uhUPMA
 */

//     (c) 2009-2013 <PERSON>, DocumentCloud and Investigative Reporters & Editors

//     Underscore may be freely distributed under the MIT license.

//     (c) 2010-2013 <PERSON>, DocumentCloud Inc.

//     Backbone may be freely distributed under the MIT license.

/*
Copyright 2012 Igor <PERSON>

Version: 3.4.1 Timestamp: Thu Jun 27 18:02:10 PDT 2013

This software is licensed under the Apache License, Version 2.0 (the "Apache License") or the GNU
General Public License version 2 (the "GPL License"). You may choose either license to govern your
use of this software only upon the condition that you accept all of the terms of either the Apache
License or the GPL License.

You may obtain a copy of the Apache License and the GPL License at:

http://www.apache.org/licenses/LICENSE-2.0
http://www.gnu.org/licenses/gpl-2.0.html

Unless required by applicable law or agreed to in writing, software distributed under the Apache License
or the GPL Licesnse is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
either express or implied. See the Apache License and the GPL License for the specific language governing
permissions and limitations under the Apache License and the GPL License.
*/

/*!
 * Pikaday
 *
 * Copyright © 2013 David Bushell | BSD & MIT license | https://github.com/dbushell/Pikaday
 */

/**
 * bootbox.js v4.0.0
 *
 * http://bootboxjs.com/license.txt
 */

(function(e,undefined){function j(e){var t=e.length,n=x.type(e);return x.isWindow(e)?!1:1===e.nodeType&&t?!0:"array"===n||"function"!==n&&(0===t||"number"==typeof t&&t>0&&t-1 in e)}function A(e){var t=D[e]={};return x.each(e.match(w)||[],function(e,n){t[n]=!0}),t}function F(){Object.defineProperty(this.cache={},0,{get:function(){return{}}}),this.expando=x.expando+Math.random()}function P(e,t,n){var r;if(n===undefined&&1===e.nodeType)if(r="data-"+t.replace(O,"-$1").toLowerCase(),n=e.getAttribute(r),"string"==typeof n){try{n="true"===n?!0:"false"===n?!1:"null"===n?null:+n+""===n?+n:H.test(n)?JSON.parse(n):n}catch(i){}L.set(e,t,n)}else n=undefined;return n}function U(){return!0}function Y(){return!1}function V(){try{return o.activeElement}catch(e){}}function Z(e,t){while((e=e[t])&&1!==e.nodeType);return e}function et(e,t,n){if(x.isFunction(t))return x.grep(e,function(e,r){return!!t.call(e,r,e)!==n});if(t.nodeType)return x.grep(e,function(e){return e===t!==n});if("string"==typeof t){if(G.test(t))return x.filter(t,e,n);t=x.filter(t,e)}return x.grep(e,function(e){return g.call(t,e)>=0!==n})}function pt(e,t){return x.nodeName(e,"table")&&x.nodeName(1===t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function ft(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function ht(e){var t=ut.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function dt(e,t){var n=e.length,r=0;for(;n>r;r++)q.set(e[r],"globalEval",!t||q.get(t[r],"globalEval"))}function gt(e,t){var n,r,i,s,o,u,a,f;if(1===t.nodeType){if(q.hasData(e)&&(s=q.access(e),o=q.set(t,s),f=s.events)){delete o.handle,o.events={};for(i in f)for(n=0,r=f[i].length;r>n;n++)x.event.add(t,i,f[i][n])}L.hasData(e)&&(u=L.access(e),a=x.extend({},u),L.set(t,a))}}function mt(e,t){var n=e.getElementsByTagName?e.getElementsByTagName(t||"*"):e.querySelectorAll?e.querySelectorAll(t||"*"):[];return t===undefined||t&&x.nodeName(e,t)?x.merge([e],n):n}function yt(e,t){var n=t.nodeName.toLowerCase();"input"===n&&ot.test(e.type)?t.checked=e.checked:("input"===n||"textarea"===n)&&(t.defaultValue=e.defaultValue)}function At(e,t){if(t in e)return t;var n=t.charAt(0).toUpperCase()+t.slice(1),r=t,i=Dt.length;while(i--)if(t=Dt[i]+n,t in e)return t;return r}function Lt(e,t){return e=t||e,"none"===x.css(e,"display")||!x.contains(e.ownerDocument,e)}function qt(t){return e.getComputedStyle(t,null)}function Ht(e,t){var n,r,i,s=[],o=0,u=e.length;for(;u>o;o++)r=e[o],r.style&&(s[o]=q.get(r,"olddisplay"),n=r.style.display,t?(s[o]||"none"!==n||(r.style.display=""),""===r.style.display&&Lt(r)&&(s[o]=q.access(r,"olddisplay",Rt(r.nodeName)))):s[o]||(i=Lt(r),(n&&"none"!==n||!i)&&q.set(r,"olddisplay",i?n:x.css(r,"display"))));for(o=0;u>o;o++)r=e[o],r.style&&(t&&"none"!==r.style.display&&""!==r.style.display||(r.style.display=t?s[o]||"":"none"));return e}function Ot(e,t,n){var r=Tt.exec(t);return r?Math.max(0,r[1]-(n||0))+(r[2]||"px"):t}function Ft(e,t,n,r,i){var s=n===(r?"border":"content")?4:"width"===t?1:0,o=0;for(;4>s;s+=2)"margin"===n&&(o+=x.css(e,n+jt[s],!0,i)),r?("content"===n&&(o-=x.css(e,"padding"+jt[s],!0,i)),"margin"!==n&&(o-=x.css(e,"border"+jt[s]+"Width",!0,i))):(o+=x.css(e,"padding"+jt[s],!0,i),"padding"!==n&&(o+=x.css(e,"border"+jt[s]+"Width",!0,i)));return o}function Pt(e,t,n){var r=!0,i="width"===t?e.offsetWidth:e.offsetHeight,s=qt(e),o=x.support.boxSizing&&"border-box"===x.css(e,"boxSizing",!1,s);if(0>=i||null==i){if(i=vt(e,t,s),(0>i||null==i)&&(i=e.style[t]),Ct.test(i))return i;r=o&&(x.support.boxSizingReliable||i===e.style[t]),i=parseFloat(i)||0}return i+Ft(e,t,n||(o?"border":"content"),r,s)+"px"}function Rt(e){var t=o,n=Nt[e];return n||(n=Mt(e,t),"none"!==n&&n||(xt=(xt||x("<iframe frameborder='0' width='0' height='0'/>").css("cssText","display:block !important")).appendTo(t.documentElement),t=(xt[0].contentWindow||xt[0].contentDocument).document,t.write("<!doctype html><html><body>"),t.close(),n=Mt(e,t),xt.detach()),Nt[e]=n),n}function Mt(e,t){var n=x(t.createElement(e)).appendTo(t.body),r=x.css(n[0],"display");return n.remove(),r}function _t(e,t,n,r){var i;if(x.isArray(t))x.each(t,function(t,i){n||$t.test(e)?r(e,i):_t(e+"["+("object"==typeof i?t:"")+"]",i,n,r)});else if(n||"object"!==x.type(t))r(e,t);else for(i in t)_t(e+"["+i+"]",t[i],n,r)}function un(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,i=0,s=t.toLowerCase().match(w)||[];if(x.isFunction(n))while(r=s[i++])"+"===r[0]?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}function ln(e,t,n,r){function o(u){var a;return i[u]=!0,x.each(e[u]||[],function(e,u){var f=u(t,n,r);return"string"!=typeof f||s||i[f]?s?!(a=f):undefined:(t.dataTypes.unshift(f),o(f),!1)}),a}var i={},s=e===on;return o(t.dataTypes[0])||!i["*"]&&o("*")}function cn(e,t){var n,r,i=x.ajaxSettings.flatOptions||{};for(n in t)t[n]!==undefined&&((i[n]?e:r||(r={}))[n]=t[n]);return r&&x.extend(!0,e,r),e}function pn(e,t,n){var r,i,s,o,u=e.contents,a=e.dataTypes;while("*"===a[0])a.shift(),r===undefined&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in u)if(u[i]&&u[i].test(r)){a.unshift(i);break}if(a[0]in n)s=a[0];else{for(i in n){if(!a[0]||e.converters[i+" "+a[0]]){s=i;break}o||(o=i)}s=s||o}return s?(s!==a[0]&&a.unshift(s),n[s]):undefined}function fn(e,t,n,r){var i,s,o,u,a,f={},l=e.dataTypes.slice();if(l[1])for(o in e.converters)f[o.toLowerCase()]=e.converters[o];s=l.shift();while(s)if(e.responseFields[s]&&(n[e.responseFields[s]]=t),!a&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),a=s,s=l.shift())if("*"===s)s=a;else if("*"!==a&&a!==s){if(o=f[a+" "+s]||f["* "+s],!o)for(i in f)if(u=i.split(" "),u[1]===s&&(o=f[a+" "+u[0]]||f["* "+u[0]])){o===!0?o=f[i]:f[i]!==!0&&(s=u[0],l.unshift(u[1]));break}if(o!==!0)if(o&&e["throws"])t=o(t);else try{t=o(t)}catch(c){return{state:"parsererror",error:o?c:"No conversion from "+a+" to "+s}}}return{state:"success",data:t}}function En(){return setTimeout(function(){xn=undefined}),xn=x.now()}function Sn(e,t,n){var r,i=(Nn[t]||[]).concat(Nn["*"]),s=0,o=i.length;for(;o>s;s++)if(r=i[s].call(n,t,e))return r}function jn(e,t,n){var r,i,s=0,o=kn.length,u=x.Deferred().always(function(){delete a.elem}),a=function(){if(i)return!1;var t=xn||En(),n=Math.max(0,f.startTime+f.duration-t),r=n/f.duration||0,s=1-r,o=0,a=f.tweens.length;for(;a>o;o++)f.tweens[o].run(s);return u.notifyWith(e,[f,s,n]),1>s&&a?n:(u.resolveWith(e,[f]),!1)},f=u.promise({elem:e,props:x.extend({},t),opts:x.extend(!0,{specialEasing:{}},n),originalProperties:t,originalOptions:n,startTime:xn||En(),duration:n.duration,tweens:[],createTween:function(t,n){var r=x.Tween(e,f.opts,t,n,f.opts.specialEasing[t]||f.opts.easing);return f.tweens.push(r),r},stop:function(t){var n=0,r=t?f.tweens.length:0;if(i)return this;for(i=!0;r>n;n++)f.tweens[n].run(1);return t?u.resolveWith(e,[f,t]):u.rejectWith(e,[f,t]),this}}),l=f.props;for(Dn(l,f.opts.specialEasing);o>s;s++)if(r=kn[s].call(f,e,l,f.opts))return r;return x.map(l,Sn,f),x.isFunction(f.opts.start)&&f.opts.start.call(e,f),x.fx.timer(x.extend(a,{elem:e,anim:f,queue:f.opts.queue})),f.progress(f.opts.progress).done(f.opts.done,f.opts.complete).fail(f.opts.fail).always(f.opts.always)}function Dn(e,t){var n,r,i,s,o;for(n in e)if(r=x.camelCase(n),i=t[r],s=e[n],x.isArray(s)&&(i=s[1],s=e[n]=s[0]),n!==r&&(e[r]=s,delete e[n]),o=x.cssHooks[r],o&&"expand"in o){s=o.expand(s),delete e[r];for(n in s)n in e||(e[n]=s[n],t[n]=i)}else t[r]=i}function An(e,t,n){var r,i,s,o,u,a,f=this,l={},c=e.style,h=e.nodeType&&Lt(e),p=q.get(e,"fxshow");n.queue||(u=x._queueHooks(e,"fx"),null==u.unqueued&&(u.unqueued=0,a=u.empty.fire,u.empty.fire=function(){u.unqueued||a()}),u.unqueued++,f.always(function(){f.always(function(){u.unqueued--,x.queue(e,"fx").length||u.empty.fire()})})),1===e.nodeType&&("height"in t||"width"in t)&&(n.overflow=[c.overflow,c.overflowX,c.overflowY],"inline"===x.css(e,"display")&&"none"===x.css(e,"float")&&(c.display="inline-block")),n.overflow&&(c.overflow="hidden",f.always(function(){c.overflow=n.overflow[0],c.overflowX=n.overflow[1],c.overflowY=n.overflow[2]}));for(r in t)if(i=t[r],wn.exec(i)){if(delete t[r],s=s||"toggle"===i,i===(h?"hide":"show")){if("show"!==i||!p||p[r]===undefined)continue;h=!0}l[r]=p&&p[r]||x.style(e,r)}if(!x.isEmptyObject(l)){p?"hidden"in p&&(h=p.hidden):p=q.access(e,"fxshow",{}),s&&(p.hidden=!h),h?x(e).show():f.done(function(){x(e).hide()}),f.done(function(){var t;q.remove(e,"fxshow");for(t in l)x.style(e,t,l[t])});for(r in l)o=Sn(h?p[r]:0,r,f),r in p||(p[r]=o.start,h&&(o.end=o.start,o.start="width"===r||"height"===r?1:0))}}function Ln(e,t,n,r,i){return new Ln.prototype.init(e,t,n,r,i)}function qn(e,t){var n,r={height:e},i=0;for(t=t?1:0;4>i;i+=2-t)n=jt[i],r["margin"+n]=r["padding"+n]=e;return t&&(r.opacity=r.width=e),r}function Hn(e){return x.isWindow(e)?e:9===e.nodeType&&e.defaultView}var t,n,r=typeof undefined,i=e.location,o=e.document,s=o.documentElement,a=e.jQuery,u=e.$,l={},c=[],p="2.0.3",f=c.concat,h=c.push,d=c.slice,g=c.indexOf,m=l.toString,y=l.hasOwnProperty,v=p.trim,x=function(e,n){return new x.fn.init(e,n,t)},b=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,w=/\S+/g,T=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,C=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,k=/^-ms-/,N=/-([\da-z])/gi,E=function(e,t){return t.toUpperCase()},S=function(){o.removeEventListener("DOMContentLoaded",S,!1),e.removeEventListener("load",S,!1),x.ready()};x.fn=x.prototype={jquery:p,constructor:x,init:function(e,t,n){var r,i;if(!e)return this;if("string"==typeof e){if(r="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:T.exec(e),!r||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof x?t[0]:t,x.merge(this,x.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:o,!0)),C.test(r[1])&&x.isPlainObject(t))for(r in t)x.isFunction(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return i=o.getElementById(r[2]),i&&i.parentNode&&(this.length=1,this[0]=i),this.context=o,this.selector=e,this}return e.nodeType?(this.context=this[0]=e,this.length=1,this):x.isFunction(e)?n.ready(e):(e.selector!==undefined&&(this.selector=e.selector,this.context=e.context),x.makeArray(e,this))},selector:"",length:0,toArray:function(){return d.call(this)},get:function(e){return null==e?this.toArray():0>e?this[this.length+e]:this[e]},pushStack:function(e){var t=x.merge(this.constructor(),e);return t.prevObject=this,t.context=this.context,t},each:function(e,t){return x.each(this,e,t)},ready:function(e){return x.ready.promise().done(e),this},slice:function(){return this.pushStack(d.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(0>e?t:0);return this.pushStack(n>=0&&t>n?[this[n]]:[])},map:function(e){return this.pushStack(x.map(this,function(t,n){return e.call(t,n,t)}))},end:function(){return this.prevObject||this.constructor(null)},push:h,sort:[].sort,splice:[].splice},x.fn.init.prototype=x.fn,x.extend=x.fn.extend=function(){var e,t,n,r,i,s,o=arguments[0]||{},u=1,a=arguments.length,f=!1;for("boolean"==typeof o&&(f=o,o=arguments[1]||{},u=2),"object"==typeof o||x.isFunction(o)||(o={}),a===u&&(o=this,--u);a>u;u++)if(null!=(e=arguments[u]))for(t in e)n=o[t],r=e[t],o!==r&&(f&&r&&(x.isPlainObject(r)||(i=x.isArray(r)))?(i?(i=!1,s=n&&x.isArray(n)?n:[]):s=n&&x.isPlainObject(n)?n:{},o[t]=x.extend(f,s,r)):r!==undefined&&(o[t]=r));return o},x.extend({expando:"jQuery"+(p+Math.random()).replace(/\D/g,""),noConflict:function(t){return e.$===x&&(e.$=u),t&&e.jQuery===x&&(e.jQuery=a),x},isReady:!1,readyWait:1,holdReady:function(e){e?x.readyWait++:x.ready(!0)},ready:function(e){(e===!0?--x.readyWait:x.isReady)||(x.isReady=!0,e!==!0&&--x.readyWait>0||(n.resolveWith(o,[x]),x.fn.trigger&&x(o).trigger("ready").off("ready")))},isFunction:function(e){return"function"===x.type(e)},isArray:Array.isArray,isWindow:function(e){return null!=e&&e===e.window},isNumeric:function(e){return!isNaN(parseFloat(e))&&isFinite(e)},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?l[m.call(e)]||"object":typeof e},isPlainObject:function(e){if("object"!==x.type(e)||e.nodeType||x.isWindow(e))return!1;try{if(e.constructor&&!y.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(t){return!1}return!0},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},error:function(e){throw Error(e)},parseHTML:function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||o;var r=C.exec(e),i=!n&&[];return r?[t.createElement(r[1])]:(r=x.buildFragment([e],t,i),i&&x(i).remove(),x.merge([],r.childNodes))},parseJSON:JSON.parse,parseXML:function(e){var t,n;if(!e||"string"!=typeof e)return null;try{n=new DOMParser,t=n.parseFromString(e,"text/xml")}catch(r){t=undefined}return(!t||t.getElementsByTagName("parsererror").length)&&x.error("Invalid XML: "+e),t},noop:function(){},globalEval:function(e){var t,n=eval;e=x.trim(e),e&&(1===e.indexOf("use strict")?(t=o.createElement("script"),t.text=e,o.head.appendChild(t).parentNode.removeChild(t)):n(e))},camelCase:function(e){return e.replace(k,"ms-").replace(N,E)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t,n){var r,i=0,s=e.length,o=j(e);if(n){if(o){for(;s>i;i++)if(r=t.apply(e[i],n),r===!1)break}else for(i in e)if(r=t.apply(e[i],n),r===!1)break}else if(o){for(;s>i;i++)if(r=t.call(e[i],i,e[i]),r===!1)break}else for(i in e)if(r=t.call(e[i],i,e[i]),r===!1)break;return e},trim:function(e){return null==e?"":v.call(e)},makeArray:function(e,t){var n=t||[];return null!=e&&(j(Object(e))?x.merge(n,"string"==typeof e?[e]:e):h.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:g.call(t,e,n)},merge:function(e,t){var n=t.length,r=e.length,i=0;if("number"==typeof n)for(;n>i;i++)e[r++]=t[i];else while(t[i]!==undefined)e[r++]=t[i++];return e.length=r,e},grep:function(e,t,n){var r,i=[],s=0,o=e.length;for(n=!!n;o>s;s++)r=!!t(e[s],s),n!==r&&i.push(e[s]);return i},map:function(e,t,n){var r,i=0,s=e.length,o=j(e),u=[];if(o)for(;s>i;i++)r=t(e[i],i,n),null!=r&&(u[u.length]=r);else for(i in e)r=t(e[i],i,n),null!=r&&(u[u.length]=r);return f.apply([],u)},guid:1,proxy:function(e,t){var n,r,i;return"string"==typeof t&&(n=e[t],t=e,e=n),x.isFunction(e)?(r=d.call(arguments,2),i=function(){return e.apply(t||this,r.concat(d.call(arguments)))},i.guid=e.guid=e.guid||x.guid++,i):undefined},access:function(e,t,n,r,i,s,o){var u=0,a=e.length,f=null==n;if("object"===x.type(n)){i=!0;for(u in n)x.access(e,t,u,n[u],!0,s,o)}else if(r!==undefined&&(i=!0,x.isFunction(r)||(o=!0),f&&(o?(t.call(e,r),t=null):(f=t,t=function(e,t,n){return f.call(x(e),n)})),t))for(;a>u;u++)t(e[u],n,o?r:r.call(e[u],u,t(e[u],n)));return i?e:f?t.call(e):a?t(e[0],n):s},now:Date.now,swap:function(e,t,n,r){var i,s,o={};for(s in t)o[s]=e.style[s],e.style[s]=t[s];i=n.apply(e,r||[]);for(s in t)e.style[s]=o[s];return i}}),x.ready.promise=function(t){return n||(n=x.Deferred(),"complete"===o.readyState?setTimeout(x.ready):(o.addEventListener("DOMContentLoaded",S,!1),e.addEventListener("load",S,!1))),n.promise(t)},x.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(e,t){l["[object "+t+"]"]=t.toLowerCase()}),t=x(o),function(e,t){function ot(e,t,n,i){var s,o,u,a,f,l,p,m,g,E;if((t?t.ownerDocument||t:w)!==h&&c(t),t=t||h,n=n||[],!e||"string"!=typeof e)return n;if(1!==(a=t.nodeType)&&9!==a)return[];if(d&&!i){if(s=Z.exec(e))if(u=s[1]){if(9===a){if(o=t.getElementById(u),!o||!o.parentNode)return n;if(o.id===u)return n.push(o),n}else if(t.ownerDocument&&(o=t.ownerDocument.getElementById(u))&&y(t,o)&&o.id===u)return n.push(o),n}else{if(s[2])return H.apply(n,t.getElementsByTagName(e)),n;if((u=s[3])&&r.getElementsByClassName&&t.getElementsByClassName)return H.apply(n,t.getElementsByClassName(u)),n}if(r.qsa&&(!v||!v.test(e))){if(m=p=b,g=t,E=9===a&&e,1===a&&"object"!==t.nodeName.toLowerCase()){l=mt(e),(p=t.getAttribute("id"))?m=p.replace(nt,"\\$&"):t.setAttribute("id",m),m="[id='"+m+"'] ",f=l.length;while(f--)l[f]=m+gt(l[f]);g=$.test(e)&&t.parentNode||t,E=l.join(",")}if(E)try{return H.apply(n,g.querySelectorAll(E)),n}catch(S){}finally{p||t.removeAttribute("id")}}}return Nt(e.replace(W,"$1"),t,n,i)}function ut(){function t(n,r){return e.push(n+=" ")>s.cacheLength&&delete t[e.shift()],t[n]=r}var e=[];return t}function at(e){return e[b]=!0,e}function ft(e){var t=h.createElement("div");try{return!!e(t)}catch(n){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function lt(e,t){var n=e.split("|"),r=e.length;while(r--)s.attrHandle[n[r]]=t}function ct(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||O)-(~e.sourceIndex||O);if(r)return r;if(n)while(n=n.nextSibling)if(n===t)return-1;return e?1:-1}function ht(e){return function(t){var n=t.nodeName.toLowerCase();return"input"===n&&t.type===e}}function pt(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function dt(e){return at(function(t){return t=+t,at(function(n,r){var i,s=e([],n.length,t),o=s.length;while(o--)n[i=s[o]]&&(n[i]=!(r[i]=n[i]))})})}function vt(){}function mt(e,t){var n,r,i,o,u,a,f,l=N[e+" "];if(l)return t?0:l.slice(0);u=e,a=[],f=s.preFilter;while(u){(!n||(r=X.exec(u)))&&(r&&(u=u.slice(r[0].length)||u),a.push(i=[])),n=!1,(r=V.exec(u))&&(n=r.shift(),i.push({value:n,type:r[0].replace(W," ")}),u=u.slice(n.length));for(o in s.filter)!(r=G[o].exec(u))||f[o]&&!(r=f[o](r))||(n=r.shift(),i.push({value:n,type:o,matches:r}),u=u.slice(n.length));if(!n)break}return t?u.length:u?ot.error(e):N(e,a).slice(0)}function gt(e){var t=0,n=e.length,r="";for(;n>t;t++)r+=e[t].value;return r}function yt(e,t,n){var r=t.dir,s=n&&"parentNode"===r,o=S++;return t.first?function(t,n,i){while(t=t[r])if(1===t.nodeType||s)return e(t,n,i)}:function(t,n,u){var a,f,l,c=E+" "+o;if(u){while(t=t[r])if((1===t.nodeType||s)&&e(t,n,u))return!0}else while(t=t[r])if(1===t.nodeType||s)if(l=t[b]||(t[b]={}),(f=l[r])&&f[0]===c){if((a=f[1])===!0||a===i)return a===!0}else if(f=l[r]=[c],f[1]=e(t,n,u)||i,f[1]===!0)return!0}}function bt(e){return e.length>1?function(t,n,r){var i=e.length;while(i--)if(!e[i](t,n,r))return!1;return!0}:e[0]}function wt(e,t,n,r,i){var s,o=[],u=0,a=e.length,f=null!=t;for(;a>u;u++)(s=e[u])&&(!n||n(s,r,i))&&(o.push(s),f&&t.push(u));return o}function Et(e,t,n,r,i,s){return r&&!r[b]&&(r=Et(r)),i&&!i[b]&&(i=Et(i,s)),at(function(s,o,u,a){var f,l,c,h=[],p=[],d=o.length,v=s||Tt(t||"*",u.nodeType?[u]:u,[]),m=!e||!s&&t?v:wt(v,h,e,u,a),g=n?i||(s?e:d||r)?[]:o:m;if(n&&n(m,g,u,a),r){f=wt(g,p),r(f,[],u,a),l=f.length;while(l--)(c=f[l])&&(g[p[l]]=!(m[p[l]]=c))}if(s){if(i||e){if(i){f=[],l=g.length;while(l--)(c=g[l])&&f.push(m[l]=c);i(null,g=[],f,a)}l=g.length;while(l--)(c=g[l])&&(f=i?j.call(s,c):h[l])>-1&&(s[f]=!(o[f]=c))}}else g=wt(g===o?g.splice(d,g.length):g),i?i(null,o,g,a):H.apply(o,g)})}function St(e){var t,n,r,i=e.length,o=s.relative[e[0].type],u=o||s.relative[" "],a=o?1:0,l=yt(function(e){return e===t},u,!0),c=yt(function(e){return j.call(t,e)>-1},u,!0),h=[function(e,n,r){return!o&&(r||n!==f)||((t=n).nodeType?l(e,n,r):c(e,n,r))}];for(;i>a;a++)if(n=s.relative[e[a].type])h=[yt(bt(h),n)];else{if(n=s.filter[e[a].type].apply(null,e[a].matches),n[b]){for(r=++a;i>r;r++)if(s.relative[e[r].type])break;return Et(a>1&&bt(h),a>1&&gt(e.slice(0,a-1).concat({value:" "===e[a-2].type?"*":""})).replace(W,"$1"),n,r>a&&St(e.slice(a,r)),i>r&&St(e=e.slice(r)),i>r&&gt(e))}h.push(n)}return bt(h)}function xt(e,t){var n=0,r=t.length>0,o=e.length>0,u=function(u,a,l,c,p){var d,v,m,g=[],y=0,b="0",w=u&&[],S=null!=p,x=f,T=u||o&&s.find.TAG("*",p&&a.parentNode||a),N=E+=null==x?1:Math.random()||.1;for(S&&(f=a!==h&&a,i=n);null!=(d=T[b]);b++){if(o&&d){v=0;while(m=e[v++])if(m(d,a,l)){c.push(d);break}S&&(E=N,i=++n)}r&&((d=!m&&d)&&y--,u&&w.push(d))}if(y+=b,r&&b!==y){v=0;while(m=t[v++])m(w,g,a,l);if(u){if(y>0)while(b--)w[b]||g[b]||(g[b]=D.call(c));g=wt(g)}H.apply(c,g),S&&!u&&g.length>0&&y+t.length>1&&ot.uniqueSort(c)}return S&&(E=N,f=x),w};return r?at(u):u}function Tt(e,t,n){var r=0,i=t.length;for(;i>r;r++)ot(e,t[r],n);return n}function Nt(e,t,n,i){var o,u,f,l,c,h=mt(e);if(!i&&1===h.length){if(u=h[0]=h[0].slice(0),u.length>2&&"ID"===(f=u[0]).type&&r.getById&&9===t.nodeType&&d&&s.relative[u[1].type]){if(t=(s.find.ID(f.matches[0].replace(rt,it),t)||[])[0],!t)return n;e=e.slice(u.shift().value.length)}o=G.needsContext.test(e)?0:u.length;while(o--){if(f=u[o],s.relative[l=f.type])break;if((c=s.find[l])&&(i=c(f.matches[0].replace(rt,it),$.test(u[0].type)&&t.parentNode||t))){if(u.splice(o,1),e=i.length&&gt(u),!e)return H.apply(n,i),n;break}}}return a(e,h)(i,t,!d,n,$.test(e)),n}var n,r,i,s,o,u,a,f,l,c,h,p,d,v,m,g,y,b="sizzle"+ -(new Date),w=e.document,E=0,S=0,T=ut(),N=ut(),C=ut(),k=!1,L=function(e,t){return e===t?(k=!0,0):0},A=typeof t,O=1<<31,M={}.hasOwnProperty,_=[],D=_.pop,P=_.push,H=_.push,B=_.slice,j=_.indexOf||function(e){var t=0,n=this.length;for(;n>t;t++)if(this[t]===e)return t;return-1},F="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",I="[\\x20\\t\\r\\n\\f]",q="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",R=q.replace("w","w#"),U="\\["+I+"*("+q+")"+I+"*(?:([*^$|!~]?=)"+I+"*(?:(['\"])((?:\\\\.|[^\\\\])*?)\\3|("+R+")|)|)"+I+"*\\]",z=":("+q+")(?:\\(((['\"])((?:\\\\.|[^\\\\])*?)\\3|((?:\\\\.|[^\\\\()[\\]]|"+U.replace(3,8)+")*)|.*)\\)|)",W=RegExp("^"+I+"+|((?:^|[^\\\\])(?:\\\\.)*)"+I+"+$","g"),X=RegExp("^"+I+"*,"+I+"*"),V=RegExp("^"+I+"*([>+~]|"+I+")"+I+"*"),$=RegExp(I+"*[+~]"),J=RegExp("="+I+"*([^\\]'\"]*)"+I+"*\\]","g"),K=RegExp(z),Q=RegExp("^"+R+"$"),G={ID:RegExp("^#("+q+")"),CLASS:RegExp("^\\.("+q+")"),TAG:RegExp("^("+q.replace("w","w*")+")"),ATTR:RegExp("^"+U),PSEUDO:RegExp("^"+z),CHILD:RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+I+"*(even|odd|(([+-]|)(\\d*)n|)"+I+"*(?:([+-]|)"+I+"*(\\d+)|))"+I+"*\\)|)","i"),bool:RegExp("^(?:"+F+")$","i"),needsContext:RegExp("^"+I+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+I+"*((?:-\\d)?\\d*)"+I+"*\\)|)(?=[^-]|$)","i")},Y=/^[^{]+\{\s*\[native \w/,Z=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,et=/^(?:input|select|textarea|button)$/i,tt=/^h\d$/i,nt=/'|\\/g,rt=RegExp("\\\\([\\da-f]{1,6}"+I+"?|("+I+")|.)","ig"),it=function(e,t,n){var r="0x"+t-65536;return r!==r||n?t:0>r?String.fromCharCode(r+65536):String.fromCharCode(55296|r>>10,56320|1023&r)};try{H.apply(_=B.call(w.childNodes),w.childNodes),_[w.childNodes.length].nodeType}catch(st){H={apply:_.length?function(e,t){P.apply(e,B.call(t))}:function(e,t){var n=e.length,r=0;while(e[n++]=t[r++]);e.length=n-1}}}u=ot.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return t?"HTML"!==t.nodeName:!1},r=ot.support={},c=ot.setDocument=function(e){var n=e?e.ownerDocument||e:w,i=n.defaultView;return n!==h&&9===n.nodeType&&n.documentElement?(h=n,p=n.documentElement,d=!u(n),i&&i.attachEvent&&i!==i.top&&i.attachEvent("onbeforeunload",function(){c()}),r.attributes=ft(function(e){return e.className="i",!e.getAttribute("className")}),r.getElementsByTagName=ft(function(e){return e.appendChild(n.createComment("")),!e.getElementsByTagName("*").length}),r.getElementsByClassName=ft(function(e){return e.innerHTML="<div class='a'></div><div class='a i'></div>",e.firstChild.className="i",2===e.getElementsByClassName("i").length}),r.getById=ft(function(e){return p.appendChild(e).id=b,!n.getElementsByName||!n.getElementsByName(b).length}),r.getById?(s.find.ID=function(e,t){if(typeof t.getElementById!==A&&d){var n=t.getElementById(e);return n&&n.parentNode?[n]:[]}},s.filter.ID=function(e){var t=e.replace(rt,it);return function(e){return e.getAttribute("id")===t}}):(delete s.find.ID,s.filter.ID=function(e){var t=e.replace(rt,it);return function(e){var n=typeof e.getAttributeNode!==A&&e.getAttributeNode("id");return n&&n.value===t}}),s.find.TAG=r.getElementsByTagName?function(e,n){return typeof n.getElementsByTagName!==A?n.getElementsByTagName(e):t}:function(e,t){var n,r=[],i=0,s=t.getElementsByTagName(e);if("*"===e){while(n=s[i++])1===n.nodeType&&r.push(n);return r}return s},s.find.CLASS=r.getElementsByClassName&&function(e,n){return typeof n.getElementsByClassName!==A&&d?n.getElementsByClassName(e):t},m=[],v=[],(r.qsa=Y.test(n.querySelectorAll))&&(ft(function(e){e.innerHTML="<select><option selected=''></option></select>",e.querySelectorAll("[selected]").length||v.push("\\["+I+"*(?:value|"+F+")"),e.querySelectorAll(":checked").length||v.push(":checked")}),ft(function(e){var t=n.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("t",""),e.querySelectorAll("[t^='']").length&&v.push("[*^$]="+I+"*(?:''|\"\")"),e.querySelectorAll(":enabled").length||v.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),v.push(",.*:")})),(r.matchesSelector=Y.test(g=p.webkitMatchesSelector||p.mozMatchesSelector||p.oMatchesSelector||p.msMatchesSelector))&&ft(function(e){r.disconnectedMatch=g.call(e,"div"),g.call(e,"[s!='']:x"),m.push("!=",z)}),v=v.length&&RegExp(v.join("|")),m=m.length&&RegExp(m.join("|")),y=Y.test(p.contains)||p.compareDocumentPosition?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!!r&&1===r.nodeType&&!!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r))}:function(e,t){if(t)while(t=t.parentNode)if(t===e)return!0;return!1},L=p.compareDocumentPosition?function(e,t){if(e===t)return k=!0,0;var i=t.compareDocumentPosition&&e.compareDocumentPosition&&e.compareDocumentPosition(t);return i?1&i||!r.sortDetached&&t.compareDocumentPosition(e)===i?e===n||y(w,e)?-1:t===n||y(w,t)?1:l?j.call(l,e)-j.call(l,t):0:4&i?-1:1:e.compareDocumentPosition?-1:1}:function(e,t){var r,i=0,s=e.parentNode,o=t.parentNode,u=[e],a=[t];if(e===t)return k=!0,0;if(!s||!o)return e===n?-1:t===n?1:s?-1:o?1:l?j.call(l,e)-j.call(l,t):0;if(s===o)return ct(e,t);r=e;while(r=r.parentNode)u.unshift(r);r=t;while(r=r.parentNode)a.unshift(r);while(u[i]===a[i])i++;return i?ct(u[i],a[i]):u[i]===w?-1:a[i]===w?1:0},n):h},ot.matches=function(e,t){return ot(e,null,null,t)},ot.matchesSelector=function(e,t){if((e.ownerDocument||e)!==h&&c(e),t=t.replace(J,"='$1']"),!(!r.matchesSelector||!d||m&&m.test(t)||v&&v.test(t)))try{var n=g.call(e,t);if(n||r.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(i){}return ot(t,h,null,[e]).length>0},ot.contains=function(e,t){return(e.ownerDocument||e)!==h&&c(e),y(e,t)},ot.attr=function(e,n){(e.ownerDocument||e)!==h&&c(e);var i=s.attrHandle[n.toLowerCase()],o=i&&M.call(s.attrHandle,n.toLowerCase())?i(e,n,!d):t;return o===t?r.attributes||!d?e.getAttribute(n):(o=e.getAttributeNode(n))&&o.specified?o.value:null:o},ot.error=function(e){throw Error("Syntax error, unrecognized expression: "+e)},ot.uniqueSort=function(e){var t,n=[],i=0,s=0;if(k=!r.detectDuplicates,l=!r.sortStable&&e.slice(0),e.sort(L),k){while(t=e[s++])t===e[s]&&(i=n.push(s));while(i--)e.splice(n[i],1)}return e},o=ot.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===i||4===i)return e.nodeValue}else for(;t=e[r];r++)n+=o(t);return n},s=ot.selectors={cacheLength:50,createPseudo:at,match:G,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(rt,it),e[3]=(e[4]||e[5]||"").replace(rt,it),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||ot.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&ot.error(e[0]),e},PSEUDO:function(e){var n,r=!e[5]&&e[2];return G.CHILD.test(e[0])?null:(e[3]&&e[4]!==t?e[2]=e[4]:r&&K.test(r)&&(n=mt(r,!0))&&(n=r.indexOf(")",r.length-n)-r.length)&&(e[0]=e[0].slice(0,n),e[2]=r.slice(0,n)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(rt,it).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=T[e+" "];return t||(t=RegExp("(^|"+I+")"+e+"("+I+"|$)"))&&T(e,function(e){return t.test("string"==typeof e.className&&e.className||typeof e.getAttribute!==A&&e.getAttribute("class")||"")})},ATTR:function(e,t,n){return function(r){var i=ot.attr(r,e);return null==i?"!="===t:t?(i+="","="===t?i===n:"!="===t?i!==n:"^="===t?n&&0===i.indexOf(n):"*="===t?n&&i.indexOf(n)>-1:"$="===t?n&&i.slice(-n.length)===n:"~="===t?(" "+i+" ").indexOf(n)>-1:"|="===t?i===n||i.slice(0,n.length+1)===n+"-":!1):!0}},CHILD:function(e,t,n,r,i){var s="nth"!==e.slice(0,3),o="last"!==e.slice(-4),u="of-type"===t;return 1===r&&0===i?function(e){return!!e.parentNode}:function(t,n,a){var f,l,c,h,p,d,v=s!==o?"nextSibling":"previousSibling",m=t.parentNode,g=u&&t.nodeName.toLowerCase(),y=!a&&!u;if(m){if(s){while(v){c=t;while(c=c[v])if(u?c.nodeName.toLowerCase()===g:1===c.nodeType)return!1;d=v="only"===e&&!d&&"nextSibling"}return!0}if(d=[o?m.firstChild:m.lastChild],o&&y){l=m[b]||(m[b]={}),f=l[e]||[],p=f[0]===E&&f[1],h=f[0]===E&&f[2],c=p&&m.childNodes[p];while(c=++p&&c&&c[v]||(h=p=0)||d.pop())if(1===c.nodeType&&++h&&c===t){l[e]=[E,p,h];break}}else if(y&&(f=(t[b]||(t[b]={}))[e])&&f[0]===E)h=f[1];else while(c=++p&&c&&c[v]||(h=p=0)||d.pop())if((u?c.nodeName.toLowerCase()===g:1===c.nodeType)&&++h&&(y&&((c[b]||(c[b]={}))[e]=[E,h]),c===t))break;return h-=i,h===r||0===h%r&&h/r>=0}}},PSEUDO:function(e,t){var n,r=s.pseudos[e]||s.setFilters[e.toLowerCase()]||ot.error("unsupported pseudo: "+e);return r[b]?r(t):r.length>1?(n=[e,e,"",t],s.setFilters.hasOwnProperty(e.toLowerCase())?at(function(e,n){var i,s=r(e,t),o=s.length;while(o--)i=j.call(e,s[o]),e[i]=!(n[i]=s[o])}):function(e){return r(e,0,n)}):r}},pseudos:{not:at(function(e){var t=[],n=[],r=a(e.replace(W,"$1"));return r[b]?at(function(e,t,n,i){var s,o=r(e,null,i,[]),u=e.length;while(u--)(s=o[u])&&(e[u]=!(t[u]=s))}):function(e,i,s){return t[0]=e,r(t,null,s,n),!n.pop()}}),has:at(function(e){return function(t){return ot(e,t).length>0}}),contains:at(function(e){return function(t){return(t.textContent||t.innerText||o(t)).indexOf(e)>-1}}),lang:at(function(e){return Q.test(e||"")||ot.error("unsupported lang: "+e),e=e.replace(rt,it).toLowerCase(),function(t){var n;do if(n=d?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return n=n.toLowerCase(),n===e||0===n.indexOf(e+"-");while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===p},focus:function(e){return e===h.activeElement&&(!h.hasFocus||h.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return e.disabled===!1},disabled:function(e){return e.disabled===!0},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,e.selected===!0},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeName>"@"||3===e.nodeType||4===e.nodeType)return!1;return!0},parent:function(e){return!s.pseudos.empty(e)},header:function(e){return tt.test(e.nodeName)},input:function(e){return et.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||t.toLowerCase()===e.type)},first:dt(function(){return[0]}),last:dt(function(e,t){return[t-1]}),eq:dt(function(e,t,n){return[0>n?n+t:n]}),even:dt(function(e,t){var n=0;for(;t>n;n+=2)e.push(n);return e}),odd:dt(function(e,t){var n=1;for(;t>n;n+=2)e.push(n);return e}),lt:dt(function(e,t,n){var r=0>n?n+t:n;for(;--r>=0;)e.push(r);return e}),gt:dt(function(e,t,n){var r=0>n?n+t:n;for(;t>++r;)e.push(r);return e})}},s.pseudos.nth=s.pseudos.eq;for(n in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})s.pseudos[n]=ht(n);for(n in{submit:!0,reset:!0})s.pseudos[n]=pt(n);vt.prototype=s.filters=s.pseudos,s.setFilters=new vt,a=ot.compile=function(e,t){var n,r=[],i=[],s=C[e+" "];if(!s){t||(t=mt(e)),n=t.length;while(n--)s=St(t[n]),s[b]?r.push(s):i.push(s);s=C(e,xt(i,r))}return s},r.sortStable=b.split("").sort(L).join("")===b,r.detectDuplicates=k,c(),r.sortDetached=ft(function(e){return 1&e.compareDocumentPosition(h.createElement("div"))}),ft(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||lt("type|href|height|width",function(e,n,r){return r?t:e.getAttribute(n,"type"===n.toLowerCase()?1:2)}),r.attributes&&ft(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||lt("value",function(e,n,r){return r||"input"!==e.nodeName.toLowerCase()?t:e.defaultValue}),ft(function(e){return null==e.getAttribute("disabled")})||lt(F,function(e,n,r){var i;return r?t:(i=e.getAttributeNode(n))&&i.specified?i.value:e[n]===!0?n.toLowerCase():null}),x.find=ot,x.expr=ot.selectors,x.expr[":"]=x.expr.pseudos,x.unique=ot.uniqueSort,x.text=ot.getText,x.isXMLDoc=ot.isXML,x.contains=ot.contains}(e);var D={};x.Callbacks=function(e){e="string"==typeof e?D[e]||A(e):x.extend({},e);var t,n,r,i,s,o,u=[],a=!e.once&&[],f=function(h){for(t=e.memory&&h,n=!0,o=i||0,i=0,s=u.length,r=!0;u&&s>o;o++)if(u[o].apply(h[0],h[1])===!1&&e.stopOnFalse){t=!1;break}r=!1,u&&(a?a.length&&f(a.shift()):t?u=[]:l.disable())},l={add:function(){if(u){var n=u.length;(function o(t){x.each(t,function(t,n){var r=x.type(n);"function"===r?e.unique&&l.has(n)||u.push(n):n&&n.length&&"string"!==r&&o(n)})})(arguments),r?s=u.length:t&&(i=n,f(t))}return this},remove:function(){return u&&x.each(arguments,function(e,t){var n;while((n=x.inArray(t,u,n))>-1)u.splice(n,1),r&&(s>=n&&s--,o>=n&&o--)}),this},has:function(e){return e?x.inArray(e,u)>-1:!!u&&!!u.length},empty:function(){return u=[],s=0,this},disable:function(){return u=a=t=undefined,this},disabled:function(){return!u},lock:function(){return a=undefined,t||l.disable(),this},locked:function(){return!a},fireWith:function(e,t){return!u||n&&!a||(t=t||[],t=[e,t.slice?t.slice():t],r?a.push(t):f(t)),this},fire:function(){return l.fireWith(this,arguments),this},fired:function(){return!!n}};return l},x.extend({Deferred:function(e){var t=[["resolve","done",x.Callbacks("once memory"),"resolved"],["reject","fail",x.Callbacks("once memory"),"rejected"],["notify","progress",x.Callbacks("memory")]],n="pending",r={state:function(){return n},always:function(){return i.done(arguments).fail(arguments),this},then:function(){var e=arguments;return x.Deferred(function(n){x.each(t,function(t,s){var o=s[0],u=x.isFunction(e[t])&&e[t];i[s[1]](function(){var e=u&&u.apply(this,arguments);e&&x.isFunction(e.promise)?e.promise().done(n.resolve).fail(n.reject).progress(n.notify):n[o+"With"](this===r?n.promise():this,u?[e]:arguments)})}),e=null}).promise()},promise:function(e){return null!=e?x.extend(e,r):r}},i={};return r.pipe=r.then,x.each(t,function(e,s){var o=s[2],u=s[3];r[s[1]]=o.add,u&&o.add(function(){n=u},t[1^e][2].disable,t[2][2].lock),i[s[0]]=function(){return i[s[0]+"With"](this===i?r:this,arguments),this},i[s[0]+"With"]=o.fireWith}),r.promise(i),e&&e.call(i,i),i},when:function(e){var t=0,n=d.call(arguments),r=n.length,i=1!==r||e&&x.isFunction(e.promise)?r:0,s=1===i?e:x.Deferred(),o=function(e,t,n){return function(r){t[e]=this,n[e]=arguments.length>1?d.call(arguments):r,n===u?s.notifyWith(t,n):--i||s.resolveWith(t,n)}},u,a,f;if(r>1)for(u=Array(r),a=Array(r),f=Array(r);r>t;t++)n[t]&&x.isFunction(n[t].promise)?n[t].promise().done(o(t,f,n)).fail(s.reject).progress(o(t,a,u)):--i;return i||s.resolveWith(f,n),s.promise()}}),x.support=function(t){var n=o.createElement("input"),r=o.createDocumentFragment(),i=o.createElement("div"),s=o.createElement("select"),u=s.appendChild(o.createElement("option"));return n.type?(n.type="checkbox",t.checkOn=""!==n.value,t.optSelected=u.selected,t.reliableMarginRight=!0,t.boxSizingReliable=!0,t.pixelPosition=!1,n.checked=!0,t.noCloneChecked=n.cloneNode(!0).checked,s.disabled=!0,t.optDisabled=!u.disabled,n=o.createElement("input"),n.value="t",n.type="radio",t.radioValue="t"===n.value,n.setAttribute("checked","t"),n.setAttribute("name","t"),r.appendChild(n),t.checkClone=r.cloneNode(!0).cloneNode(!0).lastChild.checked,t.focusinBubbles="onfocusin"in e,i.style.backgroundClip="content-box",i.cloneNode(!0).style.backgroundClip="",t.clearCloneStyle="content-box"===i.style.backgroundClip,x(function(){var n,r,s="padding:0;margin:0;border:0;display:block;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box",u=o.getElementsByTagName("body")[0];u&&(n=o.createElement("div"),n.style.cssText="border:0;width:0;height:0;position:absolute;top:0;left:-9999px;margin-top:1px",u.appendChild(n).appendChild(i),i.innerHTML="",i.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;padding:1px;border:1px;display:block;width:4px;margin-top:1%;position:absolute;top:1%",x.swap(u,null!=u.style.zoom?{zoom:1}:{},function(){t.boxSizing=4===i.offsetWidth}),e.getComputedStyle&&(t.pixelPosition="1%"!==(e.getComputedStyle(i,null)||{}).top,t.boxSizingReliable="4px"===(e.getComputedStyle(i,null)||{width:"4px"}).width,r=i.appendChild(o.createElement("div")),r.style.cssText=i.style.cssText=s,r.style.marginRight=r.style.width="0",i.style.width="1px",t.reliableMarginRight=!parseFloat((e.getComputedStyle(r,null)||{}).marginRight)),u.removeChild(n))}),t):t}({});var L,q,H=/(?:\{[\s\S]*\}|\[[\s\S]*\])$/,O=/([A-Z])/g;F.uid=1,F.accepts=function(e){return e.nodeType?1===e.nodeType||9===e.nodeType:!0},F.prototype={key:function(e){if(!F.accepts(e))return 0;var t={},n=e[this.expando];if(!n){n=F.uid++;try{t[this.expando]={value:n},Object.defineProperties(e,t)}catch(r){t[this.expando]=n,x.extend(e,t)}}return this.cache[n]||(this.cache[n]={}),n},set:function(e,t,n){var r,i=this.key(e),s=this.cache[i];if("string"==typeof t)s[t]=n;else if(x.isEmptyObject(s))x.extend(this.cache[i],t);else for(r in t)s[r]=t[r];return s},get:function(e,t){var n=this.cache[this.key(e)];return t===undefined?n:n[t]},access:function(e,t,n){var r;return t===undefined||t&&"string"==typeof t&&n===undefined?(r=this.get(e,t),r!==undefined?r:this.get(e,x.camelCase(t))):(this.set(e,t,n),n!==undefined?n:t)},remove:function(e,t){var n,r,i,s=this.key(e),o=this.cache[s];if(t===undefined)this.cache[s]={};else{x.isArray(t)?r=t.concat(t.map(x.camelCase)):(i=x.camelCase(t),t in o?r=[t,i]:(r=i,r=r in o?[r]:r.match(w)||[])),n=r.length;while(n--)delete o[r[n]]}},hasData:function(e){return!x.isEmptyObject(this.cache[e[this.expando]]||{})},discard:function(e){e[this.expando]&&delete this.cache[e[this.expando]]}},L=new F,q=new F,x.extend({acceptData:F.accepts,hasData:function(e){return L.hasData(e)||q.hasData(e)},data:function(e,t,n){return L.access(e,t,n)},removeData:function(e,t){L.remove(e,t)},_data:function(e,t,n){return q.access(e,t,n)},_removeData:function(e,t){q.remove(e,t)}}),x.fn.extend({data:function(e,t){var n,r,i=this[0],s=0,o=null;if(e===undefined){if(this.length&&(o=L.get(i),1===i.nodeType&&!q.get(i,"hasDataAttrs"))){for(n=i.attributes;n.length>s;s++)r=n[s].name,0===r.indexOf("data-")&&(r=x.camelCase(r.slice(5)),P(i,r,o[r]));q.set(i,"hasDataAttrs",!0)}return o}return"object"==typeof e?this.each(function(){L.set(this,e)}):x.access(this,function(t){var n,r=x.camelCase(e);if(i&&t===undefined){if(n=L.get(i,e),n!==undefined)return n;if(n=L.get(i,r),n!==undefined)return n;if(n=P(i,r,undefined),n!==undefined)return n}else this.each(function(){var n=L.get(this,r);L.set(this,r,t),-1!==e.indexOf("-")&&n!==undefined&&L.set(this,e,t)})},null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each(function(){L.remove(this,e)})}}),x.extend({queue:function(e,t,n){var r;return e?(t=(t||"fx")+"queue",r=q.get(e,t),n&&(!r||x.isArray(n)?r=q.access(e,t,x.makeArray(n)):r.push(n)),r||[]):undefined},dequeue:function(e,t){t=t||"fx";var n=x.queue(e,t),r=n.length,i=n.shift(),s=x._queueHooks(e,t),o=function(){x.dequeue(e,t)};"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete s.stop,i.call(e,o,s)),!r&&s&&s.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return q.get(e,n)||q.access(e,n,{empty:x.Callbacks("once memory").add(function(){q.remove(e,[t+"queue",n])})})}}),x.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),n>arguments.length?x.queue(this[0],e):t===undefined?this:this.each(function(){var n=x.queue(this,e,t);x._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&x.dequeue(this,e)})},dequeue:function(e){return this.each(function(){x.dequeue(this,e)})},delay:function(e,t){return e=x.fx?x.fx.speeds[e]||e:e,t=t||"fx",this.queue(t,function(t,n){var r=setTimeout(t,e);n.stop=function(){clearTimeout(r)}})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=x.Deferred(),s=this,o=this.length,u=function(){--r||i.resolveWith(s,[s])};"string"!=typeof e&&(t=e,e=undefined),e=e||"fx";while(o--)n=q.get(s[o],e+"queueHooks"),n&&n.empty&&(r++,n.empty.add(u));return u(),i.promise(t)}});var R,M,W=/[\t\r\n\f]/g,$=/\r/g,B=/^(?:input|select|textarea|button)$/i;x.fn.extend({attr:function(e,t){return x.access(this,x.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){x.removeAttr(this,e)})},prop:function(e,t){return x.access(this,x.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[x.propFix[e]||e]})},addClass:function(e){var t,n,r,i,s,o=0,u=this.length,a="string"==typeof e&&e;if(x.isFunction(e))return this.each(function(t){x(this).addClass(e.call(this,t,this.className))});if(a)for(t=(e||"").match(w)||[];u>o;o++)if(n=this[o],r=1===n.nodeType&&(n.className?(" "+n.className+" ").replace(W," "):" ")){s=0;while(i=t[s++])0>r.indexOf(" "+i+" ")&&(r+=i+" ");n.className=x.trim(r)}return this},removeClass:function(e){var t,n,r,i,s,o=0,u=this.length,a=0===arguments.length||"string"==typeof e&&e;if(x.isFunction(e))return this.each(function(t){x(this).removeClass(e.call(this,t,this.className))});if(a)for(t=(e||"").match(w)||[];u>o;o++)if(n=this[o],r=1===n.nodeType&&(n.className?(" "+n.className+" ").replace(W," "):"")){s=0;while(i=t[s++])while(r.indexOf(" "+i+" ")>=0)r=r.replace(" "+i+" "," ");n.className=e?x.trim(r):""}return this},toggleClass:function(e,t){var n=typeof e;return"boolean"==typeof t&&"string"===n?t?this.addClass(e):this.removeClass(e):x.isFunction(e)?this.each(function(n){x(this).toggleClass(e.call(this,n,this.className,t),t)}):this.each(function(){if("string"===n){var t,i=0,s=x(this),o=e.match(w)||[];while(t=o[i++])s.hasClass(t)?s.removeClass(t):s.addClass(t)}else(n===r||"boolean"===n)&&(this.className&&q.set(this,"__className__",this.className),this.className=this.className||e===!1?"":q.get(this,"__className__")||"")})},hasClass:function(e){var t=" "+e+" ",n=0,r=this.length;for(;r>n;n++)if(1===this[n].nodeType&&(" "+this[n].className+" ").replace(W," ").indexOf(t)>=0)return!0;return!1},val:function(e){var t,n,r,i=this[0];if(arguments.length)return r=x.isFunction(e),this.each(function(n){var i;1===this.nodeType&&(i=r?e.call(this,n,x(this).val()):e,null==i?i="":"number"==typeof i?i+="":x.isArray(i)&&(i=x.map(i,function(e){return null==e?"":e+""})),t=x.valHooks[this.type]||x.valHooks[this.nodeName.toLowerCase()],t&&"set"in t&&t.set(this,i,"value")!==undefined||(this.value=i))});if(i)return t=x.valHooks[i.type]||x.valHooks[i.nodeName.toLowerCase()],t&&"get"in t&&(n=t.get(i,"value"))!==undefined?n:(n=i.value,"string"==typeof n?n.replace($,""):null==n?"":n)}}),x.extend({valHooks:{option:{get:function(e){var t=e.attributes.value;return!t||t.specified?e.value:e.text}},select:{get:function(e){var t,n,r=e.options,i=e.selectedIndex,s="select-one"===e.type||0>i,o=s?null:[],u=s?i+1:r.length,a=0>i?u:s?i:0;for(;u>a;a++)if(n=r[a],!(!n.selected&&a!==i||(x.support.optDisabled?n.disabled:null!==n.getAttribute("disabled"))||n.parentNode.disabled&&x.nodeName(n.parentNode,"optgroup"))){if(t=x(n).val(),s)return t;o.push(t)}return o},set:function(e,t){var n,r,i=e.options,s=x.makeArray(t),o=i.length;while(o--)r=i[o],(r.selected=x.inArray(x(r).val(),s)>=0)&&(n=!0);return n||(e.selectedIndex=-1),s}}},attr:function(e,t,n){var i,s,o=e.nodeType;if(e&&3!==o&&8!==o&&2!==o)return typeof e.getAttribute===r?x.prop(e,t,n):(1===o&&x.isXMLDoc(e)||(t=t.toLowerCase(),i=x.attrHooks[t]||(x.expr.match.bool.test(t)?M:R)),n===undefined?i&&"get"in i&&null!==(s=i.get(e,t))?s:(s=x.find.attr(e,t),null==s?undefined:s):null!==n?i&&"set"in i&&(s=i.set(e,n,t))!==undefined?s:(e.setAttribute(t,n+""),n):(x.removeAttr(e,t),undefined))},removeAttr:function(e,t){var n,r,i=0,s=t&&t.match(w);if(s&&1===e.nodeType)while(n=s[i++])r=x.propFix[n]||n,x.expr.match.bool.test(n)&&(e[r]=!1),e.removeAttribute(n)},attrHooks:{type:{set:function(e,t){if(!x.support.radioValue&&"radio"===t&&x.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},propFix:{"for":"htmlFor","class":"className"},prop:function(e,t,n){var r,i,s,o=e.nodeType;if(e&&3!==o&&8!==o&&2!==o)return s=1!==o||!x.isXMLDoc(e),s&&(t=x.propFix[t]||t,i=x.propHooks[t]),n!==undefined?i&&"set"in i&&(r=i.set(e,n,t))!==undefined?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){return e.hasAttribute("tabindex")||B.test(e.nodeName)||e.href?e.tabIndex:-1}}}}),M={set:function(e,t,n){return t===!1?x.removeAttr(e,n):e.setAttribute(n,n),n}},x.each(x.expr.match.bool.source.match(/\w+/g),function(e,t){var n=x.expr.attrHandle[t]||x.find.attr;x.expr.attrHandle[t]=function(e,t,r){var i=x.expr.attrHandle[t],s=r?undefined:(x.expr.attrHandle[t]=undefined)!=n(e,t,r)?t.toLowerCase():null;return x.expr.attrHandle[t]=i,s}}),x.support.optSelected||(x.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null}}),x.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){x.propFix[this.toLowerCase()]=this}),x.each(["radio","checkbox"],function(){x.valHooks[this]={set:function(e,t){return x.isArray(t)?e.checked=x.inArray(x(e).val(),t)>=0:undefined}},x.support.checkOn||(x.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var I=/^key/,z=/^(?:mouse|contextmenu)|click/,_=/^(?:focusinfocus|focusoutblur)$/,X=/^([^.]*)(?:\.(.+)|)$/;x.event={global:{},add:function(e,t,n,i,s){var o,u,a,f,l,c,h,p,d,v,m,g=q.get(e);if(g){n.handler&&(o=n,n=o.handler,s=o.selector),n.guid||(n.guid=x.guid++),(f=g.events)||(f=g.events={}),(u=g.handle)||(u=g.handle=function(e){return typeof x===r||e&&x.event.triggered===e.type?undefined:x.event.dispatch.apply(u.elem,arguments)},u.elem=e),t=(t||"").match(w)||[""],l=t.length;while(l--)a=X.exec(t[l])||[],d=m=a[1],v=(a[2]||"").split(".").sort(),d&&(h=x.event.special[d]||{},d=(s?h.delegateType:h.bindType)||d,h=x.event.special[d]||{},c=x.extend({type:d,origType:m,data:i,handler:n,guid:n.guid,selector:s,needsContext:s&&x.expr.match.needsContext.test(s),namespace:v.join(".")},o),(p=f[d])||(p=f[d]=[],p.delegateCount=0,h.setup&&h.setup.call(e,i,v,u)!==!1||e.addEventListener&&e.addEventListener(d,u,!1)),h.add&&(h.add.call(e,c),c.handler.guid||(c.handler.guid=n.guid)),s?p.splice(p.delegateCount++,0,c):p.push(c),x.event.global[d]=!0);e=null}},remove:function(e,t,n,r,i){var s,o,u,a,f,l,c,h,p,d,v,m=q.hasData(e)&&q.get(e);if(m&&(a=m.events)){t=(t||"").match(w)||[""],f=t.length;while(f--)if(u=X.exec(t[f])||[],p=v=u[1],d=(u[2]||"").split(".").sort(),p){c=x.event.special[p]||{},p=(r?c.delegateType:c.bindType)||p,h=a[p]||[],u=u[2]&&RegExp("(^|\\.)"+d.join("\\.(?:.*\\.|)")+"(\\.|$)"),o=s=h.length;while(s--)l=h[s],!i&&v!==l.origType||n&&n.guid!==l.guid||u&&!u.test(l.namespace)||r&&r!==l.selector&&("**"!==r||!l.selector)||(h.splice(s,1),l.selector&&h.delegateCount--,c.remove&&c.remove.call(e,l));o&&!h.length&&(c.teardown&&c.teardown.call(e,d,m.handle)!==!1||x.removeEvent(e,p,m.handle),delete a[p])}else for(p in a)x.event.remove(e,p+t[f],n,r,!0);x.isEmptyObject(a)&&(delete m.handle,q.remove(e,"events"))}},trigger:function(t,n,r,i){var s,u,a,f,l,c,h,p=[r||o],d=y.call(t,"type")?t.type:t,v=y.call(t,"namespace")?t.namespace.split("."):[];if(u=a=r=r||o,3!==r.nodeType&&8!==r.nodeType&&!_.test(d+x.event.triggered)&&(d.indexOf(".")>=0&&(v=d.split("."),d=v.shift(),v.sort()),l=0>d.indexOf(":")&&"on"+d,t=t[x.expando]?t:new x.Event(d,"object"==typeof t&&t),t.isTrigger=i?2:3,t.namespace=v.join("."),t.namespace_re=t.namespace?RegExp("(^|\\.)"+v.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=undefined,t.target||(t.target=r),n=null==n?[t]:x.makeArray(n,[t]),h=x.event.special[d]||{},i||!h.trigger||h.trigger.apply(r,n)!==!1)){if(!i&&!h.noBubble&&!x.isWindow(r)){for(f=h.delegateType||d,_.test(f+d)||(u=u.parentNode);u;u=u.parentNode)p.push(u),a=u;a===(r.ownerDocument||o)&&p.push(a.defaultView||a.parentWindow||e)}s=0;while((u=p[s++])&&!t.isPropagationStopped())t.type=s>1?f:h.bindType||d,c=(q.get(u,"events")||{})[t.type]&&q.get(u,"handle"),c&&c.apply(u,n),c=l&&u[l],c&&x.acceptData(u)&&c.apply&&c.apply(u,n)===!1&&t.preventDefault();return t.type=d,i||t.isDefaultPrevented()||h._default&&h._default.apply(p.pop(),n)!==!1||!x.acceptData(r)||l&&x.isFunction(r[d])&&!x.isWindow(r)&&(a=r[l],a&&(r[l]=null),x.event.triggered=d,r[d](),x.event.triggered=undefined,a&&(r[l]=a)),t.result}},dispatch:function(e){e=x.event.fix(e);var t,n,r,i,s,o=[],u=d.call(arguments),a=(q.get(this,"events")||{})[e.type]||[],f=x.event.special[e.type]||{};if(u[0]=e,e.delegateTarget=this,!f.preDispatch||f.preDispatch.call(this,e)!==!1){o=x.event.handlers.call(this,e,a),t=0;while((i=o[t++])&&!e.isPropagationStopped()){e.currentTarget=i.elem,n=0;while((s=i.handlers[n++])&&!e.isImmediatePropagationStopped())(!e.namespace_re||e.namespace_re.test(s.namespace))&&(e.handleObj=s,e.data=s.data,r=((x.event.special[s.origType]||{}).handle||s.handler).apply(i.elem,u),r!==undefined&&(e.result=r)===!1&&(e.preventDefault(),e.stopPropagation()))}return f.postDispatch&&f.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,r,i,s,o=[],u=t.delegateCount,a=e.target;if(u&&a.nodeType&&(!e.button||"click"!==e.type))for(;a!==this;a=a.parentNode||this)if(a.disabled!==!0||"click"!==e.type){for(r=[],n=0;u>n;n++)s=t[n],i=s.selector+" ",r[i]===undefined&&(r[i]=s.needsContext?x(i,this).index(a)>=0:x.find(i,this,null,[a]).length),r[i]&&r.push(s);r.length&&o.push({elem:a,handlers:r})}return t.length>u&&o.push({elem:this,handlers:t.slice(u)}),o},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,r,i,s=t.button;return null==e.pageX&&null!=t.clientX&&(n=e.target.ownerDocument||o,r=n.documentElement,i=n.body,e.pageX=t.clientX+(r&&r.scrollLeft||i&&i.scrollLeft||0)-(r&&r.clientLeft||i&&i.clientLeft||0),e.pageY=t.clientY+(r&&r.scrollTop||i&&i.scrollTop||0)-(r&&r.clientTop||i&&i.clientTop||0)),e.which||s===undefined||(e.which=1&s?1:2&s?3:4&s?2:0),e}},fix:function(e){if(e[x.expando])return e;var t,n,r,i=e.type,s=e,u=this.fixHooks[i];u||(this.fixHooks[i]=u=z.test(i)?this.mouseHooks:I.test(i)?this.keyHooks:{}),r=u.props?this.props.concat(u.props):this.props,e=new x.Event(s),t=r.length;while(t--)n=r[t],e[n]=s[n];return e.target||(e.target=o),3===e.target.nodeType&&(e.target=e.target.parentNode),u.filter?u.filter(e,s):e},special:{load:{noBubble:!0},focus:{trigger:function(){return this!==V()&&this.focus?(this.focus(),!1):undefined},delegateType:"focusin"},blur:{trigger:function(){return this===V()&&this.blur?(this.blur(),!1):undefined},delegateType:"focusout"},click:{trigger:function(){return"checkbox"===this.type&&this.click&&x.nodeName(this,"input")?(this.click(),!1):undefined},_default:function(e){return x.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){e.result!==undefined&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,n,r){var i=x.extend(new x.Event,n,{type:e,isSimulated:!0,originalEvent:{}});r?x.event.trigger(i,null,t):x.event.dispatch.call(t,i),i.isDefaultPrevented()&&n.preventDefault()}},x.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n,!1)},x.Event=function(e,t){return this instanceof x.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||e.getPreventDefault&&e.getPreventDefault()?U:Y):this.type=e,t&&x.extend(this,t),this.timeStamp=e&&e.timeStamp||x.now(),this[x.expando]=!0,undefined):new x.Event(e,t)},x.Event.prototype={isDefaultPrevented:Y,isPropagationStopped:Y,isImmediatePropagationStopped:Y,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=U,e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=U,e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=U,this.stopPropagation()}},x.each({mouseenter:"mouseover",mouseleave:"mouseout"},function(e,t){x.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=this,i=e.relatedTarget,s=e.handleObj;return(!i||i!==r&&!x.contains(r,i))&&(e.type=s.origType,n=s.handler.apply(this,arguments),e.type=t),n}}}),x.support.focusinBubbles||x.each({focus:"focusin",blur:"focusout"},function(e,t){var n=0,r=function(e){x.event.simulate(t,e.target,x.event.fix(e),!0)};x.event.special[t]={setup:function(){0===n++&&o.addEventListener(e,r,!0)},teardown:function(){0===--n&&o.removeEventListener(e,r,!0)}}}),x.fn.extend({on:function(e,t,n,r,i){var s,o;if("object"==typeof e){"string"!=typeof t&&(n=n||t,t=undefined);for(o in e)this.on(o,t,n,e[o],i);return this}if(null==n&&null==r?(r=t,n=t=undefined):null==r&&("string"==typeof t?(r=n,n=undefined):(r=n,n=t,t=undefined)),r===!1)r=Y;else if(!r)return this;return 1===i&&(s=r,r=function(e){return x().off(e),s.apply(this,arguments)},r.guid=s.guid||(s.guid=x.guid++)),this.each(function(){x.event.add(this,e,r,n,t)})},one:function(e,t,n,r){return this.on(e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,x(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(i in e)this.off(i,t,e[i]);return this}return(t===!1||"function"==typeof t)&&(n=t,t=undefined),n===!1&&(n=Y),this.each(function(){x.event.remove(this,e,n,t)})},trigger:function(e,t){return this.each(function(){x.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];return n?x.event.trigger(e,t,n,!0):undefined}});var G=/^.[^:#\[\.,]*$/,J=/^(?:parents|prev(?:Until|All))/,Q=x.expr.match.needsContext,K={children:!0,contents:!0,next:!0,prev:!0};x.fn.extend({find:function(e){var t,n=[],r=this,i=r.length;if("string"!=typeof e)return this.pushStack(x(e).filter(function(){for(t=0;i>t;t++)if(x.contains(r[t],this))return!0}));for(t=0;i>t;t++)x.find(e,r[t],n);return n=this.pushStack(i>1?x.unique(n):n),n.selector=this.selector?this.selector+" "+e:e,n},has:function(e){var t=x(e,this),n=t.length;return this.filter(function(){var e=0;for(;n>e;e++)if(x.contains(this,t[e]))return!0})},not:function(e){return this.pushStack(et(this,e||[],!0))},filter:function(e){return this.pushStack(et(this,e||[],!1))},is:function(e){return!!et(this,"string"==typeof e&&Q.test(e)?x(e):e||[],!1).length},closest:function(e,t){var n,r=0,i=this.length,s=[],o=Q.test(e)||"string"!=typeof e?x(e,t||this.context):0;for(;i>r;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(11>n.nodeType&&(o?o.index(n)>-1:1===n.nodeType&&x.find.matchesSelector(n,e))){n=s.push(n);break}return this.pushStack(s.length>1?x.unique(s):s)},index:function(e){return e?"string"==typeof e?g.call(x(e),this[0]):g.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){var n="string"==typeof e?x(e,t):x.makeArray(e&&e.nodeType?[e]:e),r=x.merge(this.get(),n);return this.pushStack(x.unique(r))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),x.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return x.dir(e,"parentNode")},parentsUntil:function(e,t,n){return x.dir(e,"parentNode",n)},next:function(e){return Z(e,"nextSibling")},prev:function(e){return Z(e,"previousSibling")},nextAll:function(e){return x.dir(e,"nextSibling")},prevAll:function(e){return x.dir(e,"previousSibling")},nextUntil:function(e,t,n){return x.dir(e,"nextSibling",n)},prevUntil:function(e,t,n){return x.dir(e,"previousSibling",n)},siblings:function(e){return x.sibling((e.parentNode||{}).firstChild,e)},children:function(e){return x.sibling(e.firstChild)},contents:function(e){return e.contentDocument||x.merge([],e.childNodes)}},function(e,t){x.fn[e]=function(n,r){var i=x.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=x.filter(r,i)),this.length>1&&(K[e]||x.unique(i),J.test(e)&&i.reverse()),this.pushStack(i)}}),x.extend({filter:function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?x.find.matchesSelector(r,e)?[r]:[]:x.find.matches(e,x.grep(t,function(e){return 1===e.nodeType}))},dir:function(e,t,n){var r=[],i=n!==undefined;while((e=e[t])&&9!==e.nodeType)if(1===e.nodeType){if(i&&x(e).is(n))break;r.push(e)}return r},sibling:function(e,t){var n=[];for(;e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}});var tt=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,nt=/<([\w:]+)/,rt=/<|&#?\w+;/,it=/<(?:script|style|link)/i,ot=/^(?:checkbox|radio)$/i,st=/checked\s*(?:[^=]|=\s*.checked.)/i,at=/^$|\/(?:java|ecma)script/i,ut=/^true\/(.*)/,lt=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,ct={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};ct.optgroup=ct.option,ct.tbody=ct.tfoot=ct.colgroup=ct.caption=ct.thead,ct.th=ct.td,x.fn.extend({text:function(e){return x.access(this,function(e){return e===undefined?x.text(this):this.empty().append((this[0]&&this[0].ownerDocument||o).createTextNode(e))},null,e,arguments.length)},append:function(){return this.domManip(arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=pt(this,e);t.appendChild(e)}})},prepend:function(){return this.domManip(arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=pt(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},remove:function(e,t){var n,r=e?x.filter(e,this):this,i=0;for(;null!=(n=r[i]);i++)t||1!==n.nodeType||x.cleanData(mt(n)),n.parentNode&&(t&&x.contains(n.ownerDocument,n)&&dt(mt(n,"script")),n.parentNode.removeChild(n));return this},empty:function(){var e,t=0;for(;null!=(e=this[t]);t++)1===e.nodeType&&(x.cleanData(mt(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null==e?!1:e,t=null==t?e:t,this.map(function(){return x.clone(this,e,t)})},html:function(e){return x.access(this,function(e){var t=this[0]||{},n=0,r=this.length;if(e===undefined&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!it.test(e)&&!ct[(nt.exec(e)||["",""])[1].toLowerCase()]){e=e.replace(tt,"<$1></$2>");try{for(;r>n;n++)t=this[n]||{},1===t.nodeType&&(x.cleanData(mt(t,!1)),t.innerHTML=e);t=0}catch(i){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=x.map(this,function(e){return[e.nextSibling,e.parentNode]}),t=0;return this.domManip(arguments,function(n){var r=e[t++],i=e[t++];i&&(r&&r.parentNode!==i&&(r=this.nextSibling),x(this).remove(),i.insertBefore(n,r))},!0),t?this:this.remove()},detach:function(e){return this.remove(e,!0)},domManip:function(e,t,n){e=f.apply([],e);var r,i,s,o,u,a,l=0,c=this.length,h=this,p=c-1,d=e[0],v=x.isFunction(d);if(v||!(1>=c||"string"!=typeof d||x.support.checkClone)&&st.test(d))return this.each(function(r){var i=h.eq(r);v&&(e[0]=d.call(this,r,i.html())),i.domManip(e,t,n)});if(c&&(r=x.buildFragment(e,this[0].ownerDocument,!1,!n&&this),i=r.firstChild,1===r.childNodes.length&&(r=i),i)){for(s=x.map(mt(r,"script"),ft),o=s.length;c>l;l++)u=r,l!==p&&(u=x.clone(u,!0,!0),o&&x.merge(s,mt(u,"script"))),t.call(this[l],u,l);if(o)for(a=s[s.length-1].ownerDocument,x.map(s,ht),l=0;o>l;l++)u=s[l],at.test(u.type||"")&&!q.access(u,"globalEval")&&x.contains(a,u)&&(u.src?x._evalUrl(u.src):x.globalEval(u.textContent.replace(lt,"")))}return this}}),x.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){x.fn[e]=function(e){var n,r=[],i=x(e),s=i.length-1,o=0;for(;s>=o;o++)n=o===s?this:this.clone(!0),x(i[o])[t](n),h.apply(r,n.get());return this.pushStack(r)}}),x.extend({clone:function(e,t,n){var r,i,s,o,u=e.cloneNode(!0),a=x.contains(e.ownerDocument,e);if(!(x.support.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||x.isXMLDoc(e)))for(o=mt(u),s=mt(e),r=0,i=s.length;i>r;r++)yt(s[r],o[r]);if(t)if(n)for(s=s||mt(e),o=o||mt(u),r=0,i=s.length;i>r;r++)gt(s[r],o[r]);else gt(e,u);return o=mt(u,"script"),o.length>0&&dt(o,!a&&mt(e,"script")),u},buildFragment:function(e,t,n,r){var i,s,o,u,a,f,l=0,c=e.length,h=t.createDocumentFragment(),p=[];for(;c>l;l++)if(i=e[l],i||0===i)if("object"===x.type(i))x.merge(p,i.nodeType?[i]:i);else if(rt.test(i)){s=s||h.appendChild(t.createElement("div")),o=(nt.exec(i)||["",""])[1].toLowerCase(),u=ct[o]||ct._default,s.innerHTML=u[1]+i.replace(tt,"<$1></$2>")+u[2],f=u[0];while(f--)s=s.lastChild;x.merge(p,s.childNodes),s=h.firstChild,s.textContent=""}else p.push(t.createTextNode(i));h.textContent="",l=0;while(i=p[l++])if((!r||-1===x.inArray(i,r))&&(a=x.contains(i.ownerDocument,i),s=mt(h.appendChild(i),"script"),a&&dt(s),n)){f=0;while(i=s[f++])at.test(i.type||"")&&n.push(i)}return h},cleanData:function(e){var t,n,r,i,s,o,u=x.event.special,a=0;for(;(n=e[a])!==undefined;a++){if(F.accepts(n)&&(s=n[q.expando],s&&(t=q.cache[s]))){if(r=Object.keys(t.events||{}),r.length)for(o=0;(i=r[o])!==undefined;o++)u[i]?x.event.remove(n,i):x.removeEvent(n,i,t.handle);q.cache[s]&&delete q.cache[s]}delete L.cache[n[L.expando]]}},_evalUrl:function(e){return x.ajax({url:e,type:"GET",dataType:"script",async:!1,global:!1,"throws":!0})}}),x.fn.extend({wrapAll:function(e){var t;return x.isFunction(e)?this.each(function(t){x(this).wrapAll(e.call(this,t))}):(this[0]&&(t=x(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){var e=this;while(e.firstElementChild)e=e.firstElementChild;return e}).append(this)),this)},wrapInner:function(e){return x.isFunction(e)?this.each(function(t){x(this).wrapInner(e.call(this,t))}):this.each(function(){var t=x(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=x.isFunction(e);return this.each(function(n){x(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(){return this.parent().each(function(){x.nodeName(this,"body")||x(this).replaceWith(this.childNodes)}).end()}});var vt,xt,bt=/^(none|table(?!-c[ea]).+)/,wt=/^margin/,Tt=RegExp("^("+b+")(.*)$","i"),Ct=RegExp("^("+b+")(?!px)[a-z%]+$","i"),kt=RegExp("^([+-])=("+b+")","i"),Nt={BODY:"block"},Et={position:"absolute",visibility:"hidden",display:"block"},St={letterSpacing:0,fontWeight:400},jt=["Top","Right","Bottom","Left"],Dt=["Webkit","O","Moz","ms"];x.fn.extend({css:function(e,t){return x.access(this,function(e,t,n){var r,i,s={},o=0;if(x.isArray(t)){for(r=qt(e),i=t.length;i>o;o++)s[t[o]]=x.css(e,t[o],!1,r);return s}return n!==undefined?x.style(e,t,n):x.css(e,t)},e,t,arguments.length>1)},show:function(){return Ht(this,!0)},hide:function(){return Ht(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){Lt(this)?x(this).show():x(this).hide()})}}),x.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=vt(e,"opacity");return""===n?"1":n}}}},cssNumber:{columnCount:!0,fillOpacity:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{"float":"cssFloat"},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,s,o,u=x.camelCase(t),a=e.style;return t=x.cssProps[u]||(x.cssProps[u]=At(a,u)),o=x.cssHooks[t]||x.cssHooks[u],n===undefined?o&&"get"in o&&(i=o.get(e,!1,r))!==undefined?i:a[t]:(s=typeof n,"string"===s&&(i=kt.exec(n))&&(n=(i[1]+1)*i[2]+parseFloat(x.css(e,t)),s="number"),null==n||"number"===s&&isNaN(n)||("number"!==s||x.cssNumber[u]||(n+="px"),x.support.clearCloneStyle||""!==n||0!==t.indexOf("background")||(a[t]="inherit"),o&&"set"in o&&(n=o.set(e,n,r))===undefined||(a[t]=n)),undefined)}},css:function(e,t,n,r){var i,s,o,u=x.camelCase(t);return t=x.cssProps[u]||(x.cssProps[u]=At(e.style,u)),o=x.cssHooks[t]||x.cssHooks[u],o&&"get"in o&&(i=o.get(e,!0,n)),i===undefined&&(i=vt(e,t,r)),"normal"===i&&t in St&&(i=St[t]),""===n||n?(s=parseFloat(i),n===!0||x.isNumeric(s)?s||0:i):i}}),vt=function(e,t,n){var r,i,s,o=n||qt(e),u=o?o.getPropertyValue(t)||o[t]:undefined,a=e.style;return o&&(""!==u||x.contains(e.ownerDocument,e)||(u=x.style(e,t)),Ct.test(u)&&wt.test(t)&&(r=a.width,i=a.minWidth,s=a.maxWidth,a.minWidth=a.maxWidth=a.width=u,u=o.width,a.width=r,a.minWidth=i,a.maxWidth=s)),u},x.each(["height","width"],function(e,t){x.cssHooks[t]={get:function(e,n,r){return n?0===e.offsetWidth&&bt.test(x.css(e,"display"))?x.swap(e,Et,function(){return Pt(e,t,r)}):Pt(e,t,r):undefined},set:function(e,n,r){var i=r&&qt(e);return Ot(e,n,r?Ft(e,t,r,x.support.boxSizing&&"border-box"===x.css(e,"boxSizing",!1,i),i):0)}}}),x(function(){x.support.reliableMarginRight||(x.cssHooks.marginRight={get:function(e,t){return t?x.swap(e,{display:"inline-block"},vt,[e,"marginRight"]):undefined}}),!x.support.pixelPosition&&x.fn.position&&x.each(["top","left"],function(e,t){x.cssHooks[t]={get:function(e,n){return n?(n=vt(e,t),Ct.test(n)?x(e).position()[t]+"px":n):undefined}}})}),x.expr&&x.expr.filters&&(x.expr.filters.hidden=function(e){return 0>=e.offsetWidth&&0>=e.offsetHeight},x.expr.filters.visible=function(e){return!x.expr.filters.hidden(e)}),x.each({margin:"",padding:"",border:"Width"},function(e,t){x.cssHooks[e+t]={expand:function(n){var r=0,i={},s="string"==typeof n?n.split(" "):[n];for(;4>r;r++)i[e+jt[r]+t]=s[r]||s[r-2]||s[0];return i}},wt.test(e)||(x.cssHooks[e+t].set=Ot)});var Wt=/%20/g,$t=/\[\]$/,Bt=/\r?\n/g,It=/^(?:submit|button|image|reset|file)$/i,zt=/^(?:input|select|textarea|keygen)/i;x.fn.extend({serialize:function(){return x.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=x.prop(this,"elements");return e?x.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!x(this).is(":disabled")&&zt.test(this.nodeName)&&!It.test(e)&&(this.checked||!ot.test(e))}).map(function(e,t){var n=x(this).val();return null==n?null:x.isArray(n)?x.map(n,function(e){return{name:t.name,value:e.replace(Bt,"\r\n")}}):{name:t.name,value:n.replace(Bt,"\r\n")}}).get()}}),x.param=function(e,t){var n,r=[],i=function(e,t){t=x.isFunction(t)?t():null==t?"":t,r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(t===undefined&&(t=x.ajaxSettings&&x.ajaxSettings.traditional),x.isArray(e)||e.jquery&&!x.isPlainObject(e))x.each(e,function(){i(this.name,this.value)});else for(n in e)_t(n,e[n],t,i);return r.join("&").replace(Wt,"+")},x.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,t){x.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}),x.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)},bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}});var Xt,Ut,Yt=x.now(),Vt=/\?/,Gt=/#.*$/,Jt=/([?&])_=[^&]*/,Qt=/^(.*?):[ \t]*([^\r\n]*)$/gm,Kt=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Zt=/^(?:GET|HEAD)$/,en=/^\/\//,tn=/^([\w.+-]+:)(?:\/\/([^\/?#:]*)(?::(\d+)|)|)/,nn=x.fn.load,rn={},on={},sn="*/".concat("*");try{Ut=i.href}catch(an){Ut=o.createElement("a"),Ut.href="",Ut=Ut.href}Xt=tn.exec(Ut.toLowerCase())||[],x.fn.load=function(e,t,n){if("string"!=typeof e&&nn)return nn.apply(this,arguments);var r,i,s,o=this,u=e.indexOf(" ");return u>=0&&(r=e.slice(u),e=e.slice(0,u)),x.isFunction(t)?(n=t,t=undefined):t&&"object"==typeof t&&(i="POST"),o.length>0&&x.ajax({url:e,type:i,dataType:"html",data:t}).done(function(e){s=arguments,o.html(r?x("<div>").append(x.parseHTML(e)).find(r):e)}).complete(n&&function(e,t){o.each(n,s||[e.responseText,t,e])}),this},x.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){x.fn[t]=function(e){return this.on(t,e)}}),x.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ut,type:"GET",isLocal:Kt.test(Xt[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":sn,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":x.parseJSON,"text xml":x.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?cn(cn(e,x.ajaxSettings),t):cn(x.ajaxSettings,e)},ajaxPrefilter:un(rn),ajaxTransport:un(on),ajax:function(e,t){function T(e,t,s,u){var f,m,g,b,w,S=t;2!==y&&(y=2,o&&clearTimeout(o),n=undefined,i=u||"",E.readyState=e>0?4:0,f=e>=200&&300>e||304===e,s&&(b=pn(l,E,s)),b=fn(l,b,E,f),f?(l.ifModified&&(w=E.getResponseHeader("Last-Modified"),w&&(x.lastModified[r]=w),w=E.getResponseHeader("etag"),w&&(x.etag[r]=w)),204===e||"HEAD"===l.type?S="nocontent":304===e?S="notmodified":(S=b.state,m=b.data,g=b.error,f=!g)):(g=S,(e||!S)&&(S="error",0>e&&(e=0))),E.status=e,E.statusText=(t||S)+"",f?p.resolveWith(c,[m,S,E]):p.rejectWith(c,[E,S,g]),E.statusCode(v),v=undefined,a&&h.trigger(f?"ajaxSuccess":"ajaxError",[E,l,f?m:g]),d.fireWith(c,[E,S]),a&&(h.trigger("ajaxComplete",[E,l]),--x.active||x.event.trigger("ajaxStop")))}"object"==typeof e&&(t=e,e=undefined),t=t||{};var n,r,i,s,o,u,a,f,l=x.ajaxSetup({},t),c=l.context||l,h=l.context&&(c.nodeType||c.jquery)?x(c):x.event,p=x.Deferred(),d=x.Callbacks("once memory"),v=l.statusCode||{},m={},g={},y=0,b="canceled",E={readyState:0,getResponseHeader:function(e){var t;if(2===y){if(!s){s={};while(t=Qt.exec(i))s[t[1].toLowerCase()]=t[2]}t=s[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===y?i:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return y||(e=g[n]=g[n]||e,m[e]=t),this},overrideMimeType:function(e){return y||(l.mimeType=e),this},statusCode:function(e){var t;if(e)if(2>y)for(t in e)v[t]=[v[t],e[t]];else E.always(e[E.status]);return this},abort:function(e){var t=e||b;return n&&n.abort(t),T(0,t),this}};if(p.promise(E).complete=d.add,E.success=E.done,E.error=E.fail,l.url=((e||l.url||Ut)+"").replace(Gt,"").replace(en,Xt[1]+"//"),l.type=t.method||t.type||l.method||l.type,l.dataTypes=x.trim(l.dataType||"*").toLowerCase().match(w)||[""],null==l.crossDomain&&(u=tn.exec(l.url.toLowerCase()),l.crossDomain=!(!u||u[1]===Xt[1]&&u[2]===Xt[2]&&(u[3]||("http:"===u[1]?"80":"443"))===(Xt[3]||("http:"===Xt[1]?"80":"443")))),l.data&&l.processData&&"string"!=typeof l.data&&(l.data=x.param(l.data,l.traditional)),ln(rn,l,t,E),2===y)return E;a=l.global,a&&0===x.active++&&x.event.trigger("ajaxStart"),l.type=l.type.toUpperCase(),l.hasContent=!Zt.test(l.type),r=l.url,l.hasContent||(l.data&&(r=l.url+=(Vt.test(r)?"&":"?")+l.data,delete l.data),l.cache===!1&&(l.url=Jt.test(r)?r.replace(Jt,"$1_="+Yt++):r+(Vt.test(r)?"&":"?")+"_="+Yt++)),l.ifModified&&(x.lastModified[r]&&E.setRequestHeader("If-Modified-Since",x.lastModified[r]),x.etag[r]&&E.setRequestHeader("If-None-Match",x.etag[r])),(l.data&&l.hasContent&&l.contentType!==!1||t.contentType)&&E.setRequestHeader("Content-Type",l.contentType),E.setRequestHeader("Accept",l.dataTypes[0]&&l.accepts[l.dataTypes[0]]?l.accepts[l.dataTypes[0]]+("*"!==l.dataTypes[0]?", "+sn+"; q=0.01":""):l.accepts["*"]);for(f in l.headers)E.setRequestHeader(f,l.headers[f]);if(!l.beforeSend||l.beforeSend.call(c,E,l)!==!1&&2!==y){b="abort";for(f in{success:1,error:1,complete:1})E[f](l[f]);if(n=ln(on,l,t,E)){E.readyState=1,a&&h.trigger("ajaxSend",[E,l]),l.async&&l.timeout>0&&(o=setTimeout(function(){E.abort("timeout")},l.timeout));try{y=1,n.send(m,T)}catch(S){if(!(2>y))throw S;T(-1,S)}}else T(-1,"No Transport");return E}return E.abort()},getJSON:function(e,t,n){return x.get(e,t,n,"json")},getScript:function(e,t){return x.get(e,undefined,t,"script")}}),x.each(["get","post"],function(e,t){x[t]=function(e,n,r,i){return x.isFunction(n)&&(i=i||r,r=n,n=undefined),x.ajax({url:e,type:t,dataType:i,data:n,success:r})}}),x.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(e){return x.globalEval(e),e}}}),x.ajaxPrefilter("script",function(e){e.cache===undefined&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),x.ajaxTransport("script",function(e){if(e.crossDomain){var t,n;return{send:function(r,i){t=x("<script>").prop({async:!0,charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&i("error"===e.type?404:200,e.type)}),o.head.appendChild(t[0])},abort:function(){n&&n()}}}});var hn=[],dn=/(=)\?(?=&|$)|\?\?/;x.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=hn.pop()||x.expando+"_"+Yt++;return this[e]=!0,e}}),x.ajaxPrefilter("json jsonp",function(t,n,r){var i,s,o,u=t.jsonp!==!1&&(dn.test(t.url)?"url":"string"==typeof t.data&&!(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&dn.test(t.data)&&"data");return u||"jsonp"===t.dataTypes[0]?(i=t.jsonpCallback=x.isFunction(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,u?t[u]=t[u].replace(dn,"$1"+i):t.jsonp!==!1&&(t.url+=(Vt.test(t.url)?"&":"?")+t.jsonp+"="+i),t.converters["script json"]=function(){return o||x.error(i+" was not called"),o[0]},t.dataTypes[0]="json",s=e[i],e[i]=function(){o=arguments},r.always(function(){e[i]=s,t[i]&&(t.jsonpCallback=n.jsonpCallback,hn.push(i)),o&&x.isFunction(s)&&s(o[0]),o=s=undefined}),"script"):undefined}),x.ajaxSettings.xhr=function(){try{return new XMLHttpRequest}catch(e){}};var gn=x.ajaxSettings.xhr(),mn={0:200,1223:204},yn=0,vn={};e.ActiveXObject&&x(e).on("unload",function(){for(var e in vn)vn[e]();vn=undefined}),x.support.cors=!!gn&&"withCredentials"in gn,x.support.ajax=gn=!!gn,x.ajaxTransport(function(e){var t;return x.support.cors||gn&&!e.crossDomain?{send:function(n,r){var i,s,o=e.xhr();if(o.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(i in e.xhrFields)o[i]=e.xhrFields[i];e.mimeType&&o.overrideMimeType&&o.overrideMimeType(e.mimeType),e.crossDomain||n["X-Requested-With"]||(n["X-Requested-With"]="XMLHttpRequest");for(i in n)o.setRequestHeader(i,n[i]);t=function(e){return function(){t&&(delete vn[s],t=o.onload=o.onerror=null,"abort"===e?o.abort():"error"===e?r(o.status||404,o.statusText):r(mn[o.status]||o.status,o.statusText,"string"==typeof o.responseText?{text:o.responseText}:undefined,o.getAllResponseHeaders()))}},o.onload=t(),o.onerror=t("error"),t=vn[s=yn++]=t("abort"),o.send(e.hasContent&&e.data||null)},abort:function(){t&&t()}}:undefined});var xn,bn,wn=/^(?:toggle|show|hide)$/,Tn=RegExp("^(?:([+-])=|)("+b+")([a-z%]*)$","i"),Cn=/queueHooks$/,kn=[An],Nn={"*":[function(e,t){var n=this.createTween(e,t),r=n.cur(),i=Tn.exec(t),s=i&&i[3]||(x.cssNumber[e]?"":"px"),o=(x.cssNumber[e]||"px"!==s&&+r)&&Tn.exec(x.css(n.elem,e)),u=1,a=20;if(o&&o[3]!==s){s=s||o[3],i=i||[],o=+r||1;do u=u||".5",o/=u,x.style(n.elem,e,o+s);while(u!==(u=n.cur()/r)&&1!==u&&--a)}return i&&(o=n.start=+o||+r||0,n.unit=s,n.end=i[1]?o+(i[1]+1)*i[2]:+i[2]),n}]};x.Animation=x.extend(jn,{tweener:function(e,t){x.isFunction(e)?(t=e,e=["*"]):e=e.split(" ");var n,r=0,i=e.length;for(;i>r;r++)n=e[r],Nn[n]=Nn[n]||[],Nn[n].unshift(t)},prefilter:function(e,t){t?kn.unshift(e):kn.push(e)}}),x.Tween=Ln,Ln.prototype={constructor:Ln,init:function(e,t,n,r,i,s){this.elem=e,this.prop=n,this.easing=i||"swing",this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=s||(x.cssNumber[n]?"":"px")},cur:function(){var e=Ln.propHooks[this.prop];return e&&e.get?e.get(this):Ln.propHooks._default.get(this)},run:function(e){var t,n=Ln.propHooks[this.prop];return this.pos=t=this.options.duration?x.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):Ln.propHooks._default.set(this),this}},Ln.prototype.init.prototype=Ln.prototype,Ln.propHooks={_default:{get:function(e){var t;return null==e.elem[e.prop]||e.elem.style&&null!=e.elem.style[e.prop]?(t=x.css(e.elem,e.prop,""),t&&"auto"!==t?t:0):e.elem[e.prop]},set:function(e){x.fx.step[e.prop]?x.fx.step[e.prop](e):e.elem.style&&(null!=e.elem.style[x.cssProps[e.prop]]||x.cssHooks[e.prop])?x.style(e.elem,e.prop,e.now+e.unit):e.elem[e.prop]=e.now}}},Ln.propHooks.scrollTop=Ln.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},x.each(["toggle","show","hide"],function(e,t){var n=x.fn[t];x.fn[t]=function(e,r,i){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(qn(t,!0),e,r,i)}}),x.fn.extend({fadeTo:function(e,t,n,r){return this.filter(Lt).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=x.isEmptyObject(e),s=x.speed(t,n,r),o=function(){var t=jn(this,x.extend({},e),s);(i||q.get(this,"finish"))&&t.stop(!0)};return o.finish=o,i||s.queue===!1?this.each(o):this.queue(s.queue,o)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=undefined),t&&e!==!1&&this.queue(e||"fx",[]),this.each(function(){var t=!0,i=null!=e&&e+"queueHooks",s=x.timers,o=q.get(this);if(i)o[i]&&o[i].stop&&r(o[i]);else for(i in o)o[i]&&o[i].stop&&Cn.test(i)&&r(o[i]);for(i=s.length;i--;)s[i].elem!==this||null!=e&&s[i].queue!==e||(s[i].anim.stop(n),t=!1,s.splice(i,1));(t||!n)&&x.dequeue(this,e)})},finish:function(e){return e!==!1&&(e=e||"fx"),this.each(function(){var t,n=q.get(this),r=n[e+"queue"],i=n[e+"queueHooks"],s=x.timers,o=r?r.length:0;for(n.finish=!0,x.queue(this,e,[]),i&&i.stop&&i.stop.call(this,!0),t=s.length;t--;)s[t].elem===this&&s[t].queue===e&&(s[t].anim.stop(!0),s.splice(t,1));for(t=0;o>t;t++)r[t]&&r[t].finish&&r[t].finish.call(this);delete n.finish})}}),x.each({slideDown:qn("show"),slideUp:qn("hide"),slideToggle:qn("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){x.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}}),x.speed=function(e,t,n){var r=e&&"object"==typeof e?x.extend({},e):{complete:n||!n&&t||x.isFunction(e)&&e,duration:e,easing:n&&t||t&&!x.isFunction(t)&&t};return r.duration=x.fx.off?0:"number"==typeof r.duration?r.duration:r.duration in x.fx.speeds?x.fx.speeds[r.duration]:x.fx.speeds._default,(null==r.queue||r.queue===!0)&&(r.queue="fx"),r.old=r.complete,r.complete=function(){x.isFunction(r.old)&&r.old.call(this),r.queue&&x.dequeue(this,r.queue)},r},x.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2}},x.timers=[],x.fx=Ln.prototype.init,x.fx.tick=function(){var e,t=x.timers,n=0;for(xn=x.now();t.length>n;n++)e=t[n],e()||t[n]!==e||t.splice(n--,1);t.length||x.fx.stop(),xn=undefined},x.fx.timer=function(e){e()&&x.timers.push(e)&&x.fx.start()},x.fx.interval=13,x.fx.start=function(){bn||(bn=setInterval(x.fx.tick,x.fx.interval))},x.fx.stop=function(){clearInterval(bn),bn=null},x.fx.speeds={slow:600,fast:200,_default:400},x.fx.step={},x.expr&&x.expr.filters&&(x.expr.filters.animated=function(e){return x.grep(x.timers,function(t){return e===t.elem}).length}),x.fn.offset=function(e){if(arguments.length)return e===undefined?this:this.each(function(t){x.offset.setOffset(this,e,t)});var t,n,i=this[0],s={top:0,left:0},o=i&&i.ownerDocument;if(o)return t=o.documentElement,x.contains(t,i)?(typeof i.getBoundingClientRect!==r&&(s=i.getBoundingClientRect()),n=Hn(o),{top:s.top+n.pageYOffset-t.clientTop,left:s.left+n.pageXOffset-t.clientLeft}):s},x.offset={setOffset:function(e,t,n){var r,i,s,o,u,a,f,l=x.css(e,"position"),c=x(e),h={};"static"===l&&(e.style.position="relative"),u=c.offset(),s=x.css(e,"top"),a=x.css(e,"left"),f=("absolute"===l||"fixed"===l)&&(s+a).indexOf("auto")>-1,f?(r=c.position(),o=r.top,i=r.left):(o=parseFloat(s)||0,i=parseFloat(a)||0),x.isFunction(t)&&(t=t.call(e,n,u)),null!=t.top&&(h.top=t.top-u.top+o),null!=t.left&&(h.left=t.left-u.left+i),"using"in t?t.using.call(e,h):c.css(h)}},x.fn.extend({position:function(){if(this[0]){var e,t,n=this[0],r={top:0,left:0};return"fixed"===x.css(n,"position")?t=n.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),x.nodeName(e[0],"html")||(r=e.offset()),r.top+=x.css(e[0],"borderTopWidth",!0),r.left+=x.css(e[0],"borderLeftWidth",!0)),{top:t.top-r.top-x.css(n,"marginTop",!0),left:t.left-r.left-x.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){var e=this.offsetParent||s;while(e&&!x.nodeName(e,"html")&&"static"===x.css(e,"position"))e=e.offsetParent;return e||s})}}),x.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,n){var r="pageYOffset"===n;x.fn[t]=function(i){return x.access(this,function(t,i,s){var o=Hn(t);return s===undefined?o?o[n]:t[i]:(o?o.scrollTo(r?e.pageXOffset:s,r?s:e.pageYOffset):t[i]=s,undefined)},t,i,arguments.length,null)}}),x.each({Height:"height",Width:"width"},function(e,t){x.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,r){x.fn[r]=function(r,i){var s=arguments.length&&(n||"boolean"!=typeof r),o=n||(r===!0||i===!0?"margin":"border");return x.access(this,function(t,n,r){var i;return x.isWindow(t)?t.document.documentElement["client"+e]:9===t.nodeType?(i=t.documentElement,Math.max(t.body["scroll"+e],i["scroll"+e],t.body["offset"+e],i["offset"+e],i["client"+e])):r===undefined?x.css(t,n,o):x.style(t,n,r,o)},t,s?r:undefined,s,null)}})}),x.fn.size=function(){return this.length},x.fn.andSelf=x.fn.addBack,"object"==typeof module&&module&&"object"==typeof module.exports?module.exports=x:"function"==typeof define&&define.amd&&define("jquery",[],function(){return x}),"object"==typeof e&&"object"==typeof e.document&&(e.jQuery=e.$=x)})(window),function(e){function t(e,t){return function(n){return a(e.call(this,n),t)}}function n(e,t){return function(n){return this.lang().ordinal(e.call(this,n),t)}}function r(){}function i(e){w(e),o(this,e)}function s(e){var t=d(e),n=t.year||0,r=t.month||0,i=t.week||0,s=t.day||0,o=t.hour||0,u=t.minute||0,a=t.second||0,f=t.millisecond||0;this._input=e,this._milliseconds=+f+1e3*a+6e4*u+36e5*o,this._days=+s+7*i,this._months=+r+12*n,this._data={},this._bubble()}function o(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return t.hasOwnProperty("toString")&&(e.toString=t.toString),t.hasOwnProperty("valueOf")&&(e.valueOf=t.valueOf),e}function u(e){return 0>e?Math.ceil(e):Math.floor(e)}function a(e,t){for(var n=e+"";n.length<t;)n="0"+n;return n}function f(e,t,n,r){var i,s,o=t._milliseconds,u=t._days,a=t._months;o&&e._d.setTime(+e._d+o*n),(u||a)&&(i=e.minute(),s=e.hour()),u&&e.date(e.date()+u*n),a&&e.month(e.month()+a*n),o&&!r&&tt.updateOffset(e),(u||a)&&(e.minute(i),e.hour(s))}function l(e){return"[object Array]"===Object.prototype.toString.call(e)}function c(e){return"[object Date]"===Object.prototype.toString.call(e)}function h(e,t,n){var r,i=Math.min(e.length,t.length),s=Math.abs(e.length-t.length),o=0;for(r=0;i>r;r++)(n&&e[r]!==t[r]||!n&&m(e[r])!==m(t[r]))&&o++;return o+s}function p(e){if(e){var t=e.toLowerCase().replace(/(.)s$/,"$1");e=Ht[e]||Bt[t]||t}return e}function d(e){var t,n,r={};for(n in e)e.hasOwnProperty(n)&&(t=p(n),t&&(r[t]=e[n]));return r}function v(t){var n,r;if(0===t.indexOf("week"))n=7,r="day";else{if(0!==t.indexOf("month"))return;n=12,r="month"}tt[t]=function(i,s){var o,u,a=tt.fn._lang[t],f=[];if("number"==typeof i&&(s=i,i=e),u=function(e){var t=tt().utc().set(r,e);return a.call(tt.fn._lang,t,i||"")},null!=s)return u(s);for(o=0;n>o;o++)f.push(u(o));return f}}function m(e){var t=+e,n=0;return 0!==t&&isFinite(t)&&(n=t>=0?Math.floor(t):Math.ceil(t)),n}function g(e,t){return(new Date(Date.UTC(e,t+1,0))).getUTCDate()}function y(e){return b(e)?366:365}function b(e){return 0===e%4&&0!==e%100||0===e%400}function w(e){var t;e._a&&-2===e._pf.overflow&&(t=e._a[ot]<0||e._a[ot]>11?ot:e._a[ut]<1||e._a[ut]>g(e._a[st],e._a[ot])?ut:e._a[at]<0||e._a[at]>23?at:e._a[ft]<0||e._a[ft]>59?ft:e._a[lt]<0||e._a[lt]>59?lt:e._a[ct]<0||e._a[ct]>999?ct:-1,e._pf._overflowDayOfYear&&(st>t||t>ut)&&(t=ut),e._pf.overflow=t)}function E(e){e._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidMonth:null,invalidFormat:!1,userInvalidated:!1}}function S(e){return null==e._isValid&&(e._isValid=!isNaN(e._d.getTime())&&e._pf.overflow<0&&!e._pf.empty&&!e._pf.invalidMonth&&!e._pf.nullInput&&!e._pf.invalidFormat&&!e._pf.userInvalidated,e._strict&&(e._isValid=e._isValid&&0===e._pf.charsLeftOver&&0===e._pf.unusedTokens.length)),e._isValid}function x(e){return e?e.toLowerCase().replace("_","-"):e}function T(e,t){return t.abbr=e,ht[e]||(ht[e]=new r),ht[e].set(t),ht[e]}function N(e){delete ht[e]}function C(e){var t,n,r,i,s=0,o=function(e){if(!ht[e]&&pt)try{require("./lang/"+e)}catch(t){}return ht[e]};if(!e)return tt.fn._lang;if(!l(e)){if(n=o(e))return n;e=[e]}for(;s<e.length;){for(i=x(e[s]).split("-"),t=i.length,r=x(e[s+1]),r=r?r.split("-"):null;t>0;){if(n=o(i.slice(0,t).join("-")))return n;if(r&&r.length>=t&&h(i,r,!0)>=t-1)break;t--}s++}return tt.fn._lang}function k(e){return e.match(/\[[\s\S]/)?e.replace(/^\[|\]$/g,""):e.replace(/\\/g,"")}function L(e){var t,n,r=e.match(gt);for(t=0,n=r.length;n>t;t++)r[t]=qt[r[t]]?qt[r[t]]:k(r[t]);return function(i){var s="";for(t=0;n>t;t++)s+=r[t]instanceof Function?r[t].call(i,e):r[t];return s}}function A(e,t){return e.isValid()?(t=O(t,e.lang()),jt[t]||(jt[t]=L(t)),jt[t](e)):e.lang().invalidDate()}function O(e,t){function n(e){return t.longDateFormat(e)||e}var r=5;for(yt.lastIndex=0;r>=0&&yt.test(e);)e=e.replace(yt,n),yt.lastIndex=0,r-=1;return e}function M(e,t){var n;switch(e){case"DDDD":return Et;case"YYYY":case"GGGG":case"gggg":return St;case"YYYYY":case"GGGGG":case"ggggg":return xt;case"S":case"SS":case"SSS":case"DDD":return wt;case"MMM":case"MMMM":case"dd":case"ddd":case"dddd":return Tt;case"a":case"A":return C(t._l)._meridiemParse;case"X":return kt;case"Z":case"ZZ":return Nt;case"T":return Ct;case"MM":case"DD":case"YY":case"GG":case"gg":case"HH":case"hh":case"mm":case"ss":case"M":case"D":case"d":case"H":case"h":case"m":case"s":case"w":case"ww":case"W":case"WW":case"e":case"E":return bt;default:return n=new RegExp(I(F(e.replace("\\","")),"i"))}}function _(e){var t=(Nt.exec(e)||[])[0],n=(t+"").match(_t)||["-",0,0],r=+(60*n[1])+m(n[2]);return"+"===n[0]?-r:r}function D(e,t,n){var r,i=n._a;switch(e){case"M":case"MM":null!=t&&(i[ot]=m(t)-1);break;case"MMM":case"MMMM":r=C(n._l).monthsParse(t),null!=r?i[ot]=r:n._pf.invalidMonth=t;break;case"D":case"DD":null!=t&&(i[ut]=m(t));break;case"DDD":case"DDDD":null!=t&&(n._dayOfYear=m(t));break;case"YY":i[st]=m(t)+(m(t)>68?1900:2e3);break;case"YYYY":case"YYYYY":i[st]=m(t);break;case"a":case"A":n._isPm=C(n._l).isPM(t);break;case"H":case"HH":case"h":case"hh":i[at]=m(t);break;case"m":case"mm":i[ft]=m(t);break;case"s":case"ss":i[lt]=m(t);break;case"S":case"SS":case"SSS":i[ct]=m(1e3*("0."+t));break;case"X":n._d=new Date(1e3*parseFloat(t));break;case"Z":case"ZZ":n._useUTC=!0,n._tzm=_(t);break;case"w":case"ww":case"W":case"WW":case"d":case"dd":case"ddd":case"dddd":case"e":case"E":e=e.substr(0,1);case"gg":case"gggg":case"GG":case"GGGG":case"GGGGG":e=e.substr(0,2),t&&(n._w=n._w||{},n._w[e]=t)}}function P(e){var t,n,r,i,s,o,u,a,f,l,c=[];if(!e._d){for(r=B(e),e._w&&null==e._a[ut]&&null==e._a[ot]&&(s=function(t){return t?t.length<3?parseInt(t,10)>68?"19"+t:"20"+t:t:null==e._a[st]?tt().weekYear():e._a[st]},o=e._w,null!=o.GG||null!=o.W||null!=o.E?u=K(s(o.GG),o.W||1,o.E,4,1):(a=C(e._l),f=null!=o.d?X(o.d,a):null!=o.e?parseInt(o.e,10)+a._week.dow:0,l=parseInt(o.w,10)||1,null!=o.d&&f<a._week.dow&&l++,u=K(s(o.gg),l,f,a._week.doy,a._week.dow)),e._a[st]=u.year,e._dayOfYear=u.dayOfYear),e._dayOfYear&&(i=null==e._a[st]?r[st]:e._a[st],e._dayOfYear>y(i)&&(e._pf._overflowDayOfYear=!0),n=W(i,0,e._dayOfYear),e._a[ot]=n.getUTCMonth(),e._a[ut]=n.getUTCDate()),t=0;3>t&&null==e._a[t];++t)e._a[t]=c[t]=r[t];for(;7>t;t++)e._a[t]=c[t]=null==e._a[t]?2===t?1:0:e._a[t];c[at]+=m((e._tzm||0)/60),c[ft]+=m((e._tzm||0)%60),e._d=(e._useUTC?W:z).apply(null,c)}}function H(e){var t;e._d||(t=d(e._i),e._a=[t.year,t.month,t.day,t.hour,t.minute,t.second,t.millisecond],P(e))}function B(e){var t=new Date;return e._useUTC?[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()]:[t.getFullYear(),t.getMonth(),t.getDate()]}function j(e){e._a=[],e._pf.empty=!0;var t,n,r,i,s,o=C(e._l),u=""+e._i,a=u.length,f=0;for(r=O(e._f,o).match(gt)||[],t=0;t<r.length;t++)i=r[t],n=(M(i,e).exec(u)||[])[0],n&&(s=u.substr(0,u.indexOf(n)),s.length>0&&e._pf.unusedInput.push(s),u=u.slice(u.indexOf(n)+n.length),f+=n.length),qt[i]?(n?e._pf.empty=!1:e._pf.unusedTokens.push(i),D(i,n,e)):e._strict&&!n&&e._pf.unusedTokens.push(i);e._pf.charsLeftOver=a-f,u.length>0&&e._pf.unusedInput.push(u),e._isPm&&e._a[at]<12&&(e._a[at]+=12),e._isPm===!1&&12===e._a[at]&&(e._a[at]=0),P(e),w(e)}function F(e){return e.replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(e,t,n,r,i){return t||n||r||i})}function I(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function q(e){var t,n,r,i,s;if(0===e._f.length)return e._pf.invalidFormat=!0,e._d=new Date(0/0),void 0;for(i=0;i<e._f.length;i++)s=0,t=o({},e),E(t),t._f=e._f[i],j(t),S(t)&&(s+=t._pf.charsLeftOver,s+=10*t._pf.unusedTokens.length,t._pf.score=s,(null==r||r>s)&&(r=s,n=t));o(e,n||t)}function R(e){var t,n=e._i,r=Lt.exec(n);if(r){for(t=4;t>0;t--)if(r[t]){e._f=Ot[t-1]+(r[6]||" ");break}for(t=0;4>t;t++)if(Mt[t][1].exec(n)){e._f+=Mt[t][0];break}Nt.exec(n)&&(e._f+=" Z"),j(e)}else e._d=new Date(n)}function U(t){var n=t._i,r=dt.exec(n);n===e?t._d=new Date:r?t._d=new Date(+r[1]):"string"==typeof n?R(t):l(n)?(t._a=n.slice(0),P(t)):c(n)?t._d=new Date(+n):"object"==typeof n?H(t):t._d=new Date(n)}function z(e,t,n,r,i,s,o){var u=new Date(e,t,n,r,i,s,o);return 1970>e&&u.setFullYear(e),u}function W(e){var t=new Date(Date.UTC.apply(null,arguments));return 1970>e&&t.setUTCFullYear(e),t}function X(e,t){if("string"==typeof e)if(isNaN(e)){if(e=t.weekdaysParse(e),"number"!=typeof e)return null}else e=parseInt(e,10);return e}function V(e,t,n,r,i){return i.relativeTime(t||1,!!n,e,r)}function $(e,t,n){var r=it(Math.abs(e)/1e3),i=it(r/60),s=it(i/60),o=it(s/24),u=it(o/365),a=45>r&&["s",r]||1===i&&["m"]||45>i&&["mm",i]||1===s&&["h"]||22>s&&["hh",s]||1===o&&["d"]||25>=o&&["dd",o]||45>=o&&["M"]||345>o&&["MM",it(o/30)]||1===u&&["y"]||["yy",u];return a[2]=t,a[3]=e>0,a[4]=n,V.apply({},a)}function J(e,t,n){var r,i=n-t,s=n-e.day();return s>i&&(s-=7),i-7>s&&(s+=7),r=tt(e).add("d",s),{week:Math.ceil(r.dayOfYear()/7),year:r.year()}}function K(e,t,n,r,i){var s,o,u=(new Date(Date.UTC(e,0))).getUTCDay();return n=null!=n?n:i,s=i-u+(u>r?7:0),o=7*(t-1)+(n-i)+s+1,{year:o>0?e:e-1,dayOfYear:o>0?o:y(e-1)+o}}function Q(e){var t=e._i,n=e._f;return"undefined"==typeof e._pf&&E(e),null===t?tt.invalid({nullInput:!0}):("string"==typeof t&&(e._i=t=C().preparse(t)),tt.isMoment(t)?(e=o({},t),e._d=new Date(+t._d)):n?l(n)?q(e):j(e):U(e),new i(e))}function G(e,t){tt.fn[e]=tt.fn[e+"s"]=function(e){var n=this._isUTC?"UTC":"";return null!=e?(this._d["set"+n+t](e),tt.updateOffset(this),this):this._d["get"+n+t]()}}function Y(e){tt.duration.fn[e]=function(){return this._data[e]}}function Z(e,t){tt.duration.fn["as"+e]=function(){return+this/t}}function et(){"undefined"==typeof ender&&(this.moment=tt)}for(var tt,nt,rt="2.3.1",it=Math.round,st=0,ot=1,ut=2,at=3,ft=4,lt=5,ct=6,ht={},pt="undefined"!=typeof module&&module.exports,dt=/^\/?Date\((\-?\d+)/i,vt=/(\-)?(?:(\d*)\.)?(\d+)\:(\d+)(?:\:(\d+)\.?(\d{3})?)?/,mt=/^(-)?P(?:(?:([0-9,.]*)Y)?(?:([0-9,.]*)M)?(?:([0-9,.]*)D)?(?:T(?:([0-9,.]*)H)?(?:([0-9,.]*)M)?(?:([0-9,.]*)S)?)?|([0-9,.]*)W)$/,gt=/(\[[^\[]*\])|(\\)?(Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|mm?|ss?|SS?S?|X|zz?|ZZ?|.)/g,yt=/(\[[^\[]*\])|(\\)?(LT|LL?L?L?|l{1,4})/g,bt=/\d\d?/,wt=/\d{1,3}/,Et=/\d{3}/,St=/\d{1,4}/,xt=/[+\-]?\d{1,6}/,Tt=/[0-9]*['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+|[\u0600-\u06FF\/]+(\s*?[\u0600-\u06FF]+){1,2}/i,Nt=/Z|[\+\-]\d\d:?\d\d/i,Ct=/T/i,kt=/[\+\-]?\d+(\.\d{1,3})?/,Lt=/^\s*\d{4}-(?:(\d\d-\d\d)|(W\d\d$)|(W\d\d-\d)|(\d\d\d))((T| )(\d\d(:\d\d(:\d\d(\.\d\d?\d?)?)?)?)?([\+\-]\d\d:?\d\d)?)?$/,At="YYYY-MM-DDTHH:mm:ssZ",Ot=["YYYY-MM-DD","GGGG-[W]WW","GGGG-[W]WW-E","YYYY-DDD"],Mt=[["HH:mm:ss.S",/(T| )\d\d:\d\d:\d\d\.\d{1,3}/],["HH:mm:ss",/(T| )\d\d:\d\d:\d\d/],["HH:mm",/(T| )\d\d:\d\d/],["HH",/(T| )\d\d/]],_t=/([\+\-]|\d\d)/gi,Dt="Date|Hours|Minutes|Seconds|Milliseconds".split("|"),Pt={Milliseconds:1,Seconds:1e3,Minutes:6e4,Hours:36e5,Days:864e5,Months:2592e6,Years:31536e6},Ht={ms:"millisecond",s:"second",m:"minute",h:"hour",d:"day",D:"date",w:"week",W:"isoWeek",M:"month",y:"year",DDD:"dayOfYear",e:"weekday",E:"isoWeekday",gg:"weekYear",GG:"isoWeekYear"},Bt={dayofyear:"dayOfYear",isoweekday:"isoWeekday",isoweek:"isoWeek",weekyear:"weekYear",isoweekyear:"isoWeekYear"},jt={},Ft="DDD w W M D d".split(" "),It="M D H h m s w W".split(" "),qt={M:function(){return this.month()+1},MMM:function(e){return this.lang().monthsShort(this,e)},MMMM:function(e){return this.lang().months(this,e)},D:function(){return this.date()},DDD:function(){return this.dayOfYear()},d:function(){return this.day()},dd:function(e){return this.lang().weekdaysMin(this,e)},ddd:function(e){return this.lang().weekdaysShort(this,e)},dddd:function(e){return this.lang().weekdays(this,e)},w:function(){return this.week()},W:function(){return this.isoWeek()},YY:function(){return a(this.year()%100,2)},YYYY:function(){return a(this.year(),4)},YYYYY:function(){return a(this.year(),5)},gg:function(){return a(this.weekYear()%100,2)},gggg:function(){return this.weekYear()},ggggg:function(){return a(this.weekYear(),5)},GG:function(){return a(this.isoWeekYear()%100,2)},GGGG:function(){return this.isoWeekYear()},GGGGG:function(){return a(this.isoWeekYear(),5)},e:function(){return this.weekday()},E:function(){return this.isoWeekday()},a:function(){return this.lang().meridiem(this.hours(),this.minutes(),!0)},A:function(){return this.lang().meridiem(this.hours(),this.minutes(),!1)},H:function(){return this.hours()},h:function(){return this.hours()%12||12},m:function(){return this.minutes()},s:function(){return this.seconds()},S:function(){return m(this.milliseconds()/100)},SS:function(){return a(m(this.milliseconds()/10),2)},SSS:function(){return a(this.milliseconds(),3)},Z:function(){var e=-this.zone(),t="+";return 0>e&&(e=-e,t="-"),t+a(m(e/60),2)+":"+a(m(e)%60,2)},ZZ:function(){var e=-this.zone(),t="+";return 0>e&&(e=-e,t="-"),t+a(m(10*e/6),4)},z:function(){return this.zoneAbbr()},zz:function(){return this.zoneName()},X:function(){return this.unix()}},Rt=["months","monthsShort","weekdays","weekdaysShort","weekdaysMin"];Ft.length;)nt=Ft.pop(),qt[nt+"o"]=n(qt[nt],nt);for(;It.length;)nt=It.pop(),qt[nt+nt]=t(qt[nt],2);for(qt.DDDD=t(qt.DDD,3),o(r.prototype,{set:function(e){var t,n;for(n in e)t=e[n],"function"==typeof t?this[n]=t:this["_"+n]=t},_months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),months:function(e){return this._months[e.month()]},_monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),monthsShort:function(e){return this._monthsShort[e.month()]},monthsParse:function(e){var t,n,r;for(this._monthsParse||(this._monthsParse=[]),t=0;12>t;t++)if(this._monthsParse[t]||(n=tt.utc([2e3,t]),r="^"+this.months(n,"")+"|^"+this.monthsShort(n,""),this._monthsParse[t]=new RegExp(r.replace(".",""),"i")),this._monthsParse[t].test(e))return t},_weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdays:function(e){return this._weekdays[e.day()]},_weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysShort:function(e){return this._weekdaysShort[e.day()]},_weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),weekdaysMin:function(e){return this._weekdaysMin[e.day()]},weekdaysParse:function(e){var t,n,r;for(this._weekdaysParse||(this._weekdaysParse=[]),t=0;7>t;t++)if(this._weekdaysParse[t]||(n=tt([2e3,1]).day(t),r="^"+this.weekdays(n,"")+"|^"+this.weekdaysShort(n,"")+"|^"+this.weekdaysMin(n,""),this._weekdaysParse[t]=new RegExp(r.replace(".",""),"i")),this._weekdaysParse[t].test(e))return t},_longDateFormat:{LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D YYYY",LLL:"MMMM D YYYY LT",LLLL:"dddd, MMMM D YYYY LT"},longDateFormat:function(e){var t=this._longDateFormat[e];return!t&&this._longDateFormat[e.toUpperCase()]&&(t=this._longDateFormat[e.toUpperCase()].replace(/MMMM|MM|DD|dddd/g,function(e){return e.slice(1)}),this._longDateFormat[e]=t),t},isPM:function(e){return"p"===(e+"").toLowerCase().charAt(0)},_meridiemParse:/[ap]\.?m?\.?/i,meridiem:function(e,t,n){return e>11?n?"pm":"PM":n?"am":"AM"},_calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},calendar:function(e,t){var n=this._calendar[e];return"function"==typeof n?n.apply(t):n},_relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},relativeTime:function(e,t,n,r){var i=this._relativeTime[n];return"function"==typeof i?i(e,t,n,r):i.replace(/%d/i,e)},pastFuture:function(e,t){var n=this._relativeTime[e>0?"future":"past"];return"function"==typeof n?n(t):n.replace(/%s/i,t)},ordinal:function(e){return this._ordinal.replace("%d",e)},_ordinal:"%d",preparse:function(e){return e},postformat:function(e){return e},week:function(e){return J(e,this._week.dow,this._week.doy).week},_week:{dow:0,doy:6},_invalidDate:"Invalid date",invalidDate:function(){return this._invalidDate}}),tt=function(t,n,r,i){return"boolean"==typeof r&&(i=r,r=e),Q({_i:t,_f:n,_l:r,_strict:i,_isUTC:!1})},tt.utc=function(t,n,r,i){var s;return"boolean"==typeof r&&(i=r,r=e),s=Q({_useUTC:!0,_isUTC:!0,_l:r,_i:t,_f:n,_strict:i}).utc()},tt.unix=function(e){return tt(1e3*e)},tt.duration=function(e,t){var n,r,i,o=tt.isDuration(e),u="number"==typeof e,a=o?e._input:u?{}:e,f=null;return u?t?a[t]=e:a.milliseconds=e:(f=vt.exec(e))?(n="-"===f[1]?-1:1,a={y:0,d:m(f[ut])*n,h:m(f[at])*n,m:m(f[ft])*n,s:m(f[lt])*n,ms:m(f[ct])*n}):(f=mt.exec(e))&&(n="-"===f[1]?-1:1,i=function(e){var t=e&&parseFloat(e.replace(",","."));return(isNaN(t)?0:t)*n},a={y:i(f[2]),M:i(f[3]),d:i(f[4]),h:i(f[5]),m:i(f[6]),s:i(f[7]),w:i(f[8])}),r=new s(a),o&&e.hasOwnProperty("_lang")&&(r._lang=e._lang),r},tt.version=rt,tt.defaultFormat=At,tt.updateOffset=function(){},tt.lang=function(e,t){var n;return e?(t?T(x(e),t):null===t?(N(e),e="en"):ht[e]||C(e),n=tt.duration.fn._lang=tt.fn._lang=C(e),n._abbr):tt.fn._lang._abbr},tt.langData=function(e){return e&&e._lang&&e._lang._abbr&&(e=e._lang._abbr),C(e)},tt.isMoment=function(e){return e instanceof i},tt.isDuration=function(e){return e instanceof s},nt=Rt.length-1;nt>=0;--nt)v(Rt[nt]);for(tt.normalizeUnits=function(e){return p(e)},tt.invalid=function(e){var t=tt.utc(0/0);return null!=e?o(t._pf,e):t._pf.userInvalidated=!0,t},tt.parseZone=function(e){return tt(e).parseZone()},o(tt.fn=i.prototype,{clone:function(){return tt(this)},valueOf:function(){return+this._d+6e4*(this._offset||0)},unix:function(){return Math.floor(+this/1e3)},toString:function(){return this.clone().lang("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},toDate:function(){return this._offset?new Date(+this):this._d},toISOString:function(){return A(tt(this).utc(),"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]")},toArray:function(){var e=this;return[e.year(),e.month(),e.date(),e.hours(),e.minutes(),e.seconds(),e.milliseconds()]},isValid:function(){return S(this)},isDSTShifted:function(){return this._a?this.isValid()&&h(this._a,(this._isUTC?tt.utc(this._a):tt(this._a)).toArray())>0:!1},parsingFlags:function(){return o({},this._pf)},invalidAt:function(){return this._pf.overflow},utc:function(){return this.zone(0)},local:function(){return this.zone(0),this._isUTC=!1,this},format:function(e){var t=A(this,e||tt.defaultFormat);return this.lang().postformat(t)},add:function(e,t){var n;return n="string"==typeof e?tt.duration(+t,e):tt.duration(e,t),f(this,n,1),this},subtract:function(e,t){var n;return n="string"==typeof e?tt.duration(+t,e):tt.duration(e,t),f(this,n,-1),this},diff:function(e,t,n){var r,i,s=this._isUTC?tt(e).zone(this._offset||0):tt(e).local(),o=6e4*(this.zone()-s.zone());return t=p(t),"year"===t||"month"===t?(r=432e5*(this.daysInMonth()+s.daysInMonth()),i=12*(this.year()-s.year())+(this.month()-s.month()),i+=(this-tt(this).startOf("month")-(s-tt(s).startOf("month")))/r,i-=6e4*(this.zone()-tt(this).startOf("month").zone()-(s.zone()-tt(s).startOf("month").zone()))/r,"year"===t&&(i/=12)):(r=this-s,i="second"===t?r/1e3:"minute"===t?r/6e4:"hour"===t?r/36e5:"day"===t?(r-o)/864e5:"week"===t?(r-o)/6048e5:r),n?i:u(i)},from:function(e,t){return tt.duration(this.diff(e)).lang(this.lang()._abbr).humanize(!t)},fromNow:function(e){return this.from(tt(),e)},calendar:function(){var e=this.diff(tt().zone(this.zone()).startOf("day"),"days",!0),t=-6>e?"sameElse":-1>e?"lastWeek":0>e?"lastDay":1>e?"sameDay":2>e?"nextDay":7>e?"nextWeek":"sameElse";return this.format(this.lang().calendar(t,this))},isLeapYear:function(){return b(this.year())},isDST:function(){return this.zone()<this.clone().month(0).zone()||this.zone()<this.clone().month(5).zone()},day:function(e){var t=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=e?(e=X(e,this.lang()),this.add({d:e-t})):t},month:function(e){var t,n=this._isUTC?"UTC":"";return null!=e?"string"==typeof e&&(e=this.lang().monthsParse(e),"number"!=typeof e)?this:(t=this.date(),this.date(1),this._d["set"+n+"Month"](e),this.date(Math.min(t,this.daysInMonth())),tt.updateOffset(this),this):this._d["get"+n+"Month"]()},startOf:function(e){switch(e=p(e)){case"year":this.month(0);case"month":this.date(1);case"week":case"isoWeek":case"day":this.hours(0);case"hour":this.minutes(0);case"minute":this.seconds(0);case"second":this.milliseconds(0)}return"week"===e?this.weekday(0):"isoWeek"===e&&this.isoWeekday(1),this},endOf:function(e){return e=p(e),this.startOf(e).add("isoWeek"===e?"week":e,1).subtract("ms",1)},isAfter:function(e,t){return t="undefined"!=typeof t?t:"millisecond",+this.clone().startOf(t)>+tt(e).startOf(t)},isBefore:function(e,t){return t="undefined"!=typeof t?t:"millisecond",+this.clone().startOf(t)<+tt(e).startOf(t)},isSame:function(e,t){return t="undefined"!=typeof t?t:"millisecond",+this.clone().startOf(t)===+tt(e).startOf(t)},min:function(e){return e=tt.apply(null,arguments),this>e?this:e},max:function(e){return e=tt.apply(null,arguments),e>this?this:e},zone:function(e){var t=this._offset||0;return null==e?this._isUTC?t:this._d.getTimezoneOffset():("string"==typeof e&&(e=_(e)),Math.abs(e)<16&&(e=60*e),this._offset=e,this._isUTC=!0,t!==e&&f(this,tt.duration(t-e,"m"),1,!0),this)},zoneAbbr:function(){return this._isUTC?"UTC":""},zoneName:function(){return this._isUTC?"Coordinated Universal Time":""},parseZone:function(){return"string"==typeof this._i&&this.zone(this._i),this},hasAlignedHourOffset:function(e){return e=e?tt(e).zone():0,0===(this.zone()-e)%60},daysInMonth:function(){return g(this.year(),this.month())},dayOfYear:function(e){var t=it((tt(this).startOf("day")-tt(this).startOf("year"))/864e5)+1;return null==e?t:this.add("d",e-t)},weekYear:function(e){var t=J(this,this.lang()._week.dow,this.lang()._week.doy).year;return null==e?t:this.add("y",e-t)},isoWeekYear:function(e){var t=J(this,1,4).year;return null==e?t:this.add("y",e-t)},week:function(e){var t=this.lang().week(this);return null==e?t:this.add("d",7*(e-t))},isoWeek:function(e){var t=J(this,1,4).week;return null==e?t:this.add("d",7*(e-t))},weekday:function(e){var t=(this.day()+7-this.lang()._week.dow)%7;return null==e?t:this.add("d",e-t)},isoWeekday:function(e){return null==e?this.day()||7:this.day(this.day()%7?e:e-7)},get:function(e){return e=p(e),this[e]()},set:function(e,t){return e=p(e),"function"==typeof this[e]&&this[e](t),this},lang:function(t){return t===e?this._lang:(this._lang=C(t),this)}}),nt=0;nt<Dt.length;nt++)G(Dt[nt].toLowerCase().replace(/s$/,""),Dt[nt]);G("year","FullYear"),tt.fn.days=tt.fn.day,tt.fn.months=tt.fn.month,tt.fn.weeks=tt.fn.week,tt.fn.isoWeeks=tt.fn.isoWeek,tt.fn.toJSON=tt.fn.toISOString,o(tt.duration.fn=s.prototype,{_bubble:function(){var e,t,n,r,i=this._milliseconds,s=this._days,o=this._months,a=this._data;a.milliseconds=i%1e3,e=u(i/1e3),a.seconds=e%60,t=u(e/60),a.minutes=t%60,n=u(t/60),a.hours=n%24,s+=u(n/24),a.days=s%30,o+=u(s/30),a.months=o%12,r=u(o/12),a.years=r},weeks:function(){return u(this.days()/7)},valueOf:function(){return this._milliseconds+864e5*this._days+2592e6*(this._months%12)+31536e6*m(this._months/12)},humanize:function(e){var t=+this,n=$(t,!e,this.lang());return e&&(n=this.lang().pastFuture(t,n)),this.lang().postformat(n)},add:function(e,t){var n=tt.duration(e,t);return this._milliseconds+=n._milliseconds,this._days+=n._days,this._months+=n._months,this._bubble(),this},subtract:function(e,t){var n=tt.duration(e,t);return this._milliseconds-=n._milliseconds,this._days-=n._days,this._months-=n._months,this._bubble(),this},get:function(e){return e=p(e),this[e.toLowerCase()+"s"]()},as:function(e){return e=p(e),this["as"+e.charAt(0).toUpperCase()+e.slice(1)+"s"]()},lang:tt.fn.lang,toIsoString:function(){var e=Math.abs(this.years()),t=Math.abs(this.months()),n=Math.abs(this.days()),r=Math.abs(this.hours()),i=Math.abs(this.minutes()),s=Math.abs(this.seconds()+this.milliseconds()/1e3);return this.asSeconds()?(this.asSeconds()<0?"-":"")+"P"+(e?e+"Y":"")+(t?t+"M":"")+(n?n+"D":"")+(r||i||s?"T":"")+(r?r+"H":"")+(i?i+"M":"")+(s?s+"S":""):"P0D"}});for(nt in Pt)Pt.hasOwnProperty(nt)&&(Z(nt,Pt[nt]),Y(nt.toLowerCase()));Z("Weeks",6048e5),tt.duration.fn.asMonths=function(){return(+this-31536e6*this.years())/2592e6+12*this.years()},tt.lang("en",{ordinal:function(e){var t=e%10,n=1===m(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th";return e+n}}),pt?(module.exports=tt,et()):"function"==typeof define&&define.amd?define("moment",["require","exports","module"],function(e,t,n){return n.config().noGlobal!==!0&&et(),tt}):et()}.call(this),!function(e,t){"object"==typeof exports&&exports?module.exports=t:"function"==typeof define&&define.amd?define("mustache",t):e.Mustache=t}(this,function(){function e(e,t){return RegExp.prototype.test.call(e,t)}function t(t){return!e(d,t)}function n(e){return e.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function r(e){return String(e).replace(/[&<>"'\/]/g,function(e){return b[e]})}function i(e){this.string=e,this.tail=e,this.pos=0}function s(e,t){this.view=e,this.parent=t,this.clearCache()}function o(){this.clearCache()}function u(e){function t(e,t,r){if(!n[e]){var i=u(t);n[e]=function(e,t){return i(e,t,r)}}return n[e]}var n={};return function(n,r,i){for(var s,o,u="",a=0,f=e.length;f>a;++a)switch(s=e[a],s[0]){case"#":o=i.slice(s[3],s[5]),u+=n._section(s[1],r,o,t(a,s[4],i));break;case"^":u+=n._inverted(s[1],r,t(a,s[4],i));break;case">":u+=n._partial(s[1],r);break;case"&":u+=n._name(s[1],r);break;case"name":u+=n._escaped(s[1],r);break;case"text":u+=s[1]}return u}}function a(e){for(var t,n=[],r=n,i=[],s=0,o=e.length;o>s;++s)switch(t=e[s],t[0]){case"#":case"^":i.push(t),r.push(t),r=t[4]=[];break;case"/":var u=i.pop();u[5]=t[2],r=i.length>0?i[i.length-1][4]:n;break;default:r.push(t)}return n}function f(e){for(var t,n,r=[],i=0,s=e.length;s>i;++i)t=e[i],"text"===t[0]&&n&&"text"===n[0]?(n[1]+=t[1],n[3]=t[3]):(n=t,r.push(t));return r}function l(e){return[new RegExp(n(e[0])+"\\s*"),new RegExp("\\s*"+n(e[1]))]}var c={};c.name="mustache.js",c.version="0.7.2",c.tags=["{{","}}"],c.Scanner=i,c.Context=s,c.Writer=o;var h=/\s*/,p=/\s+/,d=/\S/,v=/\s*=/,m=/\s*\}/,g=/#|\^|\/|>|\{|&|=|!/,y=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},b={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};c.escape=r,i.prototype.eos=function(){return""===this.tail},i.prototype.scan=function(e){var t=this.tail.match(e);return t&&0===t.index?(this.tail=this.tail.substring(t[0].length),this.pos+=t[0].length,t[0]):""},i.prototype.scanUntil=function(e){var t,n=this.tail.search(e);switch(n){case-1:t=this.tail,this.pos+=this.tail.length,this.tail="";break;case 0:t="";break;default:t=this.tail.substring(0,n),this.tail=this.tail.substring(n),this.pos+=n}return t},s.make=function(e){return e instanceof s?e:new s(e)},s.prototype.clearCache=function(){this._cache={}},s.prototype.push=function(e){return new s(e,this)},s.prototype.lookup=function(e){var t=this._cache[e];if(!t){if("."===e)t=this.view;else for(var n=this;n;){if(e.indexOf(".")>0){var r=e.split("."),i=0;for(t=n.view;t&&i<r.length;)t=t[r[i++]]}else t=n.view[e];if(null!=t)break;n=n.parent}this._cache[e]=t}return"function"==typeof t&&(t=t.call(this.view)),t},o.prototype.clearCache=function(){this._cache={},this._partialCache={}},o.prototype.compile=function(e,t){var n=this._cache[e];if(!n){var r=c.parse(e,t);n=this._cache[e]=this.compileTokens(r,e)}return n},o.prototype.compilePartial=function(e,t,n){var r=this.compile(t,n);return this._partialCache[e]=r,r},o.prototype.compileTokens=function(e,t){var n=u(e),r=this;return function(e,i){if(i)if("function"==typeof i)r._loadPartial=i;else for(var o in i)r.compilePartial(o,i[o]);return n(r,s.make(e),t)}},o.prototype.render=function(e,t,n){return this.compile(e)(t,n)},o.prototype._section=function(e,t,n,r){var i=t.lookup(e);switch(typeof i){case"object":if(y(i)){for(var s="",o=0,u=i.length;u>o;++o)s+=r(this,t.push(i[o]));return s}return i?r(this,t.push(i)):"";case"function":var a=this,f=function(e){return a.render(e,t)},l=i.call(t.view,n,f);return null!=l?l:"";default:if(i)return r(this,t)}return""},o.prototype._inverted=function(e,t,n){var r=t.lookup(e);return!r||y(r)&&0===r.length?n(this,t):""},o.prototype._partial=function(e,t){e in this._partialCache||!this._loadPartial||this.compilePartial(e,this._loadPartial(e));var n=this._partialCache[e];return n?n(t):""},o.prototype._name=function(e,t){var n=t.lookup(e);return"function"==typeof n&&(n=n.call(t.view)),null==n?"":String(n)},o.prototype._escaped=function(e,t){return c.escape(this._name(e,t))},c.parse=function(e,r){function s(){if(T&&!N)for(;x.length;)S.splice(x.pop(),1);else x=[];T=!1,N=!1}if(e=e||"",r=r||c.tags,"string"==typeof r&&(r=r.split(p)),2!==r.length)throw new Error("Invalid tags: "+r.join(", "));for(var o,u,d,y,b=l(r),w=new i(e),E=[],S=[],x=[],T=!1,N=!1;!w.eos();){if(o=w.pos,d=w.scanUntil(b[0]))for(var C=0,L=d.length;L>C;++C)y=d.charAt(C),t(y)?x.push(S.length):N=!0,S.push(["text",y,o,o+1]),o+=1,"\n"===y&&s();if(o=w.pos,!w.scan(b[0]))break;if(T=!0,u=w.scan(g)||"name",w.scan(h),"="===u)d=w.scanUntil(v),w.scan(v),w.scanUntil(b[1]);else if("{"===u){var A=new RegExp("\\s*"+n("}"+r[1]));d=w.scanUntil(A),w.scan(m),w.scanUntil(b[1]),u="&"}else d=w.scanUntil(b[1]);if(!w.scan(b[1]))throw new Error("Unclosed tag at "+w.pos);if("/"===u){if(0===E.length)throw new Error('Unopened section "'+d+'" at '+o);var O=E.pop();if(O[1]!==d)throw new Error('Unclosed section "'+O[1]+'" at '+o)}var M=[u,d,o,w.pos];if(S.push(M),"#"===u||"^"===u)E.push(M);else if("name"===u||"{"===u||"&"===u)N=!0;else if("="===u){if(r=d.split(p),2!==r.length)throw new Error("Invalid tags at "+o+": "+r.join(", "));b=l(r)}}var O=E.pop();if(O)throw new Error('Unclosed section "'+O[1]+'" at '+w.pos);return a(f(S))};var w=new o;return c.clearCache=function(){return w.clearCache()},c.compile=function(e,t){return w.compile(e,t)},c.compilePartial=function(e,t,n){return w.compilePartial(e,t,n)},c.compileTokens=function(e,t){return w.compileTokens(e,t)},c.render=function(e,t,n){return w.render(e,t,n)},c.to_html=function(e,t,n,r){var i=c.render(e,t,n);return"function"!=typeof r?i:(r(i),void 0)},c}()),define("hash",[],function(){return Hash=function(e){this.separate=",",this.data={},this._hashChange()},Hash.prototype={get:function(e){var t=this;return e===undefined?this.data:this.data[e]},add:function(e){var t=this;for(var n in e)t.data[n]=e[n];t._change()},remove:function(e){var t=this;delete t.data[e],t._change()},clear:function(){var e=this;e.data={},location.hash=""},_change:function(){var e=this,t=e.data,n=[];for(var r in t)n.push(r+"="+t[r]);location.hash=n.join(e.separate)},_hashChange:function(){var e=this;window.addEventListener("hashchange",function(){e._hashData()},!1)},_hashData:function(){var e=this,t=location.hash.substring(1).split(e.separate);e.data={};if(t!="")for(var n=0;n<t.length;n++){var r=t[n].split("="),i=r[0],s=r[1];e.data[i]=s}}},new Hash}),define("calendar",["jquery","moment","mustache","hash"],function(e,t,n,r){function i(t){this.defaults={url_room:"calendar/loadByRoom.htm",url_instructor:"calendar/loadOnDefault.htm",url_week:"calendar/loadByWeek.htm",url_month:"calendar/loadByMonth.htm",url_customer_day:"calendar/loadAppointmentByCustomer.htm",url_customer_week:"calendar/loadAppointmentsByCustomerOnWeek.htm",url_customer_month:"calendar/loadAppointmentsByCustomerOnMonth.htm",EL:{$cal_wrap:e("#cal_content"),$cal_header:e(".cal-header"),$cal_title:e(".cal-title"),$cal_table:e(".cal-table"),$timeline:e(".timeline")}},this.VIEW="day",this.init(t)}return i.prototype={init:function(t){this.conf=e.extend({},this.defaults,t),this.scroll()},scroll:function(){var t=this,n=t.conf.EL,r=n.$cal_title,i=n.$cal_table,s=n.$timeline,o=50;e(document).on("click",".btn-update-availability",function(e){t.edithour_modal.update(e)}),e(document).on("click",".js-close-availability-modal",function(){t.edithour_modal.hideModal()}),n.$cal_wrap.scroll(function(){var t=e(this),n=t.scrollTop(),u=t.scrollLeft();n>0&&(i.css({top:o}),s.css({top:o}),r.css({position:"absolute",top:n})),u>0&&s.css({left:u}),u===0&&s.css({left:0}),n===0&&(s.css({top:0}),i.css({top:0}),r.css({position:"static"}))})},setView:function(e){var t=this;t.VIEW=e},generateMonthView:function(n){var r=this,i=r.conf.EL,s=i.$cal_header,o=i.$cal_wrap,u=i.$cal_title,a=i.$cal_table;e(".calendar-month").remove(),s.find("#cal_view_month").addClass("active").siblings().removeClass("active"),s.find(".type-switch").hide(),u.hide(),o.children(".row").hide(),o.append(n);var f=t(new Date).format("MM/DD/YYYY");e(".calendar-month").find('.js-to-day[data-date="'+f+'"]').parent().addClass("active selected")},generateWeekView:function(n){var i=this,s=i.conf,o=6,u=44,a=i.conf.EL,f=a.$cal_wrap,l=a.$cal_header,c=a.$cal_title,h=a.$cal_table,p=a.$timeline,d=e('<table class="calendar"><thead></thead><tbody></tbody'),v=[],m="",g="",y="",b,w;b=t(r.get("date")),w=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],h.empty().append(d),l.find("#cal_view_week").addClass("active").siblings().removeClass("active"),l.find(".type-switch").hide(),e(".cal-item").remove(),e(".calendar-month").remove();for(var E=0;E<w.length;E++)g+='<td data-weekday="'+E+'"></td>';e.each(w,function(e,t){m+="<th>"+w[e]+"</th>"});for(var E=o;E<u;E++)y+="<tr>"+g+"</tr>",E<12?v.push(E+" am"):E===12?v.push("Noon"):v.push(E-12+" pm");i.generateTimeline(o,u,v),c.html("<tr><th></th>"+m+"</tr>").show(),h.find("tbody").html(y),h.find("tbody tr").each(function(t,n){e(n).find("td").addClass("available").data("time",t)}),f.children(".row").show()},generateDayView:function(t){var i=this,s=i.conf,o=6,u=44,a=i.conf.EL,f=a.$cal_wrap,l=a.$cal_header,c=a.$cal_title,h=a.$cal_table,p=a.$timeline,d=r.get("view"),v=r.get("filter"),m,g=i.VIEW,y=e('<table class="calendar"><thead></thead><tbody></tbody'),b=[],w="",E="",S="",x=t.instructorList||[],T=t.roomList||[];m===undefined&&(m=v||"instructor"),g===undefined&&(g=d),h.empty().append(y),l.find("#cal_view_day").addClass("active").siblings().removeClass("active"),l.find(".type-switch").show(),e(".calendar-month").remove();if(m==="room"){e("#cal_filter_room").addClass("active").siblings().removeClass("active");for(var N=0;N<T.length;N++)E+='<td data-roomid="'+T[N].roomId+'"></td>';T.length<6?e.each(T,function(e,t){w+=n.render('<th id="room_{{roomId}}" class="room-title" style="width: auto;">{{profileRoomName}}</th>',t)}):T.length===6?e.each(T,function(e,t){w+=n.render('<th id="room_{{roomId}}" class="room-title" style="width: 107px;">{{profileRoomName}}</th>',t)}):e.each(T,function(e,t){w+=n.render('<th id="room_{{roomId}}" class="room-title">{{profileRoomName}}</th>',t)})}else{e("#cal_filter_instructor").addClass("active").siblings().removeClass("active");for(var N=0;N<x.length;N++)E+='<td data-instructorid="'+x[N].instructorId+'"></td>';x.length<6?e.each(x,function(e,t){w+=n.render('<th id="instructor_{{instructorId}}" class="instructor-title" style="width: auto;">{{person.firstName}} {{person.lastName}}</th>',t)}):x.length===6?e.each(x,function(e,t){w+=n.render('<th id="instructor_{{instructorId}}" class="instructor-title" style="width: 107px;">{{person.firstName}} {{person.lastName}}</th>',t)}):e.each(x,function(e,t){w+=n.render('<th id="instructor_{{instructorId}}" class="instructor-title">{{person.firstName}} {{person.lastName}}</th>',t)})}for(var N=o;N<u;N++)S+="<tr>"+E+"</tr>",N<12?b.push(N+" am"):N===12?b.push("Noon"):b.push(N-12+" pm");i.generateTimeline(o,u,b),c.html("<tr><th></th>"+w+"</tr>").show(),h.find("tbody").append(S),h.find("tbody tr").each(function(t,n){e(n).find("td").addClass("available").data("time",t)}),f.children(".row").show()},generateTimeline:function(e,t,n){var r=this,i="",s=r.conf.EL.$timeline;for(var o=0;o<19;o++)i+='<span class="timepoint"><p>'+n[o]+"</p></span>";s.html(i)},dayView:function(e,n){var r=this;r.setView("day"),r.getCalendar(e||t().format("L"),n),r.showDate(e)},weekView:function(e,n){var r=this;r.setView("week"),r.getCalendar(e||t().format("L"),n),r.showDate(e)},monthView:function(e,n){var r=this;r.setView("month"),r.getCalendar(e||t().format("L"),n),r.showDate(e)},prev:function(e,n){var r=this,i=r.VIEW,s;switch(i){case"day":s=t(e).subtract("days",1).format("L");break;case"week":s=t(e).subtract("weeks",1).format("L");break;case"month":s=t(e).subtract("months",1).format("L")}r.getCalendar(s,n),r.showDate(s)},next:function(e,n){var r=this,i=r.VIEW,s;switch(i){case"day":s=t(e).add("days",1).format("L");break;case"week":s=t(e).add("weeks",1).format("L");break;case"month":s=t(e).add("months",1).format("L")}r.getCalendar(s,n),r.showDate(s)},gotoToday:function(e){var n=this,r=t().format("L");n.setView("day"),n.getCalendar(r,e),n.showDate(r)},getCalendar:function(n,i,s){var o=this,u=o.conf,a=u.EL,f=a.$cal_wrap,l=r.get("date")||n,c=r.get("view"),h=r.get("filter"),p=r.get("customer"),d,s,v=o.VIEW;s===undefined&&(s=h||"instructor"),v===undefined&&(v=c),p!==undefined&&e.extend(i,{customer:p});switch(v){case"day":var m=e.extend(i,{date:n});p!==undefined?(s==="room"||s===""?m=e.extend(m,{type:"room"}):m=e.extend(m,{type:"instructor"}),d=u.url_customer_day):s==="room"?d=u.url_room:d=u.url_instructor,r.add({date:n,view:v,filter:s}),e.ajax({url:d,type:"GET",data:m,success:function(t){typeof t=="string"&&t.indexOf("DOCTYPE")!==-1&&location.reload(),e(".cal-item").remove(),o.generateDayView(t),o.generateDisabledArea(t);var n=t.appointmentList,r=t.unselectedAppointments,i=t.instructorList,a=t.roomList;u.handler(i,a,r,n,v,f,s)}});break;case"week":var g=t(n).add("weeks",1).format("L"),m=e.extend(i,{date:n+"-"+g});r.add({date:n,endDate:g,view:v}),p!==undefined?d=u.url_customer_week:d=u.url_week,e.ajax({url:d,type:"GET",data:m,success:function(t){typeof t=="string"&&t.indexOf("DOCTYPE")!==-1&&location.reload(),e(".cal-item").remove(),o.generateWeekView(),o.generateDisabledArea(t),u.handler([],[],[],t.resultMap,v,f,s)}});break;case"month":var g=t(n).add("months",1).format("L"),m=e.extend(i,{date:n+"-"+g});r.add({date:n,view:v}),p!==undefined?d=u.url_customer_month:d=u.url_month,e.ajax({url:d,type:"GET",data:m,success:function(t){typeof t=="string"&&t.indexOf("DOCTYPE")!==-1&&location.reload(),e(".cal-item").remove(),o.generateMonthView(t),e('.month-item p[data-date="'+n+'"]').parent().addClass("selected")}})}},showDate:function(n){var r=this,i,s=r.VIEW;switch(s){case"day":i=t(n).format("dddd, MMMM D, YYYY");break;case"week":i=t(n).format("dddd, MMMM D, YYYY");break;case"month":i=t(n).format("MMMM YYYY")}e(".js-cal-date").text(i)},generateDisabledArea:function(t){function c(t,r){_.forEach(t.list,function(t){var i=e(o);u=t.startTime.split(":")[0],a=t.startTime.split(":")[1],f=(u*1-5)*60+a*1,l=s*r,i.css({top:f,left:l,height:t.duration,width:s}).appendTo(n.conf.EL.$cal_table.find(".calendar"))})}function h(t,r){var i=e(o);u=t.startTime.split(":")[0],a=t.startTime.split(":")[1],f=(u*1-5)*60+a*1,i.css({top:f,height:t.duration,width:s*r}).appendTo(n.conf.EL.$cal_table.find(".calendar"))}function p(t,i){var c=r.find("th").index(e("#instructor_"+t.instructorId));l=s*(c-1),_.forEach(t.list,function(r){var c=e(o);u=r.startTime.split(":")[0],a=r.startTime.split(":")[1],f=(u*1-5)*60+a*1;var h=i.length;h>3&&h<6&&t.instructorId!=i[0].instructorId&&(l=l*1+2),c.css({top:f,left:l,height:r.duration,width:s}).appendTo(n.conf.EL.$cal_table.find(".calendar"))})}var n=this,r=n.conf.EL.$cal_title,i=r.find("th").eq(0).outerWidth(),s=r.find("th").eq(1).outerWidth(),o='<div class="disabled-cell"></div>',u,a,f,l;t.roomList&&t.roomList.length&&t.roomViewUnavailableHourDTOs&&_.forEach(t.roomViewUnavailableHourDTOs,function(e){h(e,t.roomList.length)}),t.instructorList&&t.instructorList.length&&t.instructorViewUnavailableHourDTOs&&_.forEach(t.instructorViewUnavailableHourDTOs,function(e){p(e,t.instructorList)}),t.weekViewUnavailableHourDTOs&&t.weekViewUnavailableHourDTOs.length&&_.forEach(t.weekViewUnavailableHourDTOs,function(e,t){c(e,t)})}},i}),function(e){function t(e,t,i){var s=e[0],o=/er/.test(i)?v:/bl/.test(i)?p:c;active=i==m?{checked:s[c],disabled:s[p],indeterminate:"true"==e.attr(v)||"false"==e.attr(d)}:s[o];if(/^(ch|di|in)/.test(i)&&!active)n(e,o);else if(/^(un|en|de)/.test(i)&&active)r(e,o);else if(i==m)for(var o in active)active[o]?n(e,o,!0):r(e,o,!0);else if(!t||"toggle"==i)t||e[w]("ifClicked"),active?s[g]!==l&&r(e,o):n(e,o)}function n(t,n,i){var m=t[0],w=t.parent(),E=n==c,x=n==v,T=x?d:E?h:"enabled",N=s(m,T+o(m[g])),C=s(m,n+o(m[g]));if(!0!==m[n]){if(!i&&n==c&&m[g]==l&&m.name){var L=t.closest("form"),O='input[name="'+m.name+'"]',O=L.length?L.find(O):e(O);O.each(function(){this!==m&&e.data(this,a)&&r(e(this),n)})}x?(m[n]=!0,m[c]&&r(t,c,"force")):(i||(m[n]=!0),E&&m[v]&&r(t,v,!1)),u(t,E,n,i)}m[p]&&s(m,S,!0)&&w.find("."+f).css(S,"default"),w[y](C||s(m,n)),w[b](N||s(m,T)||"")}function r(e,t,n){var r=e[0],i=e.parent(),a=t==c,l=t==v,m=l?d:a?h:"enabled",w=s(r,m+o(r[g])),E=s(r,t+o(r[g]));if(!1!==r[t]){if(l||!n||"force"==n)r[t]=!1;u(e,a,m,n)}!r[p]&&s(r,S,!0)&&i.find("."+f).css(S,"pointer"),i[b](E||s(r,t)||""),i[y](w||s(r,m))}function i(t,n){if(e.data(t,a)){var r=e(t);r.parent().html(r.attr("style",e.data(t,a).s||"")[w](n||"")),r.off(".i").unwrap(),e(E+'[for="'+t.id+'"]').add(r.closest(E)).off(".i")}}function s(t,n,r){if(e.data(t,a))return e.data(t,a).o[n+(r?"":"Class")]}function o(e){return e.charAt(0).toUpperCase()+e.slice(1)}function u(e,t,n,r){r||(t&&e[w]("ifToggled"),e[w]("ifChanged")[w]("if"+o(n)))}var a="iCheck",f=a+"-helper",l="radio",c="checked",h="un"+c,p="disabled",d="determinate",v="in"+d,m="update",g="type",y="addClass",b="removeClass",w="trigger",E="label",S="cursor",x=/ipad|iphone|ipod|android|blackberry|windows phone|opera mini/i.test(navigator.userAgent);e.fn[a]=function(s,o){var u=":checkbox, :"+l,h=e(),d=function(t){t.each(function(){var t=e(this);h=t.is(u)?h.add(t):h.add(t.find(u))})};if(/^(check|uncheck|toggle|indeterminate|determinate|disable|enable|update|destroy)$/i.test(s))return s=s.toLowerCase(),d(this),h.each(function(){"destroy"==s?i(this,"ifDestroyed"):t(e(this),!0,s),e.isFunction(o)&&o()});if("object"==typeof s||!s){var S=e.extend({checkedClass:c,disabledClass:p,indeterminateClass:v,labelHover:!0},s),T=S.handle,N=S.hoverClass||"hover",L=S.focusClass||"focus",O=S.activeClass||"active",M=!!S.labelHover,_=S.labelHoverClass||"hover",P=(""+S.increaseArea).replace("%","")|0;if("checkbox"==T||T==l)u=":"+T;return-50>P&&(P=-50),d(this),h.each(function(){i(this);var s=e(this),o=this,u=o.id,h=-P+"%",d=100+2*P+"%",d={position:"absolute",top:h,left:h,display:"block",width:d,height:d,margin:0,padding:0,background:"#fff",border:0,opacity:0},h=x?{position:"absolute",visibility:"hidden"}:P?d:{position:"absolute",opacity:0},v="checkbox"==o[g]?S.checkboxClass||"icheckbox":S.radioClass||"i"+l,T=e(E+'[for="'+u+'"]').add(s.closest(E)),k=s.wrap('<div class="'+v+'"/>')[w]("ifCreated").parent().append(S.insert),d=e('<ins class="'+f+'"/>').css(d).appendTo(k);s.data(a,{o:S,s:s.attr("style")}).css(h),S.inheritClass&&k[y](o.className),S.inheritID&&u&&k.attr("id",a+"-"+u),"static"==k.css("position")&&k.css("position","relative"),t(s,!0,m),T.length&&T.on("click.i mouseenter.i mouseleave.i touchbegin.i touchend.i",function(n){var r=n[g],i=e(this);if(!o[p]){if("click"==r?t(s,!1,!0):M&&(/ve|nd/.test(r)?(k[b](N),i[b](_)):(k[y](N),i[y](_))),!x)return!1;n.stopPropagation()}}),s.on("click.i focus.i blur.i keyup.i keydown.i keypress.i",function(e){var t=e[g];e=e.keyCode;if("click"==t)return!1;if("keydown"==t&&32==e)return o[g]==l&&o[c]||(o[c]?r(s,c):n(s,c)),!1;"keyup"==t&&o[g]==l?!o[c]&&n(s,c):/us|ur/.test(t)&&k["blur"==t?b:y](L)}),d.on("click mousedown mouseup mouseover mouseout touchbegin.i touchend.i",function(e){var n=e[g],r=/wn|up/.test(n)?O:N;if(!o[p]){"click"==n?t(s,!1,!0):(/wn|er|in/.test(n)?k[y](r):k[b](r+" "+O),T.length&&M&&r==N&&T[/ut|nd/.test(n)?b:y](_));if(!x)return!1;e.stopPropagation()}})})}return this}}(jQuery),define("icheck",["jquery"],function(e){return function(){var t,n;return t||e.iCheck}}(this)),function(){var e=this,t=e._,n={},r=Array.prototype,i=Object.prototype,s=Function.prototype,o=r.push,u=r.slice,a=r.concat,f=i.toString,l=i.hasOwnProperty,c=r.forEach,h=r.map,p=r.reduce,d=r.reduceRight,v=r.filter,m=r.every,g=r.some,y=r.indexOf,b=r.lastIndexOf,w=Array.isArray,E=Object.keys,S=s.bind,x=function(e){if(e instanceof x)return e;if(!(this instanceof x))return new x(e);this._wrapped=e};typeof exports!="undefined"?(typeof module!="undefined"&&module.exports&&(exports=module.exports=x),exports._=x):e._=x,x.VERSION="1.5.1";var T=x.each=x.forEach=function(e,t,r){if(e==null)return;if(c&&e.forEach===c)e.forEach(t,r);else if(e.length===+e.length){for(var i=0,s=e.length;i<s;i++)if(t.call(r,e[i],i,e)===n)return}else for(var o in e)if(x.has(e,o)&&t.call(r,e[o],o,e)===n)return};x.map=x.collect=function(e,t,n){var r=[];return e==null?r:h&&e.map===h?e.map(t,n):(T(e,function(e,i,s){r.push(t.call(n,e,i,s))}),r)};var N="Reduce of empty array with no initial value";x.reduce=x.foldl=x.inject=function(e,t,n,r){var i=arguments.length>2;e==null&&(e=[]);if(p&&e.reduce===p)return r&&(t=x.bind(t,r)),i?e.reduce(t,n):e.reduce(t);T(e,function(e,s,o){i?n=t.call(r,n,e,s,o):(n=e,i=!0)});if(!i)throw new TypeError(N);return n},x.reduceRight=x.foldr=function(e,t,n,r){var i=arguments.length>2;e==null&&(e=[]);if(d&&e.reduceRight===d)return r&&(t=x.bind(t,r)),i?e.reduceRight(t,n):e.reduceRight(t);var s=e.length;if(s!==+s){var o=x.keys(e);s=o.length}T(e,function(u,a,f){a=o?o[--s]:--s,i?n=t.call(r,n,e[a],a,f):(n=e[a],i=!0)});if(!i)throw new TypeError(N);return n},x.find=x.detect=function(e,t,n){var r;return C(e,function(e,i,s){if(t.call(n,e,i,s))return r=e,!0}),r},x.filter=x.select=function(e,t,n){var r=[];return e==null?r:v&&e.filter===v?e.filter(t,n):(T(e,function(e,i,s){t.call(n,e,i,s)&&r.push(e)}),r)},x.reject=function(e,t,n){return x.filter(e,function(e,r,i){return!t.call(n,e,r,i)},n)},x.every=x.all=function(e,t,r){t||(t=x.identity);var i=!0;return e==null?i:m&&e.every===m?e.every(t,r):(T(e,function(e,s,o){if(!(i=i&&t.call(r,e,s,o)))return n}),!!i)};var C=x.some=x.any=function(e,t,r){t||(t=x.identity);var i=!1;return e==null?i:g&&e.some===g?e.some(t,r):(T(e,function(e,s,o){if(i||(i=t.call(r,e,s,o)))return n}),!!i)};x.contains=x.include=function(e,t){return e==null?!1:y&&e.indexOf===y?e.indexOf(t)!=-1:C(e,function(e){return e===t})},x.invoke=function(e,t){var n=u.call(arguments,2),r=x.isFunction(t);return x.map(e,function(e){return(r?t:e[t]).apply(e,n)})},x.pluck=function(e,t){return x.map(e,function(e){return e[t]})},x.where=function(e,t,n){return x.isEmpty(t)?n?void 0:[]:x[n?"find":"filter"](e,function(e){for(var n in t)if(t[n]!==e[n])return!1;return!0})},x.findWhere=function(e,t){return x.where(e,t,!0)},x.max=function(e,t,n){if(!t&&x.isArray(e)&&e[0]===+e[0]&&e.length<65535)return Math.max.apply(Math,e);if(!t&&x.isEmpty(e))return-Infinity;var r={computed:-Infinity,value:-Infinity};return T(e,function(e,i,s){var o=t?t.call(n,e,i,s):e;o>r.computed&&(r={value:e,computed:o})}),r.value},x.min=function(e,t,n){if(!t&&x.isArray(e)&&e[0]===+e[0]&&e.length<65535)return Math.min.apply(Math,e);if(!t&&x.isEmpty(e))return Infinity;var r={computed:Infinity,value:Infinity};return T(e,function(e,i,s){var o=t?t.call(n,e,i,s):e;o<r.computed&&(r={value:e,computed:o})}),r.value},x.shuffle=function(e){var t,n=0,r=[];return T(e,function(e){t=x.random(n++),r[n-1]=r[t],r[t]=e}),r};var k=function(e){return x.isFunction(e)?e:function(t){return t[e]}};x.sortBy=function(e,t,n){var r=k(t);return x.pluck(x.map(e,function(e,t,i){return{value:e,index:t,criteria:r.call(n,e,t,i)}}).sort(function(e,t){var n=e.criteria,r=t.criteria;if(n!==r){if(n>r||n===void 0)return 1;if(n<r||r===void 0)return-1}return e.index<t.index?-1:1}),"value")};var L=function(e,t,n,r){var i={},s=k(t==null?x.identity:t);return T(e,function(t,o){var u=s.call(n,t,o,e);r(i,u,t)}),i};x.groupBy=function(e,t,n){return L(e,t,n,function(e,t,n){(x.has(e,t)?e[t]:e[t]=[]).push(n)})},x.countBy=function(e,t,n){return L(e,t,n,function(e,t){x.has(e,t)||(e[t]=0),e[t]++})},x.sortedIndex=function(e,t,n,r){n=n==null?x.identity:k(n);var i=n.call(r,t),s=0,o=e.length;while(s<o){var u=s+o>>>1;n.call(r,e[u])<i?s=u+1:o=u}return s},x.toArray=function(e){return e?x.isArray(e)?u.call(e):e.length===+e.length?x.map(e,x.identity):x.values(e):[]},x.size=function(e){return e==null?0:e.length===+e.length?e.length:x.keys(e).length},x.first=x.head=x.take=function(e,t,n){return e==null?void 0:t!=null&&!n?u.call(e,0,t):e[0]},x.initial=function(e,t,n){return u.call(e,0,e.length-(t==null||n?1:t))},x.last=function(e,t,n){return e==null?void 0:t!=null&&!n?u.call(e,Math.max(e.length-t,0)):e[e.length-1]},x.rest=x.tail=x.drop=function(e,t,n){return u.call(e,t==null||n?1:t)},x.compact=function(e){return x.filter(e,x.identity)};var A=function(e,t,n){return t&&x.every(e,x.isArray)?a.apply(n,e):(T(e,function(e){x.isArray(e)||x.isArguments(e)?t?o.apply(n,e):A(e,t,n):n.push(e)}),n)};x.flatten=function(e,t){return A(e,t,[])},x.without=function(e){return x.difference(e,u.call(arguments,1))},x.uniq=x.unique=function(e,t,n,r){x.isFunction(t)&&(r=n,n=t,t=!1);var i=n?x.map(e,n,r):e,s=[],o=[];return T(i,function(n,r){if(t?!r||o[o.length-1]!==n:!x.contains(o,n))o.push(n),s.push(e[r])}),s},x.union=function(){return x.uniq(x.flatten(arguments,!0))},x.intersection=function(e){var t=u.call(arguments,1);return x.filter(x.uniq(e),function(e){return x.every(t,function(t){return x.indexOf(t,e)>=0})})},x.difference=function(e){var t=a.apply(r,u.call(arguments,1));return x.filter(e,function(e){return!x.contains(t,e)})},x.zip=function(){var e=x.max(x.pluck(arguments,"length").concat(0)),t=new Array(e);for(var n=0;n<e;n++)t[n]=x.pluck(arguments,""+n);return t},x.object=function(e,t){if(e==null)return{};var n={};for(var r=0,i=e.length;r<i;r++)t?n[e[r]]=t[r]:n[e[r][0]]=e[r][1];return n},x.indexOf=function(e,t,n){if(e==null)return-1;var r=0,i=e.length;if(n){if(typeof n!="number")return r=x.sortedIndex(e,t),e[r]===t?r:-1;r=n<0?Math.max(0,i+n):n}if(y&&e.indexOf===y)return e.indexOf(t,n);for(;r<i;r++)if(e[r]===t)return r;return-1},x.lastIndexOf=function(e,t,n){if(e==null)return-1;var r=n!=null;if(b&&e.lastIndexOf===b)return r?e.lastIndexOf(t,n):e.lastIndexOf(t);var i=r?n:e.length;while(i--)if(e[i]===t)return i;return-1},x.range=function(e,t,n){arguments.length<=1&&(t=e||0,e=0),n=arguments[2]||1;var r=Math.max(Math.ceil((t-e)/n),0),i=0,s=new Array(r);while(i<r)s[i++]=e,e+=n;return s};var O=function(){};x.bind=function(e,t){var n,r;if(S&&e.bind===S)return S.apply(e,u.call(arguments,1));if(!x.isFunction(e))throw new TypeError;return n=u.call(arguments,2),r=function(){if(this instanceof r){O.prototype=e.prototype;var i=new O;O.prototype=null;var s=e.apply(i,n.concat(u.call(arguments)));return Object(s)===s?s:i}return e.apply(t,n.concat(u.call(arguments)))}},x.partial=function(e){var t=u.call(arguments,1);return function(){return e.apply(this,t.concat(u.call(arguments)))}},x.bindAll=function(e){var t=u.call(arguments,1);if(t.length===0)throw new Error("bindAll must be passed function names");return T(t,function(t){e[t]=x.bind(e[t],e)}),e},x.memoize=function(e,t){var n={};return t||(t=x.identity),function(){var r=t.apply(this,arguments);return x.has(n,r)?n[r]:n[r]=e.apply(this,arguments)}},x.delay=function(e,t){var n=u.call(arguments,2);return setTimeout(function(){return e.apply(null,n)},t)},x.defer=function(e){return x.delay.apply(x,[e,1].concat(u.call(arguments,1)))},x.throttle=function(e,t,n){var r,i,s,o=null,u=0;n||(n={});var a=function(){u=n.leading===!1?0:new Date,o=null,s=e.apply(r,i)};return function(){var f=new Date;!u&&n.leading===!1&&(u=f);var l=t-(f-u);return r=this,i=arguments,l<=0?(clearTimeout(o),o=null,u=f,s=e.apply(r,i)):!o&&n.trailing!==!1&&(o=setTimeout(a,l)),s}},x.debounce=function(e,t,n){var r,i=null;return function(){var s=this,o=arguments,u=function(){i=null,n||(r=e.apply(s,o))},a=n&&!i;return clearTimeout(i),i=setTimeout(u,t),a&&(r=e.apply(s,o)),r}},x.once=function(e){var t=!1,n;return function(){return t?n:(t=!0,n=e.apply(this,arguments),e=null,n)}},x.wrap=function(e,t){return function(){var n=[e];return o.apply(n,arguments),t.apply(this,n)}},x.compose=function(){var e=arguments;return function(){var t=arguments;for(var n=e.length-1;n>=0;n--)t=[e[n].apply(this,t)];return t[0]}},x.after=function(e,t){return function(){if(--e<1)return t.apply(this,arguments)}},x.keys=E||function(e){if(e!==Object(e))throw new TypeError("Invalid object");var t=[];for(var n in e)x.has(e,n)&&t.push(n);return t},x.values=function(e){var t=[];for(var n in e)x.has(e,n)&&t.push(e[n]);return t},x.pairs=function(e){var t=[];for(var n in e)x.has(e,n)&&t.push([n,e[n]]);return t},x.invert=function(e){var t={};for(var n in e)x.has(e,n)&&(t[e[n]]=n);return t},x.functions=x.methods=function(e){var t=[];for(var n in e)x.isFunction(e[n])&&t.push(n);return t.sort()},x.extend=function(e){return T(u.call(arguments,1),function(t){if(t)for(var n in t)e[n]=t[n]}),e},x.pick=function(e){var t={},n=a.apply(r,u.call(arguments,1));return T(n,function(n){n in e&&(t[n]=e[n])}),t},x.omit=function(e){var t={},n=a.apply(r,u.call(arguments,1));for(var i in e)x.contains(n,i)||(t[i]=e[i]);return t},x.defaults=function(e){return T(u.call(arguments,1),function(t){if(t)for(var n in t)e[n]===void 0&&(e[n]=t[n])}),e},x.clone=function(e){return x.isObject(e)?x.isArray(e)?e.slice():x.extend({},e):e},x.tap=function(e,t){return t(e),e};var M=function(e,t,n,r){if(e===t)return e!==0||1/e==1/t;if(e==null||t==null)return e===t;e instanceof x&&(e=e._wrapped),t instanceof x&&(t=t._wrapped);var i=f.call(e);if(i!=f.call(t))return!1;switch(i){case"[object String]":return e==String(t);case"[object Number]":return e!=+e?t!=+t:e==0?1/e==1/t:e==+t;case"[object Date]":case"[object Boolean]":return+e==+t;case"[object RegExp]":return e.source==t.source&&e.global==t.global&&e.multiline==t.multiline&&e.ignoreCase==t.ignoreCase}if(typeof e!="object"||typeof t!="object")return!1;var s=n.length;while(s--)if(n[s]==e)return r[s]==t;var o=e.constructor,u=t.constructor;if(o!==u&&!(x.isFunction(o)&&o instanceof o&&x.isFunction(u)&&u instanceof u))return!1;n.push(e),r.push(t);var a=0,l=!0;if(i=="[object Array]"){a=e.length,l=a==t.length;if(l)while(a--)if(!(l=M(e[a],t[a],n,r)))break}else{for(var c in e)if(x.has(e,c)){a++;if(!(l=x.has(t,c)&&M(e[c],t[c],n,r)))break}if(l){for(c in t)if(x.has(t,c)&&!(a--))break;l=!a}}return n.pop(),r.pop(),l};x.isEqual=function(e,t){return M(e,t,[],[])},x.isEmpty=function(e){if(e==null)return!0;if(x.isArray(e)||x.isString(e))return e.length===0;for(var t in e)if(x.has(e,t))return!1;return!0},x.isElement=function(e){return!!e&&e.nodeType===1},x.isArray=w||function(e){return f.call(e)=="[object Array]"},x.isObject=function(e){return e===Object(e)},T(["Arguments","Function","String","Number","Date","RegExp"],function(e){x["is"+e]=function(t){return f.call(t)=="[object "+e+"]"}}),x.isArguments(arguments)||(x.isArguments=function(e){return!!e&&!!x.has(e,"callee")}),typeof /./!="function"&&(x.isFunction=function(e){return typeof e=="function"}),x.isFinite=function(e){return isFinite(e)&&!isNaN(parseFloat(e))},x.isNaN=function(e){return x.isNumber(e)&&e!=+e},x.isBoolean=function(e){return e===!0||e===!1||f.call(e)=="[object Boolean]"},x.isNull=function(e){return e===null},x.isUndefined=function(e){return e===void 0},x.has=function(e,t){return l.call(e,t)},x.noConflict=function(){return e._=t,this},x.identity=function(e){return e},x.times=function(e,t,n){var r=Array(Math.max(0,e));for(var i=0;i<e;i++)r[i]=t.call(n,i);return r},x.random=function(e,t){return t==null&&(t=e,e=0),e+Math.floor(Math.random()*(t-e+1))};var _={escape:{"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","/":"&#x2F;"}};_.unescape=x.invert(_.escape);var D={escape:new RegExp("["+x.keys(_.escape).join("")+"]","g"),unescape:new RegExp("("+x.keys(_.unescape).join("|")+")","g")};x.each(["escape","unescape"],function(e){x[e]=function(t){return t==null?"":(""+t).replace(D[e],function(t){return _[e][t]})}}),x.result=function(e,t){if(e==null)return void 0;var n=e[t];return x.isFunction(n)?n.call(e):n},x.mixin=function(e){T(x.functions(e),function(t){var n=x[t]=e[t];x.prototype[t]=function(){var e=[this._wrapped];return o.apply(e,arguments),F.call(this,n.apply(x,e))}})};var P=0;x.uniqueId=function(e){var t=++P+"";return e?e+t:t},x.templateSettings={evaluate:/<%([\s\S]+?)%>/g,interpolate:/<%=([\s\S]+?)%>/g,escape:/<%-([\s\S]+?)%>/g};var H=/(.)^/,B={"'":"'","\\":"\\","\r":"r","\n":"n","	":"t","\u2028":"u2028","\u2029":"u2029"},j=/\\|'|\r|\n|\t|\u2028|\u2029/g;x.template=function(e,t,n){var r;n=x.defaults({},n,x.templateSettings);var i=new RegExp([(n.escape||H).source,(n.interpolate||H).source,(n.evaluate||H).source].join("|")+"|$","g"),s=0,o="__p+='";e.replace(i,function(t,n,r,i,u){return o+=e.slice(s,u).replace(j,function(e){return"\\"+B[e]}),n&&(o+="'+\n((__t=("+n+"))==null?'':_.escape(__t))+\n'"),r&&(o+="'+\n((__t=("+r+"))==null?'':__t)+\n'"),i&&(o+="';\n"+i+"\n__p+='"),s=u+t.length,t}),o+="';\n",n.variable||(o="with(obj||{}){\n"+o+"}\n"),o="var __t,__p='',__j=Array.prototype.join,print=function(){__p+=__j.call(arguments,'');};\n"+o+"return __p;\n";try{r=new Function(n.variable||"obj","_",o)}catch(u){throw u.source=o,u}if(t)return r(t,x);var a=function(e){return r.call(this,e,x)};return a.source="function("+(n.variable||"obj")+"){\n"+o+"}",a},x.chain=function(e){return x(e).chain()};var F=function(e){return this._chain?x(e).chain():e};x.mixin(x),T(["pop","push","reverse","shift","sort","splice","unshift"],function(e){var t=r[e];x.prototype[e]=function(){var n=this._wrapped;return t.apply(n,arguments),(e=="shift"||e=="splice")&&n.length===0&&delete n[0],F.call(this,n)}}),T(["concat","join","slice"],function(e){var t=r[e];x.prototype[e]=function(){return F.call(this,t.apply(this._wrapped,arguments))}}),x.extend(x.prototype,{chain:function(){return this._chain=!0,this},value:function(){return this._wrapped}})}.call(this),define("underscore",function(e){return function(){var t,n;return t||e._}}(this)),function(){var e=this,t=e.Backbone,n=[],r=n.push,i=n.slice,s=n.splice,o;typeof exports!="undefined"?o=exports:o=e.Backbone={},o.VERSION="1.0.0";var u=e._;!u&&typeof require!="undefined"&&(u=require("underscore")),o.$=e.jQuery||e.Zepto||e.ender||e.$,o.noConflict=function(){return e.Backbone=t,this},o.emulateHTTP=!1,o.emulateJSON=!1;var a=o.Events={on:function(e,t,n){if(!l(this,"on",e,[t,n])||!t)return this;this._events||(this._events={});var r=this._events[e]||(this._events[e]=[]);return r.push({callback:t,context:n,ctx:n||this}),this},once:function(e,t,n){if(!l(this,"once",e,[t,n])||!t)return this;var r=this,i=u.once(function(){r.off(e,i),t.apply(this,arguments)});return i._callback=t,this.on(e,i,n)},off:function(e,t,n){var r,i,s,o,a,f,c,h;if(!this._events||!l(this,"off",e,[t,n]))return this;if(!e&&!t&&!n)return this._events={},this;o=e?[e]:u.keys(this._events);for(a=0,f=o.length;a<f;a++){e=o[a];if(s=this._events[e]){this._events[e]=r=[];if(t||n)for(c=0,h=s.length;c<h;c++)i=s[c],(t&&t!==i.callback&&t!==i.callback._callback||n&&n!==i.context)&&r.push(i);r.length||delete this._events[e]}}return this},trigger:function(e){if(!this._events)return this;var t=i.call(arguments,1);if(!l(this,"trigger",e,t))return this;var n=this._events[e],r=this._events.all;return n&&c(n,t),r&&c(r,arguments),this},stopListening:function(e,t,n){var r=this._listeners;if(!r)return this;var i=!t&&!n;typeof t=="object"&&(n=this),e&&((r={})[e._listenerId]=e);for(var s in r)r[s].off(t,n,this),i&&delete this._listeners[s];return this}},f=/\s+/,l=function(e,t,n,r){if(!n)return!0;if(typeof n=="object"){for(var i in n)e[t].apply(e,[i,n[i]].concat(r));return!1}if(f.test(n)){var s=n.split(f);for(var o=0,u=s.length;o<u;o++)e[t].apply(e,[s[o]].concat(r));return!1}return!0},c=function(e,t){var n,r=-1,i=e.length,s=t[0],o=t[1],u=t[2];switch(t.length){case 0:while(++r<i)(n=e[r]).callback.call(n.ctx);return;case 1:while(++r<i)(n=e[r]).callback.call(n.ctx,s);return;case 2:while(++r<i)(n=e[r]).callback.call(n.ctx,s,o);return;case 3:while(++r<i)(n=e[r]).callback.call(n.ctx,s,o,u);return;default:while(++r<i)(n=e[r]).callback.apply(n.ctx,t)}},h={listenTo:"on",listenToOnce:"once"};u.each(h,function(e,t){a[t]=function(t,n,r){var i=this._listeners||(this._listeners={}),s=t._listenerId||(t._listenerId=u.uniqueId("l"));return i[s]=t,typeof n=="object"&&(r=this),t[e](n,r,this),this}}),a.bind=a.on,a.unbind=a.off,u.extend(o,a);var p=o.Model=function(e,t){var n,r=e||{};t||(t={}),this.cid=u.uniqueId("c"),this.attributes={},u.extend(this,u.pick(t,d)),t.parse&&(r=this.parse(r,t)||{});if(n=u.result(this,"defaults"))r=u.defaults({},r,n);this.set(r,t),this.changed={},this.initialize.apply(this,arguments)},d=["url","urlRoot","collection"];u.extend(p.prototype,a,{changed:null,validationError:null,idAttribute:"id",initialize:function(){},toJSON:function(e){return u.clone(this.attributes)},sync:function(){return o.sync.apply(this,arguments)},get:function(e){return this.attributes[e]},escape:function(e){return u.escape(this.get(e))},has:function(e){return this.get(e)!=null},set:function(e,t,n){var r,i,s,o,a,f,l,c;if(e==null)return this;typeof e=="object"?(i=e,n=t):(i={})[e]=t,n||(n={});if(!this._validate(i,n))return!1;s=n.unset,a=n.silent,o=[],f=this._changing,this._changing=!0,f||(this._previousAttributes=u.clone(this.attributes),this.changed={}),c=this.attributes,l=this._previousAttributes,this.idAttribute in i&&(this.id=i[this.idAttribute]);for(r in i)t=i[r],u.isEqual(c[r],t)||o.push(r),u.isEqual(l[r],t)?delete this.changed[r]:this.changed[r]=t,s?delete c[r]:c[r]=t;if(!a){o.length&&(this._pending=!0);for(var h=0,p=o.length;h<p;h++)this.trigger("change:"+o[h],this,c[o[h]],n)}if(f)return this;if(!a)while(this._pending)this._pending=!1,this.trigger("change",this,n);return this._pending=!1,this._changing=!1,this},unset:function(e,t){return this.set(e,void 0,u.extend({},t,{unset:!0}))},clear:function(e){var t={};for(var n in this.attributes)t[n]=void 0;return this.set(t,u.extend({},e,{unset:!0}))},hasChanged:function(e){return e==null?!u.isEmpty(this.changed):u.has(this.changed,e)},changedAttributes:function(e){if(!e)return this.hasChanged()?u.clone(this.changed):!1;var t,n=!1,r=this._changing?this._previousAttributes:this.attributes;for(var i in e){if(u.isEqual(r[i],t=e[i]))continue;(n||(n={}))[i]=t}return n},previous:function(e){return e==null||!this._previousAttributes?null:this._previousAttributes[e]},previousAttributes:function(){return u.clone(this._previousAttributes)},fetch:function(e){e=e?u.clone(e):{},e.parse===void 0&&(e.parse=!0);var t=this,n=e.success;return e.success=function(r){if(!t.set(t.parse(r,e),e))return!1;n&&n(t,r,e),t.trigger("sync",t,r,e)},j(this,e),this.sync("read",this,e)},save:function(e,t,n){var r,i,s,o=this.attributes;e==null||typeof e=="object"?(r=e,n=t):(r={})[e]=t;if(r&&(!n||!n.wait)&&!this.set(r,n))return!1;n=u.extend({validate:!0},n);if(!this._validate(r,n))return!1;r&&n.wait&&(this.attributes=u.extend({},o,r)),n.parse===void 0&&(n.parse=!0);var a=this,f=n.success;return n.success=function(e){a.attributes=o;var t=a.parse(e,n);n.wait&&(t=u.extend(r||{},t));if(u.isObject(t)&&!a.set(t,n))return!1;f&&f(a,e,n),a.trigger("sync",a,e,n)},j(this,n),i=this.isNew()?"create":n.patch?"patch":"update",i==="patch"&&(n.attrs=r),s=this.sync(i,this,n),r&&n.wait&&(this.attributes=o),s},destroy:function(e){e=e?u.clone(e):{};var t=this,n=e.success,r=function(){t.trigger("destroy",t,t.collection,e)};e.success=function(i){(e.wait||t.isNew())&&r(),n&&n(t,i,e),t.isNew()||t.trigger("sync",t,i,e)};if(this.isNew())return e.success(),!1;j(this,e);var i=this.sync("delete",this,e);return e.wait||r(),i},url:function(){var e=u.result(this,"urlRoot")||u.result(this.collection,"url")||B();return this.isNew()?e:e+(e.charAt(e.length-1)==="/"?"":"/")+encodeURIComponent(this.id)},parse:function(e,t){return e},clone:function(){return new this.constructor(this.attributes)},isNew:function(){return this.id==null},isValid:function(e){return this._validate({},u.extend(e||{},{validate:!0}))},_validate:function(e,t){if(!t.validate||!this.validate)return!0;e=u.extend({},this.attributes,e);var n=this.validationError=this.validate(e,t)||null;return n?(this.trigger("invalid",this,n,u.extend(t||{},{validationError:n})),!1):!0}});var v=["keys","values","pairs","invert","pick","omit"];u.each(v,function(e){p.prototype[e]=function(){var t=i.call(arguments);return t.unshift(this.attributes),u[e].apply(u,t)}});var m=o.Collection=function(e,t){t||(t={}),t.url&&(this.url=t.url),t.model&&(this.model=t.model),t.comparator!==void 0&&(this.comparator=t.comparator),this._reset(),this.initialize.apply(this,arguments),e&&this.reset(e,u.extend({silent:!0},t))},g={add:!0,remove:!0,merge:!0},y={add:!0,merge:!1,remove:!1};u.extend(m.prototype,a,{model:p,initialize:function(){},toJSON:function(e){return this.map(function(t){return t.toJSON(e)})},sync:function(){return o.sync.apply(this,arguments)},add:function(e,t){return this.set(e,u.defaults(t||{},y))},remove:function(e,t){e=u.isArray(e)?e.slice():[e],t||(t={});var n,r,i,s;for(n=0,r=e.length;n<r;n++){s=this.get(e[n]);if(!s)continue;delete this._byId[s.id],delete this._byId[s.cid],i=this.indexOf(s),this.models.splice(i,1),this.length--,t.silent||(t.index=i,s.trigger("remove",s,this,t)),this._removeReference(s)}return this},set:function(e,t){t=u.defaults(t||{},g),t.parse&&(e=this.parse(e,t)),u.isArray(e)||(e=e?[e]:[]);var n,i,o,a,f,l,c=t.at,h=this.comparator&&c==null&&t.sort!==!1,p=u.isString(this.comparator)?this.comparator:null,d=[],v=[],m={};for(n=0,i=e.length;n<i;n++){if(!(o=this._prepareModel(e[n],t)))continue;(f=this.get(o))?(t.remove&&(m[f.cid]=!0),t.merge&&(f.set(o.attributes,t),h&&!l&&f.hasChanged(p)&&(l=!0))):t.add&&(d.push(o),o.on("all",this._onModelEvent,this),this._byId[o.cid]=o,o.id!=null&&(this._byId[o.id]=o))}if(t.remove){for(n=0,i=this.length;n<i;++n)m[(o=this.models[n]).cid]||v.push(o);v.length&&this.remove(v,t)}d.length&&(h&&(l=!0),this.length+=d.length,c!=null?s.apply(this.models,[c,0].concat(d)):r.apply(this.models,d)),l&&this.sort({silent:!0});if(t.silent)return this;for(n=0,i=d.length;n<i;n++)(o=d[n]).trigger("add",o,this,t);return l&&this.trigger("sort",this,t),this},reset:function(e,t){t||(t={});for(var n=0,r=this.models.length;n<r;n++)this._removeReference(this.models[n]);return t.previousModels=this.models,this._reset(),this.add(e,u.extend({silent:!0},t)),t.silent||this.trigger("reset",this,t),this},push:function(e,t){return e=this._prepareModel(e,t),this.add(e,u.extend({at:this.length},t)),e},pop:function(e){var t=this.at(this.length-1);return this.remove(t,e),t},unshift:function(e,t){return e=this._prepareModel(e,t),this.add(e,u.extend({at:0},t)),e},shift:function(e){var t=this.at(0);return this.remove(t,e),t},slice:function(e,t){return this.models.slice(e,t)},get:function(e){return e==null?void 0:this._byId[e.id!=null?e.id:e.cid||e]},at:function(e){return this.models[e]},where:function(e,t){return u.isEmpty(e)?t?void 0:[]:this[t?"find":"filter"](function(t){for(var n in e)if(e[n]!==t.get(n))return!1;return!0})},findWhere:function(e){return this.where(e,!0)},sort:function(e){if(!this.comparator)throw new Error("Cannot sort a set without a comparator");return e||(e={}),u.isString(this.comparator)||this.comparator.length===1?this.models=this.sortBy(this.comparator,this):this.models.sort(u.bind(this.comparator,this)),e.silent||this.trigger("sort",this,e),this},sortedIndex:function(e,t,n){t||(t=this.comparator);var r=u.isFunction(t)?t:function(e){return e.get(t)};return u.sortedIndex(this.models,e,r,n)},pluck:function(e){return u.invoke(this.models,"get",e)},fetch:function(e){e=e?u.clone(e):{},e.parse===void 0&&(e.parse=!0);var t=e.success,n=this;return e.success=function(r){var i=e.reset?"reset":"set";n[i](r,e),t&&t(n,r,e),n.trigger("sync",n,r,e)},j(this,e),this.sync("read",this,e)},create:function(e,t){t=t?u.clone(t):{};if(!(e=this._prepareModel(e,t)))return!1;t.wait||this.add(e,t);var n=this,r=t.success;return t.success=function(i){t.wait&&n.add(e,t),r&&r(e,i,t)},e.save(null,t),e},parse:function(e,t){return e},clone:function(){return new this.constructor(this.models)},_reset:function(){this.length=0,this.models=[],this._byId={}},_prepareModel:function(e,t){if(e instanceof p)return e.collection||(e.collection=this),e;t||(t={}),t.collection=this;var n=new this.model(e,t);return n._validate(e,t)?n:(this.trigger("invalid",this,e,t),!1)},_removeReference:function(e){this===e.collection&&delete e.collection,e.off("all",this._onModelEvent,this)},_onModelEvent:function(e,t,n,r){if((e==="add"||e==="remove")&&n!==this)return;e==="destroy"&&this.remove(t,r),t&&e==="change:"+t.idAttribute&&(delete this._byId[t.previous(t.idAttribute)],t.id!=null&&(this._byId[t.id]=t)),this.trigger.apply(this,arguments)}});var b=["forEach","each","map","collect","reduce","foldl","inject","reduceRight","foldr","find","detect","filter","select","reject","every","all","some","any","include","contains","invoke","max","min","toArray","size","first","head","take","initial","rest","tail","drop","last","without","indexOf","shuffle","lastIndexOf","isEmpty","chain"];u.each(b,function(e){m.prototype[e]=function(){var t=i.call(arguments);return t.unshift(this.models),u[e].apply(u,t)}});var w=["groupBy","countBy","sortBy"];u.each(w,function(e){m.prototype[e]=function(t,n){var r=u.isFunction(t)?t:function(e){return e.get(t)};return u[e](this.models,r,n)}});var E=o.View=function(e){this.cid=u.uniqueId("view"),this._configure(e||{}),this._ensureElement(),this.initialize.apply(this,arguments),this.delegateEvents()},S=/^(\S+)\s*(.*)$/,x=["model","collection","el","id","attributes","className","tagName","events"];u.extend(E.prototype,a,{tagName:"div",$:function(e){return this.$el.find(e)},initialize:function(){},render:function(){return this},remove:function(){return this.$el.remove(),this.stopListening(),this},setElement:function(e,t){return this.$el&&this.undelegateEvents(),this.$el=e instanceof o.$?e:o.$(e),this.el=this.$el[0],t!==!1&&this.delegateEvents(),this},delegateEvents:function(e){if(!e&&!(e=u.result(this,"events")))return this;this.undelegateEvents();for(var t in e){var n=e[t];u.isFunction(n)||(n=this[e[t]]);if(!n)continue;var r=t.match(S),i=r[1],s=r[2];n=u.bind(n,this),i+=".delegateEvents"+this.cid,s===""?this.$el.on(i,n):this.$el.on(i,s,n)}return this},undelegateEvents:function(){return this.$el.off(".delegateEvents"+this.cid),this},_configure:function(e){this.options&&(e=u.extend({},u.result(this,"options"),e)),u.extend(this,u.pick(e,x)),this.options=e},_ensureElement:function(){if(!this.el){var e=u.extend({},u.result(this,"attributes"));this.id&&(e.id=u.result(this,"id")),this.className&&(e["class"]=u.result(this,"className"));var t=o.$("<"+u.result(this,"tagName")+">").attr(e);this.setElement(t,!1)}else this.setElement(u.result(this,"el"),!1)}}),o.sync=function(e,t,n){var r=T[e];u.defaults(n||(n={}),{emulateHTTP:o.emulateHTTP,emulateJSON:o.emulateJSON});var i={type:r,dataType:"json"};n.url||(i.url=u.result(t,"url")||B()),n.data==null&&t&&(e==="create"||e==="update"||e==="patch")&&(i.contentType="application/json",i.data=JSON.stringify(n.attrs||t.toJSON(n))),n.emulateJSON&&(i.contentType="application/x-www-form-urlencoded",i.data=i.data?{model:i.data}:{});if(n.emulateHTTP&&(r==="PUT"||r==="DELETE"||r==="PATCH")){i.type="POST",n.emulateJSON&&(i.data._method=r);var s=n.beforeSend;n.beforeSend=function(e){e.setRequestHeader("X-HTTP-Method-Override",r);if(s)return s.apply(this,arguments)}}i.type!=="GET"&&!n.emulateJSON&&(i.processData=!1),i.type==="PATCH"&&window.ActiveXObject&&(!window.external||!window.external.msActiveXFilteringEnabled)&&(i.xhr=function(){return new ActiveXObject("Microsoft.XMLHTTP")});var a=n.xhr=o.ajax(u.extend(i,n));return t.trigger("request",t,a,n),a};var T={create:"POST",update:"PUT",patch:"PATCH","delete":"DELETE",read:"GET"};o.ajax=function(){return o.$.ajax.apply(o.$,arguments)};var N=o.Router=function(e){e||(e={}),e.routes&&(this.routes=e.routes),this._bindRoutes(),this.initialize.apply(this,arguments)},C=/\((.*?)\)/g,k=/(\(\?)?:\w+/g,L=/\*\w+/g,A=/[\-{}\[\]+?.,\\\^$|#\s]/g;u.extend(N.prototype,a,{initialize:function(){},route:function(e,t,n){u.isRegExp(e)||(e=this._routeToRegExp(e)),u.isFunction(t)&&(n=t,t=""),n||(n=this[t]);var r=this;return o.history.route(e,function(i){var s=r._extractParameters(e,i);n&&n.apply(r,s),r.trigger.apply(r,["route:"+t].concat(s)),r.trigger("route",t,s),o.history.trigger("route",r,t,s)}),this},navigate:function(e,t){return o.history.navigate(e,t),this},_bindRoutes:function(){if(!this.routes)return;this.routes=u.result(this,"routes");var e,t=u.keys(this.routes);while((e=t.pop())!=null)this.route(e,this.routes[e])},_routeToRegExp:function(e){return e=e.replace(A,"\\$&").replace(C,"(?:$1)?").replace(k,function(e,t){return t?e:"([^/]+)"}).replace(L,"(.*?)"),new RegExp("^"+e+"$")},_extractParameters:function(e,t){var n=e.exec(t).slice(1);return u.map(n,function(e){return e?decodeURIComponent(e):null})}});var O=o.History=function(){this.handlers=[],u.bindAll(this,"checkUrl"),typeof window!="undefined"&&(this.location=window.location,this.history=window.history)},M=/^[#\/]|\s+$/g,_=/^\/+|\/+$/g,D=/msie [\w.]+/,P=/\/$/;O.started=!1,u.extend(O.prototype,a,{interval:50,getHash:function(e){var t=(e||this).location.href.match(/#(.*)$/);return t?t[1]:""},getFragment:function(e,t){if(e==null)if(this._hasPushState||!this._wantsHashChange||t){e=this.location.pathname;var n=this.root.replace(P,"");e.indexOf(n)||(e=e.substr(n.length))}else e=this.getHash();return e.replace(M,"")},start:function(e){if(O.started)throw new Error("Backbone.history has already been started");O.started=!0,this.options=u.extend({},{root:"/"},this.options,e),this.root=this.options.root,this._wantsHashChange=this.options.hashChange!==!1,this._wantsPushState=!!this.options.pushState,this._hasPushState=!!(this.options.pushState&&this.history&&this.history.pushState);var t=this.getFragment(),n=document.documentMode,r=D.exec(navigator.userAgent.toLowerCase())&&(!n||n<=7);this.root=("/"+this.root+"/").replace(_,"/"),r&&this._wantsHashChange&&(this.iframe=o.$('<iframe src="javascript:0" tabindex="-1" />').hide().appendTo("body")[0].contentWindow,this.navigate(t)),this._hasPushState?o.$(window).on("popstate",this.checkUrl):this._wantsHashChange&&"onhashchange"in window&&!r?o.$(window).on("hashchange",this.checkUrl):this._wantsHashChange&&(this._checkUrlInterval=setInterval(this.checkUrl,this.interval)),this.fragment=t;var i=this.location,s=i.pathname.replace(/[^\/]$/,"$&/")===this.root;if(this._wantsHashChange&&this._wantsPushState&&!this._hasPushState&&!s)return this.fragment=this.getFragment(null,!0),this.location.replace(this.root+this.location.search+"#"+this.fragment),!0;this._wantsPushState&&this._hasPushState&&s&&i.hash&&(this.fragment=this.getHash().replace(M,""),this.history.replaceState({},document.title,this.root+this.fragment+i.search));if(!this.options.silent)return this.loadUrl()},stop:function(){o.$(window).off("popstate",this.checkUrl).off("hashchange",this.checkUrl),clearInterval(this._checkUrlInterval),O.started=!1},route:function(e,t){this.handlers.unshift({route:e,callback:t})},checkUrl:function(e){var t=this.getFragment();t===this.fragment&&this.iframe&&(t=this.getFragment(this.getHash(this.iframe)));if(t===this.fragment)return!1;this.iframe&&this.navigate(t),this.loadUrl()||this.loadUrl(this.getHash())},loadUrl:function(e){var t=this.fragment=this.getFragment(e),n=u.any(this.handlers,function(e){if(e.route.test(t))return e.callback(t),!0});return n},navigate:function(e,t){if(!O.started)return!1;if(!t||t===!0)t={trigger:t};e=this.getFragment(e||"");if(this.fragment===e)return;this.fragment=e;var n=this.root+e;if(this._hasPushState)this.history[t.replace?"replaceState":"pushState"]({},document.title,n);else{if(!this._wantsHashChange)return this.location.assign(n);this._updateHash(this.location,e,t.replace),this.iframe&&e!==this.getFragment(this.getHash(this.iframe))&&(t.replace||this.iframe.document.open().close(),this._updateHash(this.iframe.location,e,t.replace))}t.trigger&&this.loadUrl(e)},_updateHash:function(e,t,n){if(n){var r=e.href.replace(/(javascript:|#).*$/,"");e.replace(r+"#"+t)}else e.hash="#"+t}}),o.history=new O;var H=function(e,t){var n=this,r;e&&u.has(e,"constructor")?r=e.constructor:r=function(){return n.apply(this,arguments)},u.extend(r,n,t);var i=function(){this.constructor=r};return i.prototype=n.prototype,r.prototype=new i,e&&u.extend(r.prototype,e),r.__super__=n.prototype,r};p.extend=m.extend=N.extend=E.extend=O.extend=H;var B=function(){throw new Error('A "url" property or function must be specified')},j=function(e,t){var n=t.error;t.error=function(r){n&&n(e,r,t),e.trigger("error",e,r,t)}}}.call(this),define("backbone",["underscore","jquery"],function(e){return function(){var t,n;return t||e.Backbone}}(this)),define("filter",["jquery","moment","icheck","underscore","backbone","mustache","hash"],function(e,t,n,r,i,s,o){function u(t){this.defaults={url_get_filter_service:"calendar/generateDynamicFilterFromService.htm",url_get_filter_activity:"calendar/generateDynamicFilterFromActivity.htm",url_get_all_filter:"calendar/findLatestFilter.htm",url_get_stored_filter:"findStoredFilter.htm",EL:{$filterBar:e(".filter-list"),$customerInfo:e(".customer-info")}},this.cache={serviceCache:{},instructorCache:{},roomCache:{},activityCache:{}},this.init(t)}return u.prototype={init:function(t){this.conf=e.extend({},this.defaults,t);var n=this,r=n.conf,s=r.EL,o=s.$filterBar,u,a;u=i.Model.extend({initialize:function(){var t=this,i=t.formEL;i.serviceTypeChoose.iCheck(),i.serviceType.iCheck(),i.instructorChoose.iCheck(),i.instructor.iCheck(),i.roomChoose.iCheck(),i.room.iCheck(),i.appointmentTypeChoose.iCheck(),i.appointmentType.iCheck();var s=n.cache,u=s.instructorCache,a=s.roomCache,f=s.activityCache;o.find(".js-serviceType-filter").on("ifClicked","input[type=checkbox]",function(t){var s=e(this),u;s.iCheck("toggle"),u=this.checked,setTimeout(function(){s.hasClass("js-filter")?u?o.find('input[name="serviceType"]').iCheck("check"):o.find('input[name="serviceType"]').iCheck("uncheck"):o.find(".js-serviceType-filter li").not(".main-filter").find("input:not(:checked)").length>0?i.serviceTypeChoose.iCheck("uncheck"):i.serviceTypeChoose.iCheck("check"),n.renderFilter(r.url_get_filter_service)},300)}),o.find(".js-appointmentType-filter").on("ifClicked","input[type=checkbox]",function(t){var s=e(this),u;s.iCheck("toggle"),u=this.checked;var a=t.currentTarget.value;u?f[a]=!0:f[a]=!1,setTimeout(function(){s.hasClass("js-filter")?u?(o.find('input[name="appointmentType"]').each(function(){var t=e(this).val();f[t]=!0}),o.find('input[name="appointmentType"]').iCheck("check")):(o.find('input[name="appointmentType"]').each(function(){var t=e(this).val();f[t]=!1}),o.find('input[name="appointmentType"]').iCheck("uncheck")):o.find(".js-appointmentType-filter li").not(".main-filter").find("input:not(:checked)").length>0?i.appointmentTypeChoose.iCheck("uncheck"):i.appointmentTypeChoose.iCheck("check"),n.renderFilter(r.url_get_filter_activity)},300)}),o.find(".js-instructor-filter").on("ifClicked","input[type=checkbox]",function(t){var r=e(this),s;r.iCheck("toggle"),s=this.checked;var a=e(this).val();s?u[a]=!0:u[a]=!1,setTimeout(function(){r.hasClass("js-filter")?s?(o.find('input[name="instructor"]').each(function(){u[e(this).val()]=!0}),o.find('input[name="instructor"]').iCheck("check")):(o.find('input[name="instructor"]').each(function(){u[e(this).val()]=!1}),o.find('input[name="instructor"]').iCheck("uncheck")):o.find(".js-instructor-filter li").not(".main-filter").find("input:not(:checked)").length>0?i.instructorChoose.iCheck("uncheck"):i.instructorChoose.iCheck("check"),n.renderCalendar()},300)}),o.find(".js-room-filter").on("ifClicked","input[type=checkbox]",function(t){var r=e(this),s;r.iCheck("toggle"),s=this.checked;var u=t.currentTarget.value;s?a[u]=!0:a[u]=!1,setTimeout(function(){r.hasClass("js-filter")?s?(o.find('input[name="room"]').each(function(){var t=e(this).val();a[t]=!0}),o.find('input[name="room"]').iCheck("check")):(o.find('input[name="room"]').each(function(){var t=e(this).val();a[t]=!1}),o.find('input[name="room"]').iCheck("uncheck")):o.find(".js-room-filter li").not(".main-filter").find("input:not(:checked)").length>0?i.roomChoose.iCheck("uncheck"):i.roomChoose.iCheck("check"),n.renderCalendar()},300)}),o.show()},defaults:{},formEL:{serviceTypeChoose:o.find('input[name="servie-choose"]'),serviceType:o.find('input[name="serviceType"]'),instructorChoose:o.find('input[name="instructor-choose"]'),instructor:o.find('input[name="instructor"]'),roomChoose:o.find('input[name="room-choose"]'),room:o.find('input[name="room"]'),appointmentTypeChoose:o.find('input[name="appointment-choose"]'),appointmentType:o.find('input[name="appointmentType"]')},getSeriveType:function(){var e=[];return o.find('input[name="serviceType"]:checked').each(function(){e.push(this.value)}),e.toString()},getInstructor:function(){var e=[];return o.find('input[name="instructor"]:checked').each(function(){e.push(this.value)}),e.toString()},getRoom:function(){var e=[];return o.find('input[name="room"]:checked').each(function(){e.push(this.value)}),e.toString()},getAppointmentType:function(){var e=[];return o.find('input[name="appointmentType"]:checked').each(function(){e.push(this.value)}),e.toString()},getUnSelectedServiceType:function(){var e=[];return o.find('input[name="serviceType"]:not(:checked)').each(function(){e.push(this.value)}),e.toString()},getUnSelectedActivityType:function(){var e=[];return o.find('input[name="appointmentType"]:not(:checked)').each(function(){e.push(this.value)}),e.toString()},getUnselectedRoom:function(){var e=[];return o.find('input[name="room"]:not(:checked)').each(function(){e.push(this.value)}),e.toString()},getUnselectedInstructor:function(){var e=[];return o.find('input[name="instructor"]:not(:checked)').each(function(){e.push(this.value)}),e.toString()},getUnSelectedActivityFromCache:function(){var t=n.cache,r=t.activityCache,i=[];return e.each(r,function(e,t){t||i.push(e)}),i.toString()}}),n.filter_model=new u},initFilter:function(t,n){var r=this,i=r.conf,s=i.EL.$filterBar;e.ajax({url:i.url_get_stored_filter,data:r.getFilter(),success:function(e){typeof e=="string"&&e.indexOf("DOCTYPE")!==-1&&location.reload();var i="",o="",u="",a="",f=r.cache,l=f.serviceCache,c=f.instructorCache,h=f.roomCache,p=f.activityCache,d=0;e.serviceList.forEach(function(e){var t=e.serviceId;undefined===l[t]&&(l[t]=!0),e.isSelectedService==="Y"?(d+=1,i+='<li><input name="serviceType" checked type="checkbox" data-name="'+e.serviceName+'" value="'+e.serviceId+'">'+e.serviceName+"</li>"):i+='<li><input name="serviceType" type="checkbox" data-name="'+e.serviceName+'" value="'+e.serviceId+'">'+e.serviceName+"</li>"}),d===e.serviceList.length&&s.find('input[name="servie-choose"]').iCheck("check"),s.find(".js-serviceType-filter li").first().nextAll().remove().end().after(i).end().nextAll().iCheck();var v=0;e.instructorList.forEach(function(e){var t=e.instructorId;undefined===c[t]&&(c[t]=!0),e.isSelectedInstructor==="Y"?(v+=1,o+='<li><input name="instructor" checked type="checkbox" data-name="'+e.instructorName+'" value="'+e.instructorId+'">'+e.instructorName+"</li>"):o+='<li><input name="instructor" type="checkbox" data-name="'+e.instructorName+'" value="'+e.instructorId+'">'+e.instructorName+"</li>"}),v===e.instructorList.length&&s.find('input[name="instructor-choose"]').iCheck("check"),s.find(".js-instructor-filter li").first().nextAll().remove().end().after(o).end().nextAll().iCheck();if(e.activityList){var m=0;e.activityList.forEach(function(e){var t=e.activityId;undefined===p[t]&&(p[t]=!0),e.isSelectedActivity==="Y"?(m+=1,u+='<li><input name="appointmentType" checked type="checkbox" data-name="'+e.activityName+'" value="'+e.activityId+'">'+e.activityName+"</li>"):u+='<li><input name="appointmentType" type="checkbox" data-name="'+e.activityName+'" value="'+e.activityId+'">'+e.activityName+"</li>"}),m===e.activityList.length&&s.find('input[name="appointment-choose"]').iCheck("check"),s.find(".js-appointmentType-filter li").first().nextAll().remove().end().after(u).end().nextAll().iCheck()}var g=0;e.roomList.forEach(function(e){var t=e.roomId;undefined===h[t]&&(h[t]=!0),e.isSelectedRoom==="Y"?(g+=1,a+='<li><input name="room" checked type="checkbox" data-name="'+e.profileRoomName+'" value="'+e.roomId+'">'+e.profileRoomName+"</li>"):a+='<li><input name="room" type="checkbox" data-name="'+e.profileRoomName+'" value="'+e.roomId+'">'+e.profileRoomName+"</li>"}),g===e.roomList.length&&s.find('input[name="room-choose"]').iCheck("check"),s.find(".js-room-filter li").first().nextAll().remove().end().after(a).end().nextAll().iCheck(),r.renderCalendar(t,n)}})},renderFilter:function(t){var n=this,r=n.conf,i=r.EL.$filterBar,s=n.cache,o=s.serviceCache,u=s.instructorCache,a=s.roomCache,f=s.activityCache;e.ajax({url:t,type:"GET",data:n.getFilter(),success:function(e){typeof e=="string"&&e.indexOf("DOCTYPE")!==-1&&location.reload();var t="",r="",s="";e.instructorDTOs.forEach(function(e){u[e.instructorId]?t+='<li><input name="instructor" checked type="checkbox" data-name="'+e.instructorName+'" value="'+e.instructorId+'">'+e.instructorName+"</li>":t+='<li><input name="instructor" type="checkbox" data-name="'+e.instructorName+'" value="'+e.instructorId+'">'+e.instructorName+"</li>"}),u.on?i.find(".js-instructor-filter li").first().iCheck("check"):i.find(".js-instructor-filter li").first().iCheck("uncheck"),i.find(".js-instructor-filter li").first().nextAll().remove().end().after(t).end().nextAll().iCheck(),i.find(".js-instructor-filter input:not(:first):not(:checked)").length===0?i.find(".js-instructor-filter li").first().iCheck("check"):i.find(".js-instructor-filter li").first().iCheck("uncheck"),e.activityDTOs&&(e.activityDTOs.forEach(function(e){f[e.activityId]?r+='<li><input name="appointmentType" checked type="checkbox" data-name="'+e.activityName+'" value="'+e.activityId+'">'+e.activityName+"</li>":r+='<li><input name="appointmentType" type="checkbox" data-name="'+e.activityName+'" value="'+e.activityId+'">'+e.activityName+"</li>"}),i.find(".js-appointmentType-filter li").first().nextAll().remove().end().after(r).end().nextAll().iCheck()),f.on?i.find(".js-appointmentType-filter li").first().iCheck("check"):i.find(".js-appointmentType-filter li").first().iCheck("uncheck"),i.find(".js-appointmentType-filter input:not(:first):not(:checked)").length===0?i.find(".js-appointmentType-filter li").first().iCheck("check"):i.find(".js-appointmentType-filter li").first().iCheck("uncheck"),e.roomDTOs.forEach(function(e){a[e.roomId]?s+='<li><input name="room" checked type="checkbox" data-name="'+e.profileRoomName+'" value="'+e.roomId+'">'+e.profileRoomName+"</li>":s+='<li><input name="room" type="checkbox" data-name="'+e.profileRoomName+'" value="'+e.roomId+'">'+e.profileRoomName+"</li>"}),i.find(".js-room-filter li").first().nextAll().remove().end().after(s).end().nextAll().iCheck(),a.on?i.find(".js-room-filter li").first().iCheck("check"):i.find(".js-room-filter li").first().iCheck("uncheck"),i.find(".js-room-filter input:not(:first):not(:checked)").length===0?i.find(".js-room-filter li").first().iCheck("check"):i.find(".js-room-filter li").first().iCheck("uncheck"),n.renderCalendar()}})},renderCalendar:function(n,r){var i=this,s=i.conf,u=o.get("date")||t().format("L"),n=undefined===n?o.get("view"):n,a=undefined===r?o.get("filter"):r,f=i.getFilter();e(".cal-item-disabled").remove(),s.handler(u,n,f,a)},getFilter:function(){var e=this,t={};return t={instructors:e.filter_model.getInstructor(),rooms:e.filter_model.getRoom(),services:e.filter_model.getSeriveType(),appointments:e.filter_model.getAppointmentType(),unselectedServices:e.filter_model.getUnSelectedServiceType(),unselectedActivities:e.filter_model.getUnSelectedActivityType(),unselectedRooms:e.filter_model.getUnselectedRoom(),unselectedInstructors:e.filter_model.getUnselectedInstructor(),unselectedActivitiesFromCache:e.filter_model.getUnSelectedActivityFromCache()},t},hide:function(){var e=this,t=e.conf.EL,n=t.$filterBar,r=t.$customerInfo;n.hide(),r.show()},show:function(){var e=this,t=e.conf.EL,n=t.$filterBar,r=t.$customerInfo;r.hide(),n.show()}},u}),define("appointment",["jquery","moment","mustache"],function(e,t,n){function r(e){this.defaults={},this.init(e)}return r.prototype={init:function(t){this.conf=e.extend({},this.defaults,t)},showAll:function(r,i,s,o,u,a,f){var l=this,c=6;switch(u){case"day":(o===null||o.length===0)&&e(".cal-item").remove();var h=e(".cal-title th");e.each(o,function(n,s){var o="";e.each(s.customers,function(e,t){var n=(t.person.firstName===null?"":t.person.firstName)+" "+(t.person.lastName===null?"":t.person.lastName);e<s.customers.length-1?o+=n+"("+t.lessonCounts+")":o+=n+"("+t.lessonCounts+")"}),o+="<br/>"+(s.activity.activityName||"");if(s.activity.activityId===120||s.activity.activityId===140||s.activity.activityId===320){o=s.activity.activityName;if(s.activity.activityId===140||s.activity.activityId===320)E="#90EE90",S="1px solid #90EE90"}var u,l,p,d,v=t(s.startTimeStr),m=h.eq(1).outerWidth(),g=h.eq(1).outerHeight(),y=h.eq(0).outerWidth(),b=v.hours(),w=v.minutes(),E="#339900",S="1px solid #1E8643";if(s.appointmentSeries.isRecurring.toString()==="Y"){E="#4987c4",S="1px solid #4782bb",s.canceled==="H"&&(E="#103961",S="1px solid #091e33"),s.activity.service.serviceId=="20"&&(E="#ff7f00",S="1px solid #da463b",s.canceled==="H"&&(E="#103961",S="1px solid #091e33"));if(s.customers[0]!==undefined){s.customers[0].lessonCounts=="0"&&(E="#FFCA33",S="1px solid #e6e600");if(s.activity.activityId===120||s.activity.activityId===320)E="#90EE90",S="1px solid #90EE90"}}if(s.appointmentSeries.isRecurring.toString()==="N"&&s.activity.service.serviceId=="20"){E="#ba55d3",S="1px solid #da463b",s.canceled==="H"&&(E="#103961",S="1px solid #091e33");if(s.customers[0]!==undefined){s.customers[0].lessonCounts=="0"&&(E="#FFCA33",S="1px solid #e6e600");if(s.activity.activityId===140||s.activity.activityId===320)E="#90EE90",S="1px solid #90EE90"}}else if(s.appointmentSeries.isRecurring.toString()==="N"){E="#339900",S="1px solid #1E8643",s.canceled==="H"&&(E="#103961",S="1px solid #091e33");if(s.customers[0]!==undefined){s.customers[0].lessonCounts=="0"&&(E="#FFCA33",S="1px solid #e6e600");if(s.activity.activityId===140||s.activity.activityId===320)E="#90EE90",S="1px solid #90EE90"}}s.isUpdatedByCustomer===!0&&(E="#643b5b",S="1px solid #704266",s.customers[0]!==undefined&&s.customers[0].lessonCounts=="0"&&(E="#FFCA33",S="1px solid #e6e600"));if(s.activity.activityId===20||s.activity.activityId===120||s.activity.activityId===684||s.activity.activityId===360||s.activity.activityId===400||s.activity.activityId===401){E="#d20105",S="1px solid #da463b";if(s.customers[0]!==undefined){s.customers[0].lessonCounts=="0"&&(E="#FFCA33",S="1px solid #e6e600");if(s.activity.activityId===140||s.activity.activityId===320)E="#90EE90",S="1px solid #90EE90"}}if(f==="room"||f===""){var x=h.index(e("#room_"+s.room.roomId));u=y+m*(x-1)}else{var T=s.instructor;if(T){var x=h.index(e("#instructor_"+s.instructor.instructorId));u=y+m*(x-1)}else u="-100%"}l=g+60+(b-c)*60+w,p=m,d=s.duration;if(r){var N=r.length;s.instructor&&N>3&&N<6&&s.instructor.instructorId!=r[0].instructorId&&(s.instructor.instructorId===r[1].instructorId?u=u*1+1:u=u*1+2)}else if(i){var N=i.length;N>3&&N<6&&s.room.roomId!=i[0].roomId&&(s.room.roomId===i[1].roomId?u=u*1+1:u=u*1+2)}var C=e('<div class="cal-item"><p></p></div>'),k='<div style="height:'+d+'px">'+o+"</div>";C.data("appointmentid",s.appointmentId).css({background:E,border:S,position:"abusolute",top:l,left:u,width:p,height:d}).find("p").append(k),a.append(C)});if(s===undefined||s===null||s.length===0){e(".cal-item-disabled").remove();return}e.each(s,function(n,s){var o,u,l,p,d=t(s.startTimeStr),v=h.eq(1).outerWidth(),m=h.eq(1).outerHeight(),g=h.eq(0).outerWidth(),y=d.hours(),b=d.minutes();if(f==="room"||f===""){var w=h.index(e("#room_"+s.room.roomId));o=g+v*(w-1)}else{var E=s.instructor;if(E){var w=h.index(e("#instructor_"+s.instructor.instructorId));o=g+v*(w-1)}else o="-100%"}u=m+60+(y-c)*60+b,l=v,p=s.duration;if(r){var S=r.length;s.instructor&&S>3&&S<6&&s.instructor.instructorId!=r[0].instructorId&&(s.instructor.instructorId===r[1].instructorId?o=o*1+1:o=o*1+2)}else if(i){var S=i.length;S>3&&S<6&&s.room.roomId!=i[0].roomId&&(s.room.roomId===i[1].roomId?o=o*1+1:o=o*1+2)}var x=e('<div class="cal-item-disabled"></div>');x.css({position:"abusolute",top:u,left:o,width:l,height:p}),a.append(x)});break;case"week":for(var p in o){var d=o[p];for(var v in d){var m=d[v];for(var g=0;g<m.length;g++){var y=m[g],b=(v*1-c)*60+110+t(y.startTime).format("mm")*1,w,E,S,x=95,T=50,N=0;for(var C=0;C<m.length;C++){var k=m[C];if((k.startTime>y.startTime||k.startTime==y.startTime)&&k.startTime<y.endTime||k.endTime>y.startTime&&(k.endTime<y.endTime||k.endTime==y.endTime)||(y.startTime>k.startTime||y.startTime==k.startTime)&&(y.endTime<k.endTime||y.endTime==k.endTime))N+=1}N>1?(E=x/m.length,w=50+g*E+p*95+2):(E=x,w=50+p*95+2),S=y.duration;var L="",A="#339900",O="1px solid #1E8643";y.appointmentSeries.isRecurring.toString()==="Y"&&(A="#4987c4",O="1px solid #4782bb",y.canceled==="H"&&(A="#103961",O="1px solid #091e33"),y.activity.service.serviceId=="20"&&(A="#ff7f00",O="1px solid #da463b",y.canceled==="H"&&(A="#103961",O="1px solid #091e33")),y.customers.lessonCounts=="0"&&(A="#FFCA33",O="1px solid #e6e600")),y.appointmentSeries.isRecurring.toString()==="N"&&y.activity.service.serviceId=="20"?(A="#ba55d3",O="1px solid #da463b",y.canceled==="H"&&(A="#103961",O="1px solid #091e33"),y.customers.lessonCounts=="0"&&(A="#FFCA33")):y.appointmentSeries.isRecurring.toString()==="N"&&(A="#339900",O="1px solid #1E8643",y.canceled==="H"&&(A="#103961",O="1px solid #091e33"),y.customers.lessonCounts=="0"&&(A="#FFCA33",O="1px solid #e6e600")),y.isUpdatedByCustomer===!0&&(A="#643b5b",O="1px solid #704266",y.customers.lessonCounts=="0"&&(A="#FFCA33",O="1px solid #e6e600"));if(y.activity.activityId===20||y.activity.activityId===120||y.activity.activityId===684||y.activity.activityId===360||y.activity.activityId===400||y.activity.activityId===401){A="#d20105",O="1px solid #da463b",y.customers.lessonCounts=="0"&&(A="#FFCA33",O="1px solid #e6e600");if(y.activity.activityId===140||y.activity.activityId===320)A="#90EE90",O="1px solid #90EE90"}e.each(y.customers,function(e,t){var n=(t.person.firstName===null?"":t.person.firstName)+" "+(t.person.lastName===null?"":t.person.lastName);e<y.customers.length-1?(L+=n+"("+t.lessonCounts+")"+",",t.lessonCounts=="0"&&(A="#FFCA33",O="1px solid #e6e600")):(L+=n+"("+t.lessonCounts+")",t.lessonCounts=="0"&&(A="#FFCA33",O="1px solid #e6e600"))}),L+="<br/>"+(y.activity.activityName||"");if(y.activity.activityId===120||y.activity.activityId===140||y.activity.activityId===320)L=y.activity.activityName;if(y.activity.activityId===140||y.activity.activityId===320)A="#90EE90",O="1px solid #90EE90";var M=e('<div class="cal-item week"></div>'),_=n.render('<p><div style="height:'+S+'px">'+L+"</div></p>",y);M.data("appointmentid",y.appointmentId).css({background:A,border:O,top:b,left:w,width:E,height:S}).append(_),a.append(M)}}}break;case"month":}}},r}),function(e){e.fn.each2===void 0&&e.fn.extend({each2:function(t){for(var n=e([0]),r=-1,i=this.length;i>++r&&(n.context=n[0]=this[r])&&t.call(n[0],r,n)!==!1;);return this}})}(jQuery),function(e,t){function n(e,t){for(var n=0,r=t.length;r>n;n+=1)if(i(e,t[n]))return n;return-1}function r(){var t=e(j);t.appendTo("body");var n={width:t.width()-t[0].clientWidth,height:t.height()-t[0].clientHeight};return t.remove(),n}function i(e,n){return e===n?!0:e===t||n===t?!1:null===e||null===n?!1:e.constructor===String?e+""==n+"":n.constructor===String?n+""==e+"":!1}function s(t,n){var r,i,s;if(null===t||1>t.length)return[];for(r=t.split(n),i=0,s=r.length;s>i;i+=1)r[i]=e.trim(r[i]);return r}function o(e){return e.outerWidth(!1)-e.width()}function u(n){var r="keyup-change-value";n.on("keydown",function(){e.data(n,r)===t&&e.data(n,r,n.val())}),n.on("keyup",function(){var i=e.data(n,r);i!==t&&n.val()!==i&&(e.removeData(n,r),n.trigger("keyup-change"))})}function a(n){n.on("mousemove",function(n){var r=B;(r===t||r.x!==n.pageX||r.y!==n.pageY)&&e(n.target).trigger("mousemove-filtered",n)})}function f(e,n,r){r=r||t;var i;return function(){var t=arguments;window.clearTimeout(i),i=window.setTimeout(function(){n.apply(r,t)},e)}}function l(e){var t,n=!1;return function(){return n===!1&&(t=e(),n=!0),t}}function c(e,t){var r=f(e,function(e){t.trigger("scroll-debounced",e)});t.on("scroll",function(e){n(e.target,t.get())>=0&&r(e)})}function h(e){e[0]!==document.activeElement&&window.setTimeout(function(){var t,n=e[0],r=e.val().length;e.focus(),e.is(":visible")&&n===document.activeElement&&(n.setSelectionRange?n.setSelectionRange(r,r):n.createTextRange&&(t=n.createTextRange(),t.collapse(!1),t.select()))},0)}function p(t){t=e(t)[0];var n=0,r=0;if("selectionStart"in t)n=t.selectionStart,r=t.selectionEnd-n;else if("selection"in document){t.focus();var i=document.selection.createRange();r=document.selection.createRange().text.length,i.moveStart("character",-t.value.length),n=i.text.length-r}return{offset:n,length:r}}function d(e){e.preventDefault(),e.stopPropagation()}function v(e){e.preventDefault(),e.stopImmediatePropagation()}function m(t){if(!D){var n=t[0].currentStyle||window.getComputedStyle(t[0],null);D=e(document.createElement("div")).css({position:"absolute",left:"-10000px",top:"-10000px",display:"none",fontSize:n.fontSize,fontFamily:n.fontFamily,fontStyle:n.fontStyle,fontWeight:n.fontWeight,letterSpacing:n.letterSpacing,textTransform:n.textTransform,whiteSpace:"nowrap"}),D.attr("class","select2-sizer"),e("body").append(D)}return D.text(t.val()),D.width()}function g(t,n,r){var i,s,o=[];i=t.attr("class"),i&&(i=""+i,e(i.split(" ")).each2(function(){0===this.indexOf("select2-")&&o.push(this)})),i=n.attr("class"),i&&(i=""+i,e(i.split(" ")).each2(function(){0!==this.indexOf("select2-")&&(s=r(this),s&&o.push(this))})),t.attr("class",o.join(" "))}function y(e,n,r,i){var s=e.toUpperCase().indexOf(n.toUpperCase()),o=n.length;return 0>s?(r.push(i(e)),t):(r.push(i(e.substring(0,s))),r.push("<span class='select2-match'>"),r.push(i(e.substring(s,s+o))),r.push("</span>"),r.push(i(e.substring(s+o,e.length))),t)}function b(e){var t={"\\":"&#92;","&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#47;"};return(e+"").replace(/[&<>"'\/\\]/g,function(e){return t[e]})}function w(n){var r,i=0,s=null,o=n.quietMillis||100,u=n.url,a=this;return function(f){window.clearTimeout(r),r=window.setTimeout(function(){i+=1;var r=i,o=n.data,l=u,c=n.transport||e.fn.select2.ajaxDefaults.transport,h={type:n.type||"GET",cache:n.cache||!1,jsonpCallback:n.jsonpCallback||t,dataType:n.dataType||"json"},p=e.extend({},e.fn.select2.ajaxDefaults.params,h);o=o?o.call(a,f.term,f.page,f.context):null,l="function"==typeof l?l.call(a,f.term,f.page,f.context):l,s&&s.abort(),n.params&&(e.isFunction(n.params)?e.extend(p,n.params.call(a)):e.extend(p,n.params)),e.extend(p,{url:l,dataType:n.dataType,data:o,success:function(e){if(!(i>r)){var t=n.results(e,f.page);f.callback(t)}}}),s=c.call(a,p)},o)}}function E(n){var r,i,s=n,o=function(e){return""+e.text};e.isArray(s)&&(i=s,s={results:i}),e.isFunction(s)===!1&&(i=s,s=function(){return i});var u=s();return u.text&&(o=u.text,e.isFunction(o)||(r=u.text,o=function(e){return e[r]})),function(n){var r,i=n.term,u={results:[]};return""===i?(n.callback(s()),t):(r=function(t,s){var u,a;if(t=t[0],t.children){u={};for(a in t)t.hasOwnProperty(a)&&(u[a]=t[a]);u.children=[],e(t.children).each2(function(e,t){r(t,u.children)}),(u.children.length||n.matcher(i,o(u),t))&&s.push(u)}else n.matcher(i,o(t),t)&&s.push(t)},e(s().results).each2(function(e,t){r(t,u.results)}),n.callback(u),t)}}function S(n){var r=e.isFunction(n);return function(i){var s=i.term,o={results:[]};e(r?n():n).each(function(){var e=this.text!==t,n=e?this.text:this;(""===s||i.matcher(s,n))&&o.results.push(e?this:{id:this,text:this})}),i.callback(o)}}function x(t,n){if(e.isFunction(t))return!0;if(!t)return!1;throw Error(n+" must be a function or a falsy value")}function T(t){return e.isFunction(t)?t():t}function N(t){var n=0;return e.each(t,function(e,t){t.children?n+=N(t.children):n++}),n}function C(e,n,r,s){var o,u,a,f,l,c=e,h=!1;if(!s.createSearchChoice||!s.tokenSeparators||1>s.tokenSeparators.length)return t;for(;;){for(u=-1,a=0,f=s.tokenSeparators.length;f>a&&(l=s.tokenSeparators[a],u=e.indexOf(l),!(u>=0));a++);if(0>u)break;if(o=e.substring(0,u),e=e.substring(u+l.length),o.length>0&&(o=s.createSearchChoice.call(this,o,n),o!==t&&null!==o&&s.id(o)!==t&&null!==s.id(o))){for(h=!1,a=0,f=n.length;f>a;a++)if(i(s.id(o),s.id(n[a]))){h=!0;break}h||r(o)}}return c!==e?e:t}function k(t,n){var r=function(){};return r.prototype=new t,r.prototype.constructor=r,r.prototype.parent=t.prototype,r.prototype=e.extend(r.prototype,n),r}if(window.Select2===t){var L,A,O,M,_,D,P,H,B={x:0,y:0},L={TAB:9,ENTER:13,ESC:27,SPACE:32,LEFT:37,UP:38,RIGHT:39,DOWN:40,SHIFT:16,CTRL:17,ALT:18,PAGE_UP:33,PAGE_DOWN:34,HOME:36,END:35,BACKSPACE:8,DELETE:46,isArrow:function(e){switch(e=e.which?e.which:e){case L.LEFT:case L.RIGHT:case L.UP:case L.DOWN:return!0}return!1},isControl:function(e){var t=e.which;switch(t){case L.SHIFT:case L.CTRL:case L.ALT:return!0}return e.metaKey?!0:!1},isFunctionKey:function(e){return e=e.which?e.which:e,e>=112&&123>=e}},j="<div class='select2-measure-scrollbar'></div>";P=e(document),_=function(){var e=1;return function(){return e++}}(),P.on("mousemove",function(e){B.x=e.pageX,B.y=e.pageY}),A=k(Object,{bind:function(e){var t=this;return function(){e.apply(t,arguments)}},init:function(n){var i,s,o,f,h=".select2-results";this.opts=n=this.prepareOpts(n),this.id=n.id,n.element.data("select2")!==t&&null!==n.element.data("select2")&&n.element.data("select2").destroy(),this.container=this.createContainer(),this.containerId="s2id_"+(n.element.attr("id")||"autogen"+_()),this.containerSelector="#"+this.containerId.replace(/([;&,\.\+\*\~':"\!\^#$%@\[\]\(\)=>\|])/g,"\\$1"),this.container.attr("id",this.containerId),this.body=l(function(){return n.element.closest("body")}),g(this.container,this.opts.element,this.opts.adaptContainerCssClass),this.container.css(T(n.containerCss)),this.container.addClass(T(n.containerCssClass)),this.elementTabIndex=this.opts.element.attr("tabindex"),this.opts.element.data("select2",this).attr("tabindex","-1").before(this.container),this.container.data("select2",this),this.dropdown=this.container.find(".select2-drop"),this.dropdown.addClass(T(n.dropdownCssClass)),this.dropdown.data("select2",this),this.results=i=this.container.find(h),this.search=s=this.container.find("input.select2-input"),this.resultsPage=0,this.context=null,this.initContainer(),a(this.results),this.dropdown.on("mousemove-filtered touchstart touchmove touchend",h,this.bind(this.highlightUnderEvent)),c(80,this.results),this.dropdown.on("scroll-debounced",h,this.bind(this.loadMoreIfNeeded)),e(this.container).on("change",".select2-input",function(e){e.stopPropagation()}),e(this.dropdown).on("change",".select2-input",function(e){e.stopPropagation()}),e.fn.mousewheel&&i.mousewheel(function(e,t,n,r){var s=i.scrollTop();r>0&&0>=s-r?(i.scrollTop(0),d(e)):0>r&&i.get(0).scrollHeight-i.scrollTop()+r<=i.height()&&(i.scrollTop(i.get(0).scrollHeight-i.height()),d(e))}),u(s),s.on("keyup-change input paste",this.bind(this.updateResults)),s.on("focus",function(){s.addClass("select2-focused")}),s.on("blur",function(){s.removeClass("select2-focused")}),this.dropdown.on("mouseup",h,this.bind(function(t){e(t.target).closest(".select2-result-selectable").length>0&&(this.highlightUnderEvent(t),this.selectHighlighted(t))})),this.dropdown.on("click mouseup mousedown",function(e){e.stopPropagation()}),e.isFunction(this.opts.initSelection)&&(this.initSelection(),this.monitorSource()),null!==n.maximumInputLength&&this.search.attr("maxlength",n.maximumInputLength);var o=n.element.prop("disabled");o===t&&(o=!1),this.enable(!o);var f=n.element.prop("readonly");f===t&&(f=!1),this.readonly(f),H=H||r(),this.autofocus=n.element.prop("autofocus"),n.element.prop("autofocus",!1),this.autofocus&&this.focus()},destroy:function(){var e=this.opts.element,n=e.data("select2");this.propertyObserver&&(delete this.propertyObserver,this.propertyObserver=null),n!==t&&(n.container.remove(),n.dropdown.remove(),e.removeClass("select2-offscreen").removeData("select2").off(".select2").prop("autofocus",this.autofocus||!1),this.elementTabIndex?e.attr({tabindex:this.elementTabIndex}):e.removeAttr("tabindex"),e.show())},optionToData:function(e){return e.is("option")?{id:e.prop("value"),text:e.text(),element:e.get(),css:e.attr("class"),disabled:e.prop("disabled"),locked:i(e.attr("locked"),"locked")||i(e.data("locked"),!0)}:e.is("optgroup")?{text:e.attr("label"),children:[],element:e.get(),css:e.attr("class")}:t},prepareOpts:function(n){var r,o,u,a,f=this;if(r=n.element,"select"===r.get(0).tagName.toLowerCase()&&(this.select=o=n.element),o&&e.each(["id","multiple","ajax","query","createSearchChoice","initSelection","data","tags"],function(){if(this in n)throw Error("Option '"+this+"' is not allowed for Select2 when attached to a <select> element.")}),n=e.extend({},{populateResults:function(r,i,s){var o,u=this.opts.id;o=function(r,i,a){var l,c,h,p,d,v,m,g,y,b;for(r=n.sortResults(r,i,s),l=0,c=r.length;c>l;l+=1)h=r[l],d=h.disabled===!0,p=!d&&u(h)!==t,v=h.children&&h.children.length>0,m=e("<li></li>"),m.addClass("select2-results-dept-"+a),m.addClass("select2-result"),m.addClass(p?"select2-result-selectable":"select2-result-unselectable"),d&&m.addClass("select2-disabled"),v&&m.addClass("select2-result-with-children"),m.addClass(f.opts.formatResultCssClass(h)),g=e(document.createElement("div")),g.addClass("select2-result-label"),b=n.formatResult(h,g,s,f.opts.escapeMarkup),b!==t&&g.html(b),m.append(g),v&&(y=e("<ul></ul>"),y.addClass("select2-result-sub"),o(h.children,y,a+1),m.append(y)),m.data("select2-data",h),i.append(m)},o(i,r,0)}},e.fn.select2.defaults,n),"function"!=typeof n.id&&(u=n.id,n.id=function(e){return e[u]}),e.isArray(n.element.data("select2Tags"))){if("tags"in n)throw"tags specified as both an attribute 'data-select2-tags' and in options of Select2 "+n.element.attr("id");n.tags=n.element.data("select2Tags")}if(o?(n.query=this.bind(function(e){var n,i,s,o={results:[],more:!1},u=e.term;s=function(t,n){var r;t.is("option")?e.matcher(u,t.text(),t)&&n.push(f.optionToData(t)):t.is("optgroup")&&(r=f.optionToData(t),t.children().each2(function(e,t){s(t,r.children)}),r.children.length>0&&n.push(r))},n=r.children(),this.getPlaceholder()!==t&&n.length>0&&(i=this.getPlaceholderOption(),i&&(n=n.not(i))),n.each2(function(e,t){s(t,o.results)}),e.callback(o)}),n.id=function(e){return e.id},n.formatResultCssClass=function(e){return e.css}):"query"in n||("ajax"in n?(a=n.element.data("ajax-url"),a&&a.length>0&&(n.ajax.url=a),n.query=w.call(n.element,n.ajax)):"data"in n?n.query=E(n.data):"tags"in n&&(n.query=S(n.tags),n.createSearchChoice===t&&(n.createSearchChoice=function(e){return{id:e,text:e}}),n.initSelection===t&&(n.initSelection=function(r,o){var u=[];e(s(r.val(),n.separator)).each(function(){var r=this,s=this,o=n.tags;e.isFunction(o)&&(o=o()),e(o).each(function(){return i(this.id,r)?(s=this.text,!1):t}),u.push({id:r,text:s})}),o(u)}))),"function"!=typeof n.query)throw"query function not defined for Select2 "+n.element.attr("id");return n},monitorSource:function(){var e,n=this.opts.element;n.on("change.select2",this.bind(function(){this.opts.element.data("select2-change-triggered")!==!0&&this.initSelection()})),e=this.bind(function(){var e,r=n.prop("disabled");r===t&&(r=!1),this.enable(!r);var e=n.prop("readonly");e===t&&(e=!1),this.readonly(e),g(this.container,this.opts.element,this.opts.adaptContainerCssClass),this.container.addClass(T(this.opts.containerCssClass)),g(this.dropdown,this.opts.element,this.opts.adaptDropdownCssClass),this.dropdown.addClass(T(this.opts.dropdownCssClass))}),n.on("propertychange.select2 DOMAttrModified.select2",e),this.mutationCallback===t&&(this.mutationCallback=function(t){t.forEach(e)}),"undefined"!=typeof WebKitMutationObserver&&(this.propertyObserver&&(delete this.propertyObserver,this.propertyObserver=null),this.propertyObserver=new WebKitMutationObserver(this.mutationCallback),this.propertyObserver.observe(n.get(0),{attributes:!0,subtree:!1}))},triggerSelect:function(t){var n=e.Event("select2-selecting",{val:this.id(t),object:t});return this.opts.element.trigger(n),!n.isDefaultPrevented()},triggerChange:function(t){t=t||{},t=e.extend({},t,{type:"change",val:this.val()}),this.opts.element.data("select2-change-triggered",!0),this.opts.element.trigger(t),this.opts.element.data("select2-change-triggered",!1),this.opts.element.click(),this.opts.blurOnChange&&this.opts.element.blur()},isInterfaceEnabled:function(){return this.enabledInterface===!0},enableInterface:function(){var e=this._enabled&&!this._readonly,t=!e;return e===this.enabledInterface?!1:(this.container.toggleClass("select2-container-disabled",t),this.close(),this.enabledInterface=e,!0)},enable:function(e){return e===t&&(e=!0),this._enabled===e?!1:(this._enabled=e,this.opts.element.prop("disabled",!e),this.enableInterface(),!0)},readonly:function(e){return e===t&&(e=!1),this._readonly===e?!1:(this._readonly=e,this.opts.element.prop("readonly",e),this.enableInterface(),!0)},opened:function(){return this.container.hasClass("select2-dropdown-open")},positionDropdown:function(){var t,n,r,i,s=this.dropdown,o=this.container.offset(),u=this.container.outerHeight(!1),a=this.container.outerWidth(!1),f=s.outerHeight(!1),l=e(window).scrollLeft()+e(window).width(),c=e(window).scrollTop()+e(window).height(),h=o.top+u,p=o.left,d=c>=h+f,v=o.top-f>=this.body().scrollTop(),m=s.outerWidth(!1),g=l>=p+m,y=s.hasClass("select2-drop-above");this.opts.dropdownAutoWidth?(i=e(".select2-results",s)[0],s.addClass("select2-drop-auto-width"),s.css("width",""),m=s.outerWidth(!1)+(i.scrollHeight===i.clientHeight?0:H.width),m>a?a=m:m=a,g=l>=p+m):this.container.removeClass("select2-drop-auto-width"),"static"!==this.body().css("position")&&(t=this.body().offset(),h-=t.top,p-=t.left),y?(n=!0,!v&&d&&(n=!1)):(n=!1,!d&&v&&(n=!0)),g||(p=o.left+a-m),n?(h=o.top-f,this.container.addClass("select2-drop-above"),s.addClass("select2-drop-above")):(this.container.removeClass("select2-drop-above"),s.removeClass("select2-drop-above")),r=e.extend({top:h,left:p,width:a},T(this.opts.dropdownCss)),s.css(r)},shouldOpen:function(){var t;return this.opened()?!1:this._enabled===!1||this._readonly===!0?!1:(t=e.Event("select2-opening"),this.opts.element.trigger(t),!t.isDefaultPrevented())},clearDropdownAlignmentPreference:function(){this.container.removeClass("select2-drop-above"),this.dropdown.removeClass("select2-drop-above")},open:function(){return this.shouldOpen()?(this.opening(),!0):!1},opening:function(){function t(){return{width:Math.max(document.documentElement.scrollWidth,e(window).width()),height:Math.max(document.documentElement.scrollHeight,e(window).height())}}var n,r,i=this.containerId,s="scroll."+i,o="resize."+i,u="orientationchange."+i;this.container.addClass("select2-dropdown-open").addClass("select2-container-active"),this.clearDropdownAlignmentPreference(),this.dropdown[0]!==this.body().children().last()[0]&&this.dropdown.detach().appendTo(this.body()),n=e("#select2-drop-mask"),0==n.length&&(n=e(document.createElement("div")),n.attr("id","select2-drop-mask").attr("class","select2-drop-mask"),n.hide(),n.appendTo(this.body()),n.on("mousedown touchstart click",function(t){var n,r=e("#select2-drop");r.length>0&&(n=r.data("select2"),n.opts.selectOnBlur&&n.selectHighlighted({noFocus:!0}),n.close(),t.preventDefault(),t.stopPropagation())})),this.dropdown.prev()[0]!==n[0]&&this.dropdown.before(n),e("#select2-drop").removeAttr("id"),this.dropdown.attr("id","select2-drop"),r=t(),n.css(r).show(),this.dropdown.show(),this.positionDropdown(),this.dropdown.addClass("select2-drop-active");var a=this;this.container.parents().add(window).each(function(){e(this).on(o+" "+s+" "+u,function(){var n=t();e("#select2-drop-mask").css(n),a.positionDropdown()})})},close:function(){if(this.opened()){var t=this.containerId,n="scroll."+t,r="resize."+t,i="orientationchange."+t;this.container.parents().add(window).each(function(){e(this).off(n).off(r).off(i)}),this.clearDropdownAlignmentPreference(),e("#select2-drop-mask").hide(),this.dropdown.removeAttr("id"),this.dropdown.hide(),this.container.removeClass("select2-dropdown-open"),this.results.empty(),this.clearSearch(),this.search.removeClass("select2-active"),this.opts.element.trigger(e.Event("select2-close"))}},externalSearch:function(e){this.open(),this.search.val(e),this.updateResults(!1)},clearSearch:function(){},getMaximumSelectionSize:function(){return T(this.opts.maximumSelectionSize)},ensureHighlightVisible:function(){var n,r,i,s,o,u,a,f=this.results;if(r=this.highlight(),!(0>r)){if(0==r)return f.scrollTop(0),t;n=this.findHighlightableChoices().find(".select2-result-label"),i=e(n[r]),s=i.offset().top+i.outerHeight(!0),r===n.length-1&&(a=f.find("li.select2-more-results"),a.length>0&&(s=a.offset().top+a.outerHeight(!0))),o=f.offset().top+f.outerHeight(!0),s>o&&f.scrollTop(f.scrollTop()+(s-o)),u=i.offset().top-f.offset().top,0>u&&"none"!=i.css("display")&&f.scrollTop(f.scrollTop()+u)}},findHighlightableChoices:function(){return this.results.find(".select2-result-selectable:not(.select2-selected):not(.select2-disabled)")},moveHighlight:function(t){for(var n=this.findHighlightableChoices(),r=this.highlight();r>-1&&n.length>r;){r+=t;var i=e(n[r]);if(i.hasClass("select2-result-selectable")&&!i.hasClass("select2-disabled")&&!i.hasClass("select2-selected")){this.highlight(r);break}}},highlight:function(r){var i,s,o=this.findHighlightableChoices();return 0===arguments.length?n(o.filter(".select2-highlighted")[0],o.get()):(r>=o.length&&(r=o.length-1),0>r&&(r=0),this.results.find(".select2-highlighted").removeClass("select2-highlighted"),i=e(o[r]),i.addClass("select2-highlighted"),this.ensureHighlightVisible(),s=i.data("select2-data"),s&&this.opts.element.trigger({type:"select2-highlight",val:this.id(s),choice:s}),t)},countSelectableResults:function(){return this.findHighlightableChoices().length},highlightUnderEvent:function(t){var n=e(t.target).closest(".select2-result-selectable");if(n.length>0&&!n.is(".select2-highlighted")){var r=this.findHighlightableChoices();this.highlight(r.index(n))}else 0==n.length&&this.results.find(".select2-highlighted").removeClass("select2-highlighted")},loadMoreIfNeeded:function(){var e,t=this.results,n=t.find("li.select2-more-results"),r=this.resultsPage+1,i=this,s=this.search.val(),o=this.context;0!==n.length&&(e=n.offset().top-t.offset().top-t.height(),this.opts.loadMorePadding>=e&&(n.addClass("select2-active"),this.opts.query({element:this.opts.element,term:s,page:r,context:o,matcher:this.opts.matcher,callback:this.bind(function(e){i.opened()&&(i.opts.populateResults.call(this,t,e.results,{term:s,page:r,context:o}),i.postprocessResults(e,!1,!1),e.more===!0?(n.detach().appendTo(t).text(i.opts.formatLoadMore(r+1)),window.setTimeout(function(){i.loadMoreIfNeeded()},10)):n.remove(),i.positionDropdown(),i.resultsPage=r,i.context=e.context)})})))},tokenize:function(){},updateResults:function(n){function r(){a.removeClass("select2-active"),c.positionDropdown()}function s(e){f.html(e),r()}var o,u,a=this.search,f=this.results,l=this.opts,c=this,h=a.val(),p=e.data(this.container,"select2-last-term");if((n===!0||!p||!i(h,p))&&(e.data(this.container,"select2-last-term",h),n===!0||this.showSearchInput!==!1&&this.opened())){var d=this.getMaximumSelectionSize();if(d>=1&&(o=this.data(),e.isArray(o)&&o.length>=d&&x(l.formatSelectionTooBig,"formatSelectionTooBig")))return s("<li class='select2-selection-limit'>"+l.formatSelectionTooBig(d)+"</li>"),t;if(a.val().length<l.minimumInputLength)return x(l.formatInputTooShort,"formatInputTooShort")?s("<li class='select2-no-results'>"+l.formatInputTooShort(a.val(),l.minimumInputLength)+"</li>"):s(""),n&&this.showSearch&&this.showSearch(!0),t;if(l.maximumInputLength&&a.val().length>l.maximumInputLength)return x(l.formatInputTooLong,"formatInputTooLong")?s("<li class='select2-no-results'>"+l.formatInputTooLong(a.val(),l.maximumInputLength)+"</li>"):s(""),t;l.formatSearching&&0===this.findHighlightableChoices().length&&s("<li class='select2-searching'>"+l.formatSearching()+"</li>"),a.addClass("select2-active"),u=this.tokenize(),u!=t&&null!=u&&a.val(u),this.resultsPage=1,l.query({element:l.element,term:a.val(),page:this.resultsPage,context:null,matcher:l.matcher,callback:this.bind(function(o){var u;return this.opened()?(this.context=o.context===t?null:o.context,this.opts.createSearchChoice&&""!==a.val()&&(u=this.opts.createSearchChoice.call(c,a.val(),o.results),u!==t&&null!==u&&c.id(u)!==t&&null!==c.id(u)&&0===e(o.results).filter(function(){return i(c.id(this),c.id(u))}).length&&o.results.unshift(u)),0===o.results.length&&x(l.formatNoMatches,"formatNoMatches")?(s("<li class='select2-no-results'>"+l.formatNoMatches(a.val())+"</li>"),t):(f.empty(),c.opts.populateResults.call(this,f,o.results,{term:a.val(),page:this.resultsPage,context:null}),o.more===!0&&x(l.formatLoadMore,"formatLoadMore")&&(f.append("<li class='select2-more-results'>"+c.opts.escapeMarkup(l.formatLoadMore(this.resultsPage))+"</li>"),window.setTimeout(function(){c.loadMoreIfNeeded()},10)),this.postprocessResults(o,n),r(),this.opts.element.trigger({type:"select2-loaded",items:o}),t)):(this.search.removeClass("select2-active"),t)})})}},cancel:function(){this.close()},blur:function(){this.opts.selectOnBlur&&this.selectHighlighted({noFocus:!0}),this.close(),this.container.removeClass("select2-container-active"),this.search[0]===document.activeElement&&this.search.blur(),this.clearSearch(),this.selection.find(".select2-search-choice-focus").removeClass("select2-search-choice-focus")},focusSearch:function(){h(this.search)},selectHighlighted:function(e){var t=this.highlight(),n=this.results.find(".select2-highlighted"),r=n.closest(".select2-result").data("select2-data");r?(this.highlight(t),this.onSelect(r,e)):e&&e.noFocus&&this.close()},getPlaceholder:function(){var e;return this.opts.element.attr("placeholder")||this.opts.element.attr("data-placeholder")||this.opts.element.data("placeholder")||this.opts.placeholder||((e=this.getPlaceholderOption())!==t?e.text():t)},getPlaceholderOption:function(){if(this.select){var e=this.select.children().first();if(this.opts.placeholderOption!==t)return"first"===this.opts.placeholderOption&&e||"function"==typeof this.opts.placeholderOption&&this.opts.placeholderOption(this.select);if(""===e.text()&&""===e.val())return e}},initContainerWidth:function(){function n(){var n,r,i,s,o;if("off"===this.opts.width)return null;if("element"===this.opts.width)return 0===this.opts.element.outerWidth(!1)?"auto":this.opts.element.outerWidth(!1)+"px";if("copy"===this.opts.width||"resolve"===this.opts.width){if(n=this.opts.element.attr("style"),n!==t)for(r=n.split(";"),s=0,o=r.length;o>s;s+=1)if(i=r[s].replace(/\s/g,"").match(/width:(([-+]?([0-9]*\.)?[0-9]+)(px|em|ex|%|in|cm|mm|pt|pc))/i),null!==i&&i.length>=1)return i[1];return"resolve"===this.opts.width?(n=this.opts.element.css("width"),n.indexOf("%")>0?n:0===this.opts.element.outerWidth(!1)?"auto":this.opts.element.outerWidth(!1)+"px"):null}return e.isFunction(this.opts.width)?this.opts.width():this.opts.width}var r=n.call(this);null!==r&&this.container.css("width",r)}}),O=k(A,{createContainer:function(){var t=e(document.createElement("div")).attr({"class":"select2-container"}).html(["<a href='javascript:void(0)' onclick='return false;' class='select2-choice' tabindex='-1'>","   <span class='select2-chosen'>&nbsp;</span><abbr class='select2-search-choice-close'></abbr>","   <span class='select2-arrow'><b></b></span>","</a>","<input class='select2-focusser select2-offscreen' type='text'/>","<div class='select2-drop select2-display-none'>","   <div class='select2-search'>","       <input type='text' autocomplete='off' autocorrect='off' autocapitalize='off' spellcheck='false' class='select2-input'/>","   </div>","   <ul class='select2-results'>","   </ul>","</div>"].join(""));return t},enableInterface:function(){this.parent.enableInterface.apply(this,arguments)&&this.focusser.prop("disabled",!this.isInterfaceEnabled())},opening:function(){var t,n,r;this.opts.minimumResultsForSearch>=0&&this.showSearch(!0),this.parent.opening.apply(this,arguments),this.showSearchInput!==!1&&this.search.val(this.focusser.val()),this.search.focus(),t=this.search.get(0),t.createTextRange?(n=t.createTextRange(),n.collapse(!1),n.select()):t.setSelectionRange&&(r=this.search.val().length,t.setSelectionRange(r,r)),this.focusser.prop("disabled",!0).val(""),this.updateResults(!0),this.opts.element.trigger(e.Event("select2-open"))},close:function(){this.opened()&&(this.parent.close.apply(this,arguments),this.focusser.removeAttr("disabled"),this.focusser.focus())},focus:function(){this.opened()?this.close():(this.focusser.removeAttr("disabled"),this.focusser.focus())},isFocused:function(){return this.container.hasClass("select2-container-active")},cancel:function(){this.parent.cancel.apply(this,arguments),this.focusser.removeAttr("disabled"),this.focusser.focus()},initContainer:function(){var n,r=this.container,i=this.dropdown;0>this.opts.minimumResultsForSearch?this.showSearch(!1):this.showSearch(!0),this.selection=n=r.find(".select2-choice"),this.focusser=r.find(".select2-focusser"),this.focusser.attr("id","s2id_autogen"+_()),e("label[for='"+this.opts.element.attr("id")+"']").attr("for",this.focusser.attr("id")),this.focusser.attr("tabindex",this.elementTabIndex),this.search.on("keydown",this.bind(function(e){if(this.isInterfaceEnabled()){if(e.which===L.PAGE_UP||e.which===L.PAGE_DOWN)return d(e),t;switch(e.which){case L.UP:case L.DOWN:return this.moveHighlight(e.which===L.UP?-1:1),d(e),t;case L.ENTER:return this.selectHighlighted(),d(e),t;case L.TAB:return this.selectHighlighted({noFocus:!0}),t;case L.ESC:return this.cancel(e),d(e),t}}})),this.search.on("blur",this.bind(function(){document.activeElement===this.body().get(0)&&window.setTimeout(this.bind(function(){this.search.focus()}),0)})),this.focusser.on("keydown",this.bind(function(e){if(this.isInterfaceEnabled()&&e.which!==L.TAB&&!L.isControl(e)&&!L.isFunctionKey(e)&&e.which!==L.ESC){if(this.opts.openOnEnter===!1&&e.which===L.ENTER)return d(e),t;if(e.which==L.DOWN||e.which==L.UP||e.which==L.ENTER&&this.opts.openOnEnter){if(e.altKey||e.ctrlKey||e.shiftKey||e.metaKey)return;return this.open(),d(e),t}return e.which==L.DELETE||e.which==L.BACKSPACE?(this.opts.allowClear&&this.clear(),d(e),t):t}})),u(this.focusser),this.focusser.on("keyup-change input",this.bind(function(e){if(this.opts.minimumResultsForSearch>=0){if(e.stopPropagation(),this.opened())return;this.open()}})),n.on("mousedown","abbr",this.bind(function(e){this.isInterfaceEnabled()&&(this.clear(),v(e),this.close(),this.selection.focus())})),n.on("mousedown",this.bind(function(t){this.container.hasClass("select2-container-active")||this.opts.element.trigger(e.Event("select2-focus")),this.opened()?this.close():this.isInterfaceEnabled()&&this.open(),d(t)})),i.on("mousedown",this.bind(function(){this.search.focus()})),n.on("focus",this.bind(function(e){d(e)})),this.focusser.on("focus",this.bind(function(){this.container.hasClass("select2-container-active")||this.opts.element.trigger(e.Event("select2-focus")),this.container.addClass("select2-container-active")})).on("blur",this.bind(function(){this.opened()||(this.container.removeClass("select2-container-active"),this.opts.element.trigger(e.Event("select2-blur")))})),this.search.on("focus",this.bind(function(){this.container.hasClass("select2-container-active")||this.opts.element.trigger(e.Event("select2-focus")),this.container.addClass("select2-container-active")})),this.initContainerWidth(),this.opts.element.addClass("select2-offscreen"),this.setPlaceholder()},clear:function(e){var t=this.selection.data("select2-data");if(t){var n=this.getPlaceholderOption();this.opts.element.val(n?n.val():""),this.selection.find(".select2-chosen").empty(),this.selection.removeData("select2-data"),this.setPlaceholder(),e!==!1&&(this.opts.element.trigger({type:"select2-removed",val:this.id(t),choice:t}),this.triggerChange({removed:t}))}},initSelection:function(){if(this.isPlaceholderOptionSelected())this.updateSelection([]),this.close(),this.setPlaceholder();else{var e=this;this.opts.initSelection.call(null,this.opts.element,function(n){n!==t&&null!==n&&(e.updateSelection(n),e.close(),e.setPlaceholder())})}},isPlaceholderOptionSelected:function(){var e;return(e=this.getPlaceholderOption())!==t&&e.is(":selected")||""===this.opts.element.val()||this.opts.element.val()===t||null===this.opts.element.val()},prepareOpts:function(){var t=this.parent.prepareOpts.apply(this,arguments),n=this;return"select"===t.element.get(0).tagName.toLowerCase()?t.initSelection=function(e,t){var r=e.find(":selected");t(n.optionToData(r))}:"data"in t&&(t.initSelection=t.initSelection||function(n,r){var s=n.val(),o=null;t.query({matcher:function(e,n,r){var u=i(s,t.id(r));return u&&(o=r),u},callback:e.isFunction(r)?function(){r(o)}:e.noop})}),t},getPlaceholder:function(){return this.select&&this.getPlaceholderOption()===t?t:this.parent.getPlaceholder.apply(this,arguments)},setPlaceholder:function(){var e=this.getPlaceholder();if(this.isPlaceholderOptionSelected()&&e!==t){if(this.select&&this.getPlaceholderOption()===t)return;this.selection.find(".select2-chosen").html(this.opts.escapeMarkup(e)),this.selection.addClass("select2-default"),this.container.removeClass("select2-allowclear")}},postprocessResults:function(e,n,r){var s=0,o=this;if(this.findHighlightableChoices().each2(function(e,n){return i(o.id(n.data("select2-data")),o.opts.element.val())?(s=e,!1):t}),r!==!1&&(n===!0&&s>=0?this.highlight(s):this.highlight(0)),n===!0){var u=this.opts.minimumResultsForSearch;u>=0&&this.showSearch(N(e.results)>=u)}},showSearch:function(t){this.showSearchInput!==t&&(this.showSearchInput=t,this.dropdown.find(".select2-search").toggleClass("select2-search-hidden",!t),this.dropdown.find(".select2-search").toggleClass("select2-offscreen",!t),e(this.dropdown,this.container).toggleClass("select2-with-searchbox",t))},onSelect:function(e,t){if(this.triggerSelect(e)){var n=this.opts.element.val(),r=this.data();this.opts.element.val(this.id(e)),this.updateSelection(e),this.opts.element.trigger({type:"select2-selected",val:this.id(e),choice:e}),this.close(),t&&t.noFocus||this.selection.focus(),i(n,this.id(e))||this.triggerChange({added:e,removed:r})}},updateSelection:function(e){var n,r,i=this.selection.find(".select2-chosen");this.selection.data("select2-data",e),i.empty(),n=this.opts.formatSelection(e,i,this.opts.escapeMarkup),n!==t&&i.append(n),r=this.opts.formatSelectionCssClass(e,i),r!==t&&i.addClass(r),this.selection.removeClass("select2-default"),this.opts.allowClear&&this.getPlaceholder()!==t&&this.container.addClass("select2-allowclear")},val:function(){var e,n=!1,r=null,i=this,s=this.data();if(0===arguments.length)return this.opts.element.val();if(e=arguments[0],arguments.length>1&&(n=arguments[1]),this.select)this.select.val(e).find(":selected").each2(function(e,t){return r=i.optionToData(t),!1}),this.updateSelection(r),this.setPlaceholder(),n&&this.triggerChange({added:r,removed:s});else{if(!e&&0!==e)return this.clear(n),t;if(this.opts.initSelection===t)throw Error("cannot call val() if initSelection() is not defined");this.opts.element.val(e),this.opts.initSelection(this.opts.element,function(e){i.opts.element.val(e?i.id(e):""),i.updateSelection(e),i.setPlaceholder(),n&&i.triggerChange({added:e,removed:s})})}},clearSearch:function(){this.search.val(""),this.focusser.val("")},data:function(e,n){var r;return 0===arguments.length?(r=this.selection.data("select2-data"),r==t&&(r=null),r):(e&&""!==e?(r=this.data(),this.opts.element.val(e?this.id(e):""),this.updateSelection(e),n&&this.triggerChange({added:e,removed:r})):this.clear(n),t)}}),M=k(A,{createContainer:function(){var t=e(document.createElement("div")).attr({"class":"select2-container select2-container-multi"}).html(["<ul class='select2-choices'>","  <li class='select2-search-field'>","    <input type='text' autocomplete='off' autocorrect='off' autocapitilize='off' spellcheck='false' class='select2-input'>","  </li>","</ul>","<div class='select2-drop select2-drop-multi select2-display-none'>","   <ul class='select2-results'>","   </ul>","</div>"].join(""));return t},prepareOpts:function(){var t=this.parent.prepareOpts.apply(this,arguments),n=this;return"select"===t.element.get(0).tagName.toLowerCase()?t.initSelection=function(e,t){var r=[];e.find(":selected").each2(function(e,t){r.push(n.optionToData(t))}),t(r)}:"data"in t&&(t.initSelection=t.initSelection||function(n,r){var o=s(n.val(),t.separator),u=[];t.query({matcher:function(n,r,s){var a=e.grep(o,function(e){return i(e,t.id(s))}).length;return a&&u.push(s),a},callback:e.isFunction(r)?function(){for(var e=[],n=0;o.length>n;n++)for(var s=o[n],a=0;u.length>a;a++){var f=u[a];if(i(s,t.id(f))){e.push(f),u.splice(a,1);break}}r(e)}:e.noop})}),t},selectChoice:function(e){var t=this.container.find(".select2-search-choice-focus");t.length&&e&&e[0]==t[0]||(t.length&&this.opts.element.trigger("choice-deselected",t),t.removeClass("select2-search-choice-focus"),e&&e.length&&(this.close(),e.addClass("select2-search-choice-focus"),this.opts.element.trigger("choice-selected",e)))},initContainer:function(){var n,r=".select2-choices";this.searchContainer=this.container.find(".select2-search-field"),this.selection=n=this.container.find(r);var i=this;this.selection.on("mousedown",".select2-search-choice",function(){i.search[0].focus(),i.selectChoice(e(this))}),this.search.attr("id","s2id_autogen"+_()),e("label[for='"+this.opts.element.attr("id")+"']").attr("for",this.search.attr("id")),this.search.on("input paste",this.bind(function(){this.isInterfaceEnabled()&&(this.opened()||this.open())})),this.search.attr("tabindex",this.elementTabIndex),this.keydowns=0,this.search.on("keydown",this.bind(function(e){if(this.isInterfaceEnabled()){++this.keydowns;var r=n.find(".select2-search-choice-focus"),i=r.prev(".select2-search-choice:not(.select2-locked)"),s=r.next(".select2-search-choice:not(.select2-locked)"),o=p(this.search);if(!(!r.length||e.which!=L.LEFT&&e.which!=L.RIGHT&&e.which!=L.BACKSPACE&&e.which!=L.DELETE&&e.which!=L.ENTER)){var u=r;return e.which==L.LEFT&&i.length?u=i:e.which==L.RIGHT?u=s.length?s:null:e.which===L.BACKSPACE?(this.unselect(r.first()),this.search.width(10),u=i.length?i:s):e.which==L.DELETE?(this.unselect(r.first()),this.search.width(10),u=s.length?s:null):e.which==L.ENTER&&(u=null),this.selectChoice(u),d(e),u&&u.length||this.open(),t}if((e.which===L.BACKSPACE&&1==this.keydowns||e.which==L.LEFT)&&0==o.offset&&!o.length)return this.selectChoice(n.find(".select2-search-choice:not(.select2-locked)").last()),d(e),t;if(this.selectChoice(null),this.opened())switch(e.which){case L.UP:case L.DOWN:return this.moveHighlight(e.which===L.UP?-1:1),d(e),t;case L.ENTER:return this.selectHighlighted(),d(e),t;case L.TAB:return this.selectHighlighted({noFocus:!0}),this.close(),t;case L.ESC:return this.cancel(e),d(e),t}if(e.which!==L.TAB&&!L.isControl(e)&&!L.isFunctionKey(e)&&e.which!==L.BACKSPACE&&e.which!==L.ESC){if(e.which===L.ENTER){if(this.opts.openOnEnter===!1)return;if(e.altKey||e.ctrlKey||e.shiftKey||e.metaKey)return}this.open(),(e.which===L.PAGE_UP||e.which===L.PAGE_DOWN)&&d(e),e.which===L.ENTER&&d(e)}}})),this.search.on("keyup",this.bind(function(){this.keydowns=0,this.resizeSearch()})),this.search.on("blur",this.bind(function(t){this.container.removeClass("select2-container-active"),this.search.removeClass("select2-focused"),this.selectChoice(null),this.opened()||this.clearSearch(),t.stopImmediatePropagation(),this.opts.element.trigger(e.Event("select2-blur"))})),this.container.on("click",r,this.bind(function(t){this.isInterfaceEnabled()&&(e(t.target).closest(".select2-search-choice").length>0||(this.selectChoice(null),this.clearPlaceholder(),this.container.hasClass("select2-container-active")||this.opts.element.trigger(e.Event("select2-focus")),this.open(),this.focusSearch(),t.preventDefault()))})),this.container.on("focus",r,this.bind(function(){this.isInterfaceEnabled()&&(this.container.hasClass("select2-container-active")||this.opts.element.trigger(e.Event("select2-focus")),this.container.addClass("select2-container-active"),this.dropdown.addClass("select2-drop-active"),this.clearPlaceholder())})),this.initContainerWidth(),this.opts.element.addClass("select2-offscreen"),this.clearSearch()},enableInterface:function(){this.parent.enableInterface.apply(this,arguments)&&this.search.prop("disabled",!this.isInterfaceEnabled())},initSelection:function(){if(""===this.opts.element.val()&&""===this.opts.element.text()&&(this.updateSelection([]),this.close(),this.clearSearch()),this.select||""!==this.opts.element.val()){var e=this;this.opts.initSelection.call(null,this.opts.element,function(n){n!==t&&null!==n&&(e.updateSelection(n),e.close(),e.clearSearch())})}},clearSearch:function(){var e=this.getPlaceholder(),n=this.getMaxSearchWidth();e!==t&&0===this.getVal().length&&this.search.hasClass("select2-focused")===!1?(this.search.val(e).addClass("select2-default"),this.search.width(n>0?n:this.container.css("width"))):this.search.val("").width(10)},clearPlaceholder:function(){this.search.hasClass("select2-default")&&this.search.val("").removeClass("select2-default")},opening:function(){this.clearPlaceholder(),this.resizeSearch(),this.parent.opening.apply(this,arguments),this.focusSearch(),this.updateResults(!0),this.search.focus(),this.opts.element.trigger(e.Event("select2-open"))},close:function(){this.opened()&&this.parent.close.apply(this,arguments)},focus:function(){this.close(),this.search.focus()},isFocused:function(){return this.search.hasClass("select2-focused")},updateSelection:function(t){var r=[],i=[],s=this;e(t).each(function(){0>n(s.id(this),r)&&(r.push(s.id(this)),i.push(this))}),t=i,this.selection.find(".select2-search-choice").remove(),e(t).each(function(){s.addSelectedChoice(this)}),s.postprocessResults()},tokenize:function(){var e=this.search.val();e=this.opts.tokenizer.call(this,e,this.data(),this.bind(this.onSelect),this.opts),null!=e&&e!=t&&(this.search.val(e),e.length>0&&this.open())},onSelect:function(e,t){this.triggerSelect(e)&&(this.addSelectedChoice(e),this.opts.element.trigger({type:"selected",val:this.id(e),choice:e}),(this.select||!this.opts.closeOnSelect)&&this.postprocessResults(),this.opts.closeOnSelect?(this.close(),this.search.width(10)):this.countSelectableResults()>0?(this.search.width(10),this.resizeSearch(),this.getMaximumSelectionSize()>0&&this.val().length>=this.getMaximumSelectionSize()&&this.updateResults(!0),this.positionDropdown()):(this.close(),this.search.width(10)),this.triggerChange({added:e}),t&&t.noFocus||this.focusSearch())},cancel:function(){this.close(),this.focusSearch()},addSelectedChoice:function(n){var r,i,s=!n.locked,o=e("<li class='select2-search-choice'>    <div></div>    <a href='#' onclick='return false;' class='select2-search-choice-close' tabindex='-1'></a></li>"),u=e("<li class='select2-search-choice select2-locked'><div></div></li>"),a=s?o:u,f=this.id(n),l=this.getVal();r=this.opts.formatSelection(n,a.find("div"),this.opts.escapeMarkup),r!=t&&a.find("div").replaceWith("<div>"+r+"</div>"),i=this.opts.formatSelectionCssClass(n,a.find("div")),i!=t&&a.addClass(i),s&&a.find(".select2-search-choice-close").on("mousedown",d).on("click dblclick",this.bind(function(t){this.isInterfaceEnabled()&&(e(t.target).closest(".select2-search-choice").fadeOut("fast",this.bind(function(){this.unselect(e(t.target)),this.selection.find(".select2-search-choice-focus").removeClass("select2-search-choice-focus"),this.close(),this.focusSearch()})).dequeue(),d(t))})).on("focus",this.bind(function(){this.isInterfaceEnabled()&&(this.container.addClass("select2-container-active"),this.dropdown.addClass("select2-drop-active"))})),a.data("select2-data",n),a.insertBefore(this.searchContainer),l.push(f),this.setVal(l)},unselect:function(e){var t,r,i=this.getVal();if(e=e.closest(".select2-search-choice"),0===e.length)throw"Invalid argument: "+e+". Must be .select2-search-choice";t=e.data("select2-data"),t&&(r=n(this.id(t),i),r>=0&&(i.splice(r,1),this.setVal(i),this.select&&this.postprocessResults()),e.remove(),this.opts.element.trigger({type:"removed",val:this.id(t),choice:t}),this.triggerChange({removed:t}))},postprocessResults:function(e,t,r){var i=this.getVal(),s=this.results.find(".select2-result"),o=this.results.find(".select2-result-with-children"),u=this;s.each2(function(e,t){var r=u.id(t.data("select2-data"));n(r,i)>=0&&(t.addClass("select2-selected"),t.find(".select2-result-selectable").addClass("select2-selected"))}),o.each2(function(e,t){t.is(".select2-result-selectable")||0!==t.find(".select2-result-selectable:not(.select2-selected)").length||t.addClass("select2-selected")}),-1==this.highlight()&&r!==!1&&u.highlight(0),!this.opts.createSearchChoice&&!s.filter(".select2-result:not(.select2-selected)").length>0&&(!e||e&&!e.more&&0===this.results.find(".select2-no-results").length)&&x(u.opts.formatNoMatches,"formatNoMatches")&&this.results.append("<li class='select2-no-results'>"+u.opts.formatNoMatches(u.search.val())+"</li>")},getMaxSearchWidth:function(){return this.selection.width()-o(this.search)},resizeSearch:function(){var e,t,n,r,i,s=o(this.search);e=m(this.search)+10,t=this.search.offset().left,n=this.selection.width(),r=this.selection.offset().left,i=n-(t-r)-s,e>i&&(i=n-s),40>i&&(i=n-s),0>=i&&(i=e),this.search.width(i)},getVal:function(){var e;return this.select?(e=this.select.val(),null===e?[]:e):(e=this.opts.element.val(),s(e,this.opts.separator))},setVal:function(t){var r;this.select?this.select.val(t):(r=[],e(t).each(function(){0>n(this,r)&&r.push(this)}),this.opts.element.val(0===r.length?"":r.join(this.opts.separator)))},buildChangeDetails:function(e,t){for(var t=t.slice(0),e=e.slice(0),n=0;t.length>n;n++)for(var r=0;e.length>r;r++)i(this.opts.id(t[n]),this.opts.id(e[r]))&&(t.splice(n,1),n--,e.splice(r,1),r--);return{added:t,removed:e}},val:function(n,r){var i,s=this;if(0===arguments.length)return this.getVal();if(i=this.data(),i.length||(i=[]),!n&&0!==n)return this.opts.element.val(""),this.updateSelection([]),this.clearSearch(),r&&this.triggerChange({added:this.data(),removed:i}),t;if(this.setVal(n),this.select)this.opts.initSelection(this.select,this.bind(this.updateSelection)),r&&this.triggerChange(this.buildChangeDetails(i,this.data()));else{if(this.opts.initSelection===t)throw Error("val() cannot be called if initSelection() is not defined");this.opts.initSelection(this.opts.element,function(t){var n=e.map(t,s.id);s.setVal(n),s.updateSelection(t),s.clearSearch(),r&&s.triggerChange(this.buildChangeDetails(i,this.data()))})}this.clearSearch()},onSortStart:function(){if(this.select)throw Error("Sorting of elements is not supported when attached to <select>. Attach to <input type='hidden'/> instead.");this.search.width(0),this.searchContainer.hide()},onSortEnd:function(){var t=[],n=this;this.searchContainer.show(),this.searchContainer.appendTo(this.searchContainer.parent()),this.resizeSearch(),this.selection.find(".select2-search-choice").each(function(){t.push(n.opts.id(e(this).data("select2-data")))}),this.setVal(t),this.triggerChange()},data:function(n,r){var i,s,o=this;return 0===arguments.length?this.selection.find(".select2-search-choice").map(function(){return e(this).data("select2-data")}).get():(s=this.data(),n||(n=[]),i=e.map(n,function(e){return o.opts.id(e)}),this.setVal(i),this.updateSelection(n),this.clearSearch(),r&&this.triggerChange(this.buildChangeDetails(s,this.data())),t)}}),e.fn.select2=function(){var r,i,s,o,u,a=Array.prototype.slice.call(arguments,0),f=["val","destroy","opened","open","close","focus","isFocused","container","dropdown","onSortStart","onSortEnd","enable","readonly","positionDropdown","data","search"],l=["val","opened","isFocused","container","data"],c={search:"externalSearch"};return this.each(function(){if(0===a.length||"object"==typeof a[0])r=0===a.length?{}:e.extend({},a[0]),r.element=e(this),"select"===r.element.get(0).tagName.toLowerCase()?u=r.element.prop("multiple"):(u=r.multiple||!1,"tags"in r&&(r.multiple=u=!0)),i=u?new M:new O,i.init(r);else{if("string"!=typeof a[0])throw"Invalid arguments to select2 plugin: "+a;if(0>n(a[0],f))throw"Unknown method: "+a[0];if(o=t,i=e(this).data("select2"),i===t)return;if(s=a[0],"container"===s?o=i.container:"dropdown"===s?o=i.dropdown:(c[s]&&(s=c[s]),o=i[s].apply(i,a.slice(1))),n(a[0],l)>=0)return!1}}),o===t?this:o},e.fn.select2.defaults={width:"copy",loadMorePadding:0,closeOnSelect:!0,openOnEnter:!0,containerCss:{},dropdownCss:{},containerCssClass:"",dropdownCssClass:"",formatResult:function(e,t,n,r){var i=[];return y(e.text,n.term,i,r),i.join("")},formatSelection:function(e,n,r){return e?r(e.text):t},sortResults:function(e){return e},formatResultCssClass:function(){return t},formatSelectionCssClass:function(){return t},formatNoMatches:function(){return"No matches found"},formatInputTooShort:function(e,t){var n=t-e.length;return"Please enter "+n+" more character"+(1==n?"":"s")},formatInputTooLong:function(e,t){var n=e.length-t;return"Please delete "+n+" character"+(1==n?"":"s")},formatSelectionTooBig:function(e){return"You can only select "+e+" item"+(1==e?"":"s")},formatLoadMore:function(){return"Loading more results..."},formatSearching:function(){return"Searching..."},minimumResultsForSearch:0,minimumInputLength:0,maximumInputLength:null,maximumSelectionSize:0,id:function(e){return e.id},matcher:function(e,t){return(""+t).toUpperCase().indexOf((""+e).toUpperCase())>=0},separator:",",tokenSeparators:[],tokenizer:C,escapeMarkup:b,blurOnChange:!1,selectOnBlur:!1,adaptContainerCssClass:function(e){return e},adaptDropdownCssClass:function(){return null}},e.fn.select2.ajaxDefaults={transport:e.ajax,params:{type:"GET",cache:!1,dataType:"json"}},window.Select2={query:{ajax:w,local:E,tags:S},util:{debounce:f,markMatch:y,escapeMarkup:b},"class":{"abstract":A,single:O,multi:M}}}}(jQuery),define("select2",["jquery"],function(e){return function(){var t,n;return t||e.select2}}(this)),function(e,t,n){typeof t=="function"&&t.amd?t("pikaday",["require"],function(t){var r="moment",i=t.defined&&t.defined(r)?t(r):undefined;return n(i||e.moment)}):e.Pikaday=n(e.moment)}(window,window.define,function(e){var t=typeof e=="function",n=!!window.addEventListener,r=window.document,i=window.setTimeout,s=function(e,t,r,i){n?e.addEventListener(t,r,!!i):e.attachEvent("on"+t,r)},o=function(e,t,r,i){n?e.removeEventListener(t,r,!!i):e.detachEvent("on"+t,r)},u=function(e,t,n){var i;r.createEvent?(i=r.createEvent("HTMLEvents"),i.initEvent(t,!0,!1),i=y(i,n),e.dispatchEvent(i)):r.createEventObject&&(i=r.createEventObject(),i=y(i,n),e.fireEvent("on"+t,i))},a=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")},f=function(e,t){return(" "+e.className+" ").indexOf(" "+t+" ")!==-1},l=function(e,t){f(e,t)||(e.className=e.className===""?t:e.className+" "+t)},c=function(e,t){e.className=a((" "+e.className+" ").replace(" "+t+" "," "))},h=function(e){return/Array/.test(Object.prototype.toString.call(e))},p=function(e){return/Date/.test(Object.prototype.toString.call(e))&&!isNaN(e.getTime())},d=function(e){return e%4===0&&e%100!==0||e%400===0},v=function(e,t){return[31,d(e)?29:28,31,30,31,30,31,31,30,31,30,31][t]},m=function(e){p(e)&&e.setHours(0,0,0,0)},g=function(e,t){return e.getTime()===t.getTime()},y=function(e,t,n){var r,i;for(r in t){i=e[r]!==undefined;if(i&&typeof t[r]=="object"&&t[r].nodeName===undefined)p(t[r])?n&&(e[r]=new Date(t[r].getTime())):h(t[r])?n&&(e[r]=t[r].slice(0)):e[r]=y({},t[r],n);else if(n||!i)e[r]=t[r]}return e},b={field:null,bound:undefined,format:"YYYY-MM-DD",defaultDate:null,setDefaultDate:!1,firstDay:0,minDate:null,maxDate:null,yearRange:10,minYear:0,maxYear:9999,minMonth:undefined,maxMonth:undefined,isRTL:!1,numberOfMonths:1,i18n:{previousMonth:"Previous Month",nextMonth:"Next Month",months:["January","February","March","April","May","June","July","August","September","October","November","December"],weekdays:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],weekdaysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},onSelect:null,onOpen:null,onClose:null,onDraw:null},w=function(e,t,n){t+=e.firstDay;while(t>=7)t-=7;return n?e.i18n.weekdaysShort[t]:e.i18n.weekdays[t]},E=function(e,t,n,r,i){if(i)return'<td class="is-empty"></td>';var s=[];return r&&s.push("is-disabled"),n&&s.push("is-today"),t&&s.push("is-selected"),'<td data-day="'+e+'" class="'+s.join(" ")+'"><button class="pika-button" type="button">'+e+"</button>"+"</td>"},S=function(e,t){return"<tr>"+(t?e.reverse():e).join("")+"</tr>"},x=function(e){return"<tbody>"+e.join("")+"</tbody>"},T=function(e){var t,n=[];for(t=0;t<7;t++)n.push('<th scope="col"><abbr title="'+w(e,t)+'">'+w(e,t,!0)+"</abbr></th>");return"<thead>"+(e.isRTL?n.reverse():n).join("")+"</thead>"},N=function(e){var t,n,r,i=e._o,s=e._m,o=e._y,u=o===i.minYear,a=o===i.maxYear,f='<div class="pika-title">',l=!0,c=!0;for(r=[],t=0;t<12;t++)r.push('<option value="'+t+'"'+(t===s?" selected":"")+(u&&t<i.minMonth||a&&t>i.maxMonth?"disabled":"")+">"+i.i18n.months[t]+"</option>");f+='<div class="pika-label">'+i.i18n.months[s]+'<select class="pika-select pika-select-month">'+r.join("")+"</select></div>",h(i.yearRange)?(t=i.yearRange[0],n=i.yearRange[1]+1):(t=o-i.yearRange,n=1+o+i.yearRange);for(r=[];t<n&&t<=i.maxYear;t++)t>=i.minYear&&r.push('<option value="'+t+'"'+(t===o?" selected":"")+">"+t+"</option>");return f+='<div class="pika-label">'+o+'<select class="pika-select pika-select-year">'+r.join("")+"</select></div>",u&&(s===0||i.minMonth>=s)&&(l=!1),a&&(s===11||i.maxMonth<=s)&&(c=!1),f+='<button class="pika-prev'+(l?"":" is-disabled")+'" type="button">'+i.i18n.previousMonth+"</button>",f+='<button class="pika-next'+(c?"":" is-disabled")+'" type="button">'+i.i18n.nextMonth+"</button>",f+="</div>"},C=function(e,t){return'<table cellpadding="0" cellspacing="0" class="pika-table">'+T(e)+x(t)+"</table>"},k=function(o){var u=this,a=u.config(o);u._onMouseDown=function(e){if(!u._v)return;e=e||window.event;var t=e.target||e.srcElement;if(!t)return;if(!f(t,"is-disabled")){if(f(t,"pika-button")&&!f(t,"is-empty")){u.setDate(new Date(u._y,u._m,parseInt(t.innerHTML,10))),a.bound&&i(function(){u.hide()},100);return}f(t,"pika-prev")?u.prevMonth():f(t,"pika-next")&&u.nextMonth()}if(!f(t,"pika-select")){if(!e.preventDefault)return e.returnValue=!1,!1;e.preventDefault()}else u._c=!0},u._onChange=function(e){e=e||window.event;var t=e.target||e.srcElement;if(!t)return;f(t,"pika-select-month")?u.gotoMonth(t.value):f(t,"pika-select-year")&&u.gotoYear(t.value)},u._onInputChange=function(n){var r;if(n.firedBy===u)return;t?(r=e(a.field.value,a.format),r=r&&r.isValid()?r.toDate():null):r=new Date(Date.parse(a.field.value)),u.setDate(p(r)?r:null),u._v||u.show()},u._onInputFocus=function(){u.show()},u._onInputClick=function(){u.show()},u._onInputBlur=function(){u._c||(u._b=i(function(){u.hide()},50)),u._c=!1},u._onClick=function(e){e=e||window.event;var t=e.target||e.srcElement,r=t;if(!t)return;!n&&f(t,"pika-select")&&(t.onchange||(t.setAttribute("onchange","return;"),s(t,"change",u._onChange)));do if(f(r,"pika-single"))return;while(r=r.parentNode);u._v&&t!==a.field&&u.hide()},u.el=r.createElement("div"),u.el.className="pika-single"+(a.isRTL?" is-rtl":""),s(u.el,"mousedown",u._onMouseDown,!0),s(u.el,"change",u._onChange),a.field&&(a.bound?r.body.appendChild(u.el):a.field.parentNode.insertBefore(u.el,a.field.nextSibling),s(a.field,"change",u._onInputChange),a.defaultDate||(t&&a.field.value?a.defaultDate=e(a.field.value,a.format).toDate():a.defaultDate=new Date(Date.parse(a.field.value)),a.setDefaultDate=!0));var l=a.defaultDate;p(l)?a.setDefaultDate?u.setDate(l,!0):u.gotoDate(l):u.gotoDate(new Date),a.bound?(this.hide(),u.el.className+=" is-bound",s(a.field,"click",u._onInputClick),s(a.field,"focus",u._onInputFocus),s(a.field,"blur",u._onInputBlur)):this.show()};return k.prototype={config:function(e){this._o||(this._o=y({},b,!0));var t=y(this._o,e,!0);t.isRTL=!!t.isRTL,t.field=t.field&&t.field.nodeName?t.field:null,t.bound=!!(t.bound!==undefined?t.field&&t.bound:t.field);var n=parseInt(t.numberOfMonths,10)||1;t.numberOfMonths=n>4?4:n,p(t.minDate)||(t.minDate=!1),p(t.maxDate)||(t.maxDate=!1),t.minDate&&t.maxDate&&t.maxDate<t.minDate&&(t.maxDate=t.minDate=!1),t.minDate&&(m(t.minDate),t.minYear=t.minDate.getFullYear(),t.minMonth=t.minDate.getMonth()),t.maxDate&&(m(t.maxDate),t.maxYear=t.maxDate.getFullYear(),t.maxMonth=t.maxDate.getMonth());if(h(t.yearRange)){var r=(new Date).getFullYear()-10;t.yearRange[0]=parseInt(t.yearRange[0],10)||r,t.yearRange[1]=parseInt(t.yearRange[1],10)||r}else t.yearRange=Math.abs(parseInt(t.yearRange,10))||b.yearRange,t.yearRange>100&&(t.yearRange=100);return t},toString:function(n){return p(this._d)?t?e(this._d).format(n||this._o.format):this._d.toDateString():""},getMoment:function(){return t?e(this._d):null},setMoment:function(n){t&&e.isMoment(n)&&this.setDate(n.toDate())},getDate:function(){return p(this._d)?new Date(this._d.getTime()):null},setDate:function(e,t,n){if(!e)return this._d=null,this.draw();typeof e=="string"&&(e=new Date(Date.parse(e)));if(!p(e))return;var r=this._o.minDate,i=this._o.maxDate;p(r)&&e<r?e=r:p(i)&&e>i&&(e=i),this._d=new Date(e.getTime()),m(this._d),this.gotoDate(this._d),this._o.field&&(this._o.field.value=this.toString(),n||u(this._o.field,"change",{firedBy:this})),!t&&typeof this._o.onSelect=="function"&&this._o.onSelect.call(this,this.getDate())},gotoDate:function(e){if(!p(e))return;this._y=e.getFullYear(),this._m=e.getMonth(),this.draw()},gotoToday:function(){this.gotoDate(new Date)},gotoMonth:function(e){isNaN(e=parseInt(e,10))||(this._m=e<0?0:e>11?11:e,this.draw())},nextMonth:function(){++this._m>11&&(this._m=0,this._y++),this.draw()},prevMonth:function(){--this._m<0&&(this._m=11,this._y--),this.draw()},gotoYear:function(e){isNaN(e)||(this._y=parseInt(e,10),this.draw())},draw:function(e){if(!this._v&&!e)return;var t=this._o,n=t.minYear,r=t.maxYear,s=t.minMonth,o=t.maxMonth;this._y<=n&&(this._y=n,!isNaN(s)&&this._m<s&&(this._m=s)),this._y>=r&&(this._y=r,!isNaN(o)&&this._m>o&&(this._m=o)),this.el.innerHTML=N(this)+this.render(this._y,this._m);if(t.bound){var u=t.field,a=u.offsetLeft,f=u.offsetTop+u.offsetHeight;while(u=u.offsetParent)a+=u.offsetLeft,f+=u.offsetTop;this.el.style.cssText="position:absolute;left:"+a+"px;top:"+f+"px;",i(function(){t.field.focus()},1)}if(typeof this._o.onDraw=="function"){var l=this;i(function(){l._o.onDraw.call(l)},0)}},render:function(e,t){var n=this._o,r=new Date,i=v(e,t),s=(new Date(e,t,1)).getDay(),o=[],u=[];m(r),n.firstDay>0&&(s-=n.firstDay,s<0&&(s+=7));var a=i+s,f=a;while(f>7)f-=7;a+=7-f;for(var l=0,c=0;l<a;l++){var h=new Date(e,t,1+(l-s)),d=n.minDate&&h<n.minDate||n.maxDate&&h>n.maxDate,y=p(this._d)?g(h,this._d):!1,b=g(h,r),w=l<s||l>=i+s;u.push(E(1+(l-s),y,b,d,w)),++c===7&&(o.push(S(u,n.isRTL)),u=[],c=0)}return C(n,o)},isVisible:function(){return this._v},show:function(){this._v||(this._o.bound&&s(r,"click",this._onClick),c(this.el,"is-hidden"),this._v=!0,this.draw(),typeof this._o.onOpen=="function"&&this._o.onOpen.call(this))},hide:function(){var e=this._v;e!==!1&&(this._o.bound&&o(r,"click",this._onClick),this.el.style.cssText="",l(this.el,"is-hidden"),this._v=!1,e!==undefined&&typeof this._o.onClose=="function"&&this._o.onClose.call(this))},destroy:function(){this.hide(),o(this.el,"mousedown",this._onMouseDown,!0),o(this.el,"change",this._onChange),this._o.field&&(o(this._o.field,"change",this._onInputChange),this._o.bound&&(o(this._o.field,"click",this._onInputClick),o(this._o.field,"focus",this._onInputFocus),o(this._o.field,"blur",this._onInputBlur))),this.el.parentNode&&this.el.parentNode.removeChild(this.el)}},k}),+function(e){var t=function(t,n){this.options=n,this.$element=e(t),this.$backdrop=this.isShown=null,this.options.remote&&this.$element.load(this.options.remote)};t.DEFAULTS={backdrop:!0,keyboard:!0,show:!0},t.prototype.toggle=function(e){return this[this.isShown?"hide":"show"](e)},t.prototype.show=function(t){var n=this,r=e.Event("show.bs.modal",{relatedTarget:t});this.$element.trigger(r),this.isShown||r.isDefaultPrevented()||(this.isShown=!0,this.escape(),this.$element.on("click.dismiss.modal",'[data-dismiss="modal"]',e.proxy(this.hide,this)),this.backdrop(function(){var r=e.support.transition&&n.$element.hasClass("fade");n.$element.parent().length||n.$element.appendTo(document.body),n.$element.show(),r&&n.$element[0].offsetWidth,n.$element.addClass("in").attr("aria-hidden",!1),n.enforceFocus();var i=e.Event("shown.bs.modal",{relatedTarget:t});r?n.$element.find(".modal-dialog").one(e.support.transition.end,function(){n.$element.focus().trigger(i)}).emulateTransitionEnd(300):n.$element.focus().trigger(i)}))},t.prototype.hide=function(t){t&&t.preventDefault(),t=e.Event("hide.bs.modal"),this.$element.trigger(t),this.isShown&&!t.isDefaultPrevented()&&(this.isShown=!1,this.escape(),e(document).off("focusin.bs.modal"),this.$element.removeClass("in").attr("aria-hidden",!0).off("click.dismiss.modal"),e.support.transition&&this.$element.hasClass("fade")?this.$element.one(e.support.transition.end,e.proxy(this.hideModal,this)).emulateTransitionEnd(300):this.hideModal())},t.prototype.enforceFocus=function(){e(document).off("focusin.bs.modal").on("focusin.bs.modal",e.proxy(function(e){this.$element[0]===e.target||this.$element.has(e.target).length||this.$element.focus()},this))},t.prototype.escape=function(){this.isShown&&this.options.keyboard?this.$element.on("keyup.dismiss.bs.modal",e.proxy(function(e){27==e.which&&this.hide()},this)):this.isShown||this.$element.off("keyup.dismiss.bs.modal")},t.prototype.hideModal=function(){var e=this;this.$element.hide(),this.backdrop(function(){e.removeBackdrop(),e.$element.trigger("hidden.bs.modal")})},t.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},t.prototype.backdrop=function(t){var n=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var r=e.support.transition&&n;if(this.$backdrop=e('<div class="modal-backdrop '+n+'" />').appendTo(document.body),this.$element.on("click.dismiss.modal",e.proxy(function(e){e.target===e.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus.call(this.$element[0]):this.hide.call(this))},this)),r&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!t)return;r?this.$backdrop.one(e.support.transition.end,t).emulateTransitionEnd(150):t()}else!this.isShown&&this.$backdrop?(this.$backdrop.removeClass("in"),e.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one(e.support.transition.end,t).emulateTransitionEnd(150):t()):t&&t()};var n=e.fn.modal;e.fn.modal=function(n,r){return this.each(function(){var i=e(this),s=i.data("bs.modal"),o=e.extend({},t.DEFAULTS,i.data(),"object"==typeof n&&n);s||i.data("bs.modal",s=new t(this,o)),"string"==typeof n?s[n](r):o.show&&s.show(r)})},e.fn.modal.Constructor=t,e.fn.modal.noConflict=function(){return e.fn.modal=n,this},e(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(t){var n=e(this),r=n.attr("href"),i=e(n.attr("data-target")||r&&r.replace(/.*(?=#[^\s]+$)/,"")),s=i.data("modal")?"toggle":e.extend({remote:!/#/.test(r)&&r},i.data(),n.data());t.preventDefault(),i.modal(s,this).one("hide",function(){n.is(":visible")&&n.focus()})}),e(document).on("show.bs.modal",".modal",function(){e(document.body).addClass("modal-open")}).on("hidden.bs.modal",".modal",function(){e(document.body).removeClass("modal-open")})}(window.jQuery),define("modal",["jquery"],function(){}),window.bootbox=window.bootbox||function a(e,t){function n(e){var t=y[m.locale];return t?t[e]:y.en[e]}function r(t,n,r){t.preventDefault();var i=e.isFunction(r)&&r(t)===!1;i||n.modal("hide")}function i(e){var t,n=0;for(t in e)n++;return n}function s(t,n){var r=0;e.each(t,function(e,t){n(e,t,r++)})}function o(t){var n,r;if("object"!=typeof t)throw new Error("Please supply an object of options");if(!t.message)throw new Error("Please specify a message");return t=e.extend({},m,t),t.buttons||(t.buttons={}),t.backdrop=t.backdrop?"static":!1,n=t.buttons,r=i(n),s(n,function(t,i,s){if(e.isFunction(i)&&(i=n[t]={callback:i}),"object"!==e.type(i))throw new Error("button with key "+t+" must be an object");i.label||(i.label=t),i.className||(i.className=2>=r&&s===r-1?"btn-primary":"btn-default")}),t}function u(e,t){var n=e.length,r={};if(1>n||n>2)throw new Error("Invalid argument length");return 2===n||"string"==typeof e[0]?(r[t[0]]=e[0],r[t[1]]=e[1]):r=e[0],r}function f(t,n,r){return e.extend(!0,{},t,u(n,r))}function l(e,t,n){return p(f(h.apply(null,e),t,n),e)}function c(){for(var e={},t=0,r=arguments.length;r>t;t++){var i=arguments[t],s=i.toLowerCase(),o=i.toUpperCase();e[s]={label:n(o)}}return e}function h(){return{buttons:c.apply(null,arguments)}}function p(e,n){var r={};return s(n,function(e,t){r[t]=!0}),s(e.buttons,function(e){if(r[e]===t)throw new Error("button key "+e+" is not allowed (options are "+n.join("\n")+")")}),e}var d={dialog:"<div class='bootbox modal' tabindex='-1' role='dialog'><div class='modal-dialog'><div class='modal-content'><div class='modal-body'><div class='bootbox-body'></div></div></div></div></div>",header:"<div class='modal-header'><h4 class='modal-title'></h4></div>",footer:"<div class='modal-footer'></div>",closeButton:"<button type='button' class='bootbox-close-button close'>&times;</button>",form:"<form class='bootbox-form'></form>",inputs:{text:"<input class='bootbox-input form-control' autocomplete=off type=text />"}},v=e("body"),m={locale:"en",backdrop:!0,animate:!0,className:null,closeButton:!0,show:!0},g={};g.alert=function(){var t;if(t=l(["ok"],arguments,["message","callback"]),t.callback&&!e.isFunction(t.callback))throw new Error("alert requires callback property to be a function when provided");return t.buttons.ok.callback=t.onEscape=function(){return e.isFunction(t.callback)?t.callback():!0},g.dialog(t)},g.confirm=function(){var t;if(t=l(["cancel","confirm"],arguments,["message","callback"]),t.buttons.cancel.callback=t.onEscape=function(){return t.callback(!1)},t.buttons.confirm.callback=function(){return t.callback(!0)},!e.isFunction(t.callback))throw new Error("confirm requires a callback");return g.dialog(t)},g.prompt=function(){var n,r,i,s,o,u;if(s=e(d.form),r={buttons:c("cancel","confirm"),value:""},n=p(f(r,arguments,["title","callback"]),["cancel","confirm"]),u=n.show===t?!0:n.show,n.message=s,n.buttons.cancel.callback=n.onEscape=function(){return n.callback(null)},n.buttons.confirm.callback=function(){return n.callback(o.val())},n.show=!1,!n.title)throw new Error("prompt requires a title");if(!e.isFunction(n.callback))throw new Error("prompt requires a callback");return o=e(d.inputs.text),o.val(n.value),s.append(o),s.on("submit",function(e){e.preventDefault(),i.find(".btn-primary").click()}),i=g.dialog(n),i.off("shown.bs.modal"),i.on("shown.bs.modal",function(){o.focus()}),u===!0&&i.modal("show"),i},g.dialog=function(t){t=o(t);var n=e(d.dialog),i=n.find(".modal-body"),u=t.buttons,a="",f={onEscape:t.onEscape};if(s(u,function(e,t){a+="<button data-bb-handler='"+e+"' type='button' class='btn "+t.className+"'>"+t.label+"</button>",f[e]=t.callback}),i.find(".bootbox-body").html(t.message),t.animate===!0&&n.addClass("fade"),t.className&&n.addClass(t.className),t.title&&i.before(d.header),t.closeButton){var l=e(d.closeButton);t.title?n.find(".modal-header").prepend(l):l.css("margin-top","-10px").prependTo(i)}return t.title&&n.find(".modal-title").html(t.title),a.length&&(i.after(d.footer),n.find(".modal-footer").html(a)),n.on("hidden.bs.modal",function(e){e.target===this&&n.remove()}),n.on("shown.bs.modal",function(){n.find(".btn-primary:first").focus()}),n.on("escape.close.bb",function(e){f.onEscape&&r(e,n,f.onEscape)}),n.on("click",".modal-footer button",function(t){var i=e(this).data("bb-handler");r(t,n,f[i])}),n.on("click",".bootbox-close-button",function(e){r(e,n,f.onEscape)}),n.on("keyup",function(e){27===e.which&&n.trigger("escape.close.bb")}),v.append(n),n.modal({backdrop:t.backdrop,keyboard:!1,show:!1}),t.show&&n.modal("show"),n},g.setDefaults=function(t){e.extend(m,t)},g.hideAll=function(){e(".bootbox").modal("hide")};var y={br:{OK:"OK",CANCEL:"Cancelar",CONFIRM:"Sim"},da:{OK:"OK",CANCEL:"Annuller",CONFIRM:"Accepter"},de:{OK:"OK",CANCEL:"Abbrechen",CONFIRM:"Akzeptieren"},en:{OK:"OK",CANCEL:"Cancel",CONFIRM:"OK"},es:{OK:"OK",CANCEL:"Cancelar",CONFIRM:"Aceptar"},fi:{OK:"OK",CANCEL:"Peruuta",CONFIRM:"OK"},fr:{OK:"OK",CANCEL:"Annuler",CONFIRM:"D'accord"},it:{OK:"OK",CANCEL:"Annulla",CONFIRM:"Conferma"},nl:{OK:"OK",CANCEL:"Annuleren",CONFIRM:"Accepteren"},pl:{OK:"OK",CANCEL:"Anuluj",CONFIRM:"Potwierdź"},ru:{OK:"OK",CANCEL:"Отмена",CONFIRM:"Применить"},zh_CN:{OK:"OK",CANCEL:"取消",CONFIRM:"确认"},zh_TW:{OK:"OK",CANCEL:"取消",CONFIRM:"確認"}};return g.init=function(t){window.bootbox=a(t||e)},g}(window.jQuery),define("bootbox",["jquery","modal"],function(e){return function(){var t,n;return t||e.bootbox}}(this)),define("notification",["jquery"],function(){var e=$(".notification"),t=$(".noti-error"),n;return n={show:function(t){e.show().find(".text-noti").text(t)},hide:function(){e.fadeOut()},autoHide:function(e,t){var n=this;n.show(e),setTimeout(function(){n.hide()},t||5e3)},showError:function(e){t.show().find(".text-error").text(e)},hideError:function(){t.fadeOut()},autoHideError:function(e,t){var n=this;n.showError(e),setTimeout(function(){n.hideError()},t||5e3)}},$(document).on("click",".notification",function(e){n.hide()}),e.hasClass("show")&&setTimeout(function(){n.hide()},5e3),window.notification=n,n}),define("conflictingappointments_modal",["jquery","moment","select2","icheck","mustache","underscore","backbone","pikaday","notification"],function(e,t,n,r,s,o,u,a,f){function l(t){this.defaults={url_get_conflicting_rooms:"conflicts/getConflictingAppointments.htm",EL:{$modal:e("#modal_conflicting_appointment"),$mask:e(".mask")}},this.currentVersion=undefined,this.init(t)}return l.prototype={init:function(t){this.conf=e.extend({},this.defaults,t);var n=this,r=n.conf,i=n.conf.EL,s=i.$modal,o,a,f,l;o=u.Model.extend({}),n.conflicting_model=new o,a=u.Model.extend({defaults:{}}),l=new a,n.modal=l,f=l.formEL},renderConflictingList:function(e){var t=this,n=t.conf,r=t.conf.EL,s=r.$modal,o=e;conflicts='<table height="40" width="100%" style="color: #ffffff;background-color: #CC0000;border: 3px solid #bbbcbd;"><tr>',conflicts+="<th><center>Number of Conflicting Appointments : "+o.conflictingappointmentcount+"</center></th>",conflicts+="</tr></table><br><br>",conflicts+='<table class="table table-fixed"><tr >',o.roomConflict?conflicts+='<th class="cell-150"><b>Room Name</b></th>':conflicts+='<th class="cell-150"><b>Instructor Name</b></th>',conflicts+="<th><b>Date</b></th>",conflicts+='<th class="cell-150"><b>Time Frame</b></th>',conflicts+="<th><b>Duration</b></th>",conflicts+="<th><b>Activity Type</b></th>",conflicts+="<th><b>Customer Name</b></th></tr>";if(o.conflictingappointmentlist.length>0){for(i in o.conflictingappointmentlist)conflicts+="<tr>",conflicts+='<td width="250">'+o.conflictingappointmentlist[i].roomName+"</td>",conflicts+='<td width="170">'+o.conflictingappointmentlist[i].date1+"</td>",conflicts+='<td width="200">'+o.conflictingappointmentlist[i].timeFrame+"</td>",conflicts+='<td width="150">'+o.conflictingappointmentlist[i].duration+"</td>",conflicts+='<td width="150">'+o.conflictingappointmentlist[i].activityName+"</td>",conflicts+='<td width="150">'+o.conflictingappointmentlist[i].customers[0].person.firstName+" "+o.conflictingappointmentlist[i].customers[0].person.lastName+"</td>",conflicts+="</tr>";conflicts+="</table>"}s.find(".conflicting-List").empty().html(conflicts)},showModal:function(t){var n=this,r=n.conf,i=r.EL,s=i.$mask,o=i.$modal,u=e(window).height(),a=e(window).width(),f=750,l=700,c=(u-l)/2+e(window).scrollTop(),h=(a-f)/2;c<0&&(c=10),h<0&&(h=10),o.css({left:h,top:c}),this.instructorModel=t;if(t.instructorId==""||t.roomId==""||t.startDate==""||t.startTime==""||t.duration==""){noError="Please provide the instructor, room, start date and duration fields for generating the conflicting appointments",o.find(".conflicting-List").empty(),o.find(".modal-msg").html(noError).show(),o.show(),s.show();return}e.ajax({url:r.url_get_conflicting_rooms,type:"GET",data:{instructorId:t.instructorId,roomId:t.roomId,startDate:t.startDate,endDate:t.endDate,startTime:t.startTime,duration:t.duration,isRecurring:t.isRecurring},success:function(e){e.status?(o.find(".modal-msg").hide(),n.renderConflictingList(e)):(noError="No Conflicting appointments for this room/instructor",o.find(".conflicting-List").empty(),o.find(".modal-msg").html(noError).show()),o.show(),s.show()}})},hideModal:function(){var e=this,t=e.conf.EL,n=e.modal.formEL;t.$modal.hide(),t.$mask.hide()}},l}),define("appointment_modal",["jquery","moment","backbone","select2","icheck","pikaday","hash","bootbox","notification","conflictingappointments_modal"],function(e,t,n,r,i,s,o,u,a,f){function h(t){this.defaults={url_create:"calendar/createAppointment.htm",url_update:"calendar/updateAppointment.htm",url_get:"calendar/getAppointmentById.htm",url_get_cancel_reason:"calendar/cancelReasonList.htm",url_search:"calendar/quickSearch.htm",url_cancel_appointment:"calendar/cancelAppointment.htm",url_fetch_service:"calendar/loadService.htm",url_fetch_activity:"calendar/loadActivitiesByService.htm",url_fetch_instructor:"calendar/loadInstructorByActivity.htm",url_fetch_listByService:"calendar/getServiceById.htm",url_fetch_servicelist_by_room_or_instructor:"calendar/loadServiceListByRoomOrInstructor.htm",url_fetch_activitylist_by_appointmentmodel:"calendar/loadActivityListByAppointmentModel.htm",url_fetch_instructor_or_room_list_onchange:"calendar/loadInstructorOrRoomListOnChange.htm",EL:e("#modal_app")},this.attrs={},this.init(t)}var l,c="";return h.prototype={init:function(t){this.conf=e.extend({},this.defaults,t);var r=this,i=r.conf,u=i.EL,a=this.attrs,l,c,h;return u.find(".js-conflict-app").attr("disabled",!0),l=n.Model.extend({defaults:{isRecurring:"false",instructorId:"",roomId:""}}),c=new l,r.appointment_model=c,h=n.Model.extend({defaults:{},formEL:{appointmentSeriesId:{el:u.find('input[name="appointmentSeriesId"]'),set:function(e){return c.set("appointmentSeriesId",e),this},clear:function(){return c.unset("appointmentSeriesId"),this},needValid:!1},appointmentId:{el:u.find('input[name="appointmentId"]'),set:function(e){return this.el.val(e),c.set("appointmentId",e),this},clear:function(){return this.el.val(""),c.unset("appointmentId"),this},needValid:!1},customerId:{el:u.find('input[name="customerId"]'),setAll:function(e){return this.el.select2("data",e),this},set:function(e){var t=a.customerList,n=[],r="";if(!t||t.length===0){if(typeof e!="object"||e===undefined){this.el.select2("data",[]);return}t=new Array(e)}return t.forEach(function(e,t){n.push({id:e.recordId,recordId:e.recordId,externalId:e.externalId,text:e.fullName,fullName:e.fullName}),r+=e.recordId+","}),r=r.substring(0,r.length-1),this.el.select2("data",n),c.set("customerId",r),this},clear:function(){return this.el.select2("val",""),delete a.customerList,c.unset("customerId"),this},needValid:!0},serviceId:{el:u.find('input[name="serviceId"]'),set:function(e){return this.el.select2("val",e),c.set("serviceId",e),this},clear:function(){return this.el.select2("val","").select2({placeholder:"Select Service Type",ajax:{url:i.url_fetch_service,results:function(e,t){return{results:e}}},id:function(e){return e.serviceId},formatResult:function(e){return e.serviceName},formatSelection:function(e){return e.serviceName}}),c.unset("serviceId"),this},setAll:function(e){return this.el.select2({placeholder:"Select Service Type",data:e,id:function(e){return e.serviceId},formatResult:function(e){return e.serviceName},formatSelection:function(e){return e.serviceName}}),this},needValid:!0},bandName:{el:u.find('input[name="bandName"]'),wrap:u.find(".bandName-control"),set:function(e){return this.el.val(e),c.set("bandName",e),this},clear:function(){return this.el.val(""),c.unset("bandName"),this},needValid:!0},cancelReason:{el:u.find('input[name="cancelReason"]'),wrap:u.find(".cancelList-control"),setAll:function(e){return this.el.select2({placeholder:"Select Cancel Reason Type",data:e,id:function(e){return e.appointmentcancelreasonID},formatResult:function(e){return e.cancelReason},formatSelection:function(e){return e.cancelReason}}),this},set:function(e){typeof e=="object"&&this.el.select2("data",e).select2("readonly",!1);if(typeof e=="string"||typeof e=="number")this.el.select2("val",e).select2("readonly",!1),c.set("cancelReason",e);return this},clear:function(){return this.el.select2("val","").select2("readonly",!1),c.unset("cancelReason"),this},needValid:!0},activityId:{el:u.find('input[name="activityId"]'),wrap:u.find(".activityId-control"),setAll:function(e){return this.el.select2({placeholder:"Select Lesson Type",data:e,id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}),this},set:function(e){typeof e=="object"&&this.el.select2("data",e).select2("readonly",!1);if(typeof e=="string"||typeof e=="number")this.el.select2("val",e).select2("readonly",!1),c.set("activityId",e);return this},clear:function(){return this.el.select2("val","").select2("readonly",!1),c.unset("activityId"),this},needValid:!0},instructorId:{el:u.find('input[name="instructorId"]'),wrap:u.find(".instructor-control"),setAll:function(e){return this.el.select2("close").select2("val","").select2({placeholder:"Select an Instructor",data:e,id:function(e){return e.instructorId},formatResult:function(e){return e.instructorName},formatSelection:function(e){return e.instructorName}}),this},set:function(e){return e&&e.instructorId!==null&&(this.el.select2("data",e).select2("readonly",!1),c.set("instructorId",e.instructorId)),this},clear:function(){return this.el.select2("val","").select2("readonly",!1),c.unset("instructorId"),this},needValid:!0},startDate:{el:u.find('input[name="startDate"]'),set:function(e){return this.el.val(e),c.set("startDate",e),this},clear:function(){return this.el.val(""),c.unset("startDate"),this},needValid:!0},endDate:{el:u.find('input[name="endDate"]'),wrap:u.find(".endDate-control"),set:function(e){return this.el.val(e||""),c.set("endDate",e||""),this},open:function(){return c.set("endDate",this.el.val()),this},clear:function(){return this.el.val(""),this.wrap.hide(),c.unset("endDate"),this},needValid:!1},isRecurring:{el:u.find('input[name="isRecurring"]'),set:function(e){return e=="true"?this.el.iCheck("check"):this.el.iCheck("uncheck"),c.set("isRecurring",e),this},clear:function(){return this.el.iCheck("uncheck"),c.set("isRecurring","false"),this},disabled:function(){return this.el.iCheck("disable"),this},enabled:function(){return this.el.iCheck("enable"),this},needValid:!1},startTime:{el:u.find('input[name="startTime"]'),set:function(e){return this.el.select2("val",e),c.set("startTime",e),this},clear:function(){return this.el.select2("val",""),c.unset("startTime"),this},setAll:function(e){return this.el.select2({data:e}),this},needValid:!0},duration:{el:u.find('input[name="duration"]'),set:function(e){return this.el.select2("val",e),c.set("duration",e),this},setAll:function(e){return this.el.select2({data:e}),this},clear:function(){return this.el.select2("val",""),c.unset("duration"),this},needValid:!0},roomId:{el:u.find('input[name="roomId"]'),setAll:function(e){return this.el.select2("close").select2("val","").select2({placeholder:"Select Room",data:e,id:function(e){return e.roomId},formatResult:function(e){return e.profileRoomName},formatSelection:function(e){return e.profileRoomName}}),this},set:function(e){return e.roomId!==null&&(this.el.select2("data",e).select2("readonly",!1),c.set("roomId",e.roomId)),this},setId:function(e){return this.el.select2("val",e).select2("readonly",!1),c.set("roomId",e),this},clear:function(){return this.el.select2("val","").select2("readonly",!1),c.unset("roomId"),this},needValid:!0},note:{el:u.find('textarea[name="note"]'),set:function(e){return this.el.val(e).text(e),c.set("note",e),this},clear:function(){return this.el.val("").text(""),c.set("note"),this},needValid:!1},parentFullName:{el:u.find('input[name="parentFullName"]'),wrap:u.find(".parentFullName-control"),set:function(e){return this.el.val(e),c.set("parentFullName",e),this},clear:function(){return this.el.val(""),c.unset("parentFullName"),this},needValid:!0},phone:{el:u.find('input[name="phone"]'),wrap:u.find(".phone-control"),set:function(e){return this.el.val(e),c.set("phone",e),this},clear:function(){return this.el.val(""),c.unset("phone"),this},needValid:!0},email:{el:u.find('input[name="email"]'),wrap:u.find(".email-control"),set:function(e){return this.el.val(e),c.set("email",e),this},clear:function(){return this.el.val(""),c.unset("email"),this},needValid:!0}},initialize:function(){var t=this.formEL,n=this;t.customerId.el.select2({placeholder:"Enter a customer name",multiple:!0,minimumInputLength:2,ajax:{url:i.url_search,dataType:"json",data:function(e,t){return{name:e,customers:this.val()}},results:function(t,n){return t.length>3?(e(".select-more").remove(),e("#select2-drop").append('<li class="select-more">'+t[t.length-1].status+"</li>"),{results:t.slice(0,t.length-1)}):(e(".select-more").remove(),{results:t})}},id:function(e){return e.recordId},formatResult:function(e){if(e.recordId!==null){var t=e.externalId||"",n="("+t+")";return e.fullName+" "+n}return e.status},formatSelection:function(e){var t=e.externalId||"",n="("+t+")";if(e.recordId!==null)return e.fullName+" "+n},dropdownCssClass:"dropdown-menu"}),e(document).on("keyup","#s2id_customer_list input",function(t){this.value===""&&e(".select-more").remove()}),e(document).on("focus","#s2id_customer_list input",function(t){this.value===""&&e(".select-more").remove()});var a=new s({format:"MM/DD/YYYY",field:t.startDate.el[0]}),f=new s({format:"MM/DD/YYYY",field:t.endDate.el[0]});t.startDate.el.on("change",function(e){n.requestForm("startDate",e.currentTarget.value,c.get("appointmentId"))}).on("focus",function(){a.setDate(this.value,!0,!0)}),t.endDate.el.on("focus",function(){f.setDate(new Date(this.value))}).on("change",function(e){n.requestForm("endDate",e.currentTarget.value,c.get("appointmentId"))}),t.serviceId.el.select2({data:[],id:function(e){return e.serviceId},formatResult:function(e){return e.serviceName},formatSelection:function(e){return e.serviceName}}).on("change",function(n){var s=n.currentTarget.value;t.activityId.el.select2("readonly",!0),e.ajax({url:i.url_fetch_activitylist_by_appointmentmodel,type:"GET",data:{data:JSON.stringify(e.extend(r.appointment_model.attributes,{serviceId:s})),type:o.get("filter")},success:function(n){var r=n.serviceDTO,s=n.activityDTOs,o=n.role;r.serviceId=="20"&&o.roleId!="0"?(c.set("isRecurring","false"),e("#isRecurring").hide(),t.endDate.set(c.get("endDate")).wrap.hide()):(e("#isRecurring").show(),t.endDate.set(c.get("endDate")).wrap.show()),i.EL.find(".activityId-control label").text(r.serviceName+" Type:"),t.activityId.el.select2("val","").select2({data:s,placeholder:"Select "+r.serviceName+" Type",id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}).select2("readonly",!1),t.activityId.clear(),"rehearsal"==r.serviceName.toLowerCase()?t.bandName.clear().wrap.show():t.bandName.clear().wrap.hide()}})}),t.activityId.el.select2({placeholder:"Select Lesson Type",data:[],id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}).on("change",function(n){var s=this.value;t.instructorId.el.select2("enable",!1),t.roomId.el.select2("enable",!1),t.startTime.el.select2("enable",!1),t.duration.el.select2("enable",!1),e.ajax({url:i.url_fetch_instructor_or_room_list_onchange,type:"GET",data:{data:JSON.stringify(e.extend(r.appointment_model.attributes,{activityId:s})),type:o.get("filter"),appId:c.get("appointmentId")},success:function(n){n.startTimeDTOs&&n.startTimeDTOs.length>0&&t.startTime.setAll(n.startTimeDTOs);if(s=="140"||s=="320"){e("#isRecurringCheckBox").parent().hasClass("checked")&&(e("#isRecurringCheckBox").parent().removeClass("checked"),c.set("isRecurring","false")),e("#isRecurring").hide(),t.endDate.clear().wrap.hide();var r="No Recurring allowed for In-Store/Online Trial Lesson Type.";u.find(".modal-msg1").text(r).show()}else n.serviceId!="20"&&(e("#isRecurring").show(),u.find(".modal-msg1").text(r).hide());0!=n.durationDTOs.length&&(t.duration.set(null),t.duration.setAll(n.durationDTOs)),n.appointmentID!==null&&n.appointmentID!==""?(n.instructorDTOs?n.instructorDTOs.length>0?t.instructorId.setAll(n.instructorDTOs).set(n.instructorDTOs[0]).wrap.show():t.instructorId.setAll([]).set("").wrap.show():t.instructorId.clear().wrap.hide(),n.roomDTOs&&n.roomDTOs.length>0?t.roomId.setAll(n.roomDTOs).set(n.roomDTOs[0]):t.roomId.setAll([]).setId("")):o.get("filter")?o.get("filter").toLowerCase()==="room"||o.get("filter").toLowerCase()===null?undefined!==n.instructorDTOs?n.instructorDTOs.length>0?t.instructorId.setAll(n.instructorDTOs).set(n.instructorDTOs[0]).wrap.show():t.instructorId.setAll([]).set("").wrap.show():t.instructorId.clear().wrap.hide():o.get("filter").toLowerCase()==="instructor"&&(n.roomDTOs&&n.roomDTOs.length>0?t.roomId.setAll(n.roomDTOs).set(n.roomDTOs[0]):t.roomId.setAll([]).set("")):(n.instructorDTOs?n.instructorDTOs.length>0?t.instructorId.setAll(n.instructorDTOs).set(n.instructorDTOs[0]).wrap.show():t.instructorId.setAll([]).set("").wrap.show():t.instructorId.clear().wrap.hide(),n.roomDTOs&&n.roomDTOs.length>0?t.roomId.setAll(n.roomDTOs).set(n.roomDTOs[0]):t.roomId.setAll([]).setId("")),t.instructorId.el.select2("enable",!0),t.roomId.el.select2("enable",!0),t.startTime.el.select2("enable",!0),t.duration.el.select2("enable",!0)}})}),t.instructorId.el.select2({data:[],id:function(e){return e.instructorId},formatResult:function(e){return e.instructorName},formatSelection:function(e){return e.instructorName}}).on("change",function(e){n.requestForm("instructorId",e.currentTarget.value,c.get("appointmentId"))}),t.startTime.el.select2({data:[]}).on("change",function(e){n.requestForm("startTime",e.currentTarget.value,c.get("appointmentId"))}),t.duration.el.select2({data:[]}).on("change",function(e){n.requestForm("duration",e.currentTarget.value,c.get("appointmentId"))}),t.roomId.el.select2({data:[]});for(var l in t)t[l].el.on("change",function(){c.set(this.name,this.value)});t.isRecurring.el.iCheck().on("ifClicked",function(){t.isRecurring.el.iCheck("toggle"),this.checked?(c.set("isRecurring","true"),t.endDate.set(c.get("endDate")).wrap.show()):(c.set("isRecurring","false"),t.endDate.clear().wrap.hide())})},requestForm:function(t,n,i){var s=this,u=r.conf,a={};a[t]=n,e.ajax({url:u.url_fetch_instructor_or_room_list_onchange,data:{data:JSON.stringify(e.extend(r.appointment_model.attributes,a)),type:o.get("filter"),appId:i},success:function(e){s.renderInstructorOrRoom(t,e)}})},renderInstructorOrRoom:function(e,t){var n=this.formEL;t.startTimeDTOs&&t.startTimeDTOs.length>0&&n.startTime.setAll(t.startTimeDTOs);if(t.appointmentID!==null&&t.appointmentID!==""){t.instructorDTOs?t.instructorDTOs.length>0?n.instructorId.setAll(t.instructorDTOs).set(t.instructorDTOs[0]).wrap.show():n.instructorId.setAll([]).set("").wrap.show():n.instructorId.clear().wrap.hide();var r=t.roomDTOs;r&&r.length>0?n.roomId.setAll(t.roomDTOs).set(t.roomDTOs[0]):n.roomId.setAll([])}else{var i=o.get("filter");if(i){if(i.toLowerCase()==="room")t.instructorDTOs&&t.instructorDTOs.length>0?n.instructorId.setAll(t.instructorDTOs).set(t.instructorDTOs[0]).wrap.show():n.instructorId.setAll(t.instructorDTOs||[]);else if(i.toLowerCase()==="instructor"){var r=t.roomDTOs;r&&r.length>0?n.roomId.setAll(t.roomDTOs).set(t.roomDTOs[0]):n.roomId.setAll(t.roomDTOs||[])}}else{var s=t.instructorDTOs,r=t.roomDTOs;s&&s.length>0?n.instructorId.setAll(t.instructorDTOs).set(t.instructorDTOs[0]).wrap.show():n.instructorId.setAll([]).clear(),r&&r.length>0?n.roomId.setAll(t.roomDTOs).set(t.roomDTOs[0]):n.roomId.setAll([]).clear()}}}}),this.modal=new h,e(document).on("click",".modal-close.modal-app-close",function(e){r.hide()}),r.conflicting_model=new f({EL:{$modal:e("#modal_conflicting_appointment"),$mask:e(".mask")}}),r.cancel_appointment_model=new f({EL:{$modal:e("#modal_cancel_appointment"),$mask:e(".mask")}}),e(document).on("click",".js-close-conflicting-appointment-modal",function(e){r.conflicting_model.hideModal()}),e(document).on("click",".js-cancel-type",function(t){var n=e("input:radio[name='Cancel_appointment']:checked").val();e.ajax({url:i.url_get_cancel_reason,type:"POST",data:{recStatus:n},success:function(t){e("#cancel_reason_form #select-medium").html(""),e("#cancel_reason_form #select-medium").append(e("<option  selected disabled hidden></option>").attr("appointmentcancelreasonID",0).text("Select Cancel Reason")),e.each(t.cancelReasonList,function(t,n){e("#cancel_reason_form #select-medium").append(e("<option></option>").attr("appointmentcancelreasonID",n.appointmentcancelreasonID).text(n.cancelReason))}),e("#cancel_reason_form #select-medium").attr("disabled",!1),e("#cancel_reason_form #cancelAppointment").attr("disabled",!1)}})}),this},create:function(n){var r=this,i=r.conf.EL,s=r.modal,u=s.formEL,a,f,l={};u.duration.el.select2({placeholder:"Select Duration",data:[]});var c=o.get("filter");c&&(c.toLowerCase()==="room"?u.instructorId.el.select2({placeholder:"Select an Instructor",data:[]}):c.toLowerCase()==="instructor"&&u.roomId.el.select2({placeholder:"Select Room",data:[]})),e("#isRecurring").show(),u.isRecurring.enabled(),i.find(".activityId-control label").text("Lesson Type:"),a=e(".customer-info").data("customer"),a&&u.customerId.setAll(new Array(a)).set(a),f=e(n).data();if(f!==null){var h=["05:00","05:30","06:00","06:30","07:00","07:30","08:00","08:30","09:00","09:30","10:00","10:30","11:00","11:30","12:00","12:30","13:00","13:30","14:00","14:30","15:00","15:30","16:00","16:30","17:00","17:30","18:00","18:30","19:00","19:30","20:00","20:30","21:00","21:30","22:00","22:30","23:00","23:30"],p=h[f.time],d=o.get("view"),v=t(o.get("date")),m=e(".filter-list");if(m.find('input[name="instructor"]:checked').length===1){var g={instructorId:m.find('input[name="instructor"]:checked').val(),instructorName:m.find('input[name="instructor"]:checked').data("name")};u.instructorId.setAll(new Array(g)).set(g)}else if(f.instructorid!==undefined){var g={instructorId:f.instructorid,instructorName:e("#instructor_"+f.instructorid).text()};o.add({filter:"instructor"}),u.instructorId.setAll(new Array(g)).set(g)}if(m.find('input[name="room"]:checked').length===1){var y={roomId:m.find('input[name="room"]:checked').val(),profileRoomName:m.find('input[name="room"]:checked').data("name")};u.roomId.setAll(new Array(y)).set(y)}else if(f.roomid!==undefined){var y={roomId:f.roomid,profileRoomName:e("#room_"+f.roomid).text()};o.add({filter:"room"}),u.roomId.setAll(new Array(y)).set(y)}if(m.find('input[name="appointmentType"]:checked').length===1){var b={activityId:m.find('input[name="appointmentType"]:checked').val(),activityName:m.find('input[name="appointmentType"]:checked').data("name")};u.activityId.setAll(new Array(b)).set(b.activityId)}if(m.find('input[name="serviceType"]:checked').length===1){var w=m.find('input[name="serviceType"]:checked').val(),E=[];m.find('input[name="serviceType"]').each(function(){E.push({serviceId:e(this).val(),serviceName:e(this).data("name")})}),CONF=r.conf,appointment_model=r.appointment_model;var S=e('input[name="serviceType"]:checked').val();u.activityId.el.select2("readonly",!0),e.ajax({url:CONF.url_fetch_activitylist_by_appointmentmodel,type:"GET",data:{data:JSON.stringify(e.extend(r.appointment_model.attributes,{serviceId:S})),type:o.get("filter")},success:function(e){var t=e.serviceDTO,n=e.activityDTOs;CONF.EL.find(".activityId-control label").text(t.serviceName+" Type:"),u.activityId.el.select2("val","").select2({data:n,placeholder:"Select "+t.serviceName+" Type",id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}).select2("readonly",!1),u.activityId.clear(),"rehearsal"==t.serviceName.toLowerCase()?u.bandName.clear().wrap.show():u.bandName.clear().wrap.hide()}}),u.serviceId.setAll(E).set(w)}if(d==="week"){var x=f.weekday,T=v.days();x==T?v=v.format("MM/DD/YYYY"):x<T?v=v.subtract("days",T-x).format("MM/DD/YYYY"):x>T&&(v=v.add("days",x-T).format("MM/DD/YYYY"))}else v=v.format("MM/DD/YYYY");u.startDate.set(v),u.startTime.set(p),l.type=o.get("filter"),l.data=JSON.stringify(r.appointment_model.attributes),l.time=p}else o.add({filter:""}),u.instructorId.el.select2({placeholder:"Select an Instructor",data:[]}),u.roomId.el.select2({placeholder:"Select Room",data:[]});return r.show("create",l),r},edit:function(t){var n=this,r=e(t).data("appointmentid");return n.show("edit",{appId:r}),n},submit:function(t){var n=this,r=n.conf,i=r.EL,s=n.modal,o="",u,a=e(t.currentTarget);undefined!==l&&undefined!==c&&(n.appointment_model.get("isRecurring")=="true"?c=1:(n.appointment_model.unset("endDate"),c=0),0==l&&0==c&&(o=1),1==l&&1==c&&(o=2),1==l&&0==c&&(o=3)),n.appointment_model.set("recurringStatus",o),u=n.appointment_model.attributes,a.attr("disabled",!0),e.ajax({url:i.find("form").attr("action"),type:"POST",data:{data:JSON.stringify(u)},success:function(t){typeof t=="string"&&t.indexOf("DOCTYPE")!==-1&&location.reload(),t.status?(e(".cal-item-disabled").remove(),n.hide(),r.afterCreate()):(null!=t.isConflicting&&t.isConflicting&&i.find(".js-conflict-app").attr("disabled",!1),i.find(".modal-msg").addClass("error").text(t.message).show()),a.attr("disabled",!1)}})},dataSet:function(){var n=this,r=n.conf.EL,i=n.appointment_model,s=n.attrs.appointmentDetail,o=n.attrs.serviceList,u=n.attrs.customerList,a=n.attrs.cancelReasonList,f=n.attrs.activityList,c=n.attrs.instructorList,h=n.attrs.roomList,p=n.attrs.durationList,d=n.modal.formEL,v=n.attrs.startTimeDTOs,m=n.attrs.selectedStartTime;d.appointmentId.set(i.get("appointmentId")),d.serviceId.setAll(o).set(i.get("serviceId")),u.length!==0&&d.customerId.setAll(u).set(u);var g=s.activityName;g!="Trial Lesson"||g!="Online Trial Lesson"?f.splice(e.inArray(g,f),1):f=f.splice(e.inArray(g,f),1),f.length!==0?d.activityId.setAll(f).set({activityId:s.activityId,activityName:s.activityName}):d.activityId.clear(),0!=c.length?d.instructorId.setAll(c).set({instructorId:s.instructorId,instructorName:s.instructorName}).wrap.show():d.instructorId.clear().wrap.hide(),0!=p.length&&d.duration.setAll(p).set(i.get("duration")),d.startDate.set(i.get("startDate")),d.startTime.setAll(v).set(m),d.roomId.setAll(h).setId(i.get("roomId")),d.bandName.set(i.get("bandName")),d.note.set(i.get("note"));if(i.get("isRecurring")!="true"||i.get("activityId")=="140"&&i.get("activityId")=="320")if(i.get("activityId")!="320"&&i.get("activityId")!="140"||i.get("isRecurring")=="true")l=0,d.endDate.clear().wrap.hide(),d.isRecurring.disabled().set("false"),r.find(".modal-msg").text(y).hide(),r.find(".modal-msg1").text(b).hide(),r.find(".modal-msg1").text(w).hide();else{l=0;var w="No Recurring allowed for In-Store/Online Trial Lesson Type";d.endDate.clear().wrap.hide(),d.isRecurring.disabled().set("false"),r.find(".modal-msg").text(y).hide(),r.find(".modal-msg1").text(b).hide(),r.find(".modal-msg1").text(w).show()}else{l=1;var y="This appointment occurs every "+t(i.get("startDate")).format("dddd")+" "+t(i.get("startDate")+" "+i.get("startTime")).format("hh:mm A"),b="Uncheck recurring appointment box to update this appointment only.";r.find(".modal-msg").removeClass("error").text(y).show(),r.find(".modal-msg1").text(b).show(),d.endDate.set(i.get("endDate")).wrap.show(),d.isRecurring.enabled().set("true")}},dataClear:function(){var e=this,t=e.modal,n=e.modal.formEL;for(var r in t.formEL)n[r].clear()},show:function(n,r){var i=this,s=i.conf.EL,o=this.conf,u=this.modal.formEL,a=e(".mask"),f=e(window).height(),l=e(window).width(),c=470,h=495,p=(f-h)/2+e(window).scrollTop(),d=(l-c)/2;return p<0&&(p=10),d<0&&(d=10),s.css({left:d,top:p}),s.find(".js-conflict-app").attr("disabled",!0),n==="edit"?(s.find(".js-conflict-app").hide(),e.ajax({url:o.url_get,type:"GET",data:{appointmentId:r.appId},success:function(n){typeof n=="string"&&n.indexOf("DOCTYPE")!==-1&&location.reload(),i.appointment_model.clear();for(var r in i.modal.formEL)i.appointment_model.set(r,n.appointment[r]);e.extend(i.attrs,{appointmentDetail:n.appointment,customerList:n.customerList,activityList:n.activityList,instructorList:n.instructorList,serviceList:n.serviceList,roomList:n.roomList,durationList:n.durationDTOs,startTimeDTOs:n.startTimeDTOs,selectedStartTime:n.selectedStartTime,customerDetails:n.appointment.customerDetails}),e("#isRecurring").show();var f=n.serviceDTO;e(".activityId-control label").text(f.serviceName+" Type:"),u.customerId.el.select2("readonly",!0),u.parentFullName.clear().wrap.hide(),u.phone.clear().wrap.hide(),u.email.clear().wrap.hide();if(n.customerList.length!==0){var l=n.appointment.customerDetails.parentFullName,c=n.appointment.customerDetails.phone,h=n.appointment.customerDetails.email;for(var p=0;p<=n.customerList.length;p++)l!==null?u.parentFullName.set(l).wrap.show():u.parentFullName.clear().wrap.hide(),c!==null?u.phone.set(c).wrap.show():u.phone.clear().wrap.hide(),h!==null?u.email.set(h).wrap.show():u.email.clear().wrap.hide()}"rehearsal"==f.serviceName.toLowerCase()?u.bandName.wrap.show():u.bandName.clear().wrap.hide(),i.dataSet(),s.find("form").attr("action",o.url_update),s.find(".modal-header h5").text("Edit Appointment"),s.find(".btn-submit-app").text("Update Appointment"),s.show(),a.show();var d=n.appointment,v=t(d.startDate+" "+d.startTime,"MM/DD/YYYY HH:mm").isAfter(t());if(!v){!v&&d.activityId!="320"&&d.activityId!="140"?(s.find(".btn-submit-app").hide(),s.find(".js-cancel-appointment").hide(),s.find(".js-cancel-appointment-associate").hide(),s.find(".modal-msg1").hide()):(s.find(".btn-submit-app").hide(),s.find(".js-update-appointment").hide(),s.find(".js-cancel-appointment").css({"margin-left":"-105px"}).show(),s.find(".js-cancel-appointment-associate").show(),s.find(".modal-msg1").hide());if(n.startTimeDTOs&&n.startTimeDTOs.length>0){var m=!1;n.startTimeDTOs.forEach(function(e){e===n.selectedStartTime&&(m=!0)});if(!m){var g=n.selectedStartTimeLabel,y=[],b={};b.id=n.selectedStartTime,b.text=g,y.push(b),u.startTime.setAll(y).set(n.selectedStartTime)}}else{var g=n.selectedStartTimeLabel,y=[],b={};b.id=n.selectedStartTime,b.text=g,y.push(b),u.startTime.setAll(y).set(n.selectedStartTime)}}else s.find(".btn-submit-app").show(),s.find(".js-cancel-appointment").css({"margin-left":"-375px"}).show(),s.find(".js-cancel-appointment-associate").css({"margin-right":"5px"}).show()}})):(s.find(".js-conflict-app").show(),s.find("form").attr("action",o.url_create),s.find(".modal-header h5").text("Create an Appointment"),s.find(".btn-submit-app").text("Create Appointment"),s.find(".js-cancel-appointment").hide(),s.find(".js-cancel-appointment-associate").hide(),s.find(".btn-submit-app").show(),s.find(".modal-msg").hide(),s.find(".modal-msg1").hide(),u.customerId.el.select2("readonly",!1),u.activityId.wrap.show(),u.instructorId.wrap.show(),u.bandName.needValid=!1,u.parentFullName.needValid=!1,u.phone.needValid=!1,u.email.needValid=!1,u.bandName.clear().wrap.hide(),u.parentFullName.clear().wrap.hide(),u.phone.clear().wrap.hide(),u.email.clear().wrap.hide(),s.show(),a.show(),u.activityId.el.select2({placeholder:"Select Lesson Type",data:[]}),u.serviceId.el.select2("enable",!1),e.ajax({url:o.url_fetch_servicelist_by_room_or_instructor,data:r,success:function(e){e.serviceList.length&&u.serviceId.setAll(e.serviceList).el.select2("enable",!0),e.startTimeDTOs.length>0&&(r.time&&(_.contains(_.pluck(e.startTimeDTOs,"id"),r.time)||(r.time=r.time.split(":")[0]+":"+(r.time.split(":")[1]*1+15))),u.startTime.setAll(e.startTimeDTOs).set(r.time))}})),i},cancel:function(t,n){var r=this,i=r.conf,s=r.conf.EL,o=r.modal,f=o.formEL,h=r.appointment_model,p=h.get("activityId");ServiceId=h.get("serviceId");if(p!=="400"&&p!=="20"&&p!=="120"&&p!=="100"&&p!=="140"&&p!=="320"&&p!=="200"&&ServiceId===1){s=i.EL,$modal=s.$modal,f=this.modal.formEL,data=r.appointment_model.attributes,appointmentId=h.get("appointmentId"),is_recurring=h.get("isRecurring"),data=r.appointment_model.attributes,message="Please select reason  for cancelling these  Lesson/Rehearsal? ","true"==is_recurring&&(message="Please select reason  for cancelling these  Lesson/Rehearsal? ",e("#cancelAppointmentone").show(),e("#cancelAppointmentall").show()),undefined!==l&&undefined!==c&&(r.appointment_model.get("isRecurring")=="true"?c=1:(r.appointment_model.unset("endDate"),c=0),0==l&&0==c&&(recurringStatus=1),1==l&&1==c&&(recurringStatus=2),1==l&&0==c&&(recurringStatus=3)),r.appointment_model.set("recurringStatus",recurringStatus),data=r.appointment_model.attributes;var d=e("#select-medium").find(":selected").attr("appointmentcancelreasonID"),v=u.dialog({title:"Cancel Appointment",size:"large",message:'<form class="cancel_reason_form" id = "cancel_reason_form" role="cancel_reason_form " style="height:150px"><p>Please select reason  for cancelling these  Lesson/Rehearsal? </p><br><br><input type="radio" id="cancelAppointmentone" class="js-cancel-type" name="Cancel_appointment" value="single" ><span id="cancelonelable">Cancel this Appointment</span></button> &nbsp;&nbsp;<input type="radio" id="cancelAppointmentall" class="js-cancel-type" name="Cancel_appointment" value="more" ><span id="cancelalllable">Cancel all Appointment</span></button><br><br><table style="width:100%"><tr><td><select disabled name="cancelReason" class="select-medium select2" id = "select-medium"><option selected disabled hidden>Select Cancel Reason</option></select></td><td><button type="button" id="cancelAppointment" disabled="disabled" class=" btn btn-important btn-long1 btn-submit-app" style="padding-top:12px;margin-left:5px;margin-right:10px"><span class="ui-button-text">Submit</span></button></td><td><button type="button" id="cancel"  class=" btn  btn-primary btn-close-modal btn-close-app-modal" style="padding-top:7px;  margin-left:-6px;margin-right:20px float: right">Cancel</button></div></td></tr></table ></form>'});e("#cancel").click(function(){v.modal("hide")}),e("#cancelAppointment").click(function(){e(this).prop("disabled",!1);var t=e("#select-medium").find(":selected").attr("appointmentcancelreasonID");Canceltype=e('input[name="Cancel_appointment"]:checked').val(),undefined!==Canceltype?Canceltype=e('input[name="Cancel_appointment"]:checked').val():Canceltype="single","0"!==t?e.ajax({url:i.url_cancel_appointment,type:"POST",data:{data:JSON.stringify(data),cancelType:Canceltype,cancelReason:t},success:function(t){t.status?(e(".cal-item-disabled").remove(),r.hide(),i.afterCreate(),v.modal("hide")):a.autoHideError(t.message)}}):a.autoHideError("Please select the reason for cancellation")});if("true"!==is_recurring){e("#cancelAppointment span").text("Cancel appointment"),e("#cancelAppointmentall").hide(),e("#cancelalllable").hide(),e("#cancelAppointmentone").hide(),e("#cancelonelable").hide(),e("#cancel_reason_form #select-medium").attr("disabled",!1),e("#cancel_reason_form #cancelAppointment").attr("disabled",!1);var m="single";e.ajax({url:i.url_get_cancel_reason,type:"POST",data:{recStatus:m},success:function(t){e("#cancel_reason_form #select-medium").html(""),e("#cancel_reason_form #select-medium").append(e("<option  selected disabled hidden></option>").attr("appointmentcancelreasonID",0).text("Select Cancel Reason")),e.each(t.cancelReasonList,function(t,n){e("#cancel_reason_form #select-medium").append(e("<option></option>").attr("appointmentcancelreasonID",n.appointmentcancelreasonID).text(n.cancelReason))})}})}}else appointmentId=h.get("appointmentId"),is_recurring=h.get("isRecurring"),data=r.appointment_model.attributes,message="Would you like to cancel this appointment?","true"==is_recurring&&(message="Would you like to cancel this occurrence, or all in series?",e(".js-cancel-all").show()),undefined!==l&&undefined!==c&&(r.appointment_model.get("isRecurring")=="true"?c=1:(r.appointment_model.unset("endDate"),c=0),0==l&&0==c&&(recurringStatus=1),1==l&&1==c&&(recurringStatus=2),1==l&&0==c&&(recurringStatus=3)),r.appointment_model.set("recurringStatus",recurringStatus),data=r.appointment_model.attributes,u.dialog({title:"Cancel Appointment",message:message,buttons:{cancelOne:{label:"Cancel this appointment",className:"btn btn-important",callback:function(){e.ajax({url:i.url_cancel_appointment,type:"POST",data:{data:JSON.stringify(data),cancelType:"single",cancelReason:0},success:function(t){t.status?(e(".cal-item-disabled").remove(),r.hide(),i.afterCreate()):a.autoHideError(t.message)}})}},cancelAll:{label:"Cancel all appointments",className:"btn btn-important js-cancel-all",callback:function(){e.ajax({url:i.url_cancel_appointment,type:"POST",data:{data:JSON.stringify(data),cancelType:"more",cancelReason:0},success:function(t){t.status?(e(".cal-item-disabled").remove(),r.hide(),i.afterCreate()):a.autoHideError(t.message)}})}},cancle:{label:"Cancel",className:"btn btn-primary",callback:function(){return}}}}),"true"!==is_recurring&&e(".js-cancel-all").hide()},conflictAppoointments:function(){var t=this,n=t.conf,r=t.appointment_model;t.conflicting_model.showModal(e.extend(!0,{},r.attributes))},hide:function(){var t=this,n=t.conf.EL;return n.find("modal-msg").hide(),n.hide(),e(".mask").hide(),t.dataClear(),t}},h}),define("edithour_modal",["jquery","moment","select2","icheck","mustache","underscore","backbone","hash","pikaday","notification"],function(e,t,n,r,s,o,u,a,f,l){function c(t){this.defaults={url_update:"calendar/updateStudioHour.htm",url_get:"calendar/loadStudioUpdatePage.htm",url_remove:"onetime/deleteOneTime.htm",url_online_remove:"onetime/deleteOnLineTime.htm",url_load:"onetime/loadonetimes.htm",url_online_load:"onetime/loadOnLineTimes.htm",url_updateOnline:"onetime/updateOnLineAvailable.htm",url_instore_load:"instore/loadInstoreTimes.htm",url_instore_remove:"instore/deleteInstoreTime.htm",url_updateInstore:"instore/updateInstoreAvailable.htm",EL:{$modal:e("#modal_edithour"),$mask:e(".mask")}},this.activeDay="sunday",this.currentVersion=undefined,this.groupIndex=0,this.oneTimeActive=!1,this.onLineActive=!1,this.instoreActive=!1,this.instructorId=0,this.init(t)}return c.prototype={init:function(t){this.conf=e.extend({},this.defaults,t);var n=this,r=n.conf.EL,i=r.$modal,s,a,l,c,h;s=u.Model.extend({}),n.hours_model=new s,a=u.Model.extend({}),n.onLinehours_Model=new a,l=u.Model.extend({defaults:{},formEL:{date:{wrap:i.find(".btn-group"),el:i.find(".btn-group .btn"),weekList:["sunday","monday","tuesday","wednesday","thursday","friday","saturday"]},timeFrom:{el:i.find('select[name="timeFrom"]'),set:function(e){this.el.select2("val",e)}},timeTo:{el:i.find('select[name="timeTo"]'),set:function(e){this.el.select2("val",e)}},onetimeFrom:{el:i.find('select[name="onetimeFrom"]'),set:function(e){this.el.select2("val",e)}},onetimeTo:{el:i.find('select[name="onetimeTo"]'),set:function(e){this.el.select2("val",e)}},unavaliable:{el:i.find('input[name="unavailable"]'),set:function(e){e?this.el.iCheck("check"):this.el.iCheck("uncheck")}},oneTimeavailable:{el:i.find('input[name="oneTimeavailable"]'),set:function(e){e?this.el.iCheck("check"):this.el.iCheck("uncheck")}},onLineAvailable:{el:i.find('input[name="onLineAvailable"]'),set:function(e){e?this.el.iCheck("check"):this.el.iCheck("uncheck")}},instoreAvailable:{el:i.find('input[name="instoreAvailable"]'),set:function(e){e?this.el.iCheck("check"):this.el.iCheck("uncheck")}},selectDate:{el:i.find('input[name="selectDate"]')}},getStartTime:function(e){var t=this.formEL,r=t.date,e=r.weekList.indexOf(e);return n.hours_model.get("editHourShowList").list[e].startTimeString},getEndTime:function(e){var t=this.formEL,r=t.date,e=r.weekList.indexOf(e);return n.hours_model.get("editHourShowList").list[e].endTimeString},getStartNewTime:function(e){var t=this.formEL,r=t.date,e=r.weekList.indexOf(e);return n.hours_model.get("editHourShowList").insAvl24HrsList[e].startTimeString},getEndNewTime:function(e){var t=this.formEL,r=t.date,e=r.weekList.indexOf(e);return n.hours_model.get("editHourShowList").insAvl24HrsList[e].endTimeString},setStartTime:function(e,t){var r=this.formEL,i=r.date,e=i.weekList.indexOf(e),s;s=o.clone(n.hours_model.get("editHourShowList").list),s[e].startTimeString=t,n.hours_model.set({list:s})},setEndTime:function(e,t){var r=this.formEL,i=r.date,e=i.weekList.indexOf(e),s;s=o.clone(n.hours_model.get("editHourShowList").list),s[e].endTimeString=t,n.hours_model.set({list:s})},getUnavaliable:function(e){var t=this.formEL,r=t.date,e=r.weekList.indexOf(e);return n.hours_model.get("editHourShowList").list[e].unavaliable},setUnavaliable:function(e,t){var r=this.formEL,i=r.date,e=i.weekList.indexOf(e),s;s=o.clone(n.hours_model.get("editHourShowList").list),s[e].unavaliable=t,n.hours_model.set({list:s})},getOneTimeavailable:function(e){var t=this.formEL,r=t.date,e=r.weekList.indexOf(e);return n.hours_model.get("onetimes").onetimeStartToEnd},getOnLineAvailable:function(e){var t=this.formEL,r=t.date,e=r.weekList.indexOf(e);return n.hours_model.get("onetimes").onetimeStartToEnd},setOneTimeavailable:function(e,t){var r=this.formEL,i=r.date,e=i.weekList.indexOf(e),s;s=o.clone(n.hours_model.get("onetimes")),s[e].onetimeStartToEnd=t,n.hours_model.set({list:s})},setOnLineAvailable:function(e,t){var r=this.formEL,i=r.date,e=i.weekList.indexOf(e),s;s=o.clone(n.hours_model.get("onetimes")),s[e].onetimeStartToEnd=t,n.hours_model.set({list:s})}}),h=new l,n.modal=h,c=h.formEL,c.timeFrom.el.select2().on("change",function(e){h.setStartTime(n.activeDay,this.value)}),c.timeTo.el.select2().on("change",function(e){h.setEndTime(n.activeDay,this.value)}),c.onetimeFrom.el.select2().on("change",function(e){}),c.onetimeTo.el.select2().on("change",function(e){}),c.unavaliable.el.iCheck().on("ifClicked",function(){c.unavaliable.el.iCheck("toggle"),this.checked?h.setUnavaliable(n.activeDay,!0):h.setUnavaliable(n.activeDay,!1)}),c.oneTimeavailable.el.iCheck().on("ifClicked",function(){c.oneTimeavailable.el.iCheck("toggle"),this.checked?(i.find(".hour-select").addClass("dayDisabled"),c.oneTimeavailable.el.iCheck("enable"),c.onLineAvailable.el.iCheck("disable"),c.instoreAvailable.el.iCheck("disable"),c.unavaliable.el.iCheck("disable"),i.find(".jq-selectDate").show(),i.find(".jq-availability").hide(),i.find(".one-time-wrap").show(),i.find(".availability-wrap").hide(),i.find(".breakLineWeb").hide(),i.find(".breakLineWebins").hide(),i.find(".breakLineWebonl").hide(),n.conf.url_update="onetime/updateOnetime.htm",this.groupIndex=i.find(".hour-select").find("button.active").index(),i.find(".hour-select").find("button.active").removeClass("active"),e("#modal_set_availability").addClass("modal_one_time_availability"),c.onetimeTo.set(""),c.onetimeFrom.set(""),n.oneTimeActive=!0,n.onLineActive=!1,n.instoreActive=!1):(i.find(".hour-select").find("button:nth-child("+(this.groupIndex+1)+")").trigger("click"),i.find(".hour-select").removeClass("dayDisabled"),c.oneTimeavailable.el.iCheck("enable"),c.onLineAvailable.el.iCheck("enable"),c.instoreAvailable.el.iCheck("enable"),c.unavaliable.el.iCheck("enable"),i.find(".jq-selectDate").hide(),i.find(".jq-availability").show(),i.find(".one-time-wrap").hide(),i.find(".availability-wrap").show(),i.find(".breakLineWeb").show(),i.find(".breakLineWebins").show(),i.find(".breakLineWebonl").show(),n.conf.url_update="calendar/updateStudioHour.htm",e("#modal_set_availability").removeClass("modal_one_time_availability"),n.oneTimeActive=!1,n.onLineActive=!1,n.instoreActive=!1)}),c.onLineAvailable.el.iCheck().on("ifClicked",function(){c.onLineAvailable.el.iCheck("toggle"),this.checked?(c.unavaliable.el.iCheck("disable"),c.oneTimeavailable.el.iCheck("disable"),c.onLineAvailable.el.iCheck("enable"),c.instoreAvailable.el.iCheck("disable"),n.conf.url_updateOnline="onetime/updateOnLineAvailable.htm",i.find(".jq-availability").hide(),i.find(".jq-displayDate").hide(),i.find(".one-time-wrap").hide(),i.find(".availability-wrap").hide(),i.find(".instore-time-wrap").hide(),i.find(".onlineView-time-wrap").hide(),i.find(".instoreView-time-wrap").show(),i.find(".online-time-wrap").show(),i.find(".online-time-wrap").show(),i.find(".jq-selectDate").show(),i.find(".breakLineWeb").hide(),i.find(".breakLineWebins").hide(),i.find(".breakLineWebonl").show(),n.oneTimeActive=!1,n.instoreActive=!1,n.onLineActive=!0):(i.find(".online-time-wrap").hide(),c.oneTimeavailable.el.iCheck("enable"),c.onLineAvailable.el.iCheck("enable"),c.instoreAvailable.el.iCheck("enable"),c.unavaliable.el.iCheck("enable"),i.find(".jq-selectDate").hide(),i.find(".jq-availability").show(),i.find(".availability-wrap").show(),n.conf.url_update="calendar/updateStudioHour.htm",i.find(".jq-displayDate").show(),i.find(".online-time-wrap").hide(),i.find(".onlineView-time-wrap").hide(),i.find(".instore-time-wrap").hide(),i.find(".instoreView-time-wrap").hide(),i.find(".breakLineWeb").show(),i.find(".breakLineWebins").show(),i.find(".breakLineWebonl").show(),n.oneTimeActive=!1,n.onLineActive=!1,n.instoreActive=!1)}),c.instoreAvailable.el.iCheck().on("ifClicked",function(){c.instoreAvailable.el.iCheck("toggle"),this.checked?(c.unavaliable.el.iCheck("disable"),c.oneTimeavailable.el.iCheck("disable"),c.onLineAvailable.el.iCheck("disable"),c.instoreAvailable.el.iCheck("enable"),i.find(".instore-time-wrap").show(),n.conf.url_updateInstore="instore/updateInstoreAvailable.htm",i.find(".jq-selectDate").show(),i.find(".jq-availability").hide(),i.find(".jq-displayDate").hide(),i.find(".one-time-wrap").hide(),i.find(".availability-wrap").hide(),i.find(".instore-time-wrap").show(),i.find(".onlineView-time-wrap").show(),i.find(".instoreView-time-wrap").hide(),i.find(".online-time-wrap").hide(),i.find(".breakLineWeb").hide(),i.find(".breakLineWebins").show(),i.find(".breakLineWebonl").hide(),n.oneTimeActive=!1,n.onLineActive=!1,n.instoreActive=!0):(c.unavaliable.el.iCheck("enable"),c.oneTimeavailable.el.iCheck("enable"),c.onLineAvailable.el.iCheck("enable"),i.find(".jq-selectDate").hide(),i.find(".jq-availability").show(),i.find(".availability-wrap").show(),i.find(".jq-displayDate").show(),n.conf.url_update="calendar/updateStudioHour.htm",i.find(".online-time-wrap").hide(),i.find(".onlineView-time-wrap").hide(),i.find(".instore-time-wrap").hide(),i.find(".instoreView-time-wrap").hide(),i.find(".breakLineWeb").show(),i.find(".breakLineWebins").show(),i.find(".breakLineWebonl").show(),n.oneTimeActive=!1,n.onLineActive=!1,n.instoreActive=!1)}),c.selectDate.el.on("change",function(){}),selectDate=new f({format:"MM/DD/YYYY",field:c.selectDate.el[0]}),c.date.el.click(function(){var t=e(this),r,i,s,o,u,a;t.addClass("active").siblings().removeClass("active"),a=t.data("day"),n.activeDay=a,r=h.getStartTime(a),i=h.getEndTime(a),s=h.getStartNewTime(a),o=h.getEndNewTime(a),u=h.getUnavaliable(a),c.timeTo.set(i),c.timeFrom.set(r),n.oneTimeActive||(c.onetimeTo.set(o),c.onetimeFrom.set(s)),c.unavaliable.set(u)})},renderOneTime:function(t){var n=this,r=n.conf,i=n.conf.EL,s=i.$modal,o="",u=t;for(var a=0;a<u.length;a++)u[a].onetimeStartToEnd!=""&&(o+='<p><a class="icon icon-remove js-remove-oneTime" data-id="'+u[a].onetimeId+'" href="javascript:;">x</a>'+u[a].onetimeStartToEnd+"</p>");s.find(".one-time-list").html(o),s.find(".js-remove-oneTime").on("click",function(){var t=e(this).attr("data-id")*1;_this=e(this),n.removeForId(t,_this)})},renderOnline:function(t){var n=this,r=n.conf,i=n.conf.EL,s="",o=i.$modal,u="",a=t;for(var f=0;f<a.length;f++)a[f].onetimeStartToEnd!=""&&(u+='<p><a class="icon icon-remove js-remove-onlineTime" data-id="'+a[f].onlineAvailabilityId+'" href="javascript:;">x</a>'+a[f].onetimeStartToEnd+"</p>",s+="<br>");o.find(".online-time-list").html(u),e(".breakLineWebonl").html(s),o.find(".js-remove-onlineTime").on("click",function(){var t=e(this).attr("data-id")*1;_this=e(this),n.removeForOnLineId(t,_this)})},renderOnlineView:function(t){var n=this,r=n.conf,i=n.conf.EL,s=i.$modal,o="",u="",a=t;for(var f=0;f<a.length;f++)a[f].onetimeStartToEnd!=""&&(u+="<p>"+a[f].onetimeStartToEnd+"</p>",o+="<br>");s.find(".onlineView-time-list").html(u),e(".breakLineWebonl").html(o)},renderInstore:function(t){var n=this,r=n.conf,i=n.conf.EL,s=i.$modal,o="",u="",a=t;for(var f=0;f<a.length;f++)a[f].onetimeStartToEnd!=""&&(u+='<p><a class="icon icon-remove js-remove-instoreTime" data-id="'+a[f].instoreAvailabilityId+'" href="javascript:;">x</a>'+a[f].onetimeStartToEnd+"</p>",o+="<br>");s.find(".instore-time-list").html(u),e(".breakLineWebonl").html(o),s.find(".js-remove-instoreTime").on("click",function(){var t=e(this).attr("data-id")*1;_this=e(this),n.removeForInstoreId(t,_this)})},renderInstoreView:function(t){var n=this,r=n.conf,i=n.conf.EL,s=i.$modal,o="",u="",a=t;for(var f=0;f<a.length;f++)a[f].onetimeStartToEnd!=""&&(u+="<p>"+a[f].onetimeStartToEnd+"</p>",o+="<br>");e(".breakLineWebins").html(o),s.find(".instoreView-time-list").html(u)},renderTimeList:function(){var t=this,n=t.conf.EL,r=n.$modal,i="",s="",o="",u=t.temp_hours_model,a=t.hours_model.get("onetimes");if(t.onLineActive)for(var f=0;f<u.length;f++){var l=u[f].startTimeString,c=u[f].endTimeString,s="",h=u[f].weekDay;l!=""&&(i+='<p><a class="icon icon-remove js-remove-oneTime" data-id="111" href="javascript:;">x</a>'+h+": "+l+" - "+c+"</p>"),s+="<br>"}else for(var f=0;f<u.length;f++){var l=u[f].startTimeString,c=u[f].endTimeString,s="",h=u[f].weekDay;l!=""&&(i+="<p>"+h+": "+l+" - "+c+"</p>"),s+="<br>"}for(var f=0;f<a.length;f++){var p=t.hours_model.get("onetimes")[f];p.onetimeStartToEnd!=""&&(o+='<p><a class="icon icon-remove js-remove-oneTime" data-id="'+p.onetimeId+'" href="javascript:;">x</a>'+p.onetimeStartToEnd+"</p>")}r.find(".studio-hour-list").html(i),r.find(".one-time-list").html(o),e(".breakLineWeb").html(s),r.find(".js-remove-oneTime").on("click",function(){var n=e(this).attr("data-id")*1,r=e(this);t.removeForId(n,r)})},removeForId:function(t,n){var r=this,s=r.conf,o=s.EL,u=o.$mask,a=o.$modal,f=n;e.ajax({url:s.url_remove,type:"POST",data:{oneTimeId:t},success:function(t){t.status?e.ajax({url:s.url_load,type:"POST",data:{instructorId:r.instructorId},success:function(t){var n="",s="",o=t.onetimes;r.renderOneTime(t.onetimes);if(o.length>0)for(i in o)n+='<p class="row">'+o[i].onetimeStartToEnd+"</p>";e(".js-oneTimeAvailability-wrap").empty().html(n),e(".breakLineWeb").html("<br>")}}):l.autoHideError(t.message)}})},removeForOnLineId:function(t,n){var r=this,s=r.conf,o=s.EL,u=o.$mask,a=o.$modal,f=n;e.ajax({url:s.url_online_remove,type:"POST",data:{onLineTimeId:t},success:function(t){t.status?e.ajax({url:s.url_online_load,type:"POST",data:{instructorId:r.instructorId},success:function(t){var n="",s="",o=t.onlinetimes;r.renderOnline(t.onlinetimes);if(o.length>0)for(i in o)n+='<p class="row">'+o[i].onetimeStartToEnd+"</p>";e(".onlineView-time-list").empty().html(n),e(".breakLineWeb").html("<br>")}}):l.autoHideError(t.message)}})},removeForInstoreId:function(t,n){var r=this,s=r.conf,o=s.EL,u=o.$mask,a=o.$modal,f=n;e.ajax({url:s.url_instore_remove,type:"POST",data:{instoreTimeId:t},success:function(t){t.status?e.ajax({url:s.url_instore_load,type:"POST",data:{instructorId:r.instructorId},success:function(t){var n="",s="",o=t.instoretimes;r.renderInstore(t.instoretimes);if(o.length>0)for(i in o)n+='<p class="row">'+o[i].onetimeStartToEnd+"</p>";e(".instoreView-time-list").empty().html(n),e(".breakLineWeb").html("<br>")}}):l.autoHideError(t.message)}})},showModal:function(t){var n=this,r=n.conf,i=r.EL,s=i.$mask,o=i.$modal,u=e(window).height(),a=e(window).width(),f=533,l=246,c=(u-l)/2+e(window).scrollTop(),h=(a-f)/2;c<0&&(c=10),h<0&&(h=10),o.css({left:h,top:c}),this.instructorModel=t,this.instructorModel.set("idVal",t.get("id"));if(t==undefined)e.ajax({url:r.url_get,type:"GET",success:function(e){typeof e=="string"&&e.indexOf("DOCTYPE")!==-1&&location.reload(),n.hours_model.clear(),n.hours_model.set(e),o.find(".sunday-hour").trigger("click"),n.renderTimeList(),o.show(),s.show()}});else{if(!n.oneTimeActive){if(n.hours_model.get("id")!=t.get("id")||n.currentVersion==n.hours_model.get("versionString"))n.hours_model.clear(),n.temp_hours_model=e.extend(!0,[],t.get("editHourShowList").list),n.hours_model.set(t.attributes),n.currentVersion=t.versionString;this.instructorId=t.id,e.ajax({url:r.url_online_load,type:"POST",data:{instructorId:t.id},success:function(e){n.renderOnline(e.onlinetimes)}}),e.ajax({url:r.url_instore_load,type:"POST",data:{instructorId:t.id},success:function(e){n.renderInstoreView(e.instoretimes)}}),o.find(".sunday-hour").trigger("click"),n.renderTimeList(),o.show(),s.show()}if(!n.oneTimeActive){if(n.hours_model.get("id")!=t.get("id")||n.currentVersion==n.hours_model.get("versionString"))n.hours_model.clear(),n.temp_hours_model=e.extend(!0,[],t.get("editHourShowList").list),n.hours_model.set(t.attributes),n.currentVersion=t.versionString;this.instructorId=t.id,e.ajax({url:r.url_instore_load,type:"POST",data:{instructorId:t.id},success:function(e){n.renderInstore(e.instoretimes)}}),e.ajax({url:r.url_online_load,type:"POST",data:{instructorId:t.id},success:function(e){n.renderOnlineView(e.onlinetimes)}}),o.find(".sunday-hour").trigger("click"),n.renderTimeList(),o.show(),s.show()}if(n.hours_model.get("id")!=t.get("id")||n.currentVersion==n.hours_model.get("versionString"))n.hours_model.clear(),n.temp_hours_model=e.extend(!0,[],t.get("editHourShowList").list),n.hours_model.set(t.attributes),n.currentVersion=t.versionString;this.instructorId=t.id,e.ajax({url:r.url_load,type:"POST",data:{instructorId:t.id},success:function(e){n.renderOneTime(e.onetimes)}}),o.find(".sunday-hour").trigger("click"),n.renderTimeList(),o.show(),s.show()}},update:function(t){var n=this,r=n.conf,o=r.EL,u=o.$modal,f=e(t.currentTarget);formEL=n.modal.formEL,selectDate=formEL.selectDate.el.val(),_timeFrom=formEL.onetimeFrom.el.val(),_timeTo=formEL.onetimeTo.el.val(),f.attr("disabled",!0);var c=n.instructorModel.get("idVal"),h={startDate:selectDate,fromTime:_timeFrom,toTime:_timeTo,instructorId:c};if(n.oneTimeActive)e.ajax({url:r.url_update,type:"POST",contentType:"application/json",data:JSON.stringify(h),success:function(t){var r="",s="";if(t.status){if(t.dto.length>0)for(i in t.dto)r+='<p class="row">'+t.dto[i].onetimeStartToEnd+"</p>",s+='<p><a class="icon icon-remove js-remove-oneTime" data-id="'+t.dto[i].onetimeId+'" href="javascript:;">x</a>'+t.dto[i].onetimeStartToEnd+"</p>";e(".js-oneTimeAvailability-wrap").empty().html(r),e(".one-time-list").empty().html(s),e(".breakLineWeb").html("<br>"),u.find(".js-remove-oneTime").on("click",function(){var t=e(this).attr("data-id")*1,r=e(this);n.removeForId(t,r)}),formEL.selectDate.el.val(""),formEL.onetimeTo.set(""),formEL.onetimeFrom.set("")}else l.autoHideError(t.message);f.attr("disabled",!1)}});else if(n.onLineActive&&!n.oneTimeActive&&!n.instoreActive){var p=u.find('select[name="onetimeFrom"]').val();this.groupIndex=u.find(".hour-select").find("button.active").index();var d=this.groupIndex;h={startDate:selectDate,fromTime:_timeFrom,toTime:_timeTo,instructorId:c,weekDay:d},e.ajax({url:r.url_updateOnline,type:"POST",contentType:"application/json",data:JSON.stringify(h),success:function(t){var r="",s="";if(t.status){if(t.dto&&t.dto.length>0){set=t.dto;for(i in t.dto)s+='<p><a class="icon icon-remove js-remove-onlineTime" data-id="'+t.dto[i].onlineAvailabilityId+'" href="javascript:;">x</a>'+t.dto[i].onetimeStartToEnd+"</p>";for(i in t.dto)r+='<p class="row">'+t.dto[i].onetimeStartToEnd+"</p>"}e(".online-time-list").empty().html(s),e(".onlineView-time-list").empty().html(r),u.find(".js-remove-onlineTime").on("click",function(){var t=e(this).attr("data-id")*1,r=e(this);n.removeForOnLineId(t,r)})}else l.autoHideError(t.message);f.attr("disabled",!1)}})}else if(!n.onLineActive&&!n.oneTimeActive&&n.instoreActive){var p=u.find('select[name="onetimeFrom"]').val();this.groupIndex=u.find(".hour-select").find("button.active").index();var d=this.groupIndex;h={startDate:selectDate,fromTime:_timeFrom,toTime:_timeTo,instructorId:c,weekDay:d},e.ajax({url:r.url_updateInstore,type:"POST",contentType:"application/json",data:JSON.stringify(h),success:function(t){var r="",s="";if(t.status){if(t.dto&&t.dto.length>0){set=t.dto;for(i in t.dto)s+='<p><a class="icon icon-remove js-remove-instore" data-id="'+t.dto[i].instoreAvailabilityId+'" href="javascript:;">x</a>'+t.dto[i].onetimeStartToEnd+"</p>";for(i in t.dto)r+='<p class="row">'+t.dto[i].onetimeStartToEnd+"</p>"}e(".instore-time-list").empty().html(s),e(".instoreView-time-list").empty().html(r),u.find(".js-remove-instore").on("click",function(){var t=e(this).attr("data-id")*1,r=e(this);n.removeForInstoreId(t,r)})}else l.autoHideError(t.message);f.attr("disabled",!1)}})}else h=n.hours_model.get("editHourShowList"),e.ajax({url:r.url_update,type:"POST",contentType:"application/json",data:JSON.stringify(h),success:function(t){if(t.status){var r,i="";if(t.editHourShowDTOs&&t.editHourShowDTOs.length>0)r=t.editHourShowDTOs,r.forEach(function(e){var t=e.startTimeString,n=e.endTimeString,r=e.weekDay;t!=""&&(i+='<p class="row"><span class="span2">'+r+":</span>"+t+" - "+n+"</p>")}),e(".js-availability-wrap:visible").html(i),e(".instructor-list tr.disabled").find("th").eq(3).text(t.availabilityString),n.hours_model.set("versionString",t.availabilityversion),n.temp_hours_model=e.extend(!0,[],r),n.hours_model.set("list",r),h.list=r,n.hideModal();else{r=t.set,r.forEach(function(e){i+=s.render('<div class="row"><span class="span5">{{dayString}}</span><span class="span7">{{timeString}}</span></div>',e)});if(e(".top-nav-list .active").hasClass("js-show-schedule")){var o=a.get("view");switch(o){case"day":var u=a.get("filter");u==="room"?e("#cal_filter_room").trigger("click"):e("#cal_filter_instructor").trigger("click");break;case"week":e("#cal_view_week").trigger("click");break;default:}}e(".studio-hour").html(i),n.hideModal()}}else l.autoHideError(t.message);f.attr("disabled",!1)}})},hide:function(e){var t=this,n=t.conf.EL,r=t.modal.formEL;n.$modal.hide(),n.$mask.hide()},hideModal:function(){var t=this,n=t.conf.EL,r=t.modal.formEL;t.hours_model.set("list",e.extend(!0,[],t.temp_hours_model)),n.$modal.hide(),n.$mask.hide(),n.$modal.find(".btn-group .active").removeClass("active"),r.unavaliable.el.iCheck("uncheck"),r.selectDate.el.val(""),r.timeTo.set(""),r.timeFrom.set(""),r.onetimeTo.set(""),r.onetimeFrom.set(""),this.groupIndex=0}},c}),define("studiohour_modal",["jquery","moment","select2","icheck","mustache","underscore","backbone","hash","notification"],function(e,t,n,r,i,s,o,u,a){function f(t){this.defaults={url_update:"calendar/updateStudioHour.htm",url_get:"calendar/loadStudioUpdatePage.htm",EL:{$modal:e("#modal_edithour"),$mask:e(".mask")}},this.activeDay="sunday",this.currentVersion=undefined,this.init(t)}return f.prototype={init:function(t){this.conf=e.extend({},this.defaults,t);var n=this,r=n.conf.EL,i=r.$modal,u,a,f,l;u=o.Model.extend({}),n.hours_model=new u,a=o.Model.extend({defaults:{},formEL:{date:{wrap:i.find(".btn-group"),el:i.find(".btn-group .btn"),weekList:["sunday","monday","tuesday","wednesday","thursday","friday","saturday"]},timeFrom:{el:i.find('select[name="timeFrom"]'),set:function(e){this.el.select2("val",e)}},timeTo:{el:i.find('select[name="timeTo"]'),set:function(e){this.el.select2("val",e)}},unavaliable:{el:i.find('input[name="unavailable"]'),set:function(e){e?this.el.iCheck("check"):this.el.iCheck("uncheck")}}},getStartTime:function(e){var t=this.formEL,r=t.date,e=r.weekList.indexOf(e);return n.hours_model.attributes.list[e].startTimeString},getEndTime:function(e){var t=this.formEL,r=t.date,e=r.weekList.indexOf(e);return n.hours_model.get("list")[e].endTimeString},setStartTime:function(e,t){var r=this.formEL,i=r.date,e=i.weekList.indexOf(e),o;o=s.clone(n.hours_model.get("list")),o[e].startTimeString=t,n.hours_model.set({list:o})},setEndTime:function(e,t){var r=this.formEL,i=r.date,e=i.weekList.indexOf(e),o;o=s.clone(n.hours_model.get("list")),o[e].endTimeString=t,n.hours_model.set({list:o})},getUnavaliable:function(e){var t=this.formEL,r=t.date,e=r.weekList.indexOf(e);return n.hours_model.attributes.list[e].unavaliable},setUnavaliable:function(e,t){var r=this.formEL,i=r.date,e=i.weekList.indexOf(e),o;o=s.clone(n.hours_model.get("list")),o[e].unavaliable=t,n.hours_model.set({list:o})}}),l=new a,n.modal=l,f=l.formEL,f.timeFrom.el.select2().on("change",function(e){l.setStartTime(n.activeDay,this.value)}),f.timeTo.el.select2().on("change",function(e){l.setEndTime(n.activeDay,this.value)}),f.unavaliable.el.iCheck().on("ifClicked",function(){f.unavaliable.el.iCheck("toggle"),this.checked?l.setUnavaliable(n.activeDay,!0):l.setUnavaliable(n.activeDay,!1)}),f.date.el.click(function(){var t=e(this),r,i,s,o;t.addClass("active").siblings().removeClass("active"),o=t.data("day"),n.activeDay=o,r=l.getStartTime(o),i=l.getEndTime(o),s=l.getUnavaliable(o),f.timeTo.set(i),f.timeFrom.set(r),f.unavaliable.set(s)})},renderTimeList:function(){var e=this,t=e.conf.EL,n=t.$modal,r="",i=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"];for(var s=0;s<i.length;s++){var o=e.hours_model.attributes.list[s].startTimeString,u=e.hours_model.attributes.list[s].endTimeString,a=e.hours_model.attributes.list[s].weekDay;o!=""&&(r+="<p>"+a+": "+o+" - "+u+"</p>")}n.find(".studio-hour-list").html(r)},showModal:function(t){var n=this,r=n.conf,i=r.EL,s=i.$mask,o=i.$modal,u=e(window).height(),a=e(window).width(),f=533,l=246,c=(u-l)/2+e(window).scrollTop(),h=(a-f)/2;c<0&&(c=10),h<0&&(h=10),o.css({left:h,top:c});if(t==undefined)e.ajax({url:r.url_get,type:"GET",success:function(e){typeof e=="string"&&e.indexOf("DOCTYPE")!==-1&&location.reload(),n.hours_model.clear(),n.hours_model.set(e),o.find(".sunday-hour").trigger("click"),n.renderTimeList(),o.show(),s.show()}});else{if(n.hours_model.get("idString")!=t.idString||n.currentVersion==n.hours_model.get("versionString"))n.hours_model.clear(),n.temp_hours_model=e.extend(!0,[],t.list),n.hours_model.set(t),n.currentVersion=t.versionString;o.find(".sunday-hour").trigger("click"),n.renderTimeList(),o.show(),s.show()}},update:function(t){var n=this,r=n.conf,s=r.EL,o=s.$modal,f=n.hours_model.attributes,l=e(t.currentTarget);l.attr("disabled",!0),e.ajax({url:r.url_update,type:"POST",contentType:"application/json",data:JSON.stringify(f),success:function(t){if(t.status){var r,s="";if(t.editHourShowDTOs&&t.editHourShowDTOs.length>0)r=t.editHourShowDTOs,r.forEach(function(e){var t=e.startTimeString,n=e.endTimeString,r=e.weekDay;t!=""&&(s+='<p class="row"><span class="span2">'+r+":</span>"+t+" - "+n+"</p>")}),e(".js-availability-wrap:visible").html(s),e(".instructor-list tr.disabled").find("th").eq(3).text(t.availabilityString),n.hours_model.set("versionString",t.availabilityversion),n.temp_hours_model=e.extend(!0,[],r),n.hours_model.set("list",r),n.hideModal();else{r=t.set,r.forEach(function(e){s+=i.render('<div class="row"><span class="span5">{{dayString}}</span><span class="span7">{{timeString}}</span></div>',e)});if(e(".top-nav-list .active").hasClass("js-show-schedule")){var o=u.get("view");switch(o){case"day":var f=u.get("filter");f==="room"?e("#cal_filter_room").trigger("click"):e("#cal_filter_instructor").trigger("click");break;case"week":e("#cal_view_week").trigger("click");break;default:}}e(".studio-hour").html(s),n.hideModal()}}else a.autoHideError(t.message);l.attr("disabled",!1)}})},hideModal:function(){var t=this,n=t.conf.EL,r=t.modal.formEL;t.hours_model.set("list",e.extend(!0,[],t.temp_hours_model)),n.$modal.hide(),n.$mask.hide(),n.$modal.find(".btn-group .active").removeClass("active"),r.unavaliable.el.iCheck("uncheck"),r.timeFrom.set(""),r.timeTo.set("")}},f}),define("profiletimeoff_modal",["jquery","moment","select2","icheck","mustache","underscore","backbone","pikaday","notification"],function(e,t,n,r,s,o,u,a,f){function l(t){this.defaults={url_update:"profileTimeoff/updateProfileTimeoff.htm",url_load:"profileTimeoff/loadProfileTimeoffs.htm",url_remove:"profileTimeoff/deleteProfileTimeoff.htm",url_fetch_profile_start_time:"profileTimeoff/getStartTimeByProfile.htm",url_cancel_load:"profileTimeoff/loadCancelProfileTimeoffs.htm",EL:{$modal:e("#modal_profile_timeoff"),$mask:e(".mask")}},this.currentVersion=undefined,this.init(t)}return l.prototype={init:function(t){this.conf=e.extend({},this.defaults,t);var n=this,r=n.conf,i=n.conf.EL,s=i.$modal,o,l,c,h,p;o=u.Model.extend({}),n.Profiletimeoff_Modal=new o,l=u.Model.extend({defaults:{},formEL:{dateFrom:{el:s.find('input[name="timeOffStartDate"]')},timeFrom:{el:s.find('input[name="timeOffFrom"]'),set:function(e){this.el.select2("val",e)},clear:function(){this.el.select2("val",""),this.el.select2("enable",!1)},setAll:function(e){return this.el.select2({data:e}),this}},timeTo:{el:s.find('input[name="timeOffTo"]'),set:function(e){this.el.select2("val",e)},clear:function(){this.el.select2("val",""),this.el.select2("enable",!1)},setAll:function(e){return this.el.select2({data:e}),this}}}}),h=new l,n.modal=h,c=h.formEL,p=new a({format:"MM/DD/YYYY",field:c.dateFrom.el[0]}),c.dateFrom.el.on("change",function(t){var n=this.value;c.timeTo.clear(),c.timeFrom.clear(),e.ajax({url:r.url_fetch_profile_start_time,type:"GET",data:{startDate:n},success:function(e){e.startTimeDTOs&&e.startTimeDTOs.length>0?(c.timeFrom.setAll(e.startTimeDTOs),c.timeFrom.el.select2("enable",!0),c.timeTo.setAll(e.startTimeDTOs),c.timeTo.el.select2("enable",!0)):f.autoHideError(e.message)}})})},initDate:function(){e('input[name="startDate"]').val(t(new Date).format("L")),e('input[name="endDate"]').val(t(new Date).format("L"))},renderTimeOffList:function(t){var n=this,r=n.conf,s=n.conf.EL,o=s.$modal,u="",a=t;if(a.length>0)for(i in a)u+='<p><a class="icon icon-remove js-remove-timeoff" data-id="'+a[i].profiletimeoffId+'" href="javascript:;">x</a>'+a[i].timeOffStartToEnd+"</p>";o.find(".timeoff-list").empty().html(u),o.find(".js-remove-timeoff").on("click",function(){var t=e(this).attr("data-id")*1;e.ajax({url:r.url_remove,type:"POST",data:{profileTimeoffId:t},success:function(t){var r="",s=t.profileTimeoffs;if(t.status){n.renderTimeOffList(t.profileTimeoffs);if(s.length>0)for(i in s)r+='<p class="row">'+s[i].timeOffStartToEnd+"</p>";e(".js-timeoff-wrap").empty().html(r)}else f.autoHideError(t.message)}})})},showModal:function(t){var n=this,r=n.conf,i=r.EL,s=i.$mask,o=i.$modal,u=e(window).height(),a=e(window).width(),f=533,l=246,c=(u-l)/2+e(window).scrollTop(),h=(a-f)/2;c<0&&(c=10),h<0&&(h=10),o.css({left:h,top:c}),$this=e(t.currentTarget),e.ajax({url:r.url_load,type:"GET",data:{siteID:1},success:function(e){n.renderTimeOffList(e.profileTimeoffs)}}),o.show(),s.show()},update:function(t){var n=this,r=n.conf,s=n.modal.formEL,o=s.dateFrom.el.val(),u=s.timeFrom.el.val(),a=s.timeTo.el.val();id=0,data={fromTime:u,fromDate:o,toTime:a};var l=e(".btn-update-timeoff-pr");l.prop("disabled",!0),e.ajax({url:r.url_update,type:"POST",contentType:"application/json",data:JSON.stringify(data),success:function(t){if(t.status){var r=t.dto,s="",o="",u="";if(r.length>0)for(i in r)s+='<p class="row">'+r[i].timeOffStartToEnd+"</p>";e(".js-timeoff-wrap").empty().html(s);if(r.length>0)for(i in r)u+='<p><a class="icon icon-remove js-remove-timeoff" data-id="'+r[i].profiletimeoffId+'" href="javascript:;">x</a>'+r[i].timeOffStartToEnd+"</p>";e(".timeoff-list").empty().html(u),n.cleanDate(),n.cleanTime(),n.renderTimeOffList(t.dto)}else f.autoHideError(t.message);l.prop("disabled",!1)}})},hideModal:function(t){var n=this,r=n.conf,i=r.EL,s=n.modal.formEL;e.ajax({url:r.url_cancel_load,type:"GET",data:{siteID:1},success:function(t){if(t.status){var n,r="";t.loadProfileTimeOff&&t.loadProfileTimeOff.length>0?(n=t.loadProfileTimeOff,n.forEach(function(e){var t=e.dayString,n=e.timeString,i=e.dayString;t!=""&&(r+='<p class="row"><span class="span5">'+i+'</span> <span class="span7">'+n+"</span></p>")}),e(".studio-time-off-hour").html(r)):(n=t.set,r="",e(".studio-time-off-hour").html(r))}else f.autoHideError(t.message)}}),i.$modal.hide(),i.$mask.hide(),n.cleanDate(),n.cleanTime()},cleanDate:function(){var e=this,t=e.conf.EL,n=e.modal.formEL;n.dateFrom.el.val("")},cleanTime:function(){var e=this,t=e.conf.EL,n=e.modal.formEL;n.timeFrom.set(""),n.timeTo.set(""),n.timeFrom.clear(),n.timeTo.clear()}},l}),define("customeredit_modal",["jquery","moment","select2","icheck","mustache","underscore","backbone","pikaday","notification"],function(e,t,n,r,i,s,o,u,a){function f(t){this.defaults={url_update:"customer/updateCustomerEditDetailsInfo.htm",url_load:"customer/getCustomerEditDetailsInfo.htm",url_remove:"profileTimeoff/deleteProfileTimeoff.htm",url_fetch_profile_start_time:"profileTimeoff/getStartTimeByProfile.htm",url_cancel_load:"profileTimeoff/loadCancelProfileTimeoffs.htm",EL:{$modal:e("#modal_edit_cust"),$mask:e(".mask")}},this.currentVersion=undefined,this.init(t)}return f.prototype={init:function(t){this.conf=e.extend({},this.defaults,t);var n=this,r=n.conf,i=n.conf.EL,s=i.$modal,a,f,l,c,h;a=o.Model.extend({}),n.Customeredit_Modal=new a,f=o.Model.extend({defaults:{},formEL:{dateFrom:{el:s.find('input[name="timeOffStartDate"]')},customerFullName:{el:s.find('input[name="customerFullName"]'),set:function(e){return this.el.val(e).text(e),a.set("customerFullName",e),this},clear:function(){return this.el.val("").text(""),appointment_model.set("customerFullName"),this},needValid:!1},gcId:{el:s.find('input[name="gcId"]'),set:function(e){return this.el.val(e).text(e),a.set("gcId",e),this},clear:function(){return this.el.val("").text(""),a.set("gcId"),this},needValid:!1},secondaryEmail:{el:s.find('input[name="secondaryEmail"]'),set:function(e){return this.el.val(e).text(e),a.set("secondaryEmail",e),this},clear:function(){return this.el.val("").text(""),a.set("secondaryEmail"),this},needValid:!1},searchCustCriteria:{el:s.find('input[name="searchCustCriteria"]'),set:function(e){return this.el.val(e).text(e),a.set("searchCustCriteria",e),this},clear:function(){return this.el.val("").text(""),a.set("searchCustCriteria"),this},needValid:!1},phoneNumber:{el:s.find('input[name="phoneNumber"]'),set:function(e){return this.el.val(e).text(e),a.set("phoneNumber",e),this},clear:function(){return this.el.val("").text(""),a.set("phoneNumber"),this},needValid:!1}}}),c=new f,n.modal=c,l=c.formEL,h=new u({format:"MM/DD/YYYY",field:l.dateFrom.el[0]})},initDate:function(){},renderTimeOffList:function(e){var t=this,n=t.conf,r=t.conf.EL,i=r.$modal,s="",o=e;i.find('input[name="customerFullName"]').val(o.customerFullName).attr("readonly",""),i.find('input[name="email"]').val(o.email).attr("readonly",""),i.find('input[name="secondaryEmail"]').val(o.secondaryEmail),i.find('input[name="phoneNumber"]').val(o.phoneNumber).attr("readonly",""),i.find('input[name="gcId"]').val(o.gcId),i.find("#customerFullNamelb").text(o.customerFullName),i.find("#emaillb").text(o.email),i.find("#customerStatuslb").text(o.customerStatus),i.find("#customerExternalIdlb").text(o.customerExternalId),i.find("#lessonsCountlb").text(o.lessonsCount),i.find("#phoneNumberlb").text(o.phoneNumber)},showModal:function(t,n){var r=this,i=r.conf,s=i.EL,o=s.$mask,u=s.$modal,a=e(window).height(),f=e(window).width(),l=533,c=246,h=(a-c)/2+e(window).scrollTop(),p=(f-l)/2;h<0&&(h=10),p<0&&(p=10),u.css({left:p,top:h}),e.ajax({url:i.url_load,type:"GET",data:{customerId:t},success:function(e){r.renderTimeOffList(e),r.Customeredit_Modal.set(e),u.find('input[name="searchCustCriteria"]').val(n)}}),u.show(),o.show()},update:function(t){var n=this,r=n.conf,i=n.modal.formEL,s=i.customerFullName.el.val(),o=i.gcId.el.val(),u=i.secondaryEmail.el.val(),f=i.phoneNumber.el.val(),l=0,c={customerFullName:s,gcId:o,secondaryEmail:u,phoneNumber:f},h=e(".btn-update-edit-cust");h=e(t.currentTarget),h.prop("disabled",!0),e.ajax({url:r.url_update,type:"POST",contentType:"application/json",data:JSON.stringify(c),success:function(e){if(e.status){var t=e.dto,r="",i="",s="";n.hideModal()}else a.autoHideError(e.message);h.prop("disabled",!1)}})},hideModal:function(t){var n=this,r=n.conf,i=r.EL,s=n.modal.formEL;e.ajax({url:r.url_cancel_load,type:"GET",data:{siteID:1},success:function(e){if(e.status)var t,n="";else a.autoHideError(e.message)}}),i.$modal.hide(),i.$mask.hide(),n.cleanDate(),n.cleanTime(),e(".btn-search-customer").trigger("click")},cleanDate:function(){var e=this,t=e.conf.EL,n=e.modal.formEL;n.dateFrom.el.val("")},cleanTime:function(){var e=this,t=e.conf.EL,n=e.modal.formEL}},f}),!function(e){var t="0.9.3",n={isMsie:function(){var e=/(msie) ([\w.]+)/i.exec(navigator.userAgent);return e?parseInt(e[2],10):!1},isBlankString:function(e){return!e||/^\s*$/.test(e)},escapeRegExChars:function(e){return e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isArray:e.isArray,isFunction:e.isFunction,isObject:e.isPlainObject,isUndefined:function(e){return"undefined"==typeof e},bind:e.proxy,bindAll:function(t){var n;for(var r in t)e.isFunction(n=t[r])&&(t[r]=e.proxy(n,t))},indexOf:function(e,t){for(var n=0;n<e.length;n++)if(e[n]===t)return n;return-1},each:e.each,map:e.map,filter:e.grep,every:function(t,n){var r=!0;return t?(e.each(t,function(e,i){return(r=n.call(null,i,e,t))?void 0:!1}),!!r):r},some:function(t,n){var r=!1;return t?(e.each(t,function(e,i){return(r=n.call(null,i,e,t))?!1:void 0}),!!r):r},mixin:e.extend,getUniqueId:function(){var e=0;return function(){return e++}}(),defer:function(e){setTimeout(e,0)},debounce:function(e,t,n){var r,i;return function(){var s,o,u=this,a=arguments;return s=function(){r=null,n||(i=e.apply(u,a))},o=n&&!r,clearTimeout(r),r=setTimeout(s,t),o&&(i=e.apply(u,a)),i}},throttle:function(e,t){var n,r,i,s,o,u;return o=0,u=function(){o=new Date,i=null,s=e.apply(n,r)},function(){var a=new Date,f=t-(a-o);return n=this,r=arguments,0>=f?(clearTimeout(i),i=null,o=a,s=e.apply(n,r)):i||(i=setTimeout(u,f)),s}},tokenizeQuery:function(t){return e.trim(t).toLowerCase().split(/[\s]+/)},tokenizeText:function(t){return e.trim(t).toLowerCase().split(/[\s\-_]+/)},getProtocol:function(){return location.protocol},noop:function(){}},r=function(){var e=/\s+/;return{on:function(t,n){var r;if(!n)return this;for(this._callbacks=this._callbacks||{},t=t.split(e);r=t.shift();)this._callbacks[r]=this._callbacks[r]||[],this._callbacks[r].push(n);return this},trigger:function(t,n){var r,i;if(!this._callbacks)return this;for(t=t.split(e);r=t.shift();)if(i=this._callbacks[r])for(var s=0;s<i.length;s+=1)i[s].call(this,{type:r,data:n});return this}}}(),i=function(){function t(t){t&&t.el||e.error("EventBus initialized without el"),this.$el=e(t.el)}var r="typeahead:";return n.mixin(t.prototype,{trigger:function(e){var t=[].slice.call(arguments,1);this.$el.trigger(r+e,t)}}),t}(),s=function(){function e(e){this.prefix=["__",e,"__"].join(""),this.ttlKey="__ttl__",this.keyMatcher=new RegExp("^"+this.prefix)}function t(){return(new Date).getTime()}function r(e){return JSON.stringify(n.isUndefined(e)?null:e)}function i(e){return JSON.parse(e)}var s,o;try{s=window.localStorage,s.setItem("~~~","!"),s.removeItem("~~~")}catch(u){s=null}return o=s&&window.JSON?{_prefix:function(e){return this.prefix+e},_ttlKey:function(e){return this._prefix(e)+this.ttlKey},get:function(e){return this.isExpired(e)&&this.remove(e),i(s.getItem(this._prefix(e)))},set:function(e,i,o){return n.isNumber(o)?s.setItem(this._ttlKey(e),r(t()+o)):s.removeItem(this._ttlKey(e)),s.setItem(this._prefix(e),r(i))},remove:function(e){return s.removeItem(this._ttlKey(e)),s.removeItem(this._prefix(e)),this},clear:function(){var e,t,n=[],r=s.length;for(e=0;r>e;e++)(t=s.key(e)).match(this.keyMatcher)&&n.push(t.replace(this.keyMatcher,""));for(e=n.length;e--;)this.remove(n[e]);return this},isExpired:function(e){var r=i(s.getItem(this._ttlKey(e)));return n.isNumber(r)&&t()>r?!0:!1}}:{get:n.noop,set:n.noop,remove:n.noop,clear:n.noop,isExpired:n.noop},n.mixin(e.prototype,o),e}(),o=function(){function e(e){n.bindAll(this),e=e||{},this.sizeLimit=e.sizeLimit||10,this.cache={},this.cachedKeysByAge=[]}return n.mixin(e.prototype,{get:function(e){return this.cache[e]},set:function(e,t){var n;this.cachedKeysByAge.length===this.sizeLimit&&(n=this.cachedKeysByAge.shift(),delete this.cache[n]),this.cache[e]=t,this.cachedKeysByAge.push(e)}}),e}(),u=function(){function t(e){n.bindAll(this),e=n.isString(e)?{url:e}:e,a=a||new o,u=n.isNumber(e.maxParallelRequests)?e.maxParallelRequests:u||6,this.url=e.url,this.wildcard=e.wildcard||"%QUERY",this.filter=e.filter,this.replace=e.replace,this.ajaxSettings={type:"get",cache:e.cache,timeout:e.timeout,dataType:e.dataType||"json",beforeSend:e.beforeSend},this._get=(/^throttle$/i.test(e.rateLimitFn)?n.throttle:n.debounce)(this._get,e.rateLimitWait||300)}function r(){f++}function i(){f--}function s(){return u>f}var u,a,f=0,l={};return n.mixin(t.prototype,{_get:function(e,t){function n(n){var i=r.filter?r.filter(n):n;t&&t(i),a.set(e,n)}var r=this;s()?this._sendRequest(e).done(n):this.onDeckRequestArgs=[].slice.call(arguments,0)},_sendRequest:function(t){function n(){i(),l[t]=null,s.onDeckRequestArgs&&(s._get.apply(s,s.onDeckRequestArgs),s.onDeckRequestArgs=null)}var s=this,o=l[t];return o||(r(),o=l[t]=e.ajax(t,this.ajaxSettings).always(n)),o},get:function(e,t){var r,i,s=this,o=encodeURIComponent(e||"");return t=t||n.noop,r=this.replace?this.replace(this.url,o):this.url.replace(this.wildcard,o),(i=a.get(r))?n.defer(function(){t(s.filter?s.filter(i):i)}):this._get(r,t),!!i}}),t}(),a=function(){function r(t){n.bindAll(this),n.isString(t.template)&&!t.engine&&e.error("no template engine specified"),t.local||t.prefetch||t.remote||e.error("one of local, prefetch, or remote is required"),this.name=t.name||n.getUniqueId(),this.limit=t.limit||5,this.minLength=t.minLength||1,this.header=t.header,this.footer=t.footer,this.recordId=t.recordId||t.valueKey||"value",this.valueKey=t.valueKey||"value",this.template=i(t.template,t.engine,this.valueKey),this.local=t.local,this.prefetch=t.prefetch,this.remote=t.remote,this.itemHash={},this.adjacencyList={},this.storage=t.name?new s(t.name):null}function i(e,t,r){var i,s;return n.isFunction(e)?i=e:n.isString(e)?(s=t.compile(e),i=n.bind(s.render,s)):i=function(e){return"<p>"+e[r]+"</p>"},i}var o={thumbprint:"thumbprint",protocol:"protocol",itemHash:"itemHash",adjacencyList:"adjacencyList"};return n.mixin(r.prototype,{_processLocalData:function(e){this._mergeProcessedData(this._processData(e))},_loadPrefetchData:function(r){function i(e){var t=r.filter?r.filter(e):e,i=h._processData(t),s=i.itemHash,u=i.adjacencyList;h.storage&&(h.storage.set(o.itemHash,s,r.ttl),h.storage.set(o.adjacencyList,u,r.ttl),h.storage.set(o.thumbprint,p,r.ttl),h.storage.set(o.protocol,n.getProtocol(),r.ttl)),h._mergeProcessedData(i)}var s,u,a,f,l,c,h=this,p=t+(r.thumbprint||"");return this.storage&&(s=this.storage.get(o.thumbprint),u=this.storage.get(o.protocol),a=this.storage.get(o.itemHash),f=this.storage.get(o.adjacencyList)),l=s!==p||u!==n.getProtocol(),r=n.isString(r)?{url:r}:r,r.ttl=n.isNumber(r.ttl)?r.ttl:864e5,a&&f&&!l?(this._mergeProcessedData({itemHash:a,adjacencyList:f}),c=e.Deferred().resolve()):c=e.getJSON(r.url).done(i),c},_transformDatum:function(e){var t=n.isString(e)?e:e[this.valueKey],r=n.isString(e)?e:e[this.recordId],i=e.tokens||n.tokenizeText(t),s={value:r,text:t,tokens:i};return n.isString(e)?(s.datum={},s.datum[this.valueKey]=e):s.datum=e,s.tokens=n.filter(s.tokens,function(e){return!n.isBlankString(e)}),s.tokens=n.map(s.tokens,function(e){return e.toLowerCase()}),s},_processData:function(e){var t=this,r={},i={};return n.each(e,function(e,s){var o=t._transformDatum(s),u=n.getUniqueId(o.value);r[u]=o,n.each(o.tokens,function(e,t){var r=t.charAt(0),s=i[r]||(i[r]=[u]);!~n.indexOf(s,u)&&s.push(u)})}),{itemHash:r,adjacencyList:i}},_mergeProcessedData:function(e){var t=this;n.mixin(this.itemHash,e.itemHash),n.each(e.adjacencyList,function(e,n){var r=t.adjacencyList[e];t.adjacencyList[e]=r?r.concat(n):n})},_getLocalSuggestions:function(e){var t,r=this,i=[],s=[],o=[];return n.each(e,function(e,t){var r=t.charAt(0);!~n.indexOf(i,r)&&i.push(r)}),n.each(i,function(e,n){var i=r.adjacencyList[n];return i?(s.push(i),(!t||i.length<t.length)&&(t=i),void 0):!1}),s.length<i.length?[]:(n.each(t,function(t,i){var u,a,f=r.itemHash[i];u=n.every(s,function(e){return~n.indexOf(e,i)}),a=u&&n.every(e,function(e){return n.some(f.tokens,function(t){return 0===t.indexOf(e)})}),a&&o.push(f)}),o)},initialize:function(){var t;return this.local&&this._processLocalData(this.local),this.transport=this.remote?new u(this.remote):null,t=this.prefetch?this._loadPrefetchData(this.prefetch):e.Deferred().resolve(),this.local=this.prefetch=this.remote=null,this.initialize=function(){return t},t},getSuggestions:function(e,t){function r(e){s=s.slice(0),n.each(e,function(e,t){var r,i=o._transformDatum(t);return r=n.some(s,function(e){return i.value===e.value}),!r&&s.push(i),s.length<o.limit}),t&&t(s)}var i,s,o=this,u=!1;e.length<this.minLength||(i=n.tokenizeQuery(e),s=this._getLocalSuggestions(i).slice(0,this.limit),s.length<this.limit&&this.transport&&(u=this.transport.get(e,r)),!u&&t&&t(s))}}),r}(),f=function(){function t(t){var r=this;n.bindAll(this),this.specialKeyCodeMap={9:"tab",27:"esc",37:"left",39:"right",13:"enter",38:"up",40:"down"},this.$hint=e(t.hint),this.$input=e(t.input).on("blur.tt",this._handleBlur).on("focus.tt",this._handleFocus).on("keydown.tt",this._handleSpecialKeyEvent),n.isMsie()?this.$input.on("keydown.tt keypress.tt cut.tt paste.tt",function(e){r.specialKeyCodeMap[e.which||e.keyCode]||n.defer(r._compareQueryToInputValue)}):this.$input.on("input.tt",this._compareQueryToInputValue),this.query=this.$input.val(),this.$overflowHelper=i(this.$input)}function i(t){return e("<span></span>").css({position:"absolute",left:"-9999px",visibility:"hidden",whiteSpace:"nowrap",fontFamily:t.css("font-family"),fontSize:t.css("font-size"),fontStyle:t.css("font-style"),fontVariant:t.css("font-variant"),fontWeight:t.css("font-weight"),wordSpacing:t.css("word-spacing"),letterSpacing:t.css("letter-spacing"),textIndent:t.css("text-indent"),textRendering:t.css("text-rendering"),textTransform:t.css("text-transform")}).insertAfter(t)}function s(e,t){return e=(e||"").replace(/^\s*/g,"").replace(/\s{2,}/g," "),t=(t||"").replace(/^\s*/g,"").replace(/\s{2,}/g," "),e===t}return n.mixin(t.prototype,r,{_handleFocus:function(){this.trigger("focused")},_handleBlur:function(){this.trigger("blured")},_handleSpecialKeyEvent:function(e){var t=this.specialKeyCodeMap[e.which||e.keyCode];t&&this.trigger(t+"Keyed",e)},_compareQueryToInputValue:function(){var e=this.getInputValue(),t=s(this.query,e),n=t?this.query.length!==e.length:!1;n?this.trigger("whitespaceChanged",{value:this.query}):t||this.trigger("queryChanged",{value:this.query=e})},destroy:function(){this.$hint.off(".tt"),this.$input.off(".tt"),this.$hint=this.$input=this.$overflowHelper=null},focus:function(){this.$input.focus()},blur:function(){this.$input.blur()},getQuery:function(){return this.query},setQuery:function(e){this.query=e},getInputValue:function(){return this.$input.val()},setInputValue:function(e,t){this.$input.val(e),!t&&this._compareQueryToInputValue()},getHintValue:function(){return this.$hint.val()},setHintValue:function(e){this.$hint.val(e)},getLanguageDirection:function(){return(this.$input.css("direction")||"ltr").toLowerCase()},isOverflow:function(){return this.$overflowHelper.text(this.getInputValue()),this.$overflowHelper.width()>this.$input.width()},isCursorAtEnd:function(){var e,t=this.$input.val().length,r=this.$input[0].selectionStart;return n.isNumber(r)?r===t:document.selection?(e=document.selection.createRange(),e.moveStart("character",-t),t===e.text.length):!0}}),t}(),l=function(){function t(t){n.bindAll(this),this.isOpen=!1,this.isEmpty=!0,this.isMouseOverDropdown=!1,this.$menu=e(t.menu).on("mouseenter.tt",this._handleMouseenter).on("mouseleave.tt",this._handleMouseleave).on("click.tt",".tt-suggestion",this._handleSelection).on("mouseover.tt",".tt-suggestion",this._handleMouseover)}function i(e){return e.data("suggestion")}var s={suggestionsList:'<span class="tt-suggestions"></span>'},o={suggestionsList:{display:"block"},suggestion:{whiteSpace:"nowrap",cursor:"pointer"},suggestionChild:{whiteSpace:"normal"}};return n.mixin(t.prototype,r,{_handleMouseenter:function(){this.isMouseOverDropdown=!0},_handleMouseleave:function(){this.isMouseOverDropdown=!1},_handleMouseover:function(t){var n=e(t.currentTarget);this._getSuggestions().removeClass("tt-is-under-cursor"),n.addClass("tt-is-under-cursor")},_handleSelection:function(t){var n=e(t.currentTarget);this.trigger("suggestionSelected",i(n))},_show:function(){this.$menu.css("display","block")},_hide:function(){this.$menu.hide()},_moveCursor:function(e){var t,n,r,s;if(this.isVisible()){if(t=this._getSuggestions(),n=t.filter(".tt-is-under-cursor"),n.removeClass("tt-is-under-cursor"),r=t.index(n)+e,r=(r+1)%(t.length+1)-1,-1===r)return this.trigger("cursorRemoved"),void 0;-1>r&&(r=t.length-1),s=t.eq(r).addClass("tt-is-under-cursor"),this._ensureVisibility(s),this.trigger("cursorMoved",i(s))}},_getSuggestions:function(){return this.$menu.find(".tt-suggestions > .tt-suggestion")},_ensureVisibility:function(e){var t=this.$menu.height()+parseInt(this.$menu.css("paddingTop"),10)+parseInt(this.$menu.css("paddingBottom"),10),n=this.$menu.scrollTop(),r=e.position().top,i=r+e.outerHeight(!0);0>r?this.$menu.scrollTop(n+r):i>t&&this.$menu.scrollTop(n+(i-t))},destroy:function(){this.$menu.off(".tt"),this.$menu=null},isVisible:function(){return this.isOpen&&!this.isEmpty},closeUnlessMouseIsOverDropdown:function(){this.isMouseOverDropdown||this.close()},close:function(){this.isOpen&&(this.isOpen=!1,this.isMouseOverDropdown=!1,this._hide(),this.$menu.find(".tt-suggestions > .tt-suggestion").removeClass("tt-is-under-cursor"),this.trigger("closed"))},open:function(){this.isOpen||(this.isOpen=!0,!this.isEmpty&&this._show(),this.trigger("opened"))},setLanguageDirection:function(e){var t={left:"0",right:"auto"},n={left:"auto",right:" 0"};"ltr"===e?this.$menu.css(t):this.$menu.css(n)},moveCursorUp:function(){this._moveCursor(-1)},moveCursorDown:function(){this._moveCursor(1)},getSuggestionUnderCursor:function(){var e=this._getSuggestions().filter(".tt-is-under-cursor").first();return e.length>0?i(e):null},getFirstSuggestion:function(){var e=this._getSuggestions().first();return e.length>0?i(e):null},renderSuggestions:function(t,r){var i,u,a,f,l,c="tt-dataset-"+t.name,h='<div class="tt-suggestion">%body</div>',p=this.$menu.find("."+c);0===p.length&&(u=e(s.suggestionsList).css(o.suggestionsList),p=e("<div></div>").addClass(c).append(t.header).append(u).append(t.footer).appendTo(this.$menu)),r.length>0?(this.isEmpty=!1,this.isOpen&&this._show(),a=document.createElement("div"),f=document.createDocumentFragment(),n.each(r,function(n,r){r.dataset=t.name,i=t.template(r.datum),a.innerHTML=h.replace("%body",i),l=e(a.firstChild).css(o.suggestion).data("suggestion",r),l.children().each(function(){e(this).css(o.suggestionChild)}),f.appendChild(l[0])}),p.show().find(".tt-suggestions").html(f)):this.clearSuggestions(t.name),this.trigger("suggestionsRendered")},clearSuggestions:function(e){var t=e?this.$menu.find(".tt-dataset-"+e):this.$menu.find('[class^="tt-dataset-"]'),n=t.find(".tt-suggestions");t.hide(),n.empty(),0===this._getSuggestions().length&&(this.isEmpty=!0,this._hide())}}),t}(),c=function(){function t(e){var t,r,s;n.bindAll(this),this.$node=i(e.input),this.datasets=e.datasets,this.dir=null,this.eventBus=e.eventBus,t=this.$node.find(".tt-dropdown-menu"),r=this.$node.find(".tt-query"),s=this.$node.find(".tt-hint"),this.dropdownView=(new l({menu:t})).on("suggestionSelected",this._handleSelection).on("cursorMoved",this._clearHint).on("cursorMoved",this._setInputValueToSuggestionUnderCursor).on("cursorRemoved",this._setInputValueToQuery).on("cursorRemoved",this._updateHint).on("suggestionsRendered",this._updateHint).on("opened",this._updateHint).on("closed",this._clearHint).on("opened closed",this._propagateEvent),this.inputView=(new f({input:r,hint:s})).on("focused",this._openDropdown).on("blured",this._closeDropdown).on("blured",this._setInputValueToQuery).on("enterKeyed tabKeyed",this._handleSelection).on("queryChanged",this._clearHint).on("queryChanged",this._clearSuggestions).on("queryChanged",this._getSuggestions).on("whitespaceChanged",this._updateHint).on("queryChanged whitespaceChanged",this._openDropdown).on("queryChanged whitespaceChanged",this._setLanguageDirection).on("escKeyed",this._closeDropdown).on("escKeyed",this._setInputValueToQuery).on("tabKeyed upKeyed downKeyed",this._managePreventDefault).on("upKeyed downKeyed",this._moveDropdownCursor).on("upKeyed downKeyed",this._openDropdown).on("tabKeyed leftKeyed rightKeyed",this._autocomplete)}function i(t){var n=e(o.wrapper),r=e(o.dropdown),i=e(t),s=e(o.hint);n=n.css(u.wrapper),r=r.css(u.dropdown),s.css(u.hint).css({backgroundAttachment:i.css("background-attachment"),backgroundClip:i.css("background-clip"),backgroundColor:i.css("background-color"),backgroundImage:i.css("background-image"),backgroundOrigin:i.css("background-origin"),backgroundPosition:i.css("background-position"),backgroundRepeat:i.css("background-repeat"),backgroundSize:i.css("background-size")}),i.data("ttAttrs",{dir:i.attr("dir"),autocomplete:i.attr("autocomplete"),spellcheck:i.attr("spellcheck"),style:i.attr("style")}),i.addClass("tt-query").attr({autocomplete:"off",spellcheck:!1}).css(u.query);try{!i.attr("dir")&&i.attr("dir","auto")}catch(a){}return i.wrap(n).parent().prepend(s).append(r)}function s(e){var t=e.find(".tt-query");n.each(t.data("ttAttrs"),function(e,r){n.isUndefined(r)?t.removeAttr(e):t.attr(e,r)}),t.detach().removeData("ttAttrs").removeClass("tt-query").insertAfter(e),e.remove()}var o={wrapper:'<span class="twitter-typeahead"></span>',hint:'<input class="tt-hint" type="text" autocomplete="off" spellcheck="off" disabled>',dropdown:'<span class="tt-dropdown-menu"></span>'},u={wrapper:{position:"relative",display:"inline-block"},hint:{position:"absolute",top:"0",left:"0",borderColor:"transparent",boxShadow:"none"},query:{position:"relative",verticalAlign:"top",backgroundColor:"transparent"},dropdown:{position:"absolute",top:"100%",left:"0",zIndex:"100",display:"none"}};return n.isMsie()&&n.mixin(u.query,{backgroundImage:"url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)"}),n.isMsie()&&n.isMsie()<=7&&(n.mixin(u.wrapper,{display:"inline",zoom:"1"}),n.mixin(u.query,{marginTop:"-1px"})),n.mixin(t.prototype,r,{_managePreventDefault:function(e){var t,n,r=e.data,i=!1;switch(e.type){case"tabKeyed":t=this.inputView.getHintValue(),n=this.inputView.getInputValue(),i=t&&t!==n;break;case"upKeyed":case"downKeyed":i=!r.shiftKey&&!r.ctrlKey&&!r.metaKey}i&&r.preventDefault()},_setLanguageDirection:function(){var e=this.inputView.getLanguageDirection();e!==this.dir&&(this.dir=e,this.$node.css("direction",e),this.dropdownView.setLanguageDirection(e))},_updateHint:function(){var e,t,r,i,s,o=this.dropdownView.getFirstSuggestion(),u=o?o.value:null,a=this.dropdownView.isVisible(),f=this.inputView.isOverflow();u&&a&&!f&&(e=this.inputView.getInputValue(),t=e.replace(/\s{2,}/g," ").replace(/^\s+/g,""),r=n.escapeRegExChars(t),i=new RegExp("^(?:"+r+")(.*$)","i"),s=i.exec(u),this.inputView.setHintValue(e+(s?s[1]:"")))},_clearHint:function(){this.inputView.setHintValue("")},_clearSuggestions:function(){this.dropdownView.clearSuggestions()},_setInputValueToQuery:function(){this.inputView.setInputValue(this.inputView.getQuery())},_setInputValueToSuggestionUnderCursor:function(e){var t=e.data;this.inputView.setInputValue(t.text,!0)},_openDropdown:function(){this.dropdownView.open()},_closeDropdown:function(e){this.dropdownView["blured"===e.type?"closeUnlessMouseIsOverDropdown":"close"]()},_moveDropdownCursor:function(e){var t=e.data;t.shiftKey||t.ctrlKey||t.metaKey||this.dropdownView["upKeyed"===e.type?"moveCursorUp":"moveCursorDown"]()},_handleSelection:function(e){var t="suggestionSelected"===e.type,r=t?e.data:this.dropdownView.getSuggestionUnderCursor();r&&(null!==r.text?this.inputView.setInputValue(r.text):this.inputView.setInputValue(this.inputView.getInputValue()),t?this.inputView.focus():e.data.preventDefault(),t&&n.isMsie()?n.defer(this.dropdownView.close):this.dropdownView.close(),this.eventBus.trigger("selected",r.datum,r.dataset))},_getSuggestions:function(){var e=this,t=this.inputView.getQuery();n.isBlankString(t)||n.each(this.datasets,function(n,r){r.getSuggestions(t,function(n){t===e.inputView.getQuery()&&e.dropdownView.renderSuggestions(r,n)})})},_autocomplete:function(e){var t,n,r,i,s;("rightKeyed"!==e.type&&"leftKeyed"!==e.type||(t=this.inputView.isCursorAtEnd(),n="ltr"===this.inputView.getLanguageDirection()?"leftKeyed"===e.type:"rightKeyed"===e.type,t&&!n))&&(r=this.inputView.getQuery(),i=this.inputView.getHintValue(),""!==i&&r!==i&&(s=this.dropdownView.getFirstSuggestion(),this.inputView.setInputValue(s.text),this.eventBus.trigger("autocompleted",s.datum,s.dataset)))},_propagateEvent:function(e){this.eventBus.trigger(e.type)},destroy:function(){this.inputView.destroy(),this.dropdownView.destroy(),s(this.$node),this.$node=null},setQuery:function(e){this.inputView.setQuery(e),this.inputView.setInputValue(e),this._clearHint(),this._clearSuggestions(),this._getSuggestions()}}),t}();!function(){var t,r={},s="ttView";t={initialize:function(t){function o(){var t,r=e(this),o=new i({el:r});t=n.map(u,function(e){return e.initialize()}),r.data(s,new c({input:r,eventBus:o=new i({el:r}),datasets:u})),e.when.apply(e,t).always(function(){n.defer(function(){o.trigger("initialized")})})}var u;return t=n.isArray(t)?t:[t],0===t.length&&e.error("no datasets provided"),u=n.map(t,function(e){var t=r[e.name]?r[e.name]:new a(e);return e.name&&(r[e.name]=t),t}),this.each(o)},destroy:function(){function t(){var t=e(this),n=t.data(s);n&&(n.destroy(),t.removeData(s))}return this.each(t)},setQuery:function(t){function n(){var n=e(this).data(s);n&&n.setQuery(t)}return this.each(n)}},jQuery.fn.typeahead=function(e){return t[e]?t[e].apply(this,[].slice.call(arguments,1)):t.initialize.apply(this,arguments)}}()}(window.jQuery),define("typeahead",["jquery"],function(e){return function(){var t,n;return t||e.typeahead}}(this)),define("customer",["jquery","select2","icheck","bootbox","mustache","underscore","backbone","customeredit_modal","studiohour_modal","typeahead","hash"],function(e,t,n,r,i,s,o,u,a,f,l){function c(t){this.defaults={url_fetch_collection:"searchAllCustomerList.htm",url_customer_quick_search:"customerQuickSearch.htm",url_customer_search:"customerSearch.htm",url_get_customer_detail_info:"getCustomerDetailInfo.htm",EL:e(".customer-list"),$quickSearch:e(".search-customer")},this.init(t)}return c.prototype={init:function(t){this.conf=e.extend(this.defaults,t);var n=this,r=n.conf,a=r.$quickSearch,f,c,h,p,d;n.customereditModal=new u({EL:{$modal:e("#modal_edit_customers"),$mask:e(".mask")}}),d=i.compile('<a href="javascript:;">{{fullName}} {{externalId}} {{status}}</a>'),a.typeahead({recordId:"recordId",valueKey:"fullName",remote:{url:r.url_customer_quick_search,filter:function(e){return e},replace:function(t,n){return e("#studio-customers").is(":checked")?t+"?studioCustomers=true&searchCriteria="+n:t+"?searchCriteria="+n}},template:d,limit:4,minLength:2,engine:i}).on("typeahead:selected",function(t,r){if(r){if(r.type==="more"){n.search();return}l.add({customer:r.recordId});var s=i.render('<li><a data-id="{{recordId}}" class="js-show-customer-detail-view" href="javascript:;">{{fullName}}</a></li><li>{{email}}</li><li>Phone {{phone}}</li><li>GC ID {{externalId}}</li><li>Lesson Count {{lessonCount}}</li><li>{{instrumentType}}</li>',r);e(".filter-list").hide().next().show(),e(".customer-info").data("customer",r),e(".customer-info ul").html(s),n.conf.gotoCalendar()}}),f=o.Model.extend({methodUrl:{read:r.url_fetch_collection},sync:function(e,t,n){t.methodUrl&&t.methodUrl[e.toLowerCase()]&&(n=n||{},n.url=t.methodUrl[e.toLowerCase()]),o.sync(e,t,n)}}),c=o.View.extend({initialize:function(){return this},events:{"click .js-view-schedule":"viewSchedule","click .js-create-appointment":"createAppointment","click .close-edit-panel":"close","click .js-edit-customer":"editCustomer","click .customer-detail-view":"customerDetailView"},tagName:"table",className:"table table-list table-fixed",template:e("#tmpl_customer").html(),render:function(){return this.$el.html(i.render(this.template,this.model.toJSON())),this},viewSchedule:function(t){var r=this,i=n.conf,s=e(t.currentTarget),o=s.data("id");l.add({customer:o}),e.ajax({url:i.url_get_customer_detail_info,type:"GET",data:{customerId:o},success:function(t){r.model.set(t.customerInfo),r.renderCustomerInfo(),e(".js-show-schedule").click()}})},createAppointment:function(t){var r=e(t.currentTarget),i=r.data("id"),s=r.data("name");l.add({customer:i}),this.renderCustomerInfo(),l.add({filter:""}),n.conf.createAppointment(t),n.conf.gotoToday()},renderCustomerInfo:function(){var t='<li><a data-id="{{recordId}}" class="js-show-customer-detail-view" href="javascript:;">{{fullName}}</a></li><li>{{email}}</li><li>Phone {{phone}}</li><li>GC ID {{externalId}}</li><li>Lesson Count {{lessonCount}}</li><li>{{instrumentType}}</li>',n=i.render(t,this.model.toJSON());e(".filter-list").hide().next().show(),e(".customer-info").data("customer",this.model.toJSON()),e(".customer-info ul").html(n)},customerDetailView:function(t){var r=e(t.currentTarget),i=r.data("id");n.conf.gotoCustomerDetail(i)},editCustomer:function(t){var r=e(t.currentTarget),i=r.data("id"),s=e('input[name="searchCustCriteria"]').val();n.conf.gotoCustomerEdit(i,s)}}),h=o.View.extend({initialize:function(){return this},tagName:"table",className:"table table-list table-fixed",template:e("#tmpl_emptycustomer").html(),render:function(){return this.$el.html(i.render(this.template)),this}}),p=o.Collection.extend({model:f,methodUrl:{read:r.url_fetch_collection},sync:function(e,t,n){t.methodUrl&&t.methodUrl[e.toLowerCase()]&&(n=n||{},n.url=t.methodUrl[e.toLowerCase()]),o.sync(e,t,n)}});var v=new p;n.Model=f,n.View=c,n.Empty_View=h,n.Collection=p,n.customer_collection=v,s.bindAll(n,"addAll","addOne","syncDone"),v.on("set",n.addAll),v.on("add",n.addOne),v.on("sync",n.syncDone)},renderList:function(){var e=this;e.showLoadTip(),e.is_changed=!1,e.getAll()},search:function(t){var n=this,r=n.conf,i;t?i=t:i=e(".search-customer").val(),i===""&&(i=e('input[name="searchCustCriteria"]').val()),e('input[name="searchCustCriteria"]').val(i),i.length>=2&&e.ajax({url:r.url_customer_search,type:"GET",data:{searchCriteria:i,studioCustomers:e("#studio-customers").is(":checked")},success:function(e){r.EL.empty(),n.customer_collection.reset(),n.customer_collection.set(e),n.customer_collection.length==0&&n.conf.EL.append((new n.Empty_View).render().el)}})},getAll:function(){var e=this;e.conf.EL.empty(),e.customer_collection.reset(),e.customer_collection.fetch()},addAll:function(){var e=this;e.customer_collection.each(e.addOne)},addOne:function(e){var t=this,n=t.conf.EL,r;r=new t.View({model:e}),n.append(r.render().el)},showLoadTip:function(){var e=this,t=e.conf.EL;t.append('<div class="loader"><p></p></div>')},syncDone:function(){var e=this,t=e.conf.EL;e.customer_collection.length==0&&t.append((new e.Empty_View).render().el)}},c}),define("timeoff_modal",["jquery","moment","select2","icheck","mustache","underscore","backbone","pikaday","notification"],function(e,t,n,r,s,o,u,a,f){function l(t){this.defaults={url_update:"timeoff/updateTimeoff.htm",url_remove:"timeoff/deletetimeoff.htm",url_load:"timeoff/loadtimeoffs.htm",url_load:"timeoff/loadtimeoffs.htm",url_fetch_instructor_start_time:"timeoff/getStartTimeByInstructors.htm",EL:{$modal:e("#modal_set_timeoff"),$mask:e(".mask")}},this.currentVersion=undefined,this.init(t)}return l.prototype={init:function(t){this.conf=e.extend({},this.defaults,t);var n=this,r=n.conf,i=n.conf.EL,s=i.$modal,o,l,c,h,p,d;o=u.Model.extend({}),n.timeoff_model=new o,l=u.Model.extend({defaults:{},formEL:{dateFrom:{el:s.find('input[name="timeOffStartDate"]')},dateTo:{el:s.find('input[name="timeOffendDate"]')},timeFrom:{el:s.find('input[name="timeOffFrom"]'),set:function(e){this.el.select2("val",e)},clear:function(){this.el.select2("val",""),this.el.select2("enable",!1)},setAll:function(e){return this.el.select2({data:e}),this}},timeTo:{el:s.find('input[name="timeOffTo"]'),set:function(e){this.el.select2("val",e)},clear:function(){this.el.select2("val",""),this.el.select2("enable",!1)},setAll:function(e){return this.el.select2({data:e}),this}}}}),h=new l,n.modal=h,c=h.formEL,p=new a({format:"MM/DD/YYYY",field:c.dateFrom.el[0]}),d=new a({format:"MM/DD/YYYY",field:c.dateTo.el[0]}),c.dateFrom.el.on("change",function(t){var i=this.value,s=n.instructorModel.get("id");c.timeFrom.clear(),e.ajax({url:r.url_fetch_instructor_start_time,type:"GET",data:{instructorId:s,startDate:i},success:function(e){e.startTimeDTOs&&e.startTimeDTOs.length>0?(c.timeFrom.setAll(e.startTimeDTOs),c.timeFrom.el.select2("enable",!0)):f.autoHideError(e.message)}})}),c.dateTo.el.on("change",function(t){var i=this.value,s=n.instructorModel.get("id");c.timeTo.clear(),e.ajax({url:r.url_fetch_instructor_start_time,type:"GET",data:{instructorId:s,startDate:i},success:function(e){e.startTimeDTOs&&e.startTimeDTOs.length>0?(c.timeTo.setAll(e.startTimeDTOs),c.timeTo.el.select2("enable",!0)):f.autoHideError(e.message)}})})},initDate:function(){e('input[name="startDate"]').val(t(new Date).format("L")),e('input[name="endDate"]').val(t(new Date).format("L"))},renderTimeOffList:function(t){var n=this,r=n.conf,s=n.conf.EL,o=s.$modal,u="",a=t;if(a.length>0)for(i in a)u+='<p><a class="icon icon-remove js-remove-timeoff" data-id="'+a[i].timeoffId+'" href="javascript:;">x</a>'+a[i].timeOffStartToEnd+"</p>";o.find(".timeoff-list").empty().html(u),o.find(".js-remove-timeoff").on("click",function(){var t=e(this).attr("data-id")*1;e.ajax({url:r.url_remove,type:"POST",data:{timeoffId:t},success:function(t){var r="",s=t.timeoffs;if(t.status){n.renderTimeOffList(t.timeoffs);if(s.length>0)for(i in s)r+='<p class="row">'+s[i].timeOffStartToEnd+"</p>";e(".js-timeoff-wrap").empty().html(r)}else f.autoHideError(t.message)}})})},showModal:function(t){var n=this,r=n.conf,i=r.EL,s=i.$mask,o=i.$modal,u=e(window).height(),a=e(window).width(),f=533,l=246,c=(u-l)/2+e(window).scrollTop(),h=(a-f)/2;c<0&&(c=10),h<0&&(h=10),o.css({left:h,top:c}),this.instructorModel=t,e.ajax({url:r.url_load,type:"GET",data:{instructorId:t.id},success:function(e){n.renderTimeOffList(e.timeoffs)}}),o.show(),s.show()},update:function(t){var n=this,r=n.conf,s=n.modal.formEL,o=s.dateFrom.el.val(),u=s.dateTo.el.val(),a=s.timeFrom.el.val(),l=s.timeTo.el.val();id=n.instructorModel.get("id"),data={fromTime:a,fromDate:o,toTime:l,toDate:u,instructorId:id};var c=e(".btn-update-timeoff");c.prop("disabled",!0),e.ajax({url:r.url_update,type:"POST",contentType:"application/json",data:JSON.stringify(data),success:function(t){if(t.status){var r=t.dto,s="",o="",u="";if(r.length>0)for(i in r)s+='<p class="row">'+r[i].timeOffStartToEnd+"</p>";e(".js-timeoff-wrap").empty().html(s);if(r.length>0)for(i in r)u+='<p><a class="icon icon-remove js-remove-timeoff" data-id="'+r[i].timeoffId+'" href="javascript:;">x</a>'+r[i].timeOffStartToEnd+"</p>";e(".timeoff-list").empty().html(u),n.cleanDate(),n.cleanTime(),n.renderTimeOffList(t.dto)}else f.autoHideError(t.message);c.prop("disabled",!1)}})},hideModal:function(){var e=this,t=e.conf.EL,n=e.modal.formEL;t.$modal.hide(),t.$mask.hide(),e.cleanDate(),e.cleanTime()},cleanDate:function(){var e=this,t=e.conf.EL,n=e.modal.formEL;n.dateFrom.el.val(""),n.dateTo.el.val("")},cleanTime:function(){var e=this,t=e.conf.EL,n=e.modal.formEL;n.timeFrom.set(""),n.timeTo.set(""),n.timeFrom.clear(),n.timeTo.clear()}},l}),define("instructor",["jquery","select2","icheck","bootbox","mustache","underscore","backbone","edithour_modal","timeoff_modal","notification"],function(e,t,n,r,s,o,u,a,f,l){function c(t){this.defaults={url_fetch_collection:"instructor/loadInstructorList.htm",url_fetch_model:"instructor/loadInstructorDetail.htm",url_fetch_oneTimeModel:"onetime/loadonetimes.htm",url_update_model:"updateInstructor.htm",EL:e(".instructor-list")},this.is_changed=is_changed,this.init(t)}return c.prototype={init:function(t){this.conf=e.extend(this.defaults,t);var n=this;n.edithour_modal=new a({EL:{$modal:e("#modal_set_availability"),$mask:e(".mask")}}),n.timeoff_modal=new f({EL:{$modal:e("#modal_set_timeoff"),$mask:e(".mask")}}),e(document).on("click",".btn-update-availability",function(e){n.edithour_modal.update(e)}),e(document).on("click",".js-close-availability-modal",function(){n.edithour_modal.hideModal()}),e(document).on("click",".btn-update-timeoff",function(e){n.timeoff_modal.update(e)}),e(document).on("click",".js-close-timeoff-modal",function(e){n.timeoff_modal.hideModal()});var c=n.conf,h,p,d,v,m;h=u.Model.extend({methodUrl:{read:c.url_fetch_model,create:c.url_update_model,update:c.url_update_model,"delete":""},sync:function(e,t,n){t.methodUrl&&t.methodUrl[e.toLowerCase()]&&(n=n||{},n.url=t.methodUrl[e.toLowerCase()]),u.sync(e,t,n)},initialize:function(){}}),p=u.View.extend({initialize:function(){var e=this;return this},events:{"click .js-edit-instructor":"edit","click .close-edit-panel":"close","click .js-update-instructor":"update","click .js-set-availability":"setAvailability","click .js-set-timeoff":"setTimeoff",'ifChanged input[name="active"]':"changeActive"},tagName:"table",className:"table table-list table-fixed ",template:e("#tmpl_instructor").html(),render:function(){return this.$el.html(s.render(this.template,this.model.toJSON())),this},edit:function(t){var i=this,s=e(t.currentTarget);if(s.hasClass("editing"))return;n.is_changed?r.dialog({title:"Warning!",message:"You have unsaved information. Do you want to continue?",buttons:{success:{label:"Continue",className:"btn btn-important",callback:function(){c.EL.find(".editing").removeClass("editing"),c.EL.find("tr.disabled").removeClass("disabled").nextAll().hide(),n.hideWarn(),i.getOne()}},cancle:{label:"Cancel",className:"btn btn-primary",callback:function(){n.showWarn();return}}}}):(c.EL.find(".editing").removeClass("editing"),c.EL.find("tr.disabled").removeClass("disabled").nextAll().hide(),s.addClass("editing"),i.getOne())},getOne:function(){var t=this;e.ajax({url:c.url_fetch_model,type:"GET",data:{id:t.model.get("id")},success:function(e){typeof e=="string"&&e.indexOf("DOCTYPE")!==-1&&location.reload(),t.model.set(e),t.editRender(),t.$el.find("tr").eq(0).addClass("disabled").nextAll().show(),n.is_changed=!1,is_changed=n.is_changed},error:function(e){l.autoHideError(e)}})},editRender:function(){var e=new v({model:this.model}),t="",n="",r="",s=this.model.get("editHourShowList").list,o=this.model.get("timeOffs"),u=this.model.get("onetimes");s.forEach(function(e){var n=e.startTimeString,r=e.endTimeString,i=e.weekDay;n!=""&&(t+='<p class="row"><span class="span2">'+i+":</span>"+n+" - "+r+"</p>")});if(o.length>0)for(i in o)n+='<p class="row">'+o[i].timeOffStartToEnd+"</p>";if(u.length>0)for(i in u)r+='<p class="row">'+u[i].onetimeStartToEnd+"</p>";var a=new m({model:this.model});this.$el.find(".js-availability-wrap").empty().html(t),this.$el.find(".js-timeoff-wrap").empty().html(n),this.$el.find(".js-oneTimeAvailability-wrap").empty().html(r),this.$el.find(".js-activity-wrap").empty().append(e.render().el),this.$el.find(".js-instructor-mode-wrap").empty().append(a.renderInsMd().el),this.model.get("active")?this.$el.find('input[name="active"]').iCheck().iCheck("indeterminate"):this.$el.find('input[name="active"]').iCheck().iCheck("determinate")},close:function(){return this.$el.find(".js-edit-instructor").removeClass("editing"),n.hideWarn(),this.$el.find("tr").eq(0).removeClass("disabled").nextAll().hide(),this.$el.find('input[name="active"]').iCheck("destroy"),n.is_changed=!1,is_changed=n.is_changed,this},update:function(){var e=this;this.$el.find(".edit-instructor").removeClass("editing"),this.$el.find(".js-update-instructor").attr("disabled",!0),n.hideWarn(),this.model.sync("create",this.model,{success:function(t){t.status?(e.model.set(t.object),e.render(),e.close()):l.autoHideError(t.message),e.$el.find(".js-update-instructor").attr("disabled",!1)}})},setAvailability:function(){n.edithour_modal.showModal(e.extend(!0,{},this.model))},setTimeoff:function(){n.timeoff_modal.showModal(e.extend(!0,{},this.model))},changeActive:function(e){n.is_changed=!0,is_changed=n.is_changed,e.currentTarget.checked?this.model.set("active",!1):this.model.set("active",!0)},remove:function(){}}),d=u.Collection.extend({model:h,methodUrl:{read:c.url_fetch_collection,create:"",update:"","delete":""},sync:function(e,t,n){t.methodUrl&&t.methodUrl[e.toLowerCase()]&&(n=n||{},n.url=t.methodUrl[e.toLowerCase()]),u.sync(e,t,n)}}),m=u.View.extend({className:"",templateInsMd:e("#tmpl_instructor_mode").html(),renderInsMd:function(){this.$el.html(s.render(this.templateInsMd,this.model.toJSON()));var e=this.model.get("serviceModeName");return model=this.model,this.$el.find('input[name="instructorMode"]').select2({placeholder:"Select Instructor Mode",data:this.model.get("notSelectedServiceMode"),id:function(e){return e.serviceModeId},formatResult:function(e){return e.serviceModeName},formatSelection:function(e){return e.serviceModeName}}).select2("val",this.model.get("instructorMode")).on("select2-selecting",function(e){n.is_changed=!0,is_changed=n.is_changed,model.set("instructorMode",e.val)}),this}}),v=u.View.extend({events:{"click .js-btn-add":"add","click .js-remove-activity":"remove"},className:"",template:e("#tmpl_activity").html(),subTemplate:e("#tmpl_activity_item").html(),render:function(){return this.$el.html(s.render(this.template,this.model.toJSON())),this.$el.find('input[name="activities"]').select2({placeholder:"Select Activity",data:this.model.get("notSelectedActivities"),id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}),this},add:function(){var e=this.$el.find('input[name="activities"]').val(),t=this.$el.find('input[name="activities"]'),r=this.$el.find(".js-activity-list"),i=this.subTemplate,u=this.model;if(e==="")return;o.each(u.get("notSelectedActivities"),function(t,n){t.activityId==e&&(u.get("activitys").push(t),r.append(s.render(i,t)))});var a=o.filter(u.get("notSelectedActivities"),function(t,n){return t.activityId!==e*1});u.set("notSelectedActivities",a),t.select2("val","").select2({placeholder:"Select Activity",data:this.model.get("notSelectedActivities"),id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}),n.is_changed=!0,is_changed=n.is_changed},remove:function(t){var r=this.model,i=this.$el.find('input[name="activities"]'),s=e(t.currentTarget).data("id"),u=o.filter(r.get("activitys"),function(e,t){return e.activityId===s*1});r.get("notSelectedActivities").push(u[0]);var a=o.filter(r.get("activitys"),function(e,t){return e.activityId!==s*1});r.set("activitys",a),i.select2("val","").select2({placeholder:"Select Activity",data:r.get("notSelectedActivities"),id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}),e(t.currentTarget).parent().fadeOut(function(){e(this).remove()}),n.is_changed=!0,is_changed=n.is_changed}});var g=new d;return n.Model=h,n.View=p,n.Collection=d,n.instructor_collection=g,o.bindAll(n,"addAll","addOne"),g.on("set",n.addAll),g.on("add",n.addOne),n},renderList:function(){var e=this;return e.showLoadTip(),e.is_changed=!1,is_changed=e.is_changed,e.getAll(),e},getAll:function(){var t=this,n=t.conf;n.EL.empty(),t.instructor_collection.reset(),e.ajax({url:n.url_fetch_collection,type:"GET",success:function(e){t.instructor_collection.reset(),t.instructor_collection.set(e.listInstructorDtos)}})},addAll:function(){var e=this;e.instructor_collection.each(e.addOne)},addOne:function(e){var t=this,n=t.conf.EL,r=new t.View({model:e});n.append(r.render().el)},showWarn:function(){var t=e(".instructor-list tr.disabled").nextAll().find(".js-update-instructor"),n=220,r=t.offset().top-t.height()-e(".warn-tip").height();e(".warn-tip").css({right:n,top:r}).show()},hideWarn:function(){e(".warn-tip").hide()},showLoadTip:function(){var e=this,t=e.conf.EL;t.append('<div class="loader"><p></p></div>')}},c}),define("instructorschedule",["jquery","select2","icheck","bootbox","mustache","underscore","backbone","edithour_modal","timeoff_modal","notification"],function(e,t,n,r,s,o,u,a,f,l){function c(t){this.defaults={url_fetch_collection:"instructor/loadInstructorList.htm",url_fetch_instructor_sch_collection:"instructor/loadInstructorScheduleList.htm",url_fetch_model:"instructor/loadInstructorDetail.htm",url_fetch_oneTimeModel:"onetime/loadonetimes.htm",url_update_model:"updateInstructorSchedule.htm",url_send_reminder:"instructor/sendReminder.htm",EL:e(".instructor-list-schedule")},this.is_changed=is_changed,this.init(t)}return c.prototype={init:function(t){this.conf=e.extend(this.defaults,t);var n=this;n.queryParams={queryDate:new Date},n.edithour_modal=new a({EL:{$modal:e("#Xmodal_set_availability"),$mask:e(".mask")}}),n.timeoff_modal=new f({EL:{$modal:e("#Xmodal_set_timeoff"),$mask:e(".mask")}}),e(document).on("click",".btn-update-availability_remv",function(e){n.edithour_modal.update(e)}),e(document).on("click",".js-close-availability-modal_remv",function(){n.edithour_modal.hideModal()}),e(document).on("click",".btn-update-timeoff_remv",function(e){n.timeoff_modal.update(e)}),e(document).on("click",".js-close-timeoff-modal_remv",function(e){n.timeoff_modal.hideModal()}),e(document).on("click",".btn-day-prev",function(e){n.queryPreDate()}),e(document).on("click",".btn-day-next",function(e){n.queryNextDate()});var c=n.conf,h,p,d,v,m,g,y;h=u.Model.extend({methodUrl:{read:c.url_fetch_instructor_sch_collection,create:c.url_update_model,update:c.url_fetch_instructor_sch_collection,"delete":""},sync:function(e,t,n){t.methodUrl&&t.methodUrl[e.toLowerCase()]&&(n=n||{},n.url=t.methodUrl[e.toLowerCase()]),u.sync(e,t,n)},parse:function(e){return delete e.message,delete e.status,delete e.object,e},initialize:function(){}}),p=u.View.extend({initialize:function(){var e=this;return this},events:{"click .js-edit-instructor_remv":"edit","click .close-edit-panel_remv":"close","click .js-update-instructor_remv":"update","click .email-header a":"sendReminder","click .js-set-availability_remv":"XsetAvailability_remv","click .js-set-timeoff_remv":"XsetTimeoff",'ifChanged input[name="active_remv"]':"changeActive_remv","click .remarks-modal":"remarksModal","click .status-modal":"statusModal","click .notes-modal":"notesModal","click .lesson-completed":"completeLessonStatus","click .undo-status":"undoLessonStatus","click .musicprodigy-modal":"musicprodigyModal"},tagName:"tr",className:"",template:e("#tmpl_instructor_sch").html(),render:function(){return this.model.set({cid:this.model.cid}),this.$el.html(s.render(this.template,this.model.toJSON())),this},edit:function(t){var i=this,s=e(t.currentTarget);if(s.hasClass("editing"))return;n.is_changed?r.dialog({title:"Warning!",message:"You have unsaved information. Do you want to continue?",buttons:{success:{label:"Continue",className:"btn btn-important",callback:function(){c.EL.find(".editing").removeClass("editing"),c.EL.find("tr.disabled").removeClass("disabled").nextAll().hide(),n.hideWarn(),i.getOne()}},cancle:{label:"Cancel",className:"btn btn-primary",callback:function(){n.showWarn();return}}}}):(c.EL.find(".editing").removeClass("editing"),c.EL.find("tr.disabled").removeClass("disabled").nextAll().hide(),s.addClass("editing"),i.getOne())},getOne:function(){var t=this;e.ajax({url:c.url_fetch_model,type:"GET",data:{id:t.model.get("id")},success:function(e){typeof e=="string"&&e.indexOf("DOCTYPE")!==-1&&location.reload(),t.model.set(e),t.editRender(),t.$el.find("tr").eq(0).addClass("disabled").nextAll().show(),n.is_changed=!1,is_changed=n.is_changed},error:function(e){l.autoHideError(e)}})},remarksModal:function(e){e.preventDefault();var t=new m({model:this.model});t.show()},statusModal:function(e){e.preventDefault();var t=new g({model:this.model});t.show()},notesModal:function(e){e.preventDefault();var t=new NotesModal_View({model:this.model});this.model.set({submit:"x"}),t.show()},completeLessonStatus:function(t){t.preventDefault();var n=new CompletedModal_View({model:this.model});n.show();var r=this,i=r.model,s=e(t.currentTarget),o=s.closest("span"),u="Completed";this.model.set({showStatus:u}),this.model.set({submit:"x"}),this.model.save({},{success:function(e,t){o.find(".lesson-status-msg").text("Completed").show(),o.find(".btn-default").hide(),o.find(".undo-wrapper").show(),console.log("saved status and send ajax request")},error:function(e,t){console.log("error")}})},undoLessonStatus:function(t){t.preventDefault();var n=this,r=n.model,i=e(t.currentTarget),s=i.closest("span");this.model.set({showStatus:""}),this.model.set({submit:"x"}),this.model.save({},{success:function(e,t){s.find(".undo-wrapper, .lesson-status-msg").hide(),s.find(".btn-default").show(),console.log("saved status and send ajax request")},error:function(e,t){console.log("error")}})},editRender:function(){var e=new v({model:this.model}),t="",n="",r="",s=this.model.get("editHourShowListX").list,o=this.model.get("timeOffs"),u=this.model.get("onetimes");s.forEach(function(e){var n=e.startTimeString,r=e.endTimeString,i=e.weekDay;n!=""&&(t+='<p class="row"><span class="span2">'+i+":</span>"+n+" - "+r+"</p>")});if(o.length>0)for(i in o)n+='<p class="row">'+o[i].timeOffStartToEnd+"</p>";if(u.length>0)for(i in u)r+='<p class="row">'+u[i].onetimeStartToEnd+"</p>";this.$el.find(".js-availability-wrapX").empty().html(t),this.$el.find(".js-timeoff-wrapX").empty().html(n),this.$el.find(".js-oneTimeAvailability-wrapX").empty().html(r),this.$el.find(".js-activity-wrapX").empty().append(e.render().el),this.model.get("activeX")?this.$el.find('input[name="activeX"]').iCheck().iCheck("indeterminate"):this.$el.find('input[name="activeX"]').iCheck().iCheck("determinate")},close:function(){return this.$el.find(".js-edit-instructor-remv").removeClass("editing"),n.hideWarn(),this.$el.find("tr").eq(0).removeClass("disabled").nextAll().hide(),this.$el.find('input[name="Xactive"]').iCheck("destroy"),n.is_changed=!1,is_changed=n.is_changed,this},update:function(){var e=this;this.$el.find(".edit-instructor_remv").removeClass("editing"),this.$el.find(".js-update-instructor_remv").attr("disabled",!0),n.hideWarn(),this.model.sync("create",this.model,{success:function(t){t.status?(e.model.set(t.object),e.render(),e.close()):l.autoHideError(t.message),e.$el.find(".js-update-instructor_remv").attr("disabled",!1)}})},XsetAvailability_remv:function(){},XsetTimeoff:function(){},changeActive:function(e){n.is_changed=!0,is_changed=n.is_changed,e.currentTarget.checked?this.model.set("activeX",!1):this.model.set("activeX",!0)},remove:function(){},musicprodigyModal:function(t){var i=this,s=e(t.currentTarget);r.dialog({title:"Alert!",message:"This will open new tab. Do you want to continue?",buttons:{success:{label:"Continue",className:"btn btn-important",callback:function(){n.hideWarn(),i.getLink()}},cancle:{label:"Cancel",className:"btn btn-primary",callback:function(){return}}}})},getLink:function(){function s(){console.log("_this.model.attributes.customerExternalId"+e.model.attributes.customerExternalId),console.log("studentId"+t);var s=fetch("https://t0e0p8b8rk.execute-api.us-west-2.amazonaws.com/default/v1/lessons/details",{method:"POST",headers:{"Content-Type":"application/json;charset=UTF-8","X-API-Key":"q6gbbkZXAy19dPcDugYO21U3KyPHQw6T7bdoLL4R"},body:JSON.stringify({persona:"instructor",studentId:t,instructorId:i})}).then(function(e){return e.json()}).then(function(t){t.mpUrl?window.open(t.mpUrl,"_blank"):r.dialog({title:"Alert!",message:"Music Prodigy data not available for this lesson, please try again.",buttons:{success:{label:"Try Again",className:"btn btn-important",callback:function(){n.hideWarn(),e.getLink()}},cancle:{label:"Cancel",className:"btn btn-primary",callback:function(){return}}}})}).catch(function(e){console.error("Error:",e)})}var e=this,t=e.model.attributes.customerExternalId,i=e.model.attributes.instructorId;s()},sendReminder:function(t){t.preventDefault();var r=e(t.currentTarget),i=this,s=i.model,o=r.attr("cid"),u=n.instructor_collection.get(o);console.log(u),u.unset("cid"),r.closest(".reminder-link-container").addClass("loading-spinner"),e.ajax({url:c.url_send_reminder,type:"POST",data:{params:JSON.stringify(u.attributes)},success:function(e){typeof e=="string"&&e.indexOf("DOCTYPE")!==-1&&location.reload();if(e==0)var t=new y({model:s}),n="Failed to Send Reminder, please try again.";else if(e==1)var t=new y({model:s}),n="Reminder Sent Successfully!";t.show(n),r.closest(".reminder-link-container").removeClass("loading-spinner")}})}}),d=u.Collection.extend({model:h,methodUrl:{read:c.url_fetch_collection,create:"",update:"","delete":""},initialize:function(){},sync:function(e,t,n){t.methodUrl&&t.methodUrl[e.toLowerCase()]&&(n=n||{},n.url=t.methodUrl[e.toLowerCase()]),u.sync(e,t,n)}}),m=u.View.extend({collection:d,model:h,id:"modal_edit_instructor",className:"modal fade hide instructor-schedule-modal",dataAttribute:'data-backdrop="static',template:e("#tmpl_instructor_comment_modal").html(),events:{hidden:"teardown","click .submit-remarks":"saveRemarks","click .close":"teardown"},initialize:function(e){var t=this,e=e;return t.render(),this},show:function(){var e=this,t=e.model;elModal=this.$el.modal(),pastRemarks=t.get("comments"),this.$el.attr("data-backdrop","static"),this.$el.modal("show"),this.model.set({submit:"x"}),elModal.find("#edit_instructor_comments").val(pastRemarks),elModal.find("#edit_instructor_comments").text(pastRemarks)},teardown:function(){this.$el.data("modal",null),this.remove()},render:function(){return this.$el.html(s.render(this.template)),this},renderView:function(e){this.$el.html(e()),this.$el.modal({show:!1})},saveRemarks:function(t){var n=this,r=n.model;elModal=this.$el.modal(),appointmentId=r.get("appointmentId"),showStatus=r.get("showStatus"),studentNote=r.get("studentNote"),appointmentRemarks=elModal.find("#edit_instructor_comments").val(),this.model.set({comments:appointmentRemarks}),this.model.save({},{success:function(t,n){console.log("comment submitted successfullly"),e(".modal-footer").hide(),elModal.find(".modal-body").html('<p class="submit-success-msg">Comment Submitted Successfully!</p>'),remarksTableDataEl=e('.remarks-header[data-id-remarks="'+appointmentId+'"]'),remarksTableDataEl.find(".add-remarks").hide(),remarksTableDataEl.find(".edit-remarks").show()},error:function(e,t){console.log("error")}})}}),NotesModal_View=u.View.extend({id:"modal_notes_editor",className:"modal fade hide instructor-schedule-modal",template:e("#tmpl_instructor_notes_modal").html(),events:{hidden:"teardown","click .submit-notes":"saveNotes","click .close":"close"},initialize:function(e){var t=this,e=e;return t.render(),this},show:function(){var e=this,t=e.model;elModal=this.$el.modal(),pastNotes=t.get("studentNote"),pastAssignment=t.get("assignment"),pastPracticeNotes=t.get("practiceNotes"),pastRemarks=t.get("remarks"),this.$el.attr("data-backdrop","static"),this.$el.modal("show"),elModal.find("#edit_student_notes").val(pastNotes),elModal.find("#edit_student_notes").text(pastNotes),elModal.find("#assignment").val(pastAssignment),elModal.find("#assignment").text(pastAssignment),elModal.find("#practice_notes").val(pastPracticeNotes),elModal.find("#practice_notes").text(pastPracticeNotes),elModal.find("#remarks").val(pastRemarks),elModal.find("#remarks").text(pastRemarks)},teardown:function(){this.$el.data("modal",null),this.$el.modal.remove()},render:function(){return this.$el.html(s.render(this.template)),this},renderView:function(e){this.$el.html(e()),this.$el.modal({show:!1})},saveNotes:function(t){var n=this,r=n.model;elModal=this.$el.modal(),appointmentId=r.get("appointmentId"),showStatus=r.get("showStatus"),appointmentRemarks=r.get("comments"),appointmentNotes=elModal.find("#edit_student_notes").val(),assignment=elModal.find("#assignment").val(),practiceNotes=elModal.find("#practice_notes").val(),remarks=elModal.find("#remarks").val(),this.model.set({submit:"submit_val"}),this.model.set({studentNote:appointmentNotes}),this.model.set({assignment:assignment}),this.model.set({practiceNotes:practiceNotes}),this.model.set({remarks:remarks}),this.model.save({},{success:function(t,n){console.log("notes submitted successfullly"),elModal.find(".modal-body").html('<p class="submit-success-msg">Note Submitted Successfully!</p>'),e(".modal-footer").hide(),notesTableDataEl=e('.notes-header[data-id-student-notes="'+appointmentId+'"]'),console.log("notes data :"+notesTableDataEl),notesTableDataEl.find(".add-notes").hide(),notesTableDataEl.find(".edit-notes").show()},error:function(e,t){console.log("error")}})}}),g=u.View.extend({id:"modal_status_instructor",className:"modal fade hide instructor-schedule-modal",template:e("#tmpl_instructor_status_modal").html(),events:{hidden:"teardown","click .submit-status":"saveStatus","click .close":"close"},initialize:function(e){var t=this,e=e;return t.render(),this},show:function(){var e=this,t=e.model;elModal=this.$el.modal(),pastStatus=t.get("showStatus"),this.$el.attr("data-backdrop","static"),this.$el.modal("show"),pastStatus!=null&&pastStatus!=""&&elModal.find("#js-lesson-status").val(pastStatus)},teardown:function(){this.$el.data("modal",null),this.$el.modal.remove()},render:function(){return this.$el.html(s.render(this.template)),this},renderView:function(e){this.$el.html(e()),this.$el.modal({show:!1})},saveStatus:function(t){var n=this,r=n.model;elModal=this.$el.modal(),appointmentId=r.get("appointmentId"),appointmentStatus=e("#js-lesson-status option:selected").text(),statusTableDataEl=e('.completed-header[data-id-status="'+appointmentId+'"]'),this.model.set({showStatus:appointmentStatus}),this.model.save({},{success:function(t,n){console.log("Status submitted successfullly"),elModal.find(".modal-body").html('<p class="submit-success-msg">Status Submitted Successfully!</p>'),e(".modal-footer").hide(),statusTableDataEl.find(".lesson-status-msg").text(appointmentStatus).show(),statusTableDataEl.find(".btn-default").hide(),statusTableDataEl.find(".undo-wrapper").show()},error:function(e,t){console.log("error")}})}}),CompletedModal_View=u.View.extend({id:"modal_status_instructor",className:"modal fade hide instructor-schedule-modal",template:e("#tmpl_instructor_completed_modal").html(),events:{hidden:"teardown","click .submit-completed":"saveStatus","click .close":"close"},initialize:function(e){var t=this,e=e;return t.render(),this},show:function(){var e=this,t=e.model;elModal=this.$el.modal(),pastAssignment=t.get("assignment"),pastPracticeNotes=t.get("practiceNotes"),pastRemarks=t.get("remarks"),this.$el.attr("data-backdrop","static"),this.$el.modal("show"),elModal.find("#assignment").val(pastAssignment),elModal.find("#assignment").text(pastAssignment),elModal.find("#practice_notes").val(pastPracticeNotes),elModal.find("#practice_notes").text(pastPracticeNotes),elModal.find("#remarks").val(pastRemarks),elModal.find("#remarks").text(pastRemarks)},teardown:function(){this.$el.data("modal",null),this.$el.modal.remove()},render:function(){return this.$el.html(s.render(this.template)),this},renderView:function(e){this.$el.html(e()),this.$el.modal({show:!1})},saveStatus:function(t){var n=this,r=n.model;elModal=this.$el.modal(),appointmentId=r.get("appointmentId"),showStatus=r.get("showStatus"),appointmentRemarks=r.get("comments"),appointmentNotes=elModal.find("#edit_student_notes").val(),assignment=elModal.find("#assignment").val(),practiceNotes=elModal.find("#practice_notes").val(),remarks=elModal.find("#remarks").val(),this.model.set({submit:"submit_val"}),this.model.set({studentNote:appointmentNotes}),this.model.set({assignment:assignment}),this.model.set({practiceNotes:practiceNotes}),this.model.set({remarks:remarks});var n=this,r=n.model,i=e(t.currentTarget),s=i.closest("span"),o="Completed";this.model.set({showStatus:o}),this.model.save({},{success:function(t,n){console.log("Completed submitted successfullly"),s.find(".lesson-status-msg").text("Completed").show(),elModal.find(".modal-body").html('<p class="submit-success-msg">Completed Submitted Successfully!</p>'),e(".modal-footer").hide(),s=e('.notes-header[data-id-status="'+appointmentId+'"]'),s.find(".add-notes").hide(),s.find(".edit-notes").show()},error:function(e,t){console.log("error")}})}}),y=u.View.extend({id:"modal_reminder_instructor",className:"modal fade hide instructor-schedule-modal",template:e("#tmpl_instructor_reminder_modal").html(),events:{hidden:"teardown","click .submit-status":"saveReminderCount","click .close":"close"},initialize:function(e){var t=this,e=e;return t.render(),this},show:function(e){var t=this,n=t.model;elModal=this.$el.modal(),this.$el.attr("data-backdrop","static"),this.$el.modal("show"),this.$el.find(".email-success-msg").text(e)},teardown:function(){this.$el.data("modal",null),this.$el.modal.remove()},render:function(){return this.$el.html(s.render(this.template)),this},renderView:function(e){this.$el.html(e()),this.$el.modal({show:!1})},saveReminderCount:function(t){var n=this,r=n.model;elModal=this.$el.modal(),appointmentId=r.get("appointmentId"),reminderTableDataEl=e('.email-header[data-id-email-reminder="'+appointmentId+'"]'),this.model.set({sentReminderCount:appointmentStatus}),this.model.save({},{success:function(t,n){console.log("Status submitted successfullly"),e(".modal-footer").hide()},error:function(e,t){console.log("error")}})}}),v=u.View.extend({events:{"click .js-btn-add_remv":"add","click .js-remove-activity_remv":"remove"},className:"",template:e("#tmpl_activity_remv").html(),subTemplate:e("#tmpl_activity_item_remv").html(),render:function(){return this.$el.html(s.render(this.template,this.model.toJSON())),this.$el.find('input[name="activities_remv"]').select2({placeholder:"Select ActivityX",data:this.model.get("notSelectedActivities"),id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}),this},add:function(){var e=this.$el.find('input[name="activities_remv"]').val(),t=this.$el.find('input[name="activities_remv"]'),r=this.$el.find(".js-activity-list_remv"),i=this.subTemplate,u=this.model;if(e==="")return;o.each(u.get("notSelectedActivities_remv"),function(t,n){t.activityId==e&&(u.get("activitys_remv").push(t),r.append(s.render(i,t)))});var a=o.filter(u.get("notSelectedActivities"),function(t,n){return t.activityId!==e*1});u.set("notSelectedActivities",a),t.select2("val","").select2({placeholder:"Select ActivityX",data:this.model.get("notSelectedActivities"),id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}),n.is_changed=!0,is_changed=n.is_changed},remove:function(t){var r=this.model,i=this.$el.find('input[name="activitiesX"]'),s=e(t.currentTarget).data("id"),u=o.filter(r.get("activitysX"),function(e,t){return e.activityId===s*1});r.get("notSelectedActivities").push(u[0]);var a=o.filter(r.get("activitysX"),function(e,t){return e.activityId!==s*1});r.set("activitysX",a),i.select2("val","").select2({placeholder:"Select ActivityX",data:r.get("notSelectedActivitiesX"),id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}),e(t.currentTarget).parent().fadeOut(function(){e(this).remove()}),n.is_changed=!0,is_changed=n.is_changed}});var b=new d;return n.Model=h,n.View=p,n.Collection=d,n.instructor_collection=b,o.bindAll(n,"addAll","addOne"),b.on("set",n.addAll),b.on("add",n.addOne),n},renderList:function(){var e=this;return e.showLoadTip(),e.is_changed=!1,is_changed=e.is_changed,e.getAll(),e},renderInstructorScheduleList:function(){var t=this;return t.showLoadTip(),t.is_changed=!1,is_changed=t.is_changed,t.getInstructorScheduleAll(),window.instructorScheduleInterval==null&&e(".tab-Instructor_schedule").hasClass("active")==1&&(window.instructorScheduleInterval=setInterval(function(){e(".tab-Instructor_schedule").hasClass("active")!=1?(clearInterval(window.instructorScheduleInterval),window.instructorScheduleInterval=null):t.getInstructorScheduleAll()},3e5)),t},getInstructorScheduleAll:function(){var t=this,n=t.conf,r=t.queryParams.queryDate,i=JSON.stringify({queryDate:t.dateToString(r)});n.EL.empty(),t.instructor_collection.reset(),e(".tab-Instructor_schedule").hasClass("active")!=1&&clearInterval(window.instructorScheduleInterval),console.log(t.queryParams.queryDate),e.ajax({url:n.url_fetch_instructor_sch_collection,type:"GET",data:{params:i},success:function(e){t.instructorSchedulerHeaderAppend(e,r),t.instructor_collection.reset(),t.instructor_collection.set(e.instructorLessonLinkDTO)}})},instructorSchedulerHeaderAppend:function(t,n){typeof t=="string"&&t.indexOf("DOCTYPE")!==-1&&location.reload();if(t){var r="";t.instructorLessonLinkDTO.length<=0?r="No Appointments Today":r=t.dayStartEndTime+" "+t.timeZone;var i=new Date(n),s=i.toLocaleString("default",{month:"long"})+" "+i.getDate()+", "+i.getFullYear(),o=e("#instructor_sch_table");o.find(".instructor-hours").text(r),o.find(".schedule-header .schedule-date").text(s)}else console.log("no data available")},getAll:function(){var t=this,n=t.conf;n.EL.empty(),t.instructor_collection.reset(),e.ajax({url:n.url_fetch_collection,type:"GET",success:function(e){t.instructor_collection.reset(),t.instructor_collection.set(e.listInstructorDtos)}})},addAll:function(){var e=this;e.instructor_collection.each(e.addOne)},addOne:function(e){var t=this,n=t.conf.EL,r=new t.View({model:e});n.append(r.render().el)},showWarn:function(){var t=e(".instructor-list_remv tr.disabled").nextAll().find(".js-update-instructor_remv"),n=220,r=t.offset().top-t.height()-e(".warn-tip").height();e(".warn-tip").css({right:n,top:r}).show()},hideWarn:function(){e(".warn-tip").hide()},showLoadTip:function(){var e=this,t=e.conf.EL;t.append('<div class="loader"><p></p></div>')},queryPreDate:function(){var e=this.queryParams.queryDate.getTime()-864e5;this.queryParams.queryDate=new Date(e),this.renderInstructorScheduleList()},queryNextDate:function(){var e=this.queryParams.queryDate.getTime()+864e5;this.queryParams.queryDate=new Date(e),this.renderInstructorScheduleList()},dateToString:function(e){var t=e.getFullYear(),n=(e.getMonth()+1).toString(),r=e.getDate().toString();n.length==1&&(n="0"+n),r.length==1&&(r="0"+r);var i=t+"-"+n+"-"+r;return i}},c}),define("staff",["jquery","select2","icheck","bootbox","mustache","underscore","backbone"],function(e,t,n,r,i,s,o){function u(t){this.defaults={url_fetch_collection:"instructor/loadStaffList.htm",url_fetch_model:"instructor/loadStaffDetail.htm",url_update_model:"instructor/updateStaff.htm",url_fetch_collection_staffManager:"centralized/loadStaffList.htm",url_updateManager_model:"centralized/updateStaffManager.htm",EL:e(".staff-list"),formTarget:""},this.init(t),this.is_changed=!1}return u.prototype={init:function(t){var n=this;n.conf=e.extend(n.defaults,t)},renderList:function(t){var n=this,u=n.conf,a,f,l,c;n.is_changed=!1,is_changed=n.is_changed,n.showLoadTip(),a=o.Model.extend({methodUrl:{read:u.url_fetch_model,create:u.url_updateManager_model,update:u.url_update_model,"delete":""},defaults:{},sync:function(e,t,n){t.methodUrl&&t.methodUrl[e.toLowerCase()]&&(n=n||{},n.url=t.methodUrl[e.toLowerCase()]),o.sync(e,t,n)}}),f=o.View.extend({initialize:function(){return this},events:{"click .edit-staff":"edit","click .close-edit-panel":"close","click .js-update-staff":"update",'ifChanged input[name="active"]':"changeActive"},tagName:"table",className:"table table-list table-fixed ",template:e("#tmpl_staff").html(),render:function(){return this.$el.html(i.render(this.template,this.model.toJSON())),this},edit:function(t){var i=this,s=e(t.currentTarget);if(s.hasClass("editing"))return;n.is_changed?r.dialog({title:"Warning!",message:"You have unsaved information. Do you want to continue?",buttons:{success:{label:"Continue",className:"btn btn-important",callback:function(){u.EL.find(".editing").removeClass("editing"),u.EL.find("tr.disabled").removeClass("disabled").nextAll().hide(),n.hideWarn(),i.getOne()}},cancle:{label:"Cancel",className:"btn btn-primary",callback:function(){n.showWarn();return}}}}):(u.EL.find(".editing").removeClass("editing"),u.EL.find("tr.disabled").removeClass("disabled").nextAll().hide(),s.addClass("editing"),i.getOne())},getOne:function(){var t=this;e.ajax({url:u.url_fetch_model,type:"GET",data:{id:t.model.get("id")},success:function(e){typeof e=="string"&&e.indexOf("DOCTYPE")!==-1&&location.reload(),t.model.set(e),t.editRender(),t.$el.find("tr").eq(0).addClass("disabled").nextAll().show(),n.is_changed=!1,is_changed=n.is_changed}})},editRender:function(){var e=new c({model:this.model});this.$el.find(".edit-staff").addClass("editing"),this.$el.find(".js-location-wrap").empty().append(e.render().el),this.model.get("active")?this.$el.find('input[name="active"]').iCheck().iCheck("indeterminate"):this.$el.find('input[name="active"]').iCheck().iCheck("determinate")},close:function(){return this.$el.find(".edit-staff").removeClass("editing"),this.$el.find("tr").eq(0).removeClass("disabled").nextAll().hide(),this.$el.find('input[name="active"]').iCheck("destory"),n.is_changed=!1,is_changed=n.is_changed,n.hideWarn(),this},changeActive:function(e){n.is_changed=!0,is_changed=n.is_changed,e.currentTarget.checked?this.model.set("active",!1):this.model.set("active",!0)},update:function(){var e=this;this.$el.find(".edit-staff").removeClass("editing"),n.hideWarn(),this.$el.find(".js-update-staff").attr("disabled",!0),t?this.model.sync("create",this.model,{success:function(t){t.status?(e.model.set(t.object),e.render(),e.close()):notification.autoHideError(t.message),e.$el.find(".js-update-staff").attr("disabled",!1)}}):this.model.sync("update",this.model,{success:function(t){t.status?(e.model.set(t.object),e.render(),e.close()):notification.autoHideError(t.message),e.$el.find(".js-update-staff").attr("disabled",!1)}})}}),l=o.Collection.extend({model:a,methodUrl:{read:u.url_fetch_collection,create:"",update:"","delete":""},sync:function(e,t,n){t.methodUrl&&t.methodUrl[e.toLowerCase()]&&(n=n||{},n.url=t.methodUrl[e.toLowerCase()]),o.sync(e,t,n)}}),c=o.View.extend({className:"row",events:{"click .js-btn-add":"add","click .js-remove-role":"remove"},template:e("#tmpl_location").html(),subTemplate:e("#tmpl_role_item").html(),render:function(){return this.$el.html(i.render(this.template,this.model.toJSON())),this.$el.find('input[name="location"]').select2({data:this.model.get("notSelectedLocation")}),this.$el.find('input[name="role"]').select2({data:this.model.get("roles"),id:function(e){return e.roleId},formatResult:function(e){return e.roleName},formatSelection:function(e){return e.roleName}}),this},add:function(){var e=this.$el.find('input[name="location"]'),t=this.$el.find('input[name="role"]'),r=this.$el.find(".js-role-list"),o=e.val(),u=t.select2("data"),a=this.subTemplate,f=this.model;if(e.select2("val")===""||t.select2("val")==="")return;s.each(f.get("notSelectedLocation"),function(e,t){e.id==o&&(e.roleToLocation=o+" - "+u.roleName,f.get("selectedLocationsList").push(e),r.append(i.render(a,e)))});var l=s.filter(f.get("notSelectedLocation"),function(e,t){return e.id!=o});f.set("notSelectedLocation",l),e.select2("val","").select2({data:this.model.get("notSelectedLocation")}),n.is_changed=!0,is_changed=n.is_changed},remove:function(t){var r=this.$el.find('input[name="location"]'),i=this.$el.find('input[name="role"]'),o=this.model,u=e(t.currentTarget).data("id"),a=s.filter(o.get("selectedLocationsList"),function(e,t){return e.id==u*1});o.get("notSelectedLocation").push(a[0]);var f=s.filter(o.get("selectedLocationsList"),function(e,t){return e.id!=u*1});o.set("selectedLocationsList",f),r.select2("val","").select2({data:this.model.get("notSelectedLocation")}),e(t.currentTarget).parent().fadeOut(function(){e(this).remove()}),n.is_changed=!0,is_changed=n.is_changed}});var h=new l;return n.Model=a,n.View=f,n.Collection=l,n.staff_collection=h,s.bindAll(n,"addAll","addOne"),h.on("set",n.addAll),h.on("add",n.addOne),e("#togBtn").change(function(){n.getAll()}),t?n.getAllForStaffManager():n.getAll(),n},getAllForStaffManager:function(){var t=this,n=t.conf;e("#rep-load-fade1").show(),e("#rep-load-modal1").show(),n.EL.empty(),e.ajax({url:n.url_fetch_collection_staffManager,type:"GET",success:function(n){e("#rep-load-modal1").hide(),e("#rep-load-fade1").hide(),t.staff_collection.reset(),t.staff_collection.set(n.listStaffDtos),console.log(n)},error:function(t){e("#rep-load-modal1").hide(),e("#rep-load-fade1").hide()}})},getAll:function(){var t=this,n=e("#togBtn")[0].checked,r=t.conf;e("#rep-load-fade").show(),e("#rep-load-modal").show(),r.EL.empty(),e.ajax({url:r.url_fetch_collection,type:"POST",data:{checkStatus:n},success:function(n){e("#rep-load-modal").hide(),e("#rep-load-fade").hide(),t.staff_collection.reset(),t.staff_collection.set(n.listStaffDtos)},error:function(t){e("#rep-load-modal").hide(),e("#rep-load-fade").hide()}})},addAll:function(){var e=this;e.staff_collection.each(e.addOne)},addOne:function(e){var t=this.conf.EL,n=new this.View({model:e});t.append(n.render().el)},hideWarn:function(){e(".warn-tip").hide()},showWarn:function(){var t=e(".staff-list tr.disabled").nextAll().find(".js-update-staff"),n=220,r=t.offset().top-t.height()-e(".warn-tip").height();e(".warn-tip").css({right:n,top:r}).show()},showLoadTip:function(){var e=this,t=e.conf.EL;t.append('<div class="loader"><p></p></div>')}},u}),define("service",["jquery","select2","icheck","bootbox","mustache","underscore","backbone","notification"],function(e,t,n,r,i,s,o,u){function a(t){this.defaults={url_fetch_collection:"profileService/loadProfileServiceList.htm",url_create_model:"profileService/addProfileService/saveProService.htm",url_fetch_activity:"profileService/addProfileService/addSelectedActivity.htm",url_getActivityByService:"profileService/addProfileService/loadActivitiesInServiceList.htm",url_fetch_services:"profileService/addProfileService/loadAddedServiceList.htm",url_fetch_service_detail:"profileService/loadProfileServiceDetail.htm",url_update_serivce:"profileService/updatepProfileService.htm",url_delete_service:"profileService/loadProfileServiceDetail.htm",url_delete_serivce:"profileService/deleteProfileService.htm",formTarget:e(".js-service-form"),EL:e(".service-list")},this.init(t),this.is_form_init=!1,this.is_changed=is_changed}return a.prototype={init:function(t){var n=this;n.conf=e.extend(n.defaults,t)},newServiceForm:function(){if(this.is_form_init)return;var t=this,n=t.conf,r;return r=o.Model.extend({initialize:function(){var r=this,i=this.formEL;i.serviceType.select2({ajax:{url:n.url_fetch_services,data:function(e,t){return{}},results:function(e,t){return{results:e}}},id:function(e){return e.serviceId},formatResult:function(e){return e.serviceName},formatSelection:function(e){return e.serviceName}}).on("change",function(t){var r=e(this).val();e.ajax({url:n.url_getActivityByService,data:{serviceId:r},success:function(e){i.activity.val(""),n.formTarget.find(".js-activity-list").empty(),i.activityList.select2("val","").select2({placeholder:"Select Available Activity",data:e,id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}})}})}),i.activityList.select2({placeholder:"Select Available Activity",data:[],id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}),i.enable.iCheck(),s.bindAll(this,"addActivity","removeActivity"),n.formTarget.find(".js-add-activity-item").on("click",function(e){e.preventDefault();if(i.activityList.select2("val")=="")return;r.addActivity(),r.changeActivity()}),n.formTarget.find(".js-activity-list").on("click",".js-remove-activity",function(t){e(this).parent().fadeOut(function(){e(this).remove()}),r.removeActivity(e(this).data("id")),r.changeActivity()}),t.is_form_init=!0},changeActivity:function(){var n=this.formEL,r=t.conf;n.activityList.select2("readonly",!0),e.ajax({url:r.url_fetch_activity,data:{activityString:n.activity.val(),serviceId:n.serviceType.val()},success:function(e){var t=[];e.activityList&&e.activityList.length>0&&(t=e.activityList),n.activityList.select2("val","").select2({placeholder:"Select Available Activity",data:t,id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}).select2("readonly",!1)}})},addActivity:function(){var n=this.formEL,r=e("#tmpl_activity_item").html(),s=n.activityList.select2("data"),o=t.conf;if(s==null||s.length==0)return;o.formTarget.find(".js-activity-list").append(i.render(r,s));var u=n.activity.val().split(",");u.push(s.activityId),u[0]==""&&u.shift(),n.activity.val(u.toString())},removeActivity:function(e){var t=this.formEL,n=t.activity.val().split(",");currentVal=s.filter(n,function(t){return t!=e}),t.activity.val(currentVal.toString())},clear:function(){var e=this.formEL,n=t.conf;e.serviceType.select2("val",""),e.activityList.select2("val",""),e.activity.val(""),e.enable.iCheck("uncheck"),n.formTarget.find(".js-activity-list").empty()},defaults:{enable:""},formEL:{serviceType:n.formTarget.find('input[name="serviceType"]'),activityList:n.formTarget.find('input[name="activityList"]'),activity:n.formTarget.find('input[name="activity"]'),enable:n.formTarget.find('input[name="enable"]')}}),t.newservice=new r,t},showForm:function(){var e=this.conf;e.formTarget.parent().slideDown()},hideForm:function(){var t=this.conf;t.formTarget.parent().slideUp(),t.formTarget.find(".form-msg").hide(),e(".js-show-service-form").removeClass("active"),this.newservice.clear()},renderList:function(){var t=this,n=t.conf,a,f,l,c;t.is_changed=!1,is_changed=t.is_changed,t.showLoadTip(),a=o.Model.extend({methodUrl:{read:n.url_fetch_model,create:n.url_create_model,update:n.url_update_serivce,"delete":n.url_delete_serivce},defaults:{enable:!1},sync:function(e,t,n){t.methodUrl&&t.methodUrl[e.toLowerCase()]&&(n=n||{},n.url=t.methodUrl[e.toLowerCase()]),o.sync(e,t,n)}}),f=o.View.extend({initialize:function(){return this},events:{"click .edit-service":"edit","click .close-edit-panel":"close",'ifChanged input[name="active"]':"changeActive","click .js-delete-service":"delete","click .js-update-service":"update"},tagName:"table",className:"table table-list table-fixed",template:e("#tmpl_service").html(),render:function(){return this.$el.html(i.render(this.template,this.model.toJSON())),this},edit:function(i){var s=this,o=e(i.currentTarget);if(o.hasClass("editing"))return;t.is_changed?r.dialog({title:"Warning!",message:"You have unsaved information. Do you want to continue?",buttons:{success:{label:"Continue",className:"btn btn-important",callback:function(){n.EL.find(".editing").removeClass("editing"),n.EL.find("tr.disabled").removeClass("disabled").nextAll().hide(),t.hideWarn(),s.getOne()}},cancle:{label:"Cancel",className:"btn btn-primary",callback:function(){t.showWarn();return}}}}):(n.EL.find(".editing").removeClass("editing"),n.EL.find("tr.disabled").removeClass("disabled").nextAll().hide(),o.addClass("editing"),s.getOne())},getOne:function(){var r=this;e.ajax({url:n.url_fetch_service_detail,type:"GET",data:{id:r.model.get("serviceId")},success:function(e){typeof e=="string"&&e.indexOf("DOCTYPE")!==-1&&location.reload(),r.model.set(e),r.editRender(),r.$el.find("tr").eq(0).addClass("disabled").nextAll().show(),t.is_changed=!1,is_changed=t.is_changed}})},editRender:function(){var e=new c({model:this.model});this.$el.find(".edit-service").addClass("editing"),this.$el.find('select[name="requireInstructor"]').select2(),this.$el.find('select[name="activityAvailable"]').select2(),this.model.get("enable")?this.$el.find('input[name="active"]').iCheck().iCheck("indeterminate"):this.$el.find('input[name="active"]').iCheck().iCheck("determinate"),this.$el.find(".js-activity-wrap").empty().append(e.render().el)},close:function(){return this.$el.find(".edit-service").removeClass("editing"),this.$el.find("tr").eq(0).removeClass("disabled").nextAll().hide(),this.$el.find('select[name="requireInstructor"]').select2("destroy"),this.$el.find('select[name="activityAvailable"]').select2("destroy"),this.$el.find('input[name="active"]').iCheck("destroy"),t.hideWarn(),t.is_changed=!1,is_changed=t.is_changed,this},changeActive:function(e){t.is_changed=!0,is_changed=t.is_changed,e.currentTarget.checked?this.model.set("enable",!1):this.model.set("enable",!0)},update:function(n){var r=this;this.model.unset("avaibleAct"),this.$el.find(".js-update-service").attr("disabled",!0),this.model.sync("update",this.model,{success:function(n){n.status?(r.model.set(n.object),is_changed=!1,e(".js-show-service-activity").click(),r.$el.find(".edit-service").removeClass("editing"),t.hideWarn()):u.autoHideError(n.message),r.$el.find(".js-update-service").attr("disabled",!1)}})},"delete":function(t){var i=this;r.dialog({title:"Warning!",message:"Are you sure to Delete this Service?",buttons:{success:{label:"Delete",className:"btn btn-important",callback:function(){e.ajax({type:"GET",url:n.url_delete_serivce,data:{serviceId:i.model.get("serviceId")},success:function(e){e.status?i.$el.fadeOut(function(){this.remove()}):u.autoHideError(e.message)}})}},cancle:{label:"Cancel",className:"btn btn-primary"}}})}}),c=o.View.extend({events:{"click .js-btn-add":"add"},className:"",template:e("#tmpl_service_activity").html(),subTemplate:e("#tmpl_service_activity_item").html(),render:function(){return this.$el.html(i.render(this.template,this.model.toJSON())),this.$el.find('input[name="activities"]').select2({data:this.model.get("notSelecteDtos"),id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}),this},add:function(){var e=this.$el.find('input[name="activities"]').val(),n=this.$el.find('input[name="activities"]'),r=this.$el.find(".js-activity-list"),o=this.subTemplate,u=this.model;if(e==="")return;s.each(u.get("notSelecteDtos"),function(t,n){t.activityId==e&&(u.get("activityDTOs").push(t),r.append(i.render(o,t)))});var a=s.filter(u.get("notSelecteDtos"),function(t,n){return t.activityId!==e*1});u.set("notSelecteDtos",a),n.select2({data:this.model.get("notSelecteDtos"),id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}),t.is_changed=!0,is_changed=t.is_changed}}),l=o.Collection.extend({model:a,methodUrl:{read:n.url_fetch_collection,create:"",update:"","delete":""},sync:function(e,t,n){t.methodUrl&&t.methodUrl[e.toLowerCase()]&&(n=n||{},n.url=t.methodUrl[e.toLowerCase()]),o.sync(e,t,n)}});var h=new l;t.Model=a,t.View=f,t.Collection=l,t.service_collection=h,s.bindAll(t,"addAll","addOne"),h.on("set",t.addAll),h.on("add",t.addOne),t.getAll()},getAll:function(){var e=this;e.conf.EL.empty(),e.service_collection.reset(),e.service_collection.fetch()},addAll:function(){var e=this;e.service_collection.each(e.addOne)},addOne:function(e){var t=this.conf.EL,n=new this.View({model:e});t.append(n.render().el)},showLoadTip:function(){var e=this,t=e.conf.EL;t.append('<div class="loader"><p></p></div>')},showWarn:function(){var t=e(".service-list tr.disabled").nextAll().find(".js-update-service"),n=220,r=t.offset().top-t.height()-e(".warn-tip").height();e(".warn-tip").css({right:n,top:r}).show()},hideWarn:function(){e(".warn-tip").hide()},create:function(){var t=this,n=e(".js-service-form").serializeArray(),r=new t.Model;s.each(n,function(e,t){r.set(e.name,e.value)}),r.unset("activityList"),r.sync("create",r,{success:function(n){n.status?(t.hideForm(),e(".js-show-service-activity").click(),t.is_changed=!1,is_changed=t.is_changed):t.conf.formTarget.find(".form-msg").text(n.message).show()}})}},a}),define("activity",["jquery","select2","icheck","bootbox","mustache","underscore","backbone"],function(e,t,n,r,i,s,o){function u(t){this.defaults={url_fetch_collection:"profileActivity/loadProfileActivityList.htm",url_fetch_services:"roomTemplate/loadRoomTemplateServices.htm",url_create_model:"profileActivity/addProfileActivity.htm",url_fetch_activity_detail:"service/loadActivityDetail.htm",url_update_activity:"service/updateActivity.htm",url_delete_activity:"profileActivity/deleteProfileActivity.htm",url_activity_typeahead:"profileActivity/searchActivityByName.htm",url_initProfileActivity:"profileActivity/initProfileActivity.htm",EL:e(".activity-list"),formTarget:e(".js-activity-form"),formTrigger:e(".js-show-activity-form")},this.init(t),this.is_form_init=!1,this.is_changed=is_changed}return u.prototype={init:function(t){this.conf=e.extend(this.defaults,t)},newActivityForm:function(){if(this.is_form_init)return;var t=this,n=t.conf,r;return r=o.Model.extend({initialize:function(){var r=this,i=this.formEL;i.activityId.select2({placeholder:"Enter Activity Name",multiple:!0,maximumSelectionSize:1,minimumInputLength:1,ajax:{url:n.url_activity_typeahead,dataType:"json",data:function(e,t){return{activityName:e}},results:function(e,t){return{results:e}}},id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName},dropdownCssClass:"dropdown-menu"}).on("change",function(t){var s=t.val[0];s&&s!==""?e.ajax({url:n.url_initProfileActivity,data:{activityId:s},success:function(e){i.serviceType.select2("data",e.service).select2("readonly",!0),i.minAttenders.val(e.minAttender).attr("readonly",""),i.maxAttenders.val(e.maxAttender).attr("readonly",""),i.instructor.select2("val",e.instructor).select2("readonly",!0),i.minimumDuration.select2("data",{id:e.minimumDuration,text:e.minimumDurationText}).select2("readonly",!0),i.maxmumDuration.select2("data",{id:e.maxmumDuration,text:e.maxmumDurationText}).select2("readonly",!0),e.enable?i.enable.iCheck("check"):i.enable.iCheck("uncheck")}}):r.clear()}),i.enable.iCheck(),i.instructor.select2(),i.minimumDuration.select2(),i.maxmumDuration.select2(),i.serviceType.select2({placeholder:"Select Service Type",data:[],id:function(e){return e.id},formatResult:function(e){return e.serviceName},formatSelection:function(e){return e.serviceName}}),t.is_form_init=!0},clear:function(){var e=this.formEL;e.activityId.select2("val",""),e.serviceType.select2("val","").select2({placeholder:"Select Service Type",data:[],id:function(e){return e.id},formatResult:function(e){return e.serviceName},formatSelection:function(e){return e.serviceName}}).select2("readonly",!1),e.minAttenders.val("").removeAttr("readonly"),e.maxAttenders.val("").removeAttr("readonly"),e.minimumDuration.select2("val","").select2("readonly",!1),e.maxmumDuration.select2("val","").select2("readonly",!1),e.instructor.select2("val","").select2("readonly",!1),e.enable.iCheck("enable").iCheck("uncheck")},defaults:{},formEL:{activityId:n.formTarget.find('input[name="activityId"]'),serviceType:n.formTarget.find('input[name="serviceType"]'),minAttenders:n.formTarget.find('input[name="minAttenders"]'),maxAttenders:n.formTarget.find('input[name="maxAttenders"]'),minimumDuration:n.formTarget.find('select[name="minimumDuration"]'),maxmumDuration:n.formTarget.find('select[name="maxmumDuration"]'),enable:n.formTarget.find('input[name="enable"]'),instructor:n.formTarget.find('select[name="instructor"]')}}),t.newactivity=new r,t},showForm:function(){var e=this.conf;return e.formTarget.parent().slideDown(),this},hideForm:function(){var e=this.conf;return e.formTarget.parent().slideUp(),e.formTarget.find(".form-msg").hide(),e.formTrigger.removeClass("active"),this.newactivity.clear(),this},renderList:function(){var t=this,n=t.conf,u=n.EL,a,f,l;t.is_changed=!1,is_changed=t.is_changed,t.showLoadTip(),a=o.Model.extend({methodUrl:{read:n.url_fetch_model,create:n.url_create_model,update:n.url_update_activity,"delete":n.url_delete_activity},sync:function(e,t,n){t.methodUrl&&t.methodUrl[e.toLowerCase()]&&(n=n||{},n.url=t.methodUrl[e.toLowerCase()]),o.sync(e,t,n)}}),f=o.View.extend({initialize:function(){return this},events:{"click .edit-activity":"edit","click .close-edit-panel":"close",'ifChanged input[name="active"]':"changeActive","click .js-update-activity":"update","click .js-delete-activity":"delete"},tagName:"table",className:"table table-list table-fixed",template:e("#tmpl_activity_list").html(),render:function(){return this.$el.html(i.render(this.template,this.model.toJSON())),this},edit:function(n){var i=this,s=e(n.currentTarget);if(s.hasClass("editing"))return;t.is_changed?r.dialog({title:"Warning!",message:"You have unsaved information. Do you want to continue?",buttons:{success:{label:"Continue",className:"btn btn-important",callback:function(){u.find(".editing").removeClass("editing"),u.find("tr.disabled").removeClass("disabled").nextAll().hide(),t.hideWarn(),i.getOne()}},cancle:{label:"Cancel",className:"btn btn-primary",callback:function(){t.showWarn();return}}}}):(u.find(".editing").removeClass("editing"),u.find("tr.disabled").removeClass("disabled").nextAll().hide(),s.addClass("editing"),i.getOne())},getOne:function(){var r=this;e.ajax({url:n.url_fetch_activity_detail,type:"GET",data:{id:r.model.get("activityId")},success:function(e){typeof e=="string"&&e.indexOf("DOCTYPE")!==-1&&location.reload(),r.model.set(e),r.editRender(),r.$el.find("tr").eq(0).addClass("disabled").nextAll().show(),t.is_changed=!1,is_changed=t.is_changed}})},editRender:function(){this.$el.find(".edit-activity").addClass("editing"),this.$el.find('input[name="name"]').val(this.model.get("activityName")),this.$el.find('input[name="serviceType"]').select2({data:[],id:function(e){return e.serviceId},formatResult:function(e){return e.serviceName},formatSelection:function(e){return e.serviceName}}).select2("data",this.model.get("serviceDTO")).select2("readonly",!0),this.$el.find('select[name="requireInstructor"]').select2().select2("val",this.model.get("requiresInstructor")).select2("readonly",!0),this.$el.find('input[name="minDuration"]').select2({data:[]}).select2("data",{text:this.model.get("minimumDurationText"),value:this.model.get("minimumDuration")}).select2("readonly",!0),this.$el.find('input[name="maxDuration"]').select2({data:[]}).select2("data",{text:this.model.get("maxmumDurationText"),value:this.model.get("maxmumDuration")}).select2("readonly",!0),this.$el.find('input[name="minimunAttendees"]').val(this.model.get("minimunAttendees")),this.$el.find('input[name="maximunAttendees"]').val(this.model.get("maximunAttendees")),this.model.get("enable")?this.$el.find('input[name="active"]').iCheck().iCheck("uncheck"):this.$el.find('input[name="active"]').iCheck().iCheck("check")},close:function(){return this.$el.find(".edit-activity").removeClass("editing"),this.$el.find('input[name="serviceType"]').select2("destroy"),this.$el.find('select[name="requireInstructor"]').select2("destroy"),this.$el.find('select[name="minDuration"]').select2("destroy"),this.$el.find('select[name="maxDuration"]').select2("destroy"),this.$el.find('input[name="active"]').iCheck("destroy"),t.hideWarn(),this.$el.find("tr").eq(0).removeClass("disabled").nextAll().hide(),t.is_changed=!1,is_changed=t.is_changed,this},update:function(){var n=this;this.$el.find(".js-update-activity").attr("disabled",!0),this.model.sync("update",this.model,{success:function(r){r.status?(n.model.set(r.object),is_changed=!1,e(".js-show-service-activity").click(),n.close(),n.$el.find(".edit-service").removeClass("editing"),t.hideWarn()):notification.autoHideError(r.message),n.$el.find(".js-update-activity").attr("disabled",!1)}})},"delete":function(){var t=this;r.dialog({title:"Warning!",message:"Are you sure to Delete this Activity?",buttons:{success:{label:"Delete",className:"btn btn-important",callback:function(){e.ajax({type:"GET",url:n.url_delete_activity,data:{activityId:t.model.get("activityId")},success:function(e){e.status?t.$el.fadeOut(function(){this.remove()}):notification.autoHideError(e.message)}})}},cancle:{label:"Cancel",className:"btn btn-primary"}}})},changeActive:function(e){t.is_changed=!0,is_changed=t.is_changed,e.currentTarget.checked?this.model.set("enable",!1):this.model.set("enable",!0)}}),l=o.Collection.extend({model:a,methodUrl:{read:n.url_fetch_collection,create:"",update:"","delete":""},sync:function(e,t,n){t.methodUrl&&t.methodUrl[e.toLowerCase()]&&(n=n||{},n.url=t.methodUrl[e.toLowerCase()]),o.sync(e,t,n)}});var c=new l;t.Model=a,t.View=f,t.Collection=l,t.activity_collection=c,s.bindAll(t,"addAll","addOne"),c.on("set",t.addAll),c.on("add",t.addOne),t.getAll()},getAll:function(){var e=this;e.conf.EL.empty(),e.activity_collection.reset(),e.activity_collection.fetch()},addAll:function(){var e=this;e.activity_collection.each(e.addOne)},addOne:function(e){var t=this,n=t.conf.EL,r=new t.View({model:e});n.append(r.render().el)},showLoadTip:function(){var e=this,t=e.conf.EL;t.append('<div class="loader"><p></p></div>')},hideWarn:function(){e(".warn-tip").hide()},showWarn:function(){var t=this.conf.EL,n=t.find("tr.disabled").nextAll().find(".js-update-activity"),r=220,i=n.offset().top-n.height()-e(".warn-tip").height();e(".warn-tip").css({right:r,top:i}).show()},create:function(t){var n=this,r=n.conf.formTarget,i=r.serializeArray(),o=new n.Model;s.each(i,function(e,t){o.set(e.name,e.value)}),o.sync("create",o,{success:function(t){t.status?(n.hideForm(),e(".js-show-service-activity").click(),n.is_changed=!1,is_changed=n.is_changed):n.conf.formTarget.find(".form-msg").text(t.message).show()}})}},u}),define("room",["jquery","select2","icheck","bootbox","mustache","underscore","backbone","notification"],function(e,t,n,r,i,s,o,u){function a(t){this.defaults={url_fetch_roomTemplate:"roomTemplate/loadRoomTemplate.htm",url_change_roomTemplate:"room/selectRoomTemplate.htm",url_fetch_services:"roomTemplate/loadRoomTemplateServices.htm",url_create_room_template:"roomTemplate/createTemplateRoom.htm",url_fetch_room:"room/loadRoom.htm",url_fetch_detail_room:"room/getEditRoom.htm",url_delete_room:"room/deleteRoom.htm",url_update_room:"room/editRoom.htm",url_create_room:"room/createRoom.htm",url_fetch_unselected_service:"room/loadRoomServices.htm",url_fetch_unselected_activity:"room/loadRoomActivities.htm",formTarget:e(".js-room-form"),EL:e(".room-list")},this.init(t),this.is_edit=!1,this.is_changed=is_changed}return a.prototype={init:function(t){var n=this;n.conf=e.extend(n.defaults,t)},newRoomForm:function(){var t=this,n=t.conf,r;return r=o.Model.extend({initialize:function(){var t=this,r=this.formEL;r.roomTemplate.select2().on("change",function(t){var s=e(t.currentTarget).val();r.roomNumber.select2("readonly",!0),r.roomSize.select2("readonly",!0),r.servicesList.select2("readonly",!0),r.activityList.select2("readonly",!0),e.ajax({url:n.url_change_roomTemplate,type:"GET",data:{roomTemplateId:s},success:function(t){n.formTarget.find(".js-service-list").empty(),n.formTarget.find(".js-activity-list").empty(),r.services.val(""),r.activities.val("");var s=t.canSplit,o=t.isShowRoomSize,u=t.roomNumberList,a=t.roomTemplate,f=t.roomSizeList,l=t.activityList,c=t.serviceList,h=t.selectedServiceList,p=t.selectedActivityList;r.roomNumber.select2({data:u,id:function(e){return e.roomNumberId},formatResult:function(e){return e.roomNumber},formatSelection:function(e){return e.roomNumber}}).select2("readonly",!1),r.roomSize.select2({data:f,id:function(e){return e.roomSizeId},formatResult:function(e){return e.roomSizeName},formatSelection:function(e){return e.roomSizeName}}).select2("data",t.roomTemplate.roomSize).select2("readonly",!1),r.servicesList.select2({placeholder:"Select Available Services",data:c,id:function(e){return e.serviceId},formatResult:function(e){return e.serviceName},formatSelection:function(e){return e.serviceName}}).select2("readonly",!1);var d=[];n.formTarget.find(".js-service-list").empty(),r.services.val(""),h.forEach(function(t){var r=e("#tmpl_service_item").html();n.formTarget.find(".js-service-list").append(i.render(r,t)),d.push(t.serviceId)}),r.services.val(d.toString()),r.activityList.select2({placeholder:"Select Available Activities",data:l,id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}).select2("readonly",!1);var v=[];n.formTarget.find(".js-activity-list").empty(),r.activities.val(""),p.forEach(function(t){var r=e("#tmpl_activity_item").html();n.formTarget.find(".js-activity-list").append(i.render(r,t)),v.push(t.activityId)}),r.activities.val(v.toString()),a.isSplitRoom=="Y"?r.isSplitRoom.iCheck("enable").iCheck("check"):r.isSplitRoom.iCheck("enable").iCheck("uncheck"),s?(r.isSplitRoom.attr("disabled",!1).iCheck("enable"),n.formTarget.find(".js-split-room-wrap").show()):(r.isSplitRoom.attr("disabled",!0).iCheck("disable"),n.formTarget.find(".js-split-room-wrap").hide()),o?(r.roomSize.attr("disabled",!1).select2("enable",!0),n.formTarget.find(".js-room-size-wrap").show()):(r.roomSize.attr("disabled",!0).select2("enable",!1),n.formTarget.find(".js-room-size-wrap").hide())}})}),r.roomNumber.select2({data:[]}),r.roomSize.select2({data:[]}),r.isSplitRoom.iCheck(),r.servicesList.select2({placeholder:"Select Available Services",data:[]}),r.activityList.select2({placeholder:"Select Available Activities",data:[]}),r.enabled.iCheck(),s.bindAll(this,"addService","removeService","addActivity","removeActivity"),n.formTarget.find(".js-add-service-item").on("click",function(){t.addService()}),n.formTarget.find(".js-service-list").on("click",".js-remove-service",function(n){e(this).parent().fadeOut(function(){e(this).remove()}),t.removeService(e(this).data("id"))}),n.formTarget.find(".js-add-activity-item").on("click",function(){t.addActivity()}),n.formTarget.find(".js-activity-list").on("click",".js-remove-activity",function(n){e(this).parent().fadeOut(function(){e(this).remove()}),t.removeActivity(e(this).data("id"))})},changeService:function(){var t=this,r=t.formEL;r.servicesList.select2("readonly",!0),e.ajax({url:n.url_fetch_unselected_service,data:{roomTemplateId:r.roomTemplate.select2("val"),serviceString:r.services.val()},success:function(e){r.servicesList.select2("val","").select2({placeholder:"Select Available Services",data:e.serviceList,id:function(e){return e.serviceId},formatResult:function(e){return e.serviceName},formatSelection:function(e){return e.serviceName}}).select2("val","").select2("readonly",!1),t.changeActivity()}})},changeActivity:function(){var t=this.formEL;t.activityList.select2("readonly",!0),e.ajax({url:n.url_fetch_unselected_activity,data:{roomTemplateId:t.roomTemplate.select2("val"),activityString:t.activities.val(),serviceString:t.services.val()},success:function(r){t.activityList.select2("val","").select2({placeholder:"Select Available Activities",data:r.activityList,id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}).select2("val","").select2("readonly",!1),n.formTarget.find(".js-activity-list").empty(),t.activities.val("");var s=e("#tmpl_activity_item").html(),o=[];activityHTML="",r.selectedActivityList.forEach(function(e){activityHTML+=i.render(s,e),o.push(e.activityId),n.formTarget.find(".js-activity-list").html(activityHTML)}),t.activities.val(o.toString())}})},addService:function(){var t=this.formEL,r=e("#tmpl_service_item").html(),s=t.servicesList.select2("data");if(s==null||s.length==0)return;n.formTarget.find(".js-service-list").append(i.render(r,s));var o=t.services.val().split(",");o.push(s.serviceId),o[0]==""&&o.shift(),t.services.val(o.toString()),this.changeService()},removeService:function(e){var t=this.formEL,n=t.services.val().split(",");currentVal=s.filter(n,function(t){return t!=e}),t.services.val(currentVal.toString()),this.changeService()},addActivity:function(){var t=this.formEL,r=e("#tmpl_activity_item").html(),s=t.activityList.select2("data");if(s==null||s.length==0)return;n.formTarget.find(".js-activity-list").append(i.render(r,s));var o=t.activities.val().split(",");o.push(s.activityId),o[0]==""&&o.shift(),t.activities.val(o.toString()),this.changeActivity()},removeActivity:function(e){var t=this.formEL,n=t.activities.val().split(",");currentVal=s.filter(n,function(t){return t!=e}),t.activities.val(currentVal.toString()),this.changeActivity()},clear:function(){var e=this.formEL;e.roomTemplate.select2("val",""),e.roomNumber.select2("val","").select2({data:[]}).select2("readonly",!1),e.roomSize.attr("disabeld",!1).select2("val","").select2("readonly",!1),n.formTarget.find(".js-room-size-wrap").show(),e.servicesList.select2("val","").select2({placeholder:"Select Available Services",data:[]}).select2("readonly",!1),e.activityList.select2("val","").select2({placeholder:"Select Available Activities",data:[]}).select2("readonly",!1),e.services.val(""),e.activities.val(""),e.roomName.val(""),n.formTarget.find(".js-service-list").empty(),n.formTarget.find(".js-activity-list").empty(),e.isSplitRoom.attr("disabled",!1).iCheck("enable").iCheck("uncheck"),n.formTarget.find(".js-split-room-wrap").show(),e.enabled.iCheck("check")},defaults:{},formEL:{roomTemplate:n.formTarget.find('select[name="roomTemplate"]'),roomNumber:n.formTarget.find('input[name="roomNumber"]'),roomName:n.formTarget.find('input[name="profileRoomName"]'),roomSize:n.formTarget.find('input[name="roomSize"]'),isSplitRoom:n.formTarget.find('input[name="isSplitRoom"]'),services:n.formTarget.find('input[name="services"]'),servicesList:n.formTarget.find('input[name="servicesList"]'),activities:n.formTarget.find('input[name="activities"]'),activityList:n.formTarget.find('input[name="activityList"]'),enabled:n.formTarget.find('input[name="enabled"]')}}),t.newroom=new r,t},showForm:function(){var e=this.conf;e.formTarget.parent().slideDown()},hideForm:function(){var t=this.conf;t.formTarget.parent().slideUp(),e(".js-show-room-form").removeClass("active"),t.formTarget.find(".form-msg").hide(),this.newroom.clear()},renderList:function(){var t=this,n=t.conf,a,f,l;t.is_changed=!1,is_changed=t.is_changed,t.showLoadTip(),a=o.Model.extend({methodUrl:{read:n.url_fetch_model,create:n.url_create_room,update:n.url_update_room,"delete":n.url_delete_room},sync:function(e,t,n){t.methodUrl&&t.methodUrl[e.toLowerCase()]&&(n=n||{},n.url=t.methodUrl[e.toLowerCase()]),o.sync(e,t,n)},defaults:{}}),f=o.View.extend({initialize:function(){return this},events:{"click .js-edit-room":"edit","click .js-update-room":"update","click .js-delete-room":"delete","click .close-edit-panel":"close","click .js-add-service":"addService","click .js-remove-service":"removeService","click .js-add-activity":"addActivity","click .js-remove-activity":"removeActivity",'change input[name="roomTemplate"]':"ChangeTemplate",'ifChanged input[name="enabled"]':"changeActive",'ifChanged input[name="isSplitRoom"]':"changeSplit"},tagName:"table",className:"table table-list table-fixed",template:e("#tmpl_room").html(),render:function(){return this.$el.html(i.render(this.template,this.model.toJSON())),this},edit:function(n){var i=this,s=e(n.currentTarget);if(s.hasClass("editing"))return;t.is_changed?r.dialog({title:"Warning!",message:"You have unsaved information. Do you want to continue?",buttons:{success:{label:"Continue",className:"btn btn-important",callback:function(){e(".room-list .editing").removeClass("editing"),e(".room-list tr.disabled").removeClass("disabled").nextAll().hide(),t.hideWarn(),i.getOne()}},cancle:{label:"Cancel",className:"btn btn-primary",callback:function(){t.showWarn();return}}}}):(e(".room-list .editing").removeClass("editing"),e(".room-list tr.disabled").removeClass("disabled").nextAll().hide(),s.addClass("editing"),i.getOne())},getOne:function(){var r=this;e.ajax({url:n.url_fetch_detail_room,type:"GET",data:{roomId:r.model.get("roomId")},success:function(e){return typeof e=="string"&&e.indexOf("DOCTYPE")!==-1&&location.reload(),r.model.set(e.room),r.editRender(e.roomDetail,e.canSplit,e.isShowRoomSize),r.$el.find("tr").eq(0).addClass("disabled").nextAll().show(),t.is_changed=!1,is_changed=t.is_changed,r}})},editRender:function(n,r,o){var u=this,a=e("#tmpl_service_item").html(),f=e("#tmpl_activity_item").html();this.$el.find('input[name="roomTemplate"]').select2({data:n.roomTemplate,id:function(e){return e.roomTemplateId},formatResult:function(e){return e.roomTemplateName},formatSelection:function(e){return e.roomTemplateName}}).select2("val",this.model.get("roomTemplate")).on("select2-selecting",function(e){u.model.set("roomTemplate",e.val)}),this.$el.find('input[name="roomNumber"]').select2({data:n.roomNumber,id:function(e){return e.roomNumberId},formatResult:function(e){return e.roomNumber},formatSelection:function(e){return e.roomNumber}}).select2("val",this.model.get("roomNumber")).on("select2-selecting",function(e){t.is_changed=!0,is_changed=t.is_changed,u.model.set("roomNumber",e.val)}),n.roomSize.length>0?this.$el.find('input[name="roomSize"]').select2({placeholder:"Choose Room Size",data:n.roomSize,id:function(e){return e.roomSizeId},formatResult:function(e){return e.roomSizeName},formatSelection:function(e){return e.roomSizeName}}).select2("val",this.model.get("roomSize")).on("select2-selecting",function(e){t.is_changed=!0,is_changed=t.is_changed,u.model.set("roomSize",e.val)}):(u.$el.find(".js-room-size-wrap").hide(),u.model.set("roomSize",null)),this.$el.find('input[name="servicesList"]').select2({data:n.unSelectedServices,id:function(e){return e.serviceId},formatResult:function(e){return e.serviceName},formatSelection:function(e){return e.serviceName}}),this.$el.find(".js-service-list").empty(),s.each(this.model.get("serviceList"),function(e){u.$el.find(".js-service-list").append(i.render(a,e))}),this.$el.find('input[name="activityList"]').select2({data:n.unSelectedActivities,id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}),this.$el.find(".js-activity-list").empty(),s.each(this.model.get("activityList"),function(e){u.$el.find(".js-activity-list").append(i.render(f,e))}),this.$el.find('input[name="profileRoomName"]').val(this.model.get("profileRoomName")).on("change",function(){t.is_changed=!0,is_changed=t.is_changed,u.model.set("profileRoomName",this.value)}),this.model.get("enabled")?this.$el.find('input[name="enabled"]').iCheck().iCheck("uncheck"):this.$el.find('input[name="enabled"]').iCheck().iCheck("check"),this.model.get("isSplitRoom")?this.$el.find('input[name="isSplitRoom"]').iCheck().iCheck("determinate"):this.$el.find('input[name="isSplitRoom"]').iCheck().iCheck("indeterminate"),r||this.$el.find(".js-split-room-wrap").hide(),o||this.$el.find(".js-room-size-wrap").hide(),this.$el.find(".js-edit-room").addClass("editing")},ChangeTemplate:function(r){t.is_changed=!0,is_changed=t.is_changed;var s=this,o=e(r.currentTarget).val();e.ajax({url:n.url_change_roomTemplate,data:{roomTemplateId:o},success:function(t){var n=t.roomNumberList,r=t.roomTemplate,o=t.roomSizeList,u=t.activityList,a=t.serviceList,f=t.selectedServiceList,l=t.selectedActivityList,c=t.canSplit,h=t.isShowRoomSize;console.log(s.model.attributes),s.model.get("parentRoom")?(s.$el.find(".js-split-room-wrap").hide(),s.$el.find(".js-room-size-wrap").hide(),s.model.unset("isSplitRoom"),s.model.set("roomSize",null)):(console.log(c),c?(s.$el.find(".js-split-room-wrap").show(),r.isSplitRoom==="Y"?s.$el.find('input[name="isSplitRoom"]').iCheck("check"):s.$el.find('input[name="isSplitRoom"]').iCheck("uncheck")):(s.$el.find(".js-split-room-wrap").hide(),s.model.unset("isSplitRoom")),h?(s.$el.find(".js-room-size-wrap").show(),s.$el.find('input[name="roomSize"]').select2({placeholder:"Choose Room Size",data:o,id:function(e){return e.roomSizeId},formatResult:function(e){return e.roomSizeName},formatSelection:function(e){return e.roomSizeName}}).select2("val",r.roomSize.roomSizeId).select2("enable",!0),s.model.set("roomSize",r.roomSize.roomSizeId)):(s.$el.find(".js-room-size-wrap").hide(),s.model.set("roomSize",null))),s.$el.find('input[name="servicesList"]').select2("val","").select2({data:a,id:function(e){return e.serviceId},formatResult:function(e){return e.serviceName},formatSelection:function(e){return e.serviceName}}),s.$el.find(".js-service-list").empty(),s.model.set("services","");var p=[];f.forEach(function(t){var n=e("#tmpl_service_item").html();s.$el.find(".js-service-list").append(i.render(n,t)),p.push(t.serviceId)}),s.model.set("services",p.toString()),s.$el.find('input[name="activityList"]').select2("val","").select2({data:u,id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}),s.$el.find(".js-activity-list").empty(),s.model.set("activities","");var d=[];l.forEach(function(t){var n=e("#tmpl_activity_item").html();s.$el.find(".js-activity-list").append(i.render(n,t)),d.push(t.activityId)}),s.model.set("activities",d.toString()),r.enabled==="Y"?s.$el.find('input[name="enabled"]').iCheck("uncheck"):s.$el.find('input[name="enabled"]').iCheck("check")}})},changeActive:function(e){var n=this;t.is_changed=!0,is_changed=t.is_changed,e.currentTarget.checked?n.model.set("enabled",null):n.model.set("enabled","on")},changeSplit:function(e){t.is_changed=!0,is_changed=t.is_changed;var n=this;e.currentTarget.checked?n.model.set("isSplitRoom","on"):n.model.unset("isSplitRoom")},changeService:function(){var t=this;t.$el.find('input[name="servicesList"]').select2("readonly",!0),e.ajax({url:n.url_fetch_unselected_service,data:{roomTemplateId:t.$el.find('input[name="roomTemplate"]').select2("val"),serviceString:t.model.get("services")},success:function(e){t.$el.find('input[name="servicesList"]').select2("val","").select2({data:e.serviceList,id:function(e){return e.serviceId},formatResult:function(e){return e.serviceName},formatSelection:function(e){return e.serviceName}}).select2("readonly",!1),t.changeActivity()}})},changeActivity:function(){var t=this;t.$el.find('input[name="activityList"]').select2("readonly",!0),e.ajax({url:n.url_fetch_unselected_activity,data:{roomTemplateId:t.$el.find('input[name="roomTemplate"]').select2("val"),activityString:t.model.get("activities"),serviceString:t.model.get("services")},success:function(n){t.$el.find('input[name="activityList"]').select2("val","").select2({data:n.activityList,id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}).select2("readonly",!1);var r=t.$el.find(".js-activity-list"),s=e("#tmpl_activity_item").html(),o=0,u=[],a="";n.selectedActivityList.forEach(function(e){a+=i.render(s,e),u.push(e.activityId),o+=16}),r.css({height:o}).empty(),t.model.unset("activities"),r.html(a),t.model.set("activities",u.toString())}})},addService:function(){var n=this.$el.find('input[name="servicesList"]').select2("data"),r=this.model.get("services").split(","),s=e("#tmpl_service_item").html();if(n==null||n.length==0)return;this.model.get("serviceList").push(n),r.push(n.serviceId),r[0]==""&&r.shift(),this.model.set("services",r.toString()),this.changeService(),this.$el.find(".js-service-list").append(i.render(s,n)),t.is_changed=!0,is_changed=t.is_changed},removeService:function(n){var r=e(n.currentTarget).data("id"),i=this.model.get("services").split(","),o=s.filter(this.model.get("serviceList"),function(e){return e.serviceId!=r});this.model.set("serviceList",o);var u=s.filter(i,function(e){return e!=r});this.model.set("services",u.toString()),this.changeService(),e(n.currentTarget).parent().fadeOut(function(){e(this).remove()}),t.is_changed=!0,is_changed=t.is_changed},addActivity:function(){var n=this.$el.find('input[name="activityList"]').select2("data"),r=this.model.get("activities").split(","),s=e("#tmpl_activity_item").html();if(n==null||n.length==0)return;this.model.get("activityList").push(n),r.push(n.activityId),r[0]==""&&r.shift(),this.model.set("activities",r.toString()),this.changeActivity(),this.$el.find(".js-activity-list").html(i.render(s,n)),t.is_changed=!0,is_changed=t.is_changed},removeActivity:function(n){var r=e(n.currentTarget).data("id"),i=this.model.get("activities").split(","),o=s.filter(this.model.get("activityList"),function(e){return e.activityId!=r});this.model.set("activityList",o);var u=s.filter(i,function(e){return e!=r});this.model.set("activities",u.toString()),this.changeActivity(),e(n.currentTarget).parent().fadeOut(function(){e(this).remove()}),t.is_changed=!0,is_changed=t.is_changed},update:function(){var e=this;this.$el.find(".js-update-room").attr("disabled",!0),this.model.sync("update",this.model,{success:function(n){n.status?(e.model.set(n.dto),t.getAll(),e.close()):u.autoHideError(n.message),e.$el.find(".js-update-room").attr("disabled",!1)}})},"delete":function(){var i=this;r.dialog({title:"Warning!",message:"Are you sure to delete this room ?",buttons:{success:{label:"Delete",className:"btn btn-important",callback:function(){i.model.destroy({success:function(r,s){e.ajax({type:"GET",url:n.url_delete_room,data:{roomId:i.model.get("roomId")},success:function(e){e.status?(t.getAll(),t.is_changed=!1,is_changed=t.is_changed):u.autoHideError(e.message)}})}}),t.hideWarn()}},cancle:{label:"Cancel",className:"btn btn-primary",callback:function(){return}}}})},close:function(){return this.$el.find(".js-edit-room").removeClass("editing"),t.is_changed=!1,is_changed=t.is_changed,t.hideWarn(),this.$el.find("tr").eq(0).removeClass("disabled").nextAll().hide(),this.$el.find('input[name="roomTemplate"]').select2("destroy").off("select2-selecting"),this.$el.find('input[name="roomNumber"]').select2("destroy").off("select2-selecting"),this.$el.find('input[name="roomSize"]').select2("destroy").off("select2-selecting"),this.$el.find('input[name="isSplitRoom"]').iCheck("destroy"),this.$el.find('input[name="servicesList"]').select2("destroy").off("select2-selecting"),this.$el.find('input[name="activityList"]').select2("destroy").off("select2-selecting"),this.$el.find('input[name="enabled"]').iCheck("destroy"),this.$el.find('input[name="profileRoomName"]').val("").off("change"),this}}),l=o.Collection.extend({model:a,methodUrl:{read:n.url_fetch_room,create:"",update:"","delete":""},sync:function(e,t,n){t.methodUrl&&t.methodUrl[e.toLowerCase()]&&(n=n||{},n.url=t.methodUrl[e.toLowerCase()]),o.sync(e,t,n)}});var c=new l;t.Model=a,t.View=f,t.Collection=l,t.room_collection=c,s.bindAll(t,"addAll","addOne"),c.on("set",t.addAll),c.on("add",t.addOne),t.getAll()},getAll:function(){var e=this,t=e.conf;t.EL.empty(),e.room_collection.reset(),e.room_collection.fetch()},addAll:function(){var e=this;e.room_collection.each(e.addOne)},addOne:function(e){var t=this,n=t.conf.EL,r=new t.View({model:e});n.append(r.render().el)},showWarn:function(){var t=this.conf.EL,n=t.find("tr.disabled").nextAll().find(".js-update-room"),r=220,i=n.offset().top-n.height()-e(".warn-tip").height();e(".warn-tip").css({right:r,top:i}).show()},hideWarn:function(){e(".warn-tip").hide()},showLoadTip:function(){var e=this,t=e.conf.EL;t.append('<div class="loader"><p></p></div>')},createRoom:function(e){var t=this,n=this.conf.formTarget.serializeArray(),r=new t.Model;s.each(n,function(e,t){r.set(e.name,e.value)}),r.unset("servicesList"),r.unset("activityList"),r.sync("create",r,{success:function(e){e.status?(t.hideForm(),t.getAll(),t.is_changed=!1,is_changed=t.is_changed):t.conf.formTarget.find(".form-msg").text(e.message).show()}})}},a}),define("app/modules/bookedAppointments",["jquery","moment","select2","pikaday","icheck","mustache","underscore","backbone","edithour_modal","timeoff_modal","notification"],function(e,t,n,r,i,s,o,u,a,f,l){function c(t){this.defaults={url_fetch_collection:"bookedAppointments/loadBookedAppointments.htm",url_resubmit_booked_appointment:"bookedAppointments/reSubmitBookedAppointments.htm",EL:e(".booked-Appointments-list"),formTarget:e("#booked_appointment_form")},this.is_changed=is_changed,this.init(t)}return c.prototype={init:function(t){this.conf=e.extend(this.defaults,t),this.bookedAppointmentsForm();var n=this;n.edithour_modal=new a({EL:{$modal:e("#Xmodal_set_availability"),$mask:e(".mask")}}),n.timeoff_modal=new f({EL:{$modal:e("#Xmodal_set_timeoff"),$mask:e(".mask")}}),e(document).on("click",".btn-update-availability",function(e){n.edithour_modal.update(e)}),e(document).on("click",".js-close-availability-modal",function(){n.edithour_modal.hideModal()}),e(document).on("click",".btn-update-timeoff",function(e){n.timeoff_modal.update(e)}),e(document).on("click",".js-close-timeoff-modal",function(e){n.timeoff_modal.hideModal()});var r=n.conf,i,c,h;i=u.Model.extend({methodUrl:{read:r.url_fetch_model,create:"",update:"","delete":""},sync:function(e,t,n){t.methodUrl&&t.methodUrl[e.toLowerCase()]&&(n=n||{},n.url=t.methodUrl[e.toLowerCase()]),u.sync(e,t,n)},initialize:function(){}}),c=u.View.extend({initialize:function(){var e=this;return this},events:{"click .booked-appointment-resubmit":"resubmit","click .js-edit-booked-appointments":"edit","click .close-edit-panel":"close","click .js-update-booked-appointments":"update","click .js-booked-set-availability":"setBookedAvailability","click .js-booked-set-timeoff":"setBookedTimeoff",'ifChanged input[name="active"]':"changeActive"},tagName:"tr",className:"",template:e("#tmpl_booked_appointments").html(),render:function(){return this.$el.html(s.render(this.template,this.model.toJSON())),this},resubmit:function(t){var n=this,i=e(t.currentTarget);console.log(JSON.stringify(n.model.attributes)),e.ajax({url:r.url_resubmit_booked_appointment,type:"POST",data:{params:JSON.stringify(n.model.attributes)},success:function(e){console.log(e)},error:function(e){l.autoHideError(e)}})},editRender:function(){return this},close:function(){return this.$el.find(".js-edit-booked-appointments").removeClass("editing"),n.hideWarn(),this.$el.find("tr").eq(0).removeClass("disabled").nextAll().hide(),this.$el.find('input[name="active"]').iCheck("destroy"),n.is_changed=!1,is_changed=n.is_changed,this},update:function(){var e=this;this.$el.find(".edit-booked-appointments").removeClass("editing"),this.$el.find(".js-update-booked-appointments").attr("disabled",!0),n.hideWarn(),this.model.sync("create",this.model,{success:function(t){t.status?(e.model.set(t.object),e.render(),e.close()):l.autoHideError(t.message),e.$el.find(".js-update-booked-appointments").attr("disabled",!1)}})},setBookedAvailability:function(){n.edithour_modal.showModal(e.extend(!0,{},this.model))},setBookedTimeoff:function(){n.timeoff_modal.showModal(e.extend(!0,{},this.model))},changeActive:function(e){n.is_changed=!0,is_changed=n.is_changed,e.currentTarget.checked?this.model.set("active",!1):this.model.set("active",!0)},remove:function(){}}),h=u.Collection.extend({model:i,methodUrl:{read:r.url_fetch_collection,create:"",update:"","delete":""},sync:function(e,t,n){t.methodUrl&&t.methodUrl[e.toLowerCase()]&&(n=n||{},n.url=t.methodUrl[e.toLowerCase()]),u.sync(e,t,n)}});var p=new h;return n.Model=i,n.View=c,n.Collection=h,n.bookedAppointments_collection=p,o.bindAll(n,"addAll","addOne"),p.on("set",n.addAll),p.on("add",n.addOne),n},bookedAppointmentsForm:function(){var e=this,t=e.conf,n;return n=u.Model.extend({formEL:{startDate:t.formTarget.find('input[name="startDate"]'),endDate:t.formTarget.find('input[name="endDate"]')},getParams:function(){var e=this,t=e.formEL;return{startDate:t.startDate.val(),endDate:t.endDate.val()}},clear:function(){var e=this,t=e.formEL;t.startDate.val(""),t.endDate.val("")},initialize:function(){var e=this,t,n,i=e.formEL;t=new r({format:"MM/DD/YYYY",field:i.startDate[0]}),n=new r({format:"MM/DD/YYYY",field:i.endDate[0]})}}),e.bookedAppointmentQuery=new n,e},renderList:function(){var e=this;return e.showLoadTip(),e.is_changed=!1,is_changed=e.is_changed,e.getAll(),e},getAll:function(){var t=this,n=t.conf;n.EL.empty(),t.bookedAppointments_collection.reset();var r=this.bookedAppointmentQuery.getParams(),i=/^\d{1,2}\/\d{1,2}\/\d{4}$/;if(r.startDate.search(i)==-1||r.endDate.search(i)==-1)t.initDate(),r=t.bookedAppointmentQuery.getParams();var s={startDatetime:r.startDate,endDatetime:r.endDate};e.ajax({url:n.url_fetch_collection,type:"GET",data:{params:JSON.stringify(s)},success:function(e){t.bookedAppointments_collection.reset(),t.bookedAppointments_collection.set(e.appointmentBookDTO)}})},initDate:function(){e('input[name="startDate"]').val(t(new Date).format("L")),e('input[name="endDate"]').val(t(new Date).format("L"))},addAll:function(){var e=this;e.bookedAppointments_collection.each(e.addOne)},addOne:function(e){var t=this,n=t.conf.EL,r=new t.View({model:e});n.append(r.render().el)},showWarn:function(){var t=e(".booked-appointments-list tr.disabled").nextAll().find(".js-update-booked-appointments"),n=220,r=t.offset().top-t.height()-e(".warn-tip").height();e(".warn-tip").css({right:n,top:r}).show()},hideWarn:function(){e(".warn-tip").hide()},showLoadTip:function(){var e=this,t=e.conf.EL;t.append('<div class="loader"><p></p></div>')}},c}),define("pagination",["jquery","underscore","backbone","mustache"],function(e,t,n,r){function i(e){this.init(e)}return i.prototype={init:function(t){var i=this;i.pageQuery=t.pageQuery,i.pageInfo=t.pageInfo;var s=n.View.extend({initialize:function(){return this},template:e("#tmpl_pagination").html(),events:{"click .pre-page":"prePage","click .next-page":"nextPage"},render:function(){return this.$el.html(r.render(this.template,this.model.toJSON())),this},prePage:function(e){i.pageQuery({targetPage:i.model.get("prePage"),pageSize:i.model.get("pageSize")})},nextPage:function(e){i.pageQuery({targetPage:i.model.get("nextPage"),pageSize:i.model.get("pageSize")})}});i.model=new n.Model(i.pageInfo),i.View=s},pageHtml:function(){var e=new this.View({model:this.model});return console.log(e),e.render().el},pageModel:function(){return this.model}},i}),define("customer_detail",["jquery","moment","select2","icheck","pikaday","mustache","underscore","backbone","notification","pagination","instructorschedule"],function(e,t,n,r,i,s,o,u,a,f){function l(t){this.defaults={url_fetch_customer_info:"customer/info.htm",url_fetch_customer_appointments:"customer/appointments.htm",url_send_reminder:"customer/sendReminder.htm",url_update_model:"updateInstructorScheduleByStaff.htm",EL:{customer_info:e(".js-customer-detail .customer-detail-info"),customer_appointments:e(".js-customer-detail .customer-appointments-table")},formTarget:e("#customer_appointments_form")},this.is_changed=is_changed,this.init(t)}return l.prototype={init:function(t){this.conf=e.extend(this.defaults,t),this.initCustomerAppointmentsForm();var n=this,r=n.conf,i,o,a,f=n.appointFormInit,l=!0;i=u.Model.extend();var c=new i,h=new u.Collection;return c.on("change",n.customerInfoRefresh,n),h.on("add",n.customerAppointmentsRefresh,n),n.CustomerDetail_View=u.View.extend({tagName:"div",className:"grid-center",template:e("#tmpl_customer_detail_info").html(),render:function(){return this.$el.html(s.render(this.template,this.model.toJSON())),this}}),n.CustomerAppointments_View=u.View.extend({events:{"click .email-header a":"sendReminder","click .show-remarks":"showRemarks","click .show-notes":"showNotes","click .submit-remarks":"saveRemarks","click .close":"teardown","click .status-modal":"statusModal","click .lesson-completed":"completeLessonStatus","click .undo-status":"undoLessonStatus"},tagName:"tbody",className:"customer-appointment-list",template:e("#tmpl_customer_appointments").html(),render:function(){return this.model.set({cid:this.model.cid}),this.$el.append(s.render(this.template,this.model.toJSON())),this},showRemarks:function(t){t.preventDefault();var n=this,r=e(n.el),i=e(t.currentTarget);!i.attr("data-clicked")||i.attr("data-clicked")=="false"?(r.find(".remarks-hidden").show(),i.text("Hide Remarks").attr("data-clicked","true")):(r.find(".remarks-hidden").hide(),i.text("Show Remarks").attr("data-clicked","false"))},showNotes:function(t){t.preventDefault();var n=this,r=e(n.el),i=e(t.currentTarget);!i.attr("data-clicked")||i.attr("data-clicked")=="false"?(i.text("Hide Notes").attr("data-clicked","true"),r.find(".notes-hidden").show()):(r.find(".notes-hidden").hide(),i.text("Show Notes").attr("data-clicked","false"))},statusModal:function(e){e.preventDefault();var t=new a({model:this.model});t.show()},completeLessonStatus:function(t){t.preventDefault();var i=this,s=i.model,o=e(t.currentTarget),u=o.closest("span"),a=s.get("appointmentId"),f=s.get("instructorId"),l=e('.completed-header[data-id-status="'+a+'"]'),c="Completed",h=o.attr("cid"),p=n.customer_appointments_collection.get(h);this.model.set({showStatus:c});var d={showStatus:c,appointmentId:a};e.ajax({url:r.url_update_model,type:"POST",data:{params:JSON.stringify(d)},success:function(e,t){u.find(".lesson-status-msg").text("Completed").show(),u.find(".btn-default").hide(),u.find(".undo-wrapper").show(),console.log("saved status and send ajax request")},error:function(e,t){console.log("error")}})},undoLessonStatus:function(t){t.preventDefault();var i=this,s=i.model,o=e(t.currentTarget),u=s.get("appointmentId"),a=e('.completed-header[data-id-status="'+u+'"]'),f=o.closest("span"),l=o.attr("cid"),c=n.customer_appointments_collection.get(l);this.model.set({showStatus:""});var h={showStatus:"",appointmentId:u};e.ajax({url:r.url_update_model,type:"POST",data:{params:JSON.stringify(h)},success:function(e,t){f.find(".undo-wrapper, .lesson-status-msg").hide(),f.find(".btn-default").show(),console.log("saved status and send ajax request")},error:function(e,t){console.log("error")}})},sendReminder:function(t){t.preventDefault();var i=e(t.currentTarget),s=this,u=s.model,a=i.attr("cid"),f=n.customer_appointments_collection.get(a);console.log(f),f.unset("cid"),i.closest(".reminder-link-container").addClass("loading-spinner"),e.ajax({url:r.url_send_reminder,type:"POST",data:{params:JSON.stringify(f.attributes)},success:function(e){typeof e=="string"&&e.indexOf("DOCTYPE")!==-1&&location.reload();if(e==0)var t=new o({model:u}),n="Failed to Send Reminder, please try again.";else if(e==1)var t=new o({model:u}),n="Reminder Sent Successfully!";t.show(n),i.closest(".reminder-link-container").removeClass("loading-spinner")}})}}),a=u.View.extend({id:"modal_status_instructor",className:"modal fade hide instructor-schedule-modal",template:e("#tmpl_customer_status_modal").html(),events:{hidden:"teardown","click .submit-status":"saveStatus","click .close":"close"},initialize:function(e){var t=this,e=e;return t.render(),this},show:function(){var e=this,t=e.model;elModal=this.$el.modal(),pastStatus=t.get("showStatus"),this.$el.attr("data-backdrop","static"),this.$el.modal("show"),pastStatus!=null&&pastStatus!=""&&elModal.find("#js-lesson-status").val(pastStatus)},teardown:function(){this.$el.data("modal",null),this.$el.modal.remove()},render:function(){return this.$el.html(s.render(this.template)),this},renderView:function(e){this.$el.html(e()),this.$el.modal({show:!1})},saveStatus:function(t){var n=this,i=n.model;elModal=this.$el.modal(),appointmentId=i.get("appointmentId"),appointmentStatus=e("#js-lesson-status option:selected").text(),statusTableDataEl=e('.completed-header[data-id-status="'+appointmentId+'"]'),this.model.set({showStatus:appointmentStatus});var s={showStatus:appointmentStatus,appointmentId:appointmentId};e.ajax({url:r.url_update_model,type:"POST",data:{params:JSON.stringify(s)},success:function(t,n){console.log("Status submitted successfullly"),elModal.find(".modal-body").html('<p class="submit-success-msg">Status Submitted Successfully!</p>'),e(".modal-footer").hide(),statusTableDataEl.find(".lesson-status-msg").text(appointmentStatus).show(),statusTableDataEl.find(".btn-default").hide(),statusTableDataEl.find(".undo-wrapper").show()},error:function(e,t){console.log("error")}})}}),o=u.View.extend({id:"modal_reminder_instructor",className:"modal fade hide instructor-schedule-modal",template:e("#tmpl_customer_detail_reminder_modal").html(),events:{hidden:"teardown","click .submit-status":"saveReminderCount","click .close":"close"},initialize:function(e){var t=this,e=e;return t.render(),this},show:function(e){var t=this,n=t.model;elModal=this.$el.modal(),this.$el.attr("data-backdrop","static"),this.$el.modal("show"),this.$el.find(".email-success-msg").text(e)},teardown:function(){this.$el.data("modal",null),this.$el.modal.remove()},render:function(){return this.$el.html(s.render(this.template)),this},renderView:function(e){this.$el.html(e()),this.$el.modal({show:!1})},saveReminderCount:function(t){var n=this,r=n.model;elModal=this.$el.modal(),appointmentId=r.get("appointmentId"),reminderTableDataEl=e('.email-header[data-id-email-reminder="'+appointmentId+'"]'),this.model.set({sentReminderCount:appointmentStatus}),this.model.save({},{success:function(t,n){console.log("Status submitted successfullly"),e(".modal-footer").hide()},error:function(e,t){console.log("error")}})}}),n.customer_detail_model=c,n.customer_appointments_collection=h,e(".js-customer-detail .js-query-customer-appointments").click(function(){n.getCustomerAppointmentsCollection()}),n},renderPage:function(t){if(t==null)return;var n=this,r=n.conf;return n.appointmentFormInit==0&&n.resetAppointmentDateRange(),n.customerId=t,n.showLoadTip(),n.is_changed=!1,is_changed=n.is_changed,appointmentHeader=e(".appointment-header"),n.emptyAllEL(),r.EL.customer_appointments.append(appointmentHeader),n.getCustomerDetailInfo({customerId:n.customerId}),n.getCustomerAppointmentsCollection(),n},getCustomerDetailInfo:function(t){var n=this,r=n.conf;e.ajax({url:r.url_fetch_customer_info,type:"GET",data:t,success:function(e){console.log(e),n.customer_detail_model.clear(),n.customer_detail_model.set(e)}})},getCustomerAppointmentsCollection:function(){var t=this,n=t.conf,r=n.EL.customer_appointments,i=this.customer_appointments_date_query.getParams(),s={startDatetime:i.startDate,endDatetime:i.endDate,customerId:t.customerId};e.ajax({url:n.url_fetch_customer_appointments,type:"GET",data:{params:JSON.stringify(s)},success:function(e){r.find("tbody.customer-appointment-list").remove(),t.customer_appointments_collection.reset(),t.customer_appointments_collection.set(e)}})},customerInfoRefresh:function(e){var t=this,n=t.conf.EL.customer_info,r=new t.CustomerDetail_View({model:e});n.html(r.render().el)},customerAppointmentsRefresh:function(e){console.log(e);var t=this,n=t.conf.EL.customer_appointments,r=new t.CustomerAppointments_View({model:e});appointmentHeader=n.find(".appointment-header"),n.append(r.render().el)},emptyAllEL:function(){for(var e in this.conf.EL)this.conf.EL[e].empty()},hideWarn:function(){e(".warn-tip").hide()},showLoadTip:function(){var e=this;for(var t in e.conf.EL)e.conf.EL[t].append('<div class="loader"><p></p></div>')},resetAppointmentDateRange:function(){var e=this,n=e.conf,r=n.formTarget,i=r.find('input[name="startDate"]'),s=r.find('input[name="endDate"]'),o=new Date((new Date).getTime()-2592e6);i.val(t(o).format("MM/DD/YYYY")),s.val(t().format("MM/DD/YYYY"))},initCustomerAppointmentsForm:function(){var e=this,n=e.conf,r;return r=u.Model.extend({formEL:{startDate:n.formTarget.find('input[name="startDate"]'),endDate:n.formTarget.find('input[name="endDate"]')},getParams:function(){var e=this,t=e.formEL;return{startDate:t.startDate.val(),endDate:t.endDate.val()}},clear:function(){var e=this,t=e.formEL;t.startDate.val(""),t.endDate.val("")},initialize:function(){var e=this,n,r,s=e.formEL;n=new i({format:"MM/DD/YYYY",field:s.startDate[0]}),r=new i({format:"MM/DD/YYYY",field:s.endDate[0]});var o=new Date((new Date).getTime()-2592e6);s.startDate.val(t(o).format("MM/DD/YYYY")),s.endDate.val(t().format("MM/DD/YYYY"))}}),e.customer_appointments_date_query=new r,e.appointmentFormInit=!1,e}},l}),define("report",["jquery","moment","select2","pikaday","underscore","backbone","mustache","notification"],function(e,t,n,r,i,s,o,u){function a(t){this.defaults={url_masterScheduleReport:"report/masterScheduleReport.htm",url_rehearsalBookingReport:"report/rehearsalBookingReport.htm",url_rehearsalScheduleReport:"report/rehearsalScheduleReport.htm",url_instructorScheduleReport:"report/instructorScheduleReport.htm",url_instructorStatusScheduleReport:"report/instructorAppointmentStatusScheduleReport.htm",url_cancelledAppointments:"report/cancelledAppointments.htm",url_instructorOpenAppointmentsReport:"report/instructorOpenAppointmentsReport.htm",url_conflictingAppointmentsReportByInsrtuctor:"report/conflictingAppointmentsReportByInsrtuctor.htm",url_conflictingAppointmentsReportByRoom:"report/conflictingAppointmentsReportByRoom.htm",url_cancel_conflict_appointment:"report/cancelConflictAppointment.htm",url_instructorOutsideAppointmentsReport:"report/instructorOutsideAppointmentsReport.htm",url_ActiveStudentsReport:"report/activeStudentsReport.htm",url_StudentsCheckInReport:"report/StudentCheckInReport.htm",url_InActiveStudentsReport:"report/inActiveStudents.htm",url_ProfileDetailsReport:"excelReport",url_print_masterScheduleReport:"printMasterScheduleReport.htm",url_print_rehearsalBookingReport:"printRehearsalBookingReport.htm",url_print_rehearsalScheduleReport:"printRehearsalScheduleReport.htm",url_print_instructorScheduleReport:"printInstructorScheduleReport.htm",url_print_instructorStatusScheduleReport:"printInstructorStatusScheduleReport.htm",url_print_cancelledAppointments:"printCacnelledAppointments.htm",url_print_activeStudentsReport:"printActiveStudentsReport.htm",url_print_StudentsCheckInReport:"printStudentCheckInReport.htm",url_print_inActiveStudentsReport:"printInActiveStudentsReport.htm",url_print_instructorOpenAppointmentsReport:"printInstructorOpenAppointmentsReport.htm",url_print_conflictingAppointmentsReportByInsrtuctor:"printConflictAppointmentsReportByInsrtuctor.htm",url_print_conflictingAppointmentsReportByRoom:"printConflictAppointmentsReportByRoom.htm",url_print_instructorOutsideAppointmentsReport:"printInstructorOutsideAppointmentsReport.htm",url_fetch_activitylist:"report/loadActivityList.htm",url_generate_csv_for_lesson:"generateLessonCSV.htm",url_generate_excel_masterScheduleReport:"excel/MasterScheduleReport",url_generate_excel_cancelledAppointments:"excel/cancelledAppointments",url_generate_excel_instructorScheduleReport:"excel/instructorScheduleReport",url_generate_excel_instructorScheduleStatusReport:"excel/instructorAppointmentStatusScheduleReport",url_generate_excel_instructorOpenAppointmentsReport:"excel/instructorOpenAppointmentsReport",url_generate_excel_conflictingAppointmentsReportByRoom:"excel/conflictingAppointmentsReportByRoom",url_generate_excel_conflictingAppointmentsReportByInsrtuctor:"excel/conflictingAppointmentsReportByInsrtuctor",url_generate_excel_instructorOutsideAppointmentsReport:"excel/instructorOutsideAppointmentsReport",url_generate_excel_rehearsalBookingReport:"excel/rehearsalBookingReport",url_generate_excel_rehearsalScheduleReport:"excel/rehearsalScheduleReport",url_generate_excel_activeStudentsReport:"excel/activeStudentsReport",url_generate_excel_studentCheckInReport:"excel/studentCheckInReport",url_generate_excel_inActiveStudentsReport:"excel/inActveStudentsReport",url_generate_excel_profileDetails:"excel/excelProfileReport",url_generate_excel_appointmentHistory:"excel/appointmentHistoryReport",formTarget:e("#report_form"),formTarget2:e("#report_form2"),EL:e(".report-list"),$printButton:e(".js-print"),$title:e(".report-title"),$generateExcel:e(".js-excel")},this.init(t)}return a.prototype={init:function(t){this.conf=e.extend(this.defaults,t),this.reportForm();var n=this,r=n.conf,i=r.EL},initDate:function(){e('input[name="startDate"]').val(t(new Date).format("L")),e('input[name="endDate"]').val(t(new Date).format("L")),e('input[name="inputExternalId"]').val(""),e('input[name="instructorName"]').val(""),e('input[name="activityType"]').val(""),e('input[name="externalId"]').val(""),e('input[name="externalId"]').keypress(function(e){if(e.which!=8&&e.which!=0&&(e.which<48||e.which>57))return!1}),e('input[name="inputExternalId"]').keypress(function(e){if(e.which!=8&&e.which!=0&&(e.which<48||e.which>57))return!1})},reportForm:function(){var n=this,i=n.conf,o;return o=s.Model.extend({initialize:function(){var s=this,o,u,a,f,l,c,h,p,d,v,m,g,y=s.formEL;y.reportType.select2().select2("val","").on("change",function(t){y.inputExternalId.val(""),y.instructorName.val(""),y.reportSubType.select2("val",""),y.activityType.el.select2("val",""),y.dayType.select2("val",""),y.tFrom.select2("val",""),y.tTo.select2("val",""),y.externalId.val(""),y.startDate.on(function(){n.cleanReport(),s.changePrintUrl(),s.changeExcelUrl()}),y.endDate.on(function(){n.cleanReport(),s.changePrintUrl(),s.changeExcelUrl()}),n.cleanReport(),type=y.reportType.val(),s.changePrintUrl(),s.changeExcelUrl(),type==10&&e.ajax({url:i.url_fetch_activitylist,type:"GET",success:function(e){y.activityType.el.select2("val","").select2({data:e,id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}).select2("readonly",!1),y.activityType.el.show()}})}),y.reportSubType.select2().select2("val","").on("change",function(e){n.cleanReport(),s.changePrintUrl(),s.changeExcelUrl()}),y.dayType.select2().select2("val","").on("change",function(e){n.cleanReport(),s.changePrintUrl(),s.changeExcelUrl()}),y.startDate.on("change",function(){n.cleanReport(),s.changePrintUrl(),s.changeExcelUrl()}),y.endDate.on("change",function(){n.cleanReport(),s.changePrintUrl(),s.changeExcelUrl()}),y.activityType.el.select2("val","").on("change",function(e){n.cleanReport(),s.changePrintUrl(),s.changeExcelUrl()}),y.instructorName.on("input",function(){n.cleanReport(),s.changePrintUrl(),s.changeExcelUrl()}),o=new r({format:"MM/DD/YYYY",field:y.startDate[0]}),u=new r({format:"MM/DD/YYYY",field:y.endDate[0]}),y.tFrom.select2().select2("val","").on("change",function(e){n.cleanReport(),s.changePrintUrl(),s.changeExcelUrl()}),y.tTo.select2().select2("val","").on("change",function(e){n.cleanReport(),s.changePrintUrl(),s.changeExcelUrl()}),y.inputExternalId.on("input",function(){n.cleanReport(),s.changePrintUrl(),s.changeExcelUrl()}),y.instructorName.on("input",function(){n.cleanReport(),s.changePrintUrl(),s.changeExcelUrl()}),y.externalId.on("input",function(){n.cleanReport(),s.changePrintUrl(),s.changeExcelUrl()}),e(".js-report-date").text(t().format("dddd, MMMM D, YYYY")),e(".js-report-time").text(t().format("hh:mm:ss A"))},changePrintUrl:function(){var e=this,t=e.formEL,n=t.reportType.val(),r=t.reportSubType.val(),s=t.dayType.val(),o=t.tFrom.val(),u=t.tTo.val(),a=t.startDate.val(),f=t.endDate.val(),l=t.activityType.el.val(),c=t.externalId.val(),h=t.inputExternalId.val(),p=t.instructorName.val(),d="&externalId="+c+"?startDate="+a+"&endDate="+f,v="?startDate="+a+"&endDate="+f,m="?startDate="+a+"&endDate="+f,g="?startDate="+a+"&endDate="+f+"&instructorName="+p+"&activityType="+l,y="?startDate="+a+"&endDate="+f+"&inputExternalId="+h+"&instructorName="+p,b="?tFrom="+o+"&tTo="+u+"&inputExternalId="+h+"&dayType="+s,w=i.$printButton,E;switch(n){case"1":E=i.url_print_masterScheduleReport+v;break;case"2":E=i.url_print_rehearsalBookingReport+v;break;case"3":E=i.url_print_rehearsalScheduleReport+v;break;case"4":E=i.url_print_instructorScheduleReport+v;break;case"5":E=i.url_print_cancelledAppointments+v;break;case"6":E=i.url_print_instructorOpenAppointmentsReport+y;break;case"7":E=i.url_generate_csv_for_lesson+v;break;case"8":r==1?E=i.url_print_conflictingAppointmentsReportByInsrtuctor+v:E=i.url_print_conflictingAppointmentsReportByRoom+v;break;case"9":E=i.url_print_instructorOutsideAppointmentsReport+b;break;case"10":E=i.url_print_activeStudentsReport+g;break;case"11":E=i.url_print_StudentsCheckInReport+m;break;case"12":E=i.url_print_inActiveStudentsReport;break;case"13":E=i.url_generate_excel_profileDetails+v;break;case"14":E=i.url_generate_excel_appointmentHistory+d;break;case"15":E=i.url_print_instructorStatusScheduleReport+v}w.attr("href",E)},changeExcelUrl:function(){var e=this,t=e.formEL,n=t.reportType.val(),r=t.reportSubType.val(),s=t.dayType.val(),o=t.activityType.el.val(),u=t.tFrom.val(),a=t.tTo.val(),f=t.startDate.val(),l=t.endDate.val(),c=t.externalId.val(),h=t.inputExternalId.val(),p=t.instructorName.val(),d="?startDate="+f+"&endDate="+l,v="?startDate="+f+"&endDate="+l+"&activityType="+o+"&instructorName="+p,m="?startDate="+f+"&endDate="+l+"&inputExternalId="+h+"&instructorName="+p,g="?tFrom="+u+"&tTo="+a+"&inputExternalId="+h+"&dayType="+s,y="&externalId="+c+"?startDate="+f+"&endDate="+l,b=i.$generateExcel,w;switch(n){case"1":w=i.url_generate_excel_masterScheduleReport+d;break;case"2":w=i.url_generate_excel_rehearsalBookingReport+d;break;case"3":w=i.url_generate_excel_rehearsalScheduleReport+d;break;case"4":w=i.url_generate_excel_instructorScheduleReport+d;break;case"5":w=i.url_generate_excel_cancelledAppointments+d;break;case"6":w=i.url_generate_excel_instructorOpenAppointmentsReport+m;break;case"7":w=i.url_generate_csv_for_lesson+d;break;case"8":r==1?w=i.url_generate_excel_conflictingAppointmentsReportByInsrtuctor+d:w=i.url_generate_excel_conflictingAppointmentsReportByRoom+d;break;case"9":w=i.url_generate_excel_instructorOutsideAppointmentsReport+g;break;case"10":w=i.url_generate_excel_activeStudentsReport+v;break;case"11":w=i.url_generate_excel_studentCheckInReport+d;break;case"12":w=i.url_generate_excel_inActiveStudentsReport;break;case"13":w=i.url_generate_excel_profileDetails+d;break;case"14":w=i.url_generate_excel_appointmentHistory+y;break;case"15":w=i.url_generate_excel_instructorScheduleStatusReport+d}b.attr("href",w)},clear:function(){var e=this,t=e.formEL;t.reportType.select2("val",""),t.reportSubType.select2("val",""),t.dayType.select2("val",""),t.tFrom.select2("val",""),t.tTo.select2("val",""),i.$title.text("Report"),t.startDate.val(""),t.endDate.val(""),t.activityType.el.select2("val","")},getParams:function(){var e=this,t=e.formEL,n,r,s=t.externalId.val(),o=t.inputExternalId.val(),u=t.instructorName.val(),a=t.reportType.select2("val"),f=t.reportSubType.select2("val"),l=t.dayType.select2("val");return tFrom=t.tFrom.select2("val"),tTo=t.tTo.select2("val"),activityType=t.activityType.el.val(),a=t.reportType.select2("val"),a==1?(n=i.url_masterScheduleReport,r="Daily Master Reporting"):a==2?(n=i.url_rehearsalBookingReport,r="Scheduled Rehearsal Reporting"):a==3?(n=i.url_rehearsalScheduleReport,r="Daily Rehearsal Reporting"):a==4?(n=i.url_instructorScheduleReport,r="Daily Studio Reporting"):a==5?(n=i.url_cancelledAppointments,r="Cancelled Appointments Reporting"):a==6?(n=i.url_instructorOpenAppointmentsReport,r="Instructor Open Appointments Reporting"):a==8?f==1?(n=i.url_conflictingAppointmentsReportByInsrtuctor,r="Conflicting Appointments By Instructor Reporting"):(n=i.url_conflictingAppointmentsReportByRoom,r="Conflicting Appointments By Room Reporting"):a==9?(n=i.url_instructorOutsideAppointmentsReport,r="Instructor Outside Appointments Reporting"):a==10?(n=i.url_ActiveStudentsReport,r="Active Students Reporting"):a==11?(n=i.url_StudentsCheckInReport,r="Student Check In Sheet Reporting"):a==12?(n=i.url_InActiveStudentsReport,r="InActive Students Reporting"):a==13?(n=i.url_generate_excel_profileDetails,r=""):a==14?(n=i.url_generate_excel_appointmentHistory,r="Appoinment History Reportings"):a==15?(n=i.url_instructorStatusScheduleReport,r="Instructor Schedule Status Reporting"):(n=i.url_generate_csv_for_lesson,r=""),{reportTitle:r,url:n,startDate:t.startDate.val(),endDate:t.endDate.val(),reportType:a,reportSubType:f,activityType:activityType,dayType:l,tFrom:tFrom,tTo:tTo,externalId:s,instructorName:u,inputExternalId:o}},formEL:{reportType:i.formTarget.find('select[name="reportType"]'),reportSubType:i.formTarget.find('select[name="reportSubType"]'),startDate:i.formTarget.find('input[name="startDate"]'),endDate:i.formTarget.find('input[name="endDate"]'),dayType:i.formTarget.find('select[name="dayType"]'),tFrom:i.formTarget.find('select[name="tFrom"]'),tTo:i.formTarget.find('select[name="tTo"]'),activityType:{el:i.formTarget.find('input[name="activityType"]'),setAll:function(e){return this.el.select2({placeholder:"Select Lesson Type",data:e,id:function(e){return e.activityId},formatResult:function(e){return e.activityName},formatSelection:function(e){return e.activityName}}),this},set:function(e){return typeof e=="object"&&this.el.select2("data",e).select2("readonly",!1),(typeof e=="string"||typeof e=="number")&&this.el.select2("val",e).select2("readonly",!1),this},clear:function(){return this.el.select2("val","").select2("readonly",!1),this},needValid:!0},externalId:i.formTarget.find('input[name="externalId"]'),instructorName:i.formTarget2.find('input[name="instructorName"]'),inputExternalId:i.formTarget.find('input[name="inputExternalId"]')}}),NewReport_View=s.View.extend({el:e(".report-list"),initialize:function(){return this},events:{"click .js-cancel-report":"cancel"},cancel:function(t){var n=e(t.currentTarget),r=n.data("appointmentid"),s=n.data("recurringstatus"),o='Would you like to cancel this appointment? <br><br> <input type="checkbox" name="notifyCustomer" id="notify">&nbsp;Send Email to Customer';"Y"==s&&(o='Would you like to cancel this occurrence, or all in series? <br><br> <input type="checkbox" name="notifyCustomer" id="notify">&nbsp;Send Email to Customer',e(".js-cancel-all").show()),bootbox.dialog({title:"Cancel Appointment",message:o,buttons:{cancelOne:{label:"Cancel this appointment",className:"btn btn-important",callback:function(){var t=!1;e("#notify").is(":checked")&&(t=!0),e.ajax({url:i.url_cancel_conflict_appointment,type:"POST",data:{appointmentId:r,cancelType:"single",enableMail:t},success:function(t){t.status?e(".js-create-report").click():u.autoHideError(t.message)}})}},cancelAll:{label:"Cancel all appointments",className:"btn btn-important js-cancel-all",callback:function(){var t=!1;e("#notify").is(":checked")&&(t=!0),e.ajax({url:i.url_cancel_conflict_appointment,type:"POST",data:{appointmentId:r,cancelType:"more",enableMail:t},success:function(t){t.status?e(".js-create-report").click():u.autoHideError(t.message)}})}},cancle:{label:"Cancel",className:"btn btn-primary",callback:function(){return}}}}),"Y"!==s&&e(".js-cancel-all").hide()},render:function(e){this.$el.html(e)}}),n.newreport=new o,n.View=new NewReport_View,n},renderList:function(){var n=this,r=n.conf,i=r.EL,s=r.$printButton,a=r.$generateExcel,f,l;if(!n.validateDate()){u.autoHideError("Date Range Invalid!");return}f=n.newreport.getParams();if(f.reportType==""){u.autoHideError("Select report type!");return}if(f.reportType==1)l=e("#tmpl_master_schedule_report").html();else if(f.reportType==2)l=e("#tmpl_rehearsal_booking_report").html();else if(f.reportType==3)l=e("#tmpl_rehearsal_schedule_report").html();else if(f.reportType==4)l=e("#tmpl_instructor_schedule_report").html();else if(f.reportType==15)l=e("#tmpl_instructor_status_schedule_report").html();else if(f.reportType==5)l=e("#tmpl_cancelled_appointment_report").html();else if(f.reportType==6){if(f.inputExternalId==""&&f.instructorName==""){u.autoHideError("Please Enter Instructor ID or Instructor Name!");return}l=e("#tmpl_instructor_open_appointments_report").html()}else if(f.reportType==8){if(f.reportSubType==""){u.autoHideError("Select report sub type!");return}if(!n.conflictValidateDate(365)){u.autoHideError("Date duration cannot exceed 365 days!");return}f.reportSubType==1?l=e("#tmpl_conflicting_appointments_report_by_instructor").html():l=e("#tmpl_conflicting_appointments_report_by_room").html()}else if(f.reportType==9){if(f.inputExternalId==""){u.autoHideError("Please Enter Instructor ID!");return}if(f.dayType==""){u.autoHideError("Select report Day!");return}if(f.tFrom==""){u.autoHideError("Select From Time!");return}if(f.tTo==""){u.autoHideError("Select To Time!");return}if(!n.outsideValidateDate()){u.autoHideError("Time Range Invalid!");return}l=e("#tmpl_instructor_outside_appointments_report").html()}else if(f.reportType==10){if(f.activityType==""&&f.instructorName==""){u.autoHideError("Please Select Lesson Type or Enter Instructor Name!");return}l=e("#tmpl_active_students_report").html()}else if(f.reportType==11){if(!n.conflictValidateDate(365)){u.autoHideError("Date duration cannot exceed 365 days!");return}l=e("#tmpl_student_check_in_report").html()}else f.reportType==12&&(l=e("#tmpl_inactive_students_report").html());if(f.reportType!=7&&f.reportType!=13&&f.reportType!=14)e("#rep-modal").show(),e("#rep-fade").show(),e.ajax({url:f.url,type:"POST",data:{startDate:f.startDate,endDate:f.endDate,dayType:f.dayType,tFrom:f.tFrom,tTo:f.tTo,activityType:f.activityType,externalId:f.externalId,instructorName:f.instructorName,inputExternalId:f.inputExternalId},success:function(i){e("#rep-modal").hide(),e("#rep-fade").hide(),e(".js-report-time").text(t().format("hh:mm:ss A")),s.show(),f.reportType!=1&&f.reportType!=7&&a.show(),r.$title.text(f.reportTitle);var u="";i.forEach(function(e){u+=o.render(l,e)}),n.View.render(u)}});else if(f.reportType==7){var c=e("#report_form");c.attr("action",r.url_generate_csv_for_lesson),c.submit()}else if(f.reportType==13){if(!n.conflictValidateDate(6)){u.autoHideError("Date duration cannot exceed 7 days!");return}var c=e("#report_form");c.attr("action",r.url_generate_excel_profileDetails),c.submit()}else if(f.reportType==14){if(f.externalId==""){u.autoHideError("Please Enter Customer External ID!");return}if(!n.conflictValidateDate(7)){u.autoHideError("Date duration cannot exceed 7 days!");return}var c=e("#report_form");c.attr("action",r.url_generate_excel_appointmentHistory),c.submit()}},validateDate:function(){var e=this.newreport,n=e.getParams(),r=n.startDate,i=n.endDate;return r==""&&(r=t().format("L")),i==""&&(i=t().format("L")),t(r).isBefore(i)||t(r).isSame(i)},conflictValidateDate:function(e){var n=this.newreport,r=n.getParams(),i=r.startDate,s=r.endDate,o=t(i,"MM/DD/YYYY").add("days",e).format("L");return i==""&&(i=t().format("L"),o=t(i,"MM/DD/YYYY").add("days",e).format("L")),s==""&&(s=t().format("L")),t(s).isBefore(o)||t(s).isSame(o)},outsideValidateDate:function(){var e=this.newreport,n=e.getParams(),r=t(n.tFrom,"HH:mm"),i=t(n.tTo,"HH:mm");return t(r).isBefore(i)||t(r).isSame(i)},cleanReport:function(){var n=this,r=n.conf,i=r.EL,s=r.$printButton,o=r.$generateExcel,u=n.formEL,a;e("[name='startDate']").attr("disabled",!1),e("[name='endDate']").attr("disabled",!1),a=n.newreport.getParams(),e("#instructorIdHeadLine").hide(),e("#inputExternalId").hide(),e("#instructorNameHeadLine").hide(),e("#instructorName").hide(),e("#orHeadLine").hide(),e("#instructorORHeadLine").hide(),e("#checking").hide(),e("#customerIdHeadLine").hide(),e("#externalId").hide(),e("#conflictHeadLine").hide(),e("#conflictId").hide(),e("#dayHeadLine").hide(),e("#tRangeheadLine").hide(),e("#dRangeheadLine").show(),e("#dayId").hide(),e("#sDate").show(),e("#eDate").show(),e("#tRange").hide(),e("#activityIdHeadLine").hide(),e("#activityType").hide(),a.reportType==6&&(e("#instructorIdHeadLine").show(),e("#instructorName").show(),e("#orHeadLine").show(),e("#inputExternalId").show(),e("#instructorNameHeadLine").show(),e("#instructorORHeadLine").show(),e("#checking").show()),a.reportType==8&&(e("#conflictHeadLine").show(),e("#conflictId").show()),a.reportType==9&&(e("#instructorIdHeadLine").show(),e("#inputExternalId").show(),e("#dayHeadLine").show(),e("#tRangeheadLine").show(),e("#dRangeheadLine").hide(),e("#dayId").show(),e("#sDate").hide(),e("#eDate").hide(),e("#tRange").show()),a.reportType==10&&(e("#activityIdHeadLine").show(),e("#instructorName").show(),e("#orHeadLine").show(),e("#activityIdHeadLine").show(),e("#instructorNameHeadLine").show(),e("#instructorORHeadLine").show(),e("#instructorORHeadLine").show(),e("#activityType").show(),e("#checking").show(),e("#sDate").show(),e("#eDate").show()),a.reportType==11&&(e("#sDate").show(),e("#eDate").show()),a.reportType==12&&(e("[name='startDate']").attr("disabled",!0),e("[name='endDate']").attr("disabled",!0)),a.reportType==14&&(e("#customerIdHeadLine").show(),e("#externalId").show(),e("#sDate").show(),e("#eDate").show()),i.empty(),s.hide(),o.hide(),e(".js-report-time").text(t().format("hh:mm:ss A"))}},a}),define("search",["jquery","typeahead","mustache","hash"],function(e,t,n,r){function i(t){this.defaults={url_qucikSearch:"calendar/quickSearch.htm",url_searchCustomer:"searchCustomerByNameOrMail.htm",url_getCustomer:"loadCustomerListFromTab.htm",url_getAppointments:"calendar/loadAppointmentByCustomer.htm",url_searchStudio:"location/locationSearch.htm",EL:{$searchInput:e(".search-query"),$searchForm:e(".form-search"),$quickSearch:e(".quick-search")}},this.init(t)}return i.prototype={init:function(t){return this.conf=e.extend({},this.defaults,t),this},quickSearch:function(){var t=this,i=t.conf,s=i.EL,o,u=s.$quickSearch;o=n.compile('<a href="javascript:;">{{fullName}} {{externalId}} {{status}}</a>'),u.typeahead({recordId:"recordId",valueKey:"fullName",remote:{url:i.url_qucikSearch+"?name=%QUERY",filter:function(e){return e}},template:o,limit:4,minLength:2,engine:n}).on("typeahead:selected",function(t,s){if(s){if(s.type==="more"){e(".btn-quick-search").click(),e(".search-customer").typeahead("setQuery","");return}r.add({customer:s.recordId});var o=n.render('<li><a data-id="{{recordId}}" class="js-show-customer-detail-view" href="javascript:;">{{fullName}}</a></li><li>{{email}}</li><li>Phone {{phone}}</li><li>GC ID {{externalId}}</li><li>Lesson Count {{lessonCount}}</li><li>{{instrumentType}}</li>',s);e(".filter-list").hide().next().show(),e(".customer-info").data("customer",s),e(".customer-info ul").html(o),i.handler(),e(".search-customer").typeahead("setQuery","")}})}},i}),define("studio",["jquery","mustache","underscore","backbone","notification"],function(e,t,n,r,i){function s(t){this.defaults={url_fetch_collection:"location/findAllStudios.htm",url_searchStudio:"location/locationSearch.htm",EL:e(".studio-list")},this.init(t),this.is_edit=!1,this.is_changed=!1}return s.prototype={init:function(t){var n=this;n.conf=e.extend(n.defaults,t)},renderList:function(){var i=this,s=i.conf,o,u,a;i.is_changed=!1,o=r.Model.extend({methodUrl:{read:s.url_fetch_model,create:s.url_create_model,update:s.url_update_model,"delete":""},sync:function(e,t,n){t.methodUrl&&t.methodUrl[e.toLowerCase()]&&(n=n||{},n.url=t.methodUrl[e.toLowerCase()]),r.sync(e,t,n)}}),u=r.View.extend({initialize:function(){return this},events:{"click .edit-studio":"edit","click .close-edit-panel":"close"},tagName:"table",className:"table table-list table-fixed",template:e("#tmpl_studio").html(),render:function(){return this.$el.html(t.render(this.template,this.model.toJSON())),this}}),a=r.Collection.extend({model:o,methodUrl:{read:s.url_fetch_collection,create:"",update:"","delete":""},sync:function(e,t,n){t.methodUrl&&t.methodUrl[e.toLowerCase()]&&(n=n||{},n.url=t.methodUrl[e.toLowerCase()]),r.sync(e,t,n)}});var f=new a;i.Model=o,i.View=u,i.Collection=a,i.studio_collection=f,n.bindAll(i,"addAll","addOne"),f.on("set",i.addAll),f.on("add",i.addOne),i.getAll()},search:function(){var t=this,n=t.conf;e.ajax({url:n.url_searchStudio,type:"GET",data:{searchCriteria:e(".search-query").val()},success:function(e){e.length<1?n.EL.html('<table class="table table-list table-fixed"><tr><th cosplan="4">No matching locations have been found. Please search again.</th></tr></table>'):(n.EL.empty(),t.studio_collection.reset(),t.studio_collection.set(e))}})},getAll:function(){var e=this;e.conf.EL.empty(),e.studio_collection.reset(),e.studio_collection.fetch()},addAll:function(){var e=this;e.studio_collection.each(e.addOne)},addOne:function(e){var t=this.conf.EL,n=new this.View({model:e});t.append(n.render().el)},update:function(t,n){function r(t){e.ajax({url:t,success:function(e){e.status?setTimeout(function(){location.reload()},3e3):i.autoHideError(e.msg)}})}n?r(t):bootbox.dialog({title:"Disable Studio Profile",message:"Are you sure you want to disable the Studio Profile ?\n Studio Profile will no longer be available for Studio Admin.",buttons:{success:{label:"Disable",className:"btn btn-important",callback:function(){r(t)}},cancle:{label:"Cancel",className:"btn btn-primary",callback:function(){return}}}})}},s}),define("eventdeploy",["jquery"],function(e){function t(e){var t=this;e&&e.constructor===Array&&e.forEach(function(e,n){var r,i;r=e.events,i=e.delegate,t.Deploy(r,i)})}return t.prototype={Deploy:function(t,n){var r,i,s;for(var o in t)r=o.split(" ")[0],i=e.trim(o.split(r)[1]),i==="document"&&(i=document),i==="window"&&(i=window),s=t[o],n!==undefined?e(n).on(r,i,s):e(i).on(r,s)},Destroy:function(t,n){var r,i;r=t.split(" ")[0],i=e.trim(t.split(r)[1]),i==="document"&&(i=document),i==="window"&&(i=window),n!==undefined?e(n).off(r,i):e(i).off(r)}},t}),require.config({baseUrl:"js",paths:{jquery:"vendor/jquery/jquery.min",mustache:"vendor/libs/mustache.min",moment:"vendor/libs/moment",icheck:"vendor/libs/jquery.icheck.min",select2:"vendor/libs/select2.min",typeahead:"vendor/libs/typeahead.min",modal:"vendor/libs/bootstrap.modal.min",bootbox:"vendor/libs/bootbox.min",backbone:"vendor/backbone/backbone-min",underscore:"vendor/underscore/underscore-min",pikaday:"vendor/libs/pikaday.min",calendar:"app/modules/calendar",customer:"app/modules/customer",instructor:"app/modules/instructor",instructorschedule:"app/modules/instructorschedule",staff:"app/modules/staff",service:"app/modules/service",activity:"app/modules/activity",room:"app/modules/room",report:"app/modules/report",studio:"app/modules/studio",filter:"app/modules/filter",appointment:"app/modules/appointment",appointment_modal:"app/modules/appointment_modal",edithour_modal:"app/modules/edithour_modal",studiohour_modal:"app/modules/studiohour_modal",timeoff_modal:"app/modules/timeoff_modal",search:"app/modules/search",notification:"app/modules/common/notification",pagination:"app/modules/common/pagination",profiletimeoff_modal:"app/modules/profile_timeoff_modal",customeredit_modal:"app/modules/customeredit_modal",conflictingappointments_modal:"app/modules/conflictingappointments_modal",bookedAppointments:"app/modules/bookedAppointments",customer_detail:"app/modules/customer_detail",list:"app/modules/common/list",list_view:"app/modules/common/list_view",eventdeploy:"utils/eventdeploy.min",hash:"utils/hash"},shim:{backbone:{deps:["underscore","jquery"],exports:"Backbone"},underscore:{exports:"_"},moment:{deps:["jquery"],exports:"moment"},mustache:{exports:"Mustache"},icheck:{deps:["jquery"],exports:"iCheck"},modal:{deps:["jquery"]},bootbox:{deps:["jquery","modal"],exports:"bootbox"},pikaday:{deps:["moment"],exports:"Pikaday"},select2:{deps:["jquery"],exports:"select2"},typeahead:{deps:["jquery"],exports:"typeahead"}}}),require(["jquery","moment","calendar","filter","appointment","appointment_modal","edithour_modal","studiohour_modal","profiletimeoff_modal","customeredit_modal","customer","instructor","instructorschedule","staff","service","activity","room","app/modules/bookedAppointments","customer_detail","report","search","studio","pikaday","hash","eventdeploy"],function(e,t,n,r,i,s,o,u,a,f,l,c,h,p,d,v,m,g,y,b,w,E,S,x,T){is_changed=!1;var N={$createBtn:e(".btn-create-app"),$studioHeader:e(".js-studio-info"),$reportHeader:e(".js-report-info"),$bookedAppointmentHeader:e(".js-booked-appointment-info"),_change:function(t){document.title=t.text()+" - GCS",e(".box-body").find(".btn-primary").click(),e(".warn-tip").hide(),t.addClass("active").siblings().removeClass("active"),e(t.data("tab")).addClass("active").siblings().removeClass("active")},_changeToCustomerDetail:function(t){I.renderPage(t),e(".warn-tip").hide(),e(".tab-customer_detail").addClass("active").siblings().removeClass("active")},_changeToCustomerEditDetail:function(e,t){q.showModal(e,t)},showSchedule:function(t){this.$createBtn.show(),this.$studioHeader.show(),this.$reportHeader.hide(),this.$bookedAppointmentHeader.hide(),this._change(t),e(".quick-search").typeahead("setQuery","")},showCustomer:function(t){e(".search-query").val(""),this.$createBtn.hide(),this.$studioHeader.show(),this.$reportHeader.hide(),this.$bookedAppointmentHeader.hide(),this._change(t)},showInstructorStaff:function(e){this.$createBtn.hide(),this.$studioHeader.show(),this.$reportHeader.hide(),this.$bookedAppointmentHeader.hide(),this._change(e)},showServiceActivity:function(e){this.$createBtn.hide(),this.$studioHeader.show(),this.$reportHeader.hide(),this.$bookedAppointmentHeader.hide(),this._change(e)},showInstructorSchedule:function(e){this.$createBtn.hide(),this.$studioHeader.show(),this.$reportHeader.hide(),this.$bookedAppointmentHeader.hide(),this._change(e)},showRoom:function(e){this.$createBtn.hide(),this.$studioHeader.show(),this.$reportHeader.hide(),this.$bookedAppointmentHeader.hide(),this._change(e)},showBookedAppointments:function(e){F.initDate(),this.$createBtn.hide(),this.$studioHeader.show(),this.$reportHeader.hide(),this.$bookedAppointmentHeader.show(),this._change(e)},showReport:function(e){R.initDate(),this.$studioHeader.hide(),this.$reportHeader.show(),this.$bookedAppointmentHeader.hide(),this._change(e)},showCustomerInfo:function(e){this._change(e)}},C=window.location,k=e(".type-switch"),L=e("#cal_view_day");e.ajaxSetup({beforeSend:function(){console.log("Loadings...")},complete:function(e){e&&e.responseText&&e.responseText.indexOf("DOCTYPE")!==-1&&C.reload(),console.log("Complete")},error:function(t){e("button:disabled").attr("disabled",!1)}});var A=new i,O=new s({afterCreate:function(){var t=e(".type-switch").find(".active");shtValue=t.text().toLowerCase();var n=x.get("date"),r=x.get("view"),i=W.getFilter(),s=shtValue;U.getCalendar(n,i,s)}}),M=new l({createAppointment:function(n){var r=x.get("date")||t().format("L"),i=x.get("filter");W.initFilter(x.get("view"),""),N.showSchedule(e(".js-show-schedule")),O.create(n)},gotoToday:function(){J.setDate(new Date)},gotoCalendar:function(){N.showSchedule(e(".js-show-schedule")),Y(x)},gotoCustomerDetail:function(e){N._changeToCustomerDetail(e)},gotoCustomerEdit:function(e,t){N._changeToCustomerEditDetail(e,t)}}),_=new c,D=new h,P=new p,H=new d,B=new v,j=new m,F=new g,I=new y,q=new f,R=new b,U=new n({handler:function(e,t,n,r,i,s,o){A.showAll(e,t,n,r,i,s,o)}}),z=new u,W=new r({handler:function(e,t,n,r){U.setView(t),U.getCalendar(e,n,r)}}),X=new a,V=new E,$=new w({handler:function(){e("#cal_view_month").trigger("click")}}),J=new S({onSelect:function(e){Z();var e=this.getMoment().format("L"),t=W.getFilter();U.getCalendar(e,t),U.showDate(e)}}),K=new T([{delegate:"#cal_content",events:{"click .js-to-day":function(){var t=e(this).data("date"),n=W.getFilter();U.dayView(t,n),L.addClass("active").siblings().removeClass("active"),k.show()},"click .available":function(){O.create(this)},"click .cal-item":function(){O.edit(this)},"click .month-item":function(){var t=e(this).find("p").data("date");J.setDate(new Date(t))}}}]);K.Deploy({"click .js-show-schedule":function(t){t.preventDefault();var n=e(this);is_changed?G(function(){n.click()}):(N.showSchedule(e(this)),W.initFilter(x.get("view"),x.get("filter")))},"click .js-show-customer":function(t){t.preventDefault();var n=e(this);is_changed?G(function(){n.click()}):(N.showCustomer(e(this)),e(".search-customer").typeahead("setQuery",""),M.renderList())},"click .js-show-instructor-staff":function(t){t.preventDefault();var n=e(this);is_changed?G(function(){n.click()}):(N.showInstructorStaff(e(this)),_.renderList())},"click .js-show-instructor_schedule":function(t){t.preventDefault();var n=e(this);is_changed?G(function(){n.click()}):(N.showInstructorStaff(e(this)),D.renderInstructorScheduleList())},"click .js-show-staff":function(t){t.preventDefault();var n=e(this);is_changed?G(function(){n.click()}):(N.showInstructorStaff(e(this)),e("#staff_table").length>0&&P.renderList())},"click .js-show-service-activity":function(t){t.preventDefault();var n=e(this);is_changed?G(function(){n.click()}):(N.showServiceActivity(e(this)),H.renderList(),B.renderList())},"click .js-show-booked-appointments":function(t){t.preventDefault();var n=e(this);is_changed?G(function(){n.click()}):(N.showBookedAppointments(e(this)),F.renderList())},"click .js-show-room":function(t){t.preventDefault();var n=e(this);is_changed?G(function(){n.click()}):(N.showRoom(e(this)),j.renderList())},"click .js-show-instructor_schedulex":function(t){t.preventDefault();var n=e(this);is_changed?G(function(){n.click()}):(N.showInstructorSchedule(e(this)),j.renderList())},"click .btn-search-customer":function(e){e.preventDefault(),M.search()},"click .all-customer":function(e){e.preventDefault(),M.searchAllCustomer("all")},"click .store-customer":function(e){e.preventDefault(),M.searchAllCustomer("store")},"click .js-show-report":function(t){t.preventDefault();var n=e(this);is_changed?G(function(){n.click()}):(R.cleanReport(),N.showReport(e(this)))}}),W.initFilter(x.get("view"),x.get("filter")),K.Deploy({"click .btn-cal-pre":function(){var e=x.get("date"),t=W.getFilter();U.prev(e,t);var n=x.get("date");J.setDate(new Date(n))},"click .btn-cal-next":function(){var e=x.get("date"),t=W.getFilter();U.next(e,t);var n=x.get("date");J.setDate(new Date(n))},"click #cal_view_day":function(){e(".cal-item-disabled").length>0&&Z();var t=x.get("date"),n=W.getFilter();U.dayView(t,n),k.show()},"click #cal_today":function(e){e.preventDefault();var n=t().format("L"),r=W.getFilter();U.gotoToday(r),J.setDate(new Date),L.addClass("active").siblings().removeClass("active"),k.show()},"click #cal_filter_instructor":function(){Z();var e=W.getFilter(),t=x.get("date");U.getCalendar(t,e,"instructor")},"click #cal_filter_room":function(){Z();var e=W.getFilter(),t=x.get("date");U.getCalendar(t,e,"room")},"click #cal_view_week":function(){Z();var e=x.get("date"),t=W.getFilter();U.weekView(e,t),x.remove("filter"),k.hide()},"click #cal_view_month":function(){Z();var e=x.get("date"),t=W.getFilter();U.monthView(e,t),x.remove("filter"),k.hide()}}),K.Deploy({"click .btn-create-app":function(){O.create()},"click .btn-close-app-modal":function(){O.hide()},"click .btn-submit-app":function(e){e.preventDefault(),O.submit(e)},"click .icon-calendar":function(){e(this).prev().click()},"click .js-conflict-app":function(){O.conflictAppoointments()},"click .js-cancel-appointment":function(){O.cancel()}}),K.Deploy({"click #edit_hour":function(){z.showModal()},"click #profiletimeoff_edit":function(){X.showModal(e.extend(!0,{},this.model))},"click .js-set-timeoff-pr":function(t){X.showModal(e.extend(!0,{},this.model))},"click .btn-update-timeoff-pr":function(e){X.update(e)},"click .js-close-timeoff-modal-pr":function(e){X.hideModal(e)},"click .js-close-edit-cust-modal":function(e){q.hideModal(e)},"click .btn-update-edit-cust":function(e){q.update(e)},"click .btn-close-modal":function(){z.hideModal()},"click .btn-update-studiohour":function(e){z.update(e)},"click .js-close-studio-modal":function(){z.hideModal()}}),K.Deploy({"hashchange window":function(){var t=x.get("customer");t===undefined?(W.show(),e(".customer-info").data("customer","")):W.hide()}}),K.Deploy({"click .js-clear-customer":function(){x.remove("customer");var t=x.get("view");e("#cal_view_"+t).click(),e(".quick-search").val(""),e(".quick-search").typeahead("setQuery","")}}),e("#datepicker").after(J.el),L.trigger("click"),$.quickSearch(),K.Deploy({"click .btn-quick-search":function(t){t.preventDefault(),M.search(e(".quick-search").val()),N.showCustomer(e(".js-show-customer"))}}),K.Deploy({"click .js-show-service-form":function(){e(this).addClass("active"),H.showForm()},"click .js-cancel-service":function(){H.hideForm()},"click .js-add-service":function(e){e.preventDefault(),H.create()}}),H.newServiceForm(),K.Deploy({"click .js-show-activity-form":function(t){e(this).addClass("active"),B.showForm()},"click .js-add-activity":function(e){e.preventDefault(),B.create()},"click .js-cancel-activity":function(){B.hideForm()}}),B.newActivityForm(),K.Deploy({"click .js-show-room-form":function(){e(this).addClass("active"),j.showForm()},"click .js-add-room":function(e){e.preventDefault(),j.createRoom()},"click .js-cancel-room":function(){j.hideForm()}}),j.newRoomForm(),K.Deploy({"click .js-show-booked-appointments-form":function(){e(this).addClass("active"),console.log("booked-appointments showform()")},"click .js-add-room":function(e){e.preventDefault(),console.log("booked-appointments create()")},"click .js-cancel-room":function(){console.log("booked-appointments hide()")}}),K.Deploy({"click .js-query-appointments":function(e){e.preventDefault(),F.renderList()}}),K.Deploy({"click .js-create-report":function(e){e.preventDefault(),R.renderList()}}),K.Deploy({"click .js-studio-is-enable":function(t){t.preventDefault(),e(this).hasClass("N")?V.update(this.href,!0):V.update(this.href,!1)}}),e(".js-studio-is-enable").hasClass("N")&&(K.Destroy("click .btn-create-app"),K.Destroy("click .available","#cal_content"));var Q=new T([{delegate:".customer-info",events:{"click .js-show-customer-detail-view":function(t){var n=e(t.currentTarget),r=n.data("id");N._changeToCustomerDetail(r)}}}]),G=function(e){bootbox.dialog({title:"Warning!",message:"You have unsaved information. Do you want to continue?",buttons:{success:{label:"Continue",className:"btn btn-important",callback:function(){is_changed=!1,e()}},cancle:{label:"Cancel",className:"btn btn-primary",callback:function(){return}}}})},Y=function(t){var n=t.get("view"),r=t.get("filter");n==="month"?e("#cal_view_month").trigger("click"):n==="week"?e("#cal_view_week").trigger("click"):r==="instructor"?e("#cal_filter_instructor").trigger("click"):e("#cal_filter_room").trigger("click")},Z=function(){e(".cal-item-disabled").remove()}}),define("app/scripts/schedule",function(){});