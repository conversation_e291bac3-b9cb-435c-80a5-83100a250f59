require.config({
	baseUrl: 'js',
	paths: {
		/* Vendor */
		'jquery': 'vendor/jquery/jquery.min',
		'mustache': 'vendor/libs/mustache.min',
		'moment': 'vendor/libs/moment',
		'icheck': 'vendor/libs/jquery.icheck.min',
		'select2': 'vendor/libs/select2.min',
		'typeahead': 'vendor/libs/typeahead.min',
		'modal': 'vendor/libs/bootstrap.modal.min',
		'bootbox': 'vendor/libs/bootbox.min',
		'backbone': 'vendor/backbone/backbone-min',
		'underscore': 'vendor/underscore/underscore-min',
		'pikaday': 'vendor/libs/pikaday.min',

		/* Modules */
		'calendar': 'app/modules/calendar',
		'customer': 'app/modules/customer',
		'instructor': 'app/modules/instructor',
		//--Changes made for GSSP-363 --
		'instructorschedule': 'app/modules/instructorschedule',
		'staff': 'app/modules/staff',
		'service': 'app/modules/service',
		'activity': 'app/modules/activity',
		'room': 'app/modules/room',
		'report': 'app/modules/report',
		'studio': 'app/modules/studio',
		'filter': 'app/modules/filter',
		'appointment': 'app/modules/appointment',
		'appointment_modal': 'app/modules/appointment_modal',
		'edithour_modal': 'app/modules/edithour_modal',
		'studiohour_modal': 'app/modules/studiohour_modal',
		'timeoff_modal': 'app/modules/timeoff_modal',
		'search': 'app/modules/search',
		'notification': 'app/modules/common/notification',
		'pagination': 'app/modules/common/pagination',
		//Changes made for GSSP-334
		'profiletimeoff_modal': 'app/modules/profile_timeoff_modal',
		'customeredit_modal': 'app/modules/customeredit_modal',


		//Changes made for GSSP-241
		'conflictingappointments_modal': 'app/modules/conflictingappointments_modal',

		//Changes made for GSSP-368
		'bookedAppointments': 'app/modules/bookedAppointments',
		//LES-624
		"customer_detail":'app/modules/customer_detail',
		'list': 'app/modules/common/list',
		'list_view': 'app/modules/common/list_view',

		/* Utils */
		'eventdeploy': 'utils/eventdeploy.min',
		'hash': 'utils/hash'
	},
	shim: {
		'backbone': {
			deps: ['underscore', 'jquery'],
			exports: 'Backbone'
		},
		'underscore': {
			exports: '_'
		},
		'moment': {
			deps: ['jquery'],
			exports: 'moment'
		},
		'mustache': {
			exports: 'Mustache'
		},
		'icheck': {
			deps: ['jquery'],
			exports: 'iCheck'
		},
		'modal': {
			deps: ['jquery']
		},
		'bootbox': {
			deps: ['jquery', 'modal'],
			exports: 'bootbox'
		},
		'pikaday': {
			deps: ['moment'],
			exports: 'Pikaday'
		},
		'select2': {
			deps: ['jquery'],
			exports: 'select2'
		},
		'typeahead': {
			deps: ['jquery'],
			exports: 'typeahead'
		}
	}
});

require([
			'jquery',
			'moment',
			'calendar',
			'filter',
			'appointment',
			'appointment_modal',
			'edithour_modal',
			'studiohour_modal',
			'profiletimeoff_modal',
			'customeredit_modal',
			'customer',
			'instructor',
			'instructorschedule',
			'staff',
			'service',
			'activity',
			'room',
			//FIXME: this require path need to be fixed
			'app/modules/bookedAppointments',
			'customer_detail',
			'report',
			'search',
			'studio',
			'pikaday',
			'hash',
			'eventdeploy'
		],
		function($, moment, Calendar, Filter, Appointment, AppointmentModal, EdithourModal,  StudiohourModal, ProfiletimeoffModal, CustomereditModal, Customer, Instructor,Instructorschedule, Staff, Service, Activity, Room, BookedAppointments, CustomerDetail, Report, Search, Studio, Pikaday, hash, Eventdeploy) {
			is_changed = false;

			// Global controller used for tab change
			var controller = {
				$createBtn: $('.btn-create-app'),
				$studioHeader: $('.js-studio-info'),
				$reportHeader: $('.js-report-info'),
				$bookedAppointmentHeader: $('.js-booked-appointment-info'),
				/*
        * Tab change function
        * @param {jQuery Object} the clicked target
        */
				_change: function(target) {
					// Change the title
					document.title = target.text() + ' - GCS';
					// Close the opened form
					$('.box-body').find('.btn-primary').click();
					$('.warn-tip').hide();
					// Add active class
					target.addClass('active').siblings().removeClass('active');
					$(target.data('tab')).addClass('active').siblings().removeClass('active');
				},
				_changeToCustomerDetail: function(customerId){
					customerDetail.renderPage(customerId);
					// Close the opened form
					$('.warn-tip').hide();
					// show customer detail page
					$('.tab-customer_detail').addClass('active').siblings().removeClass('active');
				},
				_changeToCustomerEditDetail: function(customerId,searchCustCriteria){
					customereditModal.showModal(customerId,searchCustCriteria);
					// Close the opened form
					//$('.warn-tip').hide();
					// show customer detail page
					//$('.tab-customer_detail').addClass('active').siblings().removeClass('active');
				},
				/*
        * Show schedule view
        */
				showSchedule: function(target) {
					this.$createBtn.show();
					this.$studioHeader.show();
					this.$reportHeader.hide();
					this.$bookedAppointmentHeader.hide();
					this._change(target);
					$('.quick-search').typeahead('setQuery', '');
				},
				/*
        * Show customer view
        */
				showCustomer: function(target) {
					//hash.clear();
					$('.search-query').val('');
					this.$createBtn.hide();
					this.$studioHeader.show();
					this.$reportHeader.hide();
					this.$bookedAppointmentHeader.hide();
					this._change(target);
				},
				/*
        * Show instructor view
        */
				showInstructorStaff: function(target) {
					//hash.clear();
					this.$createBtn.hide();
					this.$studioHeader.show();
					this.$reportHeader.hide();
					this.$bookedAppointmentHeader.hide();
					this._change(target);
				},
				/*
        * Show service&activity view
        */
				showServiceActivity: function(target) {
					//hash.clear();
					this.$createBtn.hide();
					this.$studioHeader.show();
					this.$reportHeader.hide();
					this.$bookedAppointmentHeader.hide();
					this._change(target);
				},
				/*
        * Show room view
        */
				// GSSP-363 :: Added Changes as part of Instructor View
				showInstructorSchedule: function(target) {
					//hash.clear();
					this.$createBtn.hide();
					this.$studioHeader.show();
					this.$reportHeader.hide();
					this.$bookedAppointmentHeader.hide();
					this._change(target);
				},
				/*
        * Show room view
        */
				showRoom: function(target) {
					//hash.clear();
					this.$createBtn.hide();
					this.$studioHeader.show();
					this.$reportHeader.hide();
					this.$bookedAppointmentHeader.hide();
					this._change(target);
				},
				/*
        * Show booked appointments view
        */
				showBookedAppointments: function(target) {
					//hash.clear();
					bookedAppointments.initDate();
					this.$createBtn.hide();
					this.$studioHeader.show();
					this.$reportHeader.hide();
					this.$bookedAppointmentHeader.show();
					this._change(target);
				},
				/*
        * Show report view
        */
				showReport: function(target) {
					//hash.clear();
					report.initDate();
					this.$studioHeader.hide();
					this.$reportHeader.show();
					this.$bookedAppointmentHeader.hide();
					this._change(target);
				},
				showCustomerInfo: function(target){
					//	customerInfo.loadInfo();
					this._change(target);
				}
			}

			var location = window.location,
					$type_switch_btns = $('.type-switch'),
					$view_day_btn =  $('#cal_view_day');

			// TODO
			// Ajax global setup
			$.ajaxSetup({
				beforeSend: function() {
					console.log('Loadings...');
					// $('.window-tip').show();
				},
				complete: function(result) {
					if(result && result.responseText && result.responseText.indexOf('DOCTYPE') !== -1) location.reload();
					// $('.window-tip').hide();
					console.log('Complete');
				},
				error: function(result) {
					$('button:disabled').attr('disabled', false);
				}
			});

			// Init Modules
			var appointment = new Appointment,
					appointment_modal = new AppointmentModal({
						afterCreate: function() {

							//Fixed the forward to instructor view when click on 'Create Appointment' button
							var sht = $('.type-switch').find('.active');
							shtValue = sht.text().toLowerCase();

							var date = hash.get('date'),
									view = hash.get('view'),
									params = filter.getFilter(),
//					filterType = hash.get('filter');
									filterType = shtValue;
							calendar.getCalendar(date, params, filterType);
						}
					}),
					
					customer = new Customer({
						createAppointment: function(e) {
							var date = hash.get('date') || moment().format('L'),
									filterType = hash.get('filter');
							filter.initFilter(hash.get('view'), '');//From customer tab
							controller.showSchedule($('.js-show-schedule'));
							appointment_modal.create(e);
						},
						/**
						 * Set current date and current calendar control view
						 */
						gotoToday: function() {
							picker.setDate(new Date());
						},
						/**
						 * GCSS-491
						 */
						gotoCalendar: function() {
							controller.showSchedule($('.js-show-schedule'));
							/**
							 * For gcss-575,don't trigger 'month view' click event when from customer tab to schedule tab,
							 * just keep the origion view
							 */
							checkView(hash);
							//$('#cal_view_month').trigger('click');
						},
						gotoCustomerDetail:function(customerId){
							controller._changeToCustomerDetail(customerId);
						},
						gotoCustomerEdit:function(customerId,searchCustCriteria){
							controller._changeToCustomerEditDetail(customerId,searchCustCriteria);
						}
					}),
					instructor = new Instructor,
					instructorschedule = new Instructorschedule,
					staff = new Staff,
					service = new Service,
					activity = new Activity,
					room = new Room,
					bookedAppointments = new BookedAppointments,
					customerDetail = new CustomerDetail,
					customereditModal = new CustomereditModal,
					report = new Report,
					calendar = new Calendar({
						/**
						 * For gcss-525,add the unselectedAppointments to callback
						 */
						handler: function(instructorList, roomList, unselectedAppointments, appointments, view, filter, wrapper) {
							appointment.showAll(instructorList, roomList, unselectedAppointments, appointments, view, filter, wrapper);
						}
					}),
					studiohour = new StudiohourModal,
					filter = new Filter({
						handler: function(date, view, params, filter) {
							calendar.setView(view);
							calendar.getCalendar(date, params, filter);
						}
					}),
					profiletimeoffModal = new ProfiletimeoffModal,
					studio = new Studio,
					search = new Search({
						handler: function() {
							$('#cal_view_month').trigger('click');
						}
					}),
					picker = new Pikaday({
						onSelect: function(date) {
							removeGrayoutLayer();
							var date = this.getMoment().format('L'),
									params = filter.getFilter();
							calendar.getCalendar(date, params);
							calendar.showDate(date);
						}
					});

			// The events bind
			// Like Backbone bind way
			var ed = new Eventdeploy([
				{
					delegate: '#cal_content',
					events: {
						// Delegate bind, month view jump to day view
						'click .js-to-day': function() {
							var date = $(this).data('date'),
									params = filter.getFilter();
							calendar.dayView(date, params);
							$view_day_btn.addClass('active').siblings().removeClass('active');
							$type_switch_btns.show();
						},
						// Delegate bind, show a modal to create appointment
						'click .available': function() {
							appointment_modal.create(this);
						},
						// Delegate bind, show a modal to edit appointment
						'click .cal-item': function() {
							appointment_modal.edit(this);
						},
						// Delegate bind, change the date
						'click .month-item': function() {
							var date = $(this).find('p').data('date');
							picker.setDate(new Date(date));
						}
					}
				}
			]);

			// Tab change
			ed.Deploy({
				'click .js-show-schedule': function(e) {
					e.preventDefault();

					/**
					 * For gcss-579,alert warning when navigate to another tab without saving changes.
					 */
					var $this = $(this);
					if(is_changed) {
						showDialog(function() {
							$this.click();
						});
					} else {
						controller.showSchedule($(this));
						/**
						 * For GCSS-565,make the instructor view as the default view
						 */
						filter.initFilter(hash.get('view'), hash.get('filter'));
						/**
						 * Set current date and current calendar control view
						 */
						//picker.setDate(new Date());
					}

				},
				'click .js-show-customer': function(e) {
					e.preventDefault();

					/**
					 * For gcss-579,alert warning when navigate to another tab without saving changes.
					 */
					var $this = $(this);
					if(is_changed) {
						showDialog(function() {
							$this.click();
						});
					} else {
						controller.showCustomer($(this));
						$('.search-customer').typeahead('setQuery', '');//For GCSS-494,remove the junk number when click on customer tab again.
						customer.renderList();
					}

				},
				'click .js-show-instructor-staff': function(e) {
					e.preventDefault();

					/**
					 * For gcss-579,alert warning when navigate to another tab without saving changes.
					 */
					var $this = $(this);
					if(is_changed) {
						showDialog(function() {
							$this.click();
						});
					} else {
						controller.showInstructorStaff($(this));
						instructor.renderList();
					}
				},
				//;---363
				'click .js-show-instructor_schedule': function(e) {
					e.preventDefault();

					/**
					 * For gcss-579,alert warning when navigate to another tab without saving changes.
					 */
					var $this = $(this);
					if(is_changed) {
						showDialog(function() {
							$this.click();
						});
					} else {
						controller.showInstructorStaff($(this));
						instructorschedule.renderInstructorScheduleList();
					}
				},
				//GSSP-285 changes.
				'click .js-show-staff': function(e) {
					e.preventDefault();

					/**
					 * For gcss-579,alert warning when navigate to another tab without saving changes.
					 */
					var $this = $(this);
					if(is_changed) {
						showDialog(function() {
							$this.click();
						});
					} else {
						controller.showInstructorStaff($(this));
						if($('#staff_table').length > 0) {
							staff.renderList();
						}
					}
				},
				'click .js-show-service-activity': function(e) {
					e.preventDefault();

					/**
					 * For gcss-579,alert warning when navigate to another tab without saving changes.
					 */
					var $this = $(this);
					if(is_changed) {
						showDialog(function() {
							$this.click();
						});
					} else {
						controller.showServiceActivity($(this));
						service.renderList();
						activity.renderList();
					}
				},
				'click .js-show-booked-appointments': function(e) {
					e.preventDefault();
					var $this = $(this);
					if(is_changed) {
						showDialog(function() {
							$this.click();
						});
					} else {
						controller.showBookedAppointments($(this));
						bookedAppointments.renderList();
					}
				},
				'click .js-show-room': function(e) {
					e.preventDefault();

					/**
					 * For gcss-579,alert warning when navigate to another tab without saving changes.
					 */
					var $this = $(this);
					if(is_changed) {
						showDialog(function() {
							$this.click();
						});
					} else {
						controller.showRoom($(this));
						room.renderList();
					}
				},
				// GSSP-363 :: Added Changes as part of Instructor View  suspend for test
				'click .js-show-instructor_schedulex': function(e) {
					e.preventDefault();

					/**
					 * For gcss-579,alert warning when navigate to another tab without saving changes.
					 */
					var $this = $(this);
					if(is_changed) {
						showDialog(function() {
							$this.click();
						});
					} else {
						controller.showInstructorSchedule($(this));
						//TODO:: Need to Change this.
						room.renderList();
						//alert("else part");
					}
				},
				'click .btn-search-customer': function(e) {
					e.preventDefault();
					customer.search();
				},
				'click .all-customer': function(e) {
					e.preventDefault();
					customer.searchAllCustomer('all');
				},
				'click .store-customer': function(e) {
					e.preventDefault();
					customer.searchAllCustomer('store');
				},
				'click .js-show-report': function(e) {
					e.preventDefault();

					/**
					 * For gcss-579,alert warning when navigate to another tab without saving changes.
					 */
					var $this = $(this);
					if(is_changed) {
						showDialog(function() {
							$this.click();
						});
					} else {
						report.cleanReport();
						controller.showReport($(this));
					}
				},
			});

			/**
			 * For GCSS-603,this will invoke the initFilter() method to initialize the cache object
			 */
			filter.initFilter(hash.get('view'), hash.get('filter'));

			// Calendar
			ed.Deploy({
				// Previous date
				'click .btn-cal-pre': function() {
					var	date = hash.get('date'),
							params = filter.getFilter();
					calendar.prev(date, params);
					var currentDate = hash.get('date');
					picker.setDate(new Date(currentDate));
				},
				// Next date
				'click .btn-cal-next': function() {
					var	date = hash.get('date'),
							params = filter.getFilter();
					calendar.next(date, params);
					var currentDate = hash.get('date');
					picker.setDate(new Date(currentDate));
				},
				// Day view show
				'click #cal_view_day': function() {
//			var date = moment().format('L'),
					if($('.cal-item-disabled').length > 0) {
						removeGrayoutLayer();
					}

					var date = hash.get('date'),//For GCSS-393
							params = filter.getFilter();
					calendar.dayView(date, params);
					$type_switch_btns.show();
				},
				// Goto today
				'click #cal_today': function(e) {
					e.preventDefault();
					var	date = moment().format('L'),
							params = filter.getFilter();
					calendar.gotoToday(params);
					/**
					 * Set current date and current calendar control view
					 */
					picker.setDate(new Date());
					$view_day_btn.addClass('active').siblings().removeClass('active');
					$type_switch_btns.show();
				},
				// Instructor filter
				'click #cal_filter_instructor': function() {
					removeGrayoutLayer();

					var	params = filter.getFilter(),
							date = hash.get('date');
					calendar.getCalendar(date, params, 'instructor');
				},
				// Room filter
				'click #cal_filter_room': function() {
					removeGrayoutLayer();

					var	params = filter.getFilter(),
							date = hash.get('date');
					calendar.getCalendar(date, params, 'room');
				},
				// Week view show
				'click #cal_view_week': function() {
//			var date = moment().format('L'),
					removeGrayoutLayer();

					var date = hash.get('date'),//For GCSS-393
							params = filter.getFilter();
					calendar.weekView(date, params);
					hash.remove('filter');
					$type_switch_btns.hide();
				},
				// Month view show
				'click #cal_view_month': function() {
					removeGrayoutLayer();

//			var date = moment().format('L'),
					var date = hash.get('date'),//For GCSS-393
							params = filter.getFilter();
					calendar.monthView(date, params);
					hash.remove('filter');
					$type_switch_btns.hide();
				}
			});

			// Appointment modal
			ed.Deploy({
				'click .btn-create-app': function() {
					appointment_modal.create();
				},
				'click .btn-close-app-modal': function() {
					appointment_modal.hide();
				},
				'click .btn-submit-app': function(e) {
					e.preventDefault();
					appointment_modal.submit(e);
				},
				'click .icon-calendar': function() {
					$(this).prev().click();
				},


				//Changes made for GSSP-241
				'click .js-conflict-app': function(){
					appointment_modal.conflictAppoointments();
				},


				'click .js-cancel-appointment': function(){
					appointment_modal.cancel();
				}
				// 'keydown document': function(e) {
				// 	if(e.keyCode === 27) {
				// 		var bootboxObj = $('.modal-content');
				// 		if(bootboxObj.length == 1) {
				// 			//bootboxObj.cancel();
				// 		} else {
				// 			appointment_modal.hide();
				// 		}
				// 	}
				// }
			});

			// Edithour modal
			ed.Deploy({
				'click #edit_hour': function() {
					studiohour.showModal();
				},
				'click #profiletimeoff_edit': function() {
					profiletimeoffModal.showModal($.extend(true, {}, this.model));
				},
				'click .js-set-timeoff-pr': function(e) {
					profiletimeoffModal.showModal($.extend(true, {}, this.model));
				},
				'click .btn-update-timeoff-pr': function(e) {
					profiletimeoffModal.update(e);
				},
				'click .js-close-timeoff-modal-pr': function(e) {
					profiletimeoffModal.hideModal(e)
				},
				'click .js-close-edit-cust-modal': function(e) {
					customereditModal.hideModal(e)
				},
				'click .btn-update-edit-cust': function(e) {
					customereditModal.update(e);
				},
				'click .btn-close-modal': function() {
					studiohour.hideModal();
				},
				'click .btn-update-studiohour': function(e) {
					studiohour.update(e);
				},
				'click .js-close-studio-modal': function() {
					studiohour.hideModal();
				}
			});

			// Listen the hash or url change
			// For customer change
			ed.Deploy({
				'hashchange window': function() {
					var customer = hash.get('customer');
					if(customer === undefined) {
						filter.show();
						// Clear the data store in the DOM
						$('.customer-info').data('customer','');
					} else {
						filter.hide();
					}
				}
			});

			// Clear customer info
			// Back to normal view
			ed.Deploy({
				'click .js-clear-customer': function() {
					hash.remove('customer');
					var view  = hash.get('view');
					$('#cal_view_'+ view).click();
					$('.quick-search').val('');

					//For GCSS-510,clear setQuery attribute which is called by typeahead to avoid junk number in search box.
					$('.quick-search').typeahead('setQuery', '');
				}
			});

			// Append the left datepiacker
			$('#datepicker').after(picker.el);

			// Trigger the day view
			$view_day_btn.trigger('click');

			// Search
			search.quickSearch();
			ed.Deploy({
				'click .btn-quick-search': function(e) {
					e.preventDefault();
					customer.search($('.quick-search').val());
					controller.showCustomer($('.js-show-customer'));
				}
			});

			// Service
			ed.Deploy({
				'click .js-show-service-form': function() {
					$(this).addClass('active');
					service.showForm();
				},
				'click .js-cancel-service': function() {
					service.hideForm();
				},
				'click .js-add-service': function(e) {
					e.preventDefault();
					service.create();
				}
			});
			service.newServiceForm();

			// Activity
			ed.Deploy({
				'click .js-show-activity-form': function(e) {
					$(this).addClass('active');
					activity.showForm();
				},
				'click .js-add-activity': function(e) {
					e.preventDefault();
					activity.create();
				},
				'click .js-cancel-activity': function() {
					activity.hideForm();
				}
			});
			activity.newActivityForm();

			// Room
			ed.Deploy({
				'click .js-show-room-form': function() {
					$(this).addClass('active');
					room.showForm();
				},
				'click .js-add-room': function(e) {
					e.preventDefault();
					room.createRoom();
				},
				'click .js-cancel-room': function() {
					room.hideForm();
				}
			});
			room.newRoomForm();

			// Booked Appointments
			ed.Deploy({
				'click .js-show-booked-appointments-form': function() {
					$(this).addClass('active');
					console.log('booked-appointments showform()')
					// bookedAppointments.showForm();
				},
				'click .js-add-room': function(e) {
					e.preventDefault();
					console.log('booked-appointments create()')
				},
				'click .js-cancel-room': function() {
					console.log('booked-appointments hide()')
				}
			});
			// bookedAppointments.newRoomForm();

			// bookedAppointment list query by date
			ed.Deploy({
				'click .js-query-appointments': function(e) {
					e.preventDefault();
					bookedAppointments.renderList();
				}
			});

			// Report
			ed.Deploy({
				'click .js-create-report': function(e) {
					e.preventDefault();
					report.renderList();
				}
			});

			// If it's a disabled stuido
			// Unbind the events
			ed.Deploy({
				'click .js-studio-is-enable': function(e) {
					e.preventDefault();
					if($(this).hasClass('N')) {
						studio.update(this.href, true);
					} else {
						studio.update(this.href, false);
					}
				}
			});
			if($('.js-studio-is-enable').hasClass('N')) {
				ed.Destroy('click .btn-create-app');
				ed.Destroy('click .available', '#cal_content');
			}

			var dynamicEl = new Eventdeploy([
				{
					delegate: '.customer-info',
					events: {
						'click .js-show-customer-detail-view': function(e) {
							var currentCustomerEL = $(e.currentTarget), 
								customerId = currentCustomerEL.data('id')
								controller._changeToCustomerDetail(customerId);
						}
					}
				}
			]);

			var showDialog = function(cb) {
				bootbox.dialog({
					title: 'Warning!',
					message: 'You have unsaved information. Do you want to continue?',
					buttons: {
						success: {
							label: 'Continue',
							className: 'btn btn-important',
							callback: function() {
								is_changed = false;
								cb();
							}
						},
						cancle: {
							label: 'Cancel',
							className: 'btn btn-primary',
							callback: function() {
								return;
							}
						}
					}
				});
			}

			/**
			 * This method helps to identify the current view
			 */
			var checkView = function(hash) {
				var viewName = hash.get('view');
				var filterType = hash.get('filter');
				if(viewName === 'month') {
					$('#cal_view_month').trigger('click');
				} else if(viewName === 'week'){
					$('#cal_view_week').trigger('click');
				} else {
					if(filterType === 'instructor') {
						$('#cal_filter_instructor').trigger('click');
					} else {
						$('#cal_filter_room').trigger('click');
					}
				}
			}

			/**
			 * Remove the grayout layout,for gcss-525
			 */
			var removeGrayoutLayer = function() {
				$('.cal-item-disabled').remove();
			}

		});