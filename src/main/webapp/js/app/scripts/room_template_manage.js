require.config({
	baseUrl: 'js',
	paths: {
		/* Vendor */
		'jquery': 'vendor/jquery/jquery.min',
		'mustache': 'vendor/libs/mustache.min',
		'pikaday': 'vendor/libs/pikaday.min',
		'moment': 'vendor/libs/moment',
		'icheck': 'vendor/libs/jquery.icheck.min',
		'select2': 'vendor/libs/select2.min',
		'typeahead': 'vendor/libs/typeahead.min',
		'modal': 'vendor/libs/bootstrap.modal.min',
		'bootbox': 'vendor/libs/bootbox.min',
		'backbone': 'vendor/backbone/backbone-min',
		'underscore': 'vendor/underscore/underscore-min',

		/* Modules */
		'room_centralize': 'app/modules/centralize/room_centralize',
		'edithour_modal': 'app/modules/edithour_modal',
		'search': 'app/modules/search',
		'notification': 'app/modules/common/notification',

		/* Utils */
		'eventdeploy': 'utils/eventdeploy',
		'hash': 'utils/hash'
	},
	shim: {
		'backbone': {
			deps: ['underscore', 'jquery'],
			exports: 'Backbone'
		},
		'underscore': {
			exports: '_'
		},
		'icheck': {
			deps: ['jquery'],
			exports: 'iCheck'
		},
		'modal': {
			deps: ['jquery']
		},
		'bootbox': {
			deps: ['jquery', 'modal'],
			exports: 'bootbox'
		},
		'pikaday': {
			deps: ['moment'],
			exports: 'Pikaday'
		},
		'select2': {
			deps: ['jquery'],
			exports: 'select2'
		},
		'typeahead': {
			deps: ['jquery'],
			exports: 'typeahead'
		}
	}
});

require([
	'jquery', 
	'mustache', 
	'underscore', 
	'backbone', 
	'room_centralize',
	'eventdeploy'
], 
function($, Mustache, _, Backbone, RoomCentralize, Eventdeploy) {

	var room = new RoomCentralize;

	room.renderList();

	room.newTemplateForm();

	var ed = new Eventdeploy;
	ed.Deploy({
		'click .js-show-form': function() {
			$(this).addClass('active');
			room.showForm();
		},
		'click .js-add-room': function(e) {
			e.preventDefault();
			room.createRoom();
		},
		'click .js-hide-form': function(e) {
			e.preventDefault();
			room.hideForm();
		}
	});
});