/*! jQuery v2.0.3 | (c) 2005, 2013 jQuery Foundation, Inc. | jquery.org/license
//@ sourceMappingURL=jquery.min.map
*/

/*!
 * mustache.js - Logic-less {{mustache}} templates with JavaScript
 * http://github.com/janl/mustache.js
 */

//     Underscore.js 1.5.1
//     http://underscorejs.org
//     (c) 2009-2013 <PERSON>, DocumentCloud and Investigative Reporters & Editors
//     Underscore may be freely distributed under the MIT license.

//     (c) 2010-2013 <PERSON>, DocumentCloud Inc.
//     Backbone may be freely distributed under the MIT license.
//     For all details and documentation:
//     http://backbonejs.org

/*
Copyright 2012 Igor <PERSON>

Version: 3.4.1 Timestamp: Thu Jun 27 18:02:10 PDT 2013

This software is licensed under the Apache License, Version 2.0 (the "Apache License") or the GNU
General Public License version 2 (the "GPL License"). You may choose either license to govern your
use of this software only upon the condition that you accept all of the terms of either the Apache
License or the GPL License.

You may obtain a copy of the Apache License and the GPL License at:

http://www.apache.org/licenses/LICENSE-2.0
http://www.gnu.org/licenses/gpl-2.0.html

Unless required by applicable law or agreed to in writing, software distributed under the Apache License
or the GPL Licesnse is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
either express or implied. See the Apache License and the GPL License for the specific language governing
permissions and limitations under the Apache License and the GPL License.
*/

/*!
 * iCheck v0.9.1 jQuery plugin, http://git.io/uhUPMA
 */

/**
 * bootbox.js v4.0.0
 *
 * http://bootboxjs.com/license.txt
 */

(function(e,undefined){function j(e){var t=e.length,n=x.type(e);return x.isWindow(e)?!1:1===e.nodeType&&t?!0:"array"===n||"function"!==n&&(0===t||"number"==typeof t&&t>0&&t-1 in e)}function A(e){var t=D[e]={};return x.each(e.match(w)||[],function(e,n){t[n]=!0}),t}function F(){Object.defineProperty(this.cache={},0,{get:function(){return{}}}),this.expando=x.expando+Math.random()}function P(e,t,n){var r;if(n===undefined&&1===e.nodeType)if(r="data-"+t.replace(O,"-$1").toLowerCase(),n=e.getAttribute(r),"string"==typeof n){try{n="true"===n?!0:"false"===n?!1:"null"===n?null:+n+""===n?+n:H.test(n)?JSON.parse(n):n}catch(i){}L.set(e,t,n)}else n=undefined;return n}function U(){return!0}function Y(){return!1}function V(){try{return o.activeElement}catch(e){}}function Z(e,t){while((e=e[t])&&1!==e.nodeType);return e}function et(e,t,n){if(x.isFunction(t))return x.grep(e,function(e,r){return!!t.call(e,r,e)!==n});if(t.nodeType)return x.grep(e,function(e){return e===t!==n});if("string"==typeof t){if(G.test(t))return x.filter(t,e,n);t=x.filter(t,e)}return x.grep(e,function(e){return g.call(t,e)>=0!==n})}function pt(e,t){return x.nodeName(e,"table")&&x.nodeName(1===t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function ft(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function ht(e){var t=ut.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function dt(e,t){var n=e.length,r=0;for(;n>r;r++)q.set(e[r],"globalEval",!t||q.get(t[r],"globalEval"))}function gt(e,t){var n,r,i,s,o,u,a,f;if(1===t.nodeType){if(q.hasData(e)&&(s=q.access(e),o=q.set(t,s),f=s.events)){delete o.handle,o.events={};for(i in f)for(n=0,r=f[i].length;r>n;n++)x.event.add(t,i,f[i][n])}L.hasData(e)&&(u=L.access(e),a=x.extend({},u),L.set(t,a))}}function mt(e,t){var n=e.getElementsByTagName?e.getElementsByTagName(t||"*"):e.querySelectorAll?e.querySelectorAll(t||"*"):[];return t===undefined||t&&x.nodeName(e,t)?x.merge([e],n):n}function yt(e,t){var n=t.nodeName.toLowerCase();"input"===n&&ot.test(e.type)?t.checked=e.checked:("input"===n||"textarea"===n)&&(t.defaultValue=e.defaultValue)}function At(e,t){if(t in e)return t;var n=t.charAt(0).toUpperCase()+t.slice(1),r=t,i=Dt.length;while(i--)if(t=Dt[i]+n,t in e)return t;return r}function Lt(e,t){return e=t||e,"none"===x.css(e,"display")||!x.contains(e.ownerDocument,e)}function qt(t){return e.getComputedStyle(t,null)}function Ht(e,t){var n,r,i,s=[],o=0,u=e.length;for(;u>o;o++)r=e[o],r.style&&(s[o]=q.get(r,"olddisplay"),n=r.style.display,t?(s[o]||"none"!==n||(r.style.display=""),""===r.style.display&&Lt(r)&&(s[o]=q.access(r,"olddisplay",Rt(r.nodeName)))):s[o]||(i=Lt(r),(n&&"none"!==n||!i)&&q.set(r,"olddisplay",i?n:x.css(r,"display"))));for(o=0;u>o;o++)r=e[o],r.style&&(t&&"none"!==r.style.display&&""!==r.style.display||(r.style.display=t?s[o]||"":"none"));return e}function Ot(e,t,n){var r=Tt.exec(t);return r?Math.max(0,r[1]-(n||0))+(r[2]||"px"):t}function Ft(e,t,n,r,i){var s=n===(r?"border":"content")?4:"width"===t?1:0,o=0;for(;4>s;s+=2)"margin"===n&&(o+=x.css(e,n+jt[s],!0,i)),r?("content"===n&&(o-=x.css(e,"padding"+jt[s],!0,i)),"margin"!==n&&(o-=x.css(e,"border"+jt[s]+"Width",!0,i))):(o+=x.css(e,"padding"+jt[s],!0,i),"padding"!==n&&(o+=x.css(e,"border"+jt[s]+"Width",!0,i)));return o}function Pt(e,t,n){var r=!0,i="width"===t?e.offsetWidth:e.offsetHeight,s=qt(e),o=x.support.boxSizing&&"border-box"===x.css(e,"boxSizing",!1,s);if(0>=i||null==i){if(i=vt(e,t,s),(0>i||null==i)&&(i=e.style[t]),Ct.test(i))return i;r=o&&(x.support.boxSizingReliable||i===e.style[t]),i=parseFloat(i)||0}return i+Ft(e,t,n||(o?"border":"content"),r,s)+"px"}function Rt(e){var t=o,n=Nt[e];return n||(n=Mt(e,t),"none"!==n&&n||(xt=(xt||x("<iframe frameborder='0' width='0' height='0'/>").css("cssText","display:block !important")).appendTo(t.documentElement),t=(xt[0].contentWindow||xt[0].contentDocument).document,t.write("<!doctype html><html><body>"),t.close(),n=Mt(e,t),xt.detach()),Nt[e]=n),n}function Mt(e,t){var n=x(t.createElement(e)).appendTo(t.body),r=x.css(n[0],"display");return n.remove(),r}function _t(e,t,n,r){var i;if(x.isArray(t))x.each(t,function(t,i){n||$t.test(e)?r(e,i):_t(e+"["+("object"==typeof i?t:"")+"]",i,n,r)});else if(n||"object"!==x.type(t))r(e,t);else for(i in t)_t(e+"["+i+"]",t[i],n,r)}function un(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,i=0,s=t.toLowerCase().match(w)||[];if(x.isFunction(n))while(r=s[i++])"+"===r[0]?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}function ln(e,t,n,r){function o(u){var a;return i[u]=!0,x.each(e[u]||[],function(e,u){var f=u(t,n,r);return"string"!=typeof f||s||i[f]?s?!(a=f):undefined:(t.dataTypes.unshift(f),o(f),!1)}),a}var i={},s=e===on;return o(t.dataTypes[0])||!i["*"]&&o("*")}function cn(e,t){var n,r,i=x.ajaxSettings.flatOptions||{};for(n in t)t[n]!==undefined&&((i[n]?e:r||(r={}))[n]=t[n]);return r&&x.extend(!0,e,r),e}function pn(e,t,n){var r,i,s,o,u=e.contents,a=e.dataTypes;while("*"===a[0])a.shift(),r===undefined&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in u)if(u[i]&&u[i].test(r)){a.unshift(i);break}if(a[0]in n)s=a[0];else{for(i in n){if(!a[0]||e.converters[i+" "+a[0]]){s=i;break}o||(o=i)}s=s||o}return s?(s!==a[0]&&a.unshift(s),n[s]):undefined}function fn(e,t,n,r){var i,s,o,u,a,f={},l=e.dataTypes.slice();if(l[1])for(o in e.converters)f[o.toLowerCase()]=e.converters[o];s=l.shift();while(s)if(e.responseFields[s]&&(n[e.responseFields[s]]=t),!a&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),a=s,s=l.shift())if("*"===s)s=a;else if("*"!==a&&a!==s){if(o=f[a+" "+s]||f["* "+s],!o)for(i in f)if(u=i.split(" "),u[1]===s&&(o=f[a+" "+u[0]]||f["* "+u[0]])){o===!0?o=f[i]:f[i]!==!0&&(s=u[0],l.unshift(u[1]));break}if(o!==!0)if(o&&e["throws"])t=o(t);else try{t=o(t)}catch(c){return{state:"parsererror",error:o?c:"No conversion from "+a+" to "+s}}}return{state:"success",data:t}}function En(){return setTimeout(function(){xn=undefined}),xn=x.now()}function Sn(e,t,n){var r,i=(Nn[t]||[]).concat(Nn["*"]),s=0,o=i.length;for(;o>s;s++)if(r=i[s].call(n,t,e))return r}function jn(e,t,n){var r,i,s=0,o=kn.length,u=x.Deferred().always(function(){delete a.elem}),a=function(){if(i)return!1;var t=xn||En(),n=Math.max(0,f.startTime+f.duration-t),r=n/f.duration||0,s=1-r,o=0,a=f.tweens.length;for(;a>o;o++)f.tweens[o].run(s);return u.notifyWith(e,[f,s,n]),1>s&&a?n:(u.resolveWith(e,[f]),!1)},f=u.promise({elem:e,props:x.extend({},t),opts:x.extend(!0,{specialEasing:{}},n),originalProperties:t,originalOptions:n,startTime:xn||En(),duration:n.duration,tweens:[],createTween:function(t,n){var r=x.Tween(e,f.opts,t,n,f.opts.specialEasing[t]||f.opts.easing);return f.tweens.push(r),r},stop:function(t){var n=0,r=t?f.tweens.length:0;if(i)return this;for(i=!0;r>n;n++)f.tweens[n].run(1);return t?u.resolveWith(e,[f,t]):u.rejectWith(e,[f,t]),this}}),l=f.props;for(Dn(l,f.opts.specialEasing);o>s;s++)if(r=kn[s].call(f,e,l,f.opts))return r;return x.map(l,Sn,f),x.isFunction(f.opts.start)&&f.opts.start.call(e,f),x.fx.timer(x.extend(a,{elem:e,anim:f,queue:f.opts.queue})),f.progress(f.opts.progress).done(f.opts.done,f.opts.complete).fail(f.opts.fail).always(f.opts.always)}function Dn(e,t){var n,r,i,s,o;for(n in e)if(r=x.camelCase(n),i=t[r],s=e[n],x.isArray(s)&&(i=s[1],s=e[n]=s[0]),n!==r&&(e[r]=s,delete e[n]),o=x.cssHooks[r],o&&"expand"in o){s=o.expand(s),delete e[r];for(n in s)n in e||(e[n]=s[n],t[n]=i)}else t[r]=i}function An(e,t,n){var r,i,s,o,u,a,f=this,l={},c=e.style,h=e.nodeType&&Lt(e),p=q.get(e,"fxshow");n.queue||(u=x._queueHooks(e,"fx"),null==u.unqueued&&(u.unqueued=0,a=u.empty.fire,u.empty.fire=function(){u.unqueued||a()}),u.unqueued++,f.always(function(){f.always(function(){u.unqueued--,x.queue(e,"fx").length||u.empty.fire()})})),1===e.nodeType&&("height"in t||"width"in t)&&(n.overflow=[c.overflow,c.overflowX,c.overflowY],"inline"===x.css(e,"display")&&"none"===x.css(e,"float")&&(c.display="inline-block")),n.overflow&&(c.overflow="hidden",f.always(function(){c.overflow=n.overflow[0],c.overflowX=n.overflow[1],c.overflowY=n.overflow[2]}));for(r in t)if(i=t[r],wn.exec(i)){if(delete t[r],s=s||"toggle"===i,i===(h?"hide":"show")){if("show"!==i||!p||p[r]===undefined)continue;h=!0}l[r]=p&&p[r]||x.style(e,r)}if(!x.isEmptyObject(l)){p?"hidden"in p&&(h=p.hidden):p=q.access(e,"fxshow",{}),s&&(p.hidden=!h),h?x(e).show():f.done(function(){x(e).hide()}),f.done(function(){var t;q.remove(e,"fxshow");for(t in l)x.style(e,t,l[t])});for(r in l)o=Sn(h?p[r]:0,r,f),r in p||(p[r]=o.start,h&&(o.end=o.start,o.start="width"===r||"height"===r?1:0))}}function Ln(e,t,n,r,i){return new Ln.prototype.init(e,t,n,r,i)}function qn(e,t){var n,r={height:e},i=0;for(t=t?1:0;4>i;i+=2-t)n=jt[i],r["margin"+n]=r["padding"+n]=e;return t&&(r.opacity=r.width=e),r}function Hn(e){return x.isWindow(e)?e:9===e.nodeType&&e.defaultView}var t,n,r=typeof undefined,i=e.location,o=e.document,s=o.documentElement,a=e.jQuery,u=e.$,l={},c=[],p="2.0.3",f=c.concat,h=c.push,d=c.slice,g=c.indexOf,m=l.toString,y=l.hasOwnProperty,v=p.trim,x=function(e,n){return new x.fn.init(e,n,t)},b=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,w=/\S+/g,T=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,C=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,k=/^-ms-/,N=/-([\da-z])/gi,E=function(e,t){return t.toUpperCase()},S=function(){o.removeEventListener("DOMContentLoaded",S,!1),e.removeEventListener("load",S,!1),x.ready()};x.fn=x.prototype={jquery:p,constructor:x,init:function(e,t,n){var r,i;if(!e)return this;if("string"==typeof e){if(r="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:T.exec(e),!r||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof x?t[0]:t,x.merge(this,x.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:o,!0)),C.test(r[1])&&x.isPlainObject(t))for(r in t)x.isFunction(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return i=o.getElementById(r[2]),i&&i.parentNode&&(this.length=1,this[0]=i),this.context=o,this.selector=e,this}return e.nodeType?(this.context=this[0]=e,this.length=1,this):x.isFunction(e)?n.ready(e):(e.selector!==undefined&&(this.selector=e.selector,this.context=e.context),x.makeArray(e,this))},selector:"",length:0,toArray:function(){return d.call(this)},get:function(e){return null==e?this.toArray():0>e?this[this.length+e]:this[e]},pushStack:function(e){var t=x.merge(this.constructor(),e);return t.prevObject=this,t.context=this.context,t},each:function(e,t){return x.each(this,e,t)},ready:function(e){return x.ready.promise().done(e),this},slice:function(){return this.pushStack(d.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(0>e?t:0);return this.pushStack(n>=0&&t>n?[this[n]]:[])},map:function(e){return this.pushStack(x.map(this,function(t,n){return e.call(t,n,t)}))},end:function(){return this.prevObject||this.constructor(null)},push:h,sort:[].sort,splice:[].splice},x.fn.init.prototype=x.fn,x.extend=x.fn.extend=function(){var e,t,n,r,i,s,o=arguments[0]||{},u=1,a=arguments.length,f=!1;for("boolean"==typeof o&&(f=o,o=arguments[1]||{},u=2),"object"==typeof o||x.isFunction(o)||(o={}),a===u&&(o=this,--u);a>u;u++)if(null!=(e=arguments[u]))for(t in e)n=o[t],r=e[t],o!==r&&(f&&r&&(x.isPlainObject(r)||(i=x.isArray(r)))?(i?(i=!1,s=n&&x.isArray(n)?n:[]):s=n&&x.isPlainObject(n)?n:{},o[t]=x.extend(f,s,r)):r!==undefined&&(o[t]=r));return o},x.extend({expando:"jQuery"+(p+Math.random()).replace(/\D/g,""),noConflict:function(t){return e.$===x&&(e.$=u),t&&e.jQuery===x&&(e.jQuery=a),x},isReady:!1,readyWait:1,holdReady:function(e){e?x.readyWait++:x.ready(!0)},ready:function(e){(e===!0?--x.readyWait:x.isReady)||(x.isReady=!0,e!==!0&&--x.readyWait>0||(n.resolveWith(o,[x]),x.fn.trigger&&x(o).trigger("ready").off("ready")))},isFunction:function(e){return"function"===x.type(e)},isArray:Array.isArray,isWindow:function(e){return null!=e&&e===e.window},isNumeric:function(e){return!isNaN(parseFloat(e))&&isFinite(e)},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?l[m.call(e)]||"object":typeof e},isPlainObject:function(e){if("object"!==x.type(e)||e.nodeType||x.isWindow(e))return!1;try{if(e.constructor&&!y.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(t){return!1}return!0},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},error:function(e){throw Error(e)},parseHTML:function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||o;var r=C.exec(e),i=!n&&[];return r?[t.createElement(r[1])]:(r=x.buildFragment([e],t,i),i&&x(i).remove(),x.merge([],r.childNodes))},parseJSON:JSON.parse,parseXML:function(e){var t,n;if(!e||"string"!=typeof e)return null;try{n=new DOMParser,t=n.parseFromString(e,"text/xml")}catch(r){t=undefined}return(!t||t.getElementsByTagName("parsererror").length)&&x.error("Invalid XML: "+e),t},noop:function(){},globalEval:function(e){var t,n=eval;e=x.trim(e),e&&(1===e.indexOf("use strict")?(t=o.createElement("script"),t.text=e,o.head.appendChild(t).parentNode.removeChild(t)):n(e))},camelCase:function(e){return e.replace(k,"ms-").replace(N,E)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t,n){var r,i=0,s=e.length,o=j(e);if(n){if(o){for(;s>i;i++)if(r=t.apply(e[i],n),r===!1)break}else for(i in e)if(r=t.apply(e[i],n),r===!1)break}else if(o){for(;s>i;i++)if(r=t.call(e[i],i,e[i]),r===!1)break}else for(i in e)if(r=t.call(e[i],i,e[i]),r===!1)break;return e},trim:function(e){return null==e?"":v.call(e)},makeArray:function(e,t){var n=t||[];return null!=e&&(j(Object(e))?x.merge(n,"string"==typeof e?[e]:e):h.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:g.call(t,e,n)},merge:function(e,t){var n=t.length,r=e.length,i=0;if("number"==typeof n)for(;n>i;i++)e[r++]=t[i];else while(t[i]!==undefined)e[r++]=t[i++];return e.length=r,e},grep:function(e,t,n){var r,i=[],s=0,o=e.length;for(n=!!n;o>s;s++)r=!!t(e[s],s),n!==r&&i.push(e[s]);return i},map:function(e,t,n){var r,i=0,s=e.length,o=j(e),u=[];if(o)for(;s>i;i++)r=t(e[i],i,n),null!=r&&(u[u.length]=r);else for(i in e)r=t(e[i],i,n),null!=r&&(u[u.length]=r);return f.apply([],u)},guid:1,proxy:function(e,t){var n,r,i;return"string"==typeof t&&(n=e[t],t=e,e=n),x.isFunction(e)?(r=d.call(arguments,2),i=function(){return e.apply(t||this,r.concat(d.call(arguments)))},i.guid=e.guid=e.guid||x.guid++,i):undefined},access:function(e,t,n,r,i,s,o){var u=0,a=e.length,f=null==n;if("object"===x.type(n)){i=!0;for(u in n)x.access(e,t,u,n[u],!0,s,o)}else if(r!==undefined&&(i=!0,x.isFunction(r)||(o=!0),f&&(o?(t.call(e,r),t=null):(f=t,t=function(e,t,n){return f.call(x(e),n)})),t))for(;a>u;u++)t(e[u],n,o?r:r.call(e[u],u,t(e[u],n)));return i?e:f?t.call(e):a?t(e[0],n):s},now:Date.now,swap:function(e,t,n,r){var i,s,o={};for(s in t)o[s]=e.style[s],e.style[s]=t[s];i=n.apply(e,r||[]);for(s in t)e.style[s]=o[s];return i}}),x.ready.promise=function(t){return n||(n=x.Deferred(),"complete"===o.readyState?setTimeout(x.ready):(o.addEventListener("DOMContentLoaded",S,!1),e.addEventListener("load",S,!1))),n.promise(t)},x.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(e,t){l["[object "+t+"]"]=t.toLowerCase()}),t=x(o),function(e,t){function ot(e,t,n,i){var s,o,u,a,f,l,p,m,g,E;if((t?t.ownerDocument||t:w)!==h&&c(t),t=t||h,n=n||[],!e||"string"!=typeof e)return n;if(1!==(a=t.nodeType)&&9!==a)return[];if(d&&!i){if(s=Z.exec(e))if(u=s[1]){if(9===a){if(o=t.getElementById(u),!o||!o.parentNode)return n;if(o.id===u)return n.push(o),n}else if(t.ownerDocument&&(o=t.ownerDocument.getElementById(u))&&y(t,o)&&o.id===u)return n.push(o),n}else{if(s[2])return H.apply(n,t.getElementsByTagName(e)),n;if((u=s[3])&&r.getElementsByClassName&&t.getElementsByClassName)return H.apply(n,t.getElementsByClassName(u)),n}if(r.qsa&&(!v||!v.test(e))){if(m=p=b,g=t,E=9===a&&e,1===a&&"object"!==t.nodeName.toLowerCase()){l=mt(e),(p=t.getAttribute("id"))?m=p.replace(nt,"\\$&"):t.setAttribute("id",m),m="[id='"+m+"'] ",f=l.length;while(f--)l[f]=m+gt(l[f]);g=$.test(e)&&t.parentNode||t,E=l.join(",")}if(E)try{return H.apply(n,g.querySelectorAll(E)),n}catch(S){}finally{p||t.removeAttribute("id")}}}return Nt(e.replace(W,"$1"),t,n,i)}function ut(){function t(n,r){return e.push(n+=" ")>s.cacheLength&&delete t[e.shift()],t[n]=r}var e=[];return t}function at(e){return e[b]=!0,e}function ft(e){var t=h.createElement("div");try{return!!e(t)}catch(n){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function lt(e,t){var n=e.split("|"),r=e.length;while(r--)s.attrHandle[n[r]]=t}function ct(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||O)-(~e.sourceIndex||O);if(r)return r;if(n)while(n=n.nextSibling)if(n===t)return-1;return e?1:-1}function ht(e){return function(t){var n=t.nodeName.toLowerCase();return"input"===n&&t.type===e}}function pt(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function dt(e){return at(function(t){return t=+t,at(function(n,r){var i,s=e([],n.length,t),o=s.length;while(o--)n[i=s[o]]&&(n[i]=!(r[i]=n[i]))})})}function vt(){}function mt(e,t){var n,r,i,o,u,a,f,l=N[e+" "];if(l)return t?0:l.slice(0);u=e,a=[],f=s.preFilter;while(u){(!n||(r=X.exec(u)))&&(r&&(u=u.slice(r[0].length)||u),a.push(i=[])),n=!1,(r=V.exec(u))&&(n=r.shift(),i.push({value:n,type:r[0].replace(W," ")}),u=u.slice(n.length));for(o in s.filter)!(r=G[o].exec(u))||f[o]&&!(r=f[o](r))||(n=r.shift(),i.push({value:n,type:o,matches:r}),u=u.slice(n.length));if(!n)break}return t?u.length:u?ot.error(e):N(e,a).slice(0)}function gt(e){var t=0,n=e.length,r="";for(;n>t;t++)r+=e[t].value;return r}function yt(e,t,n){var r=t.dir,s=n&&"parentNode"===r,o=S++;return t.first?function(t,n,i){while(t=t[r])if(1===t.nodeType||s)return e(t,n,i)}:function(t,n,u){var a,f,l,c=E+" "+o;if(u){while(t=t[r])if((1===t.nodeType||s)&&e(t,n,u))return!0}else while(t=t[r])if(1===t.nodeType||s)if(l=t[b]||(t[b]={}),(f=l[r])&&f[0]===c){if((a=f[1])===!0||a===i)return a===!0}else if(f=l[r]=[c],f[1]=e(t,n,u)||i,f[1]===!0)return!0}}function bt(e){return e.length>1?function(t,n,r){var i=e.length;while(i--)if(!e[i](t,n,r))return!1;return!0}:e[0]}function wt(e,t,n,r,i){var s,o=[],u=0,a=e.length,f=null!=t;for(;a>u;u++)(s=e[u])&&(!n||n(s,r,i))&&(o.push(s),f&&t.push(u));return o}function Et(e,t,n,r,i,s){return r&&!r[b]&&(r=Et(r)),i&&!i[b]&&(i=Et(i,s)),at(function(s,o,u,a){var f,l,c,h=[],p=[],d=o.length,v=s||Tt(t||"*",u.nodeType?[u]:u,[]),m=!e||!s&&t?v:wt(v,h,e,u,a),g=n?i||(s?e:d||r)?[]:o:m;if(n&&n(m,g,u,a),r){f=wt(g,p),r(f,[],u,a),l=f.length;while(l--)(c=f[l])&&(g[p[l]]=!(m[p[l]]=c))}if(s){if(i||e){if(i){f=[],l=g.length;while(l--)(c=g[l])&&f.push(m[l]=c);i(null,g=[],f,a)}l=g.length;while(l--)(c=g[l])&&(f=i?j.call(s,c):h[l])>-1&&(s[f]=!(o[f]=c))}}else g=wt(g===o?g.splice(d,g.length):g),i?i(null,o,g,a):H.apply(o,g)})}function St(e){var t,n,r,i=e.length,o=s.relative[e[0].type],u=o||s.relative[" "],a=o?1:0,l=yt(function(e){return e===t},u,!0),c=yt(function(e){return j.call(t,e)>-1},u,!0),h=[function(e,n,r){return!o&&(r||n!==f)||((t=n).nodeType?l(e,n,r):c(e,n,r))}];for(;i>a;a++)if(n=s.relative[e[a].type])h=[yt(bt(h),n)];else{if(n=s.filter[e[a].type].apply(null,e[a].matches),n[b]){for(r=++a;i>r;r++)if(s.relative[e[r].type])break;return Et(a>1&&bt(h),a>1&&gt(e.slice(0,a-1).concat({value:" "===e[a-2].type?"*":""})).replace(W,"$1"),n,r>a&&St(e.slice(a,r)),i>r&&St(e=e.slice(r)),i>r&&gt(e))}h.push(n)}return bt(h)}function xt(e,t){var n=0,r=t.length>0,o=e.length>0,u=function(u,a,l,c,p){var d,v,m,g=[],y=0,b="0",w=u&&[],S=null!=p,x=f,T=u||o&&s.find.TAG("*",p&&a.parentNode||a),N=E+=null==x?1:Math.random()||.1;for(S&&(f=a!==h&&a,i=n);null!=(d=T[b]);b++){if(o&&d){v=0;while(m=e[v++])if(m(d,a,l)){c.push(d);break}S&&(E=N,i=++n)}r&&((d=!m&&d)&&y--,u&&w.push(d))}if(y+=b,r&&b!==y){v=0;while(m=t[v++])m(w,g,a,l);if(u){if(y>0)while(b--)w[b]||g[b]||(g[b]=D.call(c));g=wt(g)}H.apply(c,g),S&&!u&&g.length>0&&y+t.length>1&&ot.uniqueSort(c)}return S&&(E=N,f=x),w};return r?at(u):u}function Tt(e,t,n){var r=0,i=t.length;for(;i>r;r++)ot(e,t[r],n);return n}function Nt(e,t,n,i){var o,u,f,l,c,h=mt(e);if(!i&&1===h.length){if(u=h[0]=h[0].slice(0),u.length>2&&"ID"===(f=u[0]).type&&r.getById&&9===t.nodeType&&d&&s.relative[u[1].type]){if(t=(s.find.ID(f.matches[0].replace(rt,it),t)||[])[0],!t)return n;e=e.slice(u.shift().value.length)}o=G.needsContext.test(e)?0:u.length;while(o--){if(f=u[o],s.relative[l=f.type])break;if((c=s.find[l])&&(i=c(f.matches[0].replace(rt,it),$.test(u[0].type)&&t.parentNode||t))){if(u.splice(o,1),e=i.length&&gt(u),!e)return H.apply(n,i),n;break}}}return a(e,h)(i,t,!d,n,$.test(e)),n}var n,r,i,s,o,u,a,f,l,c,h,p,d,v,m,g,y,b="sizzle"+ -(new Date),w=e.document,E=0,S=0,T=ut(),N=ut(),C=ut(),k=!1,L=function(e,t){return e===t?(k=!0,0):0},A=typeof t,O=1<<31,M={}.hasOwnProperty,_=[],D=_.pop,P=_.push,H=_.push,B=_.slice,j=_.indexOf||function(e){var t=0,n=this.length;for(;n>t;t++)if(this[t]===e)return t;return-1},F="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",I="[\\x20\\t\\r\\n\\f]",q="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",R=q.replace("w","w#"),U="\\["+I+"*("+q+")"+I+"*(?:([*^$|!~]?=)"+I+"*(?:(['\"])((?:\\\\.|[^\\\\])*?)\\3|("+R+")|)|)"+I+"*\\]",z=":("+q+")(?:\\(((['\"])((?:\\\\.|[^\\\\])*?)\\3|((?:\\\\.|[^\\\\()[\\]]|"+U.replace(3,8)+")*)|.*)\\)|)",W=RegExp("^"+I+"+|((?:^|[^\\\\])(?:\\\\.)*)"+I+"+$","g"),X=RegExp("^"+I+"*,"+I+"*"),V=RegExp("^"+I+"*([>+~]|"+I+")"+I+"*"),$=RegExp(I+"*[+~]"),J=RegExp("="+I+"*([^\\]'\"]*)"+I+"*\\]","g"),K=RegExp(z),Q=RegExp("^"+R+"$"),G={ID:RegExp("^#("+q+")"),CLASS:RegExp("^\\.("+q+")"),TAG:RegExp("^("+q.replace("w","w*")+")"),ATTR:RegExp("^"+U),PSEUDO:RegExp("^"+z),CHILD:RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+I+"*(even|odd|(([+-]|)(\\d*)n|)"+I+"*(?:([+-]|)"+I+"*(\\d+)|))"+I+"*\\)|)","i"),bool:RegExp("^(?:"+F+")$","i"),needsContext:RegExp("^"+I+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+I+"*((?:-\\d)?\\d*)"+I+"*\\)|)(?=[^-]|$)","i")},Y=/^[^{]+\{\s*\[native \w/,Z=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,et=/^(?:input|select|textarea|button)$/i,tt=/^h\d$/i,nt=/'|\\/g,rt=RegExp("\\\\([\\da-f]{1,6}"+I+"?|("+I+")|.)","ig"),it=function(e,t,n){var r="0x"+t-65536;return r!==r||n?t:0>r?String.fromCharCode(r+65536):String.fromCharCode(55296|r>>10,56320|1023&r)};try{H.apply(_=B.call(w.childNodes),w.childNodes),_[w.childNodes.length].nodeType}catch(st){H={apply:_.length?function(e,t){P.apply(e,B.call(t))}:function(e,t){var n=e.length,r=0;while(e[n++]=t[r++]);e.length=n-1}}}u=ot.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return t?"HTML"!==t.nodeName:!1},r=ot.support={},c=ot.setDocument=function(e){var n=e?e.ownerDocument||e:w,i=n.defaultView;return n!==h&&9===n.nodeType&&n.documentElement?(h=n,p=n.documentElement,d=!u(n),i&&i.attachEvent&&i!==i.top&&i.attachEvent("onbeforeunload",function(){c()}),r.attributes=ft(function(e){return e.className="i",!e.getAttribute("className")}),r.getElementsByTagName=ft(function(e){return e.appendChild(n.createComment("")),!e.getElementsByTagName("*").length}),r.getElementsByClassName=ft(function(e){return e.innerHTML="<div class='a'></div><div class='a i'></div>",e.firstChild.className="i",2===e.getElementsByClassName("i").length}),r.getById=ft(function(e){return p.appendChild(e).id=b,!n.getElementsByName||!n.getElementsByName(b).length}),r.getById?(s.find.ID=function(e,t){if(typeof t.getElementById!==A&&d){var n=t.getElementById(e);return n&&n.parentNode?[n]:[]}},s.filter.ID=function(e){var t=e.replace(rt,it);return function(e){return e.getAttribute("id")===t}}):(delete s.find.ID,s.filter.ID=function(e){var t=e.replace(rt,it);return function(e){var n=typeof e.getAttributeNode!==A&&e.getAttributeNode("id");return n&&n.value===t}}),s.find.TAG=r.getElementsByTagName?function(e,n){return typeof n.getElementsByTagName!==A?n.getElementsByTagName(e):t}:function(e,t){var n,r=[],i=0,s=t.getElementsByTagName(e);if("*"===e){while(n=s[i++])1===n.nodeType&&r.push(n);return r}return s},s.find.CLASS=r.getElementsByClassName&&function(e,n){return typeof n.getElementsByClassName!==A&&d?n.getElementsByClassName(e):t},m=[],v=[],(r.qsa=Y.test(n.querySelectorAll))&&(ft(function(e){e.innerHTML="<select><option selected=''></option></select>",e.querySelectorAll("[selected]").length||v.push("\\["+I+"*(?:value|"+F+")"),e.querySelectorAll(":checked").length||v.push(":checked")}),ft(function(e){var t=n.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("t",""),e.querySelectorAll("[t^='']").length&&v.push("[*^$]="+I+"*(?:''|\"\")"),e.querySelectorAll(":enabled").length||v.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),v.push(",.*:")})),(r.matchesSelector=Y.test(g=p.webkitMatchesSelector||p.mozMatchesSelector||p.oMatchesSelector||p.msMatchesSelector))&&ft(function(e){r.disconnectedMatch=g.call(e,"div"),g.call(e,"[s!='']:x"),m.push("!=",z)}),v=v.length&&RegExp(v.join("|")),m=m.length&&RegExp(m.join("|")),y=Y.test(p.contains)||p.compareDocumentPosition?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!!r&&1===r.nodeType&&!!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r))}:function(e,t){if(t)while(t=t.parentNode)if(t===e)return!0;return!1},L=p.compareDocumentPosition?function(e,t){if(e===t)return k=!0,0;var i=t.compareDocumentPosition&&e.compareDocumentPosition&&e.compareDocumentPosition(t);return i?1&i||!r.sortDetached&&t.compareDocumentPosition(e)===i?e===n||y(w,e)?-1:t===n||y(w,t)?1:l?j.call(l,e)-j.call(l,t):0:4&i?-1:1:e.compareDocumentPosition?-1:1}:function(e,t){var r,i=0,s=e.parentNode,o=t.parentNode,u=[e],a=[t];if(e===t)return k=!0,0;if(!s||!o)return e===n?-1:t===n?1:s?-1:o?1:l?j.call(l,e)-j.call(l,t):0;if(s===o)return ct(e,t);r=e;while(r=r.parentNode)u.unshift(r);r=t;while(r=r.parentNode)a.unshift(r);while(u[i]===a[i])i++;return i?ct(u[i],a[i]):u[i]===w?-1:a[i]===w?1:0},n):h},ot.matches=function(e,t){return ot(e,null,null,t)},ot.matchesSelector=function(e,t){if((e.ownerDocument||e)!==h&&c(e),t=t.replace(J,"='$1']"),!(!r.matchesSelector||!d||m&&m.test(t)||v&&v.test(t)))try{var n=g.call(e,t);if(n||r.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(i){}return ot(t,h,null,[e]).length>0},ot.contains=function(e,t){return(e.ownerDocument||e)!==h&&c(e),y(e,t)},ot.attr=function(e,n){(e.ownerDocument||e)!==h&&c(e);var i=s.attrHandle[n.toLowerCase()],o=i&&M.call(s.attrHandle,n.toLowerCase())?i(e,n,!d):t;return o===t?r.attributes||!d?e.getAttribute(n):(o=e.getAttributeNode(n))&&o.specified?o.value:null:o},ot.error=function(e){throw Error("Syntax error, unrecognized expression: "+e)},ot.uniqueSort=function(e){var t,n=[],i=0,s=0;if(k=!r.detectDuplicates,l=!r.sortStable&&e.slice(0),e.sort(L),k){while(t=e[s++])t===e[s]&&(i=n.push(s));while(i--)e.splice(n[i],1)}return e},o=ot.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===i||4===i)return e.nodeValue}else for(;t=e[r];r++)n+=o(t);return n},s=ot.selectors={cacheLength:50,createPseudo:at,match:G,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(rt,it),e[3]=(e[4]||e[5]||"").replace(rt,it),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||ot.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&ot.error(e[0]),e},PSEUDO:function(e){var n,r=!e[5]&&e[2];return G.CHILD.test(e[0])?null:(e[3]&&e[4]!==t?e[2]=e[4]:r&&K.test(r)&&(n=mt(r,!0))&&(n=r.indexOf(")",r.length-n)-r.length)&&(e[0]=e[0].slice(0,n),e[2]=r.slice(0,n)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(rt,it).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=T[e+" "];return t||(t=RegExp("(^|"+I+")"+e+"("+I+"|$)"))&&T(e,function(e){return t.test("string"==typeof e.className&&e.className||typeof e.getAttribute!==A&&e.getAttribute("class")||"")})},ATTR:function(e,t,n){return function(r){var i=ot.attr(r,e);return null==i?"!="===t:t?(i+="","="===t?i===n:"!="===t?i!==n:"^="===t?n&&0===i.indexOf(n):"*="===t?n&&i.indexOf(n)>-1:"$="===t?n&&i.slice(-n.length)===n:"~="===t?(" "+i+" ").indexOf(n)>-1:"|="===t?i===n||i.slice(0,n.length+1)===n+"-":!1):!0}},CHILD:function(e,t,n,r,i){var s="nth"!==e.slice(0,3),o="last"!==e.slice(-4),u="of-type"===t;return 1===r&&0===i?function(e){return!!e.parentNode}:function(t,n,a){var f,l,c,h,p,d,v=s!==o?"nextSibling":"previousSibling",m=t.parentNode,g=u&&t.nodeName.toLowerCase(),y=!a&&!u;if(m){if(s){while(v){c=t;while(c=c[v])if(u?c.nodeName.toLowerCase()===g:1===c.nodeType)return!1;d=v="only"===e&&!d&&"nextSibling"}return!0}if(d=[o?m.firstChild:m.lastChild],o&&y){l=m[b]||(m[b]={}),f=l[e]||[],p=f[0]===E&&f[1],h=f[0]===E&&f[2],c=p&&m.childNodes[p];while(c=++p&&c&&c[v]||(h=p=0)||d.pop())if(1===c.nodeType&&++h&&c===t){l[e]=[E,p,h];break}}else if(y&&(f=(t[b]||(t[b]={}))[e])&&f[0]===E)h=f[1];else while(c=++p&&c&&c[v]||(h=p=0)||d.pop())if((u?c.nodeName.toLowerCase()===g:1===c.nodeType)&&++h&&(y&&((c[b]||(c[b]={}))[e]=[E,h]),c===t))break;return h-=i,h===r||0===h%r&&h/r>=0}}},PSEUDO:function(e,t){var n,r=s.pseudos[e]||s.setFilters[e.toLowerCase()]||ot.error("unsupported pseudo: "+e);return r[b]?r(t):r.length>1?(n=[e,e,"",t],s.setFilters.hasOwnProperty(e.toLowerCase())?at(function(e,n){var i,s=r(e,t),o=s.length;while(o--)i=j.call(e,s[o]),e[i]=!(n[i]=s[o])}):function(e){return r(e,0,n)}):r}},pseudos:{not:at(function(e){var t=[],n=[],r=a(e.replace(W,"$1"));return r[b]?at(function(e,t,n,i){var s,o=r(e,null,i,[]),u=e.length;while(u--)(s=o[u])&&(e[u]=!(t[u]=s))}):function(e,i,s){return t[0]=e,r(t,null,s,n),!n.pop()}}),has:at(function(e){return function(t){return ot(e,t).length>0}}),contains:at(function(e){return function(t){return(t.textContent||t.innerText||o(t)).indexOf(e)>-1}}),lang:at(function(e){return Q.test(e||"")||ot.error("unsupported lang: "+e),e=e.replace(rt,it).toLowerCase(),function(t){var n;do if(n=d?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return n=n.toLowerCase(),n===e||0===n.indexOf(e+"-");while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===p},focus:function(e){return e===h.activeElement&&(!h.hasFocus||h.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return e.disabled===!1},disabled:function(e){return e.disabled===!0},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,e.selected===!0},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeName>"@"||3===e.nodeType||4===e.nodeType)return!1;return!0},parent:function(e){return!s.pseudos.empty(e)},header:function(e){return tt.test(e.nodeName)},input:function(e){return et.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||t.toLowerCase()===e.type)},first:dt(function(){return[0]}),last:dt(function(e,t){return[t-1]}),eq:dt(function(e,t,n){return[0>n?n+t:n]}),even:dt(function(e,t){var n=0;for(;t>n;n+=2)e.push(n);return e}),odd:dt(function(e,t){var n=1;for(;t>n;n+=2)e.push(n);return e}),lt:dt(function(e,t,n){var r=0>n?n+t:n;for(;--r>=0;)e.push(r);return e}),gt:dt(function(e,t,n){var r=0>n?n+t:n;for(;t>++r;)e.push(r);return e})}},s.pseudos.nth=s.pseudos.eq;for(n in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})s.pseudos[n]=ht(n);for(n in{submit:!0,reset:!0})s.pseudos[n]=pt(n);vt.prototype=s.filters=s.pseudos,s.setFilters=new vt,a=ot.compile=function(e,t){var n,r=[],i=[],s=C[e+" "];if(!s){t||(t=mt(e)),n=t.length;while(n--)s=St(t[n]),s[b]?r.push(s):i.push(s);s=C(e,xt(i,r))}return s},r.sortStable=b.split("").sort(L).join("")===b,r.detectDuplicates=k,c(),r.sortDetached=ft(function(e){return 1&e.compareDocumentPosition(h.createElement("div"))}),ft(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||lt("type|href|height|width",function(e,n,r){return r?t:e.getAttribute(n,"type"===n.toLowerCase()?1:2)}),r.attributes&&ft(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||lt("value",function(e,n,r){return r||"input"!==e.nodeName.toLowerCase()?t:e.defaultValue}),ft(function(e){return null==e.getAttribute("disabled")})||lt(F,function(e,n,r){var i;return r?t:(i=e.getAttributeNode(n))&&i.specified?i.value:e[n]===!0?n.toLowerCase():null}),x.find=ot,x.expr=ot.selectors,x.expr[":"]=x.expr.pseudos,x.unique=ot.uniqueSort,x.text=ot.getText,x.isXMLDoc=ot.isXML,x.contains=ot.contains}(e);var D={};x.Callbacks=function(e){e="string"==typeof e?D[e]||A(e):x.extend({},e);var t,n,r,i,s,o,u=[],a=!e.once&&[],f=function(h){for(t=e.memory&&h,n=!0,o=i||0,i=0,s=u.length,r=!0;u&&s>o;o++)if(u[o].apply(h[0],h[1])===!1&&e.stopOnFalse){t=!1;break}r=!1,u&&(a?a.length&&f(a.shift()):t?u=[]:l.disable())},l={add:function(){if(u){var n=u.length;(function o(t){x.each(t,function(t,n){var r=x.type(n);"function"===r?e.unique&&l.has(n)||u.push(n):n&&n.length&&"string"!==r&&o(n)})})(arguments),r?s=u.length:t&&(i=n,f(t))}return this},remove:function(){return u&&x.each(arguments,function(e,t){var n;while((n=x.inArray(t,u,n))>-1)u.splice(n,1),r&&(s>=n&&s--,o>=n&&o--)}),this},has:function(e){return e?x.inArray(e,u)>-1:!!u&&!!u.length},empty:function(){return u=[],s=0,this},disable:function(){return u=a=t=undefined,this},disabled:function(){return!u},lock:function(){return a=undefined,t||l.disable(),this},locked:function(){return!a},fireWith:function(e,t){return!u||n&&!a||(t=t||[],t=[e,t.slice?t.slice():t],r?a.push(t):f(t)),this},fire:function(){return l.fireWith(this,arguments),this},fired:function(){return!!n}};return l},x.extend({Deferred:function(e){var t=[["resolve","done",x.Callbacks("once memory"),"resolved"],["reject","fail",x.Callbacks("once memory"),"rejected"],["notify","progress",x.Callbacks("memory")]],n="pending",r={state:function(){return n},always:function(){return i.done(arguments).fail(arguments),this},then:function(){var e=arguments;return x.Deferred(function(n){x.each(t,function(t,s){var o=s[0],u=x.isFunction(e[t])&&e[t];i[s[1]](function(){var e=u&&u.apply(this,arguments);e&&x.isFunction(e.promise)?e.promise().done(n.resolve).fail(n.reject).progress(n.notify):n[o+"With"](this===r?n.promise():this,u?[e]:arguments)})}),e=null}).promise()},promise:function(e){return null!=e?x.extend(e,r):r}},i={};return r.pipe=r.then,x.each(t,function(e,s){var o=s[2],u=s[3];r[s[1]]=o.add,u&&o.add(function(){n=u},t[1^e][2].disable,t[2][2].lock),i[s[0]]=function(){return i[s[0]+"With"](this===i?r:this,arguments),this},i[s[0]+"With"]=o.fireWith}),r.promise(i),e&&e.call(i,i),i},when:function(e){var t=0,n=d.call(arguments),r=n.length,i=1!==r||e&&x.isFunction(e.promise)?r:0,s=1===i?e:x.Deferred(),o=function(e,t,n){return function(r){t[e]=this,n[e]=arguments.length>1?d.call(arguments):r,n===u?s.notifyWith(t,n):--i||s.resolveWith(t,n)}},u,a,f;if(r>1)for(u=Array(r),a=Array(r),f=Array(r);r>t;t++)n[t]&&x.isFunction(n[t].promise)?n[t].promise().done(o(t,f,n)).fail(s.reject).progress(o(t,a,u)):--i;return i||s.resolveWith(f,n),s.promise()}}),x.support=function(t){var n=o.createElement("input"),r=o.createDocumentFragment(),i=o.createElement("div"),s=o.createElement("select"),u=s.appendChild(o.createElement("option"));return n.type?(n.type="checkbox",t.checkOn=""!==n.value,t.optSelected=u.selected,t.reliableMarginRight=!0,t.boxSizingReliable=!0,t.pixelPosition=!1,n.checked=!0,t.noCloneChecked=n.cloneNode(!0).checked,s.disabled=!0,t.optDisabled=!u.disabled,n=o.createElement("input"),n.value="t",n.type="radio",t.radioValue="t"===n.value,n.setAttribute("checked","t"),n.setAttribute("name","t"),r.appendChild(n),t.checkClone=r.cloneNode(!0).cloneNode(!0).lastChild.checked,t.focusinBubbles="onfocusin"in e,i.style.backgroundClip="content-box",i.cloneNode(!0).style.backgroundClip="",t.clearCloneStyle="content-box"===i.style.backgroundClip,x(function(){var n,r,s="padding:0;margin:0;border:0;display:block;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box",u=o.getElementsByTagName("body")[0];u&&(n=o.createElement("div"),n.style.cssText="border:0;width:0;height:0;position:absolute;top:0;left:-9999px;margin-top:1px",u.appendChild(n).appendChild(i),i.innerHTML="",i.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;padding:1px;border:1px;display:block;width:4px;margin-top:1%;position:absolute;top:1%",x.swap(u,null!=u.style.zoom?{zoom:1}:{},function(){t.boxSizing=4===i.offsetWidth}),e.getComputedStyle&&(t.pixelPosition="1%"!==(e.getComputedStyle(i,null)||{}).top,t.boxSizingReliable="4px"===(e.getComputedStyle(i,null)||{width:"4px"}).width,r=i.appendChild(o.createElement("div")),r.style.cssText=i.style.cssText=s,r.style.marginRight=r.style.width="0",i.style.width="1px",t.reliableMarginRight=!parseFloat((e.getComputedStyle(r,null)||{}).marginRight)),u.removeChild(n))}),t):t}({});var L,q,H=/(?:\{[\s\S]*\}|\[[\s\S]*\])$/,O=/([A-Z])/g;F.uid=1,F.accepts=function(e){return e.nodeType?1===e.nodeType||9===e.nodeType:!0},F.prototype={key:function(e){if(!F.accepts(e))return 0;var t={},n=e[this.expando];if(!n){n=F.uid++;try{t[this.expando]={value:n},Object.defineProperties(e,t)}catch(r){t[this.expando]=n,x.extend(e,t)}}return this.cache[n]||(this.cache[n]={}),n},set:function(e,t,n){var r,i=this.key(e),s=this.cache[i];if("string"==typeof t)s[t]=n;else if(x.isEmptyObject(s))x.extend(this.cache[i],t);else for(r in t)s[r]=t[r];return s},get:function(e,t){var n=this.cache[this.key(e)];return t===undefined?n:n[t]},access:function(e,t,n){var r;return t===undefined||t&&"string"==typeof t&&n===undefined?(r=this.get(e,t),r!==undefined?r:this.get(e,x.camelCase(t))):(this.set(e,t,n),n!==undefined?n:t)},remove:function(e,t){var n,r,i,s=this.key(e),o=this.cache[s];if(t===undefined)this.cache[s]={};else{x.isArray(t)?r=t.concat(t.map(x.camelCase)):(i=x.camelCase(t),t in o?r=[t,i]:(r=i,r=r in o?[r]:r.match(w)||[])),n=r.length;while(n--)delete o[r[n]]}},hasData:function(e){return!x.isEmptyObject(this.cache[e[this.expando]]||{})},discard:function(e){e[this.expando]&&delete this.cache[e[this.expando]]}},L=new F,q=new F,x.extend({acceptData:F.accepts,hasData:function(e){return L.hasData(e)||q.hasData(e)},data:function(e,t,n){return L.access(e,t,n)},removeData:function(e,t){L.remove(e,t)},_data:function(e,t,n){return q.access(e,t,n)},_removeData:function(e,t){q.remove(e,t)}}),x.fn.extend({data:function(e,t){var n,r,i=this[0],s=0,o=null;if(e===undefined){if(this.length&&(o=L.get(i),1===i.nodeType&&!q.get(i,"hasDataAttrs"))){for(n=i.attributes;n.length>s;s++)r=n[s].name,0===r.indexOf("data-")&&(r=x.camelCase(r.slice(5)),P(i,r,o[r]));q.set(i,"hasDataAttrs",!0)}return o}return"object"==typeof e?this.each(function(){L.set(this,e)}):x.access(this,function(t){var n,r=x.camelCase(e);if(i&&t===undefined){if(n=L.get(i,e),n!==undefined)return n;if(n=L.get(i,r),n!==undefined)return n;if(n=P(i,r,undefined),n!==undefined)return n}else this.each(function(){var n=L.get(this,r);L.set(this,r,t),-1!==e.indexOf("-")&&n!==undefined&&L.set(this,e,t)})},null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each(function(){L.remove(this,e)})}}),x.extend({queue:function(e,t,n){var r;return e?(t=(t||"fx")+"queue",r=q.get(e,t),n&&(!r||x.isArray(n)?r=q.access(e,t,x.makeArray(n)):r.push(n)),r||[]):undefined},dequeue:function(e,t){t=t||"fx";var n=x.queue(e,t),r=n.length,i=n.shift(),s=x._queueHooks(e,t),o=function(){x.dequeue(e,t)};"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete s.stop,i.call(e,o,s)),!r&&s&&s.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return q.get(e,n)||q.access(e,n,{empty:x.Callbacks("once memory").add(function(){q.remove(e,[t+"queue",n])})})}}),x.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),n>arguments.length?x.queue(this[0],e):t===undefined?this:this.each(function(){var n=x.queue(this,e,t);x._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&x.dequeue(this,e)})},dequeue:function(e){return this.each(function(){x.dequeue(this,e)})},delay:function(e,t){return e=x.fx?x.fx.speeds[e]||e:e,t=t||"fx",this.queue(t,function(t,n){var r=setTimeout(t,e);n.stop=function(){clearTimeout(r)}})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=x.Deferred(),s=this,o=this.length,u=function(){--r||i.resolveWith(s,[s])};"string"!=typeof e&&(t=e,e=undefined),e=e||"fx";while(o--)n=q.get(s[o],e+"queueHooks"),n&&n.empty&&(r++,n.empty.add(u));return u(),i.promise(t)}});var R,M,W=/[\t\r\n\f]/g,$=/\r/g,B=/^(?:input|select|textarea|button)$/i;x.fn.extend({attr:function(e,t){return x.access(this,x.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){x.removeAttr(this,e)})},prop:function(e,t){return x.access(this,x.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[x.propFix[e]||e]})},addClass:function(e){var t,n,r,i,s,o=0,u=this.length,a="string"==typeof e&&e;if(x.isFunction(e))return this.each(function(t){x(this).addClass(e.call(this,t,this.className))});if(a)for(t=(e||"").match(w)||[];u>o;o++)if(n=this[o],r=1===n.nodeType&&(n.className?(" "+n.className+" ").replace(W," "):" ")){s=0;while(i=t[s++])0>r.indexOf(" "+i+" ")&&(r+=i+" ");n.className=x.trim(r)}return this},removeClass:function(e){var t,n,r,i,s,o=0,u=this.length,a=0===arguments.length||"string"==typeof e&&e;if(x.isFunction(e))return this.each(function(t){x(this).removeClass(e.call(this,t,this.className))});if(a)for(t=(e||"").match(w)||[];u>o;o++)if(n=this[o],r=1===n.nodeType&&(n.className?(" "+n.className+" ").replace(W," "):"")){s=0;while(i=t[s++])while(r.indexOf(" "+i+" ")>=0)r=r.replace(" "+i+" "," ");n.className=e?x.trim(r):""}return this},toggleClass:function(e,t){var n=typeof e;return"boolean"==typeof t&&"string"===n?t?this.addClass(e):this.removeClass(e):x.isFunction(e)?this.each(function(n){x(this).toggleClass(e.call(this,n,this.className,t),t)}):this.each(function(){if("string"===n){var t,i=0,s=x(this),o=e.match(w)||[];while(t=o[i++])s.hasClass(t)?s.removeClass(t):s.addClass(t)}else(n===r||"boolean"===n)&&(this.className&&q.set(this,"__className__",this.className),this.className=this.className||e===!1?"":q.get(this,"__className__")||"")})},hasClass:function(e){var t=" "+e+" ",n=0,r=this.length;for(;r>n;n++)if(1===this[n].nodeType&&(" "+this[n].className+" ").replace(W," ").indexOf(t)>=0)return!0;return!1},val:function(e){var t,n,r,i=this[0];if(arguments.length)return r=x.isFunction(e),this.each(function(n){var i;1===this.nodeType&&(i=r?e.call(this,n,x(this).val()):e,null==i?i="":"number"==typeof i?i+="":x.isArray(i)&&(i=x.map(i,function(e){return null==e?"":e+""})),t=x.valHooks[this.type]||x.valHooks[this.nodeName.toLowerCase()],t&&"set"in t&&t.set(this,i,"value")!==undefined||(this.value=i))});if(i)return t=x.valHooks[i.type]||x.valHooks[i.nodeName.toLowerCase()],t&&"get"in t&&(n=t.get(i,"value"))!==undefined?n:(n=i.value,"string"==typeof n?n.replace($,""):null==n?"":n)}}),x.extend({valHooks:{option:{get:function(e){var t=e.attributes.value;return!t||t.specified?e.value:e.text}},select:{get:function(e){var t,n,r=e.options,i=e.selectedIndex,s="select-one"===e.type||0>i,o=s?null:[],u=s?i+1:r.length,a=0>i?u:s?i:0;for(;u>a;a++)if(n=r[a],!(!n.selected&&a!==i||(x.support.optDisabled?n.disabled:null!==n.getAttribute("disabled"))||n.parentNode.disabled&&x.nodeName(n.parentNode,"optgroup"))){if(t=x(n).val(),s)return t;o.push(t)}return o},set:function(e,t){var n,r,i=e.options,s=x.makeArray(t),o=i.length;while(o--)r=i[o],(r.selected=x.inArray(x(r).val(),s)>=0)&&(n=!0);return n||(e.selectedIndex=-1),s}}},attr:function(e,t,n){var i,s,o=e.nodeType;if(e&&3!==o&&8!==o&&2!==o)return typeof e.getAttribute===r?x.prop(e,t,n):(1===o&&x.isXMLDoc(e)||(t=t.toLowerCase(),i=x.attrHooks[t]||(x.expr.match.bool.test(t)?M:R)),n===undefined?i&&"get"in i&&null!==(s=i.get(e,t))?s:(s=x.find.attr(e,t),null==s?undefined:s):null!==n?i&&"set"in i&&(s=i.set(e,n,t))!==undefined?s:(e.setAttribute(t,n+""),n):(x.removeAttr(e,t),undefined))},removeAttr:function(e,t){var n,r,i=0,s=t&&t.match(w);if(s&&1===e.nodeType)while(n=s[i++])r=x.propFix[n]||n,x.expr.match.bool.test(n)&&(e[r]=!1),e.removeAttribute(n)},attrHooks:{type:{set:function(e,t){if(!x.support.radioValue&&"radio"===t&&x.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},propFix:{"for":"htmlFor","class":"className"},prop:function(e,t,n){var r,i,s,o=e.nodeType;if(e&&3!==o&&8!==o&&2!==o)return s=1!==o||!x.isXMLDoc(e),s&&(t=x.propFix[t]||t,i=x.propHooks[t]),n!==undefined?i&&"set"in i&&(r=i.set(e,n,t))!==undefined?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){return e.hasAttribute("tabindex")||B.test(e.nodeName)||e.href?e.tabIndex:-1}}}}),M={set:function(e,t,n){return t===!1?x.removeAttr(e,n):e.setAttribute(n,n),n}},x.each(x.expr.match.bool.source.match(/\w+/g),function(e,t){var n=x.expr.attrHandle[t]||x.find.attr;x.expr.attrHandle[t]=function(e,t,r){var i=x.expr.attrHandle[t],s=r?undefined:(x.expr.attrHandle[t]=undefined)!=n(e,t,r)?t.toLowerCase():null;return x.expr.attrHandle[t]=i,s}}),x.support.optSelected||(x.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null}}),x.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){x.propFix[this.toLowerCase()]=this}),x.each(["radio","checkbox"],function(){x.valHooks[this]={set:function(e,t){return x.isArray(t)?e.checked=x.inArray(x(e).val(),t)>=0:undefined}},x.support.checkOn||(x.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var I=/^key/,z=/^(?:mouse|contextmenu)|click/,_=/^(?:focusinfocus|focusoutblur)$/,X=/^([^.]*)(?:\.(.+)|)$/;x.event={global:{},add:function(e,t,n,i,s){var o,u,a,f,l,c,h,p,d,v,m,g=q.get(e);if(g){n.handler&&(o=n,n=o.handler,s=o.selector),n.guid||(n.guid=x.guid++),(f=g.events)||(f=g.events={}),(u=g.handle)||(u=g.handle=function(e){return typeof x===r||e&&x.event.triggered===e.type?undefined:x.event.dispatch.apply(u.elem,arguments)},u.elem=e),t=(t||"").match(w)||[""],l=t.length;while(l--)a=X.exec(t[l])||[],d=m=a[1],v=(a[2]||"").split(".").sort(),d&&(h=x.event.special[d]||{},d=(s?h.delegateType:h.bindType)||d,h=x.event.special[d]||{},c=x.extend({type:d,origType:m,data:i,handler:n,guid:n.guid,selector:s,needsContext:s&&x.expr.match.needsContext.test(s),namespace:v.join(".")},o),(p=f[d])||(p=f[d]=[],p.delegateCount=0,h.setup&&h.setup.call(e,i,v,u)!==!1||e.addEventListener&&e.addEventListener(d,u,!1)),h.add&&(h.add.call(e,c),c.handler.guid||(c.handler.guid=n.guid)),s?p.splice(p.delegateCount++,0,c):p.push(c),x.event.global[d]=!0);e=null}},remove:function(e,t,n,r,i){var s,o,u,a,f,l,c,h,p,d,v,m=q.hasData(e)&&q.get(e);if(m&&(a=m.events)){t=(t||"").match(w)||[""],f=t.length;while(f--)if(u=X.exec(t[f])||[],p=v=u[1],d=(u[2]||"").split(".").sort(),p){c=x.event.special[p]||{},p=(r?c.delegateType:c.bindType)||p,h=a[p]||[],u=u[2]&&RegExp("(^|\\.)"+d.join("\\.(?:.*\\.|)")+"(\\.|$)"),o=s=h.length;while(s--)l=h[s],!i&&v!==l.origType||n&&n.guid!==l.guid||u&&!u.test(l.namespace)||r&&r!==l.selector&&("**"!==r||!l.selector)||(h.splice(s,1),l.selector&&h.delegateCount--,c.remove&&c.remove.call(e,l));o&&!h.length&&(c.teardown&&c.teardown.call(e,d,m.handle)!==!1||x.removeEvent(e,p,m.handle),delete a[p])}else for(p in a)x.event.remove(e,p+t[f],n,r,!0);x.isEmptyObject(a)&&(delete m.handle,q.remove(e,"events"))}},trigger:function(t,n,r,i){var s,u,a,f,l,c,h,p=[r||o],d=y.call(t,"type")?t.type:t,v=y.call(t,"namespace")?t.namespace.split("."):[];if(u=a=r=r||o,3!==r.nodeType&&8!==r.nodeType&&!_.test(d+x.event.triggered)&&(d.indexOf(".")>=0&&(v=d.split("."),d=v.shift(),v.sort()),l=0>d.indexOf(":")&&"on"+d,t=t[x.expando]?t:new x.Event(d,"object"==typeof t&&t),t.isTrigger=i?2:3,t.namespace=v.join("."),t.namespace_re=t.namespace?RegExp("(^|\\.)"+v.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=undefined,t.target||(t.target=r),n=null==n?[t]:x.makeArray(n,[t]),h=x.event.special[d]||{},i||!h.trigger||h.trigger.apply(r,n)!==!1)){if(!i&&!h.noBubble&&!x.isWindow(r)){for(f=h.delegateType||d,_.test(f+d)||(u=u.parentNode);u;u=u.parentNode)p.push(u),a=u;a===(r.ownerDocument||o)&&p.push(a.defaultView||a.parentWindow||e)}s=0;while((u=p[s++])&&!t.isPropagationStopped())t.type=s>1?f:h.bindType||d,c=(q.get(u,"events")||{})[t.type]&&q.get(u,"handle"),c&&c.apply(u,n),c=l&&u[l],c&&x.acceptData(u)&&c.apply&&c.apply(u,n)===!1&&t.preventDefault();return t.type=d,i||t.isDefaultPrevented()||h._default&&h._default.apply(p.pop(),n)!==!1||!x.acceptData(r)||l&&x.isFunction(r[d])&&!x.isWindow(r)&&(a=r[l],a&&(r[l]=null),x.event.triggered=d,r[d](),x.event.triggered=undefined,a&&(r[l]=a)),t.result}},dispatch:function(e){e=x.event.fix(e);var t,n,r,i,s,o=[],u=d.call(arguments),a=(q.get(this,"events")||{})[e.type]||[],f=x.event.special[e.type]||{};if(u[0]=e,e.delegateTarget=this,!f.preDispatch||f.preDispatch.call(this,e)!==!1){o=x.event.handlers.call(this,e,a),t=0;while((i=o[t++])&&!e.isPropagationStopped()){e.currentTarget=i.elem,n=0;while((s=i.handlers[n++])&&!e.isImmediatePropagationStopped())(!e.namespace_re||e.namespace_re.test(s.namespace))&&(e.handleObj=s,e.data=s.data,r=((x.event.special[s.origType]||{}).handle||s.handler).apply(i.elem,u),r!==undefined&&(e.result=r)===!1&&(e.preventDefault(),e.stopPropagation()))}return f.postDispatch&&f.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,r,i,s,o=[],u=t.delegateCount,a=e.target;if(u&&a.nodeType&&(!e.button||"click"!==e.type))for(;a!==this;a=a.parentNode||this)if(a.disabled!==!0||"click"!==e.type){for(r=[],n=0;u>n;n++)s=t[n],i=s.selector+" ",r[i]===undefined&&(r[i]=s.needsContext?x(i,this).index(a)>=0:x.find(i,this,null,[a]).length),r[i]&&r.push(s);r.length&&o.push({elem:a,handlers:r})}return t.length>u&&o.push({elem:this,handlers:t.slice(u)}),o},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,r,i,s=t.button;return null==e.pageX&&null!=t.clientX&&(n=e.target.ownerDocument||o,r=n.documentElement,i=n.body,e.pageX=t.clientX+(r&&r.scrollLeft||i&&i.scrollLeft||0)-(r&&r.clientLeft||i&&i.clientLeft||0),e.pageY=t.clientY+(r&&r.scrollTop||i&&i.scrollTop||0)-(r&&r.clientTop||i&&i.clientTop||0)),e.which||s===undefined||(e.which=1&s?1:2&s?3:4&s?2:0),e}},fix:function(e){if(e[x.expando])return e;var t,n,r,i=e.type,s=e,u=this.fixHooks[i];u||(this.fixHooks[i]=u=z.test(i)?this.mouseHooks:I.test(i)?this.keyHooks:{}),r=u.props?this.props.concat(u.props):this.props,e=new x.Event(s),t=r.length;while(t--)n=r[t],e[n]=s[n];return e.target||(e.target=o),3===e.target.nodeType&&(e.target=e.target.parentNode),u.filter?u.filter(e,s):e},special:{load:{noBubble:!0},focus:{trigger:function(){return this!==V()&&this.focus?(this.focus(),!1):undefined},delegateType:"focusin"},blur:{trigger:function(){return this===V()&&this.blur?(this.blur(),!1):undefined},delegateType:"focusout"},click:{trigger:function(){return"checkbox"===this.type&&this.click&&x.nodeName(this,"input")?(this.click(),!1):undefined},_default:function(e){return x.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){e.result!==undefined&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,n,r){var i=x.extend(new x.Event,n,{type:e,isSimulated:!0,originalEvent:{}});r?x.event.trigger(i,null,t):x.event.dispatch.call(t,i),i.isDefaultPrevented()&&n.preventDefault()}},x.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n,!1)},x.Event=function(e,t){return this instanceof x.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||e.getPreventDefault&&e.getPreventDefault()?U:Y):this.type=e,t&&x.extend(this,t),this.timeStamp=e&&e.timeStamp||x.now(),this[x.expando]=!0,undefined):new x.Event(e,t)},x.Event.prototype={isDefaultPrevented:Y,isPropagationStopped:Y,isImmediatePropagationStopped:Y,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=U,e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=U,e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=U,this.stopPropagation()}},x.each({mouseenter:"mouseover",mouseleave:"mouseout"},function(e,t){x.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=this,i=e.relatedTarget,s=e.handleObj;return(!i||i!==r&&!x.contains(r,i))&&(e.type=s.origType,n=s.handler.apply(this,arguments),e.type=t),n}}}),x.support.focusinBubbles||x.each({focus:"focusin",blur:"focusout"},function(e,t){var n=0,r=function(e){x.event.simulate(t,e.target,x.event.fix(e),!0)};x.event.special[t]={setup:function(){0===n++&&o.addEventListener(e,r,!0)},teardown:function(){0===--n&&o.removeEventListener(e,r,!0)}}}),x.fn.extend({on:function(e,t,n,r,i){var s,o;if("object"==typeof e){"string"!=typeof t&&(n=n||t,t=undefined);for(o in e)this.on(o,t,n,e[o],i);return this}if(null==n&&null==r?(r=t,n=t=undefined):null==r&&("string"==typeof t?(r=n,n=undefined):(r=n,n=t,t=undefined)),r===!1)r=Y;else if(!r)return this;return 1===i&&(s=r,r=function(e){return x().off(e),s.apply(this,arguments)},r.guid=s.guid||(s.guid=x.guid++)),this.each(function(){x.event.add(this,e,r,n,t)})},one:function(e,t,n,r){return this.on(e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,x(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(i in e)this.off(i,t,e[i]);return this}return(t===!1||"function"==typeof t)&&(n=t,t=undefined),n===!1&&(n=Y),this.each(function(){x.event.remove(this,e,n,t)})},trigger:function(e,t){return this.each(function(){x.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];return n?x.event.trigger(e,t,n,!0):undefined}});var G=/^.[^:#\[\.,]*$/,J=/^(?:parents|prev(?:Until|All))/,Q=x.expr.match.needsContext,K={children:!0,contents:!0,next:!0,prev:!0};x.fn.extend({find:function(e){var t,n=[],r=this,i=r.length;if("string"!=typeof e)return this.pushStack(x(e).filter(function(){for(t=0;i>t;t++)if(x.contains(r[t],this))return!0}));for(t=0;i>t;t++)x.find(e,r[t],n);return n=this.pushStack(i>1?x.unique(n):n),n.selector=this.selector?this.selector+" "+e:e,n},has:function(e){var t=x(e,this),n=t.length;return this.filter(function(){var e=0;for(;n>e;e++)if(x.contains(this,t[e]))return!0})},not:function(e){return this.pushStack(et(this,e||[],!0))},filter:function(e){return this.pushStack(et(this,e||[],!1))},is:function(e){return!!et(this,"string"==typeof e&&Q.test(e)?x(e):e||[],!1).length},closest:function(e,t){var n,r=0,i=this.length,s=[],o=Q.test(e)||"string"!=typeof e?x(e,t||this.context):0;for(;i>r;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(11>n.nodeType&&(o?o.index(n)>-1:1===n.nodeType&&x.find.matchesSelector(n,e))){n=s.push(n);break}return this.pushStack(s.length>1?x.unique(s):s)},index:function(e){return e?"string"==typeof e?g.call(x(e),this[0]):g.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){var n="string"==typeof e?x(e,t):x.makeArray(e&&e.nodeType?[e]:e),r=x.merge(this.get(),n);return this.pushStack(x.unique(r))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),x.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return x.dir(e,"parentNode")},parentsUntil:function(e,t,n){return x.dir(e,"parentNode",n)},next:function(e){return Z(e,"nextSibling")},prev:function(e){return Z(e,"previousSibling")},nextAll:function(e){return x.dir(e,"nextSibling")},prevAll:function(e){return x.dir(e,"previousSibling")},nextUntil:function(e,t,n){return x.dir(e,"nextSibling",n)},prevUntil:function(e,t,n){return x.dir(e,"previousSibling",n)},siblings:function(e){return x.sibling((e.parentNode||{}).firstChild,e)},children:function(e){return x.sibling(e.firstChild)},contents:function(e){return e.contentDocument||x.merge([],e.childNodes)}},function(e,t){x.fn[e]=function(n,r){var i=x.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=x.filter(r,i)),this.length>1&&(K[e]||x.unique(i),J.test(e)&&i.reverse()),this.pushStack(i)}}),x.extend({filter:function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?x.find.matchesSelector(r,e)?[r]:[]:x.find.matches(e,x.grep(t,function(e){return 1===e.nodeType}))},dir:function(e,t,n){var r=[],i=n!==undefined;while((e=e[t])&&9!==e.nodeType)if(1===e.nodeType){if(i&&x(e).is(n))break;r.push(e)}return r},sibling:function(e,t){var n=[];for(;e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}});var tt=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,nt=/<([\w:]+)/,rt=/<|&#?\w+;/,it=/<(?:script|style|link)/i,ot=/^(?:checkbox|radio)$/i,st=/checked\s*(?:[^=]|=\s*.checked.)/i,at=/^$|\/(?:java|ecma)script/i,ut=/^true\/(.*)/,lt=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,ct={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};ct.optgroup=ct.option,ct.tbody=ct.tfoot=ct.colgroup=ct.caption=ct.thead,ct.th=ct.td,x.fn.extend({text:function(e){return x.access(this,function(e){return e===undefined?x.text(this):this.empty().append((this[0]&&this[0].ownerDocument||o).createTextNode(e))},null,e,arguments.length)},append:function(){return this.domManip(arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=pt(this,e);t.appendChild(e)}})},prepend:function(){return this.domManip(arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=pt(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},remove:function(e,t){var n,r=e?x.filter(e,this):this,i=0;for(;null!=(n=r[i]);i++)t||1!==n.nodeType||x.cleanData(mt(n)),n.parentNode&&(t&&x.contains(n.ownerDocument,n)&&dt(mt(n,"script")),n.parentNode.removeChild(n));return this},empty:function(){var e,t=0;for(;null!=(e=this[t]);t++)1===e.nodeType&&(x.cleanData(mt(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null==e?!1:e,t=null==t?e:t,this.map(function(){return x.clone(this,e,t)})},html:function(e){return x.access(this,function(e){var t=this[0]||{},n=0,r=this.length;if(e===undefined&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!it.test(e)&&!ct[(nt.exec(e)||["",""])[1].toLowerCase()]){e=e.replace(tt,"<$1></$2>");try{for(;r>n;n++)t=this[n]||{},1===t.nodeType&&(x.cleanData(mt(t,!1)),t.innerHTML=e);t=0}catch(i){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=x.map(this,function(e){return[e.nextSibling,e.parentNode]}),t=0;return this.domManip(arguments,function(n){var r=e[t++],i=e[t++];i&&(r&&r.parentNode!==i&&(r=this.nextSibling),x(this).remove(),i.insertBefore(n,r))},!0),t?this:this.remove()},detach:function(e){return this.remove(e,!0)},domManip:function(e,t,n){e=f.apply([],e);var r,i,s,o,u,a,l=0,c=this.length,h=this,p=c-1,d=e[0],v=x.isFunction(d);if(v||!(1>=c||"string"!=typeof d||x.support.checkClone)&&st.test(d))return this.each(function(r){var i=h.eq(r);v&&(e[0]=d.call(this,r,i.html())),i.domManip(e,t,n)});if(c&&(r=x.buildFragment(e,this[0].ownerDocument,!1,!n&&this),i=r.firstChild,1===r.childNodes.length&&(r=i),i)){for(s=x.map(mt(r,"script"),ft),o=s.length;c>l;l++)u=r,l!==p&&(u=x.clone(u,!0,!0),o&&x.merge(s,mt(u,"script"))),t.call(this[l],u,l);if(o)for(a=s[s.length-1].ownerDocument,x.map(s,ht),l=0;o>l;l++)u=s[l],at.test(u.type||"")&&!q.access(u,"globalEval")&&x.contains(a,u)&&(u.src?x._evalUrl(u.src):x.globalEval(u.textContent.replace(lt,"")))}return this}}),x.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){x.fn[e]=function(e){var n,r=[],i=x(e),s=i.length-1,o=0;for(;s>=o;o++)n=o===s?this:this.clone(!0),x(i[o])[t](n),h.apply(r,n.get());return this.pushStack(r)}}),x.extend({clone:function(e,t,n){var r,i,s,o,u=e.cloneNode(!0),a=x.contains(e.ownerDocument,e);if(!(x.support.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||x.isXMLDoc(e)))for(o=mt(u),s=mt(e),r=0,i=s.length;i>r;r++)yt(s[r],o[r]);if(t)if(n)for(s=s||mt(e),o=o||mt(u),r=0,i=s.length;i>r;r++)gt(s[r],o[r]);else gt(e,u);return o=mt(u,"script"),o.length>0&&dt(o,!a&&mt(e,"script")),u},buildFragment:function(e,t,n,r){var i,s,o,u,a,f,l=0,c=e.length,h=t.createDocumentFragment(),p=[];for(;c>l;l++)if(i=e[l],i||0===i)if("object"===x.type(i))x.merge(p,i.nodeType?[i]:i);else if(rt.test(i)){s=s||h.appendChild(t.createElement("div")),o=(nt.exec(i)||["",""])[1].toLowerCase(),u=ct[o]||ct._default,s.innerHTML=u[1]+i.replace(tt,"<$1></$2>")+u[2],f=u[0];while(f--)s=s.lastChild;x.merge(p,s.childNodes),s=h.firstChild,s.textContent=""}else p.push(t.createTextNode(i));h.textContent="",l=0;while(i=p[l++])if((!r||-1===x.inArray(i,r))&&(a=x.contains(i.ownerDocument,i),s=mt(h.appendChild(i),"script"),a&&dt(s),n)){f=0;while(i=s[f++])at.test(i.type||"")&&n.push(i)}return h},cleanData:function(e){var t,n,r,i,s,o,u=x.event.special,a=0;for(;(n=e[a])!==undefined;a++){if(F.accepts(n)&&(s=n[q.expando],s&&(t=q.cache[s]))){if(r=Object.keys(t.events||{}),r.length)for(o=0;(i=r[o])!==undefined;o++)u[i]?x.event.remove(n,i):x.removeEvent(n,i,t.handle);q.cache[s]&&delete q.cache[s]}delete L.cache[n[L.expando]]}},_evalUrl:function(e){return x.ajax({url:e,type:"GET",dataType:"script",async:!1,global:!1,"throws":!0})}}),x.fn.extend({wrapAll:function(e){var t;return x.isFunction(e)?this.each(function(t){x(this).wrapAll(e.call(this,t))}):(this[0]&&(t=x(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){var e=this;while(e.firstElementChild)e=e.firstElementChild;return e}).append(this)),this)},wrapInner:function(e){return x.isFunction(e)?this.each(function(t){x(this).wrapInner(e.call(this,t))}):this.each(function(){var t=x(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=x.isFunction(e);return this.each(function(n){x(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(){return this.parent().each(function(){x.nodeName(this,"body")||x(this).replaceWith(this.childNodes)}).end()}});var vt,xt,bt=/^(none|table(?!-c[ea]).+)/,wt=/^margin/,Tt=RegExp("^("+b+")(.*)$","i"),Ct=RegExp("^("+b+")(?!px)[a-z%]+$","i"),kt=RegExp("^([+-])=("+b+")","i"),Nt={BODY:"block"},Et={position:"absolute",visibility:"hidden",display:"block"},St={letterSpacing:0,fontWeight:400},jt=["Top","Right","Bottom","Left"],Dt=["Webkit","O","Moz","ms"];x.fn.extend({css:function(e,t){return x.access(this,function(e,t,n){var r,i,s={},o=0;if(x.isArray(t)){for(r=qt(e),i=t.length;i>o;o++)s[t[o]]=x.css(e,t[o],!1,r);return s}return n!==undefined?x.style(e,t,n):x.css(e,t)},e,t,arguments.length>1)},show:function(){return Ht(this,!0)},hide:function(){return Ht(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){Lt(this)?x(this).show():x(this).hide()})}}),x.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=vt(e,"opacity");return""===n?"1":n}}}},cssNumber:{columnCount:!0,fillOpacity:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{"float":"cssFloat"},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,s,o,u=x.camelCase(t),a=e.style;return t=x.cssProps[u]||(x.cssProps[u]=At(a,u)),o=x.cssHooks[t]||x.cssHooks[u],n===undefined?o&&"get"in o&&(i=o.get(e,!1,r))!==undefined?i:a[t]:(s=typeof n,"string"===s&&(i=kt.exec(n))&&(n=(i[1]+1)*i[2]+parseFloat(x.css(e,t)),s="number"),null==n||"number"===s&&isNaN(n)||("number"!==s||x.cssNumber[u]||(n+="px"),x.support.clearCloneStyle||""!==n||0!==t.indexOf("background")||(a[t]="inherit"),o&&"set"in o&&(n=o.set(e,n,r))===undefined||(a[t]=n)),undefined)}},css:function(e,t,n,r){var i,s,o,u=x.camelCase(t);return t=x.cssProps[u]||(x.cssProps[u]=At(e.style,u)),o=x.cssHooks[t]||x.cssHooks[u],o&&"get"in o&&(i=o.get(e,!0,n)),i===undefined&&(i=vt(e,t,r)),"normal"===i&&t in St&&(i=St[t]),""===n||n?(s=parseFloat(i),n===!0||x.isNumeric(s)?s||0:i):i}}),vt=function(e,t,n){var r,i,s,o=n||qt(e),u=o?o.getPropertyValue(t)||o[t]:undefined,a=e.style;return o&&(""!==u||x.contains(e.ownerDocument,e)||(u=x.style(e,t)),Ct.test(u)&&wt.test(t)&&(r=a.width,i=a.minWidth,s=a.maxWidth,a.minWidth=a.maxWidth=a.width=u,u=o.width,a.width=r,a.minWidth=i,a.maxWidth=s)),u},x.each(["height","width"],function(e,t){x.cssHooks[t]={get:function(e,n,r){return n?0===e.offsetWidth&&bt.test(x.css(e,"display"))?x.swap(e,Et,function(){return Pt(e,t,r)}):Pt(e,t,r):undefined},set:function(e,n,r){var i=r&&qt(e);return Ot(e,n,r?Ft(e,t,r,x.support.boxSizing&&"border-box"===x.css(e,"boxSizing",!1,i),i):0)}}}),x(function(){x.support.reliableMarginRight||(x.cssHooks.marginRight={get:function(e,t){return t?x.swap(e,{display:"inline-block"},vt,[e,"marginRight"]):undefined}}),!x.support.pixelPosition&&x.fn.position&&x.each(["top","left"],function(e,t){x.cssHooks[t]={get:function(e,n){return n?(n=vt(e,t),Ct.test(n)?x(e).position()[t]+"px":n):undefined}}})}),x.expr&&x.expr.filters&&(x.expr.filters.hidden=function(e){return 0>=e.offsetWidth&&0>=e.offsetHeight},x.expr.filters.visible=function(e){return!x.expr.filters.hidden(e)}),x.each({margin:"",padding:"",border:"Width"},function(e,t){x.cssHooks[e+t]={expand:function(n){var r=0,i={},s="string"==typeof n?n.split(" "):[n];for(;4>r;r++)i[e+jt[r]+t]=s[r]||s[r-2]||s[0];return i}},wt.test(e)||(x.cssHooks[e+t].set=Ot)});var Wt=/%20/g,$t=/\[\]$/,Bt=/\r?\n/g,It=/^(?:submit|button|image|reset|file)$/i,zt=/^(?:input|select|textarea|keygen)/i;x.fn.extend({serialize:function(){return x.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=x.prop(this,"elements");return e?x.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!x(this).is(":disabled")&&zt.test(this.nodeName)&&!It.test(e)&&(this.checked||!ot.test(e))}).map(function(e,t){var n=x(this).val();return null==n?null:x.isArray(n)?x.map(n,function(e){return{name:t.name,value:e.replace(Bt,"\r\n")}}):{name:t.name,value:n.replace(Bt,"\r\n")}}).get()}}),x.param=function(e,t){var n,r=[],i=function(e,t){t=x.isFunction(t)?t():null==t?"":t,r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(t===undefined&&(t=x.ajaxSettings&&x.ajaxSettings.traditional),x.isArray(e)||e.jquery&&!x.isPlainObject(e))x.each(e,function(){i(this.name,this.value)});else for(n in e)_t(n,e[n],t,i);return r.join("&").replace(Wt,"+")},x.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,t){x.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}),x.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)},bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}});var Xt,Ut,Yt=x.now(),Vt=/\?/,Gt=/#.*$/,Jt=/([?&])_=[^&]*/,Qt=/^(.*?):[ \t]*([^\r\n]*)$/gm,Kt=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Zt=/^(?:GET|HEAD)$/,en=/^\/\//,tn=/^([\w.+-]+:)(?:\/\/([^\/?#:]*)(?::(\d+)|)|)/,nn=x.fn.load,rn={},on={},sn="*/".concat("*");try{Ut=i.href}catch(an){Ut=o.createElement("a"),Ut.href="",Ut=Ut.href}Xt=tn.exec(Ut.toLowerCase())||[],x.fn.load=function(e,t,n){if("string"!=typeof e&&nn)return nn.apply(this,arguments);var r,i,s,o=this,u=e.indexOf(" ");return u>=0&&(r=e.slice(u),e=e.slice(0,u)),x.isFunction(t)?(n=t,t=undefined):t&&"object"==typeof t&&(i="POST"),o.length>0&&x.ajax({url:e,type:i,dataType:"html",data:t}).done(function(e){s=arguments,o.html(r?x("<div>").append(x.parseHTML(e)).find(r):e)}).complete(n&&function(e,t){o.each(n,s||[e.responseText,t,e])}),this},x.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){x.fn[t]=function(e){return this.on(t,e)}}),x.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ut,type:"GET",isLocal:Kt.test(Xt[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":sn,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":x.parseJSON,"text xml":x.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?cn(cn(e,x.ajaxSettings),t):cn(x.ajaxSettings,e)},ajaxPrefilter:un(rn),ajaxTransport:un(on),ajax:function(e,t){function T(e,t,s,u){var f,m,g,b,w,S=t;2!==y&&(y=2,o&&clearTimeout(o),n=undefined,i=u||"",E.readyState=e>0?4:0,f=e>=200&&300>e||304===e,s&&(b=pn(l,E,s)),b=fn(l,b,E,f),f?(l.ifModified&&(w=E.getResponseHeader("Last-Modified"),w&&(x.lastModified[r]=w),w=E.getResponseHeader("etag"),w&&(x.etag[r]=w)),204===e||"HEAD"===l.type?S="nocontent":304===e?S="notmodified":(S=b.state,m=b.data,g=b.error,f=!g)):(g=S,(e||!S)&&(S="error",0>e&&(e=0))),E.status=e,E.statusText=(t||S)+"",f?p.resolveWith(c,[m,S,E]):p.rejectWith(c,[E,S,g]),E.statusCode(v),v=undefined,a&&h.trigger(f?"ajaxSuccess":"ajaxError",[E,l,f?m:g]),d.fireWith(c,[E,S]),a&&(h.trigger("ajaxComplete",[E,l]),--x.active||x.event.trigger("ajaxStop")))}"object"==typeof e&&(t=e,e=undefined),t=t||{};var n,r,i,s,o,u,a,f,l=x.ajaxSetup({},t),c=l.context||l,h=l.context&&(c.nodeType||c.jquery)?x(c):x.event,p=x.Deferred(),d=x.Callbacks("once memory"),v=l.statusCode||{},m={},g={},y=0,b="canceled",E={readyState:0,getResponseHeader:function(e){var t;if(2===y){if(!s){s={};while(t=Qt.exec(i))s[t[1].toLowerCase()]=t[2]}t=s[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===y?i:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return y||(e=g[n]=g[n]||e,m[e]=t),this},overrideMimeType:function(e){return y||(l.mimeType=e),this},statusCode:function(e){var t;if(e)if(2>y)for(t in e)v[t]=[v[t],e[t]];else E.always(e[E.status]);return this},abort:function(e){var t=e||b;return n&&n.abort(t),T(0,t),this}};if(p.promise(E).complete=d.add,E.success=E.done,E.error=E.fail,l.url=((e||l.url||Ut)+"").replace(Gt,"").replace(en,Xt[1]+"//"),l.type=t.method||t.type||l.method||l.type,l.dataTypes=x.trim(l.dataType||"*").toLowerCase().match(w)||[""],null==l.crossDomain&&(u=tn.exec(l.url.toLowerCase()),l.crossDomain=!(!u||u[1]===Xt[1]&&u[2]===Xt[2]&&(u[3]||("http:"===u[1]?"80":"443"))===(Xt[3]||("http:"===Xt[1]?"80":"443")))),l.data&&l.processData&&"string"!=typeof l.data&&(l.data=x.param(l.data,l.traditional)),ln(rn,l,t,E),2===y)return E;a=l.global,a&&0===x.active++&&x.event.trigger("ajaxStart"),l.type=l.type.toUpperCase(),l.hasContent=!Zt.test(l.type),r=l.url,l.hasContent||(l.data&&(r=l.url+=(Vt.test(r)?"&":"?")+l.data,delete l.data),l.cache===!1&&(l.url=Jt.test(r)?r.replace(Jt,"$1_="+Yt++):r+(Vt.test(r)?"&":"?")+"_="+Yt++)),l.ifModified&&(x.lastModified[r]&&E.setRequestHeader("If-Modified-Since",x.lastModified[r]),x.etag[r]&&E.setRequestHeader("If-None-Match",x.etag[r])),(l.data&&l.hasContent&&l.contentType!==!1||t.contentType)&&E.setRequestHeader("Content-Type",l.contentType),E.setRequestHeader("Accept",l.dataTypes[0]&&l.accepts[l.dataTypes[0]]?l.accepts[l.dataTypes[0]]+("*"!==l.dataTypes[0]?", "+sn+"; q=0.01":""):l.accepts["*"]);for(f in l.headers)E.setRequestHeader(f,l.headers[f]);if(!l.beforeSend||l.beforeSend.call(c,E,l)!==!1&&2!==y){b="abort";for(f in{success:1,error:1,complete:1})E[f](l[f]);if(n=ln(on,l,t,E)){E.readyState=1,a&&h.trigger("ajaxSend",[E,l]),l.async&&l.timeout>0&&(o=setTimeout(function(){E.abort("timeout")},l.timeout));try{y=1,n.send(m,T)}catch(S){if(!(2>y))throw S;T(-1,S)}}else T(-1,"No Transport");return E}return E.abort()},getJSON:function(e,t,n){return x.get(e,t,n,"json")},getScript:function(e,t){return x.get(e,undefined,t,"script")}}),x.each(["get","post"],function(e,t){x[t]=function(e,n,r,i){return x.isFunction(n)&&(i=i||r,r=n,n=undefined),x.ajax({url:e,type:t,dataType:i,data:n,success:r})}}),x.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(e){return x.globalEval(e),e}}}),x.ajaxPrefilter("script",function(e){e.cache===undefined&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),x.ajaxTransport("script",function(e){if(e.crossDomain){var t,n;return{send:function(r,i){t=x("<script>").prop({async:!0,charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&i("error"===e.type?404:200,e.type)}),o.head.appendChild(t[0])},abort:function(){n&&n()}}}});var hn=[],dn=/(=)\?(?=&|$)|\?\?/;x.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=hn.pop()||x.expando+"_"+Yt++;return this[e]=!0,e}}),x.ajaxPrefilter("json jsonp",function(t,n,r){var i,s,o,u=t.jsonp!==!1&&(dn.test(t.url)?"url":"string"==typeof t.data&&!(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&dn.test(t.data)&&"data");return u||"jsonp"===t.dataTypes[0]?(i=t.jsonpCallback=x.isFunction(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,u?t[u]=t[u].replace(dn,"$1"+i):t.jsonp!==!1&&(t.url+=(Vt.test(t.url)?"&":"?")+t.jsonp+"="+i),t.converters["script json"]=function(){return o||x.error(i+" was not called"),o[0]},t.dataTypes[0]="json",s=e[i],e[i]=function(){o=arguments},r.always(function(){e[i]=s,t[i]&&(t.jsonpCallback=n.jsonpCallback,hn.push(i)),o&&x.isFunction(s)&&s(o[0]),o=s=undefined}),"script"):undefined}),x.ajaxSettings.xhr=function(){try{return new XMLHttpRequest}catch(e){}};var gn=x.ajaxSettings.xhr(),mn={0:200,1223:204},yn=0,vn={};e.ActiveXObject&&x(e).on("unload",function(){for(var e in vn)vn[e]();vn=undefined}),x.support.cors=!!gn&&"withCredentials"in gn,x.support.ajax=gn=!!gn,x.ajaxTransport(function(e){var t;return x.support.cors||gn&&!e.crossDomain?{send:function(n,r){var i,s,o=e.xhr();if(o.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(i in e.xhrFields)o[i]=e.xhrFields[i];e.mimeType&&o.overrideMimeType&&o.overrideMimeType(e.mimeType),e.crossDomain||n["X-Requested-With"]||(n["X-Requested-With"]="XMLHttpRequest");for(i in n)o.setRequestHeader(i,n[i]);t=function(e){return function(){t&&(delete vn[s],t=o.onload=o.onerror=null,"abort"===e?o.abort():"error"===e?r(o.status||404,o.statusText):r(mn[o.status]||o.status,o.statusText,"string"==typeof o.responseText?{text:o.responseText}:undefined,o.getAllResponseHeaders()))}},o.onload=t(),o.onerror=t("error"),t=vn[s=yn++]=t("abort"),o.send(e.hasContent&&e.data||null)},abort:function(){t&&t()}}:undefined});var xn,bn,wn=/^(?:toggle|show|hide)$/,Tn=RegExp("^(?:([+-])=|)("+b+")([a-z%]*)$","i"),Cn=/queueHooks$/,kn=[An],Nn={"*":[function(e,t){var n=this.createTween(e,t),r=n.cur(),i=Tn.exec(t),s=i&&i[3]||(x.cssNumber[e]?"":"px"),o=(x.cssNumber[e]||"px"!==s&&+r)&&Tn.exec(x.css(n.elem,e)),u=1,a=20;if(o&&o[3]!==s){s=s||o[3],i=i||[],o=+r||1;do u=u||".5",o/=u,x.style(n.elem,e,o+s);while(u!==(u=n.cur()/r)&&1!==u&&--a)}return i&&(o=n.start=+o||+r||0,n.unit=s,n.end=i[1]?o+(i[1]+1)*i[2]:+i[2]),n}]};x.Animation=x.extend(jn,{tweener:function(e,t){x.isFunction(e)?(t=e,e=["*"]):e=e.split(" ");var n,r=0,i=e.length;for(;i>r;r++)n=e[r],Nn[n]=Nn[n]||[],Nn[n].unshift(t)},prefilter:function(e,t){t?kn.unshift(e):kn.push(e)}}),x.Tween=Ln,Ln.prototype={constructor:Ln,init:function(e,t,n,r,i,s){this.elem=e,this.prop=n,this.easing=i||"swing",this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=s||(x.cssNumber[n]?"":"px")},cur:function(){var e=Ln.propHooks[this.prop];return e&&e.get?e.get(this):Ln.propHooks._default.get(this)},run:function(e){var t,n=Ln.propHooks[this.prop];return this.pos=t=this.options.duration?x.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):Ln.propHooks._default.set(this),this}},Ln.prototype.init.prototype=Ln.prototype,Ln.propHooks={_default:{get:function(e){var t;return null==e.elem[e.prop]||e.elem.style&&null!=e.elem.style[e.prop]?(t=x.css(e.elem,e.prop,""),t&&"auto"!==t?t:0):e.elem[e.prop]},set:function(e){x.fx.step[e.prop]?x.fx.step[e.prop](e):e.elem.style&&(null!=e.elem.style[x.cssProps[e.prop]]||x.cssHooks[e.prop])?x.style(e.elem,e.prop,e.now+e.unit):e.elem[e.prop]=e.now}}},Ln.propHooks.scrollTop=Ln.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},x.each(["toggle","show","hide"],function(e,t){var n=x.fn[t];x.fn[t]=function(e,r,i){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(qn(t,!0),e,r,i)}}),x.fn.extend({fadeTo:function(e,t,n,r){return this.filter(Lt).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=x.isEmptyObject(e),s=x.speed(t,n,r),o=function(){var t=jn(this,x.extend({},e),s);(i||q.get(this,"finish"))&&t.stop(!0)};return o.finish=o,i||s.queue===!1?this.each(o):this.queue(s.queue,o)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=undefined),t&&e!==!1&&this.queue(e||"fx",[]),this.each(function(){var t=!0,i=null!=e&&e+"queueHooks",s=x.timers,o=q.get(this);if(i)o[i]&&o[i].stop&&r(o[i]);else for(i in o)o[i]&&o[i].stop&&Cn.test(i)&&r(o[i]);for(i=s.length;i--;)s[i].elem!==this||null!=e&&s[i].queue!==e||(s[i].anim.stop(n),t=!1,s.splice(i,1));(t||!n)&&x.dequeue(this,e)})},finish:function(e){return e!==!1&&(e=e||"fx"),this.each(function(){var t,n=q.get(this),r=n[e+"queue"],i=n[e+"queueHooks"],s=x.timers,o=r?r.length:0;for(n.finish=!0,x.queue(this,e,[]),i&&i.stop&&i.stop.call(this,!0),t=s.length;t--;)s[t].elem===this&&s[t].queue===e&&(s[t].anim.stop(!0),s.splice(t,1));for(t=0;o>t;t++)r[t]&&r[t].finish&&r[t].finish.call(this);delete n.finish})}}),x.each({slideDown:qn("show"),slideUp:qn("hide"),slideToggle:qn("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){x.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}}),x.speed=function(e,t,n){var r=e&&"object"==typeof e?x.extend({},e):{complete:n||!n&&t||x.isFunction(e)&&e,duration:e,easing:n&&t||t&&!x.isFunction(t)&&t};return r.duration=x.fx.off?0:"number"==typeof r.duration?r.duration:r.duration in x.fx.speeds?x.fx.speeds[r.duration]:x.fx.speeds._default,(null==r.queue||r.queue===!0)&&(r.queue="fx"),r.old=r.complete,r.complete=function(){x.isFunction(r.old)&&r.old.call(this),r.queue&&x.dequeue(this,r.queue)},r},x.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2}},x.timers=[],x.fx=Ln.prototype.init,x.fx.tick=function(){var e,t=x.timers,n=0;for(xn=x.now();t.length>n;n++)e=t[n],e()||t[n]!==e||t.splice(n--,1);t.length||x.fx.stop(),xn=undefined},x.fx.timer=function(e){e()&&x.timers.push(e)&&x.fx.start()},x.fx.interval=13,x.fx.start=function(){bn||(bn=setInterval(x.fx.tick,x.fx.interval))},x.fx.stop=function(){clearInterval(bn),bn=null},x.fx.speeds={slow:600,fast:200,_default:400},x.fx.step={},x.expr&&x.expr.filters&&(x.expr.filters.animated=function(e){return x.grep(x.timers,function(t){return e===t.elem}).length}),x.fn.offset=function(e){if(arguments.length)return e===undefined?this:this.each(function(t){x.offset.setOffset(this,e,t)});var t,n,i=this[0],s={top:0,left:0},o=i&&i.ownerDocument;if(o)return t=o.documentElement,x.contains(t,i)?(typeof i.getBoundingClientRect!==r&&(s=i.getBoundingClientRect()),n=Hn(o),{top:s.top+n.pageYOffset-t.clientTop,left:s.left+n.pageXOffset-t.clientLeft}):s},x.offset={setOffset:function(e,t,n){var r,i,s,o,u,a,f,l=x.css(e,"position"),c=x(e),h={};"static"===l&&(e.style.position="relative"),u=c.offset(),s=x.css(e,"top"),a=x.css(e,"left"),f=("absolute"===l||"fixed"===l)&&(s+a).indexOf("auto")>-1,f?(r=c.position(),o=r.top,i=r.left):(o=parseFloat(s)||0,i=parseFloat(a)||0),x.isFunction(t)&&(t=t.call(e,n,u)),null!=t.top&&(h.top=t.top-u.top+o),null!=t.left&&(h.left=t.left-u.left+i),"using"in t?t.using.call(e,h):c.css(h)}},x.fn.extend({position:function(){if(this[0]){var e,t,n=this[0],r={top:0,left:0};return"fixed"===x.css(n,"position")?t=n.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),x.nodeName(e[0],"html")||(r=e.offset()),r.top+=x.css(e[0],"borderTopWidth",!0),r.left+=x.css(e[0],"borderLeftWidth",!0)),{top:t.top-r.top-x.css(n,"marginTop",!0),left:t.left-r.left-x.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){var e=this.offsetParent||s;while(e&&!x.nodeName(e,"html")&&"static"===x.css(e,"position"))e=e.offsetParent;return e||s})}}),x.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,n){var r="pageYOffset"===n;x.fn[t]=function(i){return x.access(this,function(t,i,s){var o=Hn(t);return s===undefined?o?o[n]:t[i]:(o?o.scrollTo(r?e.pageXOffset:s,r?s:e.pageYOffset):t[i]=s,undefined)},t,i,arguments.length,null)}}),x.each({Height:"height",Width:"width"},function(e,t){x.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,r){x.fn[r]=function(r,i){var s=arguments.length&&(n||"boolean"!=typeof r),o=n||(r===!0||i===!0?"margin":"border");return x.access(this,function(t,n,r){var i;return x.isWindow(t)?t.document.documentElement["client"+e]:9===t.nodeType?(i=t.documentElement,Math.max(t.body["scroll"+e],i["scroll"+e],t.body["offset"+e],i["offset"+e],i["client"+e])):r===undefined?x.css(t,n,o):x.style(t,n,r,o)},t,s?r:undefined,s,null)}})}),x.fn.size=function(){return this.length},x.fn.andSelf=x.fn.addBack,"object"==typeof module&&module&&"object"==typeof module.exports?module.exports=x:"function"==typeof define&&define.amd&&define("jquery",[],function(){return x}),"object"==typeof e&&"object"==typeof e.document&&(e.jQuery=e.$=x)})(window),!function(e,t){"object"==typeof exports&&exports?module.exports=t:"function"==typeof define&&define.amd?define("mustache",t):e.Mustache=t}(this,function(){function e(e,t){return RegExp.prototype.test.call(e,t)}function t(t){return!e(d,t)}function n(e){return e.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function r(e){return String(e).replace(/[&<>"'\/]/g,function(e){return b[e]})}function i(e){this.string=e,this.tail=e,this.pos=0}function s(e,t){this.view=e,this.parent=t,this.clearCache()}function o(){this.clearCache()}function u(e){function t(e,t,r){if(!n[e]){var i=u(t);n[e]=function(e,t){return i(e,t,r)}}return n[e]}var n={};return function(n,r,i){for(var s,o,u="",a=0,f=e.length;f>a;++a)switch(s=e[a],s[0]){case"#":o=i.slice(s[3],s[5]),u+=n._section(s[1],r,o,t(a,s[4],i));break;case"^":u+=n._inverted(s[1],r,t(a,s[4],i));break;case">":u+=n._partial(s[1],r);break;case"&":u+=n._name(s[1],r);break;case"name":u+=n._escaped(s[1],r);break;case"text":u+=s[1]}return u}}function a(e){for(var t,n=[],r=n,i=[],s=0,o=e.length;o>s;++s)switch(t=e[s],t[0]){case"#":case"^":i.push(t),r.push(t),r=t[4]=[];break;case"/":var u=i.pop();u[5]=t[2],r=i.length>0?i[i.length-1][4]:n;break;default:r.push(t)}return n}function f(e){for(var t,n,r=[],i=0,s=e.length;s>i;++i)t=e[i],"text"===t[0]&&n&&"text"===n[0]?(n[1]+=t[1],n[3]=t[3]):(n=t,r.push(t));return r}function l(e){return[new RegExp(n(e[0])+"\\s*"),new RegExp("\\s*"+n(e[1]))]}var c={};c.name="mustache.js",c.version="0.7.2",c.tags=["{{","}}"],c.Scanner=i,c.Context=s,c.Writer=o;var h=/\s*/,p=/\s+/,d=/\S/,v=/\s*=/,m=/\s*\}/,g=/#|\^|\/|>|\{|&|=|!/,y=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},b={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};c.escape=r,i.prototype.eos=function(){return""===this.tail},i.prototype.scan=function(e){var t=this.tail.match(e);return t&&0===t.index?(this.tail=this.tail.substring(t[0].length),this.pos+=t[0].length,t[0]):""},i.prototype.scanUntil=function(e){var t,n=this.tail.search(e);switch(n){case-1:t=this.tail,this.pos+=this.tail.length,this.tail="";break;case 0:t="";break;default:t=this.tail.substring(0,n),this.tail=this.tail.substring(n),this.pos+=n}return t},s.make=function(e){return e instanceof s?e:new s(e)},s.prototype.clearCache=function(){this._cache={}},s.prototype.push=function(e){return new s(e,this)},s.prototype.lookup=function(e){var t=this._cache[e];if(!t){if("."===e)t=this.view;else for(var n=this;n;){if(e.indexOf(".")>0){var r=e.split("."),i=0;for(t=n.view;t&&i<r.length;)t=t[r[i++]]}else t=n.view[e];if(null!=t)break;n=n.parent}this._cache[e]=t}return"function"==typeof t&&(t=t.call(this.view)),t},o.prototype.clearCache=function(){this._cache={},this._partialCache={}},o.prototype.compile=function(e,t){var n=this._cache[e];if(!n){var r=c.parse(e,t);n=this._cache[e]=this.compileTokens(r,e)}return n},o.prototype.compilePartial=function(e,t,n){var r=this.compile(t,n);return this._partialCache[e]=r,r},o.prototype.compileTokens=function(e,t){var n=u(e),r=this;return function(e,i){if(i)if("function"==typeof i)r._loadPartial=i;else for(var o in i)r.compilePartial(o,i[o]);return n(r,s.make(e),t)}},o.prototype.render=function(e,t,n){return this.compile(e)(t,n)},o.prototype._section=function(e,t,n,r){var i=t.lookup(e);switch(typeof i){case"object":if(y(i)){for(var s="",o=0,u=i.length;u>o;++o)s+=r(this,t.push(i[o]));return s}return i?r(this,t.push(i)):"";case"function":var a=this,f=function(e){return a.render(e,t)},l=i.call(t.view,n,f);return null!=l?l:"";default:if(i)return r(this,t)}return""},o.prototype._inverted=function(e,t,n){var r=t.lookup(e);return!r||y(r)&&0===r.length?n(this,t):""},o.prototype._partial=function(e,t){e in this._partialCache||!this._loadPartial||this.compilePartial(e,this._loadPartial(e));var n=this._partialCache[e];return n?n(t):""},o.prototype._name=function(e,t){var n=t.lookup(e);return"function"==typeof n&&(n=n.call(t.view)),null==n?"":String(n)},o.prototype._escaped=function(e,t){return c.escape(this._name(e,t))},c.parse=function(e,r){function s(){if(T&&!N)for(;x.length;)S.splice(x.pop(),1);else x=[];T=!1,N=!1}if(e=e||"",r=r||c.tags,"string"==typeof r&&(r=r.split(p)),2!==r.length)throw new Error("Invalid tags: "+r.join(", "));for(var o,u,d,y,b=l(r),w=new i(e),E=[],S=[],x=[],T=!1,N=!1;!w.eos();){if(o=w.pos,d=w.scanUntil(b[0]))for(var C=0,L=d.length;L>C;++C)y=d.charAt(C),t(y)?x.push(S.length):N=!0,S.push(["text",y,o,o+1]),o+=1,"\n"===y&&s();if(o=w.pos,!w.scan(b[0]))break;if(T=!0,u=w.scan(g)||"name",w.scan(h),"="===u)d=w.scanUntil(v),w.scan(v),w.scanUntil(b[1]);else if("{"===u){var A=new RegExp("\\s*"+n("}"+r[1]));d=w.scanUntil(A),w.scan(m),w.scanUntil(b[1]),u="&"}else d=w.scanUntil(b[1]);if(!w.scan(b[1]))throw new Error("Unclosed tag at "+w.pos);if("/"===u){if(0===E.length)throw new Error('Unopened section "'+d+'" at '+o);var O=E.pop();if(O[1]!==d)throw new Error('Unclosed section "'+O[1]+'" at '+o)}var M=[u,d,o,w.pos];if(S.push(M),"#"===u||"^"===u)E.push(M);else if("name"===u||"{"===u||"&"===u)N=!0;else if("="===u){if(r=d.split(p),2!==r.length)throw new Error("Invalid tags at "+o+": "+r.join(", "));b=l(r)}}var O=E.pop();if(O)throw new Error('Unclosed section "'+O[1]+'" at '+w.pos);return a(f(S))};var w=new o;return c.clearCache=function(){return w.clearCache()},c.compile=function(e,t){return w.compile(e,t)},c.compilePartial=function(e,t,n){return w.compilePartial(e,t,n)},c.compileTokens=function(e,t){return w.compileTokens(e,t)},c.render=function(e,t,n){return w.render(e,t,n)},c.to_html=function(e,t,n,r){var i=c.render(e,t,n);return"function"!=typeof r?i:(r(i),void 0)},c}()),function(){var e=this,t=e._,n={},r=Array.prototype,i=Object.prototype,s=Function.prototype,o=r.push,u=r.slice,a=r.concat,f=i.toString,l=i.hasOwnProperty,c=r.forEach,h=r.map,p=r.reduce,d=r.reduceRight,v=r.filter,m=r.every,g=r.some,y=r.indexOf,b=r.lastIndexOf,w=Array.isArray,E=Object.keys,S=s.bind,x=function(e){if(e instanceof x)return e;if(!(this instanceof x))return new x(e);this._wrapped=e};typeof exports!="undefined"?(typeof module!="undefined"&&module.exports&&(exports=module.exports=x),exports._=x):e._=x,x.VERSION="1.5.1";var T=x.each=x.forEach=function(e,t,r){if(e==null)return;if(c&&e.forEach===c)e.forEach(t,r);else if(e.length===+e.length){for(var i=0,s=e.length;i<s;i++)if(t.call(r,e[i],i,e)===n)return}else for(var o in e)if(x.has(e,o)&&t.call(r,e[o],o,e)===n)return};x.map=x.collect=function(e,t,n){var r=[];return e==null?r:h&&e.map===h?e.map(t,n):(T(e,function(e,i,s){r.push(t.call(n,e,i,s))}),r)};var N="Reduce of empty array with no initial value";x.reduce=x.foldl=x.inject=function(e,t,n,r){var i=arguments.length>2;e==null&&(e=[]);if(p&&e.reduce===p)return r&&(t=x.bind(t,r)),i?e.reduce(t,n):e.reduce(t);T(e,function(e,s,o){i?n=t.call(r,n,e,s,o):(n=e,i=!0)});if(!i)throw new TypeError(N);return n},x.reduceRight=x.foldr=function(e,t,n,r){var i=arguments.length>2;e==null&&(e=[]);if(d&&e.reduceRight===d)return r&&(t=x.bind(t,r)),i?e.reduceRight(t,n):e.reduceRight(t);var s=e.length;if(s!==+s){var o=x.keys(e);s=o.length}T(e,function(u,a,f){a=o?o[--s]:--s,i?n=t.call(r,n,e[a],a,f):(n=e[a],i=!0)});if(!i)throw new TypeError(N);return n},x.find=x.detect=function(e,t,n){var r;return C(e,function(e,i,s){if(t.call(n,e,i,s))return r=e,!0}),r},x.filter=x.select=function(e,t,n){var r=[];return e==null?r:v&&e.filter===v?e.filter(t,n):(T(e,function(e,i,s){t.call(n,e,i,s)&&r.push(e)}),r)},x.reject=function(e,t,n){return x.filter(e,function(e,r,i){return!t.call(n,e,r,i)},n)},x.every=x.all=function(e,t,r){t||(t=x.identity);var i=!0;return e==null?i:m&&e.every===m?e.every(t,r):(T(e,function(e,s,o){if(!(i=i&&t.call(r,e,s,o)))return n}),!!i)};var C=x.some=x.any=function(e,t,r){t||(t=x.identity);var i=!1;return e==null?i:g&&e.some===g?e.some(t,r):(T(e,function(e,s,o){if(i||(i=t.call(r,e,s,o)))return n}),!!i)};x.contains=x.include=function(e,t){return e==null?!1:y&&e.indexOf===y?e.indexOf(t)!=-1:C(e,function(e){return e===t})},x.invoke=function(e,t){var n=u.call(arguments,2),r=x.isFunction(t);return x.map(e,function(e){return(r?t:e[t]).apply(e,n)})},x.pluck=function(e,t){return x.map(e,function(e){return e[t]})},x.where=function(e,t,n){return x.isEmpty(t)?n?void 0:[]:x[n?"find":"filter"](e,function(e){for(var n in t)if(t[n]!==e[n])return!1;return!0})},x.findWhere=function(e,t){return x.where(e,t,!0)},x.max=function(e,t,n){if(!t&&x.isArray(e)&&e[0]===+e[0]&&e.length<65535)return Math.max.apply(Math,e);if(!t&&x.isEmpty(e))return-Infinity;var r={computed:-Infinity,value:-Infinity};return T(e,function(e,i,s){var o=t?t.call(n,e,i,s):e;o>r.computed&&(r={value:e,computed:o})}),r.value},x.min=function(e,t,n){if(!t&&x.isArray(e)&&e[0]===+e[0]&&e.length<65535)return Math.min.apply(Math,e);if(!t&&x.isEmpty(e))return Infinity;var r={computed:Infinity,value:Infinity};return T(e,function(e,i,s){var o=t?t.call(n,e,i,s):e;o<r.computed&&(r={value:e,computed:o})}),r.value},x.shuffle=function(e){var t,n=0,r=[];return T(e,function(e){t=x.random(n++),r[n-1]=r[t],r[t]=e}),r};var k=function(e){return x.isFunction(e)?e:function(t){return t[e]}};x.sortBy=function(e,t,n){var r=k(t);return x.pluck(x.map(e,function(e,t,i){return{value:e,index:t,criteria:r.call(n,e,t,i)}}).sort(function(e,t){var n=e.criteria,r=t.criteria;if(n!==r){if(n>r||n===void 0)return 1;if(n<r||r===void 0)return-1}return e.index<t.index?-1:1}),"value")};var L=function(e,t,n,r){var i={},s=k(t==null?x.identity:t);return T(e,function(t,o){var u=s.call(n,t,o,e);r(i,u,t)}),i};x.groupBy=function(e,t,n){return L(e,t,n,function(e,t,n){(x.has(e,t)?e[t]:e[t]=[]).push(n)})},x.countBy=function(e,t,n){return L(e,t,n,function(e,t){x.has(e,t)||(e[t]=0),e[t]++})},x.sortedIndex=function(e,t,n,r){n=n==null?x.identity:k(n);var i=n.call(r,t),s=0,o=e.length;while(s<o){var u=s+o>>>1;n.call(r,e[u])<i?s=u+1:o=u}return s},x.toArray=function(e){return e?x.isArray(e)?u.call(e):e.length===+e.length?x.map(e,x.identity):x.values(e):[]},x.size=function(e){return e==null?0:e.length===+e.length?e.length:x.keys(e).length},x.first=x.head=x.take=function(e,t,n){return e==null?void 0:t!=null&&!n?u.call(e,0,t):e[0]},x.initial=function(e,t,n){return u.call(e,0,e.length-(t==null||n?1:t))},x.last=function(e,t,n){return e==null?void 0:t!=null&&!n?u.call(e,Math.max(e.length-t,0)):e[e.length-1]},x.rest=x.tail=x.drop=function(e,t,n){return u.call(e,t==null||n?1:t)},x.compact=function(e){return x.filter(e,x.identity)};var A=function(e,t,n){return t&&x.every(e,x.isArray)?a.apply(n,e):(T(e,function(e){x.isArray(e)||x.isArguments(e)?t?o.apply(n,e):A(e,t,n):n.push(e)}),n)};x.flatten=function(e,t){return A(e,t,[])},x.without=function(e){return x.difference(e,u.call(arguments,1))},x.uniq=x.unique=function(e,t,n,r){x.isFunction(t)&&(r=n,n=t,t=!1);var i=n?x.map(e,n,r):e,s=[],o=[];return T(i,function(n,r){if(t?!r||o[o.length-1]!==n:!x.contains(o,n))o.push(n),s.push(e[r])}),s},x.union=function(){return x.uniq(x.flatten(arguments,!0))},x.intersection=function(e){var t=u.call(arguments,1);return x.filter(x.uniq(e),function(e){return x.every(t,function(t){return x.indexOf(t,e)>=0})})},x.difference=function(e){var t=a.apply(r,u.call(arguments,1));return x.filter(e,function(e){return!x.contains(t,e)})},x.zip=function(){var e=x.max(x.pluck(arguments,"length").concat(0)),t=new Array(e);for(var n=0;n<e;n++)t[n]=x.pluck(arguments,""+n);return t},x.object=function(e,t){if(e==null)return{};var n={};for(var r=0,i=e.length;r<i;r++)t?n[e[r]]=t[r]:n[e[r][0]]=e[r][1];return n},x.indexOf=function(e,t,n){if(e==null)return-1;var r=0,i=e.length;if(n){if(typeof n!="number")return r=x.sortedIndex(e,t),e[r]===t?r:-1;r=n<0?Math.max(0,i+n):n}if(y&&e.indexOf===y)return e.indexOf(t,n);for(;r<i;r++)if(e[r]===t)return r;return-1},x.lastIndexOf=function(e,t,n){if(e==null)return-1;var r=n!=null;if(b&&e.lastIndexOf===b)return r?e.lastIndexOf(t,n):e.lastIndexOf(t);var i=r?n:e.length;while(i--)if(e[i]===t)return i;return-1},x.range=function(e,t,n){arguments.length<=1&&(t=e||0,e=0),n=arguments[2]||1;var r=Math.max(Math.ceil((t-e)/n),0),i=0,s=new Array(r);while(i<r)s[i++]=e,e+=n;return s};var O=function(){};x.bind=function(e,t){var n,r;if(S&&e.bind===S)return S.apply(e,u.call(arguments,1));if(!x.isFunction(e))throw new TypeError;return n=u.call(arguments,2),r=function(){if(this instanceof r){O.prototype=e.prototype;var i=new O;O.prototype=null;var s=e.apply(i,n.concat(u.call(arguments)));return Object(s)===s?s:i}return e.apply(t,n.concat(u.call(arguments)))}},x.partial=function(e){var t=u.call(arguments,1);return function(){return e.apply(this,t.concat(u.call(arguments)))}},x.bindAll=function(e){var t=u.call(arguments,1);if(t.length===0)throw new Error("bindAll must be passed function names");return T(t,function(t){e[t]=x.bind(e[t],e)}),e},x.memoize=function(e,t){var n={};return t||(t=x.identity),function(){var r=t.apply(this,arguments);return x.has(n,r)?n[r]:n[r]=e.apply(this,arguments)}},x.delay=function(e,t){var n=u.call(arguments,2);return setTimeout(function(){return e.apply(null,n)},t)},x.defer=function(e){return x.delay.apply(x,[e,1].concat(u.call(arguments,1)))},x.throttle=function(e,t,n){var r,i,s,o=null,u=0;n||(n={});var a=function(){u=n.leading===!1?0:new Date,o=null,s=e.apply(r,i)};return function(){var f=new Date;!u&&n.leading===!1&&(u=f);var l=t-(f-u);return r=this,i=arguments,l<=0?(clearTimeout(o),o=null,u=f,s=e.apply(r,i)):!o&&n.trailing!==!1&&(o=setTimeout(a,l)),s}},x.debounce=function(e,t,n){var r,i=null;return function(){var s=this,o=arguments,u=function(){i=null,n||(r=e.apply(s,o))},a=n&&!i;return clearTimeout(i),i=setTimeout(u,t),a&&(r=e.apply(s,o)),r}},x.once=function(e){var t=!1,n;return function(){return t?n:(t=!0,n=e.apply(this,arguments),e=null,n)}},x.wrap=function(e,t){return function(){var n=[e];return o.apply(n,arguments),t.apply(this,n)}},x.compose=function(){var e=arguments;return function(){var t=arguments;for(var n=e.length-1;n>=0;n--)t=[e[n].apply(this,t)];return t[0]}},x.after=function(e,t){return function(){if(--e<1)return t.apply(this,arguments)}},x.keys=E||function(e){if(e!==Object(e))throw new TypeError("Invalid object");var t=[];for(var n in e)x.has(e,n)&&t.push(n);return t},x.values=function(e){var t=[];for(var n in e)x.has(e,n)&&t.push(e[n]);return t},x.pairs=function(e){var t=[];for(var n in e)x.has(e,n)&&t.push([n,e[n]]);return t},x.invert=function(e){var t={};for(var n in e)x.has(e,n)&&(t[e[n]]=n);return t},x.functions=x.methods=function(e){var t=[];for(var n in e)x.isFunction(e[n])&&t.push(n);return t.sort()},x.extend=function(e){return T(u.call(arguments,1),function(t){if(t)for(var n in t)e[n]=t[n]}),e},x.pick=function(e){var t={},n=a.apply(r,u.call(arguments,1));return T(n,function(n){n in e&&(t[n]=e[n])}),t},x.omit=function(e){var t={},n=a.apply(r,u.call(arguments,1));for(var i in e)x.contains(n,i)||(t[i]=e[i]);return t},x.defaults=function(e){return T(u.call(arguments,1),function(t){if(t)for(var n in t)e[n]===void 0&&(e[n]=t[n])}),e},x.clone=function(e){return x.isObject(e)?x.isArray(e)?e.slice():x.extend({},e):e},x.tap=function(e,t){return t(e),e};var M=function(e,t,n,r){if(e===t)return e!==0||1/e==1/t;if(e==null||t==null)return e===t;e instanceof x&&(e=e._wrapped),t instanceof x&&(t=t._wrapped);var i=f.call(e);if(i!=f.call(t))return!1;switch(i){case"[object String]":return e==String(t);case"[object Number]":return e!=+e?t!=+t:e==0?1/e==1/t:e==+t;case"[object Date]":case"[object Boolean]":return+e==+t;case"[object RegExp]":return e.source==t.source&&e.global==t.global&&e.multiline==t.multiline&&e.ignoreCase==t.ignoreCase}if(typeof e!="object"||typeof t!="object")return!1;var s=n.length;while(s--)if(n[s]==e)return r[s]==t;var o=e.constructor,u=t.constructor;if(o!==u&&!(x.isFunction(o)&&o instanceof o&&x.isFunction(u)&&u instanceof u))return!1;n.push(e),r.push(t);var a=0,l=!0;if(i=="[object Array]"){a=e.length,l=a==t.length;if(l)while(a--)if(!(l=M(e[a],t[a],n,r)))break}else{for(var c in e)if(x.has(e,c)){a++;if(!(l=x.has(t,c)&&M(e[c],t[c],n,r)))break}if(l){for(c in t)if(x.has(t,c)&&!(a--))break;l=!a}}return n.pop(),r.pop(),l};x.isEqual=function(e,t){return M(e,t,[],[])},x.isEmpty=function(e){if(e==null)return!0;if(x.isArray(e)||x.isString(e))return e.length===0;for(var t in e)if(x.has(e,t))return!1;return!0},x.isElement=function(e){return!!e&&e.nodeType===1},x.isArray=w||function(e){return f.call(e)=="[object Array]"},x.isObject=function(e){return e===Object(e)},T(["Arguments","Function","String","Number","Date","RegExp"],function(e){x["is"+e]=function(t){return f.call(t)=="[object "+e+"]"}}),x.isArguments(arguments)||(x.isArguments=function(e){return!!e&&!!x.has(e,"callee")}),typeof /./!="function"&&(x.isFunction=function(e){return typeof e=="function"}),x.isFinite=function(e){return isFinite(e)&&!isNaN(parseFloat(e))},x.isNaN=function(e){return x.isNumber(e)&&e!=+e},x.isBoolean=function(e){return e===!0||e===!1||f.call(e)=="[object Boolean]"},x.isNull=function(e){return e===null},x.isUndefined=function(e){return e===void 0},x.has=function(e,t){return l.call(e,t)},x.noConflict=function(){return e._=t,this},x.identity=function(e){return e},x.times=function(e,t,n){var r=Array(Math.max(0,e));for(var i=0;i<e;i++)r[i]=t.call(n,i);return r},x.random=function(e,t){return t==null&&(t=e,e=0),e+Math.floor(Math.random()*(t-e+1))};var _={escape:{"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","/":"&#x2F;"}};_.unescape=x.invert(_.escape);var D={escape:new RegExp("["+x.keys(_.escape).join("")+"]","g"),unescape:new RegExp("("+x.keys(_.unescape).join("|")+")","g")};x.each(["escape","unescape"],function(e){x[e]=function(t){return t==null?"":(""+t).replace(D[e],function(t){return _[e][t]})}}),x.result=function(e,t){if(e==null)return void 0;var n=e[t];return x.isFunction(n)?n.call(e):n},x.mixin=function(e){T(x.functions(e),function(t){var n=x[t]=e[t];x.prototype[t]=function(){var e=[this._wrapped];return o.apply(e,arguments),F.call(this,n.apply(x,e))}})};var P=0;x.uniqueId=function(e){var t=++P+"";return e?e+t:t},x.templateSettings={evaluate:/<%([\s\S]+?)%>/g,interpolate:/<%=([\s\S]+?)%>/g,escape:/<%-([\s\S]+?)%>/g};var H=/(.)^/,B={"'":"'","\\":"\\","\r":"r","\n":"n","	":"t","\u2028":"u2028","\u2029":"u2029"},j=/\\|'|\r|\n|\t|\u2028|\u2029/g;x.template=function(e,t,n){var r;n=x.defaults({},n,x.templateSettings);var i=new RegExp([(n.escape||H).source,(n.interpolate||H).source,(n.evaluate||H).source].join("|")+"|$","g"),s=0,o="__p+='";e.replace(i,function(t,n,r,i,u){return o+=e.slice(s,u).replace(j,function(e){return"\\"+B[e]}),n&&(o+="'+\n((__t=("+n+"))==null?'':_.escape(__t))+\n'"),r&&(o+="'+\n((__t=("+r+"))==null?'':__t)+\n'"),i&&(o+="';\n"+i+"\n__p+='"),s=u+t.length,t}),o+="';\n",n.variable||(o="with(obj||{}){\n"+o+"}\n"),o="var __t,__p='',__j=Array.prototype.join,print=function(){__p+=__j.call(arguments,'');};\n"+o+"return __p;\n";try{r=new Function(n.variable||"obj","_",o)}catch(u){throw u.source=o,u}if(t)return r(t,x);var a=function(e){return r.call(this,e,x)};return a.source="function("+(n.variable||"obj")+"){\n"+o+"}",a},x.chain=function(e){return x(e).chain()};var F=function(e){return this._chain?x(e).chain():e};x.mixin(x),T(["pop","push","reverse","shift","sort","splice","unshift"],function(e){var t=r[e];x.prototype[e]=function(){var n=this._wrapped;return t.apply(n,arguments),(e=="shift"||e=="splice")&&n.length===0&&delete n[0],F.call(this,n)}}),T(["concat","join","slice"],function(e){var t=r[e];x.prototype[e]=function(){return F.call(this,t.apply(this._wrapped,arguments))}}),x.extend(x.prototype,{chain:function(){return this._chain=!0,this},value:function(){return this._wrapped}})}.call(this),define("underscore",function(e){return function(){var t,n;return t||e._}}(this)),function(){var e=this,t=e.Backbone,n=[],r=n.push,i=n.slice,s=n.splice,o;typeof exports!="undefined"?o=exports:o=e.Backbone={},o.VERSION="1.0.0";var u=e._;!u&&typeof require!="undefined"&&(u=require("underscore")),o.$=e.jQuery||e.Zepto||e.ender||e.$,o.noConflict=function(){return e.Backbone=t,this},o.emulateHTTP=!1,o.emulateJSON=!1;var a=o.Events={on:function(e,t,n){if(!l(this,"on",e,[t,n])||!t)return this;this._events||(this._events={});var r=this._events[e]||(this._events[e]=[]);return r.push({callback:t,context:n,ctx:n||this}),this},once:function(e,t,n){if(!l(this,"once",e,[t,n])||!t)return this;var r=this,i=u.once(function(){r.off(e,i),t.apply(this,arguments)});return i._callback=t,this.on(e,i,n)},off:function(e,t,n){var r,i,s,o,a,f,c,h;if(!this._events||!l(this,"off",e,[t,n]))return this;if(!e&&!t&&!n)return this._events={},this;o=e?[e]:u.keys(this._events);for(a=0,f=o.length;a<f;a++){e=o[a];if(s=this._events[e]){this._events[e]=r=[];if(t||n)for(c=0,h=s.length;c<h;c++)i=s[c],(t&&t!==i.callback&&t!==i.callback._callback||n&&n!==i.context)&&r.push(i);r.length||delete this._events[e]}}return this},trigger:function(e){if(!this._events)return this;var t=i.call(arguments,1);if(!l(this,"trigger",e,t))return this;var n=this._events[e],r=this._events.all;return n&&c(n,t),r&&c(r,arguments),this},stopListening:function(e,t,n){var r=this._listeners;if(!r)return this;var i=!t&&!n;typeof t=="object"&&(n=this),e&&((r={})[e._listenerId]=e);for(var s in r)r[s].off(t,n,this),i&&delete this._listeners[s];return this}},f=/\s+/,l=function(e,t,n,r){if(!n)return!0;if(typeof n=="object"){for(var i in n)e[t].apply(e,[i,n[i]].concat(r));return!1}if(f.test(n)){var s=n.split(f);for(var o=0,u=s.length;o<u;o++)e[t].apply(e,[s[o]].concat(r));return!1}return!0},c=function(e,t){var n,r=-1,i=e.length,s=t[0],o=t[1],u=t[2];switch(t.length){case 0:while(++r<i)(n=e[r]).callback.call(n.ctx);return;case 1:while(++r<i)(n=e[r]).callback.call(n.ctx,s);return;case 2:while(++r<i)(n=e[r]).callback.call(n.ctx,s,o);return;case 3:while(++r<i)(n=e[r]).callback.call(n.ctx,s,o,u);return;default:while(++r<i)(n=e[r]).callback.apply(n.ctx,t)}},h={listenTo:"on",listenToOnce:"once"};u.each(h,function(e,t){a[t]=function(t,n,r){var i=this._listeners||(this._listeners={}),s=t._listenerId||(t._listenerId=u.uniqueId("l"));return i[s]=t,typeof n=="object"&&(r=this),t[e](n,r,this),this}}),a.bind=a.on,a.unbind=a.off,u.extend(o,a);var p=o.Model=function(e,t){var n,r=e||{};t||(t={}),this.cid=u.uniqueId("c"),this.attributes={},u.extend(this,u.pick(t,d)),t.parse&&(r=this.parse(r,t)||{});if(n=u.result(this,"defaults"))r=u.defaults({},r,n);this.set(r,t),this.changed={},this.initialize.apply(this,arguments)},d=["url","urlRoot","collection"];u.extend(p.prototype,a,{changed:null,validationError:null,idAttribute:"id",initialize:function(){},toJSON:function(e){return u.clone(this.attributes)},sync:function(){return o.sync.apply(this,arguments)},get:function(e){return this.attributes[e]},escape:function(e){return u.escape(this.get(e))},has:function(e){return this.get(e)!=null},set:function(e,t,n){var r,i,s,o,a,f,l,c;if(e==null)return this;typeof e=="object"?(i=e,n=t):(i={})[e]=t,n||(n={});if(!this._validate(i,n))return!1;s=n.unset,a=n.silent,o=[],f=this._changing,this._changing=!0,f||(this._previousAttributes=u.clone(this.attributes),this.changed={}),c=this.attributes,l=this._previousAttributes,this.idAttribute in i&&(this.id=i[this.idAttribute]);for(r in i)t=i[r],u.isEqual(c[r],t)||o.push(r),u.isEqual(l[r],t)?delete this.changed[r]:this.changed[r]=t,s?delete c[r]:c[r]=t;if(!a){o.length&&(this._pending=!0);for(var h=0,p=o.length;h<p;h++)this.trigger("change:"+o[h],this,c[o[h]],n)}if(f)return this;if(!a)while(this._pending)this._pending=!1,this.trigger("change",this,n);return this._pending=!1,this._changing=!1,this},unset:function(e,t){return this.set(e,void 0,u.extend({},t,{unset:!0}))},clear:function(e){var t={};for(var n in this.attributes)t[n]=void 0;return this.set(t,u.extend({},e,{unset:!0}))},hasChanged:function(e){return e==null?!u.isEmpty(this.changed):u.has(this.changed,e)},changedAttributes:function(e){if(!e)return this.hasChanged()?u.clone(this.changed):!1;var t,n=!1,r=this._changing?this._previousAttributes:this.attributes;for(var i in e){if(u.isEqual(r[i],t=e[i]))continue;(n||(n={}))[i]=t}return n},previous:function(e){return e==null||!this._previousAttributes?null:this._previousAttributes[e]},previousAttributes:function(){return u.clone(this._previousAttributes)},fetch:function(e){e=e?u.clone(e):{},e.parse===void 0&&(e.parse=!0);var t=this,n=e.success;return e.success=function(r){if(!t.set(t.parse(r,e),e))return!1;n&&n(t,r,e),t.trigger("sync",t,r,e)},j(this,e),this.sync("read",this,e)},save:function(e,t,n){var r,i,s,o=this.attributes;e==null||typeof e=="object"?(r=e,n=t):(r={})[e]=t;if(r&&(!n||!n.wait)&&!this.set(r,n))return!1;n=u.extend({validate:!0},n);if(!this._validate(r,n))return!1;r&&n.wait&&(this.attributes=u.extend({},o,r)),n.parse===void 0&&(n.parse=!0);var a=this,f=n.success;return n.success=function(e){a.attributes=o;var t=a.parse(e,n);n.wait&&(t=u.extend(r||{},t));if(u.isObject(t)&&!a.set(t,n))return!1;f&&f(a,e,n),a.trigger("sync",a,e,n)},j(this,n),i=this.isNew()?"create":n.patch?"patch":"update",i==="patch"&&(n.attrs=r),s=this.sync(i,this,n),r&&n.wait&&(this.attributes=o),s},destroy:function(e){e=e?u.clone(e):{};var t=this,n=e.success,r=function(){t.trigger("destroy",t,t.collection,e)};e.success=function(i){(e.wait||t.isNew())&&r(),n&&n(t,i,e),t.isNew()||t.trigger("sync",t,i,e)};if(this.isNew())return e.success(),!1;j(this,e);var i=this.sync("delete",this,e);return e.wait||r(),i},url:function(){var e=u.result(this,"urlRoot")||u.result(this.collection,"url")||B();return this.isNew()?e:e+(e.charAt(e.length-1)==="/"?"":"/")+encodeURIComponent(this.id)},parse:function(e,t){return e},clone:function(){return new this.constructor(this.attributes)},isNew:function(){return this.id==null},isValid:function(e){return this._validate({},u.extend(e||{},{validate:!0}))},_validate:function(e,t){if(!t.validate||!this.validate)return!0;e=u.extend({},this.attributes,e);var n=this.validationError=this.validate(e,t)||null;return n?(this.trigger("invalid",this,n,u.extend(t||{},{validationError:n})),!1):!0}});var v=["keys","values","pairs","invert","pick","omit"];u.each(v,function(e){p.prototype[e]=function(){var t=i.call(arguments);return t.unshift(this.attributes),u[e].apply(u,t)}});var m=o.Collection=function(e,t){t||(t={}),t.url&&(this.url=t.url),t.model&&(this.model=t.model),t.comparator!==void 0&&(this.comparator=t.comparator),this._reset(),this.initialize.apply(this,arguments),e&&this.reset(e,u.extend({silent:!0},t))},g={add:!0,remove:!0,merge:!0},y={add:!0,merge:!1,remove:!1};u.extend(m.prototype,a,{model:p,initialize:function(){},toJSON:function(e){return this.map(function(t){return t.toJSON(e)})},sync:function(){return o.sync.apply(this,arguments)},add:function(e,t){return this.set(e,u.defaults(t||{},y))},remove:function(e,t){e=u.isArray(e)?e.slice():[e],t||(t={});var n,r,i,s;for(n=0,r=e.length;n<r;n++){s=this.get(e[n]);if(!s)continue;delete this._byId[s.id],delete this._byId[s.cid],i=this.indexOf(s),this.models.splice(i,1),this.length--,t.silent||(t.index=i,s.trigger("remove",s,this,t)),this._removeReference(s)}return this},set:function(e,t){t=u.defaults(t||{},g),t.parse&&(e=this.parse(e,t)),u.isArray(e)||(e=e?[e]:[]);var n,i,o,a,f,l,c=t.at,h=this.comparator&&c==null&&t.sort!==!1,p=u.isString(this.comparator)?this.comparator:null,d=[],v=[],m={};for(n=0,i=e.length;n<i;n++){if(!(o=this._prepareModel(e[n],t)))continue;(f=this.get(o))?(t.remove&&(m[f.cid]=!0),t.merge&&(f.set(o.attributes,t),h&&!l&&f.hasChanged(p)&&(l=!0))):t.add&&(d.push(o),o.on("all",this._onModelEvent,this),this._byId[o.cid]=o,o.id!=null&&(this._byId[o.id]=o))}if(t.remove){for(n=0,i=this.length;n<i;++n)m[(o=this.models[n]).cid]||v.push(o);v.length&&this.remove(v,t)}d.length&&(h&&(l=!0),this.length+=d.length,c!=null?s.apply(this.models,[c,0].concat(d)):r.apply(this.models,d)),l&&this.sort({silent:!0});if(t.silent)return this;for(n=0,i=d.length;n<i;n++)(o=d[n]).trigger("add",o,this,t);return l&&this.trigger("sort",this,t),this},reset:function(e,t){t||(t={});for(var n=0,r=this.models.length;n<r;n++)this._removeReference(this.models[n]);return t.previousModels=this.models,this._reset(),this.add(e,u.extend({silent:!0},t)),t.silent||this.trigger("reset",this,t),this},push:function(e,t){return e=this._prepareModel(e,t),this.add(e,u.extend({at:this.length},t)),e},pop:function(e){var t=this.at(this.length-1);return this.remove(t,e),t},unshift:function(e,t){return e=this._prepareModel(e,t),this.add(e,u.extend({at:0},t)),e},shift:function(e){var t=this.at(0);return this.remove(t,e),t},slice:function(e,t){return this.models.slice(e,t)},get:function(e){return e==null?void 0:this._byId[e.id!=null?e.id:e.cid||e]},at:function(e){return this.models[e]},where:function(e,t){return u.isEmpty(e)?t?void 0:[]:this[t?"find":"filter"](function(t){for(var n in e)if(e[n]!==t.get(n))return!1;return!0})},findWhere:function(e){return this.where(e,!0)},sort:function(e){if(!this.comparator)throw new Error("Cannot sort a set without a comparator");return e||(e={}),u.isString(this.comparator)||this.comparator.length===1?this.models=this.sortBy(this.comparator,this):this.models.sort(u.bind(this.comparator,this)),e.silent||this.trigger("sort",this,e),this},sortedIndex:function(e,t,n){t||(t=this.comparator);var r=u.isFunction(t)?t:function(e){return e.get(t)};return u.sortedIndex(this.models,e,r,n)},pluck:function(e){return u.invoke(this.models,"get",e)},fetch:function(e){e=e?u.clone(e):{},e.parse===void 0&&(e.parse=!0);var t=e.success,n=this;return e.success=function(r){var i=e.reset?"reset":"set";n[i](r,e),t&&t(n,r,e),n.trigger("sync",n,r,e)},j(this,e),this.sync("read",this,e)},create:function(e,t){t=t?u.clone(t):{};if(!(e=this._prepareModel(e,t)))return!1;t.wait||this.add(e,t);var n=this,r=t.success;return t.success=function(i){t.wait&&n.add(e,t),r&&r(e,i,t)},e.save(null,t),e},parse:function(e,t){return e},clone:function(){return new this.constructor(this.models)},_reset:function(){this.length=0,this.models=[],this._byId={}},_prepareModel:function(e,t){if(e instanceof p)return e.collection||(e.collection=this),e;t||(t={}),t.collection=this;var n=new this.model(e,t);return n._validate(e,t)?n:(this.trigger("invalid",this,e,t),!1)},_removeReference:function(e){this===e.collection&&delete e.collection,e.off("all",this._onModelEvent,this)},_onModelEvent:function(e,t,n,r){if((e==="add"||e==="remove")&&n!==this)return;e==="destroy"&&this.remove(t,r),t&&e==="change:"+t.idAttribute&&(delete this._byId[t.previous(t.idAttribute)],t.id!=null&&(this._byId[t.id]=t)),this.trigger.apply(this,arguments)}});var b=["forEach","each","map","collect","reduce","foldl","inject","reduceRight","foldr","find","detect","filter","select","reject","every","all","some","any","include","contains","invoke","max","min","toArray","size","first","head","take","initial","rest","tail","drop","last","without","indexOf","shuffle","lastIndexOf","isEmpty","chain"];u.each(b,function(e){m.prototype[e]=function(){var t=i.call(arguments);return t.unshift(this.models),u[e].apply(u,t)}});var w=["groupBy","countBy","sortBy"];u.each(w,function(e){m.prototype[e]=function(t,n){var r=u.isFunction(t)?t:function(e){return e.get(t)};return u[e](this.models,r,n)}});var E=o.View=function(e){this.cid=u.uniqueId("view"),this._configure(e||{}),this._ensureElement(),this.initialize.apply(this,arguments),this.delegateEvents()},S=/^(\S+)\s*(.*)$/,x=["model","collection","el","id","attributes","className","tagName","events"];u.extend(E.prototype,a,{tagName:"div",$:function(e){return this.$el.find(e)},initialize:function(){},render:function(){return this},remove:function(){return this.$el.remove(),this.stopListening(),this},setElement:function(e,t){return this.$el&&this.undelegateEvents(),this.$el=e instanceof o.$?e:o.$(e),this.el=this.$el[0],t!==!1&&this.delegateEvents(),this},delegateEvents:function(e){if(!e&&!(e=u.result(this,"events")))return this;this.undelegateEvents();for(var t in e){var n=e[t];u.isFunction(n)||(n=this[e[t]]);if(!n)continue;var r=t.match(S),i=r[1],s=r[2];n=u.bind(n,this),i+=".delegateEvents"+this.cid,s===""?this.$el.on(i,n):this.$el.on(i,s,n)}return this},undelegateEvents:function(){return this.$el.off(".delegateEvents"+this.cid),this},_configure:function(e){this.options&&(e=u.extend({},u.result(this,"options"),e)),u.extend(this,u.pick(e,x)),this.options=e},_ensureElement:function(){if(!this.el){var e=u.extend({},u.result(this,"attributes"));this.id&&(e.id=u.result(this,"id")),this.className&&(e["class"]=u.result(this,"className"));var t=o.$("<"+u.result(this,"tagName")+">").attr(e);this.setElement(t,!1)}else this.setElement(u.result(this,"el"),!1)}}),o.sync=function(e,t,n){var r=T[e];u.defaults(n||(n={}),{emulateHTTP:o.emulateHTTP,emulateJSON:o.emulateJSON});var i={type:r,dataType:"json"};n.url||(i.url=u.result(t,"url")||B()),n.data==null&&t&&(e==="create"||e==="update"||e==="patch")&&(i.contentType="application/json",i.data=JSON.stringify(n.attrs||t.toJSON(n))),n.emulateJSON&&(i.contentType="application/x-www-form-urlencoded",i.data=i.data?{model:i.data}:{});if(n.emulateHTTP&&(r==="PUT"||r==="DELETE"||r==="PATCH")){i.type="POST",n.emulateJSON&&(i.data._method=r);var s=n.beforeSend;n.beforeSend=function(e){e.setRequestHeader("X-HTTP-Method-Override",r);if(s)return s.apply(this,arguments)}}i.type!=="GET"&&!n.emulateJSON&&(i.processData=!1),i.type==="PATCH"&&window.ActiveXObject&&(!window.external||!window.external.msActiveXFilteringEnabled)&&(i.xhr=function(){return new ActiveXObject("Microsoft.XMLHTTP")});var a=n.xhr=o.ajax(u.extend(i,n));return t.trigger("request",t,a,n),a};var T={create:"POST",update:"PUT",patch:"PATCH","delete":"DELETE",read:"GET"};o.ajax=function(){return o.$.ajax.apply(o.$,arguments)};var N=o.Router=function(e){e||(e={}),e.routes&&(this.routes=e.routes),this._bindRoutes(),this.initialize.apply(this,arguments)},C=/\((.*?)\)/g,k=/(\(\?)?:\w+/g,L=/\*\w+/g,A=/[\-{}\[\]+?.,\\\^$|#\s]/g;u.extend(N.prototype,a,{initialize:function(){},route:function(e,t,n){u.isRegExp(e)||(e=this._routeToRegExp(e)),u.isFunction(t)&&(n=t,t=""),n||(n=this[t]);var r=this;return o.history.route(e,function(i){var s=r._extractParameters(e,i);n&&n.apply(r,s),r.trigger.apply(r,["route:"+t].concat(s)),r.trigger("route",t,s),o.history.trigger("route",r,t,s)}),this},navigate:function(e,t){return o.history.navigate(e,t),this},_bindRoutes:function(){if(!this.routes)return;this.routes=u.result(this,"routes");var e,t=u.keys(this.routes);while((e=t.pop())!=null)this.route(e,this.routes[e])},_routeToRegExp:function(e){return e=e.replace(A,"\\$&").replace(C,"(?:$1)?").replace(k,function(e,t){return t?e:"([^/]+)"}).replace(L,"(.*?)"),new RegExp("^"+e+"$")},_extractParameters:function(e,t){var n=e.exec(t).slice(1);return u.map(n,function(e){return e?decodeURIComponent(e):null})}});var O=o.History=function(){this.handlers=[],u.bindAll(this,"checkUrl"),typeof window!="undefined"&&(this.location=window.location,this.history=window.history)},M=/^[#\/]|\s+$/g,_=/^\/+|\/+$/g,D=/msie [\w.]+/,P=/\/$/;O.started=!1,u.extend(O.prototype,a,{interval:50,getHash:function(e){var t=(e||this).location.href.match(/#(.*)$/);return t?t[1]:""},getFragment:function(e,t){if(e==null)if(this._hasPushState||!this._wantsHashChange||t){e=this.location.pathname;var n=this.root.replace(P,"");e.indexOf(n)||(e=e.substr(n.length))}else e=this.getHash();return e.replace(M,"")},start:function(e){if(O.started)throw new Error("Backbone.history has already been started");O.started=!0,this.options=u.extend({},{root:"/"},this.options,e),this.root=this.options.root,this._wantsHashChange=this.options.hashChange!==!1,this._wantsPushState=!!this.options.pushState,this._hasPushState=!!(this.options.pushState&&this.history&&this.history.pushState);var t=this.getFragment(),n=document.documentMode,r=D.exec(navigator.userAgent.toLowerCase())&&(!n||n<=7);this.root=("/"+this.root+"/").replace(_,"/"),r&&this._wantsHashChange&&(this.iframe=o.$('<iframe src="javascript:0" tabindex="-1" />').hide().appendTo("body")[0].contentWindow,this.navigate(t)),this._hasPushState?o.$(window).on("popstate",this.checkUrl):this._wantsHashChange&&"onhashchange"in window&&!r?o.$(window).on("hashchange",this.checkUrl):this._wantsHashChange&&(this._checkUrlInterval=setInterval(this.checkUrl,this.interval)),this.fragment=t;var i=this.location,s=i.pathname.replace(/[^\/]$/,"$&/")===this.root;if(this._wantsHashChange&&this._wantsPushState&&!this._hasPushState&&!s)return this.fragment=this.getFragment(null,!0),this.location.replace(this.root+this.location.search+"#"+this.fragment),!0;this._wantsPushState&&this._hasPushState&&s&&i.hash&&(this.fragment=this.getHash().replace(M,""),this.history.replaceState({},document.title,this.root+this.fragment+i.search));if(!this.options.silent)return this.loadUrl()},stop:function(){o.$(window).off("popstate",this.checkUrl).off("hashchange",this.checkUrl),clearInterval(this._checkUrlInterval),O.started=!1},route:function(e,t){this.handlers.unshift({route:e,callback:t})},checkUrl:function(e){var t=this.getFragment();t===this.fragment&&this.iframe&&(t=this.getFragment(this.getHash(this.iframe)));if(t===this.fragment)return!1;this.iframe&&this.navigate(t),this.loadUrl()||this.loadUrl(this.getHash())},loadUrl:function(e){var t=this.fragment=this.getFragment(e),n=u.any(this.handlers,function(e){if(e.route.test(t))return e.callback(t),!0});return n},navigate:function(e,t){if(!O.started)return!1;if(!t||t===!0)t={trigger:t};e=this.getFragment(e||"");if(this.fragment===e)return;this.fragment=e;var n=this.root+e;if(this._hasPushState)this.history[t.replace?"replaceState":"pushState"]({},document.title,n);else{if(!this._wantsHashChange)return this.location.assign(n);this._updateHash(this.location,e,t.replace),this.iframe&&e!==this.getFragment(this.getHash(this.iframe))&&(t.replace||this.iframe.document.open().close(),this._updateHash(this.iframe.location,e,t.replace))}t.trigger&&this.loadUrl(e)},_updateHash:function(e,t,n){if(n){var r=e.href.replace(/(javascript:|#).*$/,"");e.replace(r+"#"+t)}else e.hash="#"+t}}),o.history=new O;var H=function(e,t){var n=this,r;e&&u.has(e,"constructor")?r=e.constructor:r=function(){return n.apply(this,arguments)},u.extend(r,n,t);var i=function(){this.constructor=r};return i.prototype=n.prototype,r.prototype=new i,e&&u.extend(r.prototype,e),r.__super__=n.prototype,r};p.extend=m.extend=N.extend=E.extend=O.extend=H;var B=function(){throw new Error('A "url" property or function must be specified')},j=function(e,t){var n=t.error;t.error=function(r){n&&n(e,r,t),e.trigger("error",e,r,t)}}}.call(this),define("backbone",["underscore","jquery"],function(e){return function(){var t,n;return t||e.Backbone}}(this)),function(e){e.fn.each2===void 0&&e.fn.extend({each2:function(t){for(var n=e([0]),r=-1,i=this.length;i>++r&&(n.context=n[0]=this[r])&&t.call(n[0],r,n)!==!1;);return this}})}(jQuery),function(e,t){function n(e,t){for(var n=0,r=t.length;r>n;n+=1)if(i(e,t[n]))return n;return-1}function r(){var t=e(j);t.appendTo("body");var n={width:t.width()-t[0].clientWidth,height:t.height()-t[0].clientHeight};return t.remove(),n}function i(e,n){return e===n?!0:e===t||n===t?!1:null===e||null===n?!1:e.constructor===String?e+""==n+"":n.constructor===String?n+""==e+"":!1}function s(t,n){var r,i,s;if(null===t||1>t.length)return[];for(r=t.split(n),i=0,s=r.length;s>i;i+=1)r[i]=e.trim(r[i]);return r}function o(e){return e.outerWidth(!1)-e.width()}function u(n){var r="keyup-change-value";n.on("keydown",function(){e.data(n,r)===t&&e.data(n,r,n.val())}),n.on("keyup",function(){var i=e.data(n,r);i!==t&&n.val()!==i&&(e.removeData(n,r),n.trigger("keyup-change"))})}function a(n){n.on("mousemove",function(n){var r=B;(r===t||r.x!==n.pageX||r.y!==n.pageY)&&e(n.target).trigger("mousemove-filtered",n)})}function f(e,n,r){r=r||t;var i;return function(){var t=arguments;window.clearTimeout(i),i=window.setTimeout(function(){n.apply(r,t)},e)}}function l(e){var t,n=!1;return function(){return n===!1&&(t=e(),n=!0),t}}function c(e,t){var r=f(e,function(e){t.trigger("scroll-debounced",e)});t.on("scroll",function(e){n(e.target,t.get())>=0&&r(e)})}function h(e){e[0]!==document.activeElement&&window.setTimeout(function(){var t,n=e[0],r=e.val().length;e.focus(),e.is(":visible")&&n===document.activeElement&&(n.setSelectionRange?n.setSelectionRange(r,r):n.createTextRange&&(t=n.createTextRange(),t.collapse(!1),t.select()))},0)}function p(t){t=e(t)[0];var n=0,r=0;if("selectionStart"in t)n=t.selectionStart,r=t.selectionEnd-n;else if("selection"in document){t.focus();var i=document.selection.createRange();r=document.selection.createRange().text.length,i.moveStart("character",-t.value.length),n=i.text.length-r}return{offset:n,length:r}}function d(e){e.preventDefault(),e.stopPropagation()}function v(e){e.preventDefault(),e.stopImmediatePropagation()}function m(t){if(!D){var n=t[0].currentStyle||window.getComputedStyle(t[0],null);D=e(document.createElement("div")).css({position:"absolute",left:"-10000px",top:"-10000px",display:"none",fontSize:n.fontSize,fontFamily:n.fontFamily,fontStyle:n.fontStyle,fontWeight:n.fontWeight,letterSpacing:n.letterSpacing,textTransform:n.textTransform,whiteSpace:"nowrap"}),D.attr("class","select2-sizer"),e("body").append(D)}return D.text(t.val()),D.width()}function g(t,n,r){var i,s,o=[];i=t.attr("class"),i&&(i=""+i,e(i.split(" ")).each2(function(){0===this.indexOf("select2-")&&o.push(this)})),i=n.attr("class"),i&&(i=""+i,e(i.split(" ")).each2(function(){0!==this.indexOf("select2-")&&(s=r(this),s&&o.push(this))})),t.attr("class",o.join(" "))}function y(e,n,r,i){var s=e.toUpperCase().indexOf(n.toUpperCase()),o=n.length;return 0>s?(r.push(i(e)),t):(r.push(i(e.substring(0,s))),r.push("<span class='select2-match'>"),r.push(i(e.substring(s,s+o))),r.push("</span>"),r.push(i(e.substring(s+o,e.length))),t)}function b(e){var t={"\\":"&#92;","&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#47;"};return(e+"").replace(/[&<>"'\/\\]/g,function(e){return t[e]})}function w(n){var r,i=0,s=null,o=n.quietMillis||100,u=n.url,a=this;return function(f){window.clearTimeout(r),r=window.setTimeout(function(){i+=1;var r=i,o=n.data,l=u,c=n.transport||e.fn.select2.ajaxDefaults.transport,h={type:n.type||"GET",cache:n.cache||!1,jsonpCallback:n.jsonpCallback||t,dataType:n.dataType||"json"},p=e.extend({},e.fn.select2.ajaxDefaults.params,h);o=o?o.call(a,f.term,f.page,f.context):null,l="function"==typeof l?l.call(a,f.term,f.page,f.context):l,s&&s.abort(),n.params&&(e.isFunction(n.params)?e.extend(p,n.params.call(a)):e.extend(p,n.params)),e.extend(p,{url:l,dataType:n.dataType,data:o,success:function(e){if(!(i>r)){var t=n.results(e,f.page);f.callback(t)}}}),s=c.call(a,p)},o)}}function E(n){var r,i,s=n,o=function(e){return""+e.text};e.isArray(s)&&(i=s,s={results:i}),e.isFunction(s)===!1&&(i=s,s=function(){return i});var u=s();return u.text&&(o=u.text,e.isFunction(o)||(r=u.text,o=function(e){return e[r]})),function(n){var r,i=n.term,u={results:[]};return""===i?(n.callback(s()),t):(r=function(t,s){var u,a;if(t=t[0],t.children){u={};for(a in t)t.hasOwnProperty(a)&&(u[a]=t[a]);u.children=[],e(t.children).each2(function(e,t){r(t,u.children)}),(u.children.length||n.matcher(i,o(u),t))&&s.push(u)}else n.matcher(i,o(t),t)&&s.push(t)},e(s().results).each2(function(e,t){r(t,u.results)}),n.callback(u),t)}}function S(n){var r=e.isFunction(n);return function(i){var s=i.term,o={results:[]};e(r?n():n).each(function(){var e=this.text!==t,n=e?this.text:this;(""===s||i.matcher(s,n))&&o.results.push(e?this:{id:this,text:this})}),i.callback(o)}}function x(t,n){if(e.isFunction(t))return!0;if(!t)return!1;throw Error(n+" must be a function or a falsy value")}function T(t){return e.isFunction(t)?t():t}function N(t){var n=0;return e.each(t,function(e,t){t.children?n+=N(t.children):n++}),n}function C(e,n,r,s){var o,u,a,f,l,c=e,h=!1;if(!s.createSearchChoice||!s.tokenSeparators||1>s.tokenSeparators.length)return t;for(;;){for(u=-1,a=0,f=s.tokenSeparators.length;f>a&&(l=s.tokenSeparators[a],u=e.indexOf(l),!(u>=0));a++);if(0>u)break;if(o=e.substring(0,u),e=e.substring(u+l.length),o.length>0&&(o=s.createSearchChoice.call(this,o,n),o!==t&&null!==o&&s.id(o)!==t&&null!==s.id(o))){for(h=!1,a=0,f=n.length;f>a;a++)if(i(s.id(o),s.id(n[a]))){h=!0;break}h||r(o)}}return c!==e?e:t}function k(t,n){var r=function(){};return r.prototype=new t,r.prototype.constructor=r,r.prototype.parent=t.prototype,r.prototype=e.extend(r.prototype,n),r}if(window.Select2===t){var L,A,O,M,_,D,P,H,B={x:0,y:0},L={TAB:9,ENTER:13,ESC:27,SPACE:32,LEFT:37,UP:38,RIGHT:39,DOWN:40,SHIFT:16,CTRL:17,ALT:18,PAGE_UP:33,PAGE_DOWN:34,HOME:36,END:35,BACKSPACE:8,DELETE:46,isArrow:function(e){switch(e=e.which?e.which:e){case L.LEFT:case L.RIGHT:case L.UP:case L.DOWN:return!0}return!1},isControl:function(e){var t=e.which;switch(t){case L.SHIFT:case L.CTRL:case L.ALT:return!0}return e.metaKey?!0:!1},isFunctionKey:function(e){return e=e.which?e.which:e,e>=112&&123>=e}},j="<div class='select2-measure-scrollbar'></div>";P=e(document),_=function(){var e=1;return function(){return e++}}(),P.on("mousemove",function(e){B.x=e.pageX,B.y=e.pageY}),A=k(Object,{bind:function(e){var t=this;return function(){e.apply(t,arguments)}},init:function(n){var i,s,o,f,h=".select2-results";this.opts=n=this.prepareOpts(n),this.id=n.id,n.element.data("select2")!==t&&null!==n.element.data("select2")&&n.element.data("select2").destroy(),this.container=this.createContainer(),this.containerId="s2id_"+(n.element.attr("id")||"autogen"+_()),this.containerSelector="#"+this.containerId.replace(/([;&,\.\+\*\~':"\!\^#$%@\[\]\(\)=>\|])/g,"\\$1"),this.container.attr("id",this.containerId),this.body=l(function(){return n.element.closest("body")}),g(this.container,this.opts.element,this.opts.adaptContainerCssClass),this.container.css(T(n.containerCss)),this.container.addClass(T(n.containerCssClass)),this.elementTabIndex=this.opts.element.attr("tabindex"),this.opts.element.data("select2",this).attr("tabindex","-1").before(this.container),this.container.data("select2",this),this.dropdown=this.container.find(".select2-drop"),this.dropdown.addClass(T(n.dropdownCssClass)),this.dropdown.data("select2",this),this.results=i=this.container.find(h),this.search=s=this.container.find("input.select2-input"),this.resultsPage=0,this.context=null,this.initContainer(),a(this.results),this.dropdown.on("mousemove-filtered touchstart touchmove touchend",h,this.bind(this.highlightUnderEvent)),c(80,this.results),this.dropdown.on("scroll-debounced",h,this.bind(this.loadMoreIfNeeded)),e(this.container).on("change",".select2-input",function(e){e.stopPropagation()}),e(this.dropdown).on("change",".select2-input",function(e){e.stopPropagation()}),e.fn.mousewheel&&i.mousewheel(function(e,t,n,r){var s=i.scrollTop();r>0&&0>=s-r?(i.scrollTop(0),d(e)):0>r&&i.get(0).scrollHeight-i.scrollTop()+r<=i.height()&&(i.scrollTop(i.get(0).scrollHeight-i.height()),d(e))}),u(s),s.on("keyup-change input paste",this.bind(this.updateResults)),s.on("focus",function(){s.addClass("select2-focused")}),s.on("blur",function(){s.removeClass("select2-focused")}),this.dropdown.on("mouseup",h,this.bind(function(t){e(t.target).closest(".select2-result-selectable").length>0&&(this.highlightUnderEvent(t),this.selectHighlighted(t))})),this.dropdown.on("click mouseup mousedown",function(e){e.stopPropagation()}),e.isFunction(this.opts.initSelection)&&(this.initSelection(),this.monitorSource()),null!==n.maximumInputLength&&this.search.attr("maxlength",n.maximumInputLength);var o=n.element.prop("disabled");o===t&&(o=!1),this.enable(!o);var f=n.element.prop("readonly");f===t&&(f=!1),this.readonly(f),H=H||r(),this.autofocus=n.element.prop("autofocus"),n.element.prop("autofocus",!1),this.autofocus&&this.focus()},destroy:function(){var e=this.opts.element,n=e.data("select2");this.propertyObserver&&(delete this.propertyObserver,this.propertyObserver=null),n!==t&&(n.container.remove(),n.dropdown.remove(),e.removeClass("select2-offscreen").removeData("select2").off(".select2").prop("autofocus",this.autofocus||!1),this.elementTabIndex?e.attr({tabindex:this.elementTabIndex}):e.removeAttr("tabindex"),e.show())},optionToData:function(e){return e.is("option")?{id:e.prop("value"),text:e.text(),element:e.get(),css:e.attr("class"),disabled:e.prop("disabled"),locked:i(e.attr("locked"),"locked")||i(e.data("locked"),!0)}:e.is("optgroup")?{text:e.attr("label"),children:[],element:e.get(),css:e.attr("class")}:t},prepareOpts:function(n){var r,o,u,a,f=this;if(r=n.element,"select"===r.get(0).tagName.toLowerCase()&&(this.select=o=n.element),o&&e.each(["id","multiple","ajax","query","createSearchChoice","initSelection","data","tags"],function(){if(this in n)throw Error("Option '"+this+"' is not allowed for Select2 when attached to a <select> element.")}),n=e.extend({},{populateResults:function(r,i,s){var o,u=this.opts.id;o=function(r,i,a){var l,c,h,p,d,v,m,g,y,b;for(r=n.sortResults(r,i,s),l=0,c=r.length;c>l;l+=1)h=r[l],d=h.disabled===!0,p=!d&&u(h)!==t,v=h.children&&h.children.length>0,m=e("<li></li>"),m.addClass("select2-results-dept-"+a),m.addClass("select2-result"),m.addClass(p?"select2-result-selectable":"select2-result-unselectable"),d&&m.addClass("select2-disabled"),v&&m.addClass("select2-result-with-children"),m.addClass(f.opts.formatResultCssClass(h)),g=e(document.createElement("div")),g.addClass("select2-result-label"),b=n.formatResult(h,g,s,f.opts.escapeMarkup),b!==t&&g.html(b),m.append(g),v&&(y=e("<ul></ul>"),y.addClass("select2-result-sub"),o(h.children,y,a+1),m.append(y)),m.data("select2-data",h),i.append(m)},o(i,r,0)}},e.fn.select2.defaults,n),"function"!=typeof n.id&&(u=n.id,n.id=function(e){return e[u]}),e.isArray(n.element.data("select2Tags"))){if("tags"in n)throw"tags specified as both an attribute 'data-select2-tags' and in options of Select2 "+n.element.attr("id");n.tags=n.element.data("select2Tags")}if(o?(n.query=this.bind(function(e){var n,i,s,o={results:[],more:!1},u=e.term;s=function(t,n){var r;t.is("option")?e.matcher(u,t.text(),t)&&n.push(f.optionToData(t)):t.is("optgroup")&&(r=f.optionToData(t),t.children().each2(function(e,t){s(t,r.children)}),r.children.length>0&&n.push(r))},n=r.children(),this.getPlaceholder()!==t&&n.length>0&&(i=this.getPlaceholderOption(),i&&(n=n.not(i))),n.each2(function(e,t){s(t,o.results)}),e.callback(o)}),n.id=function(e){return e.id},n.formatResultCssClass=function(e){return e.css}):"query"in n||("ajax"in n?(a=n.element.data("ajax-url"),a&&a.length>0&&(n.ajax.url=a),n.query=w.call(n.element,n.ajax)):"data"in n?n.query=E(n.data):"tags"in n&&(n.query=S(n.tags),n.createSearchChoice===t&&(n.createSearchChoice=function(e){return{id:e,text:e}}),n.initSelection===t&&(n.initSelection=function(r,o){var u=[];e(s(r.val(),n.separator)).each(function(){var r=this,s=this,o=n.tags;e.isFunction(o)&&(o=o()),e(o).each(function(){return i(this.id,r)?(s=this.text,!1):t}),u.push({id:r,text:s})}),o(u)}))),"function"!=typeof n.query)throw"query function not defined for Select2 "+n.element.attr("id");return n},monitorSource:function(){var e,n=this.opts.element;n.on("change.select2",this.bind(function(){this.opts.element.data("select2-change-triggered")!==!0&&this.initSelection()})),e=this.bind(function(){var e,r=n.prop("disabled");r===t&&(r=!1),this.enable(!r);var e=n.prop("readonly");e===t&&(e=!1),this.readonly(e),g(this.container,this.opts.element,this.opts.adaptContainerCssClass),this.container.addClass(T(this.opts.containerCssClass)),g(this.dropdown,this.opts.element,this.opts.adaptDropdownCssClass),this.dropdown.addClass(T(this.opts.dropdownCssClass))}),n.on("propertychange.select2 DOMAttrModified.select2",e),this.mutationCallback===t&&(this.mutationCallback=function(t){t.forEach(e)}),"undefined"!=typeof WebKitMutationObserver&&(this.propertyObserver&&(delete this.propertyObserver,this.propertyObserver=null),this.propertyObserver=new WebKitMutationObserver(this.mutationCallback),this.propertyObserver.observe(n.get(0),{attributes:!0,subtree:!1}))},triggerSelect:function(t){var n=e.Event("select2-selecting",{val:this.id(t),object:t});return this.opts.element.trigger(n),!n.isDefaultPrevented()},triggerChange:function(t){t=t||{},t=e.extend({},t,{type:"change",val:this.val()}),this.opts.element.data("select2-change-triggered",!0),this.opts.element.trigger(t),this.opts.element.data("select2-change-triggered",!1),this.opts.element.click(),this.opts.blurOnChange&&this.opts.element.blur()},isInterfaceEnabled:function(){return this.enabledInterface===!0},enableInterface:function(){var e=this._enabled&&!this._readonly,t=!e;return e===this.enabledInterface?!1:(this.container.toggleClass("select2-container-disabled",t),this.close(),this.enabledInterface=e,!0)},enable:function(e){return e===t&&(e=!0),this._enabled===e?!1:(this._enabled=e,this.opts.element.prop("disabled",!e),this.enableInterface(),!0)},readonly:function(e){return e===t&&(e=!1),this._readonly===e?!1:(this._readonly=e,this.opts.element.prop("readonly",e),this.enableInterface(),!0)},opened:function(){return this.container.hasClass("select2-dropdown-open")},positionDropdown:function(){var t,n,r,i,s=this.dropdown,o=this.container.offset(),u=this.container.outerHeight(!1),a=this.container.outerWidth(!1),f=s.outerHeight(!1),l=e(window).scrollLeft()+e(window).width(),c=e(window).scrollTop()+e(window).height(),h=o.top+u,p=o.left,d=c>=h+f,v=o.top-f>=this.body().scrollTop(),m=s.outerWidth(!1),g=l>=p+m,y=s.hasClass("select2-drop-above");this.opts.dropdownAutoWidth?(i=e(".select2-results",s)[0],s.addClass("select2-drop-auto-width"),s.css("width",""),m=s.outerWidth(!1)+(i.scrollHeight===i.clientHeight?0:H.width),m>a?a=m:m=a,g=l>=p+m):this.container.removeClass("select2-drop-auto-width"),"static"!==this.body().css("position")&&(t=this.body().offset(),h-=t.top,p-=t.left),y?(n=!0,!v&&d&&(n=!1)):(n=!1,!d&&v&&(n=!0)),g||(p=o.left+a-m),n?(h=o.top-f,this.container.addClass("select2-drop-above"),s.addClass("select2-drop-above")):(this.container.removeClass("select2-drop-above"),s.removeClass("select2-drop-above")),r=e.extend({top:h,left:p,width:a},T(this.opts.dropdownCss)),s.css(r)},shouldOpen:function(){var t;return this.opened()?!1:this._enabled===!1||this._readonly===!0?!1:(t=e.Event("select2-opening"),this.opts.element.trigger(t),!t.isDefaultPrevented())},clearDropdownAlignmentPreference:function(){this.container.removeClass("select2-drop-above"),this.dropdown.removeClass("select2-drop-above")},open:function(){return this.shouldOpen()?(this.opening(),!0):!1},opening:function(){function t(){return{width:Math.max(document.documentElement.scrollWidth,e(window).width()),height:Math.max(document.documentElement.scrollHeight,e(window).height())}}var n,r,i=this.containerId,s="scroll."+i,o="resize."+i,u="orientationchange."+i;this.container.addClass("select2-dropdown-open").addClass("select2-container-active"),this.clearDropdownAlignmentPreference(),this.dropdown[0]!==this.body().children().last()[0]&&this.dropdown.detach().appendTo(this.body()),n=e("#select2-drop-mask"),0==n.length&&(n=e(document.createElement("div")),n.attr("id","select2-drop-mask").attr("class","select2-drop-mask"),n.hide(),n.appendTo(this.body()),n.on("mousedown touchstart click",function(t){var n,r=e("#select2-drop");r.length>0&&(n=r.data("select2"),n.opts.selectOnBlur&&n.selectHighlighted({noFocus:!0}),n.close(),t.preventDefault(),t.stopPropagation())})),this.dropdown.prev()[0]!==n[0]&&this.dropdown.before(n),e("#select2-drop").removeAttr("id"),this.dropdown.attr("id","select2-drop"),r=t(),n.css(r).show(),this.dropdown.show(),this.positionDropdown(),this.dropdown.addClass("select2-drop-active");var a=this;this.container.parents().add(window).each(function(){e(this).on(o+" "+s+" "+u,function(){var n=t();e("#select2-drop-mask").css(n),a.positionDropdown()})})},close:function(){if(this.opened()){var t=this.containerId,n="scroll."+t,r="resize."+t,i="orientationchange."+t;this.container.parents().add(window).each(function(){e(this).off(n).off(r).off(i)}),this.clearDropdownAlignmentPreference(),e("#select2-drop-mask").hide(),this.dropdown.removeAttr("id"),this.dropdown.hide(),this.container.removeClass("select2-dropdown-open"),this.results.empty(),this.clearSearch(),this.search.removeClass("select2-active"),this.opts.element.trigger(e.Event("select2-close"))}},externalSearch:function(e){this.open(),this.search.val(e),this.updateResults(!1)},clearSearch:function(){},getMaximumSelectionSize:function(){return T(this.opts.maximumSelectionSize)},ensureHighlightVisible:function(){var n,r,i,s,o,u,a,f=this.results;if(r=this.highlight(),!(0>r)){if(0==r)return f.scrollTop(0),t;n=this.findHighlightableChoices().find(".select2-result-label"),i=e(n[r]),s=i.offset().top+i.outerHeight(!0),r===n.length-1&&(a=f.find("li.select2-more-results"),a.length>0&&(s=a.offset().top+a.outerHeight(!0))),o=f.offset().top+f.outerHeight(!0),s>o&&f.scrollTop(f.scrollTop()+(s-o)),u=i.offset().top-f.offset().top,0>u&&"none"!=i.css("display")&&f.scrollTop(f.scrollTop()+u)}},findHighlightableChoices:function(){return this.results.find(".select2-result-selectable:not(.select2-selected):not(.select2-disabled)")},moveHighlight:function(t){for(var n=this.findHighlightableChoices(),r=this.highlight();r>-1&&n.length>r;){r+=t;var i=e(n[r]);if(i.hasClass("select2-result-selectable")&&!i.hasClass("select2-disabled")&&!i.hasClass("select2-selected")){this.highlight(r);break}}},highlight:function(r){var i,s,o=this.findHighlightableChoices();return 0===arguments.length?n(o.filter(".select2-highlighted")[0],o.get()):(r>=o.length&&(r=o.length-1),0>r&&(r=0),this.results.find(".select2-highlighted").removeClass("select2-highlighted"),i=e(o[r]),i.addClass("select2-highlighted"),this.ensureHighlightVisible(),s=i.data("select2-data"),s&&this.opts.element.trigger({type:"select2-highlight",val:this.id(s),choice:s}),t)},countSelectableResults:function(){return this.findHighlightableChoices().length},highlightUnderEvent:function(t){var n=e(t.target).closest(".select2-result-selectable");if(n.length>0&&!n.is(".select2-highlighted")){var r=this.findHighlightableChoices();this.highlight(r.index(n))}else 0==n.length&&this.results.find(".select2-highlighted").removeClass("select2-highlighted")},loadMoreIfNeeded:function(){var e,t=this.results,n=t.find("li.select2-more-results"),r=this.resultsPage+1,i=this,s=this.search.val(),o=this.context;0!==n.length&&(e=n.offset().top-t.offset().top-t.height(),this.opts.loadMorePadding>=e&&(n.addClass("select2-active"),this.opts.query({element:this.opts.element,term:s,page:r,context:o,matcher:this.opts.matcher,callback:this.bind(function(e){i.opened()&&(i.opts.populateResults.call(this,t,e.results,{term:s,page:r,context:o}),i.postprocessResults(e,!1,!1),e.more===!0?(n.detach().appendTo(t).text(i.opts.formatLoadMore(r+1)),window.setTimeout(function(){i.loadMoreIfNeeded()},10)):n.remove(),i.positionDropdown(),i.resultsPage=r,i.context=e.context)})})))},tokenize:function(){},updateResults:function(n){function r(){a.removeClass("select2-active"),c.positionDropdown()}function s(e){f.html(e),r()}var o,u,a=this.search,f=this.results,l=this.opts,c=this,h=a.val(),p=e.data(this.container,"select2-last-term");if((n===!0||!p||!i(h,p))&&(e.data(this.container,"select2-last-term",h),n===!0||this.showSearchInput!==!1&&this.opened())){var d=this.getMaximumSelectionSize();if(d>=1&&(o=this.data(),e.isArray(o)&&o.length>=d&&x(l.formatSelectionTooBig,"formatSelectionTooBig")))return s("<li class='select2-selection-limit'>"+l.formatSelectionTooBig(d)+"</li>"),t;if(a.val().length<l.minimumInputLength)return x(l.formatInputTooShort,"formatInputTooShort")?s("<li class='select2-no-results'>"+l.formatInputTooShort(a.val(),l.minimumInputLength)+"</li>"):s(""),n&&this.showSearch&&this.showSearch(!0),t;if(l.maximumInputLength&&a.val().length>l.maximumInputLength)return x(l.formatInputTooLong,"formatInputTooLong")?s("<li class='select2-no-results'>"+l.formatInputTooLong(a.val(),l.maximumInputLength)+"</li>"):s(""),t;l.formatSearching&&0===this.findHighlightableChoices().length&&s("<li class='select2-searching'>"+l.formatSearching()+"</li>"),a.addClass("select2-active"),u=this.tokenize(),u!=t&&null!=u&&a.val(u),this.resultsPage=1,l.query({element:l.element,term:a.val(),page:this.resultsPage,context:null,matcher:l.matcher,callback:this.bind(function(o){var u;return this.opened()?(this.context=o.context===t?null:o.context,this.opts.createSearchChoice&&""!==a.val()&&(u=this.opts.createSearchChoice.call(c,a.val(),o.results),u!==t&&null!==u&&c.id(u)!==t&&null!==c.id(u)&&0===e(o.results).filter(function(){return i(c.id(this),c.id(u))}).length&&o.results.unshift(u)),0===o.results.length&&x(l.formatNoMatches,"formatNoMatches")?(s("<li class='select2-no-results'>"+l.formatNoMatches(a.val())+"</li>"),t):(f.empty(),c.opts.populateResults.call(this,f,o.results,{term:a.val(),page:this.resultsPage,context:null}),o.more===!0&&x(l.formatLoadMore,"formatLoadMore")&&(f.append("<li class='select2-more-results'>"+c.opts.escapeMarkup(l.formatLoadMore(this.resultsPage))+"</li>"),window.setTimeout(function(){c.loadMoreIfNeeded()},10)),this.postprocessResults(o,n),r(),this.opts.element.trigger({type:"select2-loaded",items:o}),t)):(this.search.removeClass("select2-active"),t)})})}},cancel:function(){this.close()},blur:function(){this.opts.selectOnBlur&&this.selectHighlighted({noFocus:!0}),this.close(),this.container.removeClass("select2-container-active"),this.search[0]===document.activeElement&&this.search.blur(),this.clearSearch(),this.selection.find(".select2-search-choice-focus").removeClass("select2-search-choice-focus")},focusSearch:function(){h(this.search)},selectHighlighted:function(e){var t=this.highlight(),n=this.results.find(".select2-highlighted"),r=n.closest(".select2-result").data("select2-data");r?(this.highlight(t),this.onSelect(r,e)):e&&e.noFocus&&this.close()},getPlaceholder:function(){var e;return this.opts.element.attr("placeholder")||this.opts.element.attr("data-placeholder")||this.opts.element.data("placeholder")||this.opts.placeholder||((e=this.getPlaceholderOption())!==t?e.text():t)},getPlaceholderOption:function(){if(this.select){var e=this.select.children().first();if(this.opts.placeholderOption!==t)return"first"===this.opts.placeholderOption&&e||"function"==typeof this.opts.placeholderOption&&this.opts.placeholderOption(this.select);if(""===e.text()&&""===e.val())return e}},initContainerWidth:function(){function n(){var n,r,i,s,o;if("off"===this.opts.width)return null;if("element"===this.opts.width)return 0===this.opts.element.outerWidth(!1)?"auto":this.opts.element.outerWidth(!1)+"px";if("copy"===this.opts.width||"resolve"===this.opts.width){if(n=this.opts.element.attr("style"),n!==t)for(r=n.split(";"),s=0,o=r.length;o>s;s+=1)if(i=r[s].replace(/\s/g,"").match(/width:(([-+]?([0-9]*\.)?[0-9]+)(px|em|ex|%|in|cm|mm|pt|pc))/i),null!==i&&i.length>=1)return i[1];return"resolve"===this.opts.width?(n=this.opts.element.css("width"),n.indexOf("%")>0?n:0===this.opts.element.outerWidth(!1)?"auto":this.opts.element.outerWidth(!1)+"px"):null}return e.isFunction(this.opts.width)?this.opts.width():this.opts.width}var r=n.call(this);null!==r&&this.container.css("width",r)}}),O=k(A,{createContainer:function(){var t=e(document.createElement("div")).attr({"class":"select2-container"}).html(["<a href='javascript:void(0)' onclick='return false;' class='select2-choice' tabindex='-1'>","   <span class='select2-chosen'>&nbsp;</span><abbr class='select2-search-choice-close'></abbr>","   <span class='select2-arrow'><b></b></span>","</a>","<input class='select2-focusser select2-offscreen' type='text'/>","<div class='select2-drop select2-display-none'>","   <div class='select2-search'>","       <input type='text' autocomplete='off' autocorrect='off' autocapitalize='off' spellcheck='false' class='select2-input'/>","   </div>","   <ul class='select2-results'>","   </ul>","</div>"].join(""));return t},enableInterface:function(){this.parent.enableInterface.apply(this,arguments)&&this.focusser.prop("disabled",!this.isInterfaceEnabled())},opening:function(){var t,n,r;this.opts.minimumResultsForSearch>=0&&this.showSearch(!0),this.parent.opening.apply(this,arguments),this.showSearchInput!==!1&&this.search.val(this.focusser.val()),this.search.focus(),t=this.search.get(0),t.createTextRange?(n=t.createTextRange(),n.collapse(!1),n.select()):t.setSelectionRange&&(r=this.search.val().length,t.setSelectionRange(r,r)),this.focusser.prop("disabled",!0).val(""),this.updateResults(!0),this.opts.element.trigger(e.Event("select2-open"))},close:function(){this.opened()&&(this.parent.close.apply(this,arguments),this.focusser.removeAttr("disabled"),this.focusser.focus())},focus:function(){this.opened()?this.close():(this.focusser.removeAttr("disabled"),this.focusser.focus())},isFocused:function(){return this.container.hasClass("select2-container-active")},cancel:function(){this.parent.cancel.apply(this,arguments),this.focusser.removeAttr("disabled"),this.focusser.focus()},initContainer:function(){var n,r=this.container,i=this.dropdown;0>this.opts.minimumResultsForSearch?this.showSearch(!1):this.showSearch(!0),this.selection=n=r.find(".select2-choice"),this.focusser=r.find(".select2-focusser"),this.focusser.attr("id","s2id_autogen"+_()),e("label[for='"+this.opts.element.attr("id")+"']").attr("for",this.focusser.attr("id")),this.focusser.attr("tabindex",this.elementTabIndex),this.search.on("keydown",this.bind(function(e){if(this.isInterfaceEnabled()){if(e.which===L.PAGE_UP||e.which===L.PAGE_DOWN)return d(e),t;switch(e.which){case L.UP:case L.DOWN:return this.moveHighlight(e.which===L.UP?-1:1),d(e),t;case L.ENTER:return this.selectHighlighted(),d(e),t;case L.TAB:return this.selectHighlighted({noFocus:!0}),t;case L.ESC:return this.cancel(e),d(e),t}}})),this.search.on("blur",this.bind(function(){document.activeElement===this.body().get(0)&&window.setTimeout(this.bind(function(){this.search.focus()}),0)})),this.focusser.on("keydown",this.bind(function(e){if(this.isInterfaceEnabled()&&e.which!==L.TAB&&!L.isControl(e)&&!L.isFunctionKey(e)&&e.which!==L.ESC){if(this.opts.openOnEnter===!1&&e.which===L.ENTER)return d(e),t;if(e.which==L.DOWN||e.which==L.UP||e.which==L.ENTER&&this.opts.openOnEnter){if(e.altKey||e.ctrlKey||e.shiftKey||e.metaKey)return;return this.open(),d(e),t}return e.which==L.DELETE||e.which==L.BACKSPACE?(this.opts.allowClear&&this.clear(),d(e),t):t}})),u(this.focusser),this.focusser.on("keyup-change input",this.bind(function(e){if(this.opts.minimumResultsForSearch>=0){if(e.stopPropagation(),this.opened())return;this.open()}})),n.on("mousedown","abbr",this.bind(function(e){this.isInterfaceEnabled()&&(this.clear(),v(e),this.close(),this.selection.focus())})),n.on("mousedown",this.bind(function(t){this.container.hasClass("select2-container-active")||this.opts.element.trigger(e.Event("select2-focus")),this.opened()?this.close():this.isInterfaceEnabled()&&this.open(),d(t)})),i.on("mousedown",this.bind(function(){this.search.focus()})),n.on("focus",this.bind(function(e){d(e)})),this.focusser.on("focus",this.bind(function(){this.container.hasClass("select2-container-active")||this.opts.element.trigger(e.Event("select2-focus")),this.container.addClass("select2-container-active")})).on("blur",this.bind(function(){this.opened()||(this.container.removeClass("select2-container-active"),this.opts.element.trigger(e.Event("select2-blur")))})),this.search.on("focus",this.bind(function(){this.container.hasClass("select2-container-active")||this.opts.element.trigger(e.Event("select2-focus")),this.container.addClass("select2-container-active")})),this.initContainerWidth(),this.opts.element.addClass("select2-offscreen"),this.setPlaceholder()},clear:function(e){var t=this.selection.data("select2-data");if(t){var n=this.getPlaceholderOption();this.opts.element.val(n?n.val():""),this.selection.find(".select2-chosen").empty(),this.selection.removeData("select2-data"),this.setPlaceholder(),e!==!1&&(this.opts.element.trigger({type:"select2-removed",val:this.id(t),choice:t}),this.triggerChange({removed:t}))}},initSelection:function(){if(this.isPlaceholderOptionSelected())this.updateSelection([]),this.close(),this.setPlaceholder();else{var e=this;this.opts.initSelection.call(null,this.opts.element,function(n){n!==t&&null!==n&&(e.updateSelection(n),e.close(),e.setPlaceholder())})}},isPlaceholderOptionSelected:function(){var e;return(e=this.getPlaceholderOption())!==t&&e.is(":selected")||""===this.opts.element.val()||this.opts.element.val()===t||null===this.opts.element.val()},prepareOpts:function(){var t=this.parent.prepareOpts.apply(this,arguments),n=this;return"select"===t.element.get(0).tagName.toLowerCase()?t.initSelection=function(e,t){var r=e.find(":selected");t(n.optionToData(r))}:"data"in t&&(t.initSelection=t.initSelection||function(n,r){var s=n.val(),o=null;t.query({matcher:function(e,n,r){var u=i(s,t.id(r));return u&&(o=r),u},callback:e.isFunction(r)?function(){r(o)}:e.noop})}),t},getPlaceholder:function(){return this.select&&this.getPlaceholderOption()===t?t:this.parent.getPlaceholder.apply(this,arguments)},setPlaceholder:function(){var e=this.getPlaceholder();if(this.isPlaceholderOptionSelected()&&e!==t){if(this.select&&this.getPlaceholderOption()===t)return;this.selection.find(".select2-chosen").html(this.opts.escapeMarkup(e)),this.selection.addClass("select2-default"),this.container.removeClass("select2-allowclear")}},postprocessResults:function(e,n,r){var s=0,o=this;if(this.findHighlightableChoices().each2(function(e,n){return i(o.id(n.data("select2-data")),o.opts.element.val())?(s=e,!1):t}),r!==!1&&(n===!0&&s>=0?this.highlight(s):this.highlight(0)),n===!0){var u=this.opts.minimumResultsForSearch;u>=0&&this.showSearch(N(e.results)>=u)}},showSearch:function(t){this.showSearchInput!==t&&(this.showSearchInput=t,this.dropdown.find(".select2-search").toggleClass("select2-search-hidden",!t),this.dropdown.find(".select2-search").toggleClass("select2-offscreen",!t),e(this.dropdown,this.container).toggleClass("select2-with-searchbox",t))},onSelect:function(e,t){if(this.triggerSelect(e)){var n=this.opts.element.val(),r=this.data();this.opts.element.val(this.id(e)),this.updateSelection(e),this.opts.element.trigger({type:"select2-selected",val:this.id(e),choice:e}),this.close(),t&&t.noFocus||this.selection.focus(),i(n,this.id(e))||this.triggerChange({added:e,removed:r})}},updateSelection:function(e){var n,r,i=this.selection.find(".select2-chosen");this.selection.data("select2-data",e),i.empty(),n=this.opts.formatSelection(e,i,this.opts.escapeMarkup),n!==t&&i.append(n),r=this.opts.formatSelectionCssClass(e,i),r!==t&&i.addClass(r),this.selection.removeClass("select2-default"),this.opts.allowClear&&this.getPlaceholder()!==t&&this.container.addClass("select2-allowclear")},val:function(){var e,n=!1,r=null,i=this,s=this.data();if(0===arguments.length)return this.opts.element.val();if(e=arguments[0],arguments.length>1&&(n=arguments[1]),this.select)this.select.val(e).find(":selected").each2(function(e,t){return r=i.optionToData(t),!1}),this.updateSelection(r),this.setPlaceholder(),n&&this.triggerChange({added:r,removed:s});else{if(!e&&0!==e)return this.clear(n),t;if(this.opts.initSelection===t)throw Error("cannot call val() if initSelection() is not defined");this.opts.element.val(e),this.opts.initSelection(this.opts.element,function(e){i.opts.element.val(e?i.id(e):""),i.updateSelection(e),i.setPlaceholder(),n&&i.triggerChange({added:e,removed:s})})}},clearSearch:function(){this.search.val(""),this.focusser.val("")},data:function(e,n){var r;return 0===arguments.length?(r=this.selection.data("select2-data"),r==t&&(r=null),r):(e&&""!==e?(r=this.data(),this.opts.element.val(e?this.id(e):""),this.updateSelection(e),n&&this.triggerChange({added:e,removed:r})):this.clear(n),t)}}),M=k(A,{createContainer:function(){var t=e(document.createElement("div")).attr({"class":"select2-container select2-container-multi"}).html(["<ul class='select2-choices'>","  <li class='select2-search-field'>","    <input type='text' autocomplete='off' autocorrect='off' autocapitilize='off' spellcheck='false' class='select2-input'>","  </li>","</ul>","<div class='select2-drop select2-drop-multi select2-display-none'>","   <ul class='select2-results'>","   </ul>","</div>"].join(""));return t},prepareOpts:function(){var t=this.parent.prepareOpts.apply(this,arguments),n=this;return"select"===t.element.get(0).tagName.toLowerCase()?t.initSelection=function(e,t){var r=[];e.find(":selected").each2(function(e,t){r.push(n.optionToData(t))}),t(r)}:"data"in t&&(t.initSelection=t.initSelection||function(n,r){var o=s(n.val(),t.separator),u=[];t.query({matcher:function(n,r,s){var a=e.grep(o,function(e){return i(e,t.id(s))}).length;return a&&u.push(s),a},callback:e.isFunction(r)?function(){for(var e=[],n=0;o.length>n;n++)for(var s=o[n],a=0;u.length>a;a++){var f=u[a];if(i(s,t.id(f))){e.push(f),u.splice(a,1);break}}r(e)}:e.noop})}),t},selectChoice:function(e){var t=this.container.find(".select2-search-choice-focus");t.length&&e&&e[0]==t[0]||(t.length&&this.opts.element.trigger("choice-deselected",t),t.removeClass("select2-search-choice-focus"),e&&e.length&&(this.close(),e.addClass("select2-search-choice-focus"),this.opts.element.trigger("choice-selected",e)))},initContainer:function(){var n,r=".select2-choices";this.searchContainer=this.container.find(".select2-search-field"),this.selection=n=this.container.find(r);var i=this;this.selection.on("mousedown",".select2-search-choice",function(){i.search[0].focus(),i.selectChoice(e(this))}),this.search.attr("id","s2id_autogen"+_()),e("label[for='"+this.opts.element.attr("id")+"']").attr("for",this.search.attr("id")),this.search.on("input paste",this.bind(function(){this.isInterfaceEnabled()&&(this.opened()||this.open())})),this.search.attr("tabindex",this.elementTabIndex),this.keydowns=0,this.search.on("keydown",this.bind(function(e){if(this.isInterfaceEnabled()){++this.keydowns;var r=n.find(".select2-search-choice-focus"),i=r.prev(".select2-search-choice:not(.select2-locked)"),s=r.next(".select2-search-choice:not(.select2-locked)"),o=p(this.search);if(!(!r.length||e.which!=L.LEFT&&e.which!=L.RIGHT&&e.which!=L.BACKSPACE&&e.which!=L.DELETE&&e.which!=L.ENTER)){var u=r;return e.which==L.LEFT&&i.length?u=i:e.which==L.RIGHT?u=s.length?s:null:e.which===L.BACKSPACE?(this.unselect(r.first()),this.search.width(10),u=i.length?i:s):e.which==L.DELETE?(this.unselect(r.first()),this.search.width(10),u=s.length?s:null):e.which==L.ENTER&&(u=null),this.selectChoice(u),d(e),u&&u.length||this.open(),t}if((e.which===L.BACKSPACE&&1==this.keydowns||e.which==L.LEFT)&&0==o.offset&&!o.length)return this.selectChoice(n.find(".select2-search-choice:not(.select2-locked)").last()),d(e),t;if(this.selectChoice(null),this.opened())switch(e.which){case L.UP:case L.DOWN:return this.moveHighlight(e.which===L.UP?-1:1),d(e),t;case L.ENTER:return this.selectHighlighted(),d(e),t;case L.TAB:return this.selectHighlighted({noFocus:!0}),this.close(),t;case L.ESC:return this.cancel(e),d(e),t}if(e.which!==L.TAB&&!L.isControl(e)&&!L.isFunctionKey(e)&&e.which!==L.BACKSPACE&&e.which!==L.ESC){if(e.which===L.ENTER){if(this.opts.openOnEnter===!1)return;if(e.altKey||e.ctrlKey||e.shiftKey||e.metaKey)return}this.open(),(e.which===L.PAGE_UP||e.which===L.PAGE_DOWN)&&d(e),e.which===L.ENTER&&d(e)}}})),this.search.on("keyup",this.bind(function(){this.keydowns=0,this.resizeSearch()})),this.search.on("blur",this.bind(function(t){this.container.removeClass("select2-container-active"),this.search.removeClass("select2-focused"),this.selectChoice(null),this.opened()||this.clearSearch(),t.stopImmediatePropagation(),this.opts.element.trigger(e.Event("select2-blur"))})),this.container.on("click",r,this.bind(function(t){this.isInterfaceEnabled()&&(e(t.target).closest(".select2-search-choice").length>0||(this.selectChoice(null),this.clearPlaceholder(),this.container.hasClass("select2-container-active")||this.opts.element.trigger(e.Event("select2-focus")),this.open(),this.focusSearch(),t.preventDefault()))})),this.container.on("focus",r,this.bind(function(){this.isInterfaceEnabled()&&(this.container.hasClass("select2-container-active")||this.opts.element.trigger(e.Event("select2-focus")),this.container.addClass("select2-container-active"),this.dropdown.addClass("select2-drop-active"),this.clearPlaceholder())})),this.initContainerWidth(),this.opts.element.addClass("select2-offscreen"),this.clearSearch()},enableInterface:function(){this.parent.enableInterface.apply(this,arguments)&&this.search.prop("disabled",!this.isInterfaceEnabled())},initSelection:function(){if(""===this.opts.element.val()&&""===this.opts.element.text()&&(this.updateSelection([]),this.close(),this.clearSearch()),this.select||""!==this.opts.element.val()){var e=this;this.opts.initSelection.call(null,this.opts.element,function(n){n!==t&&null!==n&&(e.updateSelection(n),e.close(),e.clearSearch())})}},clearSearch:function(){var e=this.getPlaceholder(),n=this.getMaxSearchWidth();e!==t&&0===this.getVal().length&&this.search.hasClass("select2-focused")===!1?(this.search.val(e).addClass("select2-default"),this.search.width(n>0?n:this.container.css("width"))):this.search.val("").width(10)},clearPlaceholder:function(){this.search.hasClass("select2-default")&&this.search.val("").removeClass("select2-default")},opening:function(){this.clearPlaceholder(),this.resizeSearch(),this.parent.opening.apply(this,arguments),this.focusSearch(),this.updateResults(!0),this.search.focus(),this.opts.element.trigger(e.Event("select2-open"))},close:function(){this.opened()&&this.parent.close.apply(this,arguments)},focus:function(){this.close(),this.search.focus()},isFocused:function(){return this.search.hasClass("select2-focused")},updateSelection:function(t){var r=[],i=[],s=this;e(t).each(function(){0>n(s.id(this),r)&&(r.push(s.id(this)),i.push(this))}),t=i,this.selection.find(".select2-search-choice").remove(),e(t).each(function(){s.addSelectedChoice(this)}),s.postprocessResults()},tokenize:function(){var e=this.search.val();e=this.opts.tokenizer.call(this,e,this.data(),this.bind(this.onSelect),this.opts),null!=e&&e!=t&&(this.search.val(e),e.length>0&&this.open())},onSelect:function(e,t){this.triggerSelect(e)&&(this.addSelectedChoice(e),this.opts.element.trigger({type:"selected",val:this.id(e),choice:e}),(this.select||!this.opts.closeOnSelect)&&this.postprocessResults(),this.opts.closeOnSelect?(this.close(),this.search.width(10)):this.countSelectableResults()>0?(this.search.width(10),this.resizeSearch(),this.getMaximumSelectionSize()>0&&this.val().length>=this.getMaximumSelectionSize()&&this.updateResults(!0),this.positionDropdown()):(this.close(),this.search.width(10)),this.triggerChange({added:e}),t&&t.noFocus||this.focusSearch())},cancel:function(){this.close(),this.focusSearch()},addSelectedChoice:function(n){var r,i,s=!n.locked,o=e("<li class='select2-search-choice'>    <div></div>    <a href='#' onclick='return false;' class='select2-search-choice-close' tabindex='-1'></a></li>"),u=e("<li class='select2-search-choice select2-locked'><div></div></li>"),a=s?o:u,f=this.id(n),l=this.getVal();r=this.opts.formatSelection(n,a.find("div"),this.opts.escapeMarkup),r!=t&&a.find("div").replaceWith("<div>"+r+"</div>"),i=this.opts.formatSelectionCssClass(n,a.find("div")),i!=t&&a.addClass(i),s&&a.find(".select2-search-choice-close").on("mousedown",d).on("click dblclick",this.bind(function(t){this.isInterfaceEnabled()&&(e(t.target).closest(".select2-search-choice").fadeOut("fast",this.bind(function(){this.unselect(e(t.target)),this.selection.find(".select2-search-choice-focus").removeClass("select2-search-choice-focus"),this.close(),this.focusSearch()})).dequeue(),d(t))})).on("focus",this.bind(function(){this.isInterfaceEnabled()&&(this.container.addClass("select2-container-active"),this.dropdown.addClass("select2-drop-active"))})),a.data("select2-data",n),a.insertBefore(this.searchContainer),l.push(f),this.setVal(l)},unselect:function(e){var t,r,i=this.getVal();if(e=e.closest(".select2-search-choice"),0===e.length)throw"Invalid argument: "+e+". Must be .select2-search-choice";t=e.data("select2-data"),t&&(r=n(this.id(t),i),r>=0&&(i.splice(r,1),this.setVal(i),this.select&&this.postprocessResults()),e.remove(),this.opts.element.trigger({type:"removed",val:this.id(t),choice:t}),this.triggerChange({removed:t}))},postprocessResults:function(e,t,r){var i=this.getVal(),s=this.results.find(".select2-result"),o=this.results.find(".select2-result-with-children"),u=this;s.each2(function(e,t){var r=u.id(t.data("select2-data"));n(r,i)>=0&&(t.addClass("select2-selected"),t.find(".select2-result-selectable").addClass("select2-selected"))}),o.each2(function(e,t){t.is(".select2-result-selectable")||0!==t.find(".select2-result-selectable:not(.select2-selected)").length||t.addClass("select2-selected")}),-1==this.highlight()&&r!==!1&&u.highlight(0),!this.opts.createSearchChoice&&!s.filter(".select2-result:not(.select2-selected)").length>0&&(!e||e&&!e.more&&0===this.results.find(".select2-no-results").length)&&x(u.opts.formatNoMatches,"formatNoMatches")&&this.results.append("<li class='select2-no-results'>"+u.opts.formatNoMatches(u.search.val())+"</li>")},getMaxSearchWidth:function(){return this.selection.width()-o(this.search)},resizeSearch:function(){var e,t,n,r,i,s=o(this.search);e=m(this.search)+10,t=this.search.offset().left,n=this.selection.width(),r=this.selection.offset().left,i=n-(t-r)-s,e>i&&(i=n-s),40>i&&(i=n-s),0>=i&&(i=e),this.search.width(i)},getVal:function(){var e;return this.select?(e=this.select.val(),null===e?[]:e):(e=this.opts.element.val(),s(e,this.opts.separator))},setVal:function(t){var r;this.select?this.select.val(t):(r=[],e(t).each(function(){0>n(this,r)&&r.push(this)}),this.opts.element.val(0===r.length?"":r.join(this.opts.separator)))},buildChangeDetails:function(e,t){for(var t=t.slice(0),e=e.slice(0),n=0;t.length>n;n++)for(var r=0;e.length>r;r++)i(this.opts.id(t[n]),this.opts.id(e[r]))&&(t.splice(n,1),n--,e.splice(r,1),r--);return{added:t,removed:e}},val:function(n,r){var i,s=this;if(0===arguments.length)return this.getVal();if(i=this.data(),i.length||(i=[]),!n&&0!==n)return this.opts.element.val(""),this.updateSelection([]),this.clearSearch(),r&&this.triggerChange({added:this.data(),removed:i}),t;if(this.setVal(n),this.select)this.opts.initSelection(this.select,this.bind(this.updateSelection)),r&&this.triggerChange(this.buildChangeDetails(i,this.data()));else{if(this.opts.initSelection===t)throw Error("val() cannot be called if initSelection() is not defined");this.opts.initSelection(this.opts.element,function(t){var n=e.map(t,s.id);s.setVal(n),s.updateSelection(t),s.clearSearch(),r&&s.triggerChange(this.buildChangeDetails(i,this.data()))})}this.clearSearch()},onSortStart:function(){if(this.select)throw Error("Sorting of elements is not supported when attached to <select>. Attach to <input type='hidden'/> instead.");this.search.width(0),this.searchContainer.hide()},onSortEnd:function(){var t=[],n=this;this.searchContainer.show(),this.searchContainer.appendTo(this.searchContainer.parent()),this.resizeSearch(),this.selection.find(".select2-search-choice").each(function(){t.push(n.opts.id(e(this).data("select2-data")))}),this.setVal(t),this.triggerChange()},data:function(n,r){var i,s,o=this;return 0===arguments.length?this.selection.find(".select2-search-choice").map(function(){return e(this).data("select2-data")}).get():(s=this.data(),n||(n=[]),i=e.map(n,function(e){return o.opts.id(e)}),this.setVal(i),this.updateSelection(n),this.clearSearch(),r&&this.triggerChange(this.buildChangeDetails(s,this.data())),t)}}),e.fn.select2=function(){var r,i,s,o,u,a=Array.prototype.slice.call(arguments,0),f=["val","destroy","opened","open","close","focus","isFocused","container","dropdown","onSortStart","onSortEnd","enable","readonly","positionDropdown","data","search"],l=["val","opened","isFocused","container","data"],c={search:"externalSearch"};return this.each(function(){if(0===a.length||"object"==typeof a[0])r=0===a.length?{}:e.extend({},a[0]),r.element=e(this),"select"===r.element.get(0).tagName.toLowerCase()?u=r.element.prop("multiple"):(u=r.multiple||!1,"tags"in r&&(r.multiple=u=!0)),i=u?new M:new O,i.init(r);else{if("string"!=typeof a[0])throw"Invalid arguments to select2 plugin: "+a;if(0>n(a[0],f))throw"Unknown method: "+a[0];if(o=t,i=e(this).data("select2"),i===t)return;if(s=a[0],"container"===s?o=i.container:"dropdown"===s?o=i.dropdown:(c[s]&&(s=c[s]),o=i[s].apply(i,a.slice(1))),n(a[0],l)>=0)return!1}}),o===t?this:o},e.fn.select2.defaults={width:"copy",loadMorePadding:0,closeOnSelect:!0,openOnEnter:!0,containerCss:{},dropdownCss:{},containerCssClass:"",dropdownCssClass:"",formatResult:function(e,t,n,r){var i=[];return y(e.text,n.term,i,r),i.join("")},formatSelection:function(e,n,r){return e?r(e.text):t},sortResults:function(e){return e},formatResultCssClass:function(){return t},formatSelectionCssClass:function(){return t},formatNoMatches:function(){return"No matches found"},formatInputTooShort:function(e,t){var n=t-e.length;return"Please enter "+n+" more character"+(1==n?"":"s")},formatInputTooLong:function(e,t){var n=e.length-t;return"Please delete "+n+" character"+(1==n?"":"s")},formatSelectionTooBig:function(e){return"You can only select "+e+" item"+(1==e?"":"s")},formatLoadMore:function(){return"Loading more results..."},formatSearching:function(){return"Searching..."},minimumResultsForSearch:0,minimumInputLength:0,maximumInputLength:null,maximumSelectionSize:0,id:function(e){return e.id},matcher:function(e,t){return(""+t).toUpperCase().indexOf((""+e).toUpperCase())>=0},separator:",",tokenSeparators:[],tokenizer:C,escapeMarkup:b,blurOnChange:!1,selectOnBlur:!1,adaptContainerCssClass:function(e){return e},adaptDropdownCssClass:function(){return null}},e.fn.select2.ajaxDefaults={transport:e.ajax,params:{type:"GET",cache:!1,dataType:"json"}},window.Select2={query:{ajax:w,local:E,tags:S},util:{debounce:f,markMatch:y,escapeMarkup:b},"class":{"abstract":A,single:O,multi:M}}}}(jQuery),define("select2",["jquery"],function(e){return function(){var t,n;return t||e.select2}}(this)),function(e){function t(e,t,i){var s=e[0],o=/er/.test(i)?v:/bl/.test(i)?p:c;active=i==m?{checked:s[c],disabled:s[p],indeterminate:"true"==e.attr(v)||"false"==e.attr(d)}:s[o];if(/^(ch|di|in)/.test(i)&&!active)n(e,o);else if(/^(un|en|de)/.test(i)&&active)r(e,o);else if(i==m)for(var o in active)active[o]?n(e,o,!0):r(e,o,!0);else if(!t||"toggle"==i)t||e[w]("ifClicked"),active?s[g]!==l&&r(e,o):n(e,o)}function n(t,n,i){var m=t[0],w=t.parent(),E=n==c,x=n==v,T=x?d:E?h:"enabled",N=s(m,T+o(m[g])),C=s(m,n+o(m[g]));if(!0!==m[n]){if(!i&&n==c&&m[g]==l&&m.name){var L=t.closest("form"),O='input[name="'+m.name+'"]',O=L.length?L.find(O):e(O);O.each(function(){this!==m&&e.data(this,a)&&r(e(this),n)})}x?(m[n]=!0,m[c]&&r(t,c,"force")):(i||(m[n]=!0),E&&m[v]&&r(t,v,!1)),u(t,E,n,i)}m[p]&&s(m,S,!0)&&w.find("."+f).css(S,"default"),w[y](C||s(m,n)),w[b](N||s(m,T)||"")}function r(e,t,n){var r=e[0],i=e.parent(),a=t==c,l=t==v,m=l?d:a?h:"enabled",w=s(r,m+o(r[g])),E=s(r,t+o(r[g]));if(!1!==r[t]){if(l||!n||"force"==n)r[t]=!1;u(e,a,m,n)}!r[p]&&s(r,S,!0)&&i.find("."+f).css(S,"pointer"),i[b](E||s(r,t)||""),i[y](w||s(r,m))}function i(t,n){if(e.data(t,a)){var r=e(t);r.parent().html(r.attr("style",e.data(t,a).s||"")[w](n||"")),r.off(".i").unwrap(),e(E+'[for="'+t.id+'"]').add(r.closest(E)).off(".i")}}function s(t,n,r){if(e.data(t,a))return e.data(t,a).o[n+(r?"":"Class")]}function o(e){return e.charAt(0).toUpperCase()+e.slice(1)}function u(e,t,n,r){r||(t&&e[w]("ifToggled"),e[w]("ifChanged")[w]("if"+o(n)))}var a="iCheck",f=a+"-helper",l="radio",c="checked",h="un"+c,p="disabled",d="determinate",v="in"+d,m="update",g="type",y="addClass",b="removeClass",w="trigger",E="label",S="cursor",x=/ipad|iphone|ipod|android|blackberry|windows phone|opera mini/i.test(navigator.userAgent);e.fn[a]=function(s,o){var u=":checkbox, :"+l,h=e(),d=function(t){t.each(function(){var t=e(this);h=t.is(u)?h.add(t):h.add(t.find(u))})};if(/^(check|uncheck|toggle|indeterminate|determinate|disable|enable|update|destroy)$/i.test(s))return s=s.toLowerCase(),d(this),h.each(function(){"destroy"==s?i(this,"ifDestroyed"):t(e(this),!0,s),e.isFunction(o)&&o()});if("object"==typeof s||!s){var S=e.extend({checkedClass:c,disabledClass:p,indeterminateClass:v,labelHover:!0},s),T=S.handle,N=S.hoverClass||"hover",L=S.focusClass||"focus",O=S.activeClass||"active",M=!!S.labelHover,_=S.labelHoverClass||"hover",P=(""+S.increaseArea).replace("%","")|0;if("checkbox"==T||T==l)u=":"+T;return-50>P&&(P=-50),d(this),h.each(function(){i(this);var s=e(this),o=this,u=o.id,h=-P+"%",d=100+2*P+"%",d={position:"absolute",top:h,left:h,display:"block",width:d,height:d,margin:0,padding:0,background:"#fff",border:0,opacity:0},h=x?{position:"absolute",visibility:"hidden"}:P?d:{position:"absolute",opacity:0},v="checkbox"==o[g]?S.checkboxClass||"icheckbox":S.radioClass||"i"+l,T=e(E+'[for="'+u+'"]').add(s.closest(E)),k=s.wrap('<div class="'+v+'"/>')[w]("ifCreated").parent().append(S.insert),d=e('<ins class="'+f+'"/>').css(d).appendTo(k);s.data(a,{o:S,s:s.attr("style")}).css(h),S.inheritClass&&k[y](o.className),S.inheritID&&u&&k.attr("id",a+"-"+u),"static"==k.css("position")&&k.css("position","relative"),t(s,!0,m),T.length&&T.on("click.i mouseenter.i mouseleave.i touchbegin.i touchend.i",function(n){var r=n[g],i=e(this);if(!o[p]){if("click"==r?t(s,!1,!0):M&&(/ve|nd/.test(r)?(k[b](N),i[b](_)):(k[y](N),i[y](_))),!x)return!1;n.stopPropagation()}}),s.on("click.i focus.i blur.i keyup.i keydown.i keypress.i",function(e){var t=e[g];e=e.keyCode;if("click"==t)return!1;if("keydown"==t&&32==e)return o[g]==l&&o[c]||(o[c]?r(s,c):n(s,c)),!1;"keyup"==t&&o[g]==l?!o[c]&&n(s,c):/us|ur/.test(t)&&k["blur"==t?b:y](L)}),d.on("click mousedown mouseup mouseover mouseout touchbegin.i touchend.i",function(e){var n=e[g],r=/wn|up/.test(n)?O:N;if(!o[p]){"click"==n?t(s,!1,!0):(/wn|er|in/.test(n)?k[y](r):k[b](r+" "+O),T.length&&M&&r==N&&T[/ut|nd/.test(n)?b:y](_));if(!x)return!1;e.stopPropagation()}})})}return this}}(jQuery),define("icheck",["jquery"],function(e){return function(){var t,n;return t||e.iCheck}}(this)),+function(e){var t=function(t,n){this.options=n,this.$element=e(t),this.$backdrop=this.isShown=null,this.options.remote&&this.$element.load(this.options.remote)};t.DEFAULTS={backdrop:!0,keyboard:!0,show:!0},t.prototype.toggle=function(e){return this[this.isShown?"hide":"show"](e)},t.prototype.show=function(t){var n=this,r=e.Event("show.bs.modal",{relatedTarget:t});this.$element.trigger(r),this.isShown||r.isDefaultPrevented()||(this.isShown=!0,this.escape(),this.$element.on("click.dismiss.modal",'[data-dismiss="modal"]',e.proxy(this.hide,this)),this.backdrop(function(){var r=e.support.transition&&n.$element.hasClass("fade");n.$element.parent().length||n.$element.appendTo(document.body),n.$element.show(),r&&n.$element[0].offsetWidth,n.$element.addClass("in").attr("aria-hidden",!1),n.enforceFocus();var i=e.Event("shown.bs.modal",{relatedTarget:t});r?n.$element.find(".modal-dialog").one(e.support.transition.end,function(){n.$element.focus().trigger(i)}).emulateTransitionEnd(300):n.$element.focus().trigger(i)}))},t.prototype.hide=function(t){t&&t.preventDefault(),t=e.Event("hide.bs.modal"),this.$element.trigger(t),this.isShown&&!t.isDefaultPrevented()&&(this.isShown=!1,this.escape(),e(document).off("focusin.bs.modal"),this.$element.removeClass("in").attr("aria-hidden",!0).off("click.dismiss.modal"),e.support.transition&&this.$element.hasClass("fade")?this.$element.one(e.support.transition.end,e.proxy(this.hideModal,this)).emulateTransitionEnd(300):this.hideModal())},t.prototype.enforceFocus=function(){e(document).off("focusin.bs.modal").on("focusin.bs.modal",e.proxy(function(e){this.$element[0]===e.target||this.$element.has(e.target).length||this.$element.focus()},this))},t.prototype.escape=function(){this.isShown&&this.options.keyboard?this.$element.on("keyup.dismiss.bs.modal",e.proxy(function(e){27==e.which&&this.hide()},this)):this.isShown||this.$element.off("keyup.dismiss.bs.modal")},t.prototype.hideModal=function(){var e=this;this.$element.hide(),this.backdrop(function(){e.removeBackdrop(),e.$element.trigger("hidden.bs.modal")})},t.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},t.prototype.backdrop=function(t){var n=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var r=e.support.transition&&n;if(this.$backdrop=e('<div class="modal-backdrop '+n+'" />').appendTo(document.body),this.$element.on("click.dismiss.modal",e.proxy(function(e){e.target===e.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus.call(this.$element[0]):this.hide.call(this))},this)),r&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!t)return;r?this.$backdrop.one(e.support.transition.end,t).emulateTransitionEnd(150):t()}else!this.isShown&&this.$backdrop?(this.$backdrop.removeClass("in"),e.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one(e.support.transition.end,t).emulateTransitionEnd(150):t()):t&&t()};var n=e.fn.modal;e.fn.modal=function(n,r){return this.each(function(){var i=e(this),s=i.data("bs.modal"),o=e.extend({},t.DEFAULTS,i.data(),"object"==typeof n&&n);s||i.data("bs.modal",s=new t(this,o)),"string"==typeof n?s[n](r):o.show&&s.show(r)})},e.fn.modal.Constructor=t,e.fn.modal.noConflict=function(){return e.fn.modal=n,this},e(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(t){var n=e(this),r=n.attr("href"),i=e(n.attr("data-target")||r&&r.replace(/.*(?=#[^\s]+$)/,"")),s=i.data("modal")?"toggle":e.extend({remote:!/#/.test(r)&&r},i.data(),n.data());t.preventDefault(),i.modal(s,this).one("hide",function(){n.is(":visible")&&n.focus()})}),e(document).on("show.bs.modal",".modal",function(){e(document.body).addClass("modal-open")}).on("hidden.bs.modal",".modal",function(){e(document.body).removeClass("modal-open")})}(window.jQuery),define("modal",["jquery"],function(){}),window.bootbox=window.bootbox||function a(e,t){function n(e){var t=y[m.locale];return t?t[e]:y.en[e]}function r(t,n,r){t.preventDefault();var i=e.isFunction(r)&&r(t)===!1;i||n.modal("hide")}function i(e){var t,n=0;for(t in e)n++;return n}function s(t,n){var r=0;e.each(t,function(e,t){n(e,t,r++)})}function o(t){var n,r;if("object"!=typeof t)throw new Error("Please supply an object of options");if(!t.message)throw new Error("Please specify a message");return t=e.extend({},m,t),t.buttons||(t.buttons={}),t.backdrop=t.backdrop?"static":!1,n=t.buttons,r=i(n),s(n,function(t,i,s){if(e.isFunction(i)&&(i=n[t]={callback:i}),"object"!==e.type(i))throw new Error("button with key "+t+" must be an object");i.label||(i.label=t),i.className||(i.className=2>=r&&s===r-1?"btn-primary":"btn-default")}),t}function u(e,t){var n=e.length,r={};if(1>n||n>2)throw new Error("Invalid argument length");return 2===n||"string"==typeof e[0]?(r[t[0]]=e[0],r[t[1]]=e[1]):r=e[0],r}function f(t,n,r){return e.extend(!0,{},t,u(n,r))}function l(e,t,n){return p(f(h.apply(null,e),t,n),e)}function c(){for(var e={},t=0,r=arguments.length;r>t;t++){var i=arguments[t],s=i.toLowerCase(),o=i.toUpperCase();e[s]={label:n(o)}}return e}function h(){return{buttons:c.apply(null,arguments)}}function p(e,n){var r={};return s(n,function(e,t){r[t]=!0}),s(e.buttons,function(e){if(r[e]===t)throw new Error("button key "+e+" is not allowed (options are "+n.join("\n")+")")}),e}var d={dialog:"<div class='bootbox modal' tabindex='-1' role='dialog'><div class='modal-dialog'><div class='modal-content'><div class='modal-body'><div class='bootbox-body'></div></div></div></div></div>",header:"<div class='modal-header'><h4 class='modal-title'></h4></div>",footer:"<div class='modal-footer'></div>",closeButton:"<button type='button' class='bootbox-close-button close'>&times;</button>",form:"<form class='bootbox-form'></form>",inputs:{text:"<input class='bootbox-input form-control' autocomplete=off type=text />"}},v=e("body"),m={locale:"en",backdrop:!0,animate:!0,className:null,closeButton:!0,show:!0},g={};g.alert=function(){var t;if(t=l(["ok"],arguments,["message","callback"]),t.callback&&!e.isFunction(t.callback))throw new Error("alert requires callback property to be a function when provided");return t.buttons.ok.callback=t.onEscape=function(){return e.isFunction(t.callback)?t.callback():!0},g.dialog(t)},g.confirm=function(){var t;if(t=l(["cancel","confirm"],arguments,["message","callback"]),t.buttons.cancel.callback=t.onEscape=function(){return t.callback(!1)},t.buttons.confirm.callback=function(){return t.callback(!0)},!e.isFunction(t.callback))throw new Error("confirm requires a callback");return g.dialog(t)},g.prompt=function(){var n,r,i,s,o,u;if(s=e(d.form),r={buttons:c("cancel","confirm"),value:""},n=p(f(r,arguments,["title","callback"]),["cancel","confirm"]),u=n.show===t?!0:n.show,n.message=s,n.buttons.cancel.callback=n.onEscape=function(){return n.callback(null)},n.buttons.confirm.callback=function(){return n.callback(o.val())},n.show=!1,!n.title)throw new Error("prompt requires a title");if(!e.isFunction(n.callback))throw new Error("prompt requires a callback");return o=e(d.inputs.text),o.val(n.value),s.append(o),s.on("submit",function(e){e.preventDefault(),i.find(".btn-primary").click()}),i=g.dialog(n),i.off("shown.bs.modal"),i.on("shown.bs.modal",function(){o.focus()}),u===!0&&i.modal("show"),i},g.dialog=function(t){t=o(t);var n=e(d.dialog),i=n.find(".modal-body"),u=t.buttons,a="",f={onEscape:t.onEscape};if(s(u,function(e,t){a+="<button data-bb-handler='"+e+"' type='button' class='btn "+t.className+"'>"+t.label+"</button>",f[e]=t.callback}),i.find(".bootbox-body").html(t.message),t.animate===!0&&n.addClass("fade"),t.className&&n.addClass(t.className),t.title&&i.before(d.header),t.closeButton){var l=e(d.closeButton);t.title?n.find(".modal-header").prepend(l):l.css("margin-top","-10px").prependTo(i)}return t.title&&n.find(".modal-title").html(t.title),a.length&&(i.after(d.footer),n.find(".modal-footer").html(a)),n.on("hidden.bs.modal",function(e){e.target===this&&n.remove()}),n.on("shown.bs.modal",function(){n.find(".btn-primary:first").focus()}),n.on("escape.close.bb",function(e){f.onEscape&&r(e,n,f.onEscape)}),n.on("click",".modal-footer button",function(t){var i=e(this).data("bb-handler");r(t,n,f[i])}),n.on("click",".bootbox-close-button",function(e){r(e,n,f.onEscape)}),n.on("keyup",function(e){27===e.which&&n.trigger("escape.close.bb")}),v.append(n),n.modal({backdrop:t.backdrop,keyboard:!1,show:!1}),t.show&&n.modal("show"),n},g.setDefaults=function(t){e.extend(m,t)},g.hideAll=function(){e(".bootbox").modal("hide")};var y={br:{OK:"OK",CANCEL:"Cancelar",CONFIRM:"Sim"},da:{OK:"OK",CANCEL:"Annuller",CONFIRM:"Accepter"},de:{OK:"OK",CANCEL:"Abbrechen",CONFIRM:"Akzeptieren"},en:{OK:"OK",CANCEL:"Cancel",CONFIRM:"OK"},es:{OK:"OK",CANCEL:"Cancelar",CONFIRM:"Aceptar"},fi:{OK:"OK",CANCEL:"Peruuta",CONFIRM:"OK"},fr:{OK:"OK",CANCEL:"Annuler",CONFIRM:"D'accord"},it:{OK:"OK",CANCEL:"Annulla",CONFIRM:"Conferma"},nl:{OK:"OK",CANCEL:"Annuleren",CONFIRM:"Accepteren"},pl:{OK:"OK",CANCEL:"Anuluj",CONFIRM:"Potwierdź"},ru:{OK:"OK",CANCEL:"Отмена",CONFIRM:"Применить"},zh_CN:{OK:"OK",CANCEL:"取消",CONFIRM:"确认"},zh_TW:{OK:"OK",CANCEL:"取消",CONFIRM:"確認"}};return g.init=function(t){window.bootbox=a(t||e)},g}(window.jQuery),define("bootbox",["jquery","modal"],function(e){return function(){var t,n;return t||e.bootbox}}(this)),define("notification",["jquery"],function(){var e=$(".notification"),t=$(".noti-error"),n;return n={show:function(t){e.show().find(".text-noti").text(t)},hide:function(){e.fadeOut()},autoHide:function(e,t){var n=this;n.show(e),setTimeout(function(){n.hide()},t||5e3)},showError:function(e){t.show().find(".text-error").text(e)},hideError:function(){t.fadeOut()},autoHideError:function(e,t){var n=this;n.showError(e),setTimeout(function(){n.hideError()},t||5e3)}},$(document).on("click",".notification",function(e){n.hide()}),e.hasClass("show")&&setTimeout(function(){n.hide()},5e3),window.notification=n,n}),define("dual_instructor_centralize",["jquery","select2","icheck","bootbox","mustache","underscore","backbone","notification"],function(e,t,n,r,i,s,o,u){function a(t){this.defaults={url_update_serivce:"centralized/updateDualInstructor.htm",url_delete_service:"centralized/deleteDualInstructor.htm",formTarget:e(".js-dual-form"),EL:e(".js-dual-form")},this.init(t)}return a.prototype={init:function(t){var n=this;n.conf=e.extend(n.defaults,t)},DualInstructorForm:function(){var e=this,t=e.conf,n;return n=o.Model.extend({initialize:function(){var e=this,t=e.formEL},clear:function(){var e=this.formEL;e.externalId.val("")},getParams:function(){var e=this,t=e.formEL,n=t.externalId.val(),r=t.locationId.val();return{externalId:n,locationId:r}},resetParams:function(){var e=this,t=e.formEL;t.locationId.val("-1"),t.externalId.val("")},formEL:{externalId:t.formTarget.find('input[name="instructorExternalID"]'),locationId:t.formTarget.find('select[name="location"]')}}),e.dualinstructormodel=new n,e},showForm:function(){var t=this,n=this.dualinstructormodel,r=n.getParams(),i=t.conf,s=i.EL,o=r.externalId,a=r.locationId;if(r.externalId==""){u.autoHideError("Please enter Instructor ID and select the location");return}if(r.locationId==-1){u.autoHideError("Please enter Instructor ID and select the location");return}e.ajax({url:i.url_update_serivce,type:"POST",data:{locationId:a,externalId:o},success:function(e){u.autoHideError(e.message)}})},removeAccess:function(){var t=this,n=this.dualinstructormodel,r=n.getParams(),i=t.conf,s=i.EL,o=r.externalId,a=r.locationId;if(r.externalId==""){u.autoHideError("Please enter Instructor ID and select the location");return}if(r.locationId==-1){u.autoHideError("Please enter Instructor ID and select the location");return}e.ajax({url:i.url_delete_service,type:"POST",data:{locationId:a,externalId:o},success:function(e){u.autoHideError(e.message)}})},reset:function(){var e=this,t=e.dualinstructormodel;t.resetParams()}},a}),define("eventdeploy",["jquery"],function(e){function t(e){var t=this;e&&e.constructor===Array&&e.forEach(function(e,n){var r,i;r=e.events,i=e.delegate,t.Deploy(r,i)})}return t.prototype={Deploy:function(t,n){var r,i,s;for(var o in t)r=o.split(" ")[0],i=e.trim(o.split(r)[1]),i==="document"&&(i=document),i==="window"&&(i=window),s=t[o],n!==undefined?e(n).on(r,i,s):e(i).on(r,s)},Destroy:function(t,n){var r,i;r=t.split(" ")[0],i=e.trim(t.split(r)[1]),i==="document"&&(i=document),i==="window"&&(i=window),n!==undefined?e(n).off(r,i):e(i).off(r)}},t}),require.config({baseUrl:"js",paths:{jquery:"vendor/jquery/jquery.min",mustache:"vendor/libs/mustache.min",pikaday:"vendor/libs/pikaday.min",moment:"vendor/libs/moment",icheck:"vendor/libs/jquery.icheck.min",select2:"vendor/libs/select2.min",typeahead:"vendor/libs/typeahead.min",modal:"vendor/libs/bootstrap.modal.min",bootbox:"vendor/libs/bootbox.min",backbone:"vendor/backbone/backbone-min",underscore:"vendor/underscore/underscore-min",dual_instructor_centralize:"app/modules/centralize/dual_instructor_centralize",edithour_modal:"app/modules/edithour_modal",search:"app/modules/search",notification:"app/modules/common/notification",eventdeploy:"utils/eventdeploy",hash:"utils/hash"},shim:{backbone:{deps:["underscore","jquery"],exports:"Backbone"},underscore:{exports:"_"},icheck:{deps:["jquery"],exports:"iCheck"},modal:{deps:["jquery"]},bootbox:{deps:["jquery","modal"],exports:"bootbox"},pikaday:{deps:["moment"],exports:"Pikaday"},select2:{deps:["jquery"],exports:"select2"},typeahead:{deps:["jquery"],exports:"typeahead"}}}),require(["jquery","mustache","underscore","backbone","dual_instructor_centralize","eventdeploy"],function(e,t,n,r,i,s){var o=new i;o.DualInstructorForm();var u=new s;u.Deploy({"click .js-add-dual-instructor":function(e){e.preventDefault(),o.showForm()},"click .js-delete-dual-instructor":function(e){e.preventDefault(),o.removeAccess()},"click .js-reset-dual-instructor":function(e){e.preventDefault(),o.reset()}})}),define("app/scripts/dual_instructor_manage",function(){});