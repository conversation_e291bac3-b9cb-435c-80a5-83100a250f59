/*!
 * mustache.js - Logic-less {{mustache}} templates with JavaScript
 * http://github.com/janl/mustache.js
 */
!function(a,b){"object"==typeof exports&&exports?module.exports=b:"function"==typeof define&&define.amd?define(b):a.Mustache=b}(this,function(){function a(a,b){return RegExp.prototype.test.call(a,b)}function b(b){return!a(o,b)}function c(a){return a.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function d(a){return String(a).replace(/[&<>"'\/]/g,function(a){return t[a]})}function e(a){this.string=a,this.tail=a,this.pos=0}function f(a,b){this.view=a,this.parent=b,this.clearCache()}function g(){this.clearCache()}function h(a){function b(a,b,d){if(!c[a]){var e=h(b);c[a]=function(a,b){return e(a,b,d)}}return c[a]}var c={};return function(c,d,e){for(var f,g,h="",i=0,j=a.length;j>i;++i)switch(f=a[i],f[0]){case"#":g=e.slice(f[3],f[5]),h+=c._section(f[1],d,g,b(i,f[4],e));break;case"^":h+=c._inverted(f[1],d,b(i,f[4],e));break;case">":h+=c._partial(f[1],d);break;case"&":h+=c._name(f[1],d);break;case"name":h+=c._escaped(f[1],d);break;case"text":h+=f[1]}return h}}function i(a){for(var b,c=[],d=c,e=[],f=0,g=a.length;g>f;++f)switch(b=a[f],b[0]){case"#":case"^":e.push(b),d.push(b),d=b[4]=[];break;case"/":var h=e.pop();h[5]=b[2],d=e.length>0?e[e.length-1][4]:c;break;default:d.push(b)}return c}function j(a){for(var b,c,d=[],e=0,f=a.length;f>e;++e)b=a[e],"text"===b[0]&&c&&"text"===c[0]?(c[1]+=b[1],c[3]=b[3]):(c=b,d.push(b));return d}function k(a){return[new RegExp(c(a[0])+"\\s*"),new RegExp("\\s*"+c(a[1]))]}var l={};l.name="mustache.js",l.version="0.7.2",l.tags=["{{","}}"],l.Scanner=e,l.Context=f,l.Writer=g;var m=/\s*/,n=/\s+/,o=/\S/,p=/\s*=/,q=/\s*\}/,r=/#|\^|\/|>|\{|&|=|!/,s=Array.isArray||function(a){return"[object Array]"===Object.prototype.toString.call(a)},t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};l.escape=d,e.prototype.eos=function(){return""===this.tail},e.prototype.scan=function(a){var b=this.tail.match(a);return b&&0===b.index?(this.tail=this.tail.substring(b[0].length),this.pos+=b[0].length,b[0]):""},e.prototype.scanUntil=function(a){var b,c=this.tail.search(a);switch(c){case-1:b=this.tail,this.pos+=this.tail.length,this.tail="";break;case 0:b="";break;default:b=this.tail.substring(0,c),this.tail=this.tail.substring(c),this.pos+=c}return b},f.make=function(a){return a instanceof f?a:new f(a)},f.prototype.clearCache=function(){this._cache={}},f.prototype.push=function(a){return new f(a,this)},f.prototype.lookup=function(a){var b=this._cache[a];if(!b){if("."===a)b=this.view;else for(var c=this;c;){if(a.indexOf(".")>0){var d=a.split("."),e=0;for(b=c.view;b&&e<d.length;)b=b[d[e++]]}else b=c.view[a];if(null!=b)break;c=c.parent}this._cache[a]=b}return"function"==typeof b&&(b=b.call(this.view)),b},g.prototype.clearCache=function(){this._cache={},this._partialCache={}},g.prototype.compile=function(a,b){var c=this._cache[a];if(!c){var d=l.parse(a,b);c=this._cache[a]=this.compileTokens(d,a)}return c},g.prototype.compilePartial=function(a,b,c){var d=this.compile(b,c);return this._partialCache[a]=d,d},g.prototype.compileTokens=function(a,b){var c=h(a),d=this;return function(a,e){if(e)if("function"==typeof e)d._loadPartial=e;else for(var g in e)d.compilePartial(g,e[g]);return c(d,f.make(a),b)}},g.prototype.render=function(a,b,c){return this.compile(a)(b,c)},g.prototype._section=function(a,b,c,d){var e=b.lookup(a);switch(typeof e){case"object":if(s(e)){for(var f="",g=0,h=e.length;h>g;++g)f+=d(this,b.push(e[g]));return f}return e?d(this,b.push(e)):"";case"function":var i=this,j=function(a){return i.render(a,b)},k=e.call(b.view,c,j);return null!=k?k:"";default:if(e)return d(this,b)}return""},g.prototype._inverted=function(a,b,c){var d=b.lookup(a);return!d||s(d)&&0===d.length?c(this,b):""},g.prototype._partial=function(a,b){a in this._partialCache||!this._loadPartial||this.compilePartial(a,this._loadPartial(a));var c=this._partialCache[a];return c?c(b):""},g.prototype._name=function(a,b){var c=b.lookup(a);return"function"==typeof c&&(c=c.call(b.view)),null==c?"":String(c)},g.prototype._escaped=function(a,b){return l.escape(this._name(a,b))},l.parse=function(a,d){function f(){if(y&&!z)for(;x.length;)w.splice(x.pop(),1);else x=[];y=!1,z=!1}if(a=a||"",d=d||l.tags,"string"==typeof d&&(d=d.split(n)),2!==d.length)throw new Error("Invalid tags: "+d.join(", "));for(var g,h,o,s,t=k(d),u=new e(a),v=[],w=[],x=[],y=!1,z=!1;!u.eos();){if(g=u.pos,o=u.scanUntil(t[0]))for(var A=0,B=o.length;B>A;++A)s=o.charAt(A),b(s)?x.push(w.length):z=!0,w.push(["text",s,g,g+1]),g+=1,"\n"===s&&f();if(g=u.pos,!u.scan(t[0]))break;if(y=!0,h=u.scan(r)||"name",u.scan(m),"="===h)o=u.scanUntil(p),u.scan(p),u.scanUntil(t[1]);else if("{"===h){var C=new RegExp("\\s*"+c("}"+d[1]));o=u.scanUntil(C),u.scan(q),u.scanUntil(t[1]),h="&"}else o=u.scanUntil(t[1]);if(!u.scan(t[1]))throw new Error("Unclosed tag at "+u.pos);if("/"===h){if(0===v.length)throw new Error('Unopened section "'+o+'" at '+g);var D=v.pop();if(D[1]!==o)throw new Error('Unclosed section "'+D[1]+'" at '+g)}var E=[h,o,g,u.pos];if(w.push(E),"#"===h||"^"===h)v.push(E);else if("name"===h||"{"===h||"&"===h)z=!0;else if("="===h){if(d=o.split(n),2!==d.length)throw new Error("Invalid tags at "+g+": "+d.join(", "));t=k(d)}}var D=v.pop();if(D)throw new Error('Unclosed section "'+D[1]+'" at '+u.pos);return i(j(w))};var u=new g;return l.clearCache=function(){return u.clearCache()},l.compile=function(a,b){return u.compile(a,b)},l.compilePartial=function(a,b,c){return u.compilePartial(a,b,c)},l.compileTokens=function(a,b){return u.compileTokens(a,b)},l.render=function(a,b,c){return u.render(a,b,c)},l.to_html=function(a,b,c,d){var e=l.render(a,b,c);return"function"!=typeof d?e:(d(e),void 0)},l}());