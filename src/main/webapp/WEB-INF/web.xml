<?xml version="1.0" encoding="UTF-8"?>
<web-app version="2.5" xmlns="http://java.sun.com/xml/ns/javaee"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd">
	<display-name>GC Studios Scheduler</display-name>
	<!-- Context: contextConfigLocation: Spring configuration XML -->
	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>classpath:spring/context.xml</param-value>
	</context-param>
	<context-param>
		<param-name>spring.profiles.default</param-name>
		<param-value>production</param-value>
	</context-param>
	<!-- CharacterEncoding -->
	<filter>
		<filter-name>CharacterEncodingFilter</filter-name>
		<filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>UTF-8</param-value>
		</init-param>
		<init-param>
			<param-name>forceEncoding</param-name>
			<param-value>true</param-value>
		</init-param>
	</filter>
	<!-- Add Spring Security filter to all requests -->
	<filter>
		<filter-name>springSecurityFilterChain</filter-name>
		<filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>CharacterEncodingFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>springSecurityFilterChain</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	
	
	<filter>
    <filter-name>CorsFilter</filter-name>
    <filter-class>org.apache.catalina.filters.CorsFilter</filter-class>
	    <init-param>
	        <param-name>cors.allowed.origins</param-name>
	        <param-value>*</param-value>
	    </init-param>
	    <init-param>
	        <param-name>cors.support.credentials</param-name>
	        <param-value>false</param-value>
	    </init-param>   
	     <init-param>
	        <param-name>cors.allowed.methods</param-name>
	        <param-value>GET,POST,HEAD,OPTIONS,PUT,DELETE</param-value>
  	    </init-param>
	</filter>
	<filter-mapping>
	    <filter-name>CorsFilter</filter-name>
	    <url-pattern>/*</url-pattern>
	</filter-mapping>  
	
<filter>
    <filter-name>SimpleCORSFilter</filter-name>
    <filter-class>com.guitarcenter.scheduler.security.CORSFilter</filter-class>
</filter>
<filter-mapping>
    <filter-name>SimpleCORSFilter</filter-name>
    <url-pattern>/*</url-pattern>
</filter-mapping> 
	

	<!-- Add Spring framework listener to initialise context on startup -->
	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
	</listener>
	<!-- Add Spring Web MVC DispatcherServlet as the default handler for all 
		requests -->
		
	<!-- <listener>    
  		<listener-class>org.springframework.security.web.session.HttpSessionEventPublisher</listener-class>    
	</listener>  -->
	<servlet>
		<servlet-name>scheduler</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
		<init-param>
			<param-name>contextConfigLocation</param-name>
			<param-value>classpath:spring/webmvc.xml</param-value>
		</init-param>
		<load-on-startup>1</load-on-startup>
	</servlet>
	<servlet-mapping>
		<servlet-name>scheduler</servlet-name>
		<url-pattern>*.htm</url-pattern>
		<url-pattern>/</url-pattern>
	</servlet-mapping>
	<!-- welcome page -->
	<error-page>
		<error-code>400</error-code>
		<location>/WEB-INF/views/error/400.jsp</location>
	</error-page>
	<error-page>
		<error-code>403</error-code>
		<location>/WEB-INF/views/error/403.jsp</location>
	</error-page>
	<error-page>
		<error-code>404</error-code>
		<location>/WEB-INF/views/error/404.jsp</location>
	</error-page>
	<error-page>
		<error-code>500</error-code>
		<location>/WEB-INF/views/error/500.jsp</location>
	</error-page>
	<jsp-config>
		<taglib>
			<taglib-uri>/core</taglib-uri>
			<taglib-location>/WEB-INF/tlds/c.tld</taglib-location>
		</taglib>
	</jsp-config>
	<!-- Provide a reference to container managed DataSource -->
	<resource-ref>
		<description>Scheduler DB Connection</description>
		<res-ref-name>jdbc/schedulerDB</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	<resource-env-ref>
		<description>URL for Solr connection</description>
		<resource-env-ref-name>solrURL</resource-env-ref-name>
		<resource-env-ref-type>java.lang.String</resource-env-ref-type>
	</resource-env-ref>
	<!-- Provide a reference to container managed mail session -->
	<resource-ref>
		<description>JavaMail session factory</description>
		<res-ref-name>mail/session</res-ref-name>
		<res-type>javax.mail.Session</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>
	<!-- log4j configuration -->
	<context-param>
		<!-- configure log4j configuration location -->
		<param-name>log4jConfigLocation</param-name>
		<param-value>classpath:log/log4j.properties</param-value>
	</context-param>
	<context-param>
		<!-- avoid container load error -->
		<param-name>log4jExposeWebAppRoot</param-name>
		<param-value>false</param-value>
	</context-param>
	<listener>
		<!-- declare spring and log4j integration -->
		<listener-class>org.springframework.web.util.Log4jConfigListener</listener-class>
	</listener>
<!-- GSSP-355 increase session time -->
	<session-config>
		<session-timeout>40</session-timeout>
	</session-config>

</web-app>
