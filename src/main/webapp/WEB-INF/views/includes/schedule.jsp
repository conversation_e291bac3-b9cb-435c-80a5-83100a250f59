<div class="cal-header clearfix">
	<div class="left">
		<div class="btn-group-cal clearfix">
			<button class='js-today btn-cal' id="cal_today">Today</button>
		</div>
	</div>
	<div class="view-switch btn-group-cal left clearfix">
		<button class="btn-cal btn-cal-pre">
			<i class="icon-prev"></i>
		</button>
		<button id="cal_view_day" class="btn-cal cal-action">Day</button>
		<button id="cal_view_week" class="btn-cal cal-action">Week</button>
		<button id="cal_view_month" class="btn-cal cal-action">Month</button>
		<button class="btn-cal btn-cal-next">
			<i class="icon-next"></i>
		</button>
	</div>	
	<div class="type-switch btn-group-cal left clearfix">
		<button id="cal_filter_instructor" class="btn-cal btn-cal-large cal-action">Instructor</button>
		<button id="cal_filter_room" class="btn-cal cal-action right">Room</button>
	</div>
	<form action="" class="form-search right">
		<div class="left search-query-wrap">
			<input type="text" class="search-query quick-search" placeholder="Enter Customer Name" name="search-query">
		</div>
		<button class="btn-search btn-quick-search"><i class="icon-search"></i></button>
	</form>	
</div>
<div class="cal-body clearfix">
	<aside class="cal-left">
		<h2 class="js-cal-date"></h2>
		<div id="datepicker"></div>
		<!-- <p class="text-center mt10">
			<a class='js-today' id="select_today" href="javascript:;">Select Today</a>
		</p> -->
		<%@include file="/WEB-INF/views/includes/filter.jsp"%><!-- the left filter page including service type, instructors etc -->
	</aside>
	<div id="cal_content" class="cal-content">
		<table class="cal-title"><tr></tr></table>
		<div class="row relative">
			<div class="timeline"></div>
			<div class="cal-table"></div>
		</div>
		
	</div>
	
	
	<!-- this div holds the calendar information -->
		<!--  249 color code list -->
					
	<div class="cal-content calendar-legend" > <table style="border: 0px; background:#d9d9d9" class="calendar" >
<thead>
    <tr  >
    <td style="background:#d9d9d9"> </td>      
     </tr>
  <tr style="border: 0px">
    
<td  style="border: 0px; background:#d9d9d9; width: 5%"></td>
<td style="z-index: 1; box-shadow: 0 0 3px 1px rgba(255,255,255,0.50) inset; border: 0px; width: 30%; color: #FFFFFF; background:#339900;border:2px solid #1E8643">Single Appointment</td>
<td style="border: 0px; background:#d9d9d9;width : 5%"></td>
   
    <td style="z-index: 1; box-shadow: 0 0 3px 1px rgba(255,255,255,0.50) inset; border: 0px; width : 50%;color: #FFFFFF; background:#4987c4;border:2px solid #4782bb">Recurring Appointment</td>
<td  style="border: 0px ; background:#d9d9d9; width:10%"></td>
    
  </tr>
  
  <tr>
      <td style="background:#d9d9d9"> </td>
       
  </tr>
  <tr>
    
    <td  style="border: 0px; background:#d9d9d9; width: 5%"></td>
<td style="z-index: 1; box-shadow: 0 0 3px 1px rgba(255,255,255,0.50) inset; background:#d20105; color: #FFFFFF;border:2px solid #da463b">All Break Types</td>
<td style="border: 0px; background:#d9d9d9;width : 5%"></td>
   
    <td style="z-index: 1; box-shadow: 0 0 3px 1px rgba(255,255,255,0.50) inset; border: 0px;color: #FFFFFF; width : 50%; background:#643b5b;border:2px solid #704266">Customer Changes</td>
<td  style="border: 0px ; background:#d9d9d9; width:10%"></td>
    
  </tr>
   <tr>
    <td style="background:#d9d9d9"> </td>
        
  </tr>
  <!--  Changes made for adding purple color for online single appointment -->
  
  <tr style="border: 0px">
   
<td style="border: 0px; background:#d9d9d9; width: 5%"></td>
<td style="z-index: 1; box-shadow: 0 0 3px 1px rgba(255,255,255,0.50) inset; border: 0px; width: 30%; color: #FFFFFF; background:#ba55d3;border:2px solid #da70d6">Online - Single Appointment</td>
<td style="border: 0px; background:#d9d9d9;width : 5%"></td>
  
  
    <td style="z-index: 1; box-shadow: 0 0 3px 1px rgba(255,255,255,0.50) inset; border: 0px; width : 50%;color: #FFFFFF; background:#ff7f00;border:2px solid #ffc87c">Online - Recurring Appointment</td>
<td style="border: 0px ; background:#d9d9d9; width:10%"></td>
   
   
  <!--  Changes made for adding orange color for online recurring appointment -->
  
  <!--  Changes made for adding Navy Blue color for appointments created with H Status -->   
    <tr  >
    <td style="background:#d9d9d9"> </td>      
     </tr>
  <tr style="border: 0px">
    
<td style="border: 0px; background:#d9d9d9; width: 5%"></td>
<td style="z-index: 1;box-shadow: 0 0 3px 1px rgba(255,255,255,0.50) inset;border: 0px;width : 50%;color: #FFFFFF;background:#FFCA33;border:2px solid #e6e600">0 Lesson Counts</td>
<td style="border: 0px; background:#d9d9d9;width : 5%"></td>
  
    <td style="z-index: 1; box-shadow: 0 0 3px 1px rgba(255,255,255,0.50) inset; border: 0px; width : 50%;color: #FFFFFF; background:#90EE90;border:2px solid #90EE90">Trial Lesson</td>
<td style="border: 0px ; background:#d9d9d9; width:10%"></td>
  </tr>
  
  <tr>
    <td style="background:#d9d9d9"> </td>     
     </tr>
  
  <!--  Changes made for adding orange color for online recurring appointment -->
  
  </thead>
</table>
</div>
</div>
