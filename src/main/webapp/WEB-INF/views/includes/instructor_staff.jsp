<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix = "sec" uri = "http://www.springframework.org/security/tags"%>
<section>
	<table id="instructor_table" class="table table-fixed">
		<caption class="clearfix">
			Instructor List:
		</caption>
		<thead>
			<tr>
			<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead,ROLE_Studio Associate">
				<th class="cell-action"></th>
				</sec:authorize>
				<th class="cell-160">Instructor Name</th>
				<th class="cell-160">Email</th>
				<th class="cell-205">Availability</th>
				<th class="cell-205">Activity Type</th>
				<th class= "cell-100">Instructor Mode</th>
				<th class="cell-90">Active</th>
			</tr>
		</thead>
		<tbody>
		</tbody>
	</table>
	<div class="instructor-list"></div>
</section>

<!--
					GSSP-285 changes made.
					 -->
					 
					<!-----GSSP Instructor Mode update changes -->

<%@include file="/WEB-INF/views/includes/set_availability_modal.jsp"%>
<%@include file="/WEB-INF/views/includes/set_timeoff_modal.jsp"%>

<script id="tmpl_instructor" type="text/template">
		<tr>
		<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead,ROLE_Studio Associate">
				
			<th class="cell-action">
			<a class="js-edit-instructor" href="javascript:;"><i class="icon icon-edit"></i>Edit</a>
			</th>
			</sec:authorize>		
			
			<th class="cell-160">{{instructorName}}</th>
			<th class="cell-160">{{email}}</th>
			<th class="cell-205">{{availability}}</th>
			<th class="cell-205">{{activityTypes}}</th>
			<th class="cell-100">{{instructorMode}}</th>	
			<th class="cell-90">{{^active}}Disabled{{/active}}{{#active}}Enabled{{/active}}</th>
		</tr>
		<tr class="table-edit-panel hide">
			<td></td>
			<td>{{instructorName}}</td>
			<td>{{email}}</td>
			<td>
				<h6>Availability</h6>
				<div class="js-availability-wrap mb10"></div>
				<h6>One Time Availability</h6>
				<div class="js-oneTimeAvailability-wrap mb10"></div>
				<h6>Time Off</h6>
				<div class="js-timeoff-wrap mb10"></div>
				{{#active}}
				<a href="javascript:;" class="js-set-availability">Set Availability</a>
				<a href="javascript:;" class="js-set-timeoff ml10">Set Time Off</a>
				{{/active}}
				{{^active}}

					<a href="javascript:void(0);" class="disabled" >Set Availability</a>
					<a href="javascript:void(0);" class="disabled">Set Time Off</a>
				{{/active}}
				
			</td>
			<td class="js-activity-wrap">
			</td>
			 <td class="js-instructor-mode-wrap">
			</td>
			<td>
				<div class="row">
					{{^active}}
					<div class="span3 relative">
						<input class="js-input-active" name="active" type="checkbox" checked>
					</div>
					<div class="span9">
						Disable
					</div>
					{{/active}}
					{{#active}}
					<div class="span3 relative">
						<input class="js-input-active" name="active" type="checkbox">
					</div>
					<div class="span9">
						Disable
					</div>
					{{/active}}	
				</div>
			</td>
		</tr>
		<tr class="table-edit-actions hide">
			<td colspan="7">
				<div class="clearfix right">
					<button class="btn btn-primary close-edit-panel">Cancel</button>
					<button class="btn btn-important ml10 js-update-instructor">Update</button>	
				</div>
			</td>
		</tr>
	</script>

	<script id="tmpl_activity" type="text/template">
		
	
		<div class="row m30">
			
			

				{{#active}}

					
					<input type="hidden"  name="activities" placeholder="Choose ActivityType">
					
					<div class="span3 ml10">
						<button class="btn btn-primary btn-small js-btn-add">Add</button>	
					  </div>			
				{{/active}}
 

				{{^active}}
                    
					<input type="hidden"  name="activities" placeholder="Choose ActivityType" disabled>
					
					<div class="span3 ml10">
						<button class="btn btn-primary btn-small js-btn-add disabled">Add</button>	
                   </div>			
				{{/active}}
				

			
		</div>

		<h6>Currently Selected</h6>
		<input type="hidden" name="activityIds">
		<div class="row js-activity-list">
			{{#activitys}}
			<span class="activity-selected left">
				{{#active}}
					<a class="icon icon-remove js-remove-activity" data-id="{{activityId}}" href="javascript:;">x</a>
				{{/active}}
				{{activityName}}
			</span>
			{{/activitys}}
		</div>
	</script>
	
	<!-----GSSP Instructor Mode update changes -->	
	<script id="tmpl_instructor_mode" type="text/template">

	{{#active}}
		<div class="row mb30">
			<input name="instructorMode"   class="row"  type="hidden">
		</div>
	{{/active}}

	{{^active}}
		<div class="row mb30">
			<input name="instructorMode"   class="row"  type="hidden" disabled>
		</div>
	{{/active}}
	</script>
	
	
<script id="tmpl_activity_item" type="text/template">
	<span class="activity-selected left">
		 <a class="icon icon-remove js-remove-activity" data-id="{{activityId}}" href="javascript:;">x</a>
		{{activityName}}
	</span>
</script>

