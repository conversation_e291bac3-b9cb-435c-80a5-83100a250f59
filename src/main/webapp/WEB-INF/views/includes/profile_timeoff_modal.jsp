<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix = "sec" uri = "http://www.springframework.org/security/tags"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>   
<div id="modal_profile_timeoff" class="modal">
	<div class="modal-header">
		<h5>Set profile Time Off</h5>
		<a class="modal-close js-close-timeoff-modal-pr" href="javascript:;">X</a>
	</div>
	<div class="modal-body">
		<div class="row">
			<div class="span27">
				<h6>Schedule Time Off:</h6>
				<div class="row mb30 mt10">
					<span class="pre">Date:</span>
					<div class="left mr10">
						<input name="timeOffStartDate" type="text" class="input-xsmall left" >
						<i class="left icon-calendar"></i>
					</div>
					<div class="time-wrap clearfix">
					<span class="pre">From:</span>				
	
						<div class="select2-container">
							<input name="timeOffFrom" class="input-xsmall left" placeholder="Select Start Time"  disabled>	
						</div>						
					</div>
						<div class="time-wrap clearfix">
						<span class="pre">To:</span>

						<div class="select2-container">
							<input name="timeOffTo" class="input-xsmall left" placeholder="Select End Time" disabled>	
						</div>						
					</div>
				</div>
				<button class="btn btn-important btn-update-timeoff-pr">Update</button>
				<button class="btn btn-primary js-close-timeoff-modal-pr">Cancel</button>
			</div>
			
			<div class="span4">
				<p><strong>Scheduled Time Off</strong></p>
				<div class="timeoff-list text-left">
				</div>
			</div>
		</div>
	</div>
</div>