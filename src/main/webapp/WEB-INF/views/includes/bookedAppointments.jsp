<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!--For GSSP-273,  add room access to lead and managers  -->

<section>
	<table id="booked_appointments_table" class="table-fixed responsive-table-view">
		<caption class="clearfix">
			Booked Appointments List:
		</caption>
		<thead>
			<tr>
                <th class="appointment-id-header">Appt. ID</th>
				<th class="time-header">Date</th>
				<th class="time-header">Start Time</th>
				<th class="time-header">End Time</th>
				<th class="name-header">Instructor Name</th>
				<th class="name-header">Customer Name</th>
				<th class="book-status-header">Book Flag</th>
				<th class="book-status-header">Status</th>
				<th class="pos-ref-header">POS Ref No#</th>
				<th class="error-info-header">Error Info</th>

			</tr>
		</thead>
		<tbody class="booked-Appointments-list"></tbody>
	</table>
</section>

<script id="tmpl_booked_appointments" type="text/template">
	<td class="appointment-id-header" data-label="Appt. ID">{{appointmentId}}</td>
			<td class="time-header" data-label="Date">{{startDate}}</td>
			<td class="time-header" data-label="Start Time">{{startTime}}</td>
			<td class="time-header" data-label="End Time">{{endTime}}</td>
			<td class="name-header" data-label="Instructor Name">{{instructorFirstName}} {{instructorLastName}}</td>
            <td class="name-header" data-label="Customer Name">{{customerFirstName}} {{customerLastName}}</td>
			<td class="book-status-header" data-label="Book Flag">{{bookFlag}}</td>
			<td class="book-status-header" data-label="Status">{{status}}</td>
			<td class="pos-ref-header" data-label="POS Ref No#">{{posRefNumber}}</td>
			<td class="error-info-header" data-label="Error Info">{{errorInfo}}</td>

</script>
