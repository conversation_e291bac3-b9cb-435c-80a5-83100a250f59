<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!--For GSSP-273,  add room access to lead and managers  -->
<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead">
<div class="box">
	<div class="box-header">
		<a class="js-show-room-form" href="javascript:;">+ Add New Room</a>
	</div>
	<div class="box-body row hide">
		<form class="js-room-form form-horizontal" action="">
			<div class="form-msg error hide"></div>
			<div class="control-group">
				<label for="roomTemplate">Room Template:</label>
				<div class="controls">
					<select class="select-medium" name="roomTemplate" placeholder="Select Room Template">
						<option></option>
						<c:if test="${0 != fn:length(roomTemplateList)}">
							<c:forEach items="${roomTemplateList}" var="roomTemplate">
								<option value="${roomTemplate.roomTemplateId}">${roomTemplate.roomTemplateName}</option>
							</c:forEach>
						</c:if>
					</select>
				</div>
			</div>
			<div class="control-group">
				<label for="roomNumber">Room Number:</label>
				<div class="controls">
					<input name="roomNumber" class="input-medium" type="hidden" placeholder="Select Room Number">
				</div>
			</div>
			<div class="control-group">
				<label for="profileRoomName">Room Name:</label>
				<div class="controls">
					<input name="profileRoomName" class="input-medium" type="text" placeholder="Enter Room Name">
				</div>
			</div>
			<div class="control-group js-room-size-wrap">
				<label for="roomSize">Room Size:</label>
				<div class="controls">
					<input name="roomSize" type="hidden" placeholder="Select Room Size">
				</div>
			</div>
			<div class="control-group js-split-room-wrap">
				<label for="isSplitRoom">Split room</label>
				<div class="controls">
					<div class="span3">
						<div class="relative">
							<input type="checkbox" name="isSplitRoom">
						</div>
						<div class="span8 offset1">Split Room</div>
					</div>
					<div class="span3">Note: Edit split rooms in list</div>
				</div>
			</div>
			<div class="control-group">
				<label for="servicesList">Services Available:</label>
				<div class="controls">
					<div class="row mb10">
						<input type="hidden" name="services">
						<input name="servicesList" type="hidden" class="input-medium left">
						<div class="span2 ml10">
							<button type="button" class="btn btn-primary btn-small js-add-service-item">Add</button>
						</div>
					</div>
					<div class="js-service-list span5"></div>
				</div>
			</div>
			<div class="control-group">
				<label for="activityList">Activities Available</label>
				<div class="controls">
					<input name="activities" type="hidden">
					<div class="row mb10">
						<input name="activityList" type="hidden" class="input-medium left">
						<div class="span2 ml10">
							<button type="button" class="btn btn-primary btn-small js-add-activity-item">Add</button>
						</div>
					</div>
					<div class="js-activity-list span5"></div>
				</div>
			</div>
			<div class="control-group">
				<label for="enabled">&nbsp;</label>
				<div class="controls">
					<div class="span3">
						<div class="relative">
							<input type="checkbox" name="enabled" checked>
						</div>
						<div class="span11 offset1">Enable when created</div>
					</div>
				</div>
			</div>
			<div class="control-group">
				<label for="">&nbsp;</label>
				<div class="controls">
					<button type="button" class="btn btn-important btn-long js-add-room mr20">Add Room</button>
					<button type="button" class="btn btn-primary js-cancel-room">Cancel</button>
				</div>
			</div>
		</form>
	</div>
</div>
</sec:authorize>

<section>
	<table class="table table-fixed">
		<caption class="clearfix">
			Room List:
		</caption>
		<thead>
			<tr>
			<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead,ROLE_Studio Associate">
				<th class="cell-action"></th>
				</sec:authorize>
				<th class="cell-150">Room Template</th>
				<th class="cell-100">Room #</th>
				<th class="cell-120">Room Name</th>
				<th class="cell-90">Size</th>
				<th class="">Split</th>
				<th class="cell-160">Service</th>
				<th class="cell-160">Activity</th>
				<th class="cell-90">Active</th>
			</tr>
		</thead>
		<tbody></tbody>
	</table>
	<div class="room-list"></div>
</section>

<script id="tmpl_room" type="text/template">
	<tr>
		<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead,ROLE_Studio Associate">
			
		<th class="cell-action">
		<a class="js-edit-room" href="javascript:;"><i class="icon icon-edit"></i>Edit</a>
		</th>
		</sec:authorize>
		
		<th class="cell-150">{{roomTemplate}}</th>
		<th class="cell-100">{{roomNumber}}</th>
		<th class="cell-120">{{profileRoomName}}</th>
		<th class="cell-90">{{roomSize}}</th>
		<th>
			{{#isSplitRoom}}Yes{{/isSplitRoom}}
			{{^isSplitRoom}}N/A{{/isSplitRoom}}
		</th>
		<th class="cell-160">{{services}}</th>
		<th class="cell-160">{{activities}}</th>
		<th class="cell-90">
			{{#enabled}}Enabled{{/enabled}}
			{{^enabled}}Disabled{{/enabled}}
		</th>
	</tr>
	<tr class="table-edit-panel hide">
		<td></td>
		<td>
			<div class="row">
				<input name="roomTemplate" class="span12" placeholder="Select roomTemplate" type="hidden">
			</div>
		</td>
		<td>
			<input name="roomNumber" class="row" type="hidden">
		</td>
		<td>
			<div class="row">
				<input name="profileRoomName" class="span12" type="text">
			</div>
		</td>
		<td>
			<div class="js-room-size-wrap">
				<input name="roomSize" class="input-small" type="hidden">
			</div>
		</td>
		<td>
			<div class="row js-split-room-wrap">
				<div class="span4 relative">
					{{#isSplitRoom}}
						<input type="checkbox" name="isSplitRoom" checked>
					{{/isSplitRoom}}
					{{^isSplitRoom}}
						<input type="checkbox" name="isSplitRoom">
					{{/isSplitRoom}}
				</div>
				<div class="span8">Split</div>
			</div>
		</td>
		<td>
			<div class="row mb10">
				<div class="span8">
					<input type="hidden" class="row" name="servicesList" placeholder="Select Service">
				</div>
				<div class="span3 ml10"><button class="btn btn-primary btn-small js-add-service">Add</buttton></div>
			</div>
			<h6>Currently Selected</h6>
			<div class="js-service-list row"></div>
		</td>
		<td>
			<div class="row mb10">
				<div class="span8">
					<input type="hidden" class="row" name="activityList" placeholder="Select Activity">
				</div>
				<div class="span3 ml10"><button class="btn btn-primary btn-small js-add-activity">Add</buttton></div>
			</div>
			<h6>Currently Selected</h6>
			<div class="js-activity-list row"></div>
		</td>
		<td>
			<div class="row">
				{{^enabled}}
					<div class="span3 relative">
						<input type="checkbox" name="enabled" checked>
					</div>
				{{/enabled}}
				{{#enabled}}
					<div class="span3 relative">
						<input type="checkbox" name="enabled">
					</div>
				{{/enabled}}
				<div class="span9">Disable</div>
			</div>
		</td>
	</tr>
	<tr class="table-edit-actions hide">
		<td colspan="9">
			<div class="clearfix right">
				<a href="javascript:;" class="js-delete-room mr20">Delete Room</a>
				<button class="btn btn-primary close-edit-panel">Cancel</button>
				<button class="btn btn-important ml10 js-update-room">Update</button>
			</div>
		</td>
	</tr>
</script>
<script id="tmpl_service_item" type="text/template">
	<span class="service-selected left">
		<a class="icon icon-remove js-remove-service" data-id="{{serviceId}}" href="javascript:;">x</a>
		{{serviceName}}
	</span>
</script>
<script id="tmpl_activity_item" type="text/template">
	<span class="activity-selected left">
		<a class="icon icon-remove js-remove-activity" data-id="{{activityId}}" href="javascript:;">x</a>
		{{activityName}}
	</span>
</script>