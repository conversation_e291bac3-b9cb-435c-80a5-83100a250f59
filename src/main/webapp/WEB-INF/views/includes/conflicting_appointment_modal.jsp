<%@ page language="java" contentType="text/html; charset=UTF-8"
      pageEncoding="UTF-8"%>
<%@ taglib prefix = "sec" uri = "http://www.springframework.org/security/tags"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>   
<div id="modal_conflicting_appointment" class="modal">
      <div class="modal-header">
            <h5>Conflicting Appointments</h5>
            <a class="modal-close js-close-conflicting-appointment-modal" href="javascript:;">X</a>
      </div>
            <div class="modal-body">
            <div class="row">
                <div class="span12"><br>      
                		<div class="modal-msg error hide"></div>           
                        <div class="conflicting-List text-left">                                         
                        </div>
                  </div>
            </div><br>
            <button type="button" class="btn btn-primary btn-close-modal js-close-conflicting-appointment-modal" style="float: right;">Cancel</button> <br><br>
          
      </div>
</div>
