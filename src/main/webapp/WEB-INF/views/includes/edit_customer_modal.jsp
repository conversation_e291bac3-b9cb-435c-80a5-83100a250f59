<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix = "sec" uri = "http://www.springframework.org/security/tags"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>   
<div id="modal_edit_cust" class="modal" >
	<div class="modal-header">
		<h5>Edit Customer Details</h5>
		<a class="modal-close js-close-edit-cust-modal" href="javascript:;">X</a>
	</div>
	<div class="form-horizontal" style="width: 400px;height: 300px;padding-top: 20px;padding-left: 15px;">
		 
			<div class="span12" style="white-space:wrap">
	 
				<div class="form-msg error hide"></div>
						<div class="control-group"   style="margin-bottom: 0px;">
						<div class="control-label">
							  <label style="line-height: 32px; width: 163px;"  for="activityName"  >Customer Name: </label>
							</div>
							<div class="control">
							 
								 <label style="line-height: 32px; width: 163px;" for="activityName" id = "customerFullNamelb" ></label>
								<input class="input-medium" name="customerFullName"  type="hidden" >
								</div>
								<input name="gcId" type="hidden">
								<input name="searchCustCriteria" type="hidden"  >
							
						</div>
						<div class="control-group" style="margin-bottom: 0px;">
						<div class="control-label">
							 <label style="line-height: 32px; width: 163px; "  for="activityName">Email:</label>
							</div>
							<div class="controls">
							  <label style="line-height: 18px; width: 200px; word-wrap: break-word;"  for="activityName" id = "emaillb" ></label>
								<input class="input-medium" name="email" type="hidden"  >
							</div>
						</div>
						<div class="control-group" style="margin-bottom: 0px;">
						<div class="control-label">
							 <label style="line-height: 32px; width: 163px;"  for="activityName">Status:</label>
							</div>
							<div class="controls">
							  <label style="line-height: 32px; width: 163px;"  for="activityName" id = "customerStatuslb" ></label>
								<input class="input-medium" name="customerStatus" type="hidden" >
							</div>
						</div>
						<div class="control-group" style="margin-bottom: 0px;">
						<div class="control-label">
							 <label style="line-height: 32px; width: 163px;"  for="activityName">Lessons Count:</label>
							</div>
							<div class="controls">
							  <label style="line-height: 32px; width: 163px;"  for="activityName" id = "lessonsCountlb" ></label>
								<input class="input-medium" name="lessonsCount" type="hidden"  >
							</div>
						</div>
						<div class="control-group" style="margin-bottom: 0px;">
						<div class="control-label">
							 <label style="line-height: 32px; width: 163px;"  for="activityName">GC ID:</label>
							</div>
							<div class="controls">
							  <label style="line-height: 32px; width: 163px;"  for="activityName" id = "customerExternalIdlb" ></label>
								<input class="input-medium" name="customerExternalId" type="hidden"  >
							</div>
						</div>
						<div class="control-group" style="margin-bottom: 0px;">
						<div class="control-label">
							 <label style="line-height: 32px; width: 163px;"  for="activityName">Secondary Email:</label>
							</div>
							<div class="controls">
								<input class="input-medium" name="secondaryEmail" type="text" placeholder="Enter Secondary Email">
							</div>
						</div>
						<div class="control-group" style="margin-bottom: 0px;">
						<div class="control-label">
							 <label style="line-height: 32px; width: 163px;"  for="activityName">Phone Number:</label>
							</div>
							<div class="controls">
							  <label style="line-height: 32px; width: 163px;"  for="activityName" id = "phoneNumberlb" ></label>
								<input class="input-medium" name="phoneNumber" type="hidden" >
							</div>
						</div>
						 
				<button class="btn btn-important btn-update-edit-cust">Update</button>
				<button class="btn btn-primary js-close-edit-cust-modal">Cancel</button>
			</div>
			
			<div class="span4">
				 
			</div>
		 
	</div>
</div>