<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>

<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://java.sun.com/jstl/core_rt" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jstl/fmt_rt" prefix="fmt"%>

	<c:set var="twlClock" value="12" />
	<c:set var="maxDispalyItems" value="4" />
	<c:set var="SPLITOR_SLASH" value="/" />
	<c:set var="SPLITOR_SPACE" value=" " />
	<c:set var="SPLITOR_COLON" value=":" />

	<table class="calendar calendar-month">
		<thead>
			<tr><!-- title from Sunday to Saturday -->
				<c:forEach items="${sun2satList }" var="xq">
					<th>
						<c:out value="${xq }" />
					</th>
				</c:forEach>
			</tr>
		</thead>
		
		<!-- date content -->
		<tbody>
			<c:set var="yearVal" value="${dateMap.year }" />
			<c:set var="monthVal" value="${dateMap.month }" />
			<c:set var="preMonth" value="${dateMap.preMonth }" />
			<c:set var="nextMonth" value="${dateMap.nextMonth }" />
			<c:set var="preYear" value="${dateMap.preYear }" />
			<c:set var="nextYear" value="${dateMap.nextYear }" />
			<c:set var="currDate" value="${dateMap.currDate }" />
			<c:forEach items="${resultMap }" var="m" varStatus="s">
				<tr>
					<c:forEach items="${m.value }" var="d" varStatus="s2"><!-- key:time value:list -->
						<c:if test="${!fn:contains(d.key, 'none') }">
						<c:set var="pOrNDates" value="" />
						
						<c:set var="mValue" value="${fn:split(d.key, SPLITOR_SLASH)[0] }" />
						<c:set var="yValue" value="${fn:split(d.key, SPLITOR_SLASH)[2] }" />
						<c:set var="dValue" value="${fn:split(d.key, SPLITOR_SLASH)[1] }" />
						
						<c:if test="${(mValue < monthVal) || (mValue > monthVal) }">
							<c:set var="pOrNDates" value="disabled" />
						</c:if>
	 					
	 					<td class="${pOrNDates }">
	 					<c:set var="cDate" value="${fn:split(currDate, SPLITOR_SLASH)[1] }" />
	 					<fmt:formatNumber var="cDate" value="${cDate }" />
	 					<c:set var="cMonth" value="${fn:split(currDate, SPLITOR_SLASH)[0] }" />
	 					<fmt:formatNumber var="cMonth" value="${cMonth }" />
	 					<c:set var="cYear" value="${fn:split(currDate, SPLITOR_SLASH)[2] }" />
	 					<fmt:formatNumber var="cYear" value="${cYear }" />
	 					
	 					<fmt:formatNumber var="dValue" value="${dValue }" />
						<fmt:formatNumber var="mValue" value="${mValue }" />
						<fmt:formatNumber var="yValue" value="${yValue }" />
						
						<c:set var="active" />
						<%-- <c:if test="${(cDate == dValue) && (cMonth == mValue) && (cYear == yValue) }">
							<c:set var="active" value="active" />
						</c:if> --%>
							<div class="month-item ${active }"><p class="js-to-day" data-date="${d.key }"><c:out value="${dValue }" /></p>
								<c:forEach items="${d.value }" var="app" varStatus="as">
									<c:set var="appST" value="${fn:split(app.startTime, SPLITOR_SPACE)[1] }" />
									<c:set var="hour" value="${fn:split(appST, SPLITOR_COLON)[0] }" />
									<c:set var="minutes" value="${fn:split(appST, SPLITOR_COLON)[1] }"/>
									<c:set var="lessonTime" />
									<c:if test="${hour < twlClock }">
										<c:set var="lessonTime" value="${hour }:${minutes } AM" />
									</c:if>
									<c:if test="${hour > twlClock }">
										<c:set var="lessonTime" value="${hour - twlClock }:${minutes } PM" />
									</c:if>
									<c:if test="${twlClock == hour }">
										<c:set var="lessonTime" value="12:${minutes } PM" />
									</c:if>
									<c:set var="customerName" />
									<c:if test="${!empty app.customers }">
										<c:forEach items="${app.customers }" var="c">
											<c:set var="cfn" value="" />
											<c:set var="cln" value="" />
											<c:set var="clscn" value="" />
											<c:if test="${!empty c.person.firstName }">
												<c:set var="cfn" value="${c.person.firstName }" />
											</c:if>
											<c:if test="${!empty c.person.lastName }">
												<c:set var="cln" value="${c.person.lastName }" />
											</c:if>
											<c:if test="${!empty c.lessonCounts }">
												<c:set var="clscn" value="${c.lessonCounts }" />
											</c:if>
											<c:set var="customerName" value="${customerName } ,${cfn } ${cln } (${clscn })"/>
										</c:forEach>
									</c:if>
									<c:if test="${fn:startsWith(customerName, ' ,') }">
										<c:set var="customerName" value="${fn:substring(customerName, 2, fn:length(customerName)) }" />
									</c:if>
									<c:if test="${fn:startsWith(customerName, ',') }">
										<c:set var="customerName" value="${fn:substring(customerName, 1, fn:length(customerName)) }" />
									</c:if>
									<%-- <c:set var="customerName" value="${fn:substring(customerName, 0, (fn:length(customerName) -1)) }" /> --%>
									<c:if test="${as.count <= maxDispalyItems }">
										<%-- <div class="cal-item month" data-appointmentid="${app.appointmentId }"><c:out value="${lessonTime } ${fn:substring(app.activity.activityName, 0, 7) }" /></div> --%>
										
										<%-- For gcss-574,use the 'isRecurring' to identify recurring appointment which needs to be displayed in green color --%>
										<%-- For gcss-254,Break color and  the 15 min changes are made--%>
										<c:choose>
											<c:when test="${app.activity.activityId eq '20'||  app.activity.activityId eq '120' || app.activity.activityId eq '684'}">
												<div class="cal-item month" style="background: #d20105; border: 1px solid #da463b;" data-appointmentid="${app.appointmentId }"><c:out value="${customerName } " /></div>
											</c:when>
											<c:when test="${app.activity.activityId eq '320'||  app.activity.activityId eq '140' }">
												<div class="cal-item month" style="background: #90EE90; border: 1px solid #90EE90;" data-appointmentid="${app.appointmentId }"><c:out value="${customerName } " /></div>
											</c:when>
											<c:otherwise>
														<c:choose>
													<c:when test="${app.appointmentSeries.isRecurring eq 'Y' }">
													<%-- Changes made for adding orange color for online recurring appointment--%>
														<c:choose>
														<c:when test="${app.activity.service.serviceId ne '20' }">					
														<div class="cal-item month" style="background: #4987c4; border: 1px solid #4782bb;" data-appointmentid="${app.appointmentId }"><c:out value="${customerName } " /></div>
														</c:when>
														</c:choose>
														
														<c:choose>
															<c:when test="${app.activity.service.serviceId eq '20' }">
																<div class="cal-item month" style="background: #ff7f00; border: 1px solid #da463b;" data-appointmentid="${app.appointmentId }"><c:out value="${customerName } " /></div>
															</c:when>
														</c:choose>	
														</c:when>
													</c:choose>	
													
													<c:choose>
													<c:when test="${app.appointmentSeries.isRecurring eq 'N' }">
													<%-- Changes made for adding purple color for online single appointment--%>
													
														<c:choose>
														<c:when test="${app.activity.service.serviceId ne '20' }">					
														<div class="cal-item month" style="background: #339900; border: 1px solid #1E8643;" data-appointmentid="${app.appointmentId }"><c:out value="${customerName } " /></div>
														</c:when>
														</c:choose>
														
														<c:choose>
															<c:when test="${app.activity.service.serviceId eq '20' }">
																<div class="cal-item month" style="background: #ba55d3; border: 1px solid #da463b;" data-appointmentid="${app.appointmentId }"><c:out value="${customerName } " /></div>
															</c:when>
														</c:choose>	
														</c:when>
													</c:choose>	
												<%-- Changes made for adding purple color for online single appointment--%>
											</c:otherwise>
										</c:choose>		
										
									</c:if>
								</c:forEach>
								<c:if test="${fn:length(d.value) > maxDispalyItems }">
									<div class="item-more js-to-day" data-date="${d.key }">${fn:length(d.value) - maxDispalyItems } more<span class="icon-item-more"></span></div>	
								</c:if>
							</div>
						</td>
					</c:if>
					<c:if test="${fn:contains(d.key, 'none') }">
						<td></td>
					</c:if>
					</c:forEach>
				</tr>
			</c:forEach>		
		</tbody>
	</table>