<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix = "sec" uri = "http://www.springframework.org/security/tags"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>   
<div id="modal_set_timeoff" class="modal">
	<div class="modal-header">
		<h5>Set Instructor Time Off</h5>
		<a class="modal-close js-close-timeoff-modal" href="javascript:;">X</a>
	</div>
	<div class="modal-body">
		<div class="row">
			<div class="span7">
				<h6>Schedule Time Off:</h6>
				<div class="row mb20 mt10">
					<span class="pre">From:</span>
					<div class="left mr20">
						<input name="timeOffStartDate" type="text" class="input-xsmall left" >
						<i class="left icon-calendar"></i>
					</div>
					<span class="pre">Time:</span>				
				
					<div class="time-wrap clearfix">
						<div class="select2-container">
							<input name="timeOffFrom" class="input-xsmall left" placeholder="Select Start Time"  disabled>	
						</div>						
					</div>
					
				</div>
				<div class="row mb20 mt10">
					<span class="pre">To:</span>
					<div class="left mr20">
						<input name="timeOffendDate" type="text" class="input-xsmall left">
						<i class="left icon-calendar"></i>
					</div>		
					<span class="pre">Time:</span>
					
					<div class="time-wrap clearfix">
						<div class="select2-container">
							<input name="timeOffTo" class="input-xsmall left" placeholder="Select End Time" disabled>	
						</div>						
					</div>
				</div>
				<button class="btn btn-important btn-update-timeoff">Update</button>
				<button class="btn btn-primary js-close-timeoff-modal">Cancel</button>
			</div>
			
			<div class="span5">
				<p><strong>Scheduled Time Off</strong></p>
				<div class="timeoff-list text-left">
				</div>
			</div>
		</div>
	</div>
</div>