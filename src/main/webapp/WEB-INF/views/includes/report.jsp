<%@ taglib prefix = "sec" uri = "http://www.springframework.org/security/tags"%>
<section>
	
	<div class="row mb20">
		<a href="printMasterScheduleReport.htm" target="_blank" class="btn btn-primary js-print right hide">Print&nbsp;Report</a>
	</div>
	 
 	
	 <div class="row mb20">
		<a href="excel/MasterScheduleReport" class="btn btn-primary js-excel right hide">GenerateExcel&nbsp;Report</a>
	</div>
	
	<div class="report-list"></div>
</section>

<script id="tmpl_instructor_schedule_report"  type="text/template">
<section> 
	<table class="table table-fixed">
		<caption class="clearfix">
			{{#instructorInfoDTO}}
				{{instructorNameWithAvaiTime}}
			{{/instructorInfoDTO}}
		</caption>
		<thead>
			<tr>
	<%--GSSP-252 Adding Instructor # column for instructor_schedule_report--%>	
                <th class="cell-110">Instructor #</th>	
				<th class="cell-100">Instructor Name</th>
				<%--GSSP-189 Adding date column for instructor_schedule_report--%>	
             	<th class="cell-100">Date</th>
				<th class="cell-100">Time Frame</th>
				<th class="cell-100">Duration</th>
				<th class="cell-100">Activity Type</th>
				<%--GSSP-311 Adding room name column for instructor_schedule_report--%>
				<th class="cell-100">Room Name</th>
				<th class="cell-100">Customer Name</th>
				<th>Show/<br/>NoShow/Cancel</th>				
			</tr>
		</thead>
		<tbody>
			{{#instructorReports}}
				<tr>
	<%--GSSP-252 Adding Instructor # column for instructor_schedule_report--%>	
                    <td>{{external_id}}</td>
					<td>{{instructorName}}</td>
					<%--GSSP-189 Adding date column for instructor_schedule_report--%>
	                <td>{{startDate}}</td>
					<td>{{startTime}} - {{endTime}}</td>
					<td>{{duration}}</td>
					<td>{{activityType}}</td>
					<%--GSSP-311 Adding room name column for instructor_schedule_report--%>
					<td>{{roomName}}</td>
					<td>{{customerName}}</td>
					<td>{{signitureBlock}}</td>					
				</tr>
			{{/instructorReports}}
		</tbody>
	</table>
</section>
</script>

<!-- GSSP INSTRUCTOR STATUS REPORT -->
<script id="tmpl_instructor_status_schedule_report" type="text/template">
<section>
	<table class="table table-fixed">
<caption class="clearfix">
		 Instructor Schedule Status Report
		</caption>
		<thead>
			<tr>
	<%--GSSP- Adding Instructor # column for instructor_schedule_report--%>	
<th class="cell-110">Instructor #</th>	
<th class="cell-110">Instructor Name</th>	
<th class="cell-110">Date</th>	
<th class="cell-110">Time Frame</th>	
<th class="cell-110">Activity<br>Type</th>		
<th class="cell-110">Customer<br>Name</th>	
<th class="cell-110">Completed?</th>	
<th class="cell-110">Internal Remarks</th>	
<th class="cell-110">Notes to Student</th>			
			</tr>
		</thead>
		<tbody>
			{{#instructorAppointmentStatusReports}}
				<tr>
				<td>{{instructorExternalId}}</td>
				<td>{{instructorName}}</td>
				<td>{{dateOfAppointment}}</td>
				<td>{{timeFrame}}</td>
				<td>{{activityName}}</td>
				<td>{{customerName}}</td>
				<td>{{showStatus}}</td>
				<td>{{comments}}</td>
			    <td>{{studentNote}}</td>				
				</tr>
			{{/instructorAppointmentStatusReports}}
		</tbody>
	</table>
</section>
</script>
<!-- GSSP INSTRUCTOR STATUS REPORT -->

<!-- Added for NewInsAptReport _ June 2015 Enhancement -->
<script id="tmpl_instructor_open_appointments_report" type="text/template">
<section>
	<table class="table table-fixed">

		<thead>
			<tr>
				<th class="cell-150">Instructor Name</th>
				<th class="cell-100">Date</th>
				<th class="cell-100">Time Frame</th>
				<th class="cell-100">Duration</th>
				<th class="cell-115">Activity Type</th>
				<th class="cell-116">Customer Name</th>
				<th class="cell-150">Room Name</th>
			</tr>
		</thead>
		<tbody>
			{{#instructorReports}}
				<tr>
					<td>{{instructorName}}</td>
					<td>{{startDate}}</td>
					<td>{{startTime}} - {{endTime}}</td>
					<td>{{duration}}</td>
					<td>{{activityType}}</td>
					<td>{{customerName}}</td>
					<td>{{roomName}}</td>
				</tr>
			{{/instructorReports}}
		</tbody>
	</table>
</section>
</script>

<!-- For GSSP-170 -->
<script id="tmpl_conflicting_appointments_report_by_instructor" type="text/template">
<section>
	<table class="table table-fixed">

		<thead>
			<tr>
				<th class="cell-150">Instructor Name</th>
				<th class="cell-100">Date</th>
				<th class="cell-150">Time Frame</th>
				<th class="cell-100">Duration</th>
				<th class="cell-115">Activity Type</th>
				<th class="cell-150">Customer Name</th>
				<th/>
			</tr>
		</thead>
		<tbody>
			{{#instructorReports}}
				<tr>
					<td>{{instructorName}}</td>
					<td>{{startDate}}</td>
					<td>{{startTime}} - {{endTime}}</td>
					<td>{{duration}}</td>
					<td>{{activityType}}</td>
					<td>{{customerName}}</td>
					<td>
					{{#showCancelButton}}
					<!--For GSSP-177, Recurring appointment cancel access to Associates  -->
					<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead,ROLE_Studio Associate ">
						<a class="btn btn-primary btn-long js-cancel-report" href="javascript:;" data-appointmentid="{{appointmentId}}" data-recurringstatus="{{recurringStatus}}"">Cancel Appointment</a>
					</sec:authorize>
					{{/showCancelButton}}
					</td>
				</tr>
			{{/instructorReports}}
		</tbody>
	</table>
</section>
</script>

<!-- For GSSP-170 -->
<script id="tmpl_conflicting_appointments_report_by_room" type="text/template">
<section>
	<table class="table table-fixed">

		<thead>
			<tr>
				<th class="cell-150">Room Name</th>
				<th class="cell-100">Date</th>
				<th class="cell-150">Time Frame</th>
				<th class="cell-100">Duration</th>
				<th class="cell-115">Activity Type</th>
				<th class="cell-150">Customer Name</th>
				<th/>
			</tr>
		</thead>
		<tbody>
			{{#instructorReports}}
				<tr>
					<td>{{roomName}}</td>
					<td>{{startDate}}</td>
					<td>{{startTime}} - {{endTime}}</td>
					<td>{{duration}}</td>
					<td>{{activityType}}</td>
					<td>{{customerName}}</td>
					<td>
					{{#showCancelButton}}
					<!--For GSSP-177, Recurring appointment cancel access to Associates  -->
					<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead,ROLE_Studio Associate ">
						<a class="btn btn-primary btn-long js-cancel-report" href="javascript:;" data-appointmentid="{{appointmentId}}" data-recurringstatus="{{recurringStatus}}"">Cancel Appointment</a>
					</sec:authorize>
					{{/showCancelButton}}
					</td>
				</tr>
			{{/instructorReports}}
		</tbody>
	</table>
</section>
</script>

<!-- For GSSP-161, Added -->
<script id="tmpl_instructor_outside_appointments_report" type="text/template">
<section>
	<table class="table table-fixed">

		<thead>
			<tr>
				<th class="cell-150">Instructor Name</th>
				<th class="cell-100">Date</th>
				<th class="cell-100">Time Frame</th>
				<th class="cell-100">Duration</th>
				<th class="cell-115">Activity Type</th>
				<th class="cell-116">Customer Name</th>
				<th class="cell-150">Room Name</th>
			</tr>
		</thead>
		<tbody>
			{{#instructorReports}}
				<tr>
					<td>{{instructorName}}</td>
					<td>{{startDate}}</td>
					<td>{{startTime}} - {{endTime}}</td>
					<td>{{duration}}</td>
					<td>{{activityType}}</td>
					<td>{{customerName}}</td>
					<td>{{roomName}}</td>
				</tr>
			{{/instructorReports}}
		</tbody>
	</table>
</section>
</script>

<script id="tmpl_rehearsal_booking_report" type="text/template">
<section>
	<table class="table table-fixed">
		<caption class="clearfix">
			Daily Rehearsal Schedule
		</caption>
		<thead>
			<tr>
				<th>Time</th>
				<th>Duration</th>
				<th>Activity Type</th>
				<th>Room</th>
				<th>Customer Name</th>
				<th>Band Name</th>
				<th></th>
			</tr>
		</thead>
		<tbody>
			{{#appDTOList}}
				<tr>
					<td>{{startTime}}</td>
					<td>{{duration}}</td>
					<td>{{activityType}}</td>
					<td>{{roomName}}</td>
					<td>{{customerName}}</td>
					<td>{{bandName}}</td>
					<td></td>
				</tr>
			{{/appDTOList}}
		</tbody>
	</table>
</section>
</script>

<script id="tmpl_rehearsal_schedule_report" type="text/template">
<section>
	<table class="table table-fixed">
		<caption class="clearfix">
			Daily Rehearsal Schedule
		</caption>
		<thead>
			<tr>
				<th>Time</th>
				<th>Duration</th>
				<th>Activity Type</th>
				<th>Room</th>
				<th>Customer Name</th>
				<th>Band Name</th>
				<th></th>
			</tr>
		</thead>
		<tbody>
			{{#appDTOList}}
				<tr>
					<td>{{startTime}}</td>
					<td>{{duration}}</td>
					<td>{{activityType}}</td>
					<td>{{roomName}}</td>
					<td>{{customerName}}</td>
					<td>{{bandName}}</td>
					<td></td>
				</tr>
			{{/appDTOList}}
		</tbody>
	</table>
</section>
</script>

<script id="tmpl_master_schedule_report" type="text/template">
<section>
	<table class="table table-fixed">
		<caption class="clearfix">
			Employee Schedule
		</caption>
		<thead>
			<tr>
				<th>Employee Name</th>
				<th>Schedule</th>
				<th></th>
			</tr>
		</thead>
		<tbody>
			{{#employeeDTOList}}
				<tr>
					<td>{{firstName}} {{lastName}}</td>
					<td>{{startTime}} - {{endTime}}</td>
					<td></td>
				</tr>
			{{/employeeDTOList}}
		</tbody>
	</table>
</section>
<section>
	<table class="table table-fixed">
		<caption class="clearfix">
			Daily Master Schedule
		</caption>
		<thead>
			<tr>				
				<th>Time</th>
				<th>Duration</th>
				<th>Activity Type</th>
				<th>Instructor Name</th>
				<%--GSSP-311 Adding room name column for Master_schedule_report--%>
				<th>Room Name</th>
				<th>In</th>
				<th>Out</th>
				<th>Customer Name</th>
				<th>Notes</th>
			</tr>
		</thead>
		<tbody>
			{{#appDTOList}}
				<tr>
					<td>{{startTime}}</td>
					<td>{{duration}}</td>
					<td>{{activityType}}</td>
					<td>{{instructorName}}</td>
					<%--GSSP-311 Adding room name column for Master_schedule_report--%>
					<td>{{profileRoomName}}</td>
					<td>{{}}</td>
					<td>{{}}</td>
					<td>{{customerName}}</td>
					<td>{{notes}}</td>
				</tr>
			{{/appDTOList}}
		</tbody>
	</table>
</section>
</script>

<script id="tmpl_cancelled_appointment_report" type="text/template">
<section>
	<table class="table table-fixed">
		<caption class="clearfix">
			Cancelled Appointments
		</caption>
		<thead>
			<tr>
				<%--GSSP-189 Added date column for cancelled appointment report--%>
				<th class="cell-100">Date</th>
				<th class="cell-150">Time</th>
				<th class="cell-115">Activity Type</th>
				<th>Instructor Name</th>
				<th>Customer Name</th>
				<th>Cancelled User</th>
				<th>Cancelled Timestamp</th>
				<th>Notes</th>
        		<th>Cancelled Reason</th>
			</tr>
		</thead>
		<tbody>
			{{#appDTOList}}
				<tr>
					<%--GSSP-189 fetching date  for cancelled appointment report--%>
					<td>{{startDateStr}}</td>
					<td>{{startTime}} - {{endTime}}</td>
					<td>{{activityType}}</td>
					<td>{{instructorName}}</td>
					<td>{{customerName}}</td>
					<td>{{cancelledUser}}
					<td>{{cancelledTime}}
					<td>{{notes}}</td>
					<td>{{cancelledReason}}</td> <%-- added for cancelled report-GSSP-269 --%>
				</tr>
			{{/appDTOList}}
		</tbody>
	</table>
</section>
</script>
<!-- Added for active students report-GSSP-185 -->
<script id="tmpl_active_students_report" type="text/template">
<section>
	<table class="table table-fixed">
	<caption class="clearfix">
			Active Students
		</caption>
		<thead>
			<tr>
				<th>Student Name</th>
				<th>Instructor Name</th>
				<th class="cell-100">Date</th>
				<th class="cell-100">Time Frame</th>
				<th class="cell-100">Duration</th>
				<th class="cell-115">Activity Type</th>
				<th class="cell-200">Student Email</th>
				
			</tr>
		</thead>
		<tbody>
			{{#instructorReports}}
				<tr>
					<td>{{customerName}}</td>
					<td>{{instructorName}}</td>
					<td>{{startDate}}</td>
					<td>{{startTime}} - {{endTime}}</td>
					<td>{{duration}}</td>
					<td>{{activityType}}</td>
					<td>{{customerEmail}}</td>
					
				</tr>
			{{/instructorReports}}
		</tbody>
	</table>
</section>
</script>

<!-- Added for New students report-GSSP-203 -->
<script id="tmpl_student_check_in_report" type="text/template">
<section>
	<table class="table table-fixed">
	<caption class="clearfix">
			Student Check In Sheet
		</caption>
		<thead>
			<tr>
				<th>Student Name</th>
				<th>Instructor Name</th>
				<th class="cell-100">Date</th>
				<th class="cell-115">Lesson Time</th>
				<th class="cell-100">Duration</th>
				<th>Lesson Type</th>
				<th class="cell-100">Check In</th>
				<th class="cell-100">Check Out</th>

				
			</tr>
		</thead>
		<tbody>
			{{#instructorReports}}
				<tr>
					<td>{{customerName}}</td>
					<td>{{instructorName}}</td>
					<td>{{startDate}}</td>
					<td>{{startTime}} - {{endTime}}</td>
					<td>{{duration}}</td>
					<td>{{activityType}}</td>					
					<td> </td>
					<td> </td>
				</tr>
			{{/instructorReports}}
		</tbody>
	</table>
</section>
</script>

<!-- Added for Inactive students report-GSSP-205 -->
<script id="tmpl_inactive_students_report" type="text/template">
<section>
	<table class="table table-fixed">
	<caption class="centerfix">
			Displaying inactive students for past 180 days
		</caption>
		<thead>
			<tr>
				<th>Student Name</th>
				<th>Student Email</th>
				<th>Student Phone</th>
				
			</tr>
		</thead>
		<tbody>
			{{#instructorReports}}
				<tr>
					<td>{{customerName}}</td>
					<td>{{customerEmail}}</td>
					<td>{{customerPhone}}</td>

				</tr>
			{{/instructorReports}}
		</tbody>
	</table>
</section>
</script>
