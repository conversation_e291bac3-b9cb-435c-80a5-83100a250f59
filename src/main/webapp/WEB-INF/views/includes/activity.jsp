<!--changes made for 263--->
<sec:authorize ifAnyGranted="ROLE_Site Admin">
<div class="box">	
	<div class="box-header">
		<a href="javascript:;" class="js-show-activity-form">+ Add New Activity</a>
	</div>
	
	<div class="box-body row hide">
		<form class="form-horizontal js-activity-form" action="">
			<div class="form-msg error hide"></div>
			<div class="control-group">
				<label for="activityId">Activity Name:</label>
				<div class="controls">
					<input class="input-medium hide" name="activityId" type="text">
				</div>
			</div>
			<div class="control-group">
				<label for="serviceType">Service Type:</label>
				<div class="controls">
					<input type="hidden" class="input-medium" name="serviceType">
				</div>
			</div>
			<div class="control-group">
				<label for="minAttenders">Number of Attendees:</label>
				<div class="controls">
					<div class="row">
						<input class="span1" name="minAttenders" type="text">
						<p class="span1 text-center">to</p>
						<input class="span1" name="maxAttenders" type="text">
					</div>
				</div>
			</div>
			<div class="control-group">
				<label for="minimumDuration">Minimum Duration:</label>
				<div class="controls">
					<select name="minimumDuration" class="select-medium" placeholder="Select Minimum Duration">
						<option></option>
					</select>	
				</div>
			</div>
			<div class="control-group">
				<label for="maxmumDuration">Maximum Duration:</label>
				<div class="controls">
					<select name="maxmumDuration" class="select-medium" placeholder="Select Maximum Duration">
						<option></option>
					</select>	
				</div>
			</div>
			<div class="control-group">
				<label for="instructor">Instructor:</label>
				<div class="controls">
					<select class="select-medium" placeholder="Select Instructor Requirement" name="instructor">
						<option></option>
						<option value="R">Required</option>
						<option value="N">Not Required</option>
						<option value="O">Optional</option>
					</select>
				</div>
			</div>
			<div class="control-group">
				<label for="enabledFlag">&nbsp;</label>
				<div class="controls">
					<div class="span3">
						<div class="relative">
							<input type="checkbox" name="enable">
						</div>
						<div class="span11 offset1">Enable when created</div>
					</div>
				</div>
			</div>
			<div class="control-group">
				<label for="">&nbsp;</label>
				<div class="controls">
					<button type="button" class="btn btn-important btn-long mr20 js-add-activity">Add Activity</button>
					<button type="button" class="btn btn-primary js-cancel-activity">Cancel</button>
				</div>
			</div>
		</form>
	</div>
</div>
</sec:authorize>
<section>
	<table class="table table-fixed">
		<caption class="clearfix">
			Activities List:
		</caption>
		<thead>
			<tr>
				<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead">
				<th class="cell-action"></th>
				</sec:authorize>
				<th class="cell-190">Activity Name</th>
				<th class="cell-160">Service Type</th>
				<th>Attendees</th>
				<th class="cell-100">Min/Hours</th>
				<th class="cell-100">Max/Hours</th>
				<th class="cell-170">Instructor Needs</th>
				<th class="cell-90">Active</th>
			</tr>
		</thead>
		<tbody></tbody>
	</table>
	<div class="activity-list"></div>
</section>

<script id="tmpl_activity_list" type="text/template">
		<tr>
			<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead">
			<th class="cell-action">
				<a href="javascript:;" class="edit-activity">
				<i class="icon-edit"></i>
				Edit
				</a>
			</th>
			</sec:authorize>
			<th class="cell-190">{{activityName}}</th>
			<th class="cell-160">{{serviceName}}</th>
			<th class="">{{&attenders}}</th>
			<th class="cell-100">{{minimumDuration}}</th>
			<th class="cell-100">{{maxmumDuration}}</th>
			<th class="cell-170">{{requiresInstructor}}</th>
			<th class="cell-90">
				{{#enable}}Enabled{{/enable}}
				{{^enable}}Disabled{{/enable}}
			</th>
		</tr>
		<tr class="table-edit-panel hide">
			<td></td>
			<td><input type="text" name="name" readonly></td>
			<td>
				<input name="serviceType" type="hidden">
			</td>
			<td>
				<div class="row">
					<input type="text" class="span4 " readonly name="minimunAttendees">
					<div class="span3 text-center">
						to
					</div>
					<input type="text" class="span4" readonly name="maximunAttendees">
				</div>
			</td>
			<td>
				<input name="minDuration" placeholder="Select Duration" class="row" type="hidden">
			</td>
			<td>
				<input name="maxDuration" placeholder="Select Duration" class="row" type="hidden">
			</td>
			<td>
				<select name="requireInstructor" class="row">
					<option value="R">Required</option>
					<option value="N">Not Required</option>
					<option value="O">Optional</option>
				</select>
			</td>
			<td>
				<div class="row">
				{{^enable}}
					<div class="span3 relative">
						<input class="js-input-active" name="active" type="checkbox" checked>
					</div>
					<div class="span9">
						Disable
					</div>
				{{/enable}}
				
				{{#enable}}
					<div class="span3 relative">
						<input class="js-input-active" name="active" type="checkbox">
					</div>
					<div class="span9">
						Disable
					</div>
				{{/enable}}
			</div>
		</td>
	</tr>
	<tr class="table-edit-actions hide">
		<td colspan="8">
			<div class="clearfix right">
				<!--GCSS-657--->
				<a href="#" class="js-delete-activity">Delete Activity</a>
				<button class="btn btn-primary close-edit-panel ml10">Cancel</button>
				<button class="btn btn-important ml10 js-update-activity">Update</button>	
			</div>
		</td>
	</tr>
</script>