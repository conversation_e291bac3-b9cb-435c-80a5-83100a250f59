<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>   
<%@ taglib prefix = "sec" uri = "http://www.springframework.org/security/tags"%>
<div class="notification ${sessionScope.showClass } ">
	<div class="container">
		<span class="icon-ok-noti">Success!</span>
		<div class="text-noti">${sessionScope.msg }</div>
		<a class="icon-close-noti" href="javascript:;">X</a>
	</div>
</div><!-- the notification --> 
<div class="noti-error">
	<div class="container">
		<div class="text-error"></div>
		<!-- <a class="icon-close-noti" href="javascript:;">X</a> -->
	</div>
</div><!-- the notification -->
<div class="warn-tip">
	<div class="warn-wrap">
		<p class="warn-info">Please update now so that you don't lose the changes that were entered</p>
		<span class="tri"></span>
	</div>
</div>
<header class="header hidden-print">
	<div class="container">
		<div class="window-tip">Loading...</div>
		<span class="logo"></span>
		<div class="header-right">
			<!-- <p>For password support, please call the <strong>Store Support Team:</strong></p>
			<h4>(866) 498-7876</h4> -->
			<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_District Manager,ROLE_Regional Vice President">
						<a class="profile-pic" href="#">
							<img src="images/ex-1.jpg" alt="">
						</a>
			</sec:authorize>
			<sec:authorize ifAllGranted="ROLE_Studio Manager">
						<a class="profile-pic" href="#">
							<img src="images/ex-2.jpg" alt="">
						</a>
			</sec:authorize>
			<sec:authorize ifAnyGranted="ROLE_Studio Lead">
						<a class="profile-pic" href="#">
							<img src="images/ex-3.jpg" alt="">
						</a>
			</sec:authorize>
			<sec:authorize ifAnyGranted="ROLE_Studio Associate,ROLE_Instructor">
						<a class="profile-pic" href="#">
							<img src="images/ex-4.jpg" alt="">
						</a>
			</sec:authorize>
			<div class="left">
				<p>
					<strong>${person.firstName } ${person.lastName } &nbsp; | &nbsp;</strong>
					<!-- For GSSP-181 -->
					<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_District Manager,ROLE_Regional Vice President">
						<a href="adminPage.htm">Home</a>
						<strong>&nbsp; | &nbsp;</strong>
					</sec:authorize>
					<a href="${pageContext.request.contextPath}/j_spring_security_logout">Log Out</a>
				</p>
				<p>${role.roleName }</p>
				<!-- if admin it will not show the location -->
				<p>${location.locationName }</p>
			</div>
		</div>
	</div>
</header> 
<%
	session.removeAttribute("showClass");
%>