<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead">
<div class="box">

	<div class="box-header">	
		<a href="javascript:;" class="js-show-service-form">+ Add New Service</a>	
		</div>	
		
	
	<div class="box-body row hide">
		<form class="form-horizontal js-service-form" action="">
			<div class="form-msg error hide"></div>
			<div class="control-group">
				<label for="serviceName">Service Type:</label>
				<div class="controls">
					<!-- <input class="input-medium" name="serviceType"> -->
					<input class="input-medium" name="serviceType" placeholder="Select Service Type" type="hidden">
				</div>
			</div>
			<div class="control-group">
				<label for="activity">Activity Available</label>
				<div class="controls">
					<input type="hidden" name="activity">
					<div class="row mb10">
						<input class="input-medium left" name="activityList" type="hidden">
						<div class="span2 ml10">
							<button type="button" class="btn btn-primary btn-small js-add-activity-item">Add</button>
						</div>
					</div>
					<div class="js-activity-list span5"></div>
				</div>
			</div>
			<div class="control-group">
				<label for="enable">&nbsp;</label>
				<div class="controls">
					<div class="span3">
						<div class="relative">
							<input type="checkbox" name="enable">
						</div>
						<div class="span11 offset1">Enable when created</div>
					</div>
				</div>
			</div>
			<div class="control-group">
				<label for="submit">&nbsp;</label>
				<div class="controls">
					<button type="button" class="btn btn-important btn-long mr20 js-add-service">Add Service</button>
					<button type="button" class="btn btn-primary js-cancel-service">Cancel</button>
				</div>
			</div>
		</form>
	</div>
</div>
</sec:authorize>
<section>
	<table class="table table-fixed">
		<caption class="clearfix">
			Service List:
		</caption>
		<thead>
			<tr>
			<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead">
				<th class="cell-action"></th>
			</sec:authorize>
				<th>Service Name</th>
				<th>Activity Available</th>
				<th class="cell-90">Active</th>
			</tr>
		</thead>
		<tbody></tbody>
	</table>
	<div class="service-list"></div>
</section>

<script id="tmpl_service" type="text/template">
	<tr>
		<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead">
		<th class="cell-action">
			<a href="javascript:;" class="edit-service">
			<i class="icon-edit"></i>
			Edit
			</a>
		</th>
		</sec:authorize>
		<th>{{serviceName}}</th>
		<th>{{avaibleAct}}</th>
		<th class="cell-90">
			{{#enable}}Enabled{{/enable}}
			{{^enable}}Disabled{{/enable}}
		</th>
	</tr>
	<tr class="table-edit-panel hide">
		<td></td>
		<td><input type="text" readonly value="{{serviceName}}"></td>
		<td>
			<div class="js-activity-wrap"></div>
		</td>
		<td>
			<div class="row">
				<sec:authorize ifAnyGranted="ROLE_Site Admin">
				{{^enable}}
					<div class="span3 relative">
						<input class="js-input-active" name="active" type="checkbox" checked>
					</div>
					<div class="span9">
						Disable
					</div>
				{{/enable}}
				{{#enable}}
					<div class="span3 relative">
						<input class="js-input-active" name="active" type="checkbox">
					</div>
					<div class="span9">
						Disable
					</div>
				{{/enable}}	
			</sec:authorize>
			</div>
		</td>
	</tr>
	<tr class="table-edit-actions hide">
		<td colspan="4">
			<div class="clearfix right">
				<a href="#" class="js-delete-service">Delete Service</a>
				<button class="btn btn-primary close-edit-panel ml10">Cancel</button>
				<button class="btn btn-important js-update-service ml10">Update</button>	
			</div>
		</td>
	</tr>
</script>

<script id="tmpl_service_activity" type="text/template">
	<div class="row mb30">
		<input type="hidden" class="select-medium left" name="activities" placeholder="Choose ActivityType">
		<div class="span3 ml10">
			<button class="btn btn-primary btn-small js-btn-add">Add</button>
		</div>
	</div>
	<h6>Currently Selected</h6>
	<input type="hidden" name="activityIds">
	<div class="row js-activity-list">
		{{#activityDTOs}}
		<span class="activity-selected span6">
			{{activityName}}
		</span>
		{{/activityDTOs}}
	</div>
</script>

<script id="tmpl_activity_item" type="text/template">
	<span class="activity-selected left">
		<a class="icon icon-remove js-remove-activity" data-id="{{activityId}}" href="javascript:;">x</a>
		{{activityName}}
	</span>
</script>

<script id="tmpl_service_activity_item" type="text/template">
	<span class="activity-selected span6">
		<!--a class="icon icon-remove js-remove-activity" data-id="{{activityId}}" href="javascript:;">x</a-->
		{{activityName}}
	</span>
</script>