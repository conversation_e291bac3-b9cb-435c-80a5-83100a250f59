<%--
  Author: austinliu
  Date: 4/23/2020
  Time: 10:21 AM
--%>
<section class="js-customer-detail customer-detail-container">
    <div class="row customer-detail">

        <div class="grid-center customer-detail-header">
            <div class="col-12">
                <h1 class="header-text">Customer Info</h1>
            </div>
        </div>
        <div class="customer-detail-info">
        </div>
    </div>
    <div class="container customer-detail-appointment-form">
        <h4 class="mb10" class="mb10">Customer Appointments</h4>
        <div class="row mb10">
            <div class="span3">
                <h5>Select Datetime Range:</h5>
            </div>
        </div>
        <div class="row mb10">
            <form action="" id="customer_appointments_form">
                <div class="startDate left mr20">
                    <input name="startDate" type="text" class="input-medium left" placeholder="Select Start Date">
                    <i class="left icon-calendar"></i>
                </div>
               
                <div class="endDate left mr20 ml20">
                    <input name="endDate" type="text" class="input-medium left" placeholder="Select End Date">
                    <i class="left icon-calendar"></i>
                </div>
                
                <div class="left btn-show-appt">
                    <button type="button" class="btn btn-medium btn-important js-query-customer-appointments">Show Appointments</button>
                </div>
            </form>
        </div>
    </div>

    <table class="table-fixed responsive-table-view customer-appointments-table" id="customer_appointments_table">
        <thead class="appointment-header">
        <tr>

            <th class="name-header">Instructor Name</th>
            <th class="date-header">Date</th>
            <th class="time-header">Start Time</th>
            <th class="email-header" scope="col">Send Reminder</th>
            <th class="duration-header">Duration</th>
<%--            <th class="type-header">Type</th>--%>
            <th class="store-header">Store</th>
            <th class="completed-header" scope="col">Completed?</th>
            <th class="remarks-header">Internal Remarks</th>
            <th class="notes-header">Notes to Student</th>
        </tr>
        </thead>
        


    </table>


</section>


<script id="tmpl_customer_detail_info" type="text/template">
    <div class="col-2" data-push-right="off-1">
<%--        <div class="panel-center header-sculpture">--%>
<%--            <img width="105" height="105" src="{{headSculpturePath}}"/>--%>
<%--        </div>--%>
    </div>
    <div class="col-4 panel-center customer-detail-text" data-push-right="off-1">
        <p><strong>Name : </strong><span class="customer-name">{{customerFullName}}</span></p>
        <p><strong>GC ID : </strong><span class="gc-id">{{customerExternalId}}</span></p>
<%--        <p><strong>Birthday : </strong><span class="birthday">{{birthday}}</span></p>--%>
        <p><strong>Phone Number : </strong><span class="phone-number">{{phoneNumber}}</span></p>
        <p><strong>Email : </strong><span class="email">{{email}}</span></p>
    </div>
    <div class="col-4 panel-center customer-detail-text">
        <p><strong>Last Booked : </strong><span class="last-booked">{{lastBooked}}</span></p>
        <p><strong>Lessons Count : </strong><span class="lessons-count">{{lessonsCount}}</span></p>
<%--        <p><strong>Future Appointments : </strong><span class="future-appointments">{{futureAppointments}}</span></p>--%>
    </div>
</script>


<script id="tmpl_customer_appointments" type="text/template">
<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead,ROLE_Studio Associate ">
    <tr>
        <td class="name-header" data-label="Instructor Name"><span>{{instructorFirstName}} {{instructorLastName}}</span></td>
        <td class="date-header" data-label="Date"><span>{{startDate}}</span></td>
        <td class="time-header" data-label="Start Time"><span>{{startTime}}</span></td>
        <td class="email-header" data-label="Email" data-id-email-reminder="{{appointmentId}}">
				
            {{#enabledSendRemainder}}
                <span class="reminder-link-container">
                    <a class="reminder-link" cid="{{cid}}" href="javascript:;">
                        <svg class="email-icon" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                            <path d="M.05 3.555L8 8.414l7.95-4.859A2 2 0 0014 2H2A2 2 0 00.05 3.555zM16 4.697l-5.875 3.59L16 11.743V4.697zm-.168 8.108L9.157 8.879 8 9.586l-1.157-.707-6.675 3.926A2 2 0 002 14h12a2 2 0 001.832-1.195zM0 11.743l5.875-3.456L0 4.697v7.046z"/>
                        </svg></a>
                </span>
            {{/enabledSendRemainder}}
            {{^enabledSendRemainder}}
                <span class="reminder-link-container">
                    <button class="reminder-link disabled" disabled>
                        <svg class="email-icon" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                            <path d="M.05 3.555L8 8.414l7.95-4.859A2 2 0 0014 2H2A2 2 0 00.05 3.555zM16 4.697l-5.875 3.59L16 11.743V4.697zm-.168 8.108L9.157 8.879 8 9.586l-1.157-.707-6.675 3.926A2 2 0 002 14h12a2 2 0 001.832-1.195zM0 11.743l5.875-3.456L0 4.697v7.046z"/>
                            </svg>
                    </button>
                </span>
            {{/enabledSendRemainder}}
        </td>
        <td class="duration-header" data-label="Duration"><span>{{duration}}</span></td>
        <td class="store-header" data-label="Store"><span>{{locationId}}</span></td>
        <td class="completed-header" data-label="Completed?" data-id-status="{{appointmentId}}">
				<span>
				{{#enabledStatus}} 
					<button class="btn-default yes lesson-completed" style="display:none;" data-id-appointment="{{appointmentId}}">Yes</button>
					<button class="btn-default no status-modal" style="display:none;" data-id-appointment="{{appointmentId}}">No</button>
					<p class="lesson-status-msg">{{status}}</p>
					<p class="undo-wrapper">(<a href="#" class="undo-status" data-id-appointment="{{appointmentId}}" data-backdrop="static">undo</a>)</p>
				{{/enabledStatus}}

			 {{^enabledStatus}}
				<button class="btn-default yes lesson-completed" data-id-appointment="{{appointmentId}}">Yes</button><button class="btn-default no status-modal" data-id-appointment="{{appointmentId}}">No</button>
				<p class="lesson-status-msg" style="display:none;"></p>
				<p class="undo-wrapper" style="display:none;">(<a href="#" class="undo-status" data-id-appointment="{{appointmentId}}" data-backdrop="static">undo</a>)</p>
			{{/enabledStatus}}
				</span>
			</td>
        <td class="remarks-header" data-label="Remarks">
            <span class="checkmark-icon">
            {{#enabledComments}}
            <svg class="checkmark-icon" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M13.854 3.646a.5.5 0 010 .708l-7 7a.5.5 0 01-.708 0l-3.5-3.5a.5.5 0 11.708-.708L6.5 10.293l6.646-6.647a.5.5 0 01.708 0z" clip-rule="evenodd"/>
            </svg>
            <p>(<button class="show-remarks">Show Remarks</button>)</p>
            {{/enabledComments}}
            {{^enabledComments}}
                <svg class="x-icon" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M11.854 4.146a.5.5 0 010 .708l-7 7a.5.5 0 01-.708-.708l7-7a.5.5 0 01.708 0z" clip-rule="evenodd"/>
                    <path fill-rule="evenodd" d="M4.146 4.146a.5.5 0 000 .708l7 7a.5.5 0 00.708-.708l-7-7a.5.5 0 00-.708 0z" clip-rule="evenodd"/>
                </svg>
                <p>No Remarks</p>
            {{/enabledComments}}
            </span>
        </td>
        <td class="notes-header" data-label="Student Notes"> 
            <span class="checkmark-icon">
                {{#enabledNotes}}
                    <svg class="checkmark-icon" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M13.854 3.646a.5.5 0 010 .708l-7 7a.5.5 0 01-.708 0l-3.5-3.5a.5.5 0 11.708-.708L6.5 10.293l6.646-6.647a.5.5 0 01.708 0z" clip-rule="evenodd"/>
                    </svg>
                    <p>(<button class="show-notes">Show Notes</button>)</p>
                {{/enabledNotes}}
                {{^enabledNotes}}
                    <svg class="x-icon" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M11.854 4.146a.5.5 0 010 .708l-7 7a.5.5 0 01-.708-.708l7-7a.5.5 0 01.708 0z" clip-rule="evenodd"/>
                        <path fill-rule="evenodd" d="M4.146 4.146a.5.5 0 000 .708l7 7a.5.5 0 00.708-.708l-7-7a.5.5 0 00-.708 0z" clip-rule="evenodd"/>
                    </svg>
                    <p>No Notes</p>
                {{/enabledNotes}}
            </span>
        </td>
    </tr>
    <tr class="remarks-hidden" style="display:none">
        <td class="remarks-data" colspan="9" data-label="Remarks">
            <span>
                <blockquote class="internal-remark"> <strong>Internal Remarks:</strong> <pre>{{comments}}</pre></blockquote>
            </span>
        </td>
    </tr>
    <tr class="notes-hidden" style="display:none">
        <td class="notes-data" colspan="9" data-label="Notes">
            <span>
                <blockquote class="student-notes"><strong>Notes to Student:</strong> <pre>{{studentNotes}}</pre></blockquote>
            </span>
        </td>
    </tr>
</sec:authorize>
</script>

<script id="tmpl_customer_status_modal" type="text/template">
<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead,ROLE_Studio Associate ">
		    <div class="modal-dialog">
		      <div class="modal-content">
		        <div class="modal-header">
		          <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
		        </div>
		        <div class="modal-body">
		        	 <h4 class="modal-title">Why was the lesson not complete?</h4>
		        	<div class="control-group">
						<select id="js-lesson-status" class="js-statuses">
							 <option selected value="noShow">No Show</option>
							 <option value="canceled">Canceled</option>
							 <option value="badConnection">Bad Connection</option>
							 <option value="other">Other</option>
						</select>
					</div>
		        </div>
		        <div class="modal-footer">
		          <button type="button" class="btn-default submit-status">Submit</button>
		        </div>
		      </div><!-- /.modal-content -->
		    </div><!-- /.modal-dialog -->
</sec:authorize>
</script>

<script id="tmpl_customer_detail_reminder_modal" type="text/template">
		<div class="modal-dialog">
		  <div class="modal-content">
			<div class="modal-header">
			  <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
			</div>
			  <div class="modal-body">
				<p class="email-success-msg"></p>

			</div>
			<div class="modal-footer">
			</div>
		  </div><!-- /.modal-content -->
		</div><!-- /.modal-dialog -->
</script>




