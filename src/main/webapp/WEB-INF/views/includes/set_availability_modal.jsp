<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix = "sec" uri = "http://www.springframework.org/security/tags"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>   
<div id="modal_set_availability" class="modal">
	<div class="modal-header">
		<h5>Set Instructor Availability</h5>
		<a class="modal-close js-close-availability-modal" href="javascript:;">X</a>
	</div>
	<div class="modal-body">
		<div class="row">
			<div class="span8">
				<div class="btn-group mb20 hour-select">
					<button class="sunday-hour btn" data-day="sunday">Sun</button>
					<button class="monday-hour btn" data-day="monday">Mon</button>
					<button class="tuesday-hour btn" data-day="tuesday">Tue</button>
					<button class="wednesday-hour btn" data-day="wednesday">Wed</button>
					<button class="thursday-hour btn" data-day="thursday">Thu</button>
					<button class="friday-hour btn" data-day="friday">Fri</button>
					<button class="saturday-hour btn" data-day="saturday">Sat</button>
				</div>
				<div class="row mb20 mt10 hide jq-selectDate">
					<span class="pre">From:</span>
					<div class="left mr20 jq-displayDate">
						<input name="selectDate" type="text" class="input-xsmall left" placeholder="Select Date">
						<i class="left icon-calendar"></i>
					</div>
					<div class="time-wrap clearfix">
						<span class="pre">From:</span>
						<div class="select2-container">
							<select name="onetimeFrom" placeholder="Time from">
								<option></option>
								<option value="05:00">05:00 AM</option>
								<option value="05:15">05:15 AM</option>
								<option value="05:30">05:30 AM</option>
								<option value="05:45">05:45 AM</option>
								<option value="06:00">06:00 AM</option>
								<option value="06:15">06:15 AM</option>
								<option value="06:30">06:30 AM</option>
								<option value="06:45">06:45 AM</option>
								<option value="07:00">07:00 AM</option>
								<option value="07:15">07:15 AM</option>
								<option value="07:30">07:30 AM</option>
								<option value="07:45">07:45 AM</option>
								<option value="08:00">08:00 AM</option>
								<option value="08:15">08:15 AM</option>
								<option value="08:30">08:30 AM</option>
								<option value="08:45">08:45 AM</option>
								<option value="09:00">09:00 AM</option>
								<option value="09:15">09:15 AM</option>
								<option value="09:30">09:30 AM</option>
								<option value="09:45">09:45 AM</option>
								<option value="10:00">10:00 AM</option>
								<option value="10:15">10:15 AM</option>
								<option value="10:30">10:30 AM</option>
								<option value="10:45">10:45 AM</option>
								<option value="11:00">11:00 AM</option>
								<option value="11:15">11:15 AM</option>
								<option value="11:30">11:30 AM</option>
								<option value="11:45">11:45 AM</option>
								<option value="12:00">12:00 PM</option>
								<option value="12:15">12:15 PM</option>
								<option value="12:30">12:30 PM</option>
								<option value="12:45">12:45 PM</option>
								<option value="13:00">01:00 PM</option>
								<option value="13:15">01:15 PM</option>
								<option value="13:30">01:30 PM</option>
								<option value="13:45">01:45 PM</option>
								<option value="14:00">02:00 PM</option>
								<option value="14:15">02:15 PM</option>
								<option value="14:30">02:30 PM</option>
								<option value="14:45">02:45 PM</option>
								<option value="15:00">03:00 PM</option>
								<option value="15:15">03:15 PM</option>
								<option value="15:30">03:30 PM</option>
								<option value="15:45">03:45 PM</option>
								<option value="16:00">04:00 PM</option>
								<option value="16:15">04:15 PM</option>
								<option value="16:30">04:30 PM</option>
								<option value="16:45">04:45 PM</option>
								<option value="17:00">05:00 PM</option>
								<option value="17:15">05:15 PM</option>
								<option value="17:30">05:30 PM</option>
								<option value="17:45">05:45 PM</option>
								<option value="18:00">06:00 PM</option>
								<option value="18:15">06:15 PM</option>
								<option value="18:30">06:30 PM</option>
								<option value="18:45">06:45 PM</option>
								<option value="19:00">07:00 PM</option>
								<option value="19:15">07:15 PM</option>
								<option value="19:30">07:30 PM</option>
								<option value="19:45">07:45 PM</option>
								<option value="20:00">08:00 PM</option>
								<option value="20:15">08:15 PM</option>
								<option value="20:30">08:30 PM</option>
								<option value="20:45">08:45 PM</option>
								<option value="21:00">09:00 PM</option>
								<option value="21:15">09:15 PM</option>
								<option value="21:30">09:30 PM</option>
								<option value="21:45">09:45 PM</option>
								<option value="22:00">10:00 PM</option>
								<option value="22:15">10:15 PM</option>
								<option value="22:30">10:30 PM</option>
								<option value="22:45">10:45 PM</option>
								<option value="23:00">11:00 PM</option>
								<option value="23:15">11:15 PM</option>
								<option value="23:30">11:30 PM</option>
								<option value="23:45">11:45 PM</option>
								<option value="00:00">12:00 AM</option>
							</select>
						</div>
					</div>		
					<div class="time-wrap clearfix">
						<span class="pre">To:</span>
						<div class="select2-container">
							<select name="onetimeTo" placeholder="Time to">
								<option></option>
								<option value="05:00">05:00 AM</option>
								<option value="05:15">05:15 AM</option>
								<option value="05:30">05:30 AM</option>
								<option value="05:45">05:45 AM</option>
								<option value="06:00">06:00 AM</option>
								<option value="06:15">06:15 AM</option>
								<option value="06:30">06:30 AM</option>
								<option value="06:45">06:45 AM</option>
								<option value="07:00">07:00 AM</option>
								<option value="07:15">07:15 AM</option>
								<option value="07:30">07:30 AM</option>
								<option value="07:45">07:45 AM</option>
								<option value="08:00">08:00 AM</option>
								<option value="08:15">08:15 AM</option>
								<option value="08:30">08:30 AM</option>
								<option value="08:45">08:45 AM</option>
								<option value="09:00">09:00 AM</option>
								<option value="09:15">09:15 AM</option>
								<option value="09:30">09:30 AM</option>
								<option value="09:45">09:45 AM</option>
								<option value="10:00">10:00 AM</option>
								<option value="10:15">10:15 AM</option>
								<option value="10:30">10:30 AM</option>
								<option value="10:45">10:45 AM</option>
								<option value="11:00">11:00 AM</option>
								<option value="11:15">11:15 AM</option>
								<option value="11:30">11:30 AM</option>
								<option value="11:45">11:45 AM</option>
								<option value="12:00">12:00 PM</option>
								<option value="12:15">12:15 PM</option>
								<option value="12:30">12:30 PM</option>
								<option value="12:45">12:45 PM</option>
								<option value="13:00">01:00 PM</option>
								<option value="13:15">01:15 PM</option>
								<option value="13:30">01:30 PM</option>
								<option value="13:45">01:45 PM</option>
								<option value="14:00">02:00 PM</option>
								<option value="14:15">02:15 PM</option>
								<option value="14:30">02:30 PM</option>
								<option value="14:45">02:45 PM</option>
								<option value="15:00">03:00 PM</option>
								<option value="15:15">03:15 PM</option>
								<option value="15:30">03:30 PM</option>
								<option value="15:45">03:45 PM</option>
								<option value="16:00">04:00 PM</option>
								<option value="16:15">04:15 PM</option>
								<option value="16:30">04:30 PM</option>
								<option value="16:45">04:45 PM</option>
								<option value="17:00">05:00 PM</option>
								<option value="17:15">05:15 PM</option>
								<option value="17:30">05:30 PM</option>
								<option value="17:45">05:45 PM</option>
								<option value="18:00">06:00 PM</option>
								<option value="18:15">06:15 PM</option>
								<option value="18:30">06:30 PM</option>
								<option value="18:45">06:45 PM</option>
								<option value="19:00">07:00 PM</option>
								<option value="19:15">07:15 PM</option>
								<option value="19:30">07:30 PM</option>
								<option value="19:45">07:45 PM</option>
								<option value="20:00">08:00 PM</option>
								<option value="20:15">08:15 PM</option>
								<option value="20:30">08:30 PM</option>
								<option value="20:45">08:45 PM</option>
								<option value="21:00">09:00 PM</option>
								<option value="21:15">09:15 PM</option>
								<option value="21:30">09:30 PM</option>
								<option value="21:45">09:45 PM</option>
								<option value="22:00">10:00 PM</option>
								<option value="22:15">10:15 PM</option>
								<option value="22:30">10:30 PM</option>
								<option value="22:45">10:45 PM</option>
								<option value="23:00">11:00 PM</option>
								<option value="23:15">11:15 PM</option>
								<option value="23:30">11:30 PM</option>
								<option value="23:45">11:45 PM</option>
								<option value="00:00">12:00 AM</option>
							</select>	
						</div>
					</div>
				</div>
				<div class="row mb20 jq-availability">
					<div class="time-wrap clearfix">
						<span class="pre">From:</span>
						<div class="select2-container">
							<select name="timeFrom" placeholder="Time from">
								<option></option>
								<option value="05:00 AM">05:00 AM</option>
								<option value="05:15 AM">05:15 AM</option>
								<option value="05:30 AM">05:30 AM</option>
								<option value="05:45 AM">05:45 AM</option>
								<option value="06:00 AM">06:00 AM</option>
								<option value="06:15 AM">06:15 AM</option>
								<option value="06:30 AM">06:30 AM</option>
								<option value="06:45 AM">06:45 AM</option>
								<option value="07:00 AM">07:00 AM</option>
								<option value="07:15 AM">07:15 AM</option>
								<option value="07:30 AM">07:30 AM</option>
								<option value="07:45 AM">07:45 AM</option>
								<option value="08:00 AM">08:00 AM</option>
								<option value="08:15 AM">08:15 AM</option>
								<option value="08:30 AM">08:30 AM</option>
								<option value="08:45 AM">08:45 AM</option>
								<option value="09:00 AM">09:00 AM</option>
								<option value="09:15 AM">09:15 AM</option>
								<option value="09:30 AM">09:30 AM</option>
								<option value="09:45 AM">09:45 AM</option>
								<option value="10:00 AM">10:00 AM</option>
								<option value="10:15 AM">10:15 AM</option>
								<option value="10:30 AM">10:30 AM</option>
								<option value="10:45 AM">10:45 AM</option>
								<option value="11:00 AM">11:00 AM</option>
								<option value="11:15 AM">11:15 AM</option>
								<option value="11:30 AM">11:30 AM</option>
								<option value="11:45 AM">11:45 AM</option>
								<option value="12:00 PM">12:00 PM</option>
								<option value="12:15 PM">12:15 PM</option>
								<option value="12:30 PM">12:30 PM</option>
								<option value="12:45 PM">12:45 PM</option>
								<option value="01:00 PM">01:00 PM</option>
								<option value="01:15 PM">01:15 PM</option>
								<option value="01:30 PM">01:30 PM</option>
								<option value="01:45 PM">01:45 PM</option>
								<option value="02:00 PM">02:00 PM</option>
								<option value="02:15 PM">02:15 PM</option>
								<option value="02:30 PM">02:30 PM</option>
								<option value="02:45 PM">02:45 PM</option>
								<option value="03:00 PM">03:00 PM</option>
								<option value="03:15 PM">03:15 PM</option>
								<option value="03:30 PM">03:30 PM</option>
								<option value="03:45 PM">03:45 PM</option>
								<option value="04:00 PM">04:00 PM</option>
								<option value="04:15 PM">04:15 PM</option>
								<option value="04:30 PM">04:30 PM</option>
								<option value="04:45 PM">04:45 PM</option>
								<option value="05:00 PM">05:00 PM</option>
								<option value="05:15 PM">05:15 PM</option>
								<option value="05:30 PM">05:30 PM</option>
								<option value="05:45 PM">05:45 PM</option>
								<option value="06:00 PM">06:00 PM</option>
								<option value="06:15 PM">06:15 PM</option>
								<option value="06:30 PM">06:30 PM</option>
								<option value="06:45 PM">06:45 PM</option>
								<option value="07:00 PM">07:00 PM</option>
								<option value="07:15 PM">07:15 PM</option>
								<option value="07:30 PM">07:30 PM</option>
								<option value="07:45 PM">07:45 PM</option>
								<option value="08:00 PM">08:00 PM</option>
								<option value="08:15 PM">08:15 PM</option>
								<option value="08:30 PM">08:30 PM</option>
								<option value="08:45 PM">08:45 PM</option>
								<option value="09:00 PM">09:00 PM</option>
								<option value="09:15 PM">09:15 PM</option>
								<option value="09:30 PM">09:30 PM</option>
								<option value="09:45 PM">09:45 PM</option>
								<option value="10:00 PM">10:00 PM</option>
								<option value="10:15 PM">10:15 PM</option>
								<option value="10:30 PM">10:30 PM</option>
								<option value="10:45 PM">10:45 PM</option>
								<option value="11:00 PM">11:00 PM</option>
								<option value="11:15 PM">11:15 PM</option>
								<option value="11:30 PM">11:30 PM</option>
								<option value="11:45 PM">11:45 PM</option>
							</select>
						</div>
					</div>		
					<div class="time-wrap clearfix">
						<span class="pre">To:</span>
						<div class="select2-container">
							<select name="timeTo" placeholder="Time to">
								<option></option>
								<option value="05:00 AM">05:00 AM</option>
								<option value="05:15 AM">05:15 AM</option>
								<option value="05:30 AM">05:30 AM</option>
								<option value="05:45 AM">05:45 AM</option>
								<option value="06:00 AM">06:00 AM</option>
								<option value="06:15 AM">06:15 AM</option>
								<option value="06:30 AM">06:30 AM</option>
								<option value="06:45 AM">06:45 AM</option>
								<option value="07:00 AM">07:00 AM</option>
								<option value="07:15 AM">07:15 AM</option>
								<option value="07:30 AM">07:30 AM</option>
								<option value="07:45 AM">07:45 AM</option>
								<option value="08:00 AM">08:00 AM</option>
								<option value="08:15 AM">08:15 AM</option>
								<option value="08:30 AM">08:30 AM</option>
								<option value="08:45 AM">08:45 AM</option>
								<option value="09:00 AM">09:00 AM</option>
								<option value="09:15 AM">09:15 AM</option>
								<option value="09:30 AM">09:30 AM</option>
								<option value="09:45 AM">09:45 AM</option>
								<option value="10:00 AM">10:00 AM</option>
								<option value="10:15 AM">10:15 AM</option>
								<option value="10:30 AM">10:30 AM</option>
								<option value="10:45 AM">10:45 AM</option>
								<option value="11:00 AM">11:00 AM</option>
								<option value="11:15 AM">11:15 AM</option>
								<option value="11:30 AM">11:30 AM</option>
								<option value="11:45 AM">11:45 AM</option>
								<option value="12:00 PM">12:00 PM</option>
								<option value="12:15 PM">12:15 PM</option>
								<option value="12:30 PM">12:30 PM</option>
								<option value="12:45 PM">12:45 PM</option>
								<option value="01:00 PM">01:00 PM</option>
								<option value="01:15 PM">01:15 PM</option>
								<option value="01:30 PM">01:30 PM</option>
								<option value="01:45 PM">01:45 PM</option>
								<option value="02:00 PM">02:00 PM</option>
								<option value="02:15 PM">02:15 PM</option>
								<option value="02:30 PM">02:30 PM</option>
								<option value="02:45 PM">02:45 PM</option>
								<option value="03:00 PM">03:00 PM</option>
								<option value="03:15 PM">03:15 PM</option>
								<option value="03:30 PM">03:30 PM</option>
								<option value="03:45 PM">03:45 PM</option>
								<option value="04:00 PM">04:00 PM</option>
								<option value="04:15 PM">04:15 PM</option>
								<option value="04:30 PM">04:30 PM</option>
								<option value="04:45 PM">04:45 PM</option>
								<option value="05:00 PM">05:00 PM</option>
								<option value="05:15 PM">05:15 PM</option>
								<option value="05:30 PM">05:30 PM</option>
								<option value="05:45 PM">05:45 PM</option>
								<option value="06:00 PM">06:00 PM</option>
								<option value="06:15 PM">06:15 PM</option>
								<option value="06:30 PM">06:30 PM</option>
								<option value="06:45 PM">06:45 PM</option>
								<option value="07:00 PM">07:00 PM</option>
								<option value="07:15 PM">07:15 PM</option>
								<option value="07:30 PM">07:30 PM</option>
								<option value="07:45 PM">07:45 PM</option>
								<option value="08:00 PM">08:00 PM</option>
								<option value="08:15 PM">08:15 PM</option>
								<option value="08:30 PM">08:30 PM</option>
								<option value="08:45 PM">08:45 PM</option>
								<option value="09:00 PM">09:00 PM</option>
								<option value="09:15 PM">09:15 PM</option>
								<option value="09:30 PM">09:30 PM</option>
								<option value="09:45 PM">09:45 PM</option>
								<option value="10:00 PM">10:00 PM</option>
								<option value="10:15 PM">10:15 PM</option>
								<option value="10:30 PM">10:30 PM</option>
								<option value="10:45 PM">10:45 PM</option>
								<option value="11:00 PM">11:00 PM</option>
								<option value="11:15 PM">11:15 PM</option>
								<option value="11:30 PM">11:30 PM</option>
								<option value="11:45 PM">11:45 PM</option>
								<option value="12:00 AM">12:00 AM</option>
							</select>	
						</div>
					</div>
				</div>
				<div class="row mb10" style="height: 38px;padding-bottom: 0px;width: 500px;">
					<div class="span1 relative" style="width: 20px;">
						<input type="checkbox" name="unavailable" style="position: absolute; opacity: 0;">
					</div>
                    <div class="span4" style="width: 75px;">Unavailable</div>
                    <div class="span1 relative" style="width: 20px;">
                        <input type="checkbox" name="oneTimeavailable"  style="position: absolute; opacity: 0;">
                    </div>            
                    <div class="span4" style="height: 35px; width: 128px;">One Time Availability</div>
                    <div class="span1 relative" style="width: 20px;">
                        <input type="checkbox" name="onLineAvailable" style="position: absolute; opacity: 0;">
                    </div>
                    <div class="span4" style="width: 45px;" >Online</div>
                    <div class="span1 relative" style="width: 20px;">
                        <input type="checkbox" name="instoreAvailable" style="position: absolute; opacity: 0;">
                    </div>
                    <div class="span4" style="width: 111px;height: 32px;">In-Store</div>
				</div>
				<button class="btn btn-important btn-update-availability">Update</button>
				<button class="btn btn-primary js-close-availability-modal">Cancel</button>
				<div class="breakLineWeb"></div>
				<div class="breakLineWebonl"></div>
				<div class="breakLineWebins"></div>
			</div>
			 
			<div class="span4 availability-wrap mb10">
				<p><strong>Current Availability</strong></p>
				<div class="studio-hour-list text-right">
				</div>
			</div>
			<div class="span4 availability-wrap mb10">
				<p><strong>Online  Availability</strong></p>
				<div class="onlineView-time-list text-right">
				</div>
			</div>
			<div class="span4 availability-wrap mb10">
				<p><strong>In-Store  Availability</strong></p>
				<div class="instoreView-time-list text-right">
				</div>
			</div>			
			<div class="span4 hide one-time-wrap">
				<p><strong>One Time Availability</strong></p>
				<div class="one-time-list text-left">
				</div>
			</div>
			<div class="span4 hide online-time-wrap">
				<p><strong>Online  Availability</strong></p>
				<div class="online-time-list text-left">
				</div>
			</div>
			
			<div class="span4 hide instore-time-wrap">
				<p><strong>In-Store  Availability</strong></p>
				<div class="instore-time-list text-left">
				</div>
			</div>
			<div class="span4 hide instoreView-time-wrap">
				<p><strong>View In-Store  Availability</strong></p>
				<div class="instoreView-time-list text-left">
				</div>
			</div>
			<div class="span4 hide onlineView-time-wrap">
				<p><strong>View Online  Availability</strong></p>
				<div class="onlineView-time-list text-left">
				</div>
			</div>
		</div>
	</div>
</div>