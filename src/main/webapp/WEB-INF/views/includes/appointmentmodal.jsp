<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix = "sec" uri = "http://www.springframework.org/security/tags"%>
<div id="modal_app" class="modal">
		<div class="modal-header">
			<h5>Create an Appointment</h5>
			<a class="modal-close modal-app-close" href="javascript:;">X</a>
		</div>
		<div class="modal-body">
			<div class="modal-msg hide"></div>
			<form class="form-horizontal" action="">
				<input type="hidden" name="appointmentSeriesId">
				<input type="hidden" name="appointmentId">
				<div class="control-group">
					<div class="control-label">
						<label for="customerId">Customer:</label>
					</div>
					<div class="controls">
						<input type="text" class="hide" id="customer_list" name="customerId">
					</div>
				</div>
				<div class="control-group parentFullName-control hide">
					<div class="contol-label">
						<label for="parentFullName">Parent Name:</label>
					</div>
					<div class="controls">
						<input  readonly type="text" name="parentFullName" class="input-large" placeholder="Enter Parent Name" disabled>
					</div>
				</div>
				<div class="control-group phone-control hide">
					<div class="contol-label">
						<label for="phone">Phone Number:</label>
					</div>
					<div class="controls">
						<input readonly type="text" name="phone" class="input-large" placeholder="Enter Phone Number" disabled>
					</div>
				</div>
				<div class="control-group email-control hide">
					<div class="control-label">
						<label for="email">Email:</label>
					</div>
					<div class="controls">
						<input readonly name="email" type="email" placeholder="Enter Email" disabled>
					</div>
				</div>
				<div class="control-group">
					<div class="control-label">
						<label for="serviceId">Service Type:</label>
					</div>
					<div class="controls">
						<input name="serviceId" type="hidden" placeholder="Select Service Type">
					</div>
				</div>
				<div class="control-group activityId-control">
					<div class="control-label">
						<label for="activityId">Lesson Type:</label>
					</div>
					<div class="controls">
						<input name="activityId" type="hidden">
					</div>
				</div>
				<div class="control-group row">
					<div class="control-label">
						<label for="startDate">Start Date:</label>
					</div>
					<div class="controls">
						<div class="row mb10">
							<input id="start_date" class="start-date input-medium left" type="text" name="startDate" placeholder="Enter Start Date">
							<i class="left icon-calendar"></i>
						</div><!--For GSSP-279 checkb0x id added -->
						<div class="span8 offset3 relative" id="isRecurring">
							<input type="checkbox" name="isRecurring" id="isRecurringCheckBox">
							<p class="span offset1">This is a recurring appointment.<p>
						</div>
					</div>
				</div>
				<div class="control-group endDate-control hide">
					<div class="control-label">
						<label for="endDate">End Date:</label>
					</div>
					<div class="controls">
						<div class="row mb10">
							<input id="end_date" class="end-date input-medium left" type="text" name="endDate" placeholder="Enter End Date">
							<i class="left icon-calendar"></i>
						</div>
					</div>
				</div>
				<div class="control-group bandName-control hide">
					<div class="contol-label">
						<label for="bandName">Band Name:</label>
					</div>
					<div class="controls">
						<input type="text" name="bandName" class="input-large" placeholder="Enter Band Name">
					</div>
				</div>
				<div class="control-group">
					<div class="control-label">
						<label for="startTime">Time:</label>
					</div>
					<div class="controls time-choice">
						<input name="startTime" placeholder="Select Start Time" type="hidden">
						<input type="hidden" name="duration" placeholder="Select Duration" >
					</div>
				</div>
				<div class="control-group instructor-control">
					<div class="control-label">
						<label for="instructorId">Instructor:</label>
					</div>
					<div class="controls">
						<input class="hide" type="hidden" name="instructorId" placeholder="Select an Instructor">
					</div>
				</div>
				<div class="control-group">
					<div class="control-label">
						<label for="roomId">Room:</label>
					</div>
					<div class="controls">
						<input name="roomId" type="hidden" placeholder="Select Room">
					</div>
				</div>
				<div class="control-group">
					<div class="control-label">
						<label for="note">Note:</label>
					</div>
					<div class="controls">
						<textarea name="note" cols="31" rows="2" maxlength="256"></textarea>
					</div>
				</div>
				<!--For GSSP-241, Recurring conflict appointment and changed the cancel,update appointment and cancel appointment button align to adjust  in thin client alignment    -->
				<div class="form-actions control-group row">
		
				
					<!--For GSSP-177, Recurring appointment cancel access to Associates  -->
					
					<div class="offset3">
						<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead,ROLE_Studio Associate ">
							<button type="button" class="btn btn-important btn-long1 btn-submit-app" style="padding-top:10px; margin-left:55px;margin-right:10px" >Create Appointment</button>
						</sec:authorize>	
							<button type="button" class="btn btn-primary btn-close-modal btn-close-app-modal" style="padding-top:7px;  margin-right:-2px; float: right;">Cancel</button>
						<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead,ROLE_Studio Associate ">
							<button type="button" class="disabled btn btn-primary js-conflict-app" style="padding-top:9px;margin-left:-375px; padding-right:5px;">Conflict Appointments</button>
						<a href="javascript:;" class="js-cancel-appointment hide btn btn-primary" style="padding-top:9px;margin-left:-375px; padding-right:20px;">Cancel Appointment</a>
					</sec:authorize>					
					</div>
				</div>
				
								</div>
			</form>
			<!--For GSSP-144 Issue # 2 -->
			<div class="modal-msg1 hide"></div>
		</div>
	</div>