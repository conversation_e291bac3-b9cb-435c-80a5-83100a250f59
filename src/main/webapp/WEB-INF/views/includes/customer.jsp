<section>
<table id="cutomer_table" class="table">
	<caption class="clearfix">
		Customer List:
		<form action="" class="form-search right">
			<div class="left search-query-wrap">
			    Studio Only<input type="checkbox" id="studio-customers" class="studio-customers" checked="checked"/>
				<input type="text" class="search-query search-customer" placeholder="Enter Customer Name">
			</div>
			<button class="btn-search btn-search-customer" type="submit"><i class="icon-search"></i></button>
		</form>
	</caption>
	<thead>
		<tr>
			<th class="cell-160">Name</th>
			<th class="cell-160">Email</th>
			<th class="cell-160">Secondary Email</th>
			<th class="cell-70">Status<br> Code</th>
			<th class="cell-70" align="center" >Lesson<br> Count</th>
			<th class="cell-90">GC ID</th>
			<th class="cell-110">View Schedule</th>
			<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead,ROLE_Studio Associate ">
				<th class="cell-110">Edit Customer</th>
			</sec:authorize>
			<input name="searchCustCriteria" type="hidden"  >
		</tr>
	</thead>
	<tbody></tbody>
</table>
<div class="customer-list"></div>
</section>
<script id="tmpl_customer" type="text/template">
	<tr>
		<th class="cell-160"><a data-id="{{recordId}}" class="customer-detail-view" href="javascript:;">{{fullName}}</a></th>
		<th class="cell-160">{{email}}</th>
		<th class="cell-160">{{secondaryEmail}}</th>
		<th class="cell-70">{{status}}</th>
		<th class="cell-70" align="center">{{lessonCount}}</th>
		<th class="cell-90">{{externalId}}</th>
		<th class="cell-110">
			<a href="javascript:;" class="js-view-schedule" data-id="{{recordId}}">View Schedule</a>
		</th>

		<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead,ROLE_Studio Associate ">
		<th class="cell-110">
			<a data-id="{{recordId}}" class="js-edit-customer" href="javascript:;">Edit Customer</a>				
		</th>
		</sec:authorize>
	</tr>
</script>
<script id="tmpl_emptycustomer" type="text/template">
    <tr>
        <th colspan="6">No customer found. Please try again.</th>
    </tr>
</script>
