<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix = "sec" uri = "http://www.springframework.org/security/tags"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>   
<div id="modal_edithour" class="modal">
	<div class="modal-header">
		<h5>Update Hours of Operation</h5>
		<a class="modal-close js-close-studio-modal" href="javascript:;">X</a>
	</div>
	<div class="modal-body">
		<div class="row">
			<div class="span8">
				<p class="mb10"><strong>Hours of Operation:</strong></p>
				<div class="btn-group mb20 hour-select">
					<button class="sunday-hour btn" data-day="sunday">Sun</button>
					<button class="monday-hour btn" data-day="monday">Mon</button>
					<button class="tuesday-hour btn" data-day="tuesday">Tue</button>
					<button class="wednesday-hour btn" data-day="wednesday">Wed</button>
					<button class="thursday-hour btn" data-day="thursday">Thu</button>
					<button class="friday-hour btn" data-day="friday">Fri</button>
					<button class="saturday-hour btn" data-day="saturday">Sat</button>
				</div>
				<div class="row mb30">
					<div class="time-wrap clearfix">
						<span class="pre">From:</span>
						<div class="select2-container">
							<select name="timeFrom" placeholder="Time from">
								<option></option>
								<option value="05:00 AM">05:00 AM</option>
								<option value="05:15 AM">05:15 AM</option>
								<option value="05:30 AM">05:30 AM</option>
								<option value="05:45 AM">05:45 AM</option>
								<option value="06:00 AM">06:00 AM</option>
								<option value="06:15 AM">06:15 AM</option>
								<option value="06:30 AM">06:30 AM</option>
								<option value="06:45 AM">06:45 AM</option>
								<option value="07:00 AM">07:00 AM</option>
								<option value="07:15 AM">07:15 AM</option>
								<option value="07:30 AM">07:30 AM</option>
								<option value="07:45 AM">07:45 AM</option>
								<option value="08:00 AM">08:00 AM</option>
								<option value="08:15 AM">08:15 AM</option>
								<option value="08:30 AM">08:30 AM</option>
								<option value="08:45 AM">08:45 AM</option>
								<option value="07:00 AM">07:00 AM</option>
								<option value="07:15 AM">07:15 AM</option>
								<option value="07:30 AM">07:30 AM</option>
								<option value="07:45 AM">07:45 AM</option>
								<option value="08:00 AM">08:00 AM</option>
								<option value="08:15 AM">08:15 AM</option>
								<option value="08:30 AM">08:30 AM</option>
								<option value="08:45 AM">08:45 AM</option>
								<option value="09:00 AM">09:00 AM</option>
								<option value="09:15 AM">09:15 AM</option>
								<option value="09:30 AM">09:30 AM</option>
								<option value="09:45 AM">09:45 AM</option>
								<option value="10:00 AM">10:00 AM</option>
								<option value="10:15 AM">10:15 AM</option>
								<option value="10:30 AM">10:30 AM</option>
								<option value="10:45 AM">10:45 AM</option>
								<option value="11:00 AM">11:00 AM</option>
								<option value="11:15 AM">11:15 AM</option>
								<option value="11:30 AM">11:30 AM</option>
								<option value="11:45 AM">11:45 AM</option>
								<option value="12:00 PM">12:00 PM</option>
								<option value="12:15 PM">12:15 PM</option>
								<option value="12:30 PM">12:30 PM</option>
								<option value="12:45 PM">12:45 PM</option>
								<option value="01:00 PM">01:00 PM</option>
								<option value="01:15 PM">01:15 PM</option>
								<option value="01:30 PM">01:30 PM</option>
								<option value="01:45 PM">01:45 PM</option>
								<option value="02:00 PM">02:00 PM</option>
								<option value="02:15 PM">02:15 PM</option>
								<option value="02:30 PM">02:30 PM</option>
								<option value="02:45 PM">02:45 PM</option>
								<option value="03:00 PM">03:00 PM</option>
								<option value="03:15 PM">03:15 PM</option>
								<option value="03:30 PM">03:30 PM</option>
								<option value="03:45 PM">03:45 PM</option>
								<option value="04:00 PM">04:00 PM</option>
								<option value="04:15 PM">04:15 PM</option>
								<option value="04:30 PM">04:30 PM</option>
								<option value="04:45 PM">04:45 PM</option>
								<option value="05:00 PM">05:00 PM</option>
								<option value="05:15 PM">05:15 PM</option>
								<option value="05:30 PM">05:30 PM</option>
								<option value="05:45 PM">05:45 PM</option>
								<option value="06:00 PM">06:00 PM</option>
								<option value="06:15 PM">06:15 PM</option>
								<option value="06:30 PM">06:30 PM</option>
								<option value="06:45 PM">06:45 PM</option>
								<option value="07:00 PM">07:00 PM</option>
								<option value="07:15 PM">07:15 PM</option>
								<option value="07:30 PM">07:30 PM</option>
								<option value="07:45 PM">07:45 PM</option>
								<option value="08:00 PM">08:00 PM</option>
								<option value="08:15 PM">08:15 PM</option>
								<option value="08:30 PM">08:30 PM</option>
								<option value="08:45 PM">08:45 PM</option>
								<option value="09:00 PM">09:00 PM</option>
								<option value="09:15 PM">09:15 PM</option>
								<option value="09:30 PM">09:30 PM</option>
								<option value="09:45 PM">09:45 PM</option>
								<option value="10:00 PM">10:00 PM</option>
								<option value="10:15 PM">10:15 PM</option>
								<option value="10:30 PM">10:30 PM</option>
								<option value="10:45 PM">10:45 PM</option>
								<option value="11:00 PM">11:00 PM</option>
								<option value="11:15 PM">11:15 PM</option>
								<option value="11:30 PM">11:30 PM</option>
								<option value="11:45 PM">11:45 PM</option>
							</select>
						</div>
					</div>		
					<div class="time-wrap clearfix">
						<span class="pre">To:</span>
						<div class="select2-container">
							<select name="timeTo" placeholder="Time to">
								<option></option>
								<option value="05:00 AM">05:00 AM</option>
								<option value="05:15 AM">05:15 AM</option>
								<option value="05:30 AM">05:30 AM</option>
								<option value="05:45 AM">05:45 AM</option>
								<option value="06:00 AM">06:00 AM</option>
								<option value="06:15 AM">06:15 AM</option>
								<option value="06:30 AM">06:30 AM</option>
								<option value="06:45 AM">06:45 AM</option>
								<option value="07:00 AM">07:00 AM</option>
								<option value="07:15 AM">07:15 AM</option>
								<option value="07:30 AM">07:30 AM</option>
								<option value="07:45 AM">07:45 AM</option>
								<option value="08:00 AM">08:00 AM</option>
								<option value="08:15 AM">08:15 AM</option>
								<option value="08:30 AM">08:30 AM</option>
								<option value="08:45 AM">08:45 AM</option>
								<option value="07:00 AM">07:00 AM</option>
								<option value="07:15 AM">07:15 AM</option>
								<option value="07:30 AM">07:30 AM</option>
								<option value="07:45 AM">07:45 AM</option>
								<option value="08:00 AM">08:00 AM</option>
								<option value="08:15 AM">08:15 AM</option>
								<option value="08:30 AM">08:30 AM</option>
								<option value="08:45 AM">08:45 AM</option>
								<option value="09:00 AM">09:00 AM</option>
								<option value="09:15 AM">09:15 AM</option>
								<option value="09:30 AM">09:30 AM</option>
								<option value="09:45 AM">09:45 AM</option>
								<option value="10:00 AM">10:00 AM</option>
								<option value="10:15 AM">10:15 AM</option>
								<option value="10:30 AM">10:30 AM</option>
								<option value="10:45 AM">10:45 AM</option>
								<option value="11:00 AM">11:00 AM</option>
								<option value="11:15 AM">11:15 AM</option>
								<option value="11:30 AM">11:30 AM</option>
								<option value="11:45 AM">11:45 AM</option>
								<option value="12:00 PM">12:00 PM</option>
								<option value="12:15 PM">12:15 PM</option>
								<option value="12:30 PM">12:30 PM</option>
								<option value="12:45 PM">12:45 PM</option>
								<option value="01:00 PM">01:00 PM</option>
								<option value="01:15 PM">01:15 PM</option>
								<option value="01:30 PM">01:30 PM</option>
								<option value="01:45 PM">01:45 PM</option>
								<option value="02:00 PM">02:00 PM</option>
								<option value="02:15 PM">02:15 PM</option>
								<option value="02:30 PM">02:30 PM</option>
								<option value="02:45 PM">02:45 PM</option>
								<option value="03:00 PM">03:00 PM</option>
								<option value="03:15 PM">03:15 PM</option>
								<option value="03:30 PM">03:30 PM</option>
								<option value="03:45 PM">03:45 PM</option>
								<option value="04:00 PM">04:00 PM</option>
								<option value="04:15 PM">04:15 PM</option>
								<option value="04:30 PM">04:30 PM</option>
								<option value="04:45 PM">04:45 PM</option>
								<option value="05:00 PM">05:00 PM</option>
								<option value="05:15 PM">05:15 PM</option>
								<option value="05:30 PM">05:30 PM</option>
								<option value="05:45 PM">05:45 PM</option>
								<option value="06:00 PM">06:00 PM</option>
								<option value="06:15 PM">06:15 PM</option>
								<option value="06:30 PM">06:30 PM</option>
								<option value="06:45 PM">06:45 PM</option>
								<option value="07:00 PM">07:00 PM</option>
								<option value="07:15 PM">07:15 PM</option>
								<option value="07:30 PM">07:30 PM</option>
								<option value="07:45 PM">07:45 PM</option>
								<option value="08:00 PM">08:00 PM</option>
								<option value="08:15 PM">08:15 PM</option>
								<option value="08:30 PM">08:30 PM</option>
								<option value="08:45 PM">08:45 PM</option>
								<option value="09:00 PM">09:00 PM</option>
								<option value="09:15 PM">09:15 PM</option>
								<option value="09:30 PM">09:30 PM</option>
								<option value="09:45 PM">09:45 PM</option>
								<option value="10:00 PM">10:00 PM</option>
								<option value="10:15 PM">10:15 PM</option>
								<option value="10:30 PM">10:30 PM</option>
								<option value="10:45 PM">10:45 PM</option>
								<option value="11:00 PM">11:00 PM</option>
								<option value="11:15 PM">11:15 PM</option>
								<option value="11:30 PM">11:30 PM</option>
								<option value="11:45 PM">11:45 PM</option>
								<option value="12:00 AM">12:00 AM</option>
							</select>	
						</div>
					</div>
				</div>
				<button class="btn btn-important btn-update-studiohour">Update</button>
				<button class="btn btn-primary js-close-studio-modal">Cancel</button>
			</div>
			<div class="span4">
				<p><strong>Store Hours:</strong></p>
				<div class="studio-hour-list text-right">
				</div>
			</div>
		</div>
	</div>
</div>