<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix = "sec" uri = "http://www.springframework.org/security/tags"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<c:set var="today" value="<%=new java.util.Date()%>" />
 


<section>
		<table id="instructor_sch_table" class="table-fixed responsive-table-view">
			<sec:authorize ifAnyGranted="ROLE_Instructor">
		  		<caption class="clearfix">
		  				<span class="schedule-header instructor-name">${person.firstName } ${person.lastName }</span> 
					<span class="schedule-header instructor-current-date">
						<button class="btn-day-prev arrow-left"></button>
						<span class="schedule-date"><fmt:formatDate pattern="MMMMM d, yyyy" value="${today}" /></span>
						<button class="btn-day-next arrow-right"></button>
					</span> 
					<span class="schedule-header instructor-hours"></span> 
					<span class="schedule-header instructor-id">Instructor #${person.authId }</span> 
				</caption>
			</sec:authorize>	
		  <thead>
		    <tr>
		      <th class="time-header" scope="col">Time</th>
		      <th class="name-header" scope="col">Student Name</th>
		      <th class="email-header" scope="col">Send Reminder</th>
		      <th class="phone-header" scope="col">Phone #</th>
		      <th class="duartion-header" scope="col">Duration</th>
			  <th class="type-header" scope="col">Type</th>
			  <th class="room-header" scope="col">Store</th>
		      <th class="launch-header" scope="col">Launch</th>
		      <th class="completed-header" scope="col">Completed?</th>
		      <th class="remarks-header" scope="col">Internal Remarks</th>
		      <th class="notes-header" scope="col">Notes to Student</th>		      
              <th class="notes-header" scope="col">Level Up</th>		      		      
		    </tr>
		  </thead>
		  <tbody class="instructor-list-schedule">
		  </tbody>



		
	</table>


</section>

<script id="tmpl_instructor_sch" type="text/template">

	
		<sec:authorize ifAnyGranted="ROLE_Instructor">
	 		<td class="time-header" data-label="Time"><span>{{startTime}}</span></td>
			<td class="name-header" data-label="Student Name"><span>{{customerFirstName}} {{customerLastName}}</span></td>
			<td class="email-header" data-label="Email" data-id-email-reminder="{{appointmentId}}">
				
				{{#enabledSendRemainder}}
					<span class="reminder-link-container">
						<a class="reminder-link" cid="{{cid}}" href="javascript:;">
							<svg class="email-icon" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
								<path d="M.05 3.555L8 8.414l7.95-4.859A2 2 0 0014 2H2A2 2 0 00.05 3.555zM16 4.697l-5.875 3.59L16 11.743V4.697zm-.168 8.108L9.157 8.879 8 9.586l-1.157-.707-6.675 3.926A2 2 0 002 14h12a2 2 0 001.832-1.195zM0 11.743l5.875-3.456L0 4.697v7.046z"/>
							</svg></a>
					</span>
				{{/enabledSendRemainder}}
			 	{{^enabledSendRemainder}}
			 		<span class="reminder-link-container">
						<button class="reminder-link disabled" disabled>
							<svg class="email-icon" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
								<path d="M.05 3.555L8 8.414l7.95-4.859A2 2 0 0014 2H2A2 2 0 00.05 3.555zM16 4.697l-5.875 3.59L16 11.743V4.697zm-.168 8.108L9.157 8.879 8 9.586l-1.157-.707-6.675 3.926A2 2 0 002 14h12a2 2 0 001.832-1.195zM0 11.743l5.875-3.456L0 4.697v7.046z"/>
							  </svg>
						</button>
					</span>
				{{/enabledSendRemainder}}
			</td>
			<td class="phone-header" data-label="Phone #"><span>{{formattedcustomerPhone}}</span></td>
			<td class="duartion-header" data-label="Duration"><span>{{duration}}</span></td>
			<td class="type-header" data-label="Type"><span>{{activityName}}</span></td>
			<td class="room-header service-{{serviceId}}" data-label="Store">
				<span>
						<p class="store-number">Store {{locationExternalId}}</p>
						<p class="room-number">{{roomNumberName}}</p>
						<p class="store-online">Online</p>
				</span>
			</td>

			<td class="launch-header" data-label="Launch" data-id-zoom-link="{{appointmentId}}">
				<span>
			{{#enabledLaunch}} 
				<a href="{{zoomMeetingUrl}}" target="_blank" class="zoom-link" data-launch-id="{{launchStatus}}">
					<svg class="zoom-icon" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
						<path d="M2.667 3h6.666C10.253 3 11 3.746 11 4.667v6.666c0 .92-.746 1.667-1.667 1.667H2.667C1.747 13 1 12.254 1 11.333V4.667C1 3.747 1.746 3 2.667 3z"/>
						<path d="M7.404 8.697l6.363 3.692c.54.313 1.233-.066 1.233-.697V4.308c0-.63-.693-1.01-1.233-.696L7.404 7.304a.802.802 0 000 1.393z"/>
					  </svg>
				</a>
			{{/enabledLaunch}}
			{{^enabledLaunch}}
				<button href="https://guitarcenter.zoom.us/j/" target="_blank" class="zoom-link disabled {{launchStatus}}">
					<svg class="zoom-icon" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
						<path d="M2.667 3h6.666C10.253 3 11 3.746 11 4.667v6.666c0 .92-.746 1.667-1.667 1.667H2.667C1.747 13 1 12.254 1 11.333V4.667C1 3.747 1.746 3 2.667 3z"/>
						<path d="M7.404 8.697l6.363 3.692c.54.313 1.233-.066 1.233-.697V4.308c0-.63-.693-1.01-1.233-.696L7.404 7.304a.802.802 0 000 1.393z"/>
					  </svg>
					  
					<p class="in-store">In Store</p>
					<p class="finished-text">Finished</p>
				</button>
			{{/enabledLaunch}}
				</span>
			</td>
			<td class="completed-header" data-label="Completed?" data-id-status="{{appointmentId}}">
				<span>
				{{#enabledStatus}} 
					<button class="btn-default yes lesson-completed" style="display:none;" data-id-appointment="{{appointmentId}}">Yes</button>
					<button class="btn-default no status-modal" style="display:none;" data-id-appointment="{{appointmentId}}">No</button>
					<p class="lesson-status-msg">{{showStatus}}</p>
					<p class="undo-wrapper">(<a href="#" class="undo-status" data-id-appointment="{{appointmentId}}" data-backdrop="static">undo</a>)</p>
				{{/enabledStatus}}

			 {{^enabledStatus}}
				<button class="btn-default yes lesson-completed" data-id-appointment="{{appointmentId}}">Yes</button><button class="btn-default no status-modal" data-id-appointment="{{appointmentId}}">No</button>
				<p class="lesson-status-msg" style="display:none;"></p>
				<p class="undo-wrapper" style="display:none;">(<a href="#" class="undo-status" data-id-appointment="{{appointmentId}}" data-backdrop="static">undo</a>)</p>
			{{/enabledStatus}}
				</span>
			</td>
			<td class="remarks-header" data-label="Internal Remarks" data-id-remarks="{{appointmentId}}">	
		 
				{{#enabledComments}}
					<span class="edit-remarks">(<a href="#" class="remarks-modal" data-id-appointment="{{appointmentId}}" data-backdrop="static">edit</a>)</span>
					<span class="add-remarks" style="display:none;"><button data-id-appointment="{{appointmentId}}" class="btn-circle remarks-modal" data-backdrop="static"></button></span>
				{{/enabledComments}}
			 	{{^enabledComments}}
			 		<span class="edit-remarks" style="display:none;">(<a href="#" class="remarks-modal" data-id-appointment="{{appointmentId}}" data-backdrop="static">edit</a>)</span>
					<span class="add-remarks"><button data-id-appointment="{{appointmentId}}" class="btn-circle remarks-modal" data-backdrop="static"></button></span>
				{{/enabledComments}}
			</td>
			<td class="notes-header" data-label="Notes to Student" data-id-student-notes="{{appointmentId}}" data-backdrop="static">
					{{#enabledStudentNote}}
						<span class="edit-notes">(<a href="#" class="notes-modal" data-id-appointment="{{appointmentId}}" data-backdrop="static">edit</a>)</span>
						<span class="add-notes" style="display:none;"><button data-id-appointment="{{appointmentId}}" class="btn-circle notes-modal" data-backdrop="static"></button></span>
					{{/enabledStudentNote}}
					{{^enabledStudentNote}}
						<span class="add-notes"><button class="btn-circle notes-modal" data-id-appointment="{{appointmentId}}" data-backdrop="static"></button></span>
						<span class="edit-notes" style="display:none;">(<a href="#" class="notes-modal" data-id-appointment="{{appointmentId}}" data-backdrop="static">edit</a>)</span>
					{{/enabledStudentNote}}
			</td>

			<td class="emails-header" data-label="Level Up" data-id-musicprodigy="{{appointmentId}}">
			<a target="_blank" class="musicprodigy-modal" alt_text="Level Up" data-id-appointment="{{appointmentId}}"> 	Level Up </a>            
            </td>
			
			<td id="{{appointmentId}}" class="hidden appointmentDataJson" style="display:none" data-id-appointment-json="{{appointmentId}}">
				{"{{appointmentId}}" : {"appointmentId" : "{{appointmentId}}","showStatus" : "{{showStatus"}}", "comments" : "{{comments}}", "studentNote": "{{studentNote}}", "assignment": "{{assignment}}", "practice_notes": "{{practice_notes}}", "remarks": "{{remarks}}"}}
			</td>

	</sec:authorize>
</script>

<script id="tmpl_instructor_comment_modal" type="text/template">
		<sec:authorize ifAnyGranted="ROLE_Instructor">
		    <div class="modal-dialog">
		      <div class="modal-content">
		        <div class="modal-header">
		          <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
		        </div>
		        <div class="modal-body">
		          	 <h4 class="modal-title">Internal Remarks</h4>
		        	 <textarea maxlength="2048" id="edit_instructor_comments"></textarea>
		        </div>
		        <div class="modal-footer">
		        	<p>${person.firstName } ${person.lastName }, Instructor <span class="remarks-timestamp"><fmt:formatDate type="both" dateStyle="medium"
timeStyle="medium" value="${today}" /></span></p>
		          <button type="button" class="btn-default submit-remarks">Submit</button>
		        </div>
		      </div><!-- /.modal-content -->
		    </div><!-- /.modal-dialog -->
	</sec:authorize>
</script>

<script id="tmpl_instructor_status_modal" type="text/template">
		<sec:authorize ifAnyGranted="ROLE_Instructor">
		    <div class="modal-dialog">
		      <div class="modal-content">
		        <div class="modal-header">
		          <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
		        </div>
		        <div class="modal-body">
		        	 <h4 class="modal-title">Why was the lesson not complete?</h4>
		        	<div class="control-group">
						<select id="js-lesson-status" class="js-statuses">
							 <option selected value="noShow">No Show</option>
							 <option value="canceled">Canceled</option>
							 <option value="badConnection">Bad Connection</option>
							 <option value="other">Other</option>
						</select>
					</div>
		        </div>
		        <div class="modal-footer">
		          <button type="button" class="btn-default submit-status">Submit</button>
		        </div>
		      </div><!-- /.modal-content -->
		    </div><!-- /.modal-dialog -->
	</sec:authorize>
</script>



<script id="tmpl_instructor_notes_modal" type="text/template">
		<sec:authorize ifAnyGranted="ROLE_Instructor">
		    <div class="modal-dialog" style="height: 550px;">
		      <div class="modal-content">
		        <div class="modal-header">
		          <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
		        </div>
  		        <div class="modal-body">
		          	 <h4 class="modal-title">Notes to Student</h4>
					
	 
				     <h8 class="modal-title">This week's assignment</h8>
 					 <textarea style="height: 60px;width: 405px;" style maxlength="2048" id="assignment"></textarea>
					
					 <h8 class="modal-title">Practice Notes</h8>
					 <textarea style="height: 60px;width: 405px;"  maxlength="2048" id="practice_notes"></textarea>	
		
					<h8 class="modal-title">Remarks</h8>
					<textarea style="height: 60px;width: 405px;" maxlength="2048" id="remarks"></textarea>

					<!-- <h8 class="modal-title">Review</h8>
					<div class="rate" id="rates">
    					<input type="radio" id="star5" class = "rev" name="rate" value="5" />
    					<label for="star5" title="text">5 stars</label>
    					<input type="radio" id="star4" class = "rev" name="rate" value="4" />
    					<label for="star4" title="text">4 stars</label>
    					<input type="radio" id="star3" class = "rev" name="rate" value="3" />
    					<label for="star3" title="text">3 stars</label>
   			 			<input type="radio" id="star2" class = "rev" name="rate" value="2" />
    					<label for="star2" title="text">2 stars</label>
    					<input type="radio" id="star1" name="rate" value="1" />
    					<label for="star1" title="text">1 star</label>
  					</div> -->
		        </div>

		        <div class="modal-footer">
					<p>
		        	<p>${person.firstName } 
		        		${person.lastName }, Instructor <span class="remarks-timestamp"><fmt:formatDate type="both" dateStyle="medium"
						timeStyle="medium" value="${today}" /></span>
					</p>
				</p>
		          <button type="button" class="btn-default submit-notes">Submit</button>
		        </div>
		      </div><!-- /.modal-content -->
		    </div><!-- /.modal-dialog -->
	</sec:authorize>
</script>

<script id="tmpl_instructor_completed_modal" type="text/template">
		<sec:authorize ifAnyGranted="ROLE_Instructor">
		    <div class="modal-dialog" style="height: 550px;">
		      <div class="modal-content">
		        <div class="modal-header">
		          <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
		        </div>
  		        <div class="modal-body">
		          	 <h4 class="modal-title">Notes to Students</h4>
					
	 
				     <h8 class="modal-title">This weeks's assignment</h8>
 					 <textarea style="height: 60px;width: 405px;" style maxlength="2048" id="assignment"></textarea>
					
					 <h8 class="modal-title">Practice notes</h8>
					 <textarea style="height: 60px;width: 405px;"  maxlength="2048" id="practice_notes"></textarea>	
		
					<h8 class="modal-title">Remarks</h8>
					<textarea style="height: 60px;width: 405px;" maxlength="2048" id="remarks"></textarea>

				<!--	<h8 class="modal-title">Review</h8>
					<div class="rate" id="rates">
    					<input type="radio" id="star5" class = "rev" name="rate" value="5" />
    					<label for="star5" title="text">5 stars</label>
    					<input type="radio" id="star4" class = "rev" name="rate" value="4" />
    					<label for="star4" title="text">4 stars</label>
    					<input type="radio" id="star3" class = "rev" name="rate" value="3" />
    					<label for="star3" title="text">3 stars</label>
   			 			<input type="radio" id="star2" class = "rev" name="rate" value="2" />
    					<label for="star2" title="text">2 stars</label>
    					<input type="radio" id="star1" name="rate" value="1" />
    					<label for="star1" title="text">1 star</label>
  					</div> -->
		        </div>

		        <div class="modal-footer">
					<p>
		        	<p>${person.firstName } 
		        		${person.lastName }, Instructor <span class="remarks-timestamp"><fmt:formatDate type="both" dateStyle="medium"
						timeStyle="medium" value="${today}" /></span>
					</p>
				</p>
		          <button type="button" class="btn-default submit-completed">Submit</button>
		        </div>
		      </div><!-- /.modal-content -->
		    </div><!-- /.modal-dialog -->
	</sec:authorize>
</script>

<script id="tmpl_instructor_reminder_modal" type="text/template">
	<sec:authorize ifAnyGranted="ROLE_Instructor">
		<div class="modal-dialog">
		  <div class="modal-content">
			<div class="modal-header">
			  <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
			</div>
			  <div class="modal-body">
				<p class="email-success-msg"></p>

			</div>
			<div class="modal-footer">
			</div>
		  </div><!-- /.modal-content -->
		</div><!-- /.modal-dialog -->
</sec:authorize>
</script>


