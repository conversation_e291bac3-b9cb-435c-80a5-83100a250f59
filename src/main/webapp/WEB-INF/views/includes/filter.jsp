<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>   
<%@ taglib prefix = "sec" uri = "http://www.springframework.org/security/tags"%> 
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<div class="filter-list hide">
	<ul class="js-serviceType-filter">
		<li class="main-filter">
			<input class="js-filter" name="servie-choose" checked type="checkbox">
			<label for="servie-choose">Services Types</label>
		</li>
		<c:if test="${0 != fn:length(listMap.serviceList) }">
			<c:forEach items="${listMap.serviceList }" var="s">
				<li>
					<input data-name="${s.serviceName }" name="serviceType" checked value="${s.serviceId }" type="checkbox">${s.serviceName }
				</li>
			</c:forEach>
		</c:if>
	</ul>
	<ul class="js-instructor-filter">
		<li class="main-filter">
			<input class="js-filter" name="instructor-choose" checked type="checkbox">Instructor
		</li>
		<c:if test="${0 != fn:length(listMap.instructorList) }">
			<c:forEach items="${listMap.instructorList }" var="i">
				<li>
					<input  data-name="${i.instructorName }" name="instructor" checked value="${i.instructorId }" type="checkbox">${i.instructorName }
				</li>
			</c:forEach>
		</c:if>
	</ul>
	<ul class="js-room-filter">
		<li class="main-filter">
			<input class="js-filter" name="room-choose" checked type="checkbox">Room
		</li>
		<c:if test="${0 != fn:length(listMap.roomList) }">
			<c:forEach items="${listMap.roomList }" var="r">
				<li>
					<input data-name="${r.profileRoomName }" name="room" checked value="${r.roomId }" type="checkbox">${r.profileRoomName }
				</li>
			</c:forEach>
		</c:if>
	</ul>
	<ul class="js-appointmentType-filter">
		<li class="main-filter">
			<input class="js-filter" name="appointment-choose" checked type="checkbox">Appointment Type
		</li>
		<c:if test="${0 != fn:length(listMap.activityList) }">
			<c:forEach items="${listMap.activityList }" var="a">
				<li>
					<input data-instructor="" data-name="${a.activityName }" name="appointmentType" value="${a.activityId }" checked type="checkbox">${a.activityName }
				</li>
			</c:forEach>
		</c:if>
	</ul>
</div>
<div class="customer-info hide">
	<h5 class="clearfix">
		Customer Info
		<a href="javascript:;" class="right js-clear-customer">Clear</a>
	</h5>
	<ul></ul>
</div>