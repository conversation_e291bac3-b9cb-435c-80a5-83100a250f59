<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix = "sec" uri = "http://www.springframework.org/security/tags"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<!--created for centralized staff page GSSP-288 -->
<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>Satff Page</title>
	<link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
	<link rel="stylesheet" href="css/gcs.min.css">
	<script data-main="js/app/scripts/staff_list.min" src="js/vendor/requirejs/require.js"></script>
</head>
<body>
	<%@include file="/WEB-INF/views/includes/header.jsp"%>
	<article>
		<nav class="top-nav">
			<div class="container">
				<ul class="top-nav-list">
					<li><a href="adminPage.htm">Studio Profile</a></li>
					<li class="active"><a href="staffPage.htm">Staff Manager</a></li>
					<li><a href="roomTemplateManagePage.htm">Room Manager</a></li>
                    <li><a href="servicePage.htm">Services &amp; Activities Management</a></li>
							<%--GSSP-211 CHANGES--%>
					<li><a href="dualInstructorAccess.htm">Multi Location Instructor</a></li>
				</ul>
			</div>
		</nav>

<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_District Manager,ROLE_Regional Vice President">

<div class="tab-title">
			<h1 class="container">Staff Management</h1>	
		</div>
		<div class="container">
		<section>
	<table id="staff_table" class="table table-fixed">
		<caption class="clearfix" >
			Staff List:
		</caption> 
		<thead>
			<tr>
				<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_District Manager,ROLE_Regional Vice President">
				<th class="cell-action"></th>
				</sec:authorize>
				<th class="cell-160">Staff Name</th>
				<th class="cell-160">Email</th>
				<th class="cell">Access Level for Location</th>
				<th class="cell-90">Active</th>
			</tr>
		</thead>
		<tbody></tbody>
	</table>
	<div class="staff-list"></div>
</section>
</div>
</sec:authorize>
		<div id="rep-load-fade1"></div>
		<div id="rep-load-modal1">
		<img id="rep-loader" src="images/report-spinner.gif" alt="" />
		</div>
		<script id="tmpl_staff" type="text/template">
	<tr>
<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_District Manager,ROLE_Regional Vice President">
		<th class="cell-action">		
			<a class="edit-staff" href="javascript:;"><i class="icon icon-edit"></i>Edit</a>
			</th>
</sec:authorize>
		<th class="cell-160">{{instructorName}}</th>
		<th class="cell-160">{{email}}</th>
		<th>{{roleLocationName}}</th>
		<th class="cell-90">{{^active}}Disabled{{/active}}{{#active}}Enabled{{/active}}</th>
	</tr>
	<tr class="table-edit-panel hide">
		<td></td>
		<td>{{instructorName}}</td>
		<td>{{email}}</td>
		<td class="js-location-wrap"></td>
		<td>
			<div class="row">
				{{^active}}
				<div class="span3 relative">
					<input class="js-input-active" name="active" type="checkbox" checked>
				</div>
				<div class="span9">
					Disable
				</div>
				{{/active}}
				{{#active}}
				<div class="span3 relative">
					<input class="js-input-active" name="active" type="checkbox">
				</div>
				<div class="span9">
					Disable
				</div>
				{{/active}}	
			</div>
		</td>
	</tr>
	<tr class="table-edit-actions hide">
		<td colspan="5">
			<div class="clearfix right">
				<button class="btn btn-primary close-edit-panel">Cancel</button>
				<button class="btn btn-important ml10 js-update-staff">Update</button>	
			</div>
		</td>
	</tr>
</script>

<script id="tmpl_location" type="text/template">
	<div class="row mb20">
		<div class="span5">
			<input type="hidden" class="span11" name="location" placeholder="Select Location">
		</div>
		<div class="span5 mr20">
			<input type="hidden" class="input-medium" name="role" placeholder="Select Level">
		</div>
		<div class="span2">
			<button class="btn btn-primary btn-small js-btn-add">Add</button>
		</div>
	</div>
	<div class="js-role-list row">
		{{#selectedLocationsList}}
		<span class="role-selected span4">
			<a class="icon icon-remove js-remove-role" data-id="{{id}}" href="javascript:;">x</a>
			{{roleToLocation}}
		</span>
		{{/selectedLocationsList}}
	</div>
</script>
<script id="tmpl_role_item" type="text/template">
	<span class="role-selected span6">
		<a class="icon icon-remove js-remove-role" data-id="{{id}}" href="javascript:;">x</a>
		{{roleToLocation}}
	</span>
</script>
</article>
</body>
</html>
