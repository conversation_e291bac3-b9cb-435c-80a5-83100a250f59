<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix = "sec" uri = "http://www.springframework.org/security/tags"%>
<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>Profile Manage</title>
	<link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
	<link rel="stylesheet" href="css/gcs.min.css">
	<script data-main="js/app/scripts/studio_centralize.min" src="js/vendor/requirejs/require.js"></script>
</head>
<body>
	<%@include file="/WEB-INF/views/includes/header.jsp"%>
	<article>
		<nav class="top-nav">
			<div class="container">
				<ul class="top-nav-list">
				<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_District Manager,ROLE_Regional Vice President">
					<li class="active"><a href="">Studio Profile</a></li>
					</sec:authorize>
					<sec:authorize ifAnyGranted="ROLE_Site Admin">
					<li><a href="staffPage.htm">Staff Manager</a></li> <!-- centralized staff page GSSP-288 -->
					<li><a href="roomTemplateManagePage.htm">Room Manager</a></li>
                    <li><a href="servicePage.htm">Services &amp; Activities Manager</a></li>
					<!-- <li><a href="">Scheduling</a></li> -->
												<%--GSSP-211 CHANGES--%>
					<li><a href="dualInstructorAccess.htm">Multi Location Instructor</a></li>					
				</sec:authorize>
				</ul>
			</div>
		</nav>
		<div class="tab-title">
			<h1 class="container">Profile Management</h1>	
		</div>

		<div class="container">
			<section>
				<table id="studio_table" class="table table-fixed">
					<caption class="clearfix">
						Studio Profile Results:
						<form action="" class="form-search right">
							<input type="text" class="search-query" placeholder="Search Studio Name or Studio Number">
							<button class="btn-search" type="submit"><i class="icon-search"></i> </button>
						</form>
					</caption>
					<thead>
						<tr>
							<th class="cell-160">Store Location</th>
							<th class="cell-120">Store Number</th>
							<th class="cell-120">Active Profile</th>
							<th></th>
						</tr>
					</thead>
					<tbody></tbody>
				</table>
				<div class="studio-list"></div>
			</section>
			
			<script id="tmpl_studio" type="text/template">
				<tr>
					<th class="cell-160">
						{{#enabled}}
							<a href="forwardToScheduler.htm?locationId={{recordId}}">{{locationName}}</a>
						{{/enabled}}
						{{^enabled}}
							<a href="#">{{locationName}}</a>	
						{{/enabled}}
					</th>
					<th class="cell-120">{{externalId}}</th>
					<th class="cell-120">
						{{^enabled}}

						  <sec:authorize ifAnyGranted="ROLE_Site Admin">
							<a href="createProfileFromLocation.htm?locationId={{recordId}}&locationName={{locationName}}&externalId={{externalId}}" class="btn btn-primary btn-small">Create Profile
							</a>
						 	</sec:authorize>

						{{/enabled}}
						{{#enabled}}
							{{enabled}}
						{{/enabled}}
					</th>
					<th></th>
				</tr>
			</script>
		</div>
	</article>
</body>
</html>