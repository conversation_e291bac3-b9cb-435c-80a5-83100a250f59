<%@page import="org.apache.commons.lang.StringUtils"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix = "sec" uri = "http://www.springframework.org/security/tags"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>Schedule - GCS</title>
	<link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
	<link rel="stylesheet" href="css/gcs.min.css?vId=${project.version}">
	<script src="js/vendor/requirejs/require.js"></script>
	<script>
	window.addEventListener('DOMContentLoaded', (event) => {
	require(['js/app/scripts/schedule.min.js?vId=${project.version}'])
	});
    //require(['js/app/scripts/schedule.min.js?vId=${project.version}'])
	</script>
	<!-- <script data-main="js/app/scripts/schedule.min" src="js/vendor/requirejs/require.js"></script> -->
</head>

<body>
<%@include file="/WEB-INF/views/includes/header.jsp"%><!-- the page contains information about logo, user information which on top of the page -->
<article>
	<nav class="top-nav">
		<div class="container">
			<ul class="top-nav-list">
				<!--
					GSSP-285 changes made.
					 -->
				<li class="active js-show-schedule" data-tab=".tab-schedule"><a href="#beforeGoCalendar.htm">Scheduling</a></li>
				<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead,ROLE_District Manager,ROLE_Regional Vice President">
					<li class="js-show-instructor-staff" data-tab=".tab-instructor-staff"><a href="javascript:;">Instructors</a></li>
				</sec:authorize>
				<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_District Manager,ROLE_Regional Vice President,ROLE_Studio Manager">
					<li class="js-show-staff" data-tab=".tab-staff"><a href="javascript:;">Staff</a></li>
				</sec:authorize>
				<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead,ROLE_District Manager,ROLE_Regional Vice President">
					<li class="js-show-service-activity" data-tab=".tab-service-activity"><a href="javascript:;">Services &amp; Activities</a></li>
				</sec:authorize>
				<!-- GSSP-363 :: Added Changes as part of Instructor View -->
				<sec:authorize ifAnyGranted="ROLE_Instructor">
					<li class="js-show-instructor_schedule" data-tab=".tab-Instructor_schedule"><a href="javascript:;">Instructor Schedule</a></li>
				</sec:authorize>
				<!-- GSSP-260 enabling room for istructor -->
				<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead,ROLE_District Manager,ROLE_Regional Vice President,ROLE_Instructor">
					<li class="js-show-room" data-tab=".tab-room"><a href="javascript:;">Rooms</a></li>
				</sec:authorize>
				<!-- GSSP-368 booked appointments
				<sec:authorize ifAnyGranted="ROLE_Site Admin">
					<li class="js-show-booked-appointments" data-tab=".tab-booked-appointments"><a href="javascript:;">Appointments</a></li>
				</sec:authorize>-->
				<li class="js-show-customer" data-tab=".tab-customer"><a href="javascript:;">Customers</a></li>
				<!-- For gcss-569,give the stuio assosiate access to report -->
				<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead,ROLE_Studio Associate,ROLE_Instructor,ROLE_District Manager,ROLE_Regional Vice President">
					<li class="js-show-report" data-tab=".tab-report"><a href="javascript:;">Reports</a></li>
				</sec:authorize>
			</ul>
		</div>
	</nav>
	<div class="tab-title hidden-print">
		<div class="container js-studio-info">
			<div class="row">
				<div class="span5">
					<h4 class="mb10">${locationInfo.externalId } ${locationInfo.locationName } Studio Profile</h4>
					<p class="mb10"><strong>Studio Location: </strong>${locationInfo.address1 },<c:if test="${!empty fn:trim(locationInfo.address2)}"> ${locationInfo.address2 },</c:if> ${locationInfo.city }, ${locationInfo.state } ${locationInfo.zip }</p>

					<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead,ROLE_Studio Associate ">
						<button type="button" class="btn btn-important btn-long btn-create-app">Create Appointment</button>
					</sec:authorize>
				</div>
				<div class="span6 offset1">
					<div class="row">
						<h6 class="span5 text-right">Studio Hours:&nbsp;&nbsp;&nbsp;</h6>
						<div class="span7 text-left">
							<div class="studio-hour">
								<c:forEach var="studioDto" items="${loadAvailability }">
									<div class="row">
										<span class="span5">${studioDto.dayString }</span>
										<span class="span7">${studioDto.timeString }</span>
									</div>
								</c:forEach>
							</div>
							<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager">
								<div class="row">
										<span class="span5">
											<a id="edit_hour" href="javascript:;">Edit Hours</a>
										</span>
								</div>

							</sec:authorize>
						</div>
						<h6 class="span5 text-right">Studio TimeOff:&nbsp;&nbsp;&nbsp;</h6>
						<div class="span7 text-left">
							<div class="studio-time-off-hour">
								<c:forEach var="studioDto" items="${loadProfileTimeOff}">
									<div class="row">
										<span class="span5">${studioDto.dayString }</span>
										<span class="span7">${studioDto.timeString }</span>
									</div>
								</c:forEach>
							</div>
							<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager">
								<div class="row">
										<span class="span5">
											  <a href="javascript:;" class="js-set-timeoff-pr ">Edit TimeOff</a>
											<!-- <a id="profiletimeoff_edit" href="javascript:;">Edit TimeOff</a> -->
										</span>
								</div>

							</sec:authorize>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<sec:authorize ifAnyGranted="ROLE_Site Admin">
					<c:choose>
						<c:when test="${'Y' eq locationProfileInf.enabled }">
							<a class="right js-studio-is-enable ${locationProfileInf.enabled }" href="locationProfile/disableProfile.htm?profileId=${locationProfileInf.profileId }">Disable Studio Profile</a>
						</c:when>
						<c:otherwise>
							<a class="right js-studio-is-enable ${locationProfileInf.enabled }" href="locationProfile/enableProfile.htm?profileId=${locationProfileInf.profileId }">Enable Studio Profile</a>
						</c:otherwise>
					</c:choose>
				</sec:authorize>
			</div>
		</div>
		<div class="container js-report-info hide">
			<h4 class="report-title mb10" class="mb10">Report</h4>
			<p class="mb20">
				<strong>Date: </strong>
				<span class="js-report-date"></span>&nbsp;&nbsp;&nbsp;
				<span class="js-report-time"></span>
			</p>
			<div class="row mb10">
				<div class="span3">
					<h5>Select Report to Run:</h5>
				</div>
				<!-- instructor open appointments -->
				<!--
               <div id="instructorNameHeadLine" class="left mr05 hide">
                       <h5>InstructorName&nbsp;&nbsp;&nbsp;</h5>
               </div>
                -->
				<!--
                <div id="instructorORHeadLine" class="left mr10 hide">
                        <h5>&nbsp;&nbsp;&nbsp;&nbsp;</h5>
                </div>
                 -->

				<!-- Added for NewInsAptReport _ June 2015 Enhancement -->
				<!-- GSSP-1990 new field added InstructorNam -->
				<div id="instructorIdHeadLine" class="left mr10 hide">
					<h5>Instructor #&nbsp;&nbsp;&nbsp;</h5>
				</div>
				<!-- GSSP-210 new field added InstructorNam -->
				<div id="customerIdHeadLine" class="left mr10 hide">
					<h5>CustomerId #&nbsp;&nbsp;&nbsp;</h5>
				</div>
				<!-- Added new Active Students Report for GSSP-185 -->
				<div id="activityIdHeadLine" class="left mr10 hide">
					<h5>Lesson Type:&nbsp;&nbsp;&nbsp;</h5>
				</div>
				<!-- For GSSP-170 -->
				<div id="conflictHeadLine" class="left mr52 hide">
					<h5>By Type:</h5>
				</div>
				<!-- For GSSP-161, Added head line for Day -->
				<div id="dayHeadLine" class="left mr64 hide">
					<h5>By Day:</h5>
				</div>
				<!-- For GSSP-161, Modified head line for Date Range -->
				<div id="dRangeheadLine" class="span3">
					<h5>Select Date Range:</h5>
				</div>
				<!-- For GSSP-161, Added head line for Time Range -->
				<div id="tRangeheadLine" class="span3 hide">
					<h5>Select Time Range:</h5>
				</div>
			</div>
			<div class="row mb10">
				<form action="" id="report_form">
					<div class="left mr20">
						<select name="reportType" class="select-medium" placeholder="Select Report">
							<option></option>
							<option value="1">Master Schedule</option>
							<option value="2">Rehearsal Booking</option>
							<option value="3">Rehearsal Schedule</option>
							<option value="4">Instructor Schedule</option>
							<option value="5">Cancelled Appointments</option>
							<!-- Added for NewInsAptReport _ June 2015 Enhancement -->
							<option value="6">Instructor Open Appointments</option>
							<!-- For GSSP-161 -->
							<option value="9">Appointments Outside Time Range</option>
							<!-- For GSSP-170 -->
							<option value="8">Conflicting Appointments</option>

							<!-- Modified for NewInsAptReport _ June 2015 Enhancement -->
							<option value="7">Export Lessons</option>
							<!-- Added new Active Students report-GSSP-185 -->
							<option value="10">Active Students</option>
							<!-- Added new  Students report-GSSP-203 -->
							<option value="11">Student Check In Sheet</option>
							<!-- Added new  InActive Studens  report-GSSP-205 -->
							<option value="12">InActive Students</option>


							<sec:authorize ifAnyGranted="ROLE_Site Admin">
								<!-- Added new  Export Scheduling Summary Report-GSSP-213 -->
								<option value="13">Export Scheduling Summary Report</option>
							</sec:authorize>

							<!-- Added new  Appointment History Reportt-GSSP-210 -->
							<option value="14">Appointment History Report</option>
                            <option value="15">Instructor Schedule Status Report</option>
						</select>
					</div>
					<!-- For GSSP-170 -->
					<!-- instructor open appointments -->
					<!--
                   <div id="instructorName" class="left mr10 hide">
                       <input name="instructorName" type="text" class="input-xsmall left" placeholder="First_Name Last_Name">
                   </div>
                    -->
					<!-- instructor open appointments -->
					<!-- <div id="orHeadLine" class="left mr10 hide">
                        <h5>OR</h5>
                    </div>
                     -->
					<!-- Added for NewInsAptReport _ June 2015 Enhancement -->

					<div id="inputExternalId" class="left mr10 hide">
						<input name="inputExternalId" type="text" class="input-xsmall left" placeholder="Instructor #">
					</div>
					<!-- Added for gssp-210 -->

					<div id="externalId" class="left mr10 hide">
						<input name="externalId" type="text" class="input-xsmall left" placeholder="Customer #">
					</div>
					<!-- Added new Active Students report-GSSP-185 -->

					<div id="activityType" class="left mr10 hide">
						<input type="hidden" class="input-xsmall left" name="activityType" placeholder="Lesson">
					</div>


					<!-- For GSSP-170 -->
					<div id="conflictId" class="left mr20 hide">
						<select name="reportSubType" class="input-xsmall left" placeholder="Sub Type">
							<option></option>
							<option value="1">By Instructor</option>
							<option value="2">By Room</option>
						</select>
					</div>
					<!-- For GSSP-161 -->
					<div id="dayId" class="left mr20 hide">
						<select name="dayType" class="input-xsmall left" placeholder="Day">
							<option></option>
							<option value="7">Sunday</option>
							<option value="1">Monday</option>
							<option value="2">Tuesday</option>
							<option value="3">Wednesday</option>
							<option value="4">Thursday</option>
							<option value="5">Friday</option>
							<option value="6">Saturday</option>
						</select>
					</div>
					<!-- Modified for NewInsAptReport _ June 2015 Enhancement -->
					<!-- Modified for GSSP-161 Enhancement -->
					<div id="sDate" class="left mr10 hide">
						<input name="startDate" type="text" class="input-medium left" placeholder="Select Start Date">
						<i class="left icon-calendar"></i>
					</div>
					<!-- Modified for GSSP-161 Enhancement -->
					<div id="eDate" class="left mr20 hide">
						<input name="endDate" type="text" class="input-medium left" placeholder="Select End Date">
						<i class="left icon-calendar"></i>
					</div>
					<!-- For GSSP-161 -->
					<div id="tRange" class="hide">
						<div class="left mr20">
							<span class="pre">From:</span>
							<select name="tFrom" class="input-xsmall left" placeholder="Time from">
								<option></option>
								<option value="05:00">05:00 AM</option>
								<option value="05:15">05:15 AM</option>
								<option value="05:30">05:30 AM</option>
								<option value="05:45">05:45 AM</option>
								<option value="06:00">06:00 AM</option>
								<option value="06:15">06:15 AM</option>
								<option value="06:30">06:30 AM</option>
								<option value="06:45">06:45 AM</option>
								<option value="07:00">07:00 AM</option>
								<option value="07:15">07:15 AM</option>
								<option value="07:30">07:30 AM</option>
								<option value="07:45">07:45 AM</option>
								<option value="08:00">08:00 AM</option>
								<option value="08:15">08:15 AM</option>
								<option value="08:30">08:30 AM</option>
								<option value="08:45">08:45 AM</option>								
								<option value="09:00">09:00 AM</option>
								<option value="09:15">09:15 AM</option>
								<option value="09:30">09:30 AM</option>
								<option value="09:45">09:45 AM</option>
								<option value="10:00">10:00 AM</option>
								<option value="10:15">10:15 AM</option>
								<option value="10:30">10:30 AM</option>
								<option value="10:45">10:45 AM</option>
								<option value="11:00">11:00 AM</option>
								<option value="11:15">11:15 AM</option>
								<option value="11:30">11:30 AM</option>
								<option value="11:45">11:45 AM</option>
								<option value="12:00">12:00 PM</option>
								<option value="12:15">12:15 PM</option>
								<option value="12:30">12:30 PM</option>
								<option value="12:45">12:45 PM</option>
								<option value="13:00">01:00 PM</option>
								<option value="13:15">01:15 PM</option>
								<option value="13:30">01:30 PM</option>
								<option value="13:45">01:45 PM</option>
								<option value="14:00">02:00 PM</option>
								<option value="14:15">02:15 PM</option>
								<option value="14:30">02:30 PM</option>
								<option value="14:45">02:45 PM</option>
								<option value="15:00">03:00 PM</option>
								<option value="15:15">03:15 PM</option>
								<option value="15:30">03:30 PM</option>
								<option value="15:45">03:45 PM</option>
								<option value="16:00">04:00 PM</option>
								<option value="16:15">04:15 PM</option>
								<option value="16:30">04:30 PM</option>
								<option value="16:45">04:45 PM</option>
								<option value="17:00">05:00 PM</option>
								<option value="17:15">05:15 PM</option>
								<option value="17:30">05:30 PM</option>
								<option value="17:45">05:45 PM</option>
								<option value="18:00">06:00 PM</option>
								<option value="18:15">06:15 PM</option>
								<option value="18:30">06:30 PM</option>
								<option value="18:45">06:45 PM</option>
								<option value="19:00">07:00 PM</option>
								<option value="19:15">07:15 PM</option>
								<option value="19:30">07:30 PM</option>
								<option value="19:45">07:45 PM</option>
								<option value="20:00">08:00 PM</option>
								<option value="20:15">08:15 PM</option>
								<option value="20:30">08:30 PM</option>
								<option value="20:45">08:45 PM</option>
								<option value="21:00">09:00 PM</option>
								<option value="21:15">09:15 PM</option>
								<option value="21:30">09:30 PM</option>
								<option value="21:45">09:45 PM</option>
								<option value="22:00">10:00 PM</option>
								<option value="22:15">10:15 PM</option>
								<option value="22:30">10:30 PM</option>
								<option value="22:45">10:45 PM</option>
								<option value="23:00">11:00 PM</option>
								<option value="23:15">11:15 PM</option>
								<option value="23:30">11:30 PM</option>
								<option value="23:45">11:45 PM</option>
							</select>
						</div>
						<div class="left mr20">
							<span class="pre">To:</span>
							<select name="tTo" class="input-xsmall left" placeholder="Time to">
								<option></option>
								<option value="05:00">05:00 AM</option>
								<option value="05:15">05:15 AM</option>
								<option value="05:30">05:30 AM</option>
								<option value="05:45">05:45 AM</option>
								<option value="06:00">06:00 AM</option>
								<option value="06:15">06:15 AM</option>
								<option value="06:30">06:30 AM</option>
								<option value="06:45">06:45 AM</option>
								<option value="07:00">07:00 AM</option>
								<option value="07:15">07:15 AM</option>
								<option value="07:30">07:30 AM</option>
								<option value="07:45">07:45 AM</option>
								<option value="08:00">08:00 AM</option>
								<option value="08:15">08:15 AM</option>
								<option value="08:30">08:30 AM</option>
								<option value="08:45">08:45 AM</option>
								<option value="09:00">09:00 AM</option>
								<option value="09:15">09:15 AM</option>
								<option value="09:30">09:30 AM</option>
								<option value="09:45">09:45 AM</option>
								<option value="10:00">10:00 AM</option>
								<option value="10:15">10:15 AM</option>
								<option value="10:30">10:30 AM</option>
								<option value="10:45">10:45 AM</option>
								<option value="11:00">11:00 AM</option>
								<option value="11:15">11:15 AM</option>
								<option value="11:30">11:30 AM</option>
								<option value="11:45">11:45 AM</option>
								<option value="12:00">12:00 PM</option>
								<option value="12:15">12:15 PM</option>
								<option value="12:30">12:30 PM</option>
								<option value="12:45">12:45 PM</option>
								<option value="13:00">01:00 PM</option>
								<option value="13:15">01:15 PM</option>
								<option value="13:30">01:30 PM</option>
								<option value="13:45">01:45 PM</option>
								<option value="14:00">02:00 PM</option>
								<option value="14:15">02:15 PM</option>
								<option value="14:30">02:30 PM</option>
								<option value="14:45">02:45 PM</option>
								<option value="15:00">03:00 PM</option>
								<option value="15:15">03:15 PM</option>
								<option value="15:30">03:30 PM</option>
								<option value="15:45">03:45 PM</option>
								<option value="16:00">04:00 PM</option>
								<option value="16:15">04:15 PM</option>
								<option value="16:30">04:30 PM</option>
								<option value="16:45">04:45 PM</option>
								<option value="17:00">05:00 PM</option>
								<option value="17:15">05:15 PM</option>
								<option value="17:30">05:30 PM</option>
								<option value="17:45">05:45 PM</option>
								<option value="18:00">06:00 PM</option>
								<option value="18:15">06:15 PM</option>
								<option value="18:30">06:30 PM</option>
								<option value="18:45">06:45 PM</option>
								<option value="19:00">07:00 PM</option>
								<option value="19:15">07:15 PM</option>
								<option value="19:30">07:30 PM</option>
								<option value="19:45">07:45 PM</option>
								<option value="20:00">08:00 PM</option>
								<option value="20:15">08:15 PM</option>
								<option value="20:30">08:30 PM</option>
								<option value="20:45">08:45 PM</option>
								<option value="21:00">09:00 PM</option>
								<option value="21:15">09:15 PM</option>
								<option value="21:30">09:30 PM</option>
								<option value="21:45">09:45 PM</option>
								<option value="22:00">10:00 PM</option>
								<option value="22:15">10:15 PM</option>
								<option value="22:30">10:30 PM</option>
								<option value="22:45">10:45 PM</option>
								<option value="23:00">11:00 PM</option>
								<option value="23:15">11:15 PM</option>
								<option value="23:30">11:30 PM</option>
								<option value="23:45">11:45 PM</option>
								<option value="23:59">12:00 AM</option>
							</select>
						</div>
					</div>
					<div class="left">
						<button type="button" class="btn btn-medium btn-important js-create-report">Generate Report</button>
					</div>
					<!-- For GSSP-170 -->
					<div id="rep-fade"></div>
					<div id="rep-modal">
						<img id="rep-loader" src="images/report-spinner.gif" alt=""/>
					</div>
				</form>
			</div>
			<!-- GSSP-1990 new field added InstructorNam -->
			<div class="row mb10">
				<div id="checking" class="left mr02 hide">
					<h5><span style="padding-left:265px">OR</span></h5>
				</div>
			</div>
			<div class="row mb10">
				<div id="instructorNameHeadLine" class="left mr05 hide">
					<h5><span style="padding-left:220px">Instructor Name</span></h5>
				</div>
				<!-- <div id="instructorIdHeadLine" class="left mr10 hide">
                        <h5><span style="padding-left:240px">Instructor #</span></h5>
                </div>
                 -->
			</div>
			<!-- GSSP-1990 new field added InstructorNam -->
			<div class="row mb10">
				<form action="" id="report_form2">
					<div id="instructorORHeadLine" class="left mr10 hide">
						<h5><span style="padding-left:200px">&nbsp;</span></h5>
					</div>

					<div id="instructorName" class="left mr10 hide">
						<input name="instructorName" type="text" class="input-xmedium left" placeholder="First_Name Last_Name">
					</div>
					<!--
					<div id="inputExternalId" class="left mr10 hide">
							<input name="inputExternalId" type="text" class="input-xsmall left" placeholder="Instructor #">
						</div>
						 -->
				</form>
			</div>
		</div>
		<div class="container js-booked-appointment-info hide">
			<h4 class="report-title mb10" class="mb10">Booked Appointments</h4>
			<div class="row mb10">
				<!-- For GSSP-161, Modified head line for Date Range -->
				<div class="span3">
					<h5>Select Datetime Range:</h5>
				</div>
			</div>
			<div class="row mb10">
				<form action="" id="booked_appointment_form">
					<div class="startDate left mr10">
						<input name="startDate" type="text" class="input-medium left" placeholder="Select Start Date">
						<i class="left icon-calendar"></i>
					</div>
					<div class="endDate left mr20">
						<input name="endDate" type="text" class="input-medium left" placeholder="Select End Date">
						<i class="left icon-calendar"></i>
					</div>
					<div class="left">
						<button type="button" class="btn btn-medium btn-important js-query-appointments">Show Appointments</button>
					</div>
				</form>
			</div>
		</div>
	</div>
	</div>
	<div class="tab-wrap">
		<div class="container cal-wrap tab-schedule tab-content active">
			<%@include file="/WEB-INF/views/includes/schedule.jsp"%>
		</div>

		<div class="container tab-content tab-customer">
			<%@include file="/WEB-INF/views/includes/customer.jsp"%>
		</div>

		<div class="container tab-content tab-instructor-staff">
			<%@include file="/WEB-INF/views/includes/instructor_staff.jsp"%>
		</div>
		<!--
			GSSP-285 changes made.
				-->
		<div class="container tab-content tab-staff">
			<%@include file="/WEB-INF/views/includes/staff.jsp"%>
		</div>

		<div class="container tab-content tab-service-activity">
			<%@include file="/WEB-INF/views/includes/service.jsp"%>
			<%@include file="/WEB-INF/views/includes/activity.jsp"%>
		</div>

		<div class="container tab-content tab-Instructor_schedule">
			<%@include file="/WEB-INF/views/includes/Instructor_schedule.jsp"%>
		</div>

		<div class="container tab-content tab-room">
			<%@include file="/WEB-INF/views/includes/room.jsp"%>
		</div>

		<%-- <div class="container tab-content tab-booked-appointments">
			<%@include file="/WEB-INF/views/includes/bookedAppointments.jsp"%>
		</div> --%>

		<div class="container tab-content tab-report">
			<%@include file="/WEB-INF/views/includes/report.jsp"%>
		</div>

		<div class="container tab-content tab-customer_detail">
			<%@include file="/WEB-INF/views/includes/customer_detail.jsp"%>
		</div>

	</div>
</article>
<%@include file="/WEB-INF/views/includes/appointmentmodal.jsp"%><!-- the appointment create, update etc. dialog page -->

<!-- Changes for Made for GSSP-241 -->
<%@include file="/WEB-INF/views/includes/conflicting_appointment_modal.jsp"%>

<%@include file="/WEB-INF/views/includes/pagination.jsp"%>


<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead">
	<%@include file="/WEB-INF/views/includes/edithourmodal.jsp"%><!-- the modal of edithour -->
	<%@include file="/WEB-INF/views/includes/profile_timeoff_modal.jsp"%>
	<%@include file="/WEB-INF/views/includes/edit_customer_modal.jsp"%>
</sec:authorize>
<div class="mask"></div><!-- the modal mask -->
</body>
</html>