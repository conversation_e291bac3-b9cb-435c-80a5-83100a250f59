<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix = "sec" uri = "http://www.springframework.org/security/tags"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>  
    
<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>Multi Location Instructor</title>
	<link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
	<link rel="stylesheet" href="css/gcs.min.css">
	<script data-main="js/app/scripts/dual_instructor_manage.min" src="js/vendor/requirejs/require.js"></script>
</head>
<body>
	<%@include file="/WEB-INF/views/includes/header.jsp"%>
	<article>
		<nav class="top-nav">
			<div class="container">
				<ul class="top-nav-list">
					<li><a href="adminPage.htm">Studio Profile</a></li>
					<li><a href="staffPage.htm">Staff Manager</a></li> <!-- centralized staff page GSSP-288 -->
					<li><a href="roomTemplateManagePage.htm">Room Manager</a></li>
                    <li><a href="servicePage.htm">Services &amp; Activities Management</a></li>
					<li class="active"><a href="dualInstructorAccess.htm">Multi Location Instructor</a></li>
					
				</ul>
			</div>
		</nav>
		<div class="tab-title">
			<h1 class="container">Multi Location Instructor</h1>	
		</div>
		<form class="container form-horizontal js-dual-form" action="">
						<div class="form-msg error hide"></div>
						<div class="control-group">
							<label for="instructorExternalID">Enter Instructor ID:</label>
							<div class="controls">
								<input class="input-medium" name="instructorExternalID" placeholder="Enter Instructor External ID">
							</div>
						</div>
						<div class="control-group">
							<label for="location">Select the Location:</label>
							<div class="controls">
								<select class="select-medium" placeholder="Select Location" name="location">
									<option value="-1" >Select Location</option>
									<c:forEach var="locationdto" items="${locationlist}">
										<option value="${locationdto.locationId }">${locationdto.locationName}</option>
									</c:forEach>
								</select>
							</div>
						</div>
		                
		            <div class="control-group">
							<label for="">&nbsp;</label>
							<div class="controls">
								<button type="button" class="btn btn-important btn-small mr5 js-add-dual-instructor">Grant Access</button>
								<button type="button" class="btn btn-important btn-small mr5 js-delete-dual-instructor">Remove Access</button>
								<button type="button" class="btn btn-important btn-small mr20 js-reset-dual-instructor">Reset</button>
							</div>
						</div>
		    	</form>		
			
		         </article>     	
</body>
</html>