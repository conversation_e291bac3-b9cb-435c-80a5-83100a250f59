<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix = "sec" uri = "http://www.springframework.org/security/tags"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>Room Manage Centralized</title>
	<link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
	<link rel="stylesheet" href="css/gcs.min.css">
	<script data-main="js/app/scripts/room_template_manage.min" src="js/vendor/requirejs/require.js"></script>
</head>
<body>
	<%@include file="/WEB-INF/views/includes/header.jsp"%>
	<article>
		<nav class="top-nav">
			<div class="container">
				<ul class="top-nav-list">
					<li><a href="adminPage.htm">Studio Profile</a></li>
					<li><a href="staffPage.htm">Staff Manager</a></li> <!-- centralized staff page GSSP-288 -->
					<li class="active"><a href="roomTemplateManagePage.htm">Room Manager</a></li>
                    <li><a href="servicePage.htm">Services &amp; Activities Manager</a></li>
							<%--GSSP-211 CHANGES--%>
					<li><a href="dualInstructorAccess.htm">Multi Location Instructor</a></li>	
				</ul>
			</div>
		</nav>
		<div class="tab-title">
			<h1 class="container">Room Management</h1>	
		</div>

		<div class="container">
			<div class="box">
				<div class="box-header">
					<a class="js-show-form" href="javascript:;">+ Create Room Template</a>
				</div>
				<div class="box-body row hide">
					<form class="js-room-template-form form-horizontal" action="">
						<div class="form-msg error hide"></div>
						<div class="control-group">
							<label for="roomTemplate">Room Template Name:</label>
							<div class="controls">
								<input class="input-medium" type="text" name="roomTemplateName" placeholder="Enter Template Name">
							</div>
						</div>
						<div class="control-group">
							<label for="roomType">Room Type:</label>
							<div class="controls">
								<select name="roomType" class="select-medium" placeholder="Choose Room Type">
									<option></option>
									<c:if test="${0 != fn:length(roomTypeList)}">
										<c:forEach items="${roomTypeList}" var="roomType">
											<option data-split="${roomType.canSplitRoom}" data-roomtype="${roomType.roomType}" value="${roomType.roomTypeId}">${roomType.roomType}</option>
										</c:forEach>
									</c:if>
								</select>
							</div>
						</div>
						<div class="control-group">
							<label for="roomSize">Room Size:</label>
							<div class="controls">
								<input name="roomSize" class="input-medium" type="hidden" placeholder="Choose Room Size">
							</div>
						</div>
						<div class="control-group js-split-room-wrap">
							<label for="isSplitRoom">Split room</label>
							<div class="controls">
								<div class="span3">
									<div class="relative">
										<input type="checkbox" name="isSplitRoom">
									</div>
									<div class="span8 offset1">Split Room</div>
								</div>
								<div class="span3">Note: Edit split rooms in list</div>
							</div>
						</div>
						<div class="control-group">
							<label for="services">Services Available:</label>
							<div class="controls">
								<input type="hidden" name="services">
								<div class="row mb10">
									<input name="servicesList" type="hidden" class="input-medium left">
									<div class="span2 ml10">
										<button type="button" class="btn btn-primary btn-small js-add-service">Add</button>
									</div>
								</div>
								<div class="row js-service-list span5"></div>
							</div>
						</div>
						<div class="control-group">
							<label for="activityList">Activities Available</label>
							<div class="controls">
								<input name="activities" type="hidden">
								<div class="row mb10">
									<input name="activityList" type="hidden" class="input-medium left">
									<div class="span2 ml10">
										<button type="button" class="btn btn-primary btn-small js-add-activity">Add</button>
									</div>
								</div>
								<div class="js-activity-list span5"></div>
							</div>
						</div>
						<div class="control-group">
							<label for="enabled">&nbsp;</label>
							<div class="controls">
								<div class="row">
									<div class="relative">
										<input type="checkbox" name="enabled">
									</div>
									<div class="span11 ml20">Enable when created</div>
								</div>
							</div>
						</div>
						<div class="control-group">
							<label for="">&nbsp;</label>
							<div class="controls">
								<button type="button" class="btn btn-important mr20 js-add-room">Add Template</button>
								<button type="button" class="btn btn-primary js-hide-form">Cancel</button>
							</div>
						</div>
					</form>
				</div>
			</div>

			<section>
				<table class="table table-fixed">
					<caption class="clearfix">
						Room Template List:
					</caption>
					<thead>
						<tr>
							<th class="cell-action"></th>
							<th class="cell-150">Room Template Name</th>
							<th class="cell-120">Room Type</th>
							<th class="cell-90">Size</th>
							<th>Split</th>
							<th class="cell-160">Service</th>
							<th class="cell-160">Activity</th>
							<th class="cell-90">Global Change</th>
							<th class="cell-90">Active</th>
						</tr>
					</thead>
					<tbody></tbody>
				</table>
				<div class="room-list"></div>
			</section>
		</div>
	</article>
	<script id="tmpl_room" type="text/template">
		<tr>
			<th class="cell-action">
				<a class="js-edit-room" href="javascript:;"><i class="icon icon-edit"></i>Edit</a>
			</th>
			<th class="cell-150">{{roomTemplateName}}</th>
			<th class="cell-120">{{roomType}}</th>
			<th class="cell-90">
				{{ _.helper(roomSize, 'roomSize') }}
			</th>
			<th>
				{{ _.helper(isSplitRoom, 'split') }}
			</th>
			<th class="cell-160">{{services}}</th>
			<th class="cell-160">{{activities}}</th>
			<th class="cell-90">
				Enabled
			</th>
			<th class="cell-90">
				{{ _.helper(enabled) }}
			</th>
		</tr>
		<tr class="table-edit-panel hide">
			<td></td>
			<td>
				<input name="roomTemplateName" class="row" type="text">
			</td>
			<td>
				<input name="roomType" class="row" type="hidden">
			</td>
			<td>
				<div class="js-room-size-wrap">
					<input name="roomSize" class="row" type="hidden" placeholder="Choose Room Size">
				</div>
			</td>
			<td>
				<div class="row js-split-room-wrap">
					<div class="span4 relative">
						<input type="checkbox" name="isSplitRoom">
					</div>
					<div class="span8">Split</div>
				</div>
			</td>
			<td>
				<div class="row mb10">
					<div class="span8">
						<input type="hidden" class="row" name="servicesList" placeholder="Select Service">
					</div>
					<div class="span3 ml10"><button class="btn btn-primary btn-small js-add-service">Add</buttton></div>
				</div>
				<h6>Currently Selected</h6>
				<div class="js-service-list row"></div>
			</td>
			<td>
				<div class="row mb10">
					<div class="span8">
						<input type="hidden" class="row" name="activityList" placeholder="Select Activity">
					</div>
					<div class="span3 ml10"><button class="btn btn-primary btn-small js-add-activity">Add</buttton></div>
				</div>
				<h6>Currently Selected</h6>
				<div class="js-activity-list row"></div>
			</td>
			<td>
				<div class="row">
					<div class="span4 relative">
						<input type="checkbox" name="globalChange">
					</div>
					<div class="span8">Disable</div>
				</div>
			</td>
			<td>
				<div class="row">
					<div class="span3 relative">
						<input type="checkbox" name="enabled">
					</div>
					<div class="span9">Disable</div>
				</div>
			</td>
		</tr>
		<tr class="table-edit-actions hide">
			<td colspan="9">
				<div class="clearfix right">
					<a class="js-delete-roomTemplate mr20" href="javascript:;">Delete Room Template</a>
					<button class="btn btn-primary close-edit-panel">Cancel</button>
					<button class="btn btn-important ml10 js-update-room">Update</button>	
				</div>
			</td>
		</tr>
	</script>
	<script id="tmpl_service_item" type="text/template">
		<span class="service-selected left">
			<a class="icon icon-remove js-remove-service" data-id="{{serviceId}}" href="javascript:;">x</a>
			{{serviceName}}
		</span>
	</script>
	<script id="tmpl_activity_item" type="text/template">
		<span class="activity-selected left">
			<a class="icon icon-remove js-remove-activity" data-id="{{activityId}}" {{#service}}data-service="{{serviceId}}"{{/service}} href="javascript:;">x</a>
			{{activityName}}
		</span>
	</script>
</body>
</html>