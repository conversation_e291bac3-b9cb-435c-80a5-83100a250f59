<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix = "sec" uri = "http://www.springframework.org/security/tags"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>  
<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>Service &amp; Activity Management</title>
	<link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
	<link rel="stylesheet" href="css/gcs.min.css">
    <script data-main="js/app/scripts/service_activity_centralize.min" src="js/vendor/requirejs/require.js"></script>
</head>
<body>
	<%@include file="/WEB-INF/views/includes/header.jsp"%>
	<article>
		<nav class="top-nav">
			<div class="container">
				<ul class="top-nav-list">
					<li><a href="adminPage.htm">Studio Profile</a></li>
					<li><a href="staffPage.htm">Staff Manager</a></li><!--centralized staff page GSSP-288 -->
					<li><a href="roomTemplateManagePage.htm">Room Manager</a></li>
                    <li class="active"><a href="servicePage.htm">Services &amp; Activities Management</a></li>
							<%--GSSP-211 CHANGES--%>
					<li><a href="dualInstructorAccess.htm">Multi Location Instructor</a></li>
				</ul>
			</div>
		</nav>
		<div class="tab-title">
			<h1 class="container">Service &amp; Activity Management</h1>	
		</div>

		<div class="container">
			<div class="box">
				<div class="box-header">
					<a href="javascript:;" class="js-show-service-form">+ Add New Service</a>
				</div>
				<div class="box-body row hide">
					<form class="form-horizontal js-service-form" action="">
						<div class="form-msg error hide"></div>
						<div class="control-group">
							<label for="serviceName">Service Name:</label>
							<div class="controls">
								<input class="input-medium" name="serviceName" placeholder="Enter Service Name">
							</div>
						</div>
						<div class="control-group">
							<label for="instructor">Instructor Default:</label>
							<div class="controls">
								<select class="select-medium" placeholder="Select Instructor Requirement" name="instructor">
									<option></option>
									<option value="R">Required</option>
									<option value="N">Not Required</option>
									<option value="O">Optional</option>
								</select>
							</div>
						</div>
						<div class="control-group">
							<label for="">&nbsp;</label>
							<div class="controls">
								<div class="span3">
									<div class="relative">
										<input type="checkbox" name="enable">
									</div>
									<div class="span11 offset1">Enable when created</div>
								</div>
							</div>
						</div>
						<div class="control-group">
							<label for="">&nbsp;</label>
							<div class="controls">
								<button type="button" class="btn btn-important btn-long mr20 js-add-service">Add Service</button>
								<button type="button" class="btn btn-primary js-cancel-service">Cancel</button>
							</div>
						</div>
					</form>
				</div>
			</div>
			<section>
				<table class="table table-fixed">
					<caption class="clearfix">
						Service List:
					</caption>
					<thead>
						<tr>
							<th class="cell-action"></th>
							<th>Service Name</th>
							<th class="cell-250">Default Instructor Needs</th>
							<th class="cell-90">Global Change</th>
							<th class="cell-90">Active</th>
						</tr>
					</thead>
					<tbody></tbody>
				</table>
				<div class="service-list"></div>
			</section>

			<script id="tmpl_service" type="text/template">
				<tr>
					<th class="cell-action">
						<a href="javascript:;" class="edit-service">
						<i class="icon-edit"></i>
						Edit
						</a>
					</th>
					<th>{{serviceName}}</th>
					<th class="cell-250">
						{{instructor}}
					</th>
					<th class="cell-90">
						Enabled
					</th>
					<th class="cell-90">
						{{#enable}}Enabled{{/enable}}
						{{^enable}}Disabled{{/enable}}
					</th>
				</tr>
				<tr class="table-edit-panel hide">
					<td></td>
					<td>
						<input type="text" class="input-medium" name="serviceName" value="{{serviceName}}">
					</td>
					<td>
						<select name="requireInstructor" class="select-medium" placeholder="Select Instructor Requirement">
							<option></option>
							<option value="R">Required</option>
							<option value="N">Not Required</option>
							<option value="O">Optional</option>
						</select>
					</td>
					<td>
						<div class="row">
							<div class="span3 relative">
								<input type="checkbox" name="globalChange">
							</div>
							<div class="span9">Disable</div>
						</div>
					</td>
					<td>
						<div class="row">
							{{^enable}}
								<div class="span3 relative">
									<input class="js-input-active" name="active" type="checkbox" checked>
								</div>
								<div class="span9">
									Disable
								</div>
							{{/enable}}
							{{#enable}}
								<div class="span3 relative">
									<input class="js-input-active" name="active" type="checkbox">
								</div>
								<div class="span9">
									Disable
								</div>
							{{/enable}}	
						</div>
					</td>
				</tr>
				<tr class="table-edit-actions hide">
					<td colspan="5">
						<div class="clearfix right">
							<a class="js-delete-service mr20" href="javascript:;">Delete Service</a>
							<button class="btn btn-primary close-edit-panel">Cancel</button>
							<button class="btn btn-important js-update-service ml10">Update</button>	
						</div>
					</td>
				</tr>
			</script>

			<div class="box">
			<sec:authorize ifAnyGranted="ROLE_Site Admin,ROLE_Studio Manager,ROLE_Studio Lead">
				<div class="box-header">				
					<a class="js-show-activity-form" href="javascript:;">+ Add New Activity</a>
				</div>
				</sec:authorize>
				<div class="box-body row hide">
					<form action="" class="form-horizontal js-activity-form">
						<div class="form-msg error hide"></div>
						<div class="control-group">
							<label for="activityName">Activity Name:</label>
							<div class="controls">
								<input class="input-medium" name="activityName" type="text" placeholder="Enter Activity Name">
							</div>
						</div>
						<div class="control-group">
							<label for="serviceId">Service Type:</label>
							<div class="controls">
								<input class="input-medium" name="serviceId" type="hidden" placeholder="Select Service Type">
							</div>
						</div>
						<div class="control-group">
							<label for="minAttenders">Attendees:</label>
							<div class="controls">
								<div class="row">
									<input class="span1" name="minAttenders" type="text">
									<p class="span1 text-center">to</p>
									<input class="span1" name="maxAttenders" type="text" value="1" disabled>
								</div>
							</div>
						</div>
						<div class="control-group">
							<label for="minimumDuration">Minimum Duration:</label>
							<div class="controls">
								<select name="minimumDuration" class="select-medium" placeholder="Select Minimum Duration">
									<option></option>
									<c:forEach var="durationDTO" items="${durationDTOs }">
										<option value="${durationDTO.id }">${durationDTO.text }</option>
									</c:forEach>
								</select>	
							</div>
						</div>
						<div class="control-group">
							<label for="maxmumDuration">Maximum Duration:</label>
							<div class="controls">
								<select name="maxmumDuration" class="select-medium" placeholder="Select Maximum Duration">
									<option></option>
										<c:forEach var="durationDTO" items="${durationDTOs }">
										<option value="${durationDTO.id }">${durationDTO.text }</option>
									</c:forEach>
								</select>	
							</div>
						</div>
						<div class="control-group">
							<label for="instructor">Instructor Default:</label>
							<div class="controls">
								<select class="select-medium" placeholder="Select Instructor Requirement" name="instructor">
									<option></option>
									<option value="R">Required</option>
									<option value="N">Not Required</option>
									<option value="O">Optional</option>
								</select>
							</div>
						</div>
						<div class="control-group">
							<label for="enable">&nbsp;</label>
							<div class="controls">
								<div class="span3">
									<div class="relative">
										<input type="checkbox" name="enable">
									</div>
									<div class="span11 offset1">Enable when created</div>
								</div>
							</div>
						</div>
						<div class="control-group">
							<label for="">&nbsp;</label>
							<div class="controls">
								<button type="button" class="btn btn-important btn-long mr20 js-add-activity">Add Activity</button>
								<button type="button" class="btn btn-primary js-cancel-activity">Cancel</button>
							</div>
						</div>
					</form>
				</div>
			</div>

			<section>
				<table class="table table-fixed">
					<caption class="clearfix">
						Activities List:
					</caption>
					<thead>
						<tr>
							<th class="cell-action"></th>
							<th>Activity Name</th>
							<th class="cell-150">Service Type</th>
							<th>Attendees</th>
							<th class="cell-100">Min/Hours</th>
							<th class="cell-100">Max/Hours</th>
							<th class="cell-150">Default Instructor Needs</th>
							<th class="cell-90">Global Change</th>
							<th class="cell-90">Active</th>
						</tr>
					</thead>
					<tbody></tbody>
				</table>
				<div class="activity-list"></div>
			</section>

			<script id="tmpl_activity_list" type="text/template">
				<tr>
					<th class="cell-action">
						<a href="javascript:;" class="edit-activity">
						<i class="icon-edit"></i>
						Edit
						</a>
					</th>
					<th>{{&activityName}}</th>
					<th class="cell-150">{{serviceName}}</th>
					<th>{{&attenders}}</th>
					<th class="cell-100">{{minimumDuration}}</th>
					<th class="cell-100">{{maxmumDuration}}</th>
					<th class="cell-150">
						{{requiresInstructor}}
					</th>
					<th class="cell-90">
						Enabled
					</th>
					<th class="cell-90">
						{{#enable}}Enabled{{/enable}}
						{{^enable}}Disabled{{/enable}}
					</th>
				</tr>
				<tr class="table-edit-panel hide">
					<td></td>
					<td><input type="text" name="name" class="row"></td>
					<td>
						<input name="serviceType" type="hidden" class="row">
					</td>
					<td>
						<div class="row">
							<input type="text" class="span4" name="minimunAttendees">
							<div class="span4 text-center">
								to
							</div>
							<input type="text" class="span4" name="maximunAttendees">
						</div>
					</td>
					<td>
						<select name="minDuration" placeholder="Select Duration" class="row">
							<option></option>
							<c:forEach var="durationDTO" items="${durationDTOs }">
								<option value="${durationDTO.id }">${durationDTO.text }</option>
							</c:forEach>
						</select>
					</td>
					<td>
						<select name="maxDuration" placeholder="Select Duration" class="row">
							<option></option>
							<c:forEach var="durationDTO" items="${durationDTOs }">
								<option value="${durationDTO.id }">${durationDTO.text }</option>
							</c:forEach>
						</select>
					</td>
					<td>
						<select name="requireInstructor" class="row">
							<option value="R">Required</option>
							<option value="N">Not Required</option>
							<option value="O">Optional</option>
						</select>
					</td>
					<td>
						<div class="row">
							<div class="span3 relative">
								<input type="checkbox" name="globalChange">
							</div>
							<div class="span9">Disable</div>
						</div>
					</td>
					<td>
						<div class="row">
						{{^enable}}
							<div class="span3 relative">
								<input class="js-input-active" name="active" type="checkbox" checked>
							</div>
							<div class="span9">
								Disable
							</div>
						{{/enable}}
						
						{{#enable}}
							<div class="span3 relative">
								<input class="js-input-active" name="active" type="checkbox">
							</div>
							<div class="span9">
								Disable
							</div>
						{{/enable}}
					</div>
				</td>
			</tr>
			<tr class="table-edit-actions hide">
				<td colspan="9">
					<div class="clearfix right">
						<a class="js-delete-activity mr20" href="javascript:;">Delete Activity</a>
						<button class="btn btn-primary close-edit-panel">Cancel</button>
						<button class="btn btn-important ml10 js-update-activity">Update</button>	
					</div>
				</td>
			</tr>
			</script>

		</div>
	</article>
</body>
</html>