<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%> 
<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>Index - GCS</title>
	<link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
	<link rel="stylesheet" href="css/gcs.min.css">
</head>
<body>
	<!-- when second login if the latest wrong  only store wrong there will be two error message -->
	<c:if test="${empty message_error  }">
		${ sessionScope.SPRING_SECURITY_LAST_EXCEPTION.message}
	</c:if>
	
	<header class="header">
		<div class="container">
			<span class="logo"></span>	
			<div class="header-right">
				<p>For password support, please call the <strong>Support Center Help Desk at 818-735-8800 x2660</strong></p>
			</div>
		</div>
	</header> 
	<article>
		<div class="container login-wrapper">
			<div class="login-panel">
				<c:if test="${!empty message_error  }">
					<div class="modal-msg error ">${ message_error }</div>
				</c:if>
				<form class="form-inline" name="loginForm" action="j_spring_security_check" method="post" >
		            <fieldset>
		            	<legend>Employee Login</legend>
		                <div class="control-group">
		                    <label for="employeeID">Employee ID:</label>
		                    <div class="controls">
		                    	<input class="input-large" type="text" name="j_username" id="employeeID" value="${j_username }">	
		                    </div>
		                </div>
		                <div class="control-group">
		                    <label for="password">Password:</label>
		                    <div class="controls">
			                    <input class="input-large" type="password" name="j_password" id="password" autocomplete = "off" value="${j_password}">
		                    </div>
		                </div>
		                <div class="control-group">
		                    <label for="store">Store #:</label>
		                    <div class="controls">
		                    	<input class="input-small" type="text" name="store" id="store" value="${not empty param.storeId ? param.storeId : store }">	
		                    </div>
		                </div>
		                <div class="control-group">
		                    <label for="domain">Domain</label>
		                    <div class="controls">
		                        <select name="domain" id="domain">
                                    <option value="RETAIL">Retail</option>
                                    <option value="DOMESTIC">Domestic</option>
                                     <option value="MF">MF</option>
		                        </select>
		                    </div>
		                </div>
		                
		                <div class="form-actions">
	                        <button class="btn btn-important btn-large" type="submit">Log In</button>
		                </div>
		            </fieldset>
		    	</form>		
			</div>
		</div>
	</article>
</body>
</html>
