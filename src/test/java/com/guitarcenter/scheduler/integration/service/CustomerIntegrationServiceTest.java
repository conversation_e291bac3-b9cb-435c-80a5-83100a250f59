package com.guitarcenter.scheduler.integration.service;

import static org.junit.Assert.*;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

import javax.xml.transform.stream.StreamSource;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.Resource;
import org.springframework.oxm.Unmarshaller;
import org.springframework.oxm.XmlMappingException;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.guitarcenter.scheduler.dao.ActivityDAO;
import com.guitarcenter.scheduler.dao.AppointmentDAO;
import com.guitarcenter.scheduler.dao.AppointmentSeriesDAO;
import com.guitarcenter.scheduler.dao.AvailabilityDAO;
import com.guitarcenter.scheduler.dao.CustomerDAO;
import com.guitarcenter.scheduler.dao.CustomerStatusDAO;
import com.guitarcenter.scheduler.dao.InstrumentDAO;
import com.guitarcenter.scheduler.dao.LocationDAO;
import com.guitarcenter.scheduler.dao.LocationProfileDAO;
import com.guitarcenter.scheduler.dao.PersonDAO;
import com.guitarcenter.scheduler.dao.ProfileActivityDAO;
import com.guitarcenter.scheduler.dao.ProfileServiceDAO;
import com.guitarcenter.scheduler.dao.RoomDAO;
import com.guitarcenter.scheduler.dao.RoomNumberDAO;
import com.guitarcenter.scheduler.dao.RoomTypeDAO;
import com.guitarcenter.scheduler.dao.ServiceDAO;
import com.guitarcenter.scheduler.dao.SiteDAO;
import com.guitarcenter.scheduler.dao.util.DAOHelper;
import com.guitarcenter.scheduler.dto.CreateAppointmentDTO;
import com.guitarcenter.scheduler.dto.CustomerDTO;
//import com.guitarcenter.scheduler.integration.generated.SyncCustomer;
//import com.guitarcenter.scheduler.integration.generated.SyncCustomer.PurchaseDetails.GCStudio.LessonInstrument;
import com.guitarcenter.scheduler.model.Activity;
import com.guitarcenter.scheduler.model.Appointment;
import com.guitarcenter.scheduler.model.AppointmentSeries;
import com.guitarcenter.scheduler.model.Availability;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.CustomerStatus;
import com.guitarcenter.scheduler.model.Instrument;
import com.guitarcenter.scheduler.model.Location;
import com.guitarcenter.scheduler.model.LocationProfile;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.ProfileActivity;
import com.guitarcenter.scheduler.model.ProfileService;
import com.guitarcenter.scheduler.model.Room;
import com.guitarcenter.scheduler.model.RoomNumber;
import com.guitarcenter.scheduler.model.RoomType;
import com.guitarcenter.scheduler.model.Service;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.model.enums.Enabled;
import com.guitarcenter.scheduler.model.enums.RequiresInstructor;
import com.guitarcenter.scheduler.model.enums.SplitRoom;
import com.guitarcenter.scheduler.service.AppointmentSeriesService;
import com.guitarcenter.scheduler.service.AppointmentService;
import com.guitarcenter.scheduler.service.CustomerService;
import com.guitarcenter.scheduler.service.PersonManagerService;

/**
 * Perform unit testing of CustomerIntegrationService, without using JMS.
 *
 * <AUTHOR> Emes <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class CustomerIntegrationServiceTest {
    
    @Autowired
    @Qualifier("posCustomerMarshaller")
    private Unmarshaller unmarshaller;
    
    /* POS Customer XML records from DEV, sanitised to remove personal
     * information
     */
    @Autowired
    private Resource resPOSCustomerJohnSmith;
    @Autowired
    private Resource resPOSCustomerJaneSmith;
    @Autowired
    private Resource resPOSCustomerJohnJones;
    
    @Autowired
    private CustomerIntegrationService service;
    
    @Autowired
    private CustomerService customerService;
    
    @Autowired
    private PersonManagerService personManagerService;
    
    @Autowired
    private PersonDAO personDAO;
    
    @Autowired
    private SiteDAO siteDAO;
    
    @Autowired
    private CustomerStatusDAO customerStatusDAO;
    
    @Autowired
    private CustomerDAO customerDAO;
    
    @Autowired
    private InstrumentDAO instrumentDAO;
    
    @Autowired
    private AppointmentSeriesService appointmentSeriesService;
    
    @Autowired
    private AppointmentService appointmentService;
    
    @Autowired
    private AvailabilityDAO availabilityDAO;
    
    @Autowired
    private LocationDAO locationDAO;
    
    @Autowired
    private LocationProfileDAO profileDAO;
    
    @Autowired
    private ServiceDAO serviceDAO;
    
    @Autowired
    private ActivityDAO activityDAO;
    
    @Autowired
    private AppointmentDAO appointmentDAO;
    
    @Autowired
    private AppointmentSeriesDAO appointmentSeriesDAO;
    
    @Autowired
    private RoomDAO roomDAO;
    
    @Autowired
    private RoomTypeDAO roomTypeDAO;
    
    @Autowired
    private RoomNumberDAO roomNumberDAO;
    
    @Autowired
    private ProfileActivityDAO profileActivityDAO;
    
    @Autowired
    private ProfileServiceDAO profileServiceDAO;
    
    private Person updater = null;
    private Site gcs = null;
    private CustomerStatus active = null;
    private CustomerStatus prospect = null;
    private CustomerStatus cancelled = null;
    private Instrument testInstrument = null;
    private Instrument testInstrument2 = null;
    private Availability availability = null;
    private LocationProfile profile = null;
    private Location location = null;
    private Service apptService = null;
    private Activity activity = null;
    private Room room = null;
    private RoomType roomType = null;
    private RoomNumber roomNumber = null;
    private ProfileActivity profileActivity = null;
    private ProfileService profileService = null;
    
    @Before
    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = Throwable.class)
    public void setup() {
        assertNotNull("unmarshaller is null", unmarshaller);
        assertNotNull("service is null", service);
        assertNotNull("customerService is null", customerService);
        updater =  personManagerService.getSystemUpdatePerson();
        gcs = new Site();
        gcs.setExternalId("GCS");
        siteDAO.save(gcs, updater);
        active = new CustomerStatus();
        active.setExternalId("A");
        active.setStatusName("Test Active");
        active.setSite(gcs);
        customerStatusDAO.save(active, updater);
        prospect = new CustomerStatus();
        prospect.setExternalId(CustomerStatus.CUSTOMER_STATUS_PROSPECT);
        prospect.setStatusName("Test Prospective");
        prospect.setSite(gcs);
        customerStatusDAO.save(prospect, updater);
        cancelled = new CustomerStatus();
        cancelled.setExternalId(CustomerStatus.CUSTOMER_STATUS_CANCELLED);
        cancelled.setStatusName("Test Cancelled");
        cancelled.setSite(gcs);
        customerStatusDAO.save(cancelled, updater);
        testInstrument = new Instrument();
        testInstrument.setExternalId("09");
        testInstrument.setInstrumentName("Test Instrument");
        testInstrument.setSite(gcs);
        instrumentDAO.save(testInstrument, updater);
        testInstrument2 = new Instrument();
        testInstrument2.setExternalId("foo");
        testInstrument2.setInstrumentName("Test Instrument2");
        testInstrument2.setSite(gcs);
        instrumentDAO.save(testInstrument2, updater);
        availability = new Availability();
        availability.setSite(gcs);
        availabilityDAO.save(availability, updater);
        profile = new LocationProfile();
        profile.setSite(gcs);
        profile.setAvailability(availability);
        profile.setTimeZone(TimeZone.getDefault().getDisplayName());
        profileDAO.save(profile, updater);
        location = new Location();
        location.setSite(gcs);
        location.setLocationProfile(profile);
        location.setLocationName("test");
        location.setAddress1("test");
        location.setCity("test");
        location.setState("test");
        location.setZip("test");
        location.setCountry("test");
        locationDAO.save(location, updater);
        apptService = new Service();
        apptService.setEnabled(Enabled.Y);
        apptService.setRequiresInstructor(RequiresInstructor.N);
        apptService.setServiceName("test");
        apptService.setSite(gcs);
        serviceDAO.save(apptService, updater);
        profileService = new ProfileService();
        profileService.setEnabled(Enabled.Y);
        profileService.setLocationProfile(profile);
        profileService.setService(apptService);
        profileService.setSite(gcs);
        profileServiceDAO.save(profileService, updater);
        activity = new Activity();
        activity.setService(apptService);
        activity.setActivityName("test");
        activity.setEnabled(Enabled.Y);
        activity.setMinimumAttendees(2L);
        activity.setMinimumDuration(30L);
        activity.setMaximumDuration(120L);
        activity.setRequiresInstructor(RequiresInstructor.N);
        activity.setSite(gcs);
        activityDAO.save(activity, updater);
        profileActivity = new ProfileActivity();
        profileActivity.setEnabled(Enabled.Y);
        profileActivity.setLocationProfile(profile);
        profileActivity.setActivity(activity);
        profileActivity.setSite(gcs);
        profileActivityDAO.save(profileActivity, updater);
        roomType = new RoomType();
        roomType.setSite(gcs);
        roomType.setRoomType("test");
        roomType.setCanSplitRoom(SplitRoom.N);
        roomTypeDAO.save(roomType, updater);
        roomNumber = new RoomNumber();
        roomNumber.setSite(gcs);
        roomNumber.setRoomNumber("test");
        roomNumberDAO.save(roomNumber, updater);
        room = new Room();
        room.setSite(gcs);
        room.setRoomType(roomType);
        room.setRoomNumber(roomNumber);
        room.setLocationProfile(profile);
        room.getServices().add(apptService);
        room.getActivities().add(activity);
        room.setEnabled(Enabled.Y);
        roomDAO.save(room, updater);
    }
    
    @After
    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = Throwable.class)
    public void tearDown() {
        /* Clean up database
         */
        List<Appointment> appts = appointmentDAO.searchAll();
        for (Appointment appt: appts) {
            if (gcs.getSiteId().equals(appt.getSite().getSiteId())) {
                appointmentDAO.delete(appt);
            }
        }
        List<AppointmentSeries> apptSeries = appointmentSeriesDAO.searchAll();
        for (AppointmentSeries series: apptSeries) {
            if (gcs.getSiteId().equals(series.getSite().getSiteId())) {
                appointmentSeriesDAO.delete(series);
            }
        }
        List<CustomerDTO> customerDTOs =
            customerService.loadCustomerListBySite(gcs.getSiteId());
        for (CustomerDTO customerDTO: customerDTOs) {
            Customer customer = customerDAO.get(customerDTO.getRecordId(),
                                                DAOHelper.FETCH_PERSON);
            customerDAO.delete(customer);
            personDAO.delete(customer.getPerson());
        }
        /* Clean up added reference values
         */
        roomDAO.delete(room);
        roomNumberDAO.delete(roomNumber);
        roomTypeDAO.delete(roomType);
        profileActivityDAO.delete(profileActivity);
        activityDAO.delete(activity);
        profileServiceDAO.delete(profileService);
        serviceDAO.delete(apptService);
        locationDAO.delete(location);
        profileDAO.delete(profile);
        availabilityDAO.delete(availability);
        instrumentDAO.delete(testInstrument2);
        instrumentDAO.delete(testInstrument);
        testInstrument2 = null;
        testInstrument = null;
        customerStatusDAO.delete(active);
        active = null;
        customerStatusDAO.delete(cancelled);
        cancelled = null;
        siteDAO.delete(gcs);
        gcs = null;
        updater = null;
    }
    
    /**
     * Private helper to produce a SyncCustomer from the contents of the
     * supplied spring resource.
     * 
     * @param resource Spring resource to extract as UTF-8 chars
     * @return SyncCustomer representation of resource contents
     * @throws XmlMappingException 
     * @throws IOException
     */
    /*
    private SyncCustomer getResourceContentAsSyncCustomer(Resource resource)
        throws XmlMappingException, IOException
    {
        assertNotNull("resource is null", resource);
        assertTrue("resource.exists() != true", resource.exists());
        return (SyncCustomer) unmarshaller.unmarshal(new StreamSource(resource.getInputStream()));
    }
    
    *//**
     * Update a Customer from POS and verify the results
     *//*
    @Test
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    public void testJohnJones()
        throws IntegrationServiceException, XmlMappingException, IOException
    {
        SyncCustomer posJohnJones =
            getResourceContentAsSyncCustomer(resPOSCustomerJohnJones);
        String externalId =
            posJohnJones.getEnterpriseCustomerID().getEnterpriseCustomerID();
        
         There should not be any customers matching the external id at
         * this point.
         
        Customer johnJones =
            customerService.getCustomerByExternalId(gcs.getSiteId(), externalId);
        assertNull("johnJones is not null", johnJones);

         Process the SyncCustomer record and check again
         
        service.process(posJohnJones);
        johnJones = customerService.getCustomerByExternalId(gcs.getSiteId(), externalId);
        assertNotNull("johnJones is null", johnJones);
         Site should match the gcs site created for these tests
         
        assertNotNull("johnJones.site is null", johnJones.getSite());
        assertEquals("johnJones.site != gcs", gcs, johnJones.getSite());
        String expectedResult = "2223488278";
        assertEquals("johnJones.externalId != " + expectedResult,
                     expectedResult, johnJones.getExternalId());
        expectedResult = "POS";
        assertEquals("johnJones.externalSource != " + expectedResult,
                     expectedResult, johnJones.getExternalSource());
         Test person entity is fully fleshed out
         
        assertNotNull("johnJones.person is null", johnJones.getPerson());
        expectedResult = "John";
        assertEquals("johnJones.firstName != " + expectedResult,
                     expectedResult, johnJones.getPerson().getFirstName());
        expectedResult = "Jones";
        assertEquals("johnJones.lastName != " + expectedResult,
                     expectedResult, johnJones.getPerson().getLastName());
        expectedResult = "5555555555";
        assertEquals("johnJones.phone != " + expectedResult,
                     expectedResult, johnJones.getPerson().getPhone());
        expectedResult = "<EMAIL>";
        assertEquals("johnJones.email != " + expectedResult,
                     expectedResult, johnJones.getPerson().getEmail());
         A status is required; make sure it matches Active
         
        assertNotNull("johnJones.customerStatus is null",
                      johnJones.getCustomerStatus());
        assertEquals("johnJones.customerStatus != active", active,
                     johnJones.getCustomerStatus());
         MISC. (id 9) should map to testInstrument defined above
         
        assertNotNull("johnJones.instruments is null",
                      johnJones.getInstruments());
        assertFalse("johnJones.instruments is empty",
                    johnJones.getInstruments().isEmpty());
        assertTrue("johnJones.instruments.size(" +
                   johnJones.getInstruments().size() + " != 1",
                   1 == johnJones.getInstruments().size());
        assertEquals("johnJones.instruments[0] != testInstrument",
                     testInstrument, johnJones.getInstruments().toArray()[0]);
    }
    
    *//**
     * Update another Customer from POS and verify the results
     *//*
    @Test
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    public void testJaneSmith()
        throws IntegrationServiceException, XmlMappingException, IOException
    {
        SyncCustomer posJaneSmith =
            getResourceContentAsSyncCustomer(resPOSCustomerJaneSmith);
        String externalId =
            posJaneSmith.getEnterpriseCustomerID().getEnterpriseCustomerID();
        
         There should not be any customers matching the external id at
         * this point.
         
        Customer janeSmith =
            customerService.getCustomerByExternalId(gcs.getSiteId(), externalId);
        assertNull("janeSmith is null", janeSmith);

         Process the SyncCustomer record and check again
         
        service.process(posJaneSmith);
        janeSmith = customerService.getCustomerByExternalId(gcs.getSiteId(), externalId);
        assertNotNull("janeSmith is null", janeSmith);
         Site should match the gcs site created for these tests
         
        assertNotNull("janeSmith.site is null", janeSmith.getSite());
        assertEquals("janeSmith.site != gcs", gcs, janeSmith.getSite());
        String expectedResult = "2203471209";
        assertEquals("janeSmith.externalId != " + expectedResult,
                     expectedResult, janeSmith.getExternalId());
        expectedResult = "POS";
        assertEquals("janeSmith.externalSource != " + expectedResult,
                     expectedResult, janeSmith.getExternalSource());
         Test person entity is fully fleshed out
         
        assertNotNull("janeSmith.person is null", janeSmith.getPerson());
        expectedResult = "Jane";
        assertEquals("janeSmith.firstName != " + expectedResult,
                     expectedResult, janeSmith.getPerson().getFirstName());
        expectedResult = "Smith";
        assertEquals("janeSmith.lastName != " + expectedResult,
                     expectedResult, janeSmith.getPerson().getLastName());
        expectedResult = "************";
        assertEquals("janeSmith.phone != " + expectedResult,
                     expectedResult, janeSmith.getPerson().getPhone());
        expectedResult = "<EMAIL>";
        assertEquals("janeSmith.email != " + expectedResult,
                     expectedResult, janeSmith.getPerson().getEmail());
         A status is required; make sure it matches prospect
         
        assertNotNull("janeSmith.customerStatus is null",
                      janeSmith.getCustomerStatus());
        assertEquals("janeSmith.customerStatus != prospect", prospect,
                     janeSmith.getCustomerStatus());
         MISC. (id 9) should map to testInstrument defined above
         
        assertNotNull("janeSmith.instruments is null",
                      janeSmith.getInstruments());
        assertFalse("janeSmith.instruments is empty",
                    janeSmith.getInstruments().isEmpty());
        assertTrue("janeSmith.instruments.size(" +
                   janeSmith.getInstruments().size() + " != 1",
                   1 == janeSmith.getInstruments().size());
        assertEquals("janeSmith.instruments[0] != testInstrument",
                     testInstrument, janeSmith.getInstruments().toArray()[0]);
    }
    
    *//**
     * Update final Customer from POS and verify the results
     *//*
    @Test
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    public void testJohnSmith()
        throws IntegrationServiceException, XmlMappingException, IOException
    {
        SyncCustomer posJohnSmith =
            getResourceContentAsSyncCustomer(resPOSCustomerJohnSmith);
        String externalId =
            posJohnSmith.getEnterpriseCustomerID().getEnterpriseCustomerID();
        
         There should not be any customers matching the external id at
         * this point.
         
        Customer johnSmith =
            customerService.getCustomerByExternalId(gcs.getSiteId(), externalId);
        assertNull("johnSmith is not null", johnSmith);

         Process the SyncCustomer record and check again
         
        service.process(posJohnSmith);
        johnSmith = customerService.getCustomerByExternalId(gcs.getSiteId(), externalId);
        assertNotNull("johnSmith is null", johnSmith);
         Site should match the gcs site created for these tests
         
        assertNotNull("johnSmith.site is null", johnSmith.getSite());
        assertEquals("johnSmith.site != gcs", gcs, johnSmith.getSite());
        String expectedResult = "2223488270";
        assertEquals("johnSmith.externalId != " + expectedResult,
                     expectedResult, johnSmith.getExternalId());
        expectedResult = "POS";
        assertEquals("johnSmith.externalSource != " + expectedResult,
                     expectedResult, johnSmith.getExternalSource());
         Test person entity is fully fleshed out
         
        assertNotNull("johnSmith.person is null", johnSmith.getPerson());
        expectedResult = "John";
        assertEquals("johnSmith.firstName != " + expectedResult,
                     expectedResult, johnSmith.getPerson().getFirstName());
        expectedResult = "Smith";
        assertEquals("johnSmith.lastName != " + expectedResult,
                     expectedResult, johnSmith.getPerson().getLastName());
        expectedResult = "(*************";
        assertEquals("johnSmith.phone != " + expectedResult,
                     expectedResult, johnSmith.getPerson().getPhone());
        expectedResult = "<EMAIL>";
        assertEquals("johnSmith.email != " + expectedResult,
                     expectedResult, johnSmith.getPerson().getEmail());
         A status is required; make sure it matches cancelled
         
        assertNotNull("johnSmith.customerStatus is null",
                      johnSmith.getCustomerStatus());
        assertEquals("johnSmith.customerStatus != cancelled", cancelled,
                     johnSmith.getCustomerStatus());
         John Smith does not have any instruments.
         
        assertNotNull("johnSmith.instruments is null",
                      johnSmith.getInstruments());
        assertTrue("johnSmith.instruments is not empty",
                    johnSmith.getInstruments().isEmpty());
    }
    
    *//**
     * Add an instrument to John Smith
     *//*
    @Test
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    public void testJohnSmithWithInstrument()
        throws IntegrationServiceException, XmlMappingException, IOException
    {
        SyncCustomer posJohnSmith =
            getResourceContentAsSyncCustomer(resPOSCustomerJohnSmith);
        LessonInstrument lessonInstrument = new LessonInstrument();
        lessonInstrument.setDescription("An added instrument");
        lessonInstrument.setID(testInstrument2.getExternalId());
        lessonInstrument.setName("foo bar baz");
        posJohnSmith.getPurchaseDetails().getGCStudio1().getLessonInstrumentList()
            .add(lessonInstrument);
        String externalId =
            posJohnSmith.getEnterpriseCustomerID().getEnterpriseCustomerID();
        
         There should not be any customers matching the external id at
         * this point.
         
        Customer johnSmith =
            customerService.getCustomerByExternalId(gcs.getSiteId(), externalId);
        assertNull("johnSmith is not null", johnSmith);

         Process the SyncCustomer record and check again
         
        service.process(posJohnSmith);
        johnSmith = customerService.getCustomerByExternalId(gcs.getSiteId(), externalId);
        assertNotNull("johnSmith is null", johnSmith);
         Site should match the gcs site created for these tests
         
        assertNotNull("johnSmith.site is null", johnSmith.getSite());
        assertEquals("johnSmith.site != gcs", gcs, johnSmith.getSite());
        String expectedResult = "2223488270";
        assertEquals("johnSmith.externalId != " + expectedResult,
                     expectedResult, johnSmith.getExternalId());
        expectedResult = "POS";
        assertEquals("johnSmith.externalSource != " + expectedResult,
                     expectedResult, johnSmith.getExternalSource());
         Test person entity is fully fleshed out
         
        assertNotNull("johnSmith.person is null", johnSmith.getPerson());
        expectedResult = "John";
        assertEquals("johnSmith.firstName != " + expectedResult,
                     expectedResult, johnSmith.getPerson().getFirstName());
        expectedResult = "Smith";
        assertEquals("johnSmith.lastName != " + expectedResult,
                     expectedResult, johnSmith.getPerson().getLastName());
        expectedResult = "(*************";
        assertEquals("johnSmith.phone != " + expectedResult,
                     expectedResult, johnSmith.getPerson().getPhone());
        expectedResult = "<EMAIL>";
        assertEquals("johnSmith.email != " + expectedResult,
                     expectedResult, johnSmith.getPerson().getEmail());
         A status is required; make sure it matches cancelled
         
        assertNotNull("johnSmith.customerStatus is null",
                      johnSmith.getCustomerStatus());
        assertEquals("johnSmith.customerStatus != cancelled", cancelled,
                     johnSmith.getCustomerStatus());
         John Smith does not have any instruments.
         
        assertNotNull("johnSmith.instruments is null",
                      johnSmith.getInstruments());
        assertFalse("johnSmith.instruments is empty",
                    johnSmith.getInstruments().isEmpty());
        assertEquals("johnSmith.instruments[0] != testInstrument2",
                     testInstrument2, johnSmith.getInstruments().toArray()[0]);
    }
    
    *//**
     * Update Jane Smith's record with John Smith's XML
     *//*
    @Test
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    public void testJaneSmithMergeJohnSmith()
        throws IntegrationServiceException, XmlMappingException, IOException
    {
        SyncCustomer posJaneSmith =
            getResourceContentAsSyncCustomer(resPOSCustomerJaneSmith);
        SyncCustomer posJohnSmith =
            getResourceContentAsSyncCustomer(resPOSCustomerJohnSmith);
        String externalId =
            posJaneSmith.getEnterpriseCustomerID().getEnterpriseCustomerID();
        
         There should not be any customers matching the external id at
         * this point.
         
        Customer janeSmith =
            customerService.getCustomerByExternalId(gcs.getSiteId(), externalId);
        assertNull("janeSmith is not null", janeSmith);
        
         Merge some details from John Smith to Jane Smith
         
        posJaneSmith.setContactDetails(posJohnSmith.getContactDetails());

         Process the SyncCustomer record and check again
         
        service.process(posJaneSmith);
        janeSmith = customerService.getCustomerByExternalId(gcs.getSiteId(), externalId);
        assertNotNull("janeSmith is null", janeSmith);
         Site should match the gcs site created for these tests
         
        assertNotNull("janeSmith.site is null", janeSmith.getSite());
        assertEquals("janeSmith.site != gcs", gcs, janeSmith.getSite());
        String expectedResult = "2203471209";
        assertEquals("janeSmith.externalId != " + expectedResult,
                     expectedResult, janeSmith.getExternalId());
        expectedResult = "POS";
        assertEquals("janeSmith.externalSource != " + expectedResult,
                     expectedResult, janeSmith.getExternalSource());
         Test person entity is fully fleshed out
         
        assertNotNull("janeSmith.person is null", janeSmith.getPerson());
        expectedResult = "Jane";
        assertEquals("janeSmith.firstName != " + expectedResult,
                     expectedResult, janeSmith.getPerson().getFirstName());
        expectedResult = "Smith";
        assertEquals("janeSmith.lastName != " + expectedResult,
                     expectedResult, janeSmith.getPerson().getLastName());
        expectedResult = "(*************";
        assertEquals("janeSmith.phone != " + expectedResult,
                     expectedResult, janeSmith.getPerson().getPhone());
        expectedResult = "<EMAIL>";
        assertEquals("janeSmith.email != " + expectedResult,
                     expectedResult, janeSmith.getPerson().getEmail());
         A status is required; make sure it matches prospect
         
        assertNotNull("janeSmith.customerStatus is null",
                      janeSmith.getCustomerStatus());
        assertEquals("janeSmith.customerStatus != prospect", prospect,
                     janeSmith.getCustomerStatus());
         MISC. (id 9) should map to testInstrument defined above
         
        assertNotNull("janeSmith.instruments is null",
                      janeSmith.getInstruments());
        assertFalse("janeSmith.instruments is empty",
                    janeSmith.getInstruments().isEmpty());
        assertTrue("janeSmith.instruments.size(" +
                   janeSmith.getInstruments().size() + " != 1",
                   1 == janeSmith.getInstruments().size());
        assertEquals("janeSmith.instruments[0] != testInstrument",
                     testInstrument, janeSmith.getInstruments().toArray()[0]);
    }
    
    *//**
     * Override John Jones' email address.
     *//*
    @Test
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    public void testOverideEmailJohnJones()
        throws IntegrationServiceException, XmlMappingException, IOException
    {
        try {
            String testEmail = "<EMAIL>";
            System.getProperties().setProperty("gcss.emailOverride", testEmail);
            SyncCustomer posJohnJones =
                getResourceContentAsSyncCustomer(resPOSCustomerJohnJones);
            String externalId =
                posJohnJones.getEnterpriseCustomerID().getEnterpriseCustomerID();
            
             There should not be any customers matching the external id at
             * this point.
             
            Customer johnJones =
                customerService.getCustomerByExternalId(gcs.getSiteId(), externalId);
            assertNull("johnJones is not null", johnJones);
            
             Process the SyncCustomer record and check again
             
            service.process(posJohnJones);
            johnJones = customerService.getCustomerByExternalId(gcs.getSiteId(), externalId);
            assertNotNull("johnJones is null", johnJones);
             Site should match the gcs site created for these tests
             
            assertNotNull("johnJones.site is null", johnJones.getSite());
            assertEquals("johnJones.site != gcs", gcs, johnJones.getSite());
            String expectedResult = "2223488278";
            assertEquals("johnJones.externalId != " + expectedResult,
                         expectedResult, johnJones.getExternalId());
            expectedResult = "POS";
            assertEquals("johnJones.externalSource != " + expectedResult,
                         expectedResult, johnJones.getExternalSource());
             Test person entity is fully fleshed out
             
            assertNotNull("johnJones.person is null", johnJones.getPerson());
            expectedResult = "John";
            assertEquals("johnJones.firstName != " + expectedResult,
                         expectedResult, johnJones.getPerson().getFirstName());
            expectedResult = "Jones";
            assertEquals("johnJones.lastName != " + expectedResult,
                         expectedResult, johnJones.getPerson().getLastName());
            expectedResult = "5555555555";
            assertEquals("johnJones.phone != " + expectedResult,
                         expectedResult, johnJones.getPerson().getPhone());
            assertEquals("johnJones.email != " + testEmail,
                         testEmail, johnJones.getPerson().getEmail());
             A status is required; make sure it matches Active
             
            assertNotNull("johnJones.customerStatus is null",
                          johnJones.getCustomerStatus());
            assertEquals("johnJones.customerStatus != active", active,
                         johnJones.getCustomerStatus());
             MISC. (id 9) should map to testInstrument defined above
             
            assertNotNull("johnJones.instruments is null",
                          johnJones.getInstruments());
            assertFalse("johnJones.instruments is empty",
                        johnJones.getInstruments().isEmpty());
            assertTrue("johnJones.instruments.size(" +
                       johnJones.getInstruments().size() + " != 1",
                       1 == johnJones.getInstruments().size());
            assertEquals("johnJones.instruments[0] != testInstrument",
                         testInstrument, johnJones.getInstruments().toArray()[0]);
        } finally {
            System.getProperties().remove("gcss.emailOverride");
        }
    }
    
    *//**
     * Test cancelled status effect on appointments. Create a customer,
     * add appointments and process a cancellation POS message.
     *//*
    @Test
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    public void testCancelledAppointments()
        throws IntegrationServiceException, XmlMappingException, IOException
    {
        SyncCustomer posJohnJones =
            getResourceContentAsSyncCustomer(resPOSCustomerJohnJones);
        String externalId =
            posJohnJones.getEnterpriseCustomerID().getEnterpriseCustomerID();
        
         There should not be any customers matching the external id at
         * this point.
         
        Customer johnJones =
            customerService.getCustomerByExternalId(gcs.getSiteId(), externalId);
        assertNull("johnJones is not null", johnJones);

         Process the SyncCustomer record and check again
         
        service.process(posJohnJones);
        johnJones = customerService.getCustomerByExternalId(gcs.getSiteId(), externalId);
        Long customerId = johnJones.getCustomerId();
        assertNotNull("customerId is null", customerId);
        
         Add a repeating appointment starting two weeks ago at 12pm for 1
         * hour, ending two weeks in future for a total of 5 appointments.
         * 
         * Note: this appointment is not valid, but the test case is bypassing
         * validation.
         
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM/dd/yyyy");
        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -15);
        cal.set(Calendar.HOUR_OF_DAY, 12);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date searchRangeStart = cal.getTime();
        cal.add(Calendar.DAY_OF_MONTH, 1);
        Date start = cal.getTime();
        CreateAppointmentDTO dto = new CreateAppointmentDTO();
        dto.setStartDate(dateFormat.format(start));
        dto.setStartTime(timeFormat.format(start));
        dto.setDuration("60");
        dto.setIsRecurring(Boolean.TRUE.toString());
        cal.add(Calendar.MINUTE, 60);
        cal.add(Calendar.DAY_OF_MONTH, 28);
        dto.setEndDate(dateFormat.format(cal.getTime()));
        dto.setCustomerId(customerId.toString());
        dto.setServiceId(apptService.getServiceId());
        dto.setActivityId(activity.getActivityId().toString());
        dto.setRoomId(room.getRoomId());
        AppointmentSeries series = dto.getAppointmentSeries();
        series.setLocationProfile(profile);
        series.setSite(gcs);
        series.setUpdatedBy(updater);
        series = appointmentSeriesService.createAppointmentSeries(series, updater);
        Appointment appt = dto.getAppointment();
        appt.setLocationProfile(profile);
        appt.setSite(gcs);
        appt.setUpdatedBy(updater);
        appt.setAppointmentSeries(series);
        appointmentService.createAppointment(appt);
        
         Verify 5 appointments were created for customer
         
        cal.add(Calendar.DAY_OF_MONTH, 1);
        List<Appointment> appts =
            appointmentService.loadByCustomer(customerId.longValue(),
                                              searchRangeStart, cal.getTime(),
                                              profile.getProfileId());
        assertNotNull("appts is null", appts);
        assertTrue("appts.size() != 5: " + appts.size(), 5 == appts.size());
        
         Send a cancelled POS message and verify all appointments today and
         * after are deleted. 2 should remain.
         
        posJohnJones.getPurchaseDetails().getGCStudio1()
            .setCustomerStatus(CustomerStatus.CUSTOMER_STATUS_CANCELLED);
        service.process(posJohnJones);
        johnJones = customerService.getCustomerByExternalId(gcs.getSiteId(), externalId);
        assertNotNull("johnJones is null", johnJones);
        appts = appointmentService.loadByCustomer(customerId.longValue(),
                                                  searchRangeStart,
                                                  cal.getTime(),
                                                  profile.getProfileId());
        assertNotNull("appts is null", appts);
        assertTrue("appts.size() != 2: " + appts.size(), 2 == appts.size());
        
         Check the remaining appointments and verify they are all before start
         * of today.
         
        cal.setTime(new Date());
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        for (Appointment remaining: appts) {
            assertNotNull("remaining is null", remaining);
            assertNotNull("remaining.startTime is null", remaining.getStartTime());
            assertTrue("appointment start time is after midnight this morning: " +
                       remaining.getStartTimeStr(),
                       remaining.getStartTime().compareTo(cal.getTime()) < 0);
            assertNotNull("remaining.endTime is null", remaining.getEndTime());
            assertTrue("appointment end time is after midnight this morning: " +
                       remaining.getEndTimeStr(),
                       remaining.getEndTime().compareTo(cal.getTime()) < 0);
            assertNotNull("appt series is null", remaining.getAppointmentSeries());
            assertEquals("appt series.id(" +
                         remaining.getAppointmentSeries().getAppointmentSeriesId() +
                         ") != series.id(" + series.getAppointmentSeriesId() + ")",
                         series.getAppointmentSeriesId(),
                         remaining.getAppointmentSeries().getAppointmentSeriesId());
            assertNotNull("appt series.startTime is null",
                    remaining.getAppointmentSeries().getSeriesStartTime());
            assertTrue("appt series start time is after midnight this morning: " +
                       remaining.getAppointmentSeries().getSeriesStartTime(),
                       remaining.getAppointmentSeries().getSeriesStartTime().compareTo(cal.getTime()) < 0);
            assertNotNull("appt series.endTime is null",
                         remaining.getAppointmentSeries().getSeriesEndTime());
            assertTrue("appt series end time is after midnight this morning: " +
                 remaining.getAppointmentSeries().getSeriesEndTime(),
                 remaining.getAppointmentSeries().getSeriesEndTime().compareTo(cal.getTime()) < 0);
        }
    }

    *//**
     * Test cancelled status effect on appointments with multiple customers.
     *//*
    @Test
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Throwable.class)
    public void testCancelledMultiCustomerAppointments()
        throws IntegrationServiceException, XmlMappingException, IOException
    {
        SyncCustomer posJohnJones =
            getResourceContentAsSyncCustomer(resPOSCustomerJohnJones);
        String johnJonesExternalId =
            posJohnJones.getEnterpriseCustomerID().getEnterpriseCustomerID();
        SyncCustomer posJaneSmith =
                getResourceContentAsSyncCustomer(resPOSCustomerJaneSmith);
        String janeSmithExternalId =
                posJaneSmith.getEnterpriseCustomerID().getEnterpriseCustomerID();
            
         There should not be any customers matching either external id at
         * this point.
         
        Customer customer =
            customerService.getCustomerByExternalId(gcs.getSiteId(),
                                                    johnJonesExternalId);
        assertNull("customer is not null", customer);
        customer = customerService.getCustomerByExternalId(gcs.getSiteId(),
                                                            janeSmithExternalId);
        assertNull("customer is not null", customer);

         Process the SyncCustomer records and check again
         
        service.process(posJohnJones);
        service.process(posJaneSmith);
        customer = customerService.getCustomerByExternalId(gcs.getSiteId(),
                                                            johnJonesExternalId);
        assertNotNull("johnJones is null", customer);
        Long johnJonesId = customer.getCustomerId();
        assertNotNull("customerId is null", johnJonesId);
        customer = customerService.getCustomerByExternalId(gcs.getSiteId(),
                                                            janeSmithExternalId);
        assertNotNull("johnJones is null", customer);
        Long janeSmithId = customer.getCustomerId();
        assertNotNull("customerId is null", janeSmithId);

         Add a repeating appointment starting two weeks ago at 12pm for 1
         * hour, ending two weeks in future for a total of 5 appointments. Add
         * John and Jane to all appointments.
         * 
         * Note: this appointment is not valid, but the test case is bypassing
         * validation.
         
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM/dd/yyyy");
        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -15);
        cal.set(Calendar.HOUR_OF_DAY, 12);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date searchRangeStart = cal.getTime();
        cal.add(Calendar.DAY_OF_MONTH, 1);
        Date start = cal.getTime();
        CreateAppointmentDTO dto = new CreateAppointmentDTO();
        dto.setStartDate(dateFormat.format(start));
        dto.setStartTime(timeFormat.format(start));
        dto.setDuration("60");
        dto.setIsRecurring(Boolean.TRUE.toString());
        cal.add(Calendar.MINUTE, 60);
        cal.add(Calendar.DAY_OF_MONTH, 28);
        dto.setEndDate(dateFormat.format(cal.getTime()));
        dto.setCustomerId(johnJonesId.toString() + "," + janeSmithId.toString());
        dto.setServiceId(apptService.getServiceId());
        dto.setActivityId(activity.getActivityId().toString());
        dto.setRoomId(room.getRoomId());
        AppointmentSeries series = dto.getAppointmentSeries();
        series.setLocationProfile(profile);
        series.setSite(gcs);
        series.setUpdatedBy(updater);
        series = appointmentSeriesService.createAppointmentSeries(series, updater);
        Appointment appt = dto.getAppointment();
        appt.setLocationProfile(profile);
        appt.setSite(gcs);
        appt.setUpdatedBy(updater);
        appt.setAppointmentSeries(series);
        appointmentService.createAppointment(appt);
        
         Verify 5 appointments were created for customers; confirm with both
         * customer ids.
         
        cal.add(Calendar.DAY_OF_MONTH, 1);
        Date end = cal.getTime();
        List<Appointment> appts =
            appointmentService.loadByCustomer(johnJonesId.longValue(),
                                              searchRangeStart, end,
                                              profile.getProfileId());
        assertNotNull("appts is null", appts);
        assertTrue("appts.size() != 5: " + appts.size(), 5 == appts.size());
        appts = appointmentService.loadByCustomer(janeSmithId.longValue(),
                                                  searchRangeStart, end,
                                                  profile.getProfileId());
        assertNotNull("appts is null", appts);
        assertTrue("appts.size() != 5: " + appts.size(), 5 == appts.size());
        
         Send a cancelled POS message and verify all appointments today and
         * after are deleted for JOHN. See later for JANE verification.
         
        posJohnJones.getPurchaseDetails().getGCStudio1()
            .setCustomerStatus(CustomerStatus.CUSTOMER_STATUS_CANCELLED);
        service.process(posJohnJones);
        customer = customerService.getCustomerByExternalId(gcs.getSiteId(),
                                                           johnJonesExternalId);
        assertNotNull("customer is null", customer);
        appts = appointmentService.loadByCustomer(johnJonesId.longValue(),
                                                  searchRangeStart, end,
                                                  profile.getProfileId());
        assertNotNull("appts is null", appts);
        assertTrue("appts.size() != 2: " + appts.size(), 2 == appts.size());
        
         Check the remaining appointments and verify they are all before start
         * of today.
         
        cal.setTime(new Date());
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        for (Appointment remaining: appts) {
            assertNotNull("remaining is null", remaining);
            assertTrue("remaining.customers.size(" +
                       remaining.getCustomers().size() + ") != 2",
                       2 == remaining.getCustomers().size());
            assertNotNull("remaining.startTime is null", remaining.getStartTime());
            assertTrue("appointment start time is after midnight this morning: " +
                       remaining.getStartTimeStr(),
                       remaining.getStartTime().compareTo(cal.getTime()) < 0);
            assertNotNull("remaining.endTime is null", remaining.getEndTime());
            assertTrue("appointment end time is after midnight this morning: " +
                       remaining.getEndTimeStr(),
                       remaining.getEndTime().compareTo(cal.getTime()) < 0);
            assertNotNull("appt series is null", remaining.getAppointmentSeries());
            assertEquals("appt series.id(" +
                         remaining.getAppointmentSeries().getAppointmentSeriesId() +
                         ") != series.id(" + series.getAppointmentSeriesId() + ")",
                         series.getAppointmentSeriesId(),
                         remaining.getAppointmentSeries().getAppointmentSeriesId());
            assertNotNull("appt series.startTime is null",
                    remaining.getAppointmentSeries().getSeriesStartTime());
            assertNotNull("appt series.endTime is null",
                         remaining.getAppointmentSeries().getSeriesEndTime());
        }
        
         Jane should still see all 5 appointments; appointments on or after
         * today should have only one customer id.
         
        appts = appointmentService.loadByCustomer(janeSmithId.longValue(),
                                                  searchRangeStart, end,
                                                  profile.getProfileId());
        assertNotNull("appts is null", appts);
        assertTrue("appts.size() != 5: " + appts.size(), 5 == appts.size());
        
         Check the remaining appointments and verify that appointments before
         * today have two customer ids, others have 1.
         
        for (Appointment remaining: appts) {
            assertNotNull("remaining is null", remaining);
            if (remaining.getStartTime().compareTo(cal.getTime()) < 0) {
                assertTrue("remaining.customers.size(" +
                           remaining.getCustomers().size() + ") != 2",
                           2 == remaining.getCustomers().size());
            } else {
                assertTrue("remaining.customers.size(" +
                        remaining.getCustomers().size() + ") != 1",
                        1 == remaining.getCustomers().size());
            }
            assertNotNull("remaining.startTime is null", remaining.getStartTime());
            assertNotNull("remaining.endTime is null", remaining.getEndTime());
            assertNotNull("appt series is null", remaining.getAppointmentSeries());
            assertEquals("appt series.id(" +
                         remaining.getAppointmentSeries().getAppointmentSeriesId() +
                         ") != series.id(" + series.getAppointmentSeriesId() + ")",
                         series.getAppointmentSeriesId(),
                         remaining.getAppointmentSeries().getAppointmentSeriesId());
            assertNotNull("appt series.startTime is null",
                          remaining.getAppointmentSeries().getSeriesStartTime());
            assertNotNull("appt series.endTime is null",
                          remaining.getAppointmentSeries().getSeriesEndTime());
        }
 
    }*/
}