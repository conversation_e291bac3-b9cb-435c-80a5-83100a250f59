package com.guitarcenter.scheduler.integration.adapter;

import static org.junit.Assert.*;

import java.io.IOException;
import java.util.List;
import javax.xml.transform.stream.StreamSource;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.oxm.Unmarshaller;
import org.springframework.oxm.XmlMappingException;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.guitarcenter.scheduler.dto.CustomerDTO;
import com.guitarcenter.scheduler.dto.InstrumentDTO;
import com.guitarcenter.scheduler.integration.generated.SyncCustomer;
import com.guitarcenter.scheduler.integration.generated.SyncCustomer.PurchaseDetails.GCStudio.LessonInstrument;
import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;

/**
 * Implement some test cases for SyncCustomerCustomerDTO adapter
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class SyncCustomerCustomerDTOTest {
    
    @Autowired
    private Unmarshaller posCustomerMarshaller;
    
    /* POS Customer XML records from DEV, sanitised to remove personal
     * information
     */
    @Autowired
    private Resource resPOSCustomerJohnSmith;
    
    /* SyncCustomer representation of the resource
     */
    private SyncCustomer syncCustomerJohnSmith;
    
    @Before
    public void setUp()
        throws XmlMappingException, IOException
    {
        assertNotNull("unmarshaller is null", posCustomerMarshaller);
        assertNotNull("resPOSCustomerJohnSmith is null", resPOSCustomerJohnSmith);
        assertTrue("resPOSCustomerJohnSmith.exists() != true",
                   resPOSCustomerJohnSmith.exists());
        syncCustomerJohnSmith = (SyncCustomer)
            posCustomerMarshaller.unmarshal(new StreamSource(resPOSCustomerJohnSmith.getInputStream()));
    }
    
    /* Simple test; if the supplied XML file does not convert properly then
     * there will be real problems later!
     */
    @Test
    public void testSampleJohnSmith()
        throws IntegrationServiceException
    {
        CustomerDTO johnSmith =
            ExternalAdapterFactory.getExternalAdapter(syncCustomerJohnSmith, CustomerDTO.class).getEntity();
        assertNotNull("johnSmith is null", johnSmith);
        String expectedResult = "GCS";
        assertEquals("johnSmith.site != '" + expectedResult + "': " +
                     johnSmith.getSite(), expectedResult, johnSmith.getSite());
        expectedResult = "2223488270";
        assertEquals("johnSmith.externalId != '" + expectedResult + "': " +
                     johnSmith.getExternalId(), expectedResult,
                     johnSmith.getExternalId());
        expectedResult = "POS";
        assertEquals("johnSmith.externalSource != '" + expectedResult + "': " +
                     johnSmith.getExternalSource(), expectedResult,
                     johnSmith.getExternalSource());
        expectedResult = "John";
        assertEquals("johnSmith.firstName != '" + expectedResult + "': " +
                     johnSmith.getFirstName(), expectedResult,
                     johnSmith.getFirstName());
        expectedResult = "Smith";
        assertEquals("johnSmith.lastName != '" + expectedResult + "': " +
                     johnSmith.getLastName(), expectedResult,
                     johnSmith.getLastName());
        expectedResult = "(*************";
        assertEquals("johnSmith.phone != '" + expectedResult + "': " +
                     johnSmith.getPhone(), expectedResult, johnSmith.getPhone());
        expectedResult = "<EMAIL>";
        assertEquals("johnSmith.email != '" + expectedResult + "': " +
                     johnSmith.getEmail(), expectedResult, johnSmith.getEmail());
        expectedResult = "C";
        assertEquals("johnSmith.customerStatus != " + expectedResult,
                     expectedResult, johnSmith.getCustomerStatus());
        assertNull("johnSmith.instruments is not null",
                   johnSmith.getInstruments());
    }
    
    /* Add an instrument and verify
     */
    @Test
    public void testInstrument()
        throws IntegrationServiceException
    {
        List<LessonInstrument> instruments =
            syncCustomerJohnSmith.getPurchaseDetails().getGCStudio1().getLessonInstrumentList();
        LessonInstrument instrument = new LessonInstrument();
        instrument.setID("instrumentId");
        instrument.setDescription("test instrument description");
        instrument.setName("test instrument");
        instruments.add(instrument);
        CustomerDTO johnSmith =
                ExternalAdapterFactory.getExternalAdapter(syncCustomerJohnSmith, CustomerDTO.class).getEntity();
        assertNotNull("johnSmith is null", johnSmith);
        String expectedResult = "GCS";
        assertEquals("johnSmith.site != '" + expectedResult + "': " +
                     johnSmith.getSite(), expectedResult, johnSmith.getSite());
        expectedResult = "2223488270";
        assertEquals("johnSmith.externalId != '" + expectedResult + "': " +
                     johnSmith.getExternalId(), expectedResult,
                     johnSmith.getExternalId());
        expectedResult = "POS";
        assertEquals("johnSmith.externalSource != '" + expectedResult + "': " +
                     johnSmith.getExternalSource(), expectedResult,
                     johnSmith.getExternalSource());
        expectedResult = "John";
        assertEquals("johnSmith.firstName != '" + expectedResult + "': " +
                     johnSmith.getFirstName(), expectedResult,
                     johnSmith.getFirstName());
        expectedResult = "Smith";
        assertEquals("johnSmith.lastName != '" + expectedResult + "': " +
                     johnSmith.getLastName(), expectedResult,
                     johnSmith.getLastName());
        expectedResult = "(*************";
        assertEquals("johnSmith.phone != '" + expectedResult + "': " +
                     johnSmith.getPhone(), expectedResult, johnSmith.getPhone());
        expectedResult = "<EMAIL>";
        assertEquals("johnSmith.email != '" + expectedResult + "': " +
                     johnSmith.getEmail(), expectedResult, johnSmith.getEmail());
        assertNotNull("johnSmith.customerStatus is null",
                   johnSmith.getCustomerStatus());
        expectedResult = "C";
        assertEquals("johnSmith.customerStatus != " + expectedResult,
                     expectedResult, johnSmith.getCustomerStatus());
        assertNotNull("johnSmith.instruments is null",
                      johnSmith.getInstruments());
        assertTrue("johnSmith.instruments.size() != 1: " +
                    johnSmith.getInstruments().size(),
                    1 == johnSmith.getInstruments().size());
        InstrumentDTO instrumentDTO =
            (InstrumentDTO) johnSmith.getInstruments().toArray()[0];
        assertEquals("johnSmith.getInstruments(0).externalId != " +
                     instrument.getID(), instrument.getID(),
                     instrumentDTO.getExternalId());
    }
    
    /* Change a status and verify
     */
    @Test
    public void testStatus()
        throws IntegrationServiceException
    {
        syncCustomerJohnSmith.getPurchaseDetails().getGCStudio1().setCustomerStatus("testStatus");
        CustomerDTO johnSmith =
                ExternalAdapterFactory.getExternalAdapter(syncCustomerJohnSmith, CustomerDTO.class).getEntity();
        assertNotNull("johnSmith is null", johnSmith);
        String expectedResult = "GCS";
        assertEquals("johnSmith.site != '" + expectedResult + "': " +
                     johnSmith.getSite(), expectedResult, johnSmith.getSite());
        expectedResult = "2223488270";
        assertEquals("johnSmith.externalId != '" + expectedResult + "': " +
                     johnSmith.getExternalId(), expectedResult,
                     johnSmith.getExternalId());
        expectedResult = "POS";
        assertEquals("johnSmith.externalSource != '" + expectedResult + "': " +
                     johnSmith.getExternalSource(), expectedResult,
                     johnSmith.getExternalSource());
        expectedResult = "John";
        assertEquals("johnSmith.firstName != '" + expectedResult + "': " +
                     johnSmith.getFirstName(), expectedResult,
                     johnSmith.getFirstName());
        expectedResult = "Smith";
        assertEquals("johnSmith.lastName != '" + expectedResult + "': " +
                     johnSmith.getLastName(), expectedResult,
                     johnSmith.getLastName());
        expectedResult = "(*************";
        assertEquals("johnSmith.phone != '" + expectedResult + "': " +
                     johnSmith.getPhone(), expectedResult, johnSmith.getPhone());
        expectedResult = "<EMAIL>";
        assertEquals("johnSmith.email != '" + expectedResult + "': " +
                     johnSmith.getEmail(), expectedResult, johnSmith.getEmail());
        assertNotNull("johnSmith.customerStatus is null",
                      johnSmith.getCustomerStatus());
        expectedResult = "testStatus";
        assertEquals("johnSmith.customerStatus != " + expectedResult,
                     expectedResult, johnSmith.getCustomerStatus());
        assertNull("johnSmith.instruments is not null",
                      johnSmith.getInstruments());
    }
    

    /* Test override of email address
     */
    @Test
    public void testOverrideEmailJohnSmith()
        throws IntegrationServiceException
    {
        try {
            String testEmail = "<EMAIL>";
            System.getProperties().setProperty("gcss.emailOverride", testEmail);
            CustomerDTO johnSmith =
                ExternalAdapterFactory.getExternalAdapter(syncCustomerJohnSmith, CustomerDTO.class).getEntity();
            assertNotNull("johnSmith is null", johnSmith);
            String expectedResult = "GCS";
            assertEquals("johnSmith.site != '" + expectedResult + "': " +
                         johnSmith.getSite(), expectedResult, johnSmith.getSite());
            expectedResult = "2223488270";
            assertEquals("johnSmith.externalId != '" + expectedResult + "': " +
                         johnSmith.getExternalId(), expectedResult,
                         johnSmith.getExternalId());
            expectedResult = "POS";
            assertEquals("johnSmith.externalSource != '" + expectedResult + "': " +
                         johnSmith.getExternalSource(), expectedResult,
                         johnSmith.getExternalSource());
            expectedResult = "John";
            assertEquals("johnSmith.firstName != '" + expectedResult + "': " +
                         johnSmith.getFirstName(), expectedResult,
                         johnSmith.getFirstName());
            expectedResult = "Smith";
            assertEquals("johnSmith.lastName != '" + expectedResult + "': " +
                         johnSmith.getLastName(), expectedResult,
                         johnSmith.getLastName());
            expectedResult = "(*************";
            assertEquals("johnSmith.phone != '" + expectedResult + "': " +
                         johnSmith.getPhone(), expectedResult, johnSmith.getPhone());
            assertEquals("johnSmith.email != '" + testEmail + "': " +
                         johnSmith.getEmail(), testEmail, johnSmith.getEmail());
            expectedResult = "C";
            assertEquals("johnSmith.customerStatus != " + expectedResult,
                         expectedResult, johnSmith.getCustomerStatus());
            assertNull("johnSmith.instruments is not null",
                       johnSmith.getInstruments());
        } finally {
            System.getProperties().remove("gcss.emailOverride");
        }
    }
}
