package com.guitarcenter.scheduler.integration.adapter;

import static org.junit.Assert.*;

import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.guitarcenter.scheduler.integration.dto.EmployeeDTO;
import com.guitarcenter.scheduler.integration.dto.InstructorPersonalDetailsDTO;
import com.guitarcenter.scheduler.integration.service.IntegrationServiceException;

/**
 * Test harness for EDW Employee file to EmployeeDTO List adapter.
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class AdpInstructorMasterDTOListAdapterTest {
    /**
     * Sample EDW employee files
     */
    @Autowired
    private Resource resADPInstructorFullSample;
    
   
    
    @Before
    public void setup() {
        assertNotNull("resADPInstructorFullSample is null", resADPInstructorFullSample);
        assertTrue("resADPInstructorFullSample.exists() != true",
        		resADPInstructorFullSample.exists());
       
    }
    
    /* Helper to make sure that all expected fields are present in an
     * EmployeeDTO
     * 
     * @param location an instance of EmployeeDTO to test
     */
    private void validateinstructorDTO(InstructorPersonalDetailsDTO instructorpersonaldetails) {
        assertNotNull("instructorpersonaldetails.externalid is null",
        		instructorpersonaldetails.getExternalSource());
        String expectedResult = "ADP";
        assertEquals("employee.externalSource != " + expectedResult,
                     expectedResult, instructorpersonaldetails.getExternalSource());
        /* Latest sample EDW FULL file contains one entry that will fail to
         * validate. If the record is for that 'employee' skip the tests.
         */
        if ("787999".equals(instructorpersonaldetails.getExternalId())) {
            return;
        }
       /* assertNotNull("employee.site is null", instructorpersonaldetails.get);
         EDW files may contain sites other than GCS; filtering happens in
         * service business logic, so just check for non-blank.
         
        assertFalse("employee.site is blank",
                    StringUtils.isBlank(employee.getSite()));
        assertNotNull("employee.studio is null", employee.getStudio());
         Expect the studio to be a studio number; will fail if not a
         * valid number. Modified because not all studio numbers are strictly
         * numeric - MFI has a store number of '9901M'. Changed the validation
         * to simply check for non-blank studio identifier.
         */
        assertFalse("instructorpersonaldetails.Location is blank",
                    StringUtils.isBlank(instructorpersonaldetails.getLocation()));
        assertNotNull("instructorpersonaldetails.externalId is null", instructorpersonaldetails.getExternalId());
        assertFalse("instructorpersonaldetails.externalId is blank",
                    StringUtils.isBlank( instructorpersonaldetails.getExternalId()));
        assertNotNull("instructorpersonaldetails.firstName is null", instructorpersonaldetails.getFirstName());
        assertFalse("instructorpersonaldetails.firstName is blank",
                    StringUtils.isBlank( instructorpersonaldetails.getLastName()));
        assertNotNull("instructorpersonaldetails.lastName is null", instructorpersonaldetails.getLastName());
        assertFalse("instructorpersonaldetails.lastName is blank",
                    StringUtils.isBlank(instructorpersonaldetails.getFirstName()));
        /* Do not test email; some employees do not have an email address in
         * system of record.
         */
        assertFalse("instructorpersonaldetails.jobCode == 0", "0" == instructorpersonaldetails.getJobCode());
        assertNotNull("instructorpersonaldetails.status is null", instructorpersonaldetails.getRehire());
        assertFalse("employee.status is blank",
                    StringUtils.isBlank( instructorpersonaldetails.getRehire()));
    }
    
    /* Make sure that the supplied FULL sample file can be parsed and validates!
     */
    @Test
    public void testFullSampleFile()
        throws IntegrationServiceException, IOException
    {
        List<InstructorPersonalDetailsDTO> instructorPersonalDetails =
        		ExternalADPAdapterFactory.getExternalAdapter(new InputStreamReader(resADPInstructorFullSample.getInputStream(), Charset.forName("UTF-8")), InstructorPersonalDetailsDTO.class).getEntities();
        assertNotNull("instructor is null", instructorPersonalDetails);
        assertFalse("instructor is empty", instructorPersonalDetails.isEmpty());
        /* There are 11478 lines in the file, but only 9903 are for GCS.
         * However, filtering happens in service business logic so expect all
         * 11478 to be converted to DTO.
         */
        int expectedCount = 11478;
        assertTrue("instructor.size(" + instructorPersonalDetails.size() + ") != " +
                   expectedCount, expectedCount == instructorPersonalDetails.size());
        /* Perform a minimal validation on each record
         */
       /* for (InstructorPersonalDetailsDTO instructorPersonalDetail: instructorPersonalDetails) {
            validateEmployeeDTO(instructorPersonalDetail);
        }*/
    }
    
    /* Make sure that the supplied INCR sample file can be parsed and validates!
     */
   
}
