package com.guitarcenter.scheduler.service;

import static org.junit.Assert.*;

import java.io.IOException;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrServer;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.common.SolrDocument;
import org.apache.solr.common.SolrDocumentList;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.guitarcenter.scheduler.common.util.AppConstants;
import com.guitarcenter.scheduler.dto.SearchDTO;
import com.guitarcenter.scheduler.model.Customer;
import com.guitarcenter.scheduler.model.CustomerStatus;
import com.guitarcenter.scheduler.model.Instructor;
import com.guitarcenter.scheduler.model.Person;
import com.guitarcenter.scheduler.model.Site;
import com.guitarcenter.scheduler.dto.SearchDTOBuilder;

/**
 * Implements a set of test cases for Solr implementation of SearchService.
 * 
 * <AUTHOR> Emes <a
 *         href="mailto:<EMAIL>">&lt;<EMAIL>&gt;</a>
 * @version $Id:$
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:spring/test-context.xml" })
public class SolrSearchServiceTest {
	/**
	 * Query to match all records
	 */
	public static final String	MATCH_EVERYTHING	= "*:*";

	/**
	 * Copy of the solrServer provided to search service; used to query directly
	 * to test searching facade.
	 */
	@Autowired
	private SolrServer			solrServer;

	/**
	 * An instance of SearchService to test
	 */
	@Autowired
	private SearchService		searchService;

	/**
	 * Some constants to use in tests
	 */
	private Site				site1;
	private Site				site99;
	private CustomerStatus		active;
	private CustomerStatus		cancelled;



	public SolrSearchServiceTest() {
		site1 = new Site();
		site1.setSiteId(1l);
		site1.setName("Test Site 1");
		site99 = new Site();
		site99.setSiteId(99l);
		site99.setName("Test Site 99");
		active = new CustomerStatus();
		active.setCustomerStatusId(1l);
		active.setStatusName(active.getExternalId());
		cancelled = new CustomerStatus();
		cancelled.setCustomerStatusId(2l);
		cancelled.setExternalId(CustomerStatus.CUSTOMER_STATUS_CANCELLED);
	}



	/**
	 * Prepare the Solr index before each test 1. Test for autowiring of
	 * searchService and solrServer 2. Delete all documents 2.1 sleep to make
	 * sure searches do not see stale data 3. Verify index is empty
	 * 
	 * @throws SolrServerException
	 * @throws IOException
	 * @throws InterruptedException
	 */
	@Before
	public void readyForTesting() throws SolrServerException, IOException, InterruptedException {
		assertNotNull("searchService is null", searchService);
		assertNotNull("solrServer is null", solrServer);
		solrServer.deleteByQuery(MATCH_EVERYTHING);
		solrServer.commit();
		// Give Solr time to commit the delete!
		Thread.sleep(500l);
		SolrDocumentList docs = getQueryResults(MATCH_EVERYTHING);
		assertTrue("docs.getNumFound() != 0 before test case", 0 == docs.getNumFound());
	}



	/**
	 * Query helper
	 * 
	 * @param query
	 *            String containing a query to execute
	 * @return a SolrDocumentList from Solr
	 * @throws SolrServerException
	 */
	private SolrDocumentList getQueryResults(String query) throws SolrServerException {
		assertNotNull("solrServer is null", solrServer);
		SolrQuery request = new SolrQuery();
		request.setQuery(query);
		QueryResponse response = solrServer.query(request);
		assertNotNull("response is null", response);
		assertNotNull("response.getResults() are null", response.getResults());
		return response.getResults();
	}



	/**
	 * Helper to create John Smith in site 1
	 */
	private Customer buildJohnSmith() {
		Customer customer = new Customer();
		customer.setSite(site1);
		customer.setCustomerId(1l);
		customer.setExternalId("001");
		customer.setPerson(new Person());
		customer.getPerson().setFirstName("John");
		customer.getPerson().setLastName("Smith");
		customer.getPerson().setEmail("<EMAIL>");
		customer.getPerson().setPhone("1111111111");
		customer.setCustomerStatus(active);
		return customer;
	}



	/**
	 * Add a customer and verify it appears in Solr when queried
	 * 
	 * @throws SolrServerException
	 * @throws IOException
	 */
	@Test
	public void testAddCustomer() throws SolrServerException, IOException {
		Customer customer = buildJohnSmith();
		searchService.updateRecord(customer);
		SolrDocumentList docs = getQueryResults(MATCH_EVERYTHING);
		assertTrue("docs does not have one record", 1 == docs.getNumFound());
		verifyCustomer(customer, docs.get(0));

	}



	/**
	 * Helper method to verify a customer record and object matches
	 * 
	 * @param customer
	 *            an instance of Customer to compare
	 * @param record
	 *            a Solr record to verify
	 */
	private void verifyCustomer(Customer customer, SolrDocument record) {
		assertEquals("record does not have type '" + AppConstants.SEARCH_CUSTOMER_TYPE_STRING + "'",
				AppConstants.SEARCH_CUSTOMER_TYPE_STRING, record.getFieldValue(SearchDTO.SOLR_TYPE_FIELD));
		assertEquals("record customer id != " + customer.getCustomerId(),
				record.getFieldValue(SearchDTO.SOLR_RECORD_ID_FIELD), Long.valueOf(customer.getCustomerId()));
		assertEquals("record external id != " + customer.getExternalId(),
				record.getFieldValue(SearchDTO.SOLR_EXTERNAL_ID_FIELD), customer.getExternalId());
		assertNotNull("customer person is null", customer.getPerson());
		assertEquals("record first name != " + customer.getPerson().getFirstName(),
				record.getFieldValue(SearchDTO.SOLR_FIRST_NAME_FIELD), customer.getPerson().getFirstName());
		assertEquals("record last name != " + customer.getPerson().getLastName(),
				record.getFieldValue(SearchDTO.SOLR_LAST_NAME_FIELD), customer.getPerson().getLastName());
		if (StringUtils.isNotBlank(customer.getPerson().getEmail())) {
			assertEquals("record email != " + customer.getPerson().getEmail(),
					record.getFieldValue(SearchDTO.SOLR_EMAIL_FIELD), customer.getPerson().getEmail().trim());
		}
        if (StringUtils.isNotBlank(customer.getPerson().getPhone())) {
            assertEquals("record phone != " + customer.getPerson().getPhone(),
                    record.getFieldValue(SearchDTO.SOLR_PHONE_FIELD), customer.getPerson().getPhone().trim());
        }
		if (customer.getCustomerStatus() != null
				&& StringUtils.isNotBlank(customer.getCustomerStatus().getExternalId())) {
			assertEquals("record customer status != " + customer.getCustomerStatus().getStatusName(),
					record.getFieldValue(SearchDTO.SOLR_STATUS_FIELD), customer.getCustomerStatus().getStatusName()
							.trim());
		}
	}



	/**
	 * updateRecord method should ignore a null object
	 * 
	 * @throws SolrServerException
	 * @throws IOException
	 */
	@Test
	public void testAddNull() throws SolrServerException, IOException {
		searchService.updateRecord(null);
		SolrDocumentList docs = getQueryResults(MATCH_EVERYTHING);
		assertTrue("docs does not have zero records", 0 == docs.getNumFound());
	}



	/**
	 * updateRecord method should ignore unrecognised object classes
	 */
	@Test
	public void testAddObject() throws SolrServerException, IOException {
		searchService.updateRecord(new Object());
		SolrDocumentList docs = getQueryResults(MATCH_EVERYTHING);
		assertTrue("docs does not have zero records", 0 == docs.getNumFound());
	}



	/**
	 * Helper to build John Jones in site 1
	 */
	private Customer buildJohnJones() {
		Customer customer = new Customer();
		customer.setSite(site1);
		customer.setCustomerId(2l);
		customer.setExternalId("002");
		customer.setPerson(new Person());
		customer.getPerson().setFirstName("John");
		customer.getPerson().setLastName("Jones");
		customer.getPerson().setEmail("<EMAIL>");
		customer.getPerson().setPhone("2");
		customer.setCustomerStatus(active);
		return customer;
	}



	/**
	 * Add two customers and verify the records are in Solr
	 * 
	 * @throws IOException
	 * @throws SolrServerException
	 */
	@Test
	public void testAddSecondCustomer() throws SolrServerException, IOException {
		Customer johnSmith = buildJohnSmith();
		searchService.updateRecord(johnSmith);
		Customer johnJones = buildJohnJones();
		searchService.updateRecord(johnJones);
		SolrDocumentList docs = getQueryResults(SearchDTO.SOLR_SEARCH_FIRST_NAME_FIELD + ":john");
		assertTrue("docs does not have two records", 2 == docs.getNumFound());
		docs = getQueryResults(SearchDTO.SOLR_SEARCH_LAST_NAME_FIELD + ":smith");
		assertTrue("docs does not have one record", 1 == docs.getNumFound());
		verifyCustomer(johnSmith, docs.get(0));
		docs = getQueryResults(SearchDTO.SOLR_SEARCH_LAST_NAME_FIELD + ":jones");
		assertTrue("docs does not have one record", 1 == docs.getNumFound());
		verifyCustomer(johnJones, docs.get(0));
	}



	/**
	 * Test quick search interface searching with null and blank terms.
	 * 
	 * @throws SolrServerException
	 */
	@Test
	public void testQuickSearchMissingValues() throws SolrServerException {
		List<SearchDTO> results = searchService.quickSearch(site1.getSiteId(), null, null);
		assertNotNull("results are null", results);
		assertTrue("results are not empty", 0 == results.size());
		results = searchService.quickSearch(site1.getSiteId(), "", null);
		assertNotNull("results are null", results);
		assertTrue("results are not empty", 0 == results.size());
	}



	/**
	 * Helper method to verify a customer record and SearchDTO matches
	 * 
	 * @param customer
	 *            an instance of Customer to compare
	 * @param dto
	 *            an instance of SearchDTO to verify
	 */
	private void verifyQuickCustomer(Customer customer, SearchDTO dto) {
		assertEquals("dto customer id != " + customer.getCustomerId(), dto.getRecordId(), customer.getCustomerId());
		assertNotNull("customer person is null", customer.getPerson());
		assertEquals("dto first name != " + customer.getPerson().getFirstName(), dto.getFirstName(), customer
				.getPerson().getFirstName());
		assertEquals("dto last name != " + customer.getPerson().getLastName(), dto.getLastName(), customer.getPerson()
				.getLastName());
	}



	/**
	 * Test quick search for one customer.
	 * 
	 * @throws SolrServerException
	 * @throws IOException
	 */
	@Test
	public void testQuickSearchOneRecord() throws SolrServerException, IOException {
		/*
		 * Populate Solr index with John Jones
		 */
		Customer johnJones = buildJohnJones();
		searchService.updateRecord(johnJones);
		SolrDocumentList docs = getQueryResults(MATCH_EVERYTHING);
		assertTrue("docs does not have one record", 1 == docs.getNumFound());

		/*
		 * Search for John Jones in site1 using quickSearch method.
		 */
		List<SearchDTO> results = searchService.quickSearch(site1.getSiteId(), "joh", null);
		assertNotNull("results are null", results);
		assertTrue("results does not have 1 record: " + results.size(), 1 == results.size());
		verifyQuickCustomer(johnJones, results.get(0));
	}



	/**
	 * Helper to build John Jones in site 99
	 */
	private Customer buildJohnJones2() {
		Customer customer = new Customer();
		customer.setSite(site99);
		customer.setCustomerId(3l);
		customer.setExternalId("003");
		customer.setPerson(new Person());
		customer.getPerson().setFirstName("John");
		customer.getPerson().setLastName("Jones");
		customer.getPerson().setEmail("<EMAIL>");
		customer.getPerson().setPhone("************");
		customer.setCustomerStatus(active);
		return customer;
	}



	/**
	 * Test quick search for one valid customer.
	 * 
	 * @throws SolrServerException
	 * @throws IOException
	 */
	@Test
	public void testQuickSearchOneValidRecord() throws SolrServerException, IOException {
		/*
		 * Populate Solr index with John Jones and John Jones (site 99)
		 */
		Customer johnJones = buildJohnJones();
		searchService.updateRecord(johnJones);
		Customer johnJones2 = buildJohnJones2();
		searchService.updateRecord(johnJones2);
		SolrDocumentList docs = getQueryResults(MATCH_EVERYTHING);
		assertTrue("docs does not have two records", 2 == docs.getNumFound());

		/*
		 * Search for 'jo' in site1 using quickSearch method. Only John Jones in
		 * site 1 should be in results.
		 */
		List<SearchDTO> results = searchService.quickSearch(site1.getSiteId(), "joh", null);
		assertNotNull("results are null", results);
		assertTrue("results does not have 1 record: " + results.size(), 1 == results.size());
		verifyQuickCustomer(johnJones, results.get(0));

		/*
		 * Search for 'joh' in site99 using quickSearch method. Only John Jones
		 * in site 99 should be in results.
		 */
		results = searchService.quickSearch(site99.getSiteId(), "joh", null);
		assertNotNull("results are null", results);
		assertTrue("results does not have 1 record: " + results.size(), 1 == results.size());
		verifyQuickCustomer(johnJones2, results.get(0));
	}



	/**
	 * Helper to build Joyce James in site 1
	 */
	private Customer buildJoyceJames() {
		Customer customer = new Customer();
		customer.setSite(site1);
		customer.setCustomerId(4l);
		customer.setExternalId("004");
		customer.setPerson(new Person());
		customer.getPerson().setFirstName("Joyce");
		customer.getPerson().setLastName("James");
		customer.getPerson().setEmail("<EMAIL>");
		customer.getPerson().setPhone("(*************");
		customer.setCustomerStatus(active);
		return customer;
	}



	/**
	 * Test quick search when there are three records.
	 * 
	 * @throws SolrServerException
	 * @throws IOException
	 */
	@Test
	public void testQuickSearchThreeMatchingRecords() throws SolrServerException, IOException {
		/*
		 * Populate Solr index with John Smith, John Jones and Joyce James, all
		 * in site 1
		 */
		Customer johnSmith = buildJohnSmith();
		searchService.updateRecord(johnSmith);
		Customer johnJones = buildJohnJones();
		searchService.updateRecord(johnJones);
		Customer joyceJames = buildJoyceJames();
		searchService.updateRecord(joyceJames);
		SolrDocumentList docs = getQueryResults(MATCH_EVERYTHING);
		assertTrue("docs does not have three records", 3 == docs.getNumFound());

		/*
		 * Search for 'jo' in site1 using quickSearch method. There should be
		 * three results, with John Jones being the 'best' match (first and last
		 * name) ahead of John Smith and Joyce James(first name only).
		 * 
		 * Second order of sorting is descending alphabetical search on first
		 * and last names. E.g. John Smith will be ahead of Joyce James.
		 */
		List<SearchDTO> results = searchService.quickSearch(site1.getSiteId(), "joh", null);
		assertNotNull("results are null", results);
		assertTrue("results does not have 2 records: " + results.size(), 2 == results.size());
		verifyQuickCustomer(johnJones, results.get(0));
		verifyQuickCustomer(johnSmith, results.get(1));
	}



	/**
	 * Helper to build Jon Cancel in site 1
	 */
	private Customer buildJonCancel() {
		Customer customer = new Customer();
		customer.setSite(site1);
		customer.setCustomerId(5l);
		customer.setExternalId("005");
		customer.setPerson(new Person());
		customer.getPerson().setFirstName("Jon");
		customer.getPerson().setLastName("Cancel");
		customer.getPerson().setEmail("<EMAIL>");
		customer.getPerson().setPhone("******-555 5555 ");
		customer.setCustomerStatus(cancelled);
		return customer;
	}



	/**
	 * Test quick search should not include cancelled customers.
	 * 
	 * @throws SolrServerException
	 * @throws IOException
	 */
	@Test
	public void testQuickSearchIgnoresCancelled() throws SolrServerException, IOException {
		/*
		 * Add a cancelled customer and then verify that the customer is not in
		 * any quick search results.
		 */
		searchService.updateRecord(buildJonCancel());

		/*
		 * Search for 'jon cancel' in site1 using quickSearch method. There
		 * should be no matching results.
		 */
		List<SearchDTO> results = searchService.quickSearch(site1.getSiteId(), "jon cancel", null);
		assertNotNull("results are null", results);
		assertTrue("results does not have 0 records: " + results.size(), 0 == results.size());
	}



	/**
	 * Helper to build Anne Instructor in site 1
	 */
	private Instructor buildAnneInstructor() {
		Instructor instructor = new Instructor();
		instructor.setSite(site1);
		instructor.setInstructorId(1l);
		instructor.setExternalId("I1");
		instructor.setPerson(new Person());
		instructor.getPerson().setFirstName("Anne");
		instructor.getPerson().setLastName("Instructor");
		instructor.getPerson().setEmail("<EMAIL>");
		/* No phone number
		 */
		return instructor;
	}



	/**
	 * Test quick search should not include instructors for sprint 2.
	 * 
	 * @throws SolrServerException
	 * @throws IOException
	 */
	@Test
	public void testQuickSearchIgnoresInstructors() throws SolrServerException, IOException {
		/*
		 * Add an instructor and verify that quick search does not include the
		 * instructor in results.
		 */
		searchService.updateRecord(buildAnneInstructor());

		/*
		 * Search for 'anne instructor' in site1 using quickSearch method. There
		 * should be no matching results.
		 */
		List<SearchDTO> results = searchService.quickSearch(site1.getSiteId(), "anne instructor", null);
		assertNotNull("results are null", results);
		assertTrue("results does not have 0 records: " + results.size(), 0 == results.size());
	}



	/**
	 * Helper to build Joe Rocker in site 1
	 */
	private Customer buildJoeRocker() {
		Customer customer = new Customer();
		customer.setSite(site1);
		customer.setCustomerId(6l);
		customer.setExternalId("006");
		customer.setPerson(new Person());
		customer.getPerson().setFirstName("Joe");
		customer.getPerson().setLastName("Rocker");
		/* No email
		 */
		customer.getPerson().setPhone("abc def ghij");
		customer.setCustomerStatus(active);
		return customer;
	}



	/**
	 * Helper to build John von Zipper in site 1
	 */
	private Customer buildJohnVonZipper() {
		Customer customer = new Customer();
		customer.setSite(site1);
		customer.setCustomerId(7l);
		customer.setExternalId("007");
		customer.setPerson(new Person());
		customer.getPerson().setFirstName("John");
		customer.getPerson().setLastName("von Zipper");
		customer.getPerson().setEmail("<EMAIL>");
		customer.getPerson().setPhone("6666666666");
		customer.setCustomerStatus(active);
		return customer;
	}



	/**
	 * Helper to add all customer/instructor records to search index
	 * 
	 * @throws IOException
	 * @throws SolrServerException
	 * @throws InterruptedException
	 */
	private void addAllRecords() throws SolrServerException, IOException, InterruptedException {
		/*
		 * Populate Solr index with all records
		 */
		searchService.updateRecord(buildJohnSmith());
		searchService.updateRecord(buildJohnJones());
		searchService.updateRecord(buildJoyceJames());
		searchService.updateRecord(buildJohnJones2());
		searchService.updateRecord(buildJonCancel());
		searchService.updateRecord(buildAnneInstructor());
		searchService.updateRecord(buildJoeRocker());
		searchService.updateRecord(buildJohnVonZipper());
		SolrDocumentList docs = getQueryResults(MATCH_EVERYTHING);
		assertTrue("docs does not have 8 records: " + docs.getNumFound(), 8 == docs.getNumFound());
	}



	/**
	 * Test quick search functions correctly for last name matches
	 * 
	 * @throws SolrServerException
	 * @throws IOException
	 * @throws InterruptedException
	 */
	@Test
	public void testQuickSearchLastName() throws SolrServerException, IOException, InterruptedException {
		addAllRecords();

		/*
		 * Search for 'smi' in site1 using quickSearch method. There should be
		 * one result for John Smith.
		 */
		List<SearchDTO> results = searchService.quickSearch(site1.getSiteId(), "smi", null);
		assertNotNull("results are null", results);
		assertTrue("results does not have 1 record: " + results.size(), 1 == results.size());
		verifyQuickCustomer(buildJohnSmith(), results.get(0));

		/*
		 * Search for 'vo' in site1 using quickSearch method. There should be
		 * one result for John von Zipper.
		 */
		results = searchService.quickSearch(site1.getSiteId(), "von", null);
		assertNotNull("results are null", results);
		assertTrue("results does not have 1 record: " + results.size(), 1 == results.size());
		verifyQuickCustomer(buildJohnVonZipper(), results.get(0));
	}



	/**
	 * Test quick search for multiple words
	 * 
	 * @throws SolrServerException
	 * @throws IOException
	 * @throws InterruptedException
	 */
	@Test
	public void testQuickSearchMultipleWords() throws SolrServerException, IOException, InterruptedException {
		addAllRecords();

		/*
		 * Search for 'john jon' in site1 using quickSearch method. There should
		 * be one match:- John Jones (first name matches john last name matches
		 * jon)
		 */
		List<SearchDTO> results = searchService.quickSearch(site1.getSiteId(), "john jon", null);
		assertNotNull("results are null", results);
		assertTrue("results does not have 1 record: " + results.size(), 1 == results.size());
		verifyQuickCustomer(buildJohnJones(), results.get(0));

		/*
		 * Search for 'john zip' in site1 using quickSearch method. There should
		 * be no matches.
		 */
		results = searchService.quickSearch(site1.getSiteId(), "john zip", null);
		assertNotNull("results are null", results);
		assertTrue("results does not have 0 records: " + results.size(), 0 == results.size());

		/*
		 * However, search for 'john VOn z' in site1 should match, despite the
		 * numerous spaces and tabs and case issues.
		 */
		results = searchService.quickSearch(site1.getSiteId(), "john  \tVOn\t  z", null);
		assertNotNull("results are null", results);
		assertTrue("results does not have 1 records: " + results.size(), 1 == results.size());
		verifyQuickCustomer(buildJohnVonZipper(), results.get(0));
	}
}
