package com.guitarcenter.scheduler.service.mail;

import java.io.File;
import java.io.IOException;
import java.util.Collections;
import java.util.Map;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import freemarker.template.Configuration;
import freemarker.template.TemplateException;

@Service("mimeMailService")
public class MimeMailService {

	private static final String	DEFAULT_ENCODING	= "utf-8";

	private static Logger		logger				= LoggerFactory.getLogger(MimeMailService.class);

	@Autowired
	@Qualifier("mailSender")
	private JavaMailSender		mailSender;

	@Autowired
	@Qualifier("freemarkerConfiguration")
	private Configuration		freemarkerConfiguration;



	public void sendNotificationMail(String userName) {

		try {
			MimeMessage msg = mailSender.createMimeMessage();
			MimeMessageHelper helper = new MimeMessageHelper(msg, true, DEFAULT_ENCODING);

			helper.setTo("<EMAIL>");
			helper.setFrom("<EMAIL>");
			helper.setSubject("User EMail Notification");

			String content = generateContent(userName);
			helper.setText(content, true);

			File attachment = generateAttachment();
			helper.addAttachment("mailAttachment.txt", attachment);

			mailSender.send(msg);
			logger.info("HTML EMail is <NAME_EMAIL>");
		} catch (MessagingException e) {
			logger.error("Build Message throws error", e);
		} catch (Exception e) {
			logger.error("Sent Message throws error", e);
		}
	}



	private String generateContent(String userName) throws MessagingException {

		try {
			Map<String, String> context = Collections.singletonMap("userName", userName);
			return FreeMarkerTemplateUtils.processTemplateIntoString(
					freemarkerConfiguration.getTemplate("mailTemplate.ftl", DEFAULT_ENCODING), context);
		} catch (IOException e) {
			logger.error("FreeMarker template is not found", e);
			throw new MessagingException("FreeMarker template is not found", e);
		} catch (TemplateException e) {
			logger.error("FreeMarker processes error", e);
			throw new MessagingException("FreeMarker processes error", e);
		}
	}



	private File generateAttachment() throws MessagingException {
		try {
			Resource resource = new ClassPathResource("/email/mailAttachment.txt");
			return resource.getFile();
		} catch (IOException e) {
			logger.error("Mail Attachment is not found", e);
			throw new MessagingException("Mail Attachment is not found", e);
		}
	}

}
