package com.guitarcenter.scheduler.dao.criterion;

import static com.guitarcenter.scheduler.dao.util.DAOHelper.FETCH_UPDATEBY_PERSON;
import static com.guitarcenter.scheduler.dao.util.DAOHelper.addFetchHQL;

import java.util.List;

import org.hibernate.Query;
import org.hibernate.Session;

import com.guitarcenter.scheduler.model.TestBean;
import com.guitarcenter.scheduler.model.dto.TestBeanDTO;

public abstract class TestBeanCriterion<E> extends AbstractCriterion<TestBean, E> implements Criterion<TestBean, E> {

	private static final Criterion<TestBean, TestBean>	DEFAULT_INSTANCE	= new TestBeanCriterion<TestBean>() {
																			};



	private TestBeanCriterion() {
		super(TestBean.class);
	}



	@Override
	protected String getFetchScript(int pFetchMode) {
		StringBuilder sb = new StringBuilder();
		sb.append(addFetchHQL(FETCH_UPDATEBY_PERSON, pFetchMode, "t.updatedBy", true));
		return sb.toString();
	}



	@Override
	public List<E> search(Session pSession, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	@Override
	public E get(Session pSession, int pFetchMode) {
		throw new UnsupportedOperationException("current criteria object unsupport this operation.");
	}



	public static Criterion<TestBean, TestBean> getInstance() {
		return DEFAULT_INSTANCE;
	}



	public static Criterion<TestBean, TestBean> findByName(String pName) {
		final String name = "%" + pName + "%";

		TestBeanCriterion<TestBean> instance = new TestBeanCriterion<TestBean>() {

			@Override
			@SuppressWarnings("unchecked")
			public List<TestBean> search(Session pSession, int pFetchMode) {
				StringBuilder sb = new StringBuilder("from TestBean t ");
				sb.append(getFetchScript(pFetchMode));
				sb.append("where t.name like :name");
				Query query = pSession.createQuery(sb.toString());
				query.setString("name", name);
				return query.list();
			}

		};
		return instance;
	}



	public static Criterion<TestBean, TestBeanDTO> findByNameWithDTO(String pName) {
		final String name = "%" + pName + "%";

		TestBeanCriterion<TestBeanDTO> instance = new TestBeanCriterion<TestBeanDTO>() {

			@Override
			@SuppressWarnings("unchecked")
			public List<TestBeanDTO> search(Session pSession, int pFetchMode) {
				Query query = pSession
						.createQuery("select new com.guitarcenter.scheduler.model.dto.TestBeanDTO(t.testId, t.updatedBy.personId, t.updated, t.name) from TestBean t where t.name like :name");
				query.setString("name", name);
				return query.list();
			}

		};

		return instance;
	}

}
