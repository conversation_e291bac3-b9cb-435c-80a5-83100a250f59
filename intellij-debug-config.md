# IntelliJ IDEA Remote Debug Configuration Guide

## Quick Start
1. Run the debug script: `./debug-intellij.sh`
2. Follow the IntelliJ setup instructions below
3. Start debugging!

## IntelliJ IDEA Debug Configuration Setup

### Step 1: Create Remote Debug Configuration
1. Open IntelliJ IDEA
2. Go to **Run** → **Edit Configurations...**
3. Click the **+** button (Add New Configuration)
4. Select **Remote JVM Debug**

### Step 2: Configure Debug Settings
Set the following configuration:

| Field | Value |
|-------|-------|
| **Name** | `Scheduler Debug` |
| **Host** | `localhost` |
| **Port** | `8000` |
| **Debugger mode** | `Attach to remote JVM` |
| **Transport** | `Socket` |
| **Use module classpath** | `scheduler` |

### Step 3: Advanced Settings (Optional)
- **Auto restart**: Unchecked (recommended)
- **Reconnect**: Checked (helpful for development)

### Step 4: Save and Use
1. Click **OK** to save the configuration
2. Set breakpoints in your Java code where you want to debug
3. Run the `debug-intellij.sh` script to start Tomcat with debugging
4. In IntelliJ, click the **Debug** button (🐛) and select **Scheduler Debug**
5. You should see "Connected to the target VM" in the Debug console

## Debugging Workflow

### Starting a Debug Session
```bash
# 1. Start the application with debugging enabled
./debug-intellij.sh

# 2. In IntelliJ, start the remote debugger
# Run → Debug → Scheduler Debug
```

### During Debugging
- Set breakpoints by clicking in the left margin of the code editor
- Use the Debug panel to:
  - Step over (F8)
  - Step into (F7)
  - Step out (Shift+F8)
  - Resume execution (F9)
  - Evaluate expressions (Alt+F8)

### Stopping the Debug Session
```bash
# Stop Tomcat
./stop-tomcat.sh

# Or use Ctrl+C in the terminal where Tomcat is running
```

## Troubleshooting

### Common Issues

#### 1. "Connection refused" error
- **Cause**: Tomcat is not running or debug port is not open
- **Solution**: Ensure `debug-intellij.sh` completed successfully

#### 2. "Port already in use" error
- **Cause**: Another process is using port 8000
- **Solution**: Run `./stop-tomcat.sh` or change the debug port in the script

#### 3. Breakpoints not being hit
- **Cause**: Code might not be deployed or source doesn't match
- **Solution**: 
  - Rebuild with `./build-war.sh`
  - Restart debugging session
  - Verify breakpoints are in the correct source files

#### 4. "Unable to open debugger port" 
- **Cause**: Firewall or network issue
- **Solution**: Check if port 8000 is blocked by firewall

### Useful Commands

```bash
# Check if Tomcat is running
lsof -i :8080

# Check if debug port is open
lsof -i :8000

# View Tomcat logs in real-time
tail -f /Users/<USER>/tomcat7/logs/catalina.out

# Check Java processes
jps -v
```

## Configuration Files

### Key Files for Debugging
- `debug-intellij.sh` - Main debugging script
- `build-war.sh` - Builds the WAR file
- `stop-tomcat.sh` - Stops Tomcat
- `custom-tomcat-context.xml` - Tomcat context configuration

### Environment Variables Used
- `JAVA_HOME` - Java 8 installation path
- `CATALINA_OPTS` - Tomcat JVM options including debug settings
- `JPDA_ADDRESS` - Debug port (8000)
- `JPDA_TRANSPORT` - Debug transport method (dt_socket)

## Tips for Effective Debugging

1. **Use conditional breakpoints** for loops or frequently called methods
2. **Evaluate expressions** to inspect variable values without modifying code
3. **Use the Variables panel** to see all local variables and their values
4. **Set method breakpoints** on method entry/exit
5. **Use the Call Stack** to understand the execution flow

## Spring Profile Configuration

The debug script automatically sets the Spring profile to `dev`, which:
- Enables ActiveMQ dependencies
- Uses development-specific configurations
- Provides more verbose logging

To change the profile, modify the `CATALINA_OPTS` in `debug-intellij.sh`:
```bash
export CATALINA_OPTS="-Dspring.profiles.active=production -Xms512m -Xmx1024m -Xdebug"
```
