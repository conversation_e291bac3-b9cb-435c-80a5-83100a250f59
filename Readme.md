
Functional Description :

With the GC Studios appointment scheduling system, you will be able to easily create, optimize and manage all appointments within your establishment.
You can also manage each of your rooms, set availability for each room and build instructor schedules within their set availability.

Services it interacts:

Database: For Datastore.
Elastic Search : For Instructor Avaiablity Feed.
TIBCO: Intracating with POS for Customer Feed.
Webservices/APIs Calls: For SAM.
EDW: For Employee and Instructor HR Feed.
Active Directory : For Login Authentication.

Build :

1. Download the latest Repo : https://bitbucket.org/guitarcenter-it/scheduler/src/master/

2. Local Development Build:
   - Use the provided script to build with Java 8 and development profiles:
     ```
     ./use-java8-compile-only.sh
     ```
   - This script uses Java 8 and the Maven profiles 'dev' and 'skip-integration-tests'
   - The script will compile the project without running tests or generating reports

3. Production Build:
   - Build the WAR from Local Maven command:
     ```
     mvn clean package -Dproject.version=2020-5-10.C -DskipTests -Pproduction,skip-integration-tests
     ```

4. Deployment:
   - Copy the WAR to Unix Box Directory : /var/lib/gcss/tomcat/webapps
   - Run the Restart Script : /var/lib/gcss/script_restart.sh

Maven Profiles:

- dev: Development profile with ActiveMQ dependencies
- production: Production profile with Oracle dependencies for clustering
- skip-integration-tests: Skips problematic integration tests that require network access


QA	 : Machines

phxweb0001.guitarcenter.com	*************	Apache
phxweb0002.guitarcenter.com	*************	Apache
ecm-app-001-lq.domestic.guitarcenter.com	**************	Tomcat, Zookeeper,solr
ecm-app-002-lq.domestic.guitarcenter.com	**************	Tomcat, Zookeeper,solr
ecm-app-003-lq.domestic.guitarcenter.com	**************	Tomcat, Zookeeper,solr


Code Deployed on ecm-app-001-lq,ecm-app-002-lq,ecm-app-003-lq
Logs Under: /var/lib/gcss/tomcat/logs
Restart: Run  the Restart Script : /var/lib/gcss/script_restart.sh


Production

phxweb1001.guitarcenter.com	*************	Apache
phxweb1002.guitarcenter.com	*************	Apache
ecm-app-001-lp.domestic.guitarcenter.com	**************	Tomcat, Zookeeper,solr
ecm-app-002-lp.domestic.guitarcenter.com	**************	Tomcat, Zookeeper,solr
ecm-app-003-lp.domestic.guitarcenter.com	**************	Tomcat, Zookeeper,solr

Code Deployed on ecm-app-001-lp,ecm-app-002-lp,ecm-app-003-lp
Logs Under: /var/lib/gcss/tomcat/logs
Restart: Run  the Restart Script : /var/lib/gcss/script_restart.sh




