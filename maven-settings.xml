<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">

  <mirrors>
    <!-- Disable the default blocking of HTTP repositories -->
    <mirror>
      <id>maven-default-http-blocker-unblocker</id>
      <mirrorOf>external:http:*</mirrorOf>
      <name>Pseudo repository to mirror external repositories initially using HTTP.</name>
      <url>http://maven-default-http-blocker-unblocker</url>
      <blocked>false</blocked>
    </mirror>
  </mirrors>

  <profiles>
    <profile>
      <id>additional-repositories</id>
      <repositories>
        <repository>
          <id>central</id>
          <name>Central Repository</name>
          <url>https://repo.maven.apache.org/maven2</url>
          <layout>default</layout>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </repository>
        <repository>
          <id>jasperreports</id>
          <name>JasperReports Repository</name>
          <url>https://jaspersoft.jfrog.io/jaspersoft/third-party-ce-artifacts/</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </repository>
        <repository>
          <id>maven-restlet</id>
          <name>Restlet Repository</name>
          <url>https://maven.restlet.talend.com</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </repository>
      </repositories>
    </profile>
  </profiles>

  <activeProfiles>
    <activeProfile>additional-repositories</activeProfile>
  </activeProfiles>
</settings>
