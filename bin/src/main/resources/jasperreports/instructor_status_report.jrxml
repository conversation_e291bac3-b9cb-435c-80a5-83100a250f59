<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.8.0.final using JasperReports Library version 6.8.0-2ed8dfabb690ff337a5797129f2cd92902b0c87b  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="instructro_report" pageWidth="620" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="580" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="bed04340-e8e7-40a5-8087-a71ee8b57593">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\reportfile\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="TO_DATE" class="java.lang.String"/>
	<parameter name="FROM_DATE" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="currentDate" class="java.lang.String">
		<fieldDescription><![CDATA[currentDate]]></fieldDescription>
	</field>
 
	<field name="instructorAppointmentStatusReports" class="java.util.List">
		<fieldDescription><![CDATA[instructorAppointmentStatusReports]]></fieldDescription>
	</field>
	<group name="currentDateGroup">
		<groupExpression><![CDATA[$F{currentDate}]]></groupExpression>
	</group>
	 
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="75" splitType="Stretch">
			<staticText>
				<reportElement x="49" y="17" width="456" height="40" uuid="82b0fdb6-195d-4a00-beee-25e325bd74ae"/>
				<textElement textAlignment="Center">
					<font size="28" isBold="true"/>
				</textElement>
				<text><![CDATA[Instructor Status Schedule Report]]></text>
			</staticText>
			<elementGroup>
				<staticText>
					<reportElement x="184" y="45" width="31" height="22" backcolor="#D9DADB" uuid="56271236-2e3a-4b02-ae40-16a4bcbbcd82"/>
					<box>
						<pen lineColor="#D0D0D0"/>
						<topPen lineColor="#D0D0D0"/>
						<leftPen lineColor="#D0D0D0"/>
						<bottomPen lineColor="#D0D0D0"/>
						<rightPen lineColor="#D0D0D0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true" isStrikeThrough="false"/>
					</textElement>
					<text><![CDATA[From:]]></text>
				</staticText>
				<staticText>
					<reportElement x="287" y="45" width="20" height="22" backcolor="#D9DADB" uuid="de81c6a8-3302-465f-a180-0f245591fb8f"/>
					<box>
						<pen lineColor="#D0D0D0"/>
						<topPen lineColor="#D0D0D0"/>
						<leftPen lineColor="#D0D0D0"/>
						<bottomPen lineColor="#D0D0D0"/>
						<rightPen lineColor="#D0D0D0"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<text><![CDATA[To:]]></text>
				</staticText>
			</elementGroup>
			<textField>
				<reportElement x="307" y="45" width="100" height="22" uuid="3a02ac2d-7ec3-46af-9a62-47af8a82b963"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{TO_DATE}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="215" y="45" width="87" height="22" uuid="dd16799e-a882-4180-9a81-2ccd6f655406"/>
				<box>
					<pen lineColor="#D0D0D0"/>
					<topPen lineColor="#D0D0D0"/>
					<leftPen lineColor="#D0D0D0"/>
					<bottomPen lineColor="#D0D0D0"/>
					<rightPen lineColor="#D0D0D0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{FROM_DATE}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="33">
			<subreport>
				<reportElement x="0" y="20" width="642" height="13" uuid="dd8efc95-a9d3-4999-b226-c4df7ef055fa">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "instructor_status_report_title_subreport.jasper"]]></subreportExpression>
			</subreport>
	 
		</band>
		<band height="12">
			<subreport>
				<reportElement x="0" y="-1" width="642" height="13" uuid="6dc360bc-35be-4895-8e9c-1d6561e488c7">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource((Collection)$F{instructorAppointmentStatusReports})]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "instructor_status_report_subreport1.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
