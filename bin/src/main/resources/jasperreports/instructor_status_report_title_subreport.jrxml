<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.8.0.final using JasperReports Library version 6.8.0-2ed8dfabb690ff337a5797129f2cd92902b0c87b  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="instructor_report_title_subreport" pageWidth="714" pageHeight="752" whenNoDataType="AllSectionsNoDetail" columnWidth="714" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="b654b8f6-a6ed-432b-884e-374f4bee78b2">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="rowBgStyle" mode="Opaque" fill="Solid" pattern="" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false">
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style mode="Opaque" backcolor="#D9DADB"/>
		</conditionalStyle>
	</style>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="instructorAvailibilityTime" class="java.lang.String">
		<fieldDescription><![CDATA[instructorAvailibilityTime]]></fieldDescription>
	</field>
	<field name="instructorId" class="java.lang.String">
		<fieldDescription><![CDATA[instructorId]]></fieldDescription>
	</field>
	<field name="instructorName" class="java.lang.String">
		<fieldDescription><![CDATA[instructorName]]></fieldDescription>
	</field>
	<field name="instructorNameWithAvaiTime" class="java.lang.String">
		<fieldDescription><![CDATA[instructorNameWithAvaiTime]]></fieldDescription>
	</field>
	<field name="isClosestScheduled" class="java.lang.String">
		<fieldDescription><![CDATA[isClosestScheduled]]></fieldDescription>
	</field>
	<field name="isSelectedInstructor" class="java.lang.String">
		<fieldDescription><![CDATA[isSelectedInstructor]]></fieldDescription>
	</field>
	<pageHeader>
		<band height="31" splitType="Stretch">
			<staticText>
				<reportElement positionType="FixRelativeToBottom" mode="Opaque" x="0" y="1" width="572" height="30" forecolor="#FFFFFF" backcolor="#666666" uuid="15942d82-ccf1-4e40-a9a0-65416247b09c">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle" markup="none">
					<font size="12" isBold="true"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[ ]]></text>
			</staticText>
		</band>
	</pageHeader>
</jasperReport>
