<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
			     http://maven.apache.org/maven-v4_0_0.xsd">
	<!-- Maven configuration for GC Studios Scheduling project $Id: $ -->
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.guitarcenter</groupId>
	<artifactId>scheduler</artifactId>
	<packaging>war</packaging>
	<version>1.0-SNAPSHOT</version>
	<name>scheduler</name>
	<description>GC Studios Scheduling web-application</description>

	<!-- Properties of dependent components -->
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<java.version>1.8</java.version>
		<!-- Changes made for GSSP-160 -->
		<springframework.version>4.0.3.RELEASE</springframework.version>
		<springsecurity.version>3.1.4.RELEASE</springsecurity.version>
		<spring-ldap.version>1.3.1.RELEASE</spring-ldap.version>
		<hibernate.version>4.2.2.Final</hibernate.version>
		<hibernatejpa.version>1.0.1.Final</hibernatejpa.version>
		<ehcache.version>2.6.5</ehcache.version>
		<slf4j.version>1.7.5</slf4j.version>
		<junit.version>4.11</junit.version>
		<!-- Spring Web MVC depends on 3.0.1 and 2.1 for Servlet/JSP API respectively -->
		<servlet.version>3.0.1</servlet.version>
		<jsp.version>2.1</jsp.version>
		<jstl.version>1.2</jstl.version>
		<!-- SolrJ version -->
		<solrj.version>4.3.1</solrj.version>
		<!-- jackson -->
		<jackson.version>2.12.2</jackson.version>
		<!-- taglibs -->
		<taglibs-standard.version>1.1.2</taglibs-standard.version>
		<!-- validation -->
		<validation.version>1.1.0.Final</validation.version>
		<!-- hibernate-validator -->
		<hibernate-validator.version>4.3.1.Final</hibernate-validator.version>
		<!-- joda -->
		<joda-time.version>2.2</joda-time.version>
		<joda-time-jsptags.version>1.1.1</joda-time-jsptags.version>
		<jadira.version>3.0.0.GA</jadira.version>
		<!-- freemarker -->
		<freemarker.version>2.3.23</freemarker.version>
		<!-- jasperreports -->
		<jasperreports.version>5.2.0</jasperreports.version>
		<jasperreports.fonts.version>4.0.0</jasperreports.fonts.version>
		<jasperreports.output.path>src/main/webapp/WEB-INF/views/reports</jasperreports.output.path>
		<!-- JiBX -->
		<jibx.version>1.2.5</jibx.version>
		<guava.version>16.0.1</guava.version>
		<!-- ActiveMQ -->
		<activemq.version>5.8.0</activemq.version>
		<jsch.version>0.1.55</jsch.version>
		<!-- Quartz - still using 1.8.x -->
		<!-- Changes made for GSSP-160 -->
		<quartz.version>1.8.6</quartz.version>
		<!-- supercsv version 2.2.0 -->
		<supercsv.version>2.2.0</supercsv.version>
		<!-- Pattern of tests to skip; should be empty unless set in a profile -->
		<exclude.tests>none</exclude.tests>
		<skipTests>true</skipTests>
	</properties>


	<dependencies>

		<!-- Springsource recommend logging via SLF4J and Log4j rather than the
			prior default of commons-logging -->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>${slf4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jcl-over-slf4j</artifactId>
			<version>${slf4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-log4j12</artifactId>
			<version>${slf4j.version}</version>
			<scope>runtime</scope>
		</dependency>



		<!-- jackson -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>${jackson.version}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>${jackson.version}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>${jackson.version}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-hibernate4</artifactId>
			<version>${jackson.version}</version>
		</dependency>

		<!-- spring framework dependencies -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<version>${springframework.version}</version>
			<exclusions>
				<!-- Exclude commons-logging in favour of SLF4j; see below -->
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			<version>${springframework.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
			<version>${springframework.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
			<version>${springframework.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-expression</artifactId>
			<version>${springframework.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aop</artifactId>
			<version>${springframework.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
			<version>${springframework.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-orm</artifactId>
			<version>${springframework.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
			<version>${springframework.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<version>${springframework.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
			<version>${springframework.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-oxm</artifactId>
			<version>${springframework.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jms</artifactId>
			<version>${springframework.version}</version>
		</dependency>


		<!-- spring security framework dependencies -->
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-core</artifactId>
			<version>${springsecurity.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-acl</artifactId>
			<version>${springsecurity.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-taglibs</artifactId>
			<version>${springsecurity.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-config</artifactId>
			<version>${springsecurity.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-web</artifactId>
			<version>${springsecurity.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.ldap</groupId>
			<artifactId>spring-ldap-core</artifactId>
			<version>${spring-ldap.version}</version>
			<exclusions>
				<!-- Exclude commons-logging in favour of SLF4j -->
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-ldap</artifactId>
			<version>${springsecurity.version}</version>
		</dependency>

		<!-- Hibernate ORM via JPA with EHCache -->
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
			<version>${hibernate.version}</version>
		</dependency>
		<dependency>
			<groupId>org.hibernate.javax.persistence</groupId>
			<artifactId>hibernate-jpa-2.0-api</artifactId>
			<version>${hibernatejpa.version}</version>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-entitymanager</artifactId>
			<version>${hibernate.version}</version>
		</dependency>
		<dependency>
			<groupId>net.sf.ehcache</groupId>
			<artifactId>ehcache-core</artifactId>
			<version>${ehcache.version}</version>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-ehcache</artifactId>
			<version>${hibernate.version}</version>
		</dependency>

		<!-- JSR 330 validation dependency -->
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-validator</artifactId>
			<version>${hibernate-validator.version}</version>
		</dependency>

		<!-- Guava dependency -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>

		<!-- SolrJ client library -->
		<dependency>
			<groupId>org.apache.solr</groupId>
			<artifactId>solr-solrj</artifactId>
			<version>${solrj.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- Joda time dependency -->
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>${joda-time.version}</version>
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time-jsptags</artifactId>
			<version>${joda-time-jsptags.version}</version>
		</dependency>
		<dependency>
			<groupId>org.jadira.usertype</groupId>
			<artifactId>usertype.core</artifactId>
			<version>${jadira.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- freemarker dependency -->
		<dependency>
			<groupId>org.freemarker</groupId>
			<artifactId>freemarker</artifactId>
			<version>${freemarker.version}</version>
		</dependency>

		<!-- email dependencies -->
		<dependency>
			<groupId>javax.mail</groupId>
			<artifactId>mail</artifactId>
			<version>1.4.7</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.activation</groupId>
			<artifactId>activation</artifactId>
			<version>1.1.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>${quartz.version}</version>
		</dependency>

		<!-- Servlet/JSP dependencies that will be provided by Tomcat -->
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>${servlet.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.servlet.jsp</groupId>
			<artifactId>jsp-api</artifactId>
			<version>${jsp.version}</version>
			<scope>provided</scope>
		</dependency>

		<!-- JSTL dependencies -->
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jstl</artifactId>
			<version>${jstl.version}</version>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>taglibs</groupId>
			<artifactId>standard</artifactId>
			<version>${taglibs-standard.version}</version>
			<scope>runtime</scope>
		</dependency>

		<!-- jasperreports dependency -->
		<dependency>
			<groupId>net.sf.jasperreports</groupId>
			<artifactId>jasperreports</artifactId>
			<version>${jasperreports.version}</version>
			<exclusions>
				<!-- Exclude commons-logging in favour of SLF4j -->
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>net.sf.jasperreports</groupId>
			<artifactId>jasperreports-fonts</artifactId>
			<version>${jasperreports.fonts.version}</version>
		</dependency>

		<!-- JiBX dependencies -->
		<dependency>
			<groupId>org.jibx</groupId>
			<artifactId>jibx-run</artifactId>
			<version>${jibx.version}</version>
		</dependency>
		<!-- Itext dependency -->
	<!--	<dependency>
			<groupId>com.lowagie</groupId>
			<artifactId>itext</artifactId>
			<version>2.1.7</version>
		</dependency>-->

       <dependency>
                  <groupId>com.itextpdf</groupId>
                  <artifactId>itextpdf</artifactId>
                  <version>5.0.6</version>
       </dependency>




		<!-- Jsch dependency -->
		<dependency>
			<groupId>com.jcraft</groupId>
			<artifactId>jsch</artifactId>
			<version>${jsch.version}</version>
		</dependency>

		<!-- <dependency>
			 <groupId>com.github.mwiede</groupId>
			<artifactId>jsch</artifactId>
			<version>0.1.72</version>
		</dependency> -->
		<!-- supercsv dependency -->
		<dependency>
		    <groupId>net.sf.supercsv</groupId>
		    <artifactId>super-csv</artifactId>
			<version>${supercsv.version}</version>
		</dependency>

		<!-- Testing env dependencies -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<version>${springframework.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>${junit.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-core</artifactId>
			<version>1.3</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.hsqldb</groupId>
			<artifactId>hsqldb</artifactId>
			<version>2.3.0</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.tomcat</groupId>
			<artifactId>tomcat-dbcp</artifactId>
			<version>7.0.42</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.solr</groupId>
			<artifactId>solr-test-framework</artifactId>
			<version>${solrj.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.solr</groupId>
			<artifactId>solr-dataimporthandler</artifactId>
			<version>${solrj.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.icegreen</groupId>
			<artifactId>greenmail</artifactId>
			<version>1.3.1b</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.activemq</groupId>
			<artifactId>activemq-client</artifactId>
			<version>${activemq.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.activemq</groupId>
			<artifactId>activemq-broker</artifactId>
			<version>${activemq.version}</version>
			<scope>test</scope>
		</dependency>
		<!-- Excel dependency -->
		<dependency>
    		<groupId>org.apache.poi</groupId>
    		<artifactId>poi</artifactId>
   			 <version>3.9</version>
		</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>3.9</version>
		</dependency>

   <!-- https://mvnrepository.com/artifact/org.bouncycastle/bcprov-jdk15on -->

	<dependency>
    <groupId>org.bouncycastle</groupId>
    <artifactId>bcpg-jdk15on</artifactId>
    <version>1.47</version>
    </dependency>

    <!-- https://mvnrepository.com/artifact/com.sun.xml.bind/jaxb-impl -->
		<dependency>
		    <groupId>com.sun.xml.bind</groupId>
		    <artifactId>jaxb-impl</artifactId>
		    <version>2.2.11</version>
		</dependency>

		<dependency>
		    <groupId>com.sun.xml.bind</groupId>
		    <artifactId>jaxb-core</artifactId>
		    <version>2.2.11</version>
		</dependency>


    <dependency>
		    <groupId>org.elasticsearch.client</groupId>
		    <artifactId>elasticsearch-rest-high-level-client</artifactId>
		    <version>7.11.1</version>
		    <exclusions>
				<!-- Exclude commons-logging in favour of SLF4j -->
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
		    <groupId>com.amazonaws</groupId>
		    <artifactId>aws-java-sdk-core</artifactId>
		    <version>1.11.779</version>
		     <exclusions>
				<!-- Exclude commons-logging in favour of SLF4j -->
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.lucene/lucene-core -->
<dependency>
    <groupId>org.apache.lucene</groupId>
    <artifactId>lucene-core</artifactId>
    <version>8.7.0</version>
</dependency>







	   <dependency>
		    <groupId>org.apache.httpcomponents</groupId>
		    <artifactId>httpclient</artifactId>
		    <version>4.5.6</version>
		    <exclusions>
				<!-- Exclude commons-logging in favour of SLF4j -->
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
     </dependency>

		    <!-- https://mvnrepository.com/artifact/com.amazonaws/aws-java-sdk-core -->
		<!-- <dependency>
		    <groupId>com.amazonaws</groupId>
		    <artifactId>aws-java-sdk-core</artifactId>
		    <version>1.11.779</version>
		</dependency>

		<dependency>
		    <groupId>org.elasticsearch.client</groupId>
		    <artifactId>elasticsearch-rest-high-level-client</artifactId>
		    <version>7.11.1</version>
		</dependency>

		<dependency>
		   <groupId>org.apache.lucene</groupId>
		   <artifactId>lucene-core</artifactId>
		   <version>3.6.0</version>
	   </dependency>

	   <dependency>
		    <groupId>org.apache.httpcomponents</groupId>
		    <artifactId>httpclient</artifactId>
		    <version>4.5</version>
     </dependency> -->


		<!-- https://mvnrepository.com/artifact/org.elasticsearch.client/elasticsearch-rest-high-level-client -->


		<!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpclient -->




	</dependencies>

	<build>
		<defaultGoal>package</defaultGoal>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
				<excludes>
					<exclude>**/*.jrxml</exclude>
					<exclude>**/node_modules/**</exclude>
				</excludes>
			</resource>
		</resources>
		<testResources>
			<testResource>
				<directory>src/test/resources</directory>
				<filtering>true</filtering>
			</testResource>
			<testResource>
				<directory>sql</directory>
				<filtering>true</filtering>
			</testResource>
		</testResources>

		<plugins>
			<!-- compiler plugin -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.1</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
				</configuration>
			</plugin>


			<!-- resources plugin -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>2.6</version>
			</plugin>

			<!-- run unit or integration test plugin -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.15</version>
				<configuration>
					<argLine>-Xmx256m -XX:MaxPermSize=128m</argLine>
					<systemPropertyVariables>
						<solr.solr.home>${project.build.testOutputDirectory}/solr</solr.solr.home>
					</systemPropertyVariables>
					  <skipTests>${skipTests}</skipTests>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>org.apache.maven.surefire</groupId>
						<artifactId>surefire-junit47</artifactId>
						<version>2.15</version>
					</dependency>
				</dependencies>
			</plugin>

			<!-- war package plugin -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<version>2.4</version>
				<configuration>
					<warName>${project.artifactId}</warName>
					<warSourceExcludes>**/node_modules/**</warSourceExcludes>
					<webResources>
						<resource>
							<directory>src/main/webapp</directory>
							<filtering>true</filtering>
							<includes>
								<include>**/*.jsp</include>
							</includes>
						</resource>
					</webResources>

				</configuration>
			</plugin>

			<!-- clean plugin -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-clean-plugin</artifactId>
				<version>2.5</version>
				<configuration>
					<filesets>
						<fileset>
							<directory>${jasperreports.output.path}</directory>
							<followSymlinks>false</followSymlinks>
							<useDefaultExcludes>true</useDefaultExcludes>
							<includes>
								<include>*.jasper</include>
							</includes>
						</fileset>
					</filesets>
				</configuration>
			</plugin>

			<!-- enforcer plugin -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<version>1.3.1</version>
				<executions>
					<execution>
						<id>enforce-banned-dependencies</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<rules>
								<requireJavaVersion>
									<version>[${java.version}.0-00,${java.version}.99-99]</version>
								</requireJavaVersion>
								<bannedDependencies>
									<searchTransitive>true</searchTransitive>
									<excludes>
										<exclude>commons-logging</exclude>
										<exclude>aspectj:aspectj*</exclude>
										<exclude>org.springframework:2.*</exclude>
										<exclude>org.springframework:3.0.*</exclude>
									</excludes>
								</bannedDependencies>
							</rules>
							<fail>true</fail>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!-- cobertura plugin -->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>cobertura-maven-plugin</artifactId>
				<version>2.5.2</version>
				<configuration>
					<instrumentation>
						<excludes>
							<exclude>**/model/**/*.class</exclude>
							<exclude>**/*Controller.class</exclude>
						</excludes>
					</instrumentation>
				</configuration>
			</plugin>

			<!-- Replacer plugin: Modify SQL scripts to remove Oracle/HSQL incompatibilities -->
			<plugin>
				<groupId>com.google.code.maven-replacer-plugin</groupId>
				<artifactId>replacer</artifactId>
				<version>1.5.2</version>
				<executions>
					<execution>
						<id>forTesting</id>
						<phase>process-test-resources</phase>
						<goals>
							<goal>replace</goal>
						</goals>
						<configuration>
							<includes>
								<include>target/test-classes/00_create_schema.*</include>
								<include>target/test-classes/sql/10_create_junit_test_environment.*</include>
							</includes>
							<replacements>
								<replacement>
									<token>MAXVALUE[\n ]+9+</token>
									<value>NOMAXVALUE</value>
								</replacement>
								<replacement>
									<token>CASCADE[\n ]CONSTRAINTS</token>
									<value>CASCADE</value>
								</replacement>
								<replacement>
									<token>TIMESTAMP WITH LOCAL TIME ZONE</token>
									<value>TIMESTAMP WITH TIME ZONE</value>
								</replacement>
								<replacement>
									<token>LOGGING ;</token>
									<value>;</value>
								</replacement>
								<replacement>
									<token>NOT[ \n]DEFERRABLE</token>
									<value></value>
								</replacement>
								<replacement>
									<token>(\.\d{3})(\d{6}|\d{3}) ([AP]M)</token>
									<value>$1 $3</value>
								</replacement>
								<replacement>
									<token>\.SSXFF</token>
									<value>\.SS\.FF</value>
								</replacement>
								<replacement>
									<token>-RR </token>
									<value>-YY </value>
								</replacement>
								<replacement>
									<token>to_timestamp_tz\('(\d{2})-(\w{3})-(\d{2}) (\d{2})\.(\d{2})\.(\d{2})\.(\d{3}) ([AP]M) ([+-]\d{2}:\d{2})','DD-MON-YY HH\.MI\.SS\.FF AM TZR'\)</token>
									<value>TIMESTAMP(to_char(TO_TIMESTAMP\('$1-$2-$3 $4.$5.$6.$7 $8','DD-MON-YY HH\.MI\.SS\.FF AM'\),'YYYY-MM-DD HH24:MI:SS.FF') || '$9')</value>
								</replacement>
							</replacements>
							<regexFlags>
								<regexFlag>CASE_INSENSITIVE</regexFlag>
								<regexFlag>MULTILINE</regexFlag>
							</regexFlags>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!-- Compile jrxml files -->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>jasperreports-maven-plugin</artifactId>
				<version>1.0-beta-2</version>
				<configuration>
					<outputDirectory>${jasperreports.output.path}</outputDirectory>
					<sourceDirectory>src/main/resources/jasperreports</sourceDirectory>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>compile-reports</goal>
						</goals>
						<phase>test-compile</phase>
					</execution>
				</executions>
				<dependencies>
					<dependency>
						<groupId>net.sf.jasperreports</groupId>
						<artifactId>jasperreports</artifactId>
						<version>${jasperreports.version}</version>
						<exclusions>
							<exclusion>
								<groupId>commons-logging</groupId>
								<artifactId>commons-logging</artifactId>
							</exclusion>
						</exclusions>
					</dependency>
                    <dependency>
                        <groupId>org.slf4j</groupId>
                        <artifactId>jcl-over-slf4j</artifactId>
                        <version>${slf4j.version}</version>
                    </dependency>
					<dependency>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-simple</artifactId>
						<version>${slf4j.version}</version>
					</dependency>
				</dependencies>
			</plugin>

			<!-- Using JiBX for XSD/Java mapping -->
			<plugin>
				<groupId>org.jibx</groupId>
				<artifactId>jibx-maven-plugin</artifactId>
				<version>${jibx.version}</version>
				<executions>
					<execution>
						<id>generate-java-from-xsd</id>
						<goals>
							<goal>schema-codegen</goal>
						</goals>
						<configuration>
							<customizations>
								<customization>src/main/config/jixb-customize.xml</customization>
							</customizations>
						</configuration>
					</execution>
					<execution>
						<id>bind-generated-java-code</id>
						<goals>
							<goal>bind</goal>
						</goals>
						<configuration>
							<schemaBindingDirectory>${project.build.directory}/generated-sources</schemaBindingDirectory>
							<load>true</load>
							<validate>true</validate>
							<verify>false</verify>
							<verbose>false</verbose>
						</configuration>
					</execution>
				</executions>

				<!-- Changes made for GSSP-160 -->
					<dependencies>
						<dependency>
							<groupId>org.jibx</groupId>
							<artifactId>jibx-bind</artifactId>
							<version>${jibx.version}</version>
							<exclusions>
							<exclusion>
							<artifactId>bcel</artifactId>
							<groupId>bcel</groupId>
							</exclusion>
							</exclusions>
						</dependency>
						<dependency>
						<groupId>org.apache.bcel</groupId>
						<artifactId>bcel</artifactId>
						<version>6.0-RC3</version>
						</dependency>


						<dependency>
								  <groupId>org.apache.bcel</groupId>
								  <artifactId>bcel</artifactId>
								  <version>6.0</version>
						</dependency>

					</dependencies>
				<!-- End of  Changes made for GSSP-160 -->
			</plugin>
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>production</id>
			<dependencies>
				<!-- Using Oracle for clustering in Production target -->
				<dependency>
					<groupId>org.quartz-scheduler</groupId>
					<artifactId>quartz-oracle</artifactId>
					<version>${quartz.version}</version>
				</dependency>
			</dependencies>
		</profile>

		<profile>
			<id>dev</id>
			<activation>
				<activeByDefault>false</activeByDefault>
			</activation>
			<properties>
				<skipTests>false</skipTests>
			</properties>
			<dependencies>
				<dependency>
					<groupId>org.apache.activemq</groupId>
					<artifactId>activemq-client</artifactId>
					<version>${activemq.version}</version>
				</dependency>
				<dependency>
					<groupId>org.apache.activemq</groupId>
					<artifactId>activemq-broker</artifactId>
					<version>${activemq.version}</version>
				</dependency>
			</dependencies>
		</profile>

		<!-- This profile prevents surefire from executing integration tests
			 that are problematic to run in certain scenarios.

			 1. When on-site at client, remote tests fail for network issues
			    - RemoteDataIntegrationServiceTest
			    - EmployeeDataIntegrationServiceTest
			    - LocationDataIntegrationServiceTest
	    -->
		<profile>
			<id>skip-integration-tests</id>
			<activation>
				<activeByDefault>false</activeByDefault>
			</activation>
			<properties>
				<exclude.tests>%regex[.*(RemoteData|Employee|Location|Customer)IntegrationServiceTest.*]</exclude.tests>
				<skipTests>false</skipTests>
			</properties>
		</profile>

		<profile>
			<id>init-dev-database</id>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-resources-plugin</artifactId>
						<executions>
							<execution>
								<id>copy-sql-script-for-init-database</id>
								<phase>package</phase>
								<goals>
									<goal>copy-resources</goal>
								</goals>
								<configuration>
									<encoding>UTF-8</encoding>
									<outputDirectory>${project.build.directory}/sql</outputDirectory>
									<resources>
										<resource>
											<directory>sql/</directory>
											<includes>
												<include>**/*.sql</include>
											</includes>
											<filtering>true</filtering>
										</resource>
									</resources>
								</configuration>
							</execution>
						</executions>
					</plugin>

					<!-- Replacer plugin: Modify SQL scripts to avoid error -->
					<plugin>
						<groupId>com.google.code.maven-replacer-plugin</groupId>
						<artifactId>replacer</artifactId>
						<version>1.5.2</version>
						<executions>
							<execution>
								<id>forInitDevDatabase</id>
								<phase>package</phase>
								<goals>
									<goal>replace</goal>
								</goals>
								<configuration>
									<includes>
										<include>target/sql/99_development_setup.sql</include>
										<include>target/sql/patches/*.sql</include>
									</includes>
									<replacements>
										<replacement>
											<token> \+00\:00</token>
											<value> ${user.timezone}</value>
										</replacement>
										<replacement>
											<token>whenever sqlerror .*</token>
											<value></value>
										</replacement>
									</replacements>
									<regexFlags>
										<regexFlag>CASE_INSENSITIVE</regexFlag>
										<regexFlag>MULTILINE</regexFlag>
									</regexFlags>
								</configuration>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>sql-maven-plugin</artifactId>
						<version>1.5</version>

						<dependencies>
							<!-- specify the dependent jdbc driver here -->
							<dependency>
								<groupId>com.oracle</groupId>
								<artifactId>ojdbc6</artifactId>
								<version>0</version>
								<scope>system</scope>
								<systemPath>${basedir}/lib/ojdbc6.jar</systemPath>
							</dependency>
						</dependencies>

						<!-- common configuration shared by all executions -->
						<configuration>
							<driver>oracle.jdbc.OracleDriver</driver>
							<url>**************************************</url>
							<username>gcs</username>
							<password>gcs</password>
							<autocommit>true</autocommit>
							<!-- ignore error when database is not avaiable -->
							<onError>continue</onError>
						</configuration>
						<executions>
							<execution>
								<phase>package</phase>
								<goals>
									<goal>execute</goal>
								</goals>
								<!-- specific configuration for this execution -->
								<configuration>
									<!-- specify your SQL commands, can be all of the following configurations -->
									<srcFiles>
										<srcFile>target/sql/00_create_schema.sql</srcFile>
										<srcFile>target/sql/01_populate_reference_values.sql</srcFile>
										<srcFile>target/sql/02_quartz_schema_prod.sql</srcFile>
										<srcFile>target/sql/03_data_migration_tables.sql</srcFile>
										<srcFile>target/sql/99_development_setup.sql</srcFile>
										<srcFile>target/sql/patches/02_home_recording_setup_gcss-547.sql</srcFile>
										<srcFile>target/sql/patches/03_add_break_activity_gcss-549.sql</srcFile>
										<srcFile>target/sql/patches/11_update_sparring_partner_mapping_TimeTrade_gcss-587.sql</srcFile>
										<srcFile>target/sql/patches/12_make_unending_series_gcss-591.sql</srcFile>
										<srcFile>target/sql/patches/13_create_time_off_table_gcss-590.sql</srcFile>
										<srcFile>target/sql/patches/19_create_one_time_availability_table_gcss-650.sql</srcFile>
									</srcFiles>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>
</project>
